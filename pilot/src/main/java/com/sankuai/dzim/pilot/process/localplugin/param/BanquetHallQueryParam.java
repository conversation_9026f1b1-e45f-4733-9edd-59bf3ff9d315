package com.sankuai.dzim.pilot.process.localplugin.param;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import lombok.Data;

@Data
public class BanquetHallQueryParam {

    @JsonPropertyDescription("需要查询的门店Id,门店Id请从系统消息中获取")
    @JsonProperty(required = true)
    private long shopId;

    @JsonPropertyDescription("需要查询的宴会厅Id,宴会厅Id请从系统消息中获取")
    @JsonProperty(required = true)
    private long banquetHallId;

    @JsonPropertyDescription("与宴会厅相关的具体问题,越具体越好,最好是一个问句,例如“门店有几个主题的婚宴厅?宴会厅大小？”等")
    @JsonProperty(required = true)
    private String question;
}
