package com.sankuai.dzim.pilot.chain.eval.evaluator;

import com.alibaba.fastjson.JSONObject;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.message.dto.MessageDTO;
import com.sankuai.dzim.pilot.chain.data.AIAnswerData;
import com.sankuai.dzim.pilot.chain.data.AIServiceContext;
import com.sankuai.dzim.pilot.chain.eval.data.EvalCase;
import com.sankuai.dzim.pilot.chain.eval.data.EvalCaseResult;
import com.sankuai.dzim.pilot.chain.eval.data.EvalContext;
import com.sankuai.dzim.pilot.chain.impl.AdvancedRagAIService;
import com.sankuai.dzim.pilot.process.data.AIServiceConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @author: zhouyibing
 * @date: 2025/4/17
 */
@Component
@Slf4j
public class LLMEvaluator implements Evaluator {

    @Autowired
    private AdvancedRagAIService advancedRagAIService;

    @ConfigValue(key = "com.sankuai.mim.pilot.eval.config", defaultValue = "{}")
    private HashMap<String, AIServiceConfig> evalConfig;

    @Override
    public boolean accept(String evaluator) {
        return this.getClass().getSimpleName().equals(evaluator);
    }

    public EvalCaseResult measure(EvalContext evalContext, EvalCase evalCase) {
        // 1. 调用AI服务
        AIAnswerData aiAnswerData = getAiAnswerData(evalContext, evalCase);

        // 2. 构造结果
        return buildEvalResult(evalCase, aiAnswerData);
    }

    protected AIAnswerData getAiAnswerData(EvalContext evalContext, EvalCase evalCase) {
        // 1. 构造上下文
        AIServiceContext aiServiceContext = buildAIServiceContext(evalContext, evalCase);
        if (aiServiceContext == null) {
            return null;
        }

        // 2. 执行调用
        try {
            return advancedRagAIService.execute(aiServiceContext);
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("getAiAnswerData").build(),
                    new WarnMessage("LLMEvaluator", "获取模型回复异常", null), evalCase, null, e);
            return null;
        }
    }

    public AIServiceContext buildAIServiceContext(EvalContext evalContext, EvalCase evalCase) {
        AIServiceConfig config = MapUtils.getObject(evalConfig, evalContext.getMetric());
        if (config == null) {
            return null;
        }

        MessageDTO messageDTO = buildMessageDTO(evalCase);

        String systemPrompt = buildSystemPrompt(config.getSystemPrompt(), evalCase);

        AIServiceContext aIServiceContext = new AIServiceContext();
        aIServiceContext.setMessageDTO(messageDTO);
        aIServiceContext.setMemoryComponents(config.getMemoryComponents());
        aIServiceContext.setQueryUnderstandComponents(config.getQueryUnderstandComponents());
        aIServiceContext.setRetrievalComponents(config.getRetrievalComponents());
        aIServiceContext.setPluginNames(config.getPluginNames());
        aIServiceContext.setSystemPrompt(systemPrompt);
        aIServiceContext.setModel(config.getModel());
        aIServiceContext.setAppId(config.getAppId());
        aIServiceContext.setTemperature(config.getTemperature());
        aIServiceContext.setTopP(config.getTopP());
        aIServiceContext.setIsJsonModel(config.getIsJsonModel());
        aIServiceContext.setStream(config.isStream());
        aIServiceContext.setExtraInfo(null);
        aIServiceContext.setRetrievalMap(config.getRetrievalMap());
        aIServiceContext.setMaxTokens(config.getMaxTokens());
        return aIServiceContext;
    }

    protected MessageDTO buildMessageDTO(EvalCase evalCase) {
        MessageDTO messageDTO = new MessageDTO();
        messageDTO.setMessage("请开始输出");
        return messageDTO;
    }

    protected String buildSystemPrompt(String systemPrompt, EvalCase evalCase) {
        if (systemPrompt == null || evalCase == null) {
            return null;
        }

        String result = systemPrompt;
        // 通过反射获取所有字段并替换
        Field[] fields = evalCase.getClass().getDeclaredFields();
        for (Field field : fields) {
            try {
                field.setAccessible(true);
                String fieldName = field.getName();
                Object value = field.get(evalCase);
                // 替换模板中的占位符 ${fieldName}
                if (value != null) {
                    result = result.replace("${" + fieldName + "}", value.toString());
                }
            } catch (IllegalAccessException e) {
                continue;
            }
        }

        // 替换knowledge里的字段
        result = replaceJsonStr(result, evalCase.getKnowledge());
        // 替换context里的字段
        result = replaceJsonStr(result, evalCase.getContext());

        return result;
    }

    private String replaceJsonStr(String result, String replaceJsonStr) {
        try {
            JSONObject contextJson = JSONObject.parseObject(replaceJsonStr);
            for (String fieldName : contextJson.keySet()) {
                Object value = contextJson.get(fieldName);
                if (value != null) {
                    result = result.replace("${" + fieldName + "}", value.toString());
                }
            }
            return result;
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("replaceJsonStr").build(),
                    new WarnMessage("LLMEvaluator", "replaceJsonStr异常", null), replaceJsonStr, null, e);
            return result;
        }
    }

    protected EvalCaseResult buildEvalResult(EvalCase evalCase, AIAnswerData aiAnswerData) {
        EvalCaseResult result = new EvalCaseResult();
        BeanUtils.copyProperties(evalCase, result);

        if (aiAnswerData == null || aiAnswerData.getAnswer() == null) {
            return result;
        }

        try {
            // 解析 JSON 响应
            String answerText = removeMarkdownJson(aiAnswerData.getAnswer());
            Map<String, Object> resultMap = JsonCodec.decode(answerText, Map.class);
            if (resultMap == null) {
                return result;
            }

            Integer score = MapUtils.getInteger(resultMap, "score");
            String reason = MapUtils.getString(resultMap, "reason");
            result.setScore(score);
            result.setReason(reason);

            return result;
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("buildEvalResult").build(),
                    new WarnMessage("FunctionCallingEvaluator", "buildEvalResult异常", null), aiAnswerData, null, e);
            return result;
        }
    }

    @NotNull
    protected static String removeMarkdownJson(String answerText) {
        Pattern pattern = Pattern.compile("```json(.*?)```", Pattern.DOTALL);
        Matcher matcher = pattern.matcher(answerText);
        if (matcher.find()) {
            answerText = matcher.group(1).trim();
        }
        return answerText;
    }
}
