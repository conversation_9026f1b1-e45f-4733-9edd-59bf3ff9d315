package com.sankuai.dzim.pilot.process.aibook;

import com.sankuai.dzim.pilot.api.data.beauty.ai.BookCallbackReq;
import com.sankuai.dzim.pilot.process.aibook.data.AIBookCallAgainDelayData;
import com.sankuai.dzim.pilot.process.aibook.data.AIBookCallbackDelayData;
import com.sankuai.dzim.pilot.process.aibook.data.AIBookDispatchDelayData;
import com.sankuai.dzim.pilot.process.aibook.data.UserBookRequest;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/1/9 11:11
 */
public interface AIBookDispatchProcessService {

    /**
     * 门店召回(内部递归)
     *
     * @param request StringUtils.EMPTY
     * @return
     */
    List<Long> recallRecommendShopIds(UserBookRequest request);


    /**
     * 用户预约任务发起
     *
     * @param request request
     * @return
     */
    Pair<Integer, Long> dispatchUserBookTask(UserBookRequest request);


    /**
     * 外呼延时任务处理
     *
     * @param data data
     * @return
     */
    boolean dispatchDelayTask(AIBookDispatchDelayData data);


    /**
     * 外呼回调超时处理
     *
     * @param data data
     * @return
     */
    boolean processCallbackTimeout(AIBookCallbackDelayData data);


    /**
     * 二次外呼延时处理
     *
     * @param data data
     * @return
     */
    boolean processCallAgain(AIBookCallAgainDelayData data);


    /**
     * 单个执行taskId
     *
     * @param taskId taskId
     */
    boolean executeTaskCall(long taskId);


    /**
     * AI 外呼回调
     *
     * @param req req
     * @return
     */
    boolean callbackAIBook(BookCallbackReq req);


    /**
     * 取消预约任务
     *
     * @param request
     * @return
     */
    boolean dispatchCancelTask(Long leadsId);
}
