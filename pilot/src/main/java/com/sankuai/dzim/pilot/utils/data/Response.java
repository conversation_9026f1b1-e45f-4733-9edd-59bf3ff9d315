package com.sankuai.dzim.pilot.utils.data;

public class Response<T> {

    private static int SUCCESS = 200;

    private static int FAILED = 500;

    /**
     * 基本错误代码
     */
    private int code = 500;
    /**
     * 描述
     */
    private String msg;
    /**
     * 业务数据
     */
    private T data;

    public Response() {
    }

    public Response(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Response(int code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public T getData() {
        return data;
    }

    public boolean isSuccess(){
        return code == SUCCESS;
    }

    public static <T> Response<T> success(String msg) {
        return new Response<>(SUCCESS, msg, null);
    }

    public static <T> Response<T> success() {
        return new Response<>(SUCCESS, "成功", null);
    }

    public static <T> Response<T> success(T data) {
        return new Response<>(SUCCESS, "成功", data);
    }

    public static <T> Response<T> error(String msg) {
        return new Response<>(FAILED, msg);
    }

    public static <T> Response<T> error(int code, String errorMsg) {
        return new Response<>(code, errorMsg);
    }
}
