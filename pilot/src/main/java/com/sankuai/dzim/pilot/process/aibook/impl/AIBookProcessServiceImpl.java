package com.sankuai.dzim.pilot.process.aibook.impl;

import com.dianping.account.dto.UserAccountDTO;
import com.dianping.cat.Cat;
import com.dianping.gis.remote.dto.RegionInfoDTO;
import com.dianping.gis.remote.enums.RegionType;
import com.dianping.gis.remote.service.RegionInfoService;
import com.dianping.poi.bizhour.dto.BizForecastDTO;
import com.dianping.poi.mtDto.MtLocationDTO;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.dianping.vc.enums.VCPlatformEnum;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.dianping.zebra.group.router.ZebraForceMasterHelper;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.nibmp.mem.common.resource.enums.CooperationStatusEnum;
import com.meituan.nibmp.mem.vaf.query.thrift.dto.BatchQueryBizCooperateInfoReqDTO;
import com.meituan.nibmp.mem.vaf.query.thrift.dto.BizCooperateInfoDTO;
import com.meituan.nibmp.mem.vaf.query.thrift.enums.MemTypeEnum;
import com.meituan.service.mobile.group.geo.bean.AreaInfo;
import com.meituan.service.mobile.group.geo.bean.AreaInfoTypeEnum;
import com.meituan.service.mobile.group.geo.bean.SubwayLine;
import com.meituan.service.mobile.group.geo.bean.SubwayStation;
import com.meituan.service.mobile.group.geo.service.AreaService;
import com.meituan.service.mobile.group.geo.service.SubwayService;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.douhu.absdk.bean.DouHuRequest;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.acl.*;
import com.sankuai.dzim.pilot.acl.data.AbTestData;
import com.sankuai.dzim.pilot.acl.data.DealDetailData;
import com.sankuai.dzim.pilot.acl.data.ShopCardData;
import com.sankuai.dzim.pilot.acl.data.ShopCategoryData;
import com.sankuai.dzim.pilot.acl.data.req.ProductSearchReq;
import com.sankuai.dzim.pilot.acl.data.req.ShopCardQueryReq;
import com.sankuai.dzim.pilot.api.enums.aibook.BusinessBookStatusEnum;
import com.sankuai.dzim.pilot.api.enums.aibook.TaskStatusEnum;
import com.sankuai.dzim.pilot.dal.entity.aibook.AIBookCallCntEntity;
import com.sankuai.dzim.pilot.dal.entity.aibook.AIBookCallDurationEntity;
import com.sankuai.dzim.pilot.dal.entity.aibook.AIBookDetailEntity;
import com.sankuai.dzim.pilot.dal.entity.aibook.AIBookTaskEntity;
import com.sankuai.dzim.pilot.api.enums.assistant.PlatformEnum;
import com.sankuai.dzim.pilot.dal.pilotdao.aibook.AIBookCallCntDAO;
import com.sankuai.dzim.pilot.dal.pilotdao.aibook.AIBookCallDurationDAO;
import com.sankuai.dzim.pilot.dal.pilotdao.aibook.AIBookDetailDAO;
import com.sankuai.dzim.pilot.dal.pilotdao.aibook.AIBookTaskDAO;
import com.sankuai.dzim.pilot.dal.respository.aibook.AIBookTaskRepositoryService;
import com.sankuai.dzim.pilot.dal.respository.aibook.data.AIBookTaskE;
import com.sankuai.dzim.pilot.gateway.api.aibook.AIBookCancelInterruptMAPI;
import com.sankuai.dzim.pilot.gateway.api.aibook.AIBookSubmitMAPI;
import com.sankuai.dzim.pilot.gateway.api.aibook.data.*;
import com.sankuai.dzim.pilot.process.aibook.AIBookDispatchProcessService;
import com.sankuai.dzim.pilot.process.aibook.AIBookProcessService;
import com.sankuai.dzim.pilot.process.aibook.data.*;
import com.sankuai.dzim.pilot.utils.*;
import com.sankuai.dztheme.shop.vo.Label;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import com.sankuai.sinai.data.api.dto.MtPoiDTO;
import com.sankuai.sinai.data.api.util.MtPoiUtil;
import com.sankuai.wpt.user.retrieve.thrift.message.UserModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AIBookProcessServiceImpl implements AIBookProcessService {

    private static final Cache<String, List<NaviVO>> positionCache = CacheBuilder.newBuilder()
            .expireAfterWrite(1, TimeUnit.HOURS).maximumSize(2048).recordStats().build();

    @Resource
    private AreaService areaService;

    @Autowired
    private LionConfigUtil lionConfigUtil;

    @Resource
    RegionInfoService regionInfoService;

    @Resource
    private SubwayService subwayService;

    @Autowired
    private ProductAclService productAclService;

    @Autowired
    private AIBookTaskRepositoryService aiBookTaskRepositoryService;

    @Resource
    private UserAclService userAclService;

    @Resource
    private ShopAclService shopAclService;

    @Resource
    private AIBookTaskDAO aiBookTaskDAO;

    @Resource
    private AIBookDetailDAO aiBookDetailDAO;

    @Autowired
    private TokenAccessAclService tokenAccessAclService;

    @Resource(name = "redisClient")
    private RedisStoreClient redisClient;

    @Resource
    private AIBookDispatchProcessService aiBookDispatchProcessService;

    @Resource
    private AbTestAclService abTestAclService;

    @Resource
    private MtRgcAclService mtRgcAclService;

    @Resource
    private AIBookCallCntDAO aiBookCallCntDAO;

    @Resource
    private AIBookCallDurationDAO aiBookCallDurationDAO;

    @ConfigValue(key = "com.sankuai.mim.pilot.mock.book.shopid", defaultValue = "0")
    private long mockBookShopId;

    // 预约来源为公域入口
    public static final int PUBLIC_BOOK_SOURCE = 1;

    // 预约来源为私域入口
    public static final int PRIVATE_BOOK_SOURCE = 2;

    // 美团落地页跳链
    private static final String MT_AI_BOOK_LANDING_PAGE_URL = "imeituan://www.meituan.com/mrn?mrn_biz=gcbu&mrn_entry=mrn-gc-booking-ai-agent&mrn_component=booking-ai-agent&shopid=%d&source=%s&catid=%d";

    // 点评落地页跳链
    private static final String DP_AI_BOOK_LANDING_PAGE_URL = "dianping://mrn?mrn_biz=gcbu&mrn_entry=mrn-gc-booking-ai-agent&mrn_component=booking-ai-agent&shopid=%d&source=%s&catid=%d";

    private static final String POLLING_TIME_PERIOD = "尽快到店";

    // 轮询状态。1-未在轮询，2-轮询中，3-轮询已结束
    public static int POLLING_NOT_EXECUTING_STATUS = 1;
    public static int POLLING_EXECUTING_STATUS = 2;
    public static int POLLING_ENDED_STATUS = 3;

    private static final String TIME_PERIOD_V2 = "1";

    public static final String TIME_PERIOD_V3 = "2";


    @Override
    public AIBookDetailVO queryAIBookDetail(AIBookDetailQueryRequest request) {
        try {
            AIBookDetailVO aiBookDetailVO = new AIBookDetailVO();
            // 1.查询最近预约任务 和 请求指定任务
            AIBookTaskEntity taskEntity;
            if (request.getTaskId() > 0) {
                taskEntity = aiBookTaskDAO.getById(request.getTaskId());
            } else {
                taskEntity = aiBookTaskRepositoryService.fetchLatestBookTask(request.getUserId(), request.getPlatform());
            }
            BookPageFixContentRequest fixContentRequest = buildFixContentRequest(request, taskEntity);

            // 2.填充预约状态详情数据
            fillBookDetail(taskEntity, request, aiBookDetailVO, fixContentRequest);

            // 3.填充标题、副标题、图片
            fillTileAndSubTitle(aiBookDetailVO, fixContentRequest);

            // 4.填充预约中心跳链
            fillBookCenterJumpUrl(request.getUserId(), request.getPlatform(), aiBookDetailVO);

            // 5.查询后置处理
            aiBookDetailQueryPost(request, taskEntity);

            logAndMetricBookDetailQuery(request, aiBookDetailVO);
            return aiBookDetailVO;
        } catch (Exception e) {
            log.error("queryAIBookDetail Error, request: {} ", JsonCodec.encodeWithUTF8(request), e);
        }
        return new AIBookDetailVO();
    }


    private void logAndMetricBookDetailQuery(AIBookDetailQueryRequest request, AIBookDetailVO aiBookDetailVO) {
        Map<String, String> tags = Maps.newHashMap();
        tags.put("platform", String.valueOf(request.getPlatform()));
        CatUtils.logMetricSucc("QueryAIBookDetail", tags);
        log.info("QueryAIBookDetail request = {}, response = {}", JsonCodec.encodeWithUTF8(request), JsonCodec.encodeWithUTF8(aiBookDetailVO));
    }

    private void aiBookDetailQueryPost(AIBookDetailQueryRequest request, AIBookTaskEntity aiBookTaskEntity) {
        // 预约失败查看一次后设置为已完成
        if (aiBookTaskEntity != null && aiBookTaskEntity.getTaskStatus() == TaskStatusEnum.BOOK_FAILED.getCode() &&
                request.getNeedUpdateFailed() == 1 && request.getTaskId() <= 0) {
            aiBookTaskDAO.updateTaskStatus(aiBookTaskEntity.getId(), TaskStatusEnum.BOOK_FAILED_EXPIRED.getCode());
        }
    }

    private void fillBookCenterJumpUrl(long userId, int platform, AIBookDetailVO aiBookDetailVO) {
        List<AIBookTaskEntity> bookTaskEntities = aiBookTaskDAO.queryBookTaskList(userId, platform);
        List<AIBookTaskEntity> succBookTaskEntities = CollectionUtils.emptyIfNull(bookTaskEntities).stream()
                .filter(entity -> (entity.getTaskStatus() == TaskStatusEnum.BOOK_SUCCESS.getCode() || entity.getTaskStatus() == TaskStatusEnum.CANCEL.getCode()))
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(succBookTaskEntities)) {
            aiBookDetailVO.setBookCenterJumpUrl(platform == PlatformEnum.DP.getType() ? lionConfigUtil.getDpBookCenterJumpUrl() : lionConfigUtil.getMtBookCenterJumpUrl());
        }
    }

    private void fillTileAndSubTitle(AIBookDetailVO aiBookDetailVO, BookPageFixContentRequest fixContentRequest) {
        BookPageFixedContentConfigData bookPageFixedContentConfigData = lionConfigUtil.getBookPageFixedContentConfig();

        String title = bookPageFixedContentConfigData.getTitle(fixContentRequest);
        String subTitle = bookPageFixedContentConfigData.getSubTitle(fixContentRequest);
        String titlePic = bookPageFixedContentConfigData.getTitlePic();
        aiBookDetailVO.setTitle(StringUtils.defaultString(title));
        aiBookDetailVO.setSubTitle(StringUtils.defaultString(subTitle));
        aiBookDetailVO.setTitlePic(titlePic);
    }

    private BookPageFixContentRequest buildFixContentRequest(AIBookDetailQueryRequest request, AIBookTaskEntity aiBookTaskEntity) {
        BookPageFixContentRequest fixContentRequest = new BookPageFixContentRequest();
        Integer bookTaskStatus = Optional.ofNullable(aiBookTaskEntity).map(AIBookTaskEntity::getTaskStatus).orElse(null);
        Long taskId = Optional.ofNullable(aiBookTaskEntity).map(AIBookTaskEntity::getId).orElse(null);
        boolean hasTargetShopLatestBook = checkHasTargetShop(aiBookTaskEntity);
        boolean hasTargetShopFromRequest = request.getShopId() > 0;
        boolean targetShopBookResult = getTargetShopBookResult(aiBookTaskEntity);
        boolean overBookTime = isBookOverTime(aiBookTaskEntity);
        Integer bookType = Optional.ofNullable(aiBookTaskEntity).map(AIBookTaskEntity::getBookType).orElse(null);
        Integer leadsStatus = aiBookTaskRepositoryService.queryBookLeadsStatus(taskId);
        Date bookTime = Optional.ofNullable(aiBookTaskEntity).map(AIBookTaskEntity::getBookTime).orElse(null);

        fixContentRequest.setBookTime(bookTime);
        fixContentRequest.setBookTaskStatus(bookTaskStatus);
        fixContentRequest.setOverBookTime(overBookTime);
        fixContentRequest.setTargetShopBookSucc(targetShopBookResult);
        fixContentRequest.setHasTargetShopLatestBook(hasTargetShopLatestBook);
        fixContentRequest.setHasTargetShopFromRequest(hasTargetShopFromRequest);
        fixContentRequest.setBookProject(AIBookTypeEnum.getDesc(bookType));
        fixContentRequest.setLeadsStatus(leadsStatus);
        return fixContentRequest;
    }

    private boolean checkHasTargetShop(AIBookTaskEntity aiBookTaskEntity) {
        if (aiBookTaskEntity == null) {
            return false;
        }
        return Optional.ofNullable(aiBookTaskEntity.getShopId()).orElse(0L) != 0;
    }

    private boolean getTargetShopBookResult(AIBookTaskEntity aiBookTaskEntity) {
        if (aiBookTaskEntity == null || aiBookTaskEntity.getShopId() == null || aiBookTaskEntity.getShopId() == 0) {
            return false;
        }

        ZebraForceMasterHelper.forceMasterInLocalContext();
        AIBookDetailEntity aiBookDetailEntity = aiBookDetailDAO.queryByShopId(aiBookTaskEntity.getId(), aiBookTaskEntity.getShopId(), aiBookTaskEntity.getPlatform());
        if (aiBookDetailEntity != null && aiBookDetailEntity.getCallResult() == TaskCallResultEnum.BOOK_SUCCESS.getCode()) {
            return true;
        }
        return false;
    }

    private boolean isBookOverTime(AIBookTaskEntity aiBookTaskEntity) {
        if (aiBookTaskEntity == null || aiBookTaskEntity.getBookTime() == null) {
            return false;
        }

        Date now = new Date();
        return now.after(aiBookTaskEntity.getBookTime());
    }

    private void fillBookDetail(AIBookTaskEntity aiBookTaskEntity,
                                AIBookDetailQueryRequest request, AIBookDetailVO aiBookDetailVO,
                                BookPageFixContentRequest fixContentRequest) {

        if (request.getTaskId() <= 0) {
            // 请求中无taskId，按落地页逻辑拼装详情数据
            fillBookDetailLanding(aiBookTaskEntity, request, aiBookDetailVO, fixContentRequest);
            return;
        }

        // 按小助手消息卡片逻辑拼装详情数据
        fillBookDetailAssistant(aiBookTaskEntity, request, aiBookDetailVO, fixContentRequest);
    }

    private void fillBookDetailAssistant(AIBookTaskEntity aiBookTaskEntity,
                                         AIBookDetailQueryRequest request, AIBookDetailVO aiBookDetailVO,
                                         BookPageFixContentRequest fixContentRequest) {
        Integer businessBookStatus = queryBusinessBookStatusAssistant(aiBookTaskEntity);
        aiBookDetailVO.setBookStatus(businessBookStatus);
        if (businessBookStatus == null) {
            return;
        }
        if (businessBookStatus == BusinessBookStatusEnum.INITIAL.getCode()) {
            AIBookFormDataVO AIBookFormDataVO = buildAIBookFormDataVO(request);
            aiBookDetailVO.setBookFormData(AIBookFormDataVO);
        }

        if (businessBookStatus == BusinessBookStatusEnum.POLLING.getCode() ||
                businessBookStatus == BusinessBookStatusEnum.POLLING_CANCEL.getCode()) {
            AIBookProgressData aiBookProgressVO = buildAIBookPollingProgressData(aiBookTaskEntity, request.getVersion());
            aiBookDetailVO.setBookPollingData(aiBookProgressVO);
        }

        if (businessBookStatus == BusinessBookStatusEnum.DELAY.getCode()) {
            AIBookProgressData aiBookProgressVO = buildAIBookDelayProgressData(aiBookTaskEntity, request.getVersion());
            aiBookDetailVO.setBookPollingData(aiBookProgressVO);
        }

        if (businessBookStatus == BusinessBookStatusEnum.SUCCESS.getCode()) {
            AIBookSuccDataVO AIBookSuccDataVO = buildBookSuccData(request, aiBookTaskEntity, fixContentRequest);
            aiBookDetailVO.setBookSuccData(AIBookSuccDataVO);
        }

        if (businessBookStatus == BusinessBookStatusEnum.CANCEL.getCode()) {
            AIBookSuccDataVO AIBookSuccDataVO = buildBookSuccessCanceledData(request, aiBookTaskEntity, fixContentRequest);
            aiBookDetailVO.setBookSuccData(AIBookSuccDataVO);
        }

        if (businessBookStatus == BusinessBookStatusEnum.FAIL.getCode()) {
            AIBookFailDataVO aiBookFailDataVO = buildBookFailData(aiBookTaskEntity, fixContentRequest);
            aiBookDetailVO.setBookFailData(aiBookFailDataVO);
        }
    }

    private void fillBookDetailLanding(AIBookTaskEntity aiBookTaskEntity, AIBookDetailQueryRequest request, AIBookDetailVO aiBookDetailVO, BookPageFixContentRequest fixContentRequest) {
        Integer businessBookStatus = queryBusinessBookStatus(aiBookTaskEntity);
        aiBookDetailVO.setBookStatus(businessBookStatus);
        if (businessBookStatus == null) {
            return;
        }

        if (businessBookStatus == BusinessBookStatusEnum.INITIAL.getCode() || businessBookStatus == BusinessBookStatusEnum.FAIL.getCode()) {
            AIBookFormDataVO AIBookFormDataVO = buildAIBookFormDataVO(request);
            aiBookDetailVO.setBookFormData(AIBookFormDataVO);
        }

        if (businessBookStatus == BusinessBookStatusEnum.POLLING.getCode()) {
            AIBookProgressData aiBookProgressVO = buildAIBookPollingProgressData(aiBookTaskEntity, request.getVersion());
            aiBookDetailVO.setBookPollingData(aiBookProgressVO);
        }

        if (businessBookStatus == BusinessBookStatusEnum.DELAY.getCode()) {
            AIBookProgressData aiBookProgressVO = buildAIBookDelayProgressData(aiBookTaskEntity, request.getVersion());
            aiBookDetailVO.setBookPollingData(aiBookProgressVO);
        }

        if (businessBookStatus == BusinessBookStatusEnum.SUCCESS.getCode()) {
            AIBookSuccDataVO AIBookSuccDataVO = buildBookSuccData(request, aiBookTaskEntity, fixContentRequest);
            aiBookDetailVO.setBookSuccData(AIBookSuccDataVO);
        }

        if (businessBookStatus == BusinessBookStatusEnum.FAIL.getCode()) {
            AIBookFailDataVO aiBookFailDataVO = buildBookFailData(aiBookTaskEntity, fixContentRequest);
            aiBookDetailVO.setBookFailData(aiBookFailDataVO);
        }
    }

    private AIBookProgressData buildAIBookDelayProgressData(AIBookTaskEntity aiBookTaskEntity, String version) {
        AIBookProgressData bookProgressData = new AIBookProgressData();
        if (aiBookTaskEntity == null) {
            return null;
        }

        Date now = new Date();
        Integer leadsStatus = aiBookTaskRepositoryService.queryBookLeadsStatus(aiBookTaskEntity.getId());
        String shopName = queryShopName(aiBookTaskEntity.getShopId(), aiBookTaskEntity.getPlatform());
        // 展示文案
        BookPageFixedContentConfigData fixedContentConfigData = lionConfigUtil.getBookPageFixedContentConfig();
        DispatchConfigData dispatchConfigData = lionConfigUtil.getDispatchConfigData();
        String pollingBeginTime = getPollingBeginTime(dispatchConfigData);
        String remainTime = DateUtils.getHourOfDay(now) >= dispatchConfigData.getDispatchEndHour() ? "明天上午" : "今天上午";
        String progressDesc = String.format(fixedContentConfigData.getPollingDelayProgressDesc(), pollingBeginTime);

        bookProgressData.setBookProject(AIBookTypeEnum.getDesc(aiBookTaskEntity.getBookType()));
        bookProgressData.setBookTime(buildBookTimeDesc(aiBookTaskEntity.getBookEndTime(), aiBookTaskEntity.getBookPeriodDesc(), version));
        bookProgressData.setTip(fixedContentConfigData.getTip(aiBookTaskEntity.getTaskStatus(), leadsStatus));
        bookProgressData.setPosition(buildPositionDesc(aiBookTaskEntity.getPosition()));
        bookProgressData.setShopName(shopName);
        bookProgressData.setRecommendSwitch(aiBookTaskEntity.getRecommendSwitch());
        bookProgressData.setSource(aiBookTaskEntity.getShopId() > 0 ? PRIVATE_BOOK_SOURCE : PUBLIC_BOOK_SOURCE);
        bookProgressData.setProgressDesc(progressDesc);
        bookProgressData.setRemainTime(remainTime);
        bookProgressData.setPollingBeginTime(pollingBeginTime);
        bookProgressData.setPollingStatus(POLLING_NOT_EXECUTING_STATUS);
        return bookProgressData;
    }

    private String buildPositionDesc(String position) {
        if (StringUtils.isEmpty(position)) {
            return StringUtils.EMPTY;
        }
        String regPattern = "^\\d+km$";
        Pattern pattern = Pattern.compile(regPattern);
        Matcher matcher = pattern.matcher(position);
        if (matcher.matches()) {
            return "附近" + position + "内";
        }
        return position;
    }

    private String buildBookTimeDesc(Date bookEndTime, String bookPeriodDesc, String version) {
        if (bookEndTime == null) {
            return StringUtils.EMPTY;
        }
        if (StringUtils.defaultString(version).equals(TIME_PERIOD_V3)) {
            return DateUtils.getDateDesc(bookEndTime) + " " + bookPeriodDesc;
        }
        if (StringUtils.defaultString(version).equals(TIME_PERIOD_V2)) {
            return DateUtils.getDateDesc(bookEndTime) + bookPeriodDesc;
        }
        return DateUtils.getDateDesc(bookEndTime);
    }

    private String getPollingBeginTime(DispatchConfigData dispatchConfigData) {
        Date now = new Date();
        String dayDesc = DateUtils.getHourOfDay(now) >= dispatchConfigData.getDispatchEndHour() ? "明早" : "今早";
        return String.format("%s%2d:00", dayDesc, dispatchConfigData.getDispatchBeginHour());
    }

    private AIBookProgressData buildAIBookPollingProgressData(AIBookTaskEntity aiBookTaskEntity, String version) {
        AIBookProgressData bookProgressData = new AIBookProgressData();
        if (aiBookTaskEntity == null) {
            return null;
        }

        List<AIBookDetailEntity> aiBookDetailEntities = CollectionUtils.emptyIfNull(aiBookDetailDAO.getByTaskId(aiBookTaskEntity.getId())).stream()
                .sorted(Comparator.comparingLong(AIBookDetailEntity::getId)).collect(Collectors.toList());
        BookPageFixedContentConfigData fixedContentConfigData = lionConfigUtil.getBookPageFixedContentConfig();
        // 外呼进度
        int totalCallCount = aiBookDetailEntities.size();
        int callOrder = aiBookDetailEntities.stream().filter(entity -> entity.getCallResult() != TaskCallResultEnum.INITIAL.getCode()).collect(Collectors.toList()).size();
        // 展示文案
        Integer leadsStatus = aiBookTaskRepositoryService.queryBookLeadsStatus(aiBookTaskEntity.getId());
        String shopName = queryShopName(aiBookTaskEntity.getShopId(), aiBookTaskEntity.getPlatform());
        String remainTime = queryPollingRemainTime(aiBookTaskEntity);

        bookProgressData.setBookProject(AIBookTypeEnum.getDesc(aiBookTaskEntity.getBookType()));
        bookProgressData.setBookTime(POLLING_TIME_PERIOD.equals(aiBookTaskEntity.getBookPeriodDesc()) ?
                POLLING_TIME_PERIOD : buildBookTimeDesc(aiBookTaskEntity.getBookEndTime(), aiBookTaskEntity.getBookPeriodDesc(), version));
        bookProgressData.setTip(fixedContentConfigData.getTip(aiBookTaskEntity.getTaskStatus(), leadsStatus));
        bookProgressData.setPosition(buildPositionDesc(aiBookTaskEntity.getPosition()));
        bookProgressData.setShopName(shopName);
        bookProgressData.setRemainTime(remainTime);
        bookProgressData.setCallOrder(callOrder);
        bookProgressData.setTotalCallCount(totalCallCount);
        bookProgressData.setRecommendSwitch(aiBookTaskEntity.getRecommendSwitch());
        bookProgressData.setSource(aiBookTaskEntity.getShopId() > 0 ? PRIVATE_BOOK_SOURCE : PUBLIC_BOOK_SOURCE);
        bookProgressData.setProgressDesc(getPollingDesc(callOrder, totalCallCount, aiBookTaskEntity));
        bookProgressData.setPollingStatus(POLLING_EXECUTING_STATUS);
        return bookProgressData;
    }

    private Integer queryBusinessBookStatus(AIBookTaskEntity aiBookTaskEntity) {
        Long taskId = Optional.ofNullable(aiBookTaskEntity).map(AIBookTaskEntity::getId).orElse(null);
        Integer bookTaskStatus = Optional.ofNullable(aiBookTaskEntity).map(AIBookTaskEntity::getTaskStatus).orElse(null);
        boolean overBookTime = isBookOverTime(aiBookTaskEntity);
        Integer leadsStatus = aiBookTaskRepositoryService.queryBookLeadsStatus(taskId);
        return BusinessBookStatusEnum.getBusinessBookStatus(bookTaskStatus, overBookTime, leadsStatus);
    }

    private Integer queryBusinessBookStatusAssistant(AIBookTaskEntity aiBookTaskEntity) {
        Long taskId = Optional.ofNullable(aiBookTaskEntity).map(AIBookTaskEntity::getId).orElse(null);
        Integer bookTaskStatus = Optional.ofNullable(aiBookTaskEntity).map(AIBookTaskEntity::getTaskStatus).orElse(null);
        boolean overBookTime = isBookOverTime(aiBookTaskEntity);
        Integer leadsStatus = aiBookTaskRepositoryService.queryBookLeadsStatus(taskId);
        return BusinessBookStatusEnum.getBusinessBookStatusAssistant(bookTaskStatus, overBookTime, leadsStatus);
    }

    private AIBookFailDataVO buildBookFailData(AIBookTaskEntity aiBookTaskEntity, BookPageFixContentRequest fixContentRequest) {
        BookPageFixedContentConfigData fixedContentConfig = lionConfigUtil.getBookPageFixedContentConfig();
        AIBookFailDataVO aiBookFailDataVO = new AIBookFailDataVO();
        aiBookFailDataVO.setBookPeriodDesc(DateUtils.covertDateStr(aiBookTaskEntity.getBookEndTime(), "MM-dd HH:mm"));
        aiBookFailDataVO.setBookProject(AIBookTypeEnum.getDesc(aiBookTaskEntity.getBookType()));
        aiBookFailDataVO.setFailReason(fixedContentConfig.getReason(fixContentRequest));
        aiBookFailDataVO.setFailTip(fixedContentConfig.getSubTitle(fixContentRequest));
        fillShopInfo(aiBookFailDataVO, aiBookTaskEntity);
        return aiBookFailDataVO;
    }

    private void fillShopInfo(AIBookFailDataVO aiBookFailDataVO, AIBookTaskEntity aiBookTaskEntity) {
        long shopId = Optional.ofNullable(aiBookTaskEntity.getShopId()).orElse(0L);
        String shopName = queryShopName(shopId, aiBookTaskEntity.getPlatform());
        int source = shopId > 0 ? PRIVATE_BOOK_SOURCE : PUBLIC_BOOK_SOURCE;

        aiBookFailDataVO.setShopId(shopId);
        aiBookFailDataVO.setShopName(shopName);
        aiBookFailDataVO.setSource(source);
    }

    private AIBookSuccDataVO buildBookSuccData(AIBookDetailQueryRequest request, AIBookTaskEntity aiBookTaskEntity, BookPageFixContentRequest fixContentRequest) {
        // 查询成功明细
        try {
            ZebraForceMasterHelper.forceMasterInLocalContext();
            AIBookDetailEntity aiBookDetailEntity = aiBookDetailDAO.findSuccessBookByTaskId(aiBookTaskEntity.getId());
            if (aiBookDetailEntity == null) {
                log.error("未查询到预约成功记录, , taskId:{}", aiBookTaskEntity.getId());
                return null;
            }

            // mock shopid
            if (mockBookShopId > 0) {
                aiBookDetailEntity.setShopId(mockBookShopId);
            }

            // 查询门店卡片
            ShopCardData shopCard = shopAclService.getShopCard(buildShopCardRequest(request, aiBookDetailEntity));

            // 查团购列表
            ProductSearchReq productSearchReq = buildSearchReq(request, aiBookDetailEntity.getShopId(), aiBookTaskEntity.getBookType());
            List<DealDetailData> dealDetailData = productAclService.searchProduct(productSearchReq);

            return buildBookSuccVO(request, aiBookTaskEntity, aiBookDetailEntity, shopCard, fixContentRequest, dealDetailData);
        } catch (Exception e) {
            log.error("buildBookSuccData error, taskId:{}", aiBookTaskEntity.getId(), e);
        } finally {
            ZebraForceMasterHelper.clearLocalContext();
        }
        return null;
    }

    private AIBookSuccDataVO buildBookSuccessCanceledData(AIBookDetailQueryRequest request, AIBookTaskEntity aiBookTaskEntity, BookPageFixContentRequest fixContentRequest) {
        // 查询成功明细
        try {
            ZebraForceMasterHelper.forceMasterInLocalContext();
            List<AIBookDetailEntity> aiBookDetailEntities = aiBookDetailDAO.queryByTaskIdsAndCallResults(Lists.newArrayList(aiBookTaskEntity.getId()),
                    Lists.newArrayList(TaskCallResultEnum.CANCEL_SUCCESS.getCode(), TaskCallResultEnum.CANCEL_FAILED.getCode(),
                            TaskCallResultEnum.CANCEL_SKIP.getCode()));


            if (CollectionUtils.isEmpty(aiBookDetailEntities)) {
                log.error("未查询到预约成功后取消记录, , taskId:{}", aiBookTaskEntity.getId());
                return null;
            }

            AIBookDetailEntity aiBookDetailEntity = aiBookDetailEntities.get(0);
            // mock shopid
            if (mockBookShopId > 0) {
                aiBookDetailEntity.setShopId(mockBookShopId);
            }

            // 查询门店卡片
            ShopCardData shopCard = shopAclService.getShopCard(buildShopCardRequest(request, aiBookDetailEntity));

            // 查团购列表
            ProductSearchReq productSearchReq = buildSearchReq(request, aiBookDetailEntity.getShopId(), aiBookTaskEntity.getBookType());
            List<DealDetailData> dealDetailData = productAclService.searchProduct(productSearchReq);

            return buildBookSuccVO(request, aiBookTaskEntity, aiBookDetailEntity, shopCard, fixContentRequest, dealDetailData);
        } catch (Exception e) {
            log.error("buildBookSuccData error, taskId:{}", aiBookTaskEntity.getId(), e);
        } finally {
            ZebraForceMasterHelper.clearLocalContext();
        }
        return null;
    }

    private AIBookSuccDataVO buildBookSuccVO(AIBookDetailQueryRequest request, AIBookTaskEntity aiBookTaskEntity, AIBookDetailEntity aiBookDetailEntity,
                                             ShopCardData shopCard, BookPageFixContentRequest fixContentRequest, List<DealDetailData> dealDetailData) {
        BookPageFixedContentConfigData fixedContentConfig = lionConfigUtil.getBookPageFixedContentConfig();
        Date bookTime = aiBookDetailEntity.getBookTime();
        AIBookSuccDataVO aIBookSuccDataVO = new AIBookSuccDataVO();
        aIBookSuccDataVO.setBookTime(DateUtils.formatChineseDate(bookTime));
        aIBookSuccDataVO.setBookTimeDesc(DateUtils.getDateDesc(bookTime) + DateUtils.covertDateStr(bookTime, "HH:mm"));
        aIBookSuccDataVO.setBookProject(Optional.ofNullable(aiBookTaskEntity.getBookType()).map(IntentProjectCodeEnum::getByCode).orElse(IntentProjectCodeEnum.HAIRDRESSING).getDesc());
        aIBookSuccDataVO.setBookPhone(PhoneUtils.maskPhoneNo(tokenAccessAclService.getPlainMobile(aiBookTaskEntity.getUserPhone())));
        aIBookSuccDataVO.setMerchantRemark(getMerchantRemark(aiBookDetailEntity.getExtraData()));
        aIBookSuccDataVO.setShopCard(buildShopCardVO(shopCard));
        if (CollectionUtils.isNotEmpty(dealDetailData)) {
            // 按销量排序倒排
            List<DealVO> deals = dealDetailData.stream().sorted(Comparator.comparing(DealDetailData::getSale).reversed()).limit(10).filter(Objects::nonNull)
                    .map(this::convertDealDetailData2DealVO)
                    .collect(Collectors.toList());
            aIBookSuccDataVO.setDealList(deals);
        }
        aIBookSuccDataVO.setSuccReason(fixedContentConfig.getReason(fixContentRequest));
        aIBookSuccDataVO.setSuccTip(fixedContentConfig.getSubTitle(fixContentRequest));

        aIBookSuccDataVO.setMapUrl(getMapUrl(request, aiBookDetailEntity));
        aIBookSuccDataVO.setBookDetailUrl(getBookDetailUrl(request, aiBookDetailEntity));
        aIBookSuccDataVO.setLeadsId(aiBookDetailEntity.getBookLeadsId());
        return aIBookSuccDataVO;
    }

    private String getBookDetailUrl(AIBookDetailQueryRequest request, AIBookDetailEntity aiBookDetailEntity) {
        StringBuilder url = new StringBuilder(request.getPlatform() == PlatformEnum.MT.getType() ?
                "imeituan://www.meituan.com/mrn?mrn_biz=gc&mrn_entry=vg-mrn-reserve&mrn_component=reserveresult"
                : "dianping://mrn?mrn_biz=gc&mrn_entry=vg-mrn-reserve&mrn_component=reserveresult");
        url.append("&").append("leadsId").append("=").append(aiBookDetailEntity.getBookLeadsId());

        long mtShopId = getMtShopId(aiBookDetailEntity.getShopId(), request.getPlatform());
        long dpShopId = getDpShopId(aiBookDetailEntity.getShopId(), request.getPlatform());
        if (mtShopId > 0) {
            url.append("&").append("mtShopId").append("=").append(mtShopId);
        }
        if (dpShopId > 0) {
            url.append("&").append("dpShopId").append("=").append(dpShopId);
        }
        return url.toString();
    }

    private long getMtShopId(long shopId, int platform) {
        if (platform == VCPlatformEnum.MT.getType()) {
            return shopId;
        }
        return shopAclService.loadMTShopIdByDPShopId(shopId);
    }

    private long getDpShopId(long shopId, int platform) {
        if (platform == VCPlatformEnum.MT.getType()) {
            return shopAclService.loadDpShopIdByMtShopId(shopId);
        }
        return shopId;
    }

    private String getMapUrl(AIBookDetailQueryRequest request, AIBookDetailEntity aiBookDetailEntity) {
        if (aiBookDetailEntity.getPlatform() == PlatformEnum.DP.getType()) {
            return buildDpMapUrl(request, aiBookDetailEntity);
        }

        return buildMtMapUrl(request, aiBookDetailEntity);
    }

    private String buildDpMapUrl(AIBookDetailQueryRequest request, AIBookDetailEntity aiBookDetailEntity) {
        DpPoiDTO shopInfo = shopAclService.getShopInfo(aiBookDetailEntity.getShopId());
        if (shopInfo == null) {
            return StringUtils.EMPTY;
        }

        StringBuilder url = new StringBuilder("dianping://picassobox?notitlebar=1");
        url.append("&").append(urlEncoder("picassoid")).append("=").append(urlEncoder("picasso-navigation-map/navigation-map-bundle.js"));
        url.append("&").append(urlEncoder("destpoiid")).append("=").append(aiBookDetailEntity.getShopId());
        url.append("&").append(urlEncoder("destpoiname")).append("=").append(urlEncoder(getShopName(shopInfo)));
        url.append("&").append(urlEncoder("destcoord")).append("=").append(urlEncoder("GCJ02"));
        if (request.getLat() > 0 && request.getLng() > 0) {
            url.append("&").append(urlEncoder("srclat")).append("=").append(request.getLat());
            url.append("&").append(urlEncoder("srclng")).append("=").append(request.getLng());
        }
        if (shopInfo.getLat() > 0 && shopInfo.getLng() > 0) {
            url.append("&").append(urlEncoder("destlat")).append("=").append(shopInfo.getLat());
            url.append("&").append(urlEncoder("destlng")).append("=").append(shopInfo.getLng());
        }
        return url.toString();
    }

    private String getShopName(DpPoiDTO shopInfo) {
        String shopName = "";
        if (StringUtils.isNotBlank(shopInfo.getShopName())) {
            shopName = shopInfo.getShopName();
        }
        if (StringUtils.isNotBlank(shopInfo.getBranchName())) {
            shopName = shopName + "(" + shopInfo.getBranchName() + ")";
        }
        return shopName;
    }

    private String buildMtMapUrl(AIBookDetailQueryRequest request, AIBookDetailEntity aiBookDetailEntity) {
        MtPoiDTO mtShopInfo = shopAclService.getMtShopInfo(aiBookDetailEntity.getShopId());
        if (mtShopInfo == null) {
            return StringUtils.EMPTY;
        }

        StringBuilder url = new StringBuilder("imeituan://www.meituan.com/mapchannel?");
        url.append(urlEncoder("mapsource")).append("=").append(urlEncoder("gcassistant"));
        url.append("&").append(urlEncoder("pagetype")).append("=").append(urlEncoder("1"));
        url.append("&").append(urlEncoder("poi_id")).append("=").append(urlEncoder(String.valueOf(aiBookDetailEntity.getShopId())));
        url.append("&").append(urlEncoder("overseas")).append("=").append(urlEncoder("0"));
        url.append("&").append(urlEncoder("latitude")).append("=").append(urlEncoder(String.valueOf(mtShopInfo.getLatitude())));
        url.append("&").append(urlEncoder("longitude")).append("=").append(urlEncoder(String.valueOf(mtShopInfo.getLongitude())));
        url.append("&").append(urlEncoder("stage")).append("=").append(urlEncoder("1"));
        url.append("&").append(urlEncoder("coordtype")).append("=").append(urlEncoder("0"));
        if (request.getLat() > 0 && request.getLng() > 0) {
            url.append("&").append(urlEncoder("start_latitude")).append("=").append(urlEncoder(String.valueOf(request.getLat())));
            url.append("&").append(urlEncoder("start_longitude")).append("=").append(urlEncoder(String.valueOf(request.getLng())));
        }
        return url.toString();
    }

    private String urlEncoder(String str) {
        try {
            return URLEncoder.encode(str, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            log.error("urlEncoder error, str: {}.", str, e);
        }
        return str;
    }

    private ShopCardVO buildShopCardVO(ShopCardData shopCard) {
        ShopCardVO shopCardVO = new ShopCardVO();
        shopCardVO.setShopId(shopCard.getShopId());
        shopCardVO.setShopName(shopCard.getShopName());
        shopCardVO.setHeadPic(shopCard.getHeadPic());
        shopCardVO.setDetailUrl(shopCard.getDetailUrl());
        shopCardVO.setStar(shopCard.getStar());
        shopCardVO.setScore(shopCard.getScore());
        shopCardVO.setArea(shopCard.getArea());
        shopCardVO.setDistance(shopCard.getDistance());
        if (CollectionUtils.isNotEmpty(shopCard.getTags())) {
            shopCardVO.setTags(buildShopTags(shopCard.getTags()));
        }
        return shopCardVO;
    }

    private List<LabelVO> buildShopTags(List<Label> tags) {
        if (CollectionUtils.isEmpty(tags)) {
            return Collections.emptyList();
        }
        List<LabelVO> labelVOS = Lists.newArrayList();
        for (Label tag : tags) {
            LabelVO labelVO = new LabelVO();
            labelVO.setTitle(tag.getTitle());
            labelVO.setIconUrl(tag.getUrl());
            labelVO.setStyleType(tag.getStyleType());
            labelVO.setTagId(tag.getTagId());
            labelVOS.add(labelVO);
        }
        return labelVOS;
    }

    private String getMerchantRemark(String extraData) {
        if (StringUtils.isBlank(extraData)) {
            return StringUtils.EMPTY;
        }
        AIBookDetailExtraData aiBookDetailExtraData = JsonCodec.decode(extraData, AIBookDetailExtraData.class);
        if (aiBookDetailExtraData == null || StringUtils.isBlank(aiBookDetailExtraData.getShopNote())) {
            return StringUtils.EMPTY;
        }

        return aiBookDetailExtraData.getShopNote();
    }

    private ShopCardQueryReq buildShopCardRequest(AIBookDetailQueryRequest request, AIBookDetailEntity aiBookDetailEntity) {
        ShopCardQueryReq shopCardQueryReq = new ShopCardQueryReq();
        shopCardQueryReq.setShopId(aiBookDetailEntity.getShopId());
        shopCardQueryReq.setPlatform(aiBookDetailEntity.getPlatform());
        shopCardQueryReq.setUserId(request.getUserId());
        shopCardQueryReq.setLat(request.getLat());
        shopCardQueryReq.setLng(request.getLng());
        shopCardQueryReq.setCityId(request.getCityId());
        shopCardQueryReq.setUnionId(request.getUnionId());
        shopCardQueryReq.setPlanId(aiBookDetailEntity.getPlatform() == PlatformEnum.DP.getType() ? "10500933" : "10500932");
        shopCardQueryReq.setDeviceId(request.getDeviceId());
        shopCardQueryReq.setAppVersion(request.getAppVersion());
        Map<String, Object> extra = Maps.newHashMap();
        extra.put("source", "listing");
        long mtUserId = getMtUserId(aiBookDetailEntity.getPlatform(), request.getUserId());
        extra.put("mtUserId", mtUserId);
        shopCardQueryReq.setExtra(extra);
        shopCardQueryReq.setClient(request.getClient());
        return shopCardQueryReq;
    }

    private AIBookProgressVO buildAIBookProgressVO(AIBookTaskEntity aiBookTaskEntity, String version, long taskId) {
        int businessBookStatus = taskId > 0 ? queryBusinessBookStatusAssistant(aiBookTaskEntity) : queryBusinessBookStatus(aiBookTaskEntity);

        AIBookProgressVO aiBookProgressVO = new AIBookProgressVO();
        aiBookProgressVO.setBookStatus(businessBookStatus);
        if (businessBookStatus != BusinessBookStatusEnum.POLLING.getCode() && businessBookStatus != BusinessBookStatusEnum.DELAY.getCode()) {
            AIBookProgressData aiBookProgressData = new AIBookProgressData();
            aiBookProgressData.setPollingStatus(POLLING_ENDED_STATUS);
            aiBookProgressVO.setBookPollingData(aiBookProgressData);
            return aiBookProgressVO;
        }

        if (businessBookStatus == BusinessBookStatusEnum.POLLING.getCode()) {
            aiBookProgressVO.setBookPollingData(buildAIBookPollingProgressData(aiBookTaskEntity, version));
        }
        if (businessBookStatus == BusinessBookStatusEnum.DELAY.getCode()) {
            aiBookProgressVO.setBookPollingData(buildAIBookDelayProgressData(aiBookTaskEntity, version));
        }
        return aiBookProgressVO;
    }

    private String getPollingDesc(int callOrder, int totalCallCount, AIBookTaskEntity aiBookTaskEntity) {
        BookPageFixedContentConfigData fixedContentConfigData = lionConfigUtil.getBookPageFixedContentConfig();
        if (aiBookTaskEntity.getShopId() != null && aiBookTaskEntity.getShopId() > 0) {
            if (callOrder == 1) {
                return fixedContentConfigData.getPrivateFirstShopProcessDesc();
            } else {
                return fixedContentConfigData.getPrivateNonFirstShopProcessDesc();
            }
        }

        String publicProgressDesc = String.format(fixedContentConfigData.getPublicProgressDesc(), totalCallCount, callOrder, totalCallCount);
        return publicProgressDesc;
    }

    private AIBookFormDataVO buildAIBookFormDataVO(AIBookDetailQueryRequest request) {
        DispatchConfigData dispatchConfigData = lionConfigUtil.getDispatchConfigData();
        PositionRequest positionRequest = buildPositionRequest(request.getPlatform(), request.getCityId(), request.getLat(), request.getLng(), request.getLocalCityId());

        List<NaviVO> positions = buildPosition(positionRequest);
        String userPhone = queryUserPhone(request.getUserId(), request.getPlatform());
        String shopName = queryShopName(request.getShopId(), request.getPlatform());
        String pollingBeginTime = getPollingBeginTime(dispatchConfigData);
        List<TimePeriodVO> arrivalTimePeriodList = buildArrivalTimePeriodList(request);

        AIBookFormDataVO bookFormDataVO = new AIBookFormDataVO();
        bookFormDataVO.setDefaultPosition(buildDefaultPosition(request));
        bookFormDataVO.setPosition(positions);
        bookFormDataVO.setArriveShopTimeList(arrivalTimePeriodList);
        bookFormDataVO.setUserPhone(userPhone);
        bookFormDataVO.setShopName(shopName);
        bookFormDataVO.setBookProjectList(buildBookProjectList(request.getSource()));
        bookFormDataVO.setStartBookTime(getCurrentTimeNeedDelay() ? pollingBeginTime : StringUtils.EMPTY);
        return bookFormDataVO;
    }


    private List<TimePeriodVO> buildArrivalTimePeriodList(AIBookDetailQueryRequest request) {

        if (StringUtils.defaultString(request.getVersion()).equals(TIME_PERIOD_V3)) {
            return getArrivalTimePeriodV3();
        }
        if (StringUtils.defaultString(request.getVersion()).equals(TIME_PERIOD_V2)) {
            return getArrivalTimePeriodV2();
        }
        return getArrivalTimePeriod();
    }

    private boolean getCurrentTimeNeedDelay() {
        int hourOfDay = LocalDateTime.now().getHourOfDay();
        DispatchConfigData dispatchConfigData = lionConfigUtil.getDispatchConfigData();
        return hourOfDay >= dispatchConfigData.getDispatchEndHour() || hourOfDay < dispatchConfigData.getDispatchBeginHour();
    }

    private NaviVO buildDefaultPosition(AIBookDetailQueryRequest request) {
        if (isRemoteLocation(request.getCityId(), request.getLocalCityId())
                || isLocationUnauthorized(request.getLocalCityId(), request.getLng(), request.getLat())) {
            return null;
        }
        return lionConfigUtil.getDefaultChoosePosition();
    }

    /**
     * 是否未授权定位
     */
    private boolean isLocationUnauthorized(int localCityId, double lng, double lat) {
        return localCityId <= 0 || (lng <= 0 && lat <= 0);
    }

    /**
     * 是否异地
     */
    private boolean isRemoteLocation(int cityId, int localCityId) {
        return cityId != localCityId;
    }

    private PositionRequest buildPositionRequest(int platform, int cityId, double lat, double lng, int localCityId) {
        PositionRequest request = new PositionRequest();
        request.setPlatform(platform);
        request.setCityId(cityId);
        request.setLng(lng);
        request.setLat(lat);
        request.setLocalCityId(localCityId);
        return request;
    }


    private List<AIBookProjectVO> buildBookProjectList(int source) {
        List<AIBookTypeEnum> sortedAiBookType = Arrays.stream(AIBookTypeEnum.values())
                .sorted(Comparator.comparing(AIBookTypeEnum::getType)).collect(Collectors.toList());

        List<AIBookProjectVO> aiBookProjectVOS = Lists.newArrayList();

        for (AIBookTypeEnum aiBookTypeEnum : sortedAiBookType) {
            AIBookProjectVO aiBookProjectVO = convert2BookProjectVO(aiBookTypeEnum, source);
            aiBookProjectVOS.add(aiBookProjectVO);
        }
        return aiBookProjectVOS;
    }

    private AIBookProjectVO convert2BookProjectVO(AIBookTypeEnum bookTypeEnum, int source) {
        Map<String, List<BookingPriceDetailData>> bookingPriceData = lionConfigUtil.getBookingPriceData();

        AIBookProjectVO aiBookProjectVO = new AIBookProjectVO();
        aiBookProjectVO.setBookType(bookTypeEnum.getType());
        aiBookProjectVO.setBookProject(bookTypeEnum.getDesc());
        // 公域 且 价格开关true 才会展示价格
        if (source == PUBLIC_BOOK_SOURCE && lionConfigUtil.isPublicHairPriceSwitch()) {
            aiBookProjectVO.setBookingPrices(bookingPriceData.get(String.valueOf(bookTypeEnum.getType())));
        }
        return aiBookProjectVO;
    }


    private String queryShopName(long shopId, int platform) {
        if (shopId == 0) {
            return StringUtils.EMPTY;
        }

        long mtShopId = platform == PlatformEnum.MT.getType() ? shopId : shopAclService.loadMTShopIdByDPShopId(shopId);
        MtPoiDTO mtPoiDTO = shopAclService.getMtShopInfo(mtShopId);
        return Optional.ofNullable(mtPoiDTO).map(dto -> MtPoiUtil.getMtPoiName(dto.getName(), dto.getBranchName())).orElse(StringUtils.EMPTY);
    }

    private String queryUserPhone(long userId, int platform) {
        if (platform == PlatformEnum.DP.getType()) {
            UserAccountDTO userAccountDTO = userAclService.getDpUserAccountDTO(userId);
            return Optional.ofNullable(userAccountDTO).map(UserAccountDTO::getMobile).orElse(StringUtils.EMPTY);
        }

        if (platform == PlatformEnum.MT.getType()) {
            UserModel userModel = userAclService.getMtUserModel(userId);
            return Optional.ofNullable(userModel).map(UserModel::getMobile).orElse(StringUtils.EMPTY);
        }
        return StringUtils.EMPTY;
    }

    @Override
    public List<TimePeriodVO> getArrivalTimePeriod() {
        Date now = new Date();
        int currentHour = DateUtils.getHourOfDay(now);
        Map<String, List<TimePeriodConfigData>> timePeriodConfig = lionConfigUtil.getTimePeriodConfig();

        // 对 HashMap 的 key 进行降序排序
        List<String> timePeriodKeys = new ArrayList<>(timePeriodConfig.keySet());
        Collections.sort(timePeriodKeys, (s1, s2) -> Integer.compare(Integer.parseInt(s2), Integer.parseInt(s1)));

        for (String hourKey : timePeriodKeys) {
            if (currentHour >= Integer.parseInt(hourKey)) {
                return buildTimePeriodVOList(timePeriodConfig.get(hourKey));
            }
        }
        return Lists.newArrayList();
    }

    @Override
    public List<TimePeriodVO> getArrivalTimePeriodV2() {
        Date now = new Date();
        int currentHour = DateUtils.getHourOfDay(now);
        Map<String, List<TimePeriodConfigData>> timePeriodConfig = lionConfigUtil.getTimePeriodConfigV2();

        // 对 HashMap 的 key 进行降序排序
        List<String> timePeriodKeys = new ArrayList<>(timePeriodConfig.keySet());
        Collections.sort(timePeriodKeys, (s1, s2) -> Integer.compare(Integer.parseInt(s2), Integer.parseInt(s1)));

        for (String hourKey : timePeriodKeys) {
            if (currentHour >= Integer.parseInt(hourKey)) {
                return buildTimePeriodVOListV2(timePeriodConfig.get(hourKey));
            }
        }
        return Lists.newArrayList();
    }

    @Override
    public List<TimePeriodVO> getArrivalTimePeriodV3() {
        Date now = new Date();
        int currentHour = DateUtils.getHourOfDay(now);
        Map<String, List<TimePeriodConfigData>> timePeriodConfig = lionConfigUtil.getTimePeriodConfigV3();

        // 对 HashMap 的 key 进行降序排序
        List<String> timePeriodKeys = new ArrayList<>(timePeriodConfig.keySet());
        Collections.sort(timePeriodKeys, (s1, s2) -> Integer.compare(Integer.parseInt(s2), Integer.parseInt(s1)));

        for (String hourKey : timePeriodKeys) {
            if (currentHour >= Integer.parseInt(hourKey)) {
                return buildTimePeriodVOListV3(timePeriodConfig.get(hourKey));
            }
        }
        return Lists.newArrayList();
    }

    @Override
    public int cancelAIBook(AIBookCancelRequest request) {

        try {
            // 1.查询最近预约任务
            AIBookTaskEntity latestBookTask = aiBookTaskRepositoryService.fetchLatestBookTask(request.getUserId(), request.getPlatform());
            if (latestBookTask == null) {
                log.info("cancelAIBook error, request = {}", JsonCodec.encode(request), AIBookCancelInterruptMAPI.BOOK_CANCEL_RECENT_TASK_NOT_FOUND);
                return AIBookCancelInterruptMAPI.BOOK_CANCEL_RECENT_TASK_NOT_FOUND;
            }
            // 2.判断任务是否为轮询态、延迟态
            if (!latestBookTask.getTaskStatus().equals(TaskStatusEnum.HAS_TASK_ASKING.getCode())
                    && !latestBookTask.getTaskStatus().equals(TaskStatusEnum.HAS_TASK_DELAY.getCode())) {
                log.info("cancelAIBook error, request = {}", JsonCodec.encode(request), AIBookCancelInterruptMAPI.BOOK_CANCEL_RECENT_TASK_NOT_ASKING);
                return AIBookCancelInterruptMAPI.BOOK_CANCEL_RECENT_TASK_NOT_ASKING;
            }
            // 3.更新为取消状态
            aiBookTaskDAO.updateTaskStatus(latestBookTask.getId(), TaskStatusEnum.CANCEL_INTERUPT.getCode());

        } catch (Exception e) {
            log.error("cancelAIBook error, request = {}", JsonCodec.encode(request), e, AIBookCancelInterruptMAPI.BOOK_CANCEL_RECENT_TASK_EXCEPTION);
            return AIBookCancelInterruptMAPI.BOOK_CANCEL_RECENT_TASK_EXCEPTION;
        }
        return AIBookCancelInterruptMAPI.BOOK_CANCEL_RECENT_TASK_SUCCESS;
    }

    private List<TimePeriodVO> buildTimePeriodVOList(List<TimePeriodConfigData> timePeriodConfigList) {
        return CollectionUtils.emptyIfNull(timePeriodConfigList).stream()
                .sorted(Comparator.comparingLong(TimePeriodConfigData::getId))
                .map(this::convert2TimePeriodVO)
                .collect(Collectors.toList());
    }

    private List<TimePeriodVO> buildTimePeriodVOListV2(List<TimePeriodConfigData> timePeriodConfigList) {
        if (CollectionUtils.isEmpty(timePeriodConfigList)) {
            return Lists.newArrayList();
        }

        List<TimePeriodVO> timePeriodVOList = Lists.newArrayList();
        for (TimePeriodConfigData configData : CollectionUtils.emptyIfNull(timePeriodConfigList)) {
            TimePeriodVO timePeriodVO = convert2TimePeriodVO(configData);
            timePeriodVO.setSubs(buildTimePeriodVOListV2(configData.getSubs()));
            timePeriodVOList.add(timePeriodVO);
        }

        return timePeriodVOList.stream().sorted(Comparator.comparingLong(TimePeriodVO::getTimePeriodId)).collect(Collectors.toList());
    }


    private List<TimePeriodVO> buildTimePeriodVOListV3(List<TimePeriodConfigData> timePeriodConfigList) {
        return CollectionUtils.emptyIfNull(timePeriodConfigList).stream()
                .sorted(Comparator.comparingLong(TimePeriodConfigData::getId))
                .map(this::convert2TimePeriodV3)
                .collect(Collectors.toList());
    }

    private TimePeriodVO convert2TimePeriodV3(TimePeriodConfigData timePeriodConfigData) {
        TimePeriodVO timePeriodVO = new TimePeriodVO();
        timePeriodVO.setTimePeriodId(timePeriodConfigData.getId());
        timePeriodVO.setTimeDesc(timePeriodConfigData.getDesc());

        Date now = new Date();
        DispatchConfigData dispatchConfigData = lionConfigUtil.getDispatchConfigData();
        // 开始时间
        Date bookStartTime = getBookBeginTime(timePeriodConfigData, now, dispatchConfigData.getDispatchMinuteOffset(), TIME_PERIOD_V3);
        // 今天的结束时间
        Date bookEndTime = timePeriodConfigData.getEndTime(now, dispatchConfigData.getDispatchMinuteOffset());
        // 开始时间+2小时 与 结束时间取小值
        Date bookStartTimePlusTwoHours = new Date(bookStartTime.getTime() + lionConfigUtil.getEndTimeDelayHour() * 60 * 60 * 1000);
        Date bookMinEndTime = bookEndTime.before(bookStartTimePlusTwoHours) ? bookEndTime : bookStartTimePlusTwoHours;

        // 开始时间 10点
        timePeriodVO.setDayStartTime(getSpecificTimeStamp(bookStartTime, 10));
        // 结束时间 22点
        timePeriodVO.setDayEndTime(getSpecificTimeStamp(bookMinEndTime, 22));
        // 默认开始时间
        timePeriodVO.setDefaultStartTime(bookStartTime.getTime());
        // 默认结束时间
        timePeriodVO.setDefaultEndTime(bookMinEndTime.getTime());
        return timePeriodVO;
    }

    private long getSpecificTimeStamp(Date bookStartTime, int time) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(bookStartTime);
        calendar.set(Calendar.HOUR_OF_DAY, time);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        return calendar.getTimeInMillis();
    }

    private TimePeriodVO convert2TimePeriodVO(TimePeriodConfigData timePeriodConfigData) {
        TimePeriodVO timePeriodVO = new TimePeriodVO();
        timePeriodVO.setTimePeriodId(timePeriodConfigData.getId());
        timePeriodVO.setTimeDesc(timePeriodConfigData.getDesc());
        return timePeriodVO;
    }

    public List<NaviVO> buildPosition(PositionRequest request) {
        String key = request.getCityId() + "_" + request.getPlatform();
        // 仅缓存基本位置信息（行政区/商圈和地铁站）
        List<NaviVO> basePositions;
        try {
            basePositions = positionCache.get(key, () -> {
                List<NaviVO> newBasePositions = buildBasePositions(request);
                positionCache.put(key, newBasePositions);
                return newBasePositions;
            });
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }

        List<NaviVO> positions = deepCopyNaviVOList(basePositions);
        fillDistance(request, positions);
        return positions;
    }

    private void fillDistance(PositionRequest request, List<NaviVO> positions) {
        if (isRemoteLocation(request.getCityId(), request.getLocalCityId())
                || isLocationUnauthorized(request.getLocalCityId(), request.getLng(), request.getLat())) {
            return;
        }

        NaviVO distance = buildDistance(request);
        if (distance != null && positions.stream().noneMatch(navi -> navi.getNaviName().equals("直线距离"))) {
            positions.add(distance);
        }
    }

    private List<NaviVO> buildBasePositions(PositionRequest request) {
        List<NaviVO> basePositions = Lists.newArrayList();
        if (request.getPlatform() == VCPlatformEnum.MT.getType()) {
            basePositions.add(buildMtDistrictArea(request));
            NaviVO mtSubway = buildMtSubway(request);
            if (mtSubway != null && CollectionUtils.isNotEmpty(mtSubway.getSubs())) {
                basePositions.add(mtSubway);
            }
        } else if (request.getPlatform() == VCPlatformEnum.DP.getType()) {
            basePositions.add(buildDpDistrictArea(request));
            NaviVO dpSubway = buildDpSubway(request);
            if (dpSubway != null && CollectionUtils.isNotEmpty(dpSubway.getSubs())) {
                basePositions.add(dpSubway);
            }
        }
        return basePositions;
    }

    private NaviVO buildDpSubway(PositionRequest request) {
        NaviVO subway = new NaviVO();
        subway.setNaviName("地铁线");
        subway.setFilterId("地铁线");
        List<RegionInfoDTO> subwayLines = regionInfoService.findRegionListByCityId(request.getCityId(),
                RegionType.MetroLine, 0, 1000);
        if (CollectionUtils.isEmpty(subwayLines)) {
            return null;
        }
        List<NaviVO> subwayLineSubs = buildDpSubwayLineSubs(subwayLines);
        if (CollectionUtils.isEmpty(subwayLineSubs)) {
            return null;
        }
        subway.setSubs(subwayLineSubs);
        return subway;
    }

    private List<NaviVO> buildDpSubwayLineSubs(List<RegionInfoDTO> subwayLines) {
        return subwayLines.stream().map(this::buildDpSubwayLineNaviVO).collect(Collectors.toList());
    }

    private NaviVO buildDpSubwayLineNaviVO(RegionInfoDTO subwayLine) {
        NaviVO lineNaviVO = new NaviVO();
        lineNaviVO.setNaviName(subwayLine.getRegionName());
        lineNaviVO.setFilterId("地铁线_" + subwayLine.getRegionName());
        lineNaviVO.setSubs(buildDpStationSubs(subwayLine.getRegionName(), subwayLine.getRegionId()));
        return lineNaviVO;
    }

    private List<NaviVO> buildDpStationSubs(String subwayLineName, int lineId) {
        List<RegionInfoDTO> stations = regionInfoService.findChildRegionList(lineId, RegionType.MetroStation, false, 0,
                1000);
        List<NaviVO> stationSubs = Lists.newArrayList();
        stationSubs.add(0, buildDpFirstStationSub(subwayLineName, lineId));
        stationSubs.addAll(stations.stream().map(this::buildDpStationNaviVO).collect(Collectors.toList()));
        return stationSubs;
    }

    private NaviVO buildDpFirstStationSub(String subwayLineName, int lineId) {
        NaviVO firstStationNaviVO = new NaviVO();
        firstStationNaviVO.setNaviName("全部" + subwayLineName);
        firstStationNaviVO.setType(PositionEnum.SUBWAY.getPositionType());
        firstStationNaviVO.setFilterId("地铁线_" + subwayLineName + "_" + lineId);
        return firstStationNaviVO;
    }

    private NaviVO buildDpStationNaviVO(RegionInfoDTO station) {
        NaviVO stationNaviVO = new NaviVO();
        stationNaviVO.setNaviName(station.getRegionName());
        stationNaviVO.setType(PositionEnum.STATION.getPositionType());
        stationNaviVO.setFilterId("地铁线_" + station.getRegionName() + "_" + station.getRegionId());
        stationNaviVO.setLng(station.getgLng());
        stationNaviVO.setLat(station.getgLat());
        return stationNaviVO;
    }

    private NaviVO buildDpDistrictArea(PositionRequest request) {
        NaviVO districtArea = new NaviVO();
        districtArea.setNaviName("行政区/商圈");
        districtArea.setFilterId("行政区/商圈");
        List<RegionInfoDTO> districts = regionInfoService.findRegionListByCityId(request.getCityId(),
                RegionType.District, 0, 1000);
        if (CollectionUtils.isEmpty(districts)) {
            return districtArea;
        }
        List<NaviVO> districtSubs = buildDpDistrictSubs(districts);
        if (CollectionUtils.isEmpty(districtSubs)) {
            return districtArea;
        }
        districtArea.setSubs(districtSubs);
        return districtArea;
    }

    private List<NaviVO> buildDpDistrictSubs(List<RegionInfoDTO> districts) {
        return districts.stream().map(this::buildDpDistrictNaviVO).collect(Collectors.toList());
    }

    private NaviVO buildDpDistrictNaviVO(RegionInfoDTO district) {
        NaviVO districtNaviVO = new NaviVO();
        districtNaviVO.setNaviName(district.getRegionName());
        districtNaviVO.setFilterId("行政区/商圈_" + district.getRegionName());
        districtNaviVO.setType(PositionEnum.DISTRICT.getPositionType());
        districtNaviVO.setLng(district.getgLng());
        districtNaviVO.setLat(district.getgLat());
        districtNaviVO.setSubs(buildDpRegionSubs(district.getRegionName(), district.getRegionId(), district.getgLng(), district.getgLat()));
        return districtNaviVO;
    }

    private List<NaviVO> buildDpRegionSubs(String districtName, int districtId, double lng, double lat) {
        List<RegionInfoDTO> regions = regionInfoService.findChildRegionList(districtId, RegionType.BusinessDistrict,
                false, 0, 1000);
        List<NaviVO> regionSubs = Lists.newArrayList();
        regionSubs.add(0, buildDpFirstRegionSub(districtName, districtId, lng, lat));
        if (CollectionUtils.isEmpty(regions)) {
            return regionSubs;
        }
        regionSubs.addAll(
                regions.stream()
                        .map(this::buildDpRegionNaviVO)
                        .collect(Collectors.toList()));
        return regionSubs;
    }

    private NaviVO buildDpFirstRegionSub(String districtName, int districtId, double lng, double lat) {
        NaviVO firstRegionNaviVO = new NaviVO();
        firstRegionNaviVO.setNaviName("全部" + districtName);
        firstRegionNaviVO.setType(PositionEnum.DISTRICT.getPositionType());
        firstRegionNaviVO.setFilterId("行政区/商圈_" + districtName + "_" + districtId);
        firstRegionNaviVO.setLng(lng);
        firstRegionNaviVO.setLat(lat);
        return firstRegionNaviVO;
    }

    private NaviVO buildDpRegionNaviVO(RegionInfoDTO region) {
        NaviVO regionNaviVO = new NaviVO();
        regionNaviVO.setNaviName(region.getRegionName());
        regionNaviVO.setType(PositionEnum.AREA.getPositionType());
        regionNaviVO.setFilterId("行政区/商圈_" + region.getRegionName() + "_" + region.getRegionId());
        regionNaviVO.setLng(region.getgLng());
        regionNaviVO.setLat(region.getgLat());
        return regionNaviVO;
    }

    private NaviVO buildMtSubway(PositionRequest request) {
        NaviVO subway = new NaviVO();
        subway.setNaviName("地铁线");
        subway.setFilterId("地铁线");

        List<SubwayLine> subwayLines = subwayService.listSubwayLineByCity(request.getCityId());
        if (CollectionUtils.isEmpty(subwayLines)) {
            return null;
        }

        List<NaviVO> subwayLineSubs = buildMtSubwayLineSubs(subwayLines);
        if (CollectionUtils.isEmpty(subwayLineSubs)) {
            return null;
        }
        subway.setSubs(subwayLineSubs);
        return subway;
    }

    private List<NaviVO> buildMtSubwayLineSubs(List<SubwayLine> subwayLines) {
        return subwayLines.stream().filter(subwayLine -> subwayLine.getStatus() == 1).map(this::buildMtSubwayLineNaviVO)
                .collect(Collectors.toList());
    }

    private NaviVO buildMtSubwayLineNaviVO(SubwayLine subwayLine) {
        NaviVO lineNaviVO = new NaviVO();
        lineNaviVO.setNaviName(subwayLine.getName());
        lineNaviVO.setFilterId("地铁线_" + subwayLine.getName());
        lineNaviVO.setSubs(buildMtStationSubs(subwayLine.getName(), subwayLine.getId()));
        return lineNaviVO;
    }

    private List<NaviVO> buildMtStationSubs(String subwayLineName, int lineId) {
        List<SubwayStation> stations = subwayService.listSubwayStation(lineId);
        List<NaviVO> stationSubs = Lists.newArrayList();
        stationSubs.add(0, buildMtFirstStationSub(subwayLineName, lineId));

        stationSubs.addAll(stations.stream()
                .filter(station -> station.getStatus() == 1)
                .map(this::buildMtStationNaviVO)
                .collect(Collectors.toList()));

        return stationSubs;
    }

    private NaviVO buildMtFirstStationSub(String subwayLineName, int lineId) {
        NaviVO firstStationNaviVO = new NaviVO();
        firstStationNaviVO.setNaviName("全部" + subwayLineName);
        firstStationNaviVO.setType(PositionEnum.SUBWAY.getPositionType());
        firstStationNaviVO.setFilterId("地铁线_" + subwayLineName + "_" + lineId);
        return firstStationNaviVO;
    }

    private NaviVO buildMtStationNaviVO(SubwayStation station) {
        NaviVO stationNaviVO = new NaviVO();
        stationNaviVO.setNaviName(station.getName());
        stationNaviVO.setType(PositionEnum.STATION.getPositionType());
        stationNaviVO.setFilterId("地铁线_" + station.getName() + "_" + station.getId());
        stationNaviVO.setLng(station.getLongitude() / 1E6);
        stationNaviVO.setLat(station.getLatitude() / 1E6);
        return stationNaviVO;
    }

    private NaviVO buildMtDistrictArea(PositionRequest request) {
        NaviVO districtArea = new NaviVO();
        districtArea.setNaviName("行政区/商圈");
        districtArea.setFilterId("行政区/商圈");

        List<AreaInfo> areaInfos = areaService.listByCity(request.getCityId());
        if (CollectionUtils.isEmpty(areaInfos)) {
            return districtArea;
        }

        // 构造第一层行政区筛选
        List<NaviVO> districtAreaSubs = convertDistrictAreaInfos2NaviVOs(areaInfos);
        if (CollectionUtils.isEmpty(districtAreaSubs)) {
            return districtArea;
        }
        // 构造行政区id与商圈筛选的映射关系
        Map<Integer, List<AreaInfo>> districtId2Regions = buildDistrictId2Regions(areaInfos);
        Map<String, Integer> districtName2Id = buildDistrictName2Id(areaInfos);
        // 根据映射关系填充行政区下的商圈
        fillDistrictNaviByRegionsMap(districtAreaSubs, districtId2Regions, districtName2Id);

        districtArea.setSubs(districtAreaSubs);
        return districtArea;
    }

    private void fillDistrictNaviByRegionsMap(List<NaviVO> districtAreaSubs,
                                              Map<Integer, List<AreaInfo>> districtId2Regions, Map<String, Integer> districtName2Id) {
        for (NaviVO districtNaviVO : districtAreaSubs) {
            String districtName = districtNaviVO.getNaviName();
            Integer districtId = districtName2Id.get(districtName);
            if (districtId == null) {
                continue;
            }
            List<AreaInfo> areaInfos = districtId2Regions.get(districtId);
            if (CollectionUtils.isEmpty(areaInfos)) {
                addFirstSub(districtNaviVO.getSubs(), districtNaviVO, districtId);
                continue;
            }
            // 构造第二层商区筛选
            List<NaviVO> areas = convertRegionInfo2NaviVOs(areaInfos);
            addFirstSub(areas, districtNaviVO, districtId);
            districtNaviVO.setSubs(areas);
        }
    }

    private void addFirstSub(List<NaviVO> subNaviVOs, NaviVO fatherNavi, int districtId) {
        if (CollectionUtils.isEmpty(subNaviVOs)) {
            subNaviVOs = Lists.newArrayList();
        }
        NaviVO firstNavi = new NaviVO();
        firstNavi.setNaviName("全部" + fatherNavi.getNaviName());
        firstNavi.setType(PositionEnum.DISTRICT.getPositionType());
        firstNavi.setFilterId(fatherNavi.getFilterId() + "_" + districtId);
        firstNavi.setLng(fatherNavi.getLng());
        firstNavi.setLat(fatherNavi.getLat());
        subNaviVOs.add(0, firstNavi);
        fatherNavi.setSubs(subNaviVOs);
    }

    private List<NaviVO> convertRegionInfo2NaviVOs(List<AreaInfo> areaInfos) {
        return areaInfos.stream().filter(Objects::nonNull)
                .map(this::convertRegionInfo2NaviVO)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private NaviVO convertRegionInfo2NaviVO(AreaInfo areaInfo) {
        if (StringUtils.isEmpty(areaInfo.getName()) || areaInfo.getId() == 0) {
            return null;
        }
        NaviVO naviVO = new NaviVO();
        naviVO.setNaviName(areaInfo.getName());
        naviVO.setType(PositionEnum.AREA.getPositionType());
        naviVO.setFilterId("行政区/商圈_" + areaInfo.getName() + "_" + areaInfo.getId());
        naviVO.setLng(areaInfo.getLng() / 1E6);
        naviVO.setLat(areaInfo.getLat() / 1E6);
        return naviVO;
    }

    private Map<Integer, List<AreaInfo>> buildDistrictId2Regions(List<AreaInfo> areaInfos) {
        Map<Integer, List<AreaInfo>> districtId2Regions = Maps.newHashMap();
        for (AreaInfo areaInfo : areaInfos) {
            if (areaInfo.getDistrictId() == 0 && areaInfo.getType().equals(AreaInfoTypeEnum.district)) {
                continue;
            }
            if (areaInfo.getType().equals(AreaInfoTypeEnum.area)) {
                districtId2Regions.computeIfAbsent(areaInfo.getDistrictId(), k -> new ArrayList<>()).add(areaInfo);
            }
        }
        return districtId2Regions;
    }

    private Map<String, Integer> buildDistrictName2Id(List<AreaInfo> areaInfos) {
        return areaInfos.stream().filter(Objects::nonNull)
                .filter(areaInfo -> areaInfo.getDistrictId() <= 0 && areaInfo.getType() == AreaInfoTypeEnum.district)
                .collect(Collectors.toMap(AreaInfo::getName, AreaInfo::getId, (a, b) -> a));
    }

    private List<NaviVO> convertDistrictAreaInfos2NaviVOs(List<AreaInfo> areaInfos) {
        return areaInfos.stream().filter(Objects::nonNull)
                .filter(areaInfo -> areaInfo.getDistrictId() <= 0 && areaInfo.getType() == AreaInfoTypeEnum.district)
                .map(this::convertDistrictInfo2NaviVO).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private NaviVO convertDistrictInfo2NaviVO(AreaInfo areaInfo) {
        if (StringUtils.isEmpty(areaInfo.getName()) || areaInfo.getId() == 0) {
            return null;
        }
        NaviVO naviVO = new NaviVO();
        naviVO.setNaviName(areaInfo.getName());
        naviVO.setType(PositionEnum.DISTRICT.getPositionType());
        naviVO.setFilterId("行政区/商圈_" + areaInfo.getName());
        naviVO.setLng(areaInfo.getLng() / 1E6);
        naviVO.setLat(areaInfo.getLat() / 1E6);
        return naviVO;
    }

    private List<NaviVO> deepCopyNaviVOList(List<NaviVO> original) {
        if (original == null) {
            return null;
        }
        return original.stream()
                .map(this::deepCopyNaviVO)
                .collect(Collectors.toList());
    }

    private NaviVO deepCopyNaviVO(NaviVO original) {
        if (original == null) {
            return null;
        }
        NaviVO copy = new NaviVO();
        copy.setNaviName(original.getNaviName());
        copy.setFilterId(original.getFilterId());
        copy.setType(original.getType());
        copy.setLng(original.getLng());
        copy.setLat(original.getLat());
        if (original.getSubs() != null) {
            copy.setSubs(deepCopyNaviVOList(original.getSubs()));
        }
        return copy;
    }


    private NaviVO buildDistance(PositionRequest request) {
        if (request.getLat() == 0 || request.getLng() == 0) {
            return null;
        }
        NaviVO distance = new NaviVO();
        distance.setNaviName(PositionEnum.DISTANCE.getPositionDesc());

        List<NaviVO> distanceSubs = Lists.newArrayList();
        distanceSubs.add(buildDistanceSub("1km", DistanceRangeEnum.LT_1000.getValue()));
        distanceSubs.add(buildDistanceSub("3km", DistanceRangeEnum.LT_3000.getValue()));
        distanceSubs.add(buildDistanceSub("5km", DistanceRangeEnum.LT_5000.getValue()));
        distanceSubs.add(buildDistanceSub("10km", DistanceRangeEnum.LT_10000.getValue()));
        distance.setSubs(distanceSubs);
        return distance;
    }

    private NaviVO buildDistanceSub(String distanceName, String distanceNum) {
        NaviVO distanceSub = new NaviVO();
        distanceSub.setNaviName(distanceName);
        distanceSub.setType(PositionEnum.DISTANCE.getPositionType());
        distanceSub.setFilterId(distanceNum);
        return distanceSub;
    }


    @Override
    public DealResultVO queryDealListByLeadsId(DealDetailQueryRequest request) {
        // 1.查询预约详情
        AIBookTaskE aiBookTask = aiBookTaskRepositoryService.findTaskByLeadsId(request.getLeadsId());
        if (aiBookTask == null || aiBookTask.getUserId() != request.getUserId()) {
            return new DealResultVO();
        }

        // 2.查询团购
        ProductSearchReq productSearchReq = buildSearchReq(request, aiBookTask.getDetails().get(0).getShopId(), aiBookTask.getBookType());
        List<DealDetailData> dealDetailDatas = productAclService.searchProduct(productSearchReq);
        if (CollectionUtils.isEmpty(dealDetailDatas)) {
            return new DealResultVO();
        }

        // 按销量排序倒排
        dealDetailDatas = dealDetailDatas.stream().sorted(Comparator.comparing(DealDetailData::getSale).reversed())
                .limit(30).collect(Collectors.toList());
        return convertDealDetailDatas2DealVOs(dealDetailDatas);
    }

    @Override
    public int submitAIBook(AIBookSubmitRequest aiBookSubmitRequest) {
        try {
            // 0.获取并发锁
            long mtUserId = getMtUserId(aiBookSubmitRequest.getPlatform(), aiBookSubmitRequest.getUserId());
            StoreKey userSubmitLock = new StoreKey(AIBookConstants.AI_BOOK_USER_SUBMIT_LOCK, mtUserId);
            if (!redisClient.setnx(userSubmitLock, 1, 5)) {
                return AIBookSubmitMAPI.BOOK_SUBMIT_FAIL_DUPLICATE_SUBMIT_CODE;
            }

            // 1. 重复创建校验
            boolean hasExecutingTask = checkUserHasExecutingTask(aiBookSubmitRequest.getUserId(), aiBookSubmitRequest.getPlatform());
            if (hasExecutingTask) {
                return AIBookSubmitMAPI.BOOK_SUBMIT_FAIL_DUPLICATE_SUBMIT_CODE;
            }

            // 2. 用户预约频次校验
            boolean usageReachMaximum = checkUserDailyUsageReachMaximum(aiBookSubmitRequest, mtUserId);
            if (usageReachMaximum) {
                return AIBookSubmitMAPI.BOOK_SUBMIT_FAIL_REACH_MAX_USAGE_CODE;
            }

            // 3.时间跨度判断
            TimePeriodConfigData timePeriodConfig = getTimePeriodConfig(aiBookSubmitRequest);
            if (timePeriodConfig == null) {
                return AIBookSubmitMAPI.BOOK_TIME_NOT_EXIST_CODE;
            }
            BookTimeDetailData bookTimeDetailData = calcBookStartTimeEndTime(timePeriodConfig, aiBookSubmitRequest);
            if (!checkBookTimeRange(bookTimeDetailData.getBookStartTime(), bookTimeDetailData.getBookEndTime())) {
                return AIBookSubmitMAPI.BOOK_TIME_RANGE_EXCEPTION_CODE;
            }
            if (!checkUserStayTime(bookTimeDetailData.getBookStartTime(), bookTimeDetailData.getBookEndTime())) {
                return AIBookSubmitMAPI.BOOK_SUBMIT_STAY_TOO_LONG;
            }

            // 获取价格上下限
            Pair<Integer, Integer> minMaxPrice = getMinMaxPrice(aiBookSubmitRequest.getPriceId(), aiBookSubmitRequest.getBookType());

            // 4. 创建预约任务
            UserBookRequest userBookRequest = buildUserBookRequest(aiBookSubmitRequest, bookTimeDetailData.getBookStartTime(), bookTimeDetailData.getDesc(), bookTimeDetailData.getBookEndTime(), minMaxPrice);
            Pair<Integer, Long> dispatchPair = aiBookDispatchProcessService.dispatchUserBookTask(userBookRequest);
            return dispatchPair.getLeft();
        } catch (Exception e) {
            log.error("submitAIBook error, request = {}", JsonCodec.encode(aiBookSubmitRequest), e);
        }
        return AIBookSubmitMAPI.BOOK_SUBMIT_FAIL_SYSTEM_EXCEPTION_CODE;
    }

    private Pair<Integer, Integer> getMinMaxPrice(Integer priceId, int bookType) {
        if (priceId == null) {
            return Pair.of(null, null);
        }

        Map<String, List<BookingPriceDetailData>> bookingPriceData = lionConfigUtil.getBookingPriceData();
        List<BookingPriceDetailData> bookingPriceDetailDataList = bookingPriceData.get(String.valueOf(bookType));
        if (CollectionUtils.isEmpty(bookingPriceDetailDataList)) {
            return Pair.of(null, null);
        }

        BookingPriceDetailData bookingPriceDetailData = bookingPriceDetailDataList.stream()
                .filter(priceDetail -> priceDetail.getPriceId() == priceId)
                .findFirst().orElse(null);
        if (bookingPriceDetailData == null) {
            return Pair.of(null, null);
        }

        return Pair.of(bookingPriceDetailData.getMinPrice(), bookingPriceDetailData.getMaxPrice());
    }

    private BookTimeDetailData calcBookStartTimeEndTime(TimePeriodConfigData timePeriodConfig, AIBookSubmitRequest aiBookSubmitRequest) {
        BookTimeDetailData bookTimeDetailData = new BookTimeDetailData();

        if (StringUtils.defaultString(aiBookSubmitRequest.getVersion()).equals(TIME_PERIOD_V3)) {
            if (aiBookSubmitRequest.getBookStartTime() == 0L || aiBookSubmitRequest.getBookEndTime() == 0L) {
                return bookTimeDetailData;
            }

            Date bookStartTimeDate = new Date(aiBookSubmitRequest.getBookStartTime());
            Date bookEndTimeDate = new Date(aiBookSubmitRequest.getBookEndTime());

            bookTimeDetailData.setBookStartTime(bookStartTimeDate);
            bookTimeDetailData.setBookEndTime(bookEndTimeDate);
            bookTimeDetailData.setDesc(buildV3TimePeriodDesc(bookStartTimeDate, bookEndTimeDate));

            return bookTimeDetailData;
        }

        DispatchConfigData dispatchConfigData = lionConfigUtil.getDispatchConfigData();
        Date now = new Date();
        Date bookStartTime = getBookBeginTime(timePeriodConfig, now, dispatchConfigData.getDispatchMinuteOffset(), aiBookSubmitRequest.getVersion());
        String timePeriodDesc = timePeriodConfig.getDesc();
        Date bookEndTime = timePeriodConfig.getEndTime(now, dispatchConfigData.getDispatchMinuteOffset());
        bookTimeDetailData.setBookStartTime(bookStartTime);
        bookTimeDetailData.setBookEndTime(bookEndTime);
        bookTimeDetailData.setDesc(timePeriodDesc);
        return bookTimeDetailData;
    }

    private String buildV3TimePeriodDesc(Date bookStartDate, Date bookEndDate) {
        SimpleDateFormat hourMinuteFormatter = new SimpleDateFormat("HH:mm");

        String startHM = hourMinuteFormatter.format(bookStartDate);
        String endHM = hourMinuteFormatter.format(bookEndDate);
        if (StringUtils.isBlank(startHM) || StringUtils.isBlank(endHM)) {
            return StringUtils.EMPTY;
        }
        return startHM + "-" + endHM;
    }

    private Date getBookBeginTime(TimePeriodConfigData timePeriodConfig, Date now, int dispatchMinuteOffset, String version) {
        if (StringUtils.defaultString(version).equals(TIME_PERIOD_V2)) {
            return timePeriodConfig.getBeginTimeV2(now, dispatchMinuteOffset);
        }
        return timePeriodConfig.getBeginTime(now, dispatchMinuteOffset);
    }

    private TimePeriodConfigData getTimePeriodConfig(AIBookSubmitRequest aiBookSubmitRequest) {
        String version = aiBookSubmitRequest.getVersion();
        int timePeriodId = aiBookSubmitRequest.getTimePeriodId();
        // V3版本
        if (StringUtils.defaultString(version).equals(TIME_PERIOD_V3)) {
            Map<Integer, TimePeriodConfigData> timePeriodId2ConfigMap = lionConfigUtil.getTimePeriodConfigV3().values().stream()
                    .flatMap(List::stream)
                    .collect(Collectors.toMap(TimePeriodConfigData::getId, Function.identity(), (o1, o2) -> o1));
            return timePeriodId2ConfigMap.get(aiBookSubmitRequest.getTimePeriodId());
        }

        // V2版本
        if (StringUtils.defaultString(version).equals(TIME_PERIOD_V2)) {
            Map<String, List<TimePeriodConfigData>> timePeriodConfigMap = lionConfigUtil.getTimePeriodConfigV2();
            List<TimePeriodConfigData> parentTimePeriodConfigList = timePeriodConfigMap.values().stream()
                    .flatMap(List::stream)
                    .collect(Collectors.toList());

            List<TimePeriodConfigData> allTimePeriodConfigList = flattenConfigDataList(parentTimePeriodConfigList);
            Map<Integer, TimePeriodConfigData> timePeriodId2ConfigMap = allTimePeriodConfigList.stream()
                    .collect(Collectors.toMap(TimePeriodConfigData::getId, Function.identity(), (o1, o2) -> o1));
            return timePeriodId2ConfigMap.get(timePeriodId);
        }

        // 默认版本
        Map<Integer, TimePeriodConfigData> timePeriodId2ConfigMap = lionConfigUtil.getTimePeriodConfig().values().stream()
                .flatMap(List::stream)
                .collect(Collectors.toMap(TimePeriodConfigData::getId, Function.identity(), (o1, o2) -> o1));
        return timePeriodId2ConfigMap.get(aiBookSubmitRequest.getTimePeriodId());
    }

    public List<TimePeriodConfigData> flattenConfigDataList(List<TimePeriodConfigData> configDataList) {
        List<TimePeriodConfigData> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(configDataList)) {
            return result;
        }

        for (TimePeriodConfigData configData : configDataList) {
            result.add(configData);
            if (CollectionUtils.isNotEmpty(configData.getSubs())) {
                result.addAll(flattenConfigDataList(configData.getSubs()));
            }
        }

        return result;
    }

    @Override
    public AIBookProgressVO queryBookProcess(long userId, int platform, String version, long taskId) {
        AIBookTaskEntity taskEntity;
        if (taskId > 0) {
            taskEntity = aiBookTaskRepositoryService.fetchBookTaskById(taskId);
        } else {
            taskEntity = aiBookTaskRepositoryService.fetchLatestBookTask(userId, platform);
        }
        return buildAIBookProgressVO(taskEntity, version, taskId);
    }

    @Override
    public AIBookEntryVO queryAIBookEntry(AIBookEntryQueryRequest request) {
        // 1.灰度判断（全局开关）
        boolean isInGray = checkIsInGray(request);
        if (!isInGray) {
            Cat.logEvent("AIBookEntry", "NotInGray");
            return AIBookEntryVO.builder().showEntry(false).build();
        }

        // 2.门店校验
        boolean isMeetRequirement = checkShopIsMeetRequirement(request);
        if (!isMeetRequirement) {
            Cat.logEvent("AIBookEntry", "ShopNotMeetRequirement");
            return AIBookEntryVO.builder().showEntry(false).build();
        }

        // 3.ab实验判断
        AbTestData abTestData = getAbTestData(request);
        return buildAIBookEntryVO(abTestData, request);
    }

    private AIBookEntryVO buildAIBookEntryVO(AbTestData abTestData, AIBookEntryQueryRequest request) {
        OceanVO oceanVO = buildOceanVO(abTestData);
        if (abTestData == null || !abTestData.isHit()) {
            return AIBookEntryVO.builder().showEntry(false).oceanData(oceanVO).build();
        }

        AIBookEntryConfigData aiBookEntryConfigData = lionConfigUtil.getAiBookEntryConfigData();
        AIBookEntryVO aiBookEntryVO = new AIBookEntryVO();
        aiBookEntryVO.setShowEntry(true);
        aiBookEntryVO.setOceanData(oceanVO);
        aiBookEntryVO.setIconUrl(aiBookEntryConfigData.getEntryIconUrl());
        aiBookEntryVO.setJumpUrl(buildEntryJumpUrl(request));
        return aiBookEntryVO;
    }

    private String buildEntryJumpUrl(AIBookEntryQueryRequest request) {
        String poiEntrySource = "poi";
        int hairMtCatId = 74, hairDpCatId = 157;
        if (request.getPlatform() == PlatformEnum.DP.getType()) {
            return String.format(DP_AI_BOOK_LANDING_PAGE_URL, request.getShopId(), poiEntrySource, hairDpCatId);
        }
        if (request.getPlatform() == PlatformEnum.MT.getType()) {
            return String.format(MT_AI_BOOK_LANDING_PAGE_URL, request.getShopId(), poiEntrySource, hairMtCatId);
        }
        return StringUtils.EMPTY;
    }

    private OceanVO buildOceanVO(AbTestData abTestData) {
        OceanVO oceanVO = new OceanVO();
        String abTestInfoJson = Optional.ofNullable(abTestData).map(AbTestData::getAbConfig).orElse(null);
        OceanVO.AbTestDataVO abTestDataVO = JsonCodec.decode(abTestInfoJson, OceanVO.AbTestDataVO.class);

        oceanVO.setCategory("gc");
        oceanVO.setBidClick("b_gc_221vuj7a_mc");
        oceanVO.setBidView("b_gc_221vuj7a_mv");
        oceanVO.setAbTestData(abTestDataVO);
        return oceanVO;
    }

    private AbTestData getAbTestData(AIBookEntryQueryRequest request) {
        AIBookEntryConfigData aiBookEntryConfigData = lionConfigUtil.getAiBookEntryConfigData();
        String expId = MapUtils.emptyIfNull(aiBookEntryConfigData.getAiBookAbTestExpIdMap()).get(String.valueOf(request.getPlatform()));
        if (StringUtils.isEmpty(expId)) {
            return buildDefaultHitAbTestData();
        }

        DouHuRequest douHuRequest = buildAIBookDouhuRequest(request.getPlatform(), request.getUnionId(), expId);
        AbTestData abTestData = abTestAclService.tryAb(douHuRequest);
        if (abTestData == null) {
            return null;
        }
        abTestData.setHit(Objects.equals(abTestData.getStrategyKey(), expId + "_b") ||
                Objects.equals(abTestData.getStrategyKey(), expId + "_d"));
        return abTestData;
    }

    private AbTestData buildDefaultHitAbTestData() {
        AbTestData abTestData = new AbTestData();
        abTestData.setHit(true);
        return abTestData;
    }

    private DouHuRequest buildAIBookDouhuRequest(int platform, String unionId, String expId) {
        DouHuRequest douHuRequest = new DouHuRequest();
        douHuRequest.setUnionId(unionId);
        douHuRequest.setPlatform(DouHuUtils.getPlatform(platform));
        douHuRequest.setExpId(expId);
        return douHuRequest;
    }


    private boolean checkShopIsMeetRequirement(AIBookEntryQueryRequest request) {
        // 美发类目校验
        long mtShopId = getMtShopId(request.getShopId(), request.getPlatform());
        long dpShopId = getDpShopId(request.getShopId(), request.getPlatform());
        ShopCategoryData shopCategoryData = shopAclService.loadShopFrontCatePath(dpShopId);
        if (shopCategoryData == null || shopCategoryData.getForeSecondCateId() != 157) {
            return false;
        }

        // 营业时间校验
        Date now = new Date();
        BizForecastDTO bizForecast = shopAclService.getBizForecast(dpShopId, DateUtils.covertDateStr(now));
        if (!isShopCurrentOpen(bizForecast, now)) {
            return false;
        }

        // 合作状态校验
        boolean isShopCooperate = checkShopIsCooperate(request);
        if (!isShopCooperate) {
            return false;
        }

        // 门店手机号判断
        List<String> shopPhone = shopAclService.getShopPhone(dpShopId);
        if (CollectionUtils.isEmpty(shopPhone) || shopPhone.stream().allMatch(phone -> phone.startsWith("400"))) {
            return false;
        }

        // 门店手机号黑名单判断
        StoreKey shopPhoneBlackList = new StoreKey(AIBookConstants.AI_BOOK_SHOP_PHONE_BLACK_LIST, mtShopId);
        if (redisClient.exists(shopPhoneBlackList)) {
            return false;
        }

        return true;
    }

    private boolean checkShopIsCooperate(AIBookEntryQueryRequest request) {
        long mtShopId = getMtShopId(request.getShopId(), request.getPlatform());
        BatchQueryBizCooperateInfoReqDTO bizCooperateInfoReqDTO = buildBatchQueryBizCooperateInfoReqDTO(mtShopId);

        Map<Long, BizCooperateInfoDTO> bizCooperateInfoDTOMap = shopAclService.queryShopCooperateInfo(bizCooperateInfoReqDTO);
        BizCooperateInfoDTO bizCooperateInfoDTO = MapUtils.emptyIfNull(bizCooperateInfoDTOMap).get(mtShopId);
        if (bizCooperateInfoDTO == null || bizCooperateInfoDTO.getCooperationStatus() != CooperationStatusEnum.OPENED.getCode()) {
            return false;
        }
        return true;
    }

    private BatchQueryBizCooperateInfoReqDTO buildBatchQueryBizCooperateInfoReqDTO(long mtShopId) {
        AIBookEntryConfigData aiBookEntryConfigData = lionConfigUtil.getAiBookEntryConfigData();
        BatchQueryBizCooperateInfoReqDTO reqDTO = new BatchQueryBizCooperateInfoReqDTO();
        reqDTO.setMemType(MemTypeEnum.SHOP.getValue());
        reqDTO.setBizIdList(Lists.newArrayList(mtShopId));
        reqDTO.setProductClassifyIdList(aiBookEntryConfigData.getAiBookEntryGrayConfig().getProductClassifyIds());
        return reqDTO;
    }

    private boolean isShopCurrentOpen(BizForecastDTO bizForecast, Date now) {
        if (bizForecast == null || StringUtils.isEmpty(bizForecast.getToday())) {
            return false;
        }

        int hour = DateUtils.getHourOfDay(now);
        int minute = DateUtils.getMinuteOfHour(now);
        int halfHourIndex;
        if (minute < 30) {
            halfHourIndex = hour * 2;
        } else {
            halfHourIndex = hour * 2 + 1;
        }

        String today = bizForecast.getToday();
        if (today.charAt(halfHourIndex) == '1') {
            return true;
        }
        return false;
    }

    private boolean checkIsInGray(AIBookEntryQueryRequest request) {
        DispatchConfigData dispatchConfigData = lionConfigUtil.getDispatchConfigData();
        AIBookEntryGrayConfig aiBookEntryGrayConfig = lionConfigUtil.getAiBookEntryConfigData().getAiBookEntryGrayConfig();
        if (!aiBookEntryGrayConfig.isGlobalSwitch()) {
            return false;
        }

        if (!aiBookEntryGrayConfig.isInGrayDate()) {
            return false;
        }

        if (!aiBookEntryGrayConfig.isInGrayCity(request.getCityId(), request.getPlatform())) {
            return false;
        }

        long dpShopId = getDpShopId(request.getShopId(), request.getPlatform());
        if (CollectionUtils.emptyIfNull(dispatchConfigData.getDpShopIdBlackList()).contains(dpShopId)) {
            return false;
        }

        return true;
    }

    private boolean checkBookTimeRange(Date bookStartTime, Date bookEndTime) {
        if (bookStartTime == null || bookEndTime == null) {
            return false;
        }
        long bookStartTimestamp = bookStartTime.getTime();
        long bookEndTimestamp = bookEndTime.getTime();
        if (bookStartTimestamp > bookEndTimestamp) {
            return false;
        }
        if (bookEndTimestamp - bookStartTimestamp < 15 * 60 * 1000) {
            return false;
        }
        return true;
    }

    private boolean checkUserStayTime(Date bookStartTime, Date bookEndTime) {
        if (bookStartTime == null || bookEndTime == null) {
            return false;
        }
        long bookStartTimestamp = bookStartTime.getTime();
        long currentTime = System.currentTimeMillis();
        if (bookStartTimestamp - currentTime < 20 * 60 * 1000) {
            return false;
        }
        return true;
    }

    private UserBookRequest buildUserBookRequest(AIBookSubmitRequest aiBookSubmitRequest, Date bookStartTime,
                                                 String timePeriodDesc, Date bookEndTime,
                                                 Pair<Integer, Integer> minMaxPrice) {
        UserBookRequest userBookRequest = new UserBookRequest();
        userBookRequest.setBookType(aiBookSubmitRequest.getBookType());
        userBookRequest.setUserId(aiBookSubmitRequest.getUserId());
        userBookRequest.setBookStartTime(bookStartTime);
        userBookRequest.setTimePeriodDesc(timePeriodDesc);
        userBookRequest.setBookEndTime(bookEndTime);
        userBookRequest.setCityId(aiBookSubmitRequest.getCityId());
        userBookRequest.setUserPhone(aiBookSubmitRequest.getUserPhone());
        userBookRequest.setPlatform(aiBookSubmitRequest.getPlatform());
        userBookRequest.setRecommendSwitch(aiBookSubmitRequest.getRecommendSwitch());
        userBookRequest.setShopId(aiBookSubmitRequest.getShopId());
        userBookRequest.setShopIds(StringUtils.isEmpty(aiBookSubmitRequest.getShopIds()) ? Lists.newArrayList() :
                Arrays.stream(StringUtils.split(aiBookSubmitRequest.getShopIds(), ','))
                        .map(NumberUtils::toLong).collect(Collectors.toList()));
        userBookRequest.setDeviceId(aiBookSubmitRequest.getDeviceId());
        userBookRequest.setLowerPrice(minMaxPrice.getLeft());
        userBookRequest.setUpperPrice(minMaxPrice.getRight());
        fillPosition(aiBookSubmitRequest, userBookRequest);
        fillRecommendCondition(userBookRequest, aiBookSubmitRequest);
        return userBookRequest;
    }

    private void fillPosition(AIBookSubmitRequest aiBookSubmitRequest, UserBookRequest userBookRequest) {
        if (aiBookSubmitRequest.getShopId() > 0) {
            userBookRequest.setPosition(StringUtils.EMPTY);
        } else {
            userBookRequest.setPosition(aiBookSubmitRequest.getNaviName());
        }
    }

    private void fillRecommendCondition(UserBookRequest userBookRequest, AIBookSubmitRequest aiBookSubmitRequest) {
        fillLatAndLng(userBookRequest, aiBookSubmitRequest);
        List<String> naviFilterIdItemList = Arrays.asList(StringUtils.defaultString(aiBookSubmitRequest.getNaviFilterId()).split("_"));
        if (CollectionUtils.isEmpty(naviFilterIdItemList)) {
            return;
        }

        String lastNaviFilterIdItem = naviFilterIdItemList.get(naviFilterIdItemList.size() - 1);
        if (aiBookSubmitRequest.getNaviType() == PositionEnum.AREA.getPositionType()) {
            userBookRequest.setRegionId(Lists.newArrayList(NumberUtils.toInt(lastNaviFilterIdItem)));
        }
        if (aiBookSubmitRequest.getNaviType() == PositionEnum.DISTRICT.getPositionType()) {
            userBookRequest.setDistrictId(Lists.newArrayList(NumberUtils.toInt(lastNaviFilterIdItem)));
        }
        if (aiBookSubmitRequest.getNaviType() == PositionEnum.SUBWAY.getPositionType()) {
            userBookRequest.setStationLine(Lists.newArrayList(NumberUtils.toInt(lastNaviFilterIdItem)));
        }
        if (aiBookSubmitRequest.getNaviType() == PositionEnum.STATION.getPositionType()) {
            userBookRequest.setStationId(Lists.newArrayList(NumberUtils.toInt(lastNaviFilterIdItem)));
        }
        if (aiBookSubmitRequest.getNaviType() == PositionEnum.DISTANCE.getPositionType()) {
            userBookRequest.setDistanceRange(aiBookSubmitRequest.getNaviFilterId());
        }
    }

    private void fillLatAndLng(UserBookRequest userBookRequest, AIBookSubmitRequest aiBookSubmitRequest) {
        if (aiBookSubmitRequest == null) {
            return;
        }

        // 若是直线距离类型，则使用用户位置的经纬度
        if (aiBookSubmitRequest.getNaviType() == PositionEnum.DISTANCE.getPositionType()) {
            userBookRequest.setLat(aiBookSubmitRequest.getLat());
            userBookRequest.setLng(aiBookSubmitRequest.getLng());
            return;
        }

        // 若是除直线距离外的导航类型，则使用对应导航位置的经纬度
        PositionRequest positionRequest = buildPositionRequest(aiBookSubmitRequest.getPlatform(), aiBookSubmitRequest.getCityId(), aiBookSubmitRequest.getLat(), aiBookSubmitRequest.getLng(), 0);
        List<NaviVO> basePositions = buildPosition(positionRequest);

        List<NaviVO> allPositions = flattenNaviList(basePositions);
        NaviVO targetPosition = CollectionUtils.emptyIfNull(allPositions).stream()
                .filter(position -> StringUtils.isNotEmpty(position.getFilterId()))
                .filter(position -> position.getFilterId().equals(StringUtils.defaultString(aiBookSubmitRequest.getNaviFilterId())) && position.getType() == aiBookSubmitRequest.getNaviType())
                .findFirst().orElse(null);
        if (targetPosition == null) {
            return;
        }
        userBookRequest.setLng(targetPosition.getLng());
        userBookRequest.setLat(targetPosition.getLat());
    }

    public static List<NaviVO> flattenNaviList(List<NaviVO> naviList) {
        List<NaviVO> flattenedList = new ArrayList<>();
        flattenNaviListRecursive(naviList, flattenedList);
        return flattenedList;
    }

    private static void flattenNaviListRecursive(List<NaviVO> naviList, List<NaviVO> flattenedList) {
        if (CollectionUtils.isEmpty(naviList)) {
            return;
        }
        for (NaviVO navi : naviList) {
            flattenedList.add(navi);
            if (navi != null && navi.getSubs() != null && !navi.getSubs().isEmpty()) {
                flattenNaviListRecursive(navi.getSubs(), flattenedList);
            }
        }
    }

    private boolean checkUserDailyUsageReachMaximum(AIBookSubmitRequest aiBookSubmitRequest, long mtUserId) {
        StoreKey userBookCntKey = new StoreKey(AIBookConstants.AI_BOOK_USER_DISPATCH_CNT_CATEGORY, mtUserId, DateUtils.covertDateNumStr(new Date()));
        try {
            Object storeVal = redisClient.get(userBookCntKey);
            int dailyUseCount = Optional.ofNullable(storeVal).map(Object::toString).map(NumberUtils::toInt).orElse(0);
            return dailyUseCount >= lionConfigUtil.getUserMaxDailyUsageCount();
        } catch (Exception e) {
            log.error("get dailyUseCount error, request = {}", JsonCodec.encodeWithUTF8(aiBookSubmitRequest), e);
        }
        return false;
    }

    private long getMtUserId(int platform, long userId) {
        if (platform == VCPlatformEnum.DP.getType()) {
            return userAclService.getMtRealBindUserId(userId);
        }
        return userId;
    }

    @Override
    public boolean checkUserHasExecutingTask(long userId, int platform) {
        List<AIBookTaskEntity> aiBookTaskEntities = aiBookTaskDAO.queryBookTaskList(userId, platform);
        if (CollectionUtils.isEmpty(aiBookTaskEntities)) {
            return false;
        }

        return aiBookTaskEntities.stream()
                .filter(entity -> !TaskStatusEnum.isBookCallEnd(entity.getTaskStatus()))
                .findFirst().isPresent();
    }

    private DealResultVO convertDealDetailDatas2DealVOs(List<DealDetailData> dealDetailDatas) {
        List<DealVO> dealList = dealDetailDatas.stream().filter(Objects::nonNull)
                .map(this::convertDealDetailData2DealVO)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        return DealResultVO.builder().dealList(dealList).build();
    }

    private DealVO convertDealDetailData2DealVO(DealDetailData dealDetailData) {
        DealVO dealVO = new DealVO();
        dealVO.setDealId(dealDetailData.getDealId());
        dealVO.setDealName(dealDetailData.getDealName());
        dealVO.setHeadPic(dealDetailData.getHeadPic());
        dealVO.setDetailUrl(dealDetailData.getDetailUrl());
        dealVO.setMarketPrice(dealDetailData.getMarketPrice());
        dealVO.setPromoPrice(dealDetailData.getPromoPrice());
        dealVO.setSaleTag(dealDetailData.getSaleTag());
        dealVO.setDiscountTag(dealDetailData.getDiscountTag());
        return dealVO;
    }

    private ProductSearchReq buildSearchReq(DealDetailQueryRequest request, long shopId, int bookType) {
        ProductSearchReq productSearchReq = new ProductSearchReq();
        productSearchReq.setPlanId("10002655");
        productSearchReq.setPlatform(request.getPlatform());
        productSearchReq.setUserId(request.getUserId());
        productSearchReq.setShopIds(Lists.newArrayList(shopId));
        productSearchReq.setFilterTagIds(getProjectTagIds(bookType));
        productSearchReq.setDeviceId(request.getDeviceId());
        productSearchReq.setUnionId(request.getUnionId());
        productSearchReq.setAppVersion(request.getAppVersion());
        productSearchReq.setLat(request.getLat());
        productSearchReq.setLng(request.getLng());
        productSearchReq.setCityId(request.getCityId());
        productSearchReq.setPageNo(1);
        productSearchReq.setPageSize(50);
        return productSearchReq;
    }

    private ProductSearchReq buildSearchReq(AIBookDetailQueryRequest request, long shopId, int bookType) {
        ProductSearchReq productSearchReq = new ProductSearchReq();
        productSearchReq.setPlanId("10002655");
        productSearchReq.setPlatform(request.getPlatform());
        productSearchReq.setUserId(request.getUserId());
        productSearchReq.setShopIds(Lists.newArrayList(shopId));
        productSearchReq.setFilterTagIds(getProjectTagIds(bookType));
        productSearchReq.setDeviceId(request.getDeviceId());
        productSearchReq.setUnionId(request.getUnionId());
        productSearchReq.setAppVersion(request.getAppVersion());
        productSearchReq.setLat(request.getLat());
        productSearchReq.setLng(request.getLng());
        productSearchReq.setCityId(request.getCityId());
        productSearchReq.setPageNo(1);
        productSearchReq.setPageSize(50);
        return productSearchReq;
    }


    private List<Long> getProjectTagIds(Integer bookType) {
        List<Integer> tags = IntentProjectCodeEnum.getTags(bookType);
        if (CollectionUtils.isEmpty(tags)) {
            return Lists.newArrayList();
        }
        return tags.stream().map(Long::valueOf).collect(Collectors.toList());
    }


    private String queryPollingRemainTime(AIBookTaskEntity taskEntity) {
        BookPageFixedContentConfigData fixedContentConfigData = lionConfigUtil.getBookPageFixedContentConfig();
        try {
            if (taskEntity == null || StringUtils.isEmpty(taskEntity.getLat()) || StringUtils.isEmpty(taskEntity.getLng())) {
                return fixedContentConfigData.getRemainCallTimeDesc();
            }
            MtLocationDTO locationDTO = mtRgcAclService.getInfoByLnglat(NumberUtils.toDouble(taskEntity.getLng()), NumberUtils.toDouble(taskEntity.getLat()));
            if (locationDTO == null) {
                return fixedContentConfigData.getRemainCallTimeDesc();
            }
            Integer mtCityId = locationDTO.getCityId();
            Integer mtDistrictId = locationDTO.getDistrictId();
            Integer mtRegionId = locationDTO.getFrontAreaId();

            AIBookCallCntEntity cntEntity = queryCnt(mtCityId, mtDistrictId, mtRegionId);
            AIBookCallDurationEntity durationEntity = queryDuration(mtCityId, mtDistrictId, mtRegionId);
            if (cntEntity == null || durationEntity == null) {
                return fixedContentConfigData.getRemainCallTimeDesc();
            }
            String callCnt = cntEntity.getCallCnt();
            String callDuration = durationEntity.getCallDuration();
            return Math.round((NumberUtils.toDouble(callCnt) * NumberUtils.toDouble(callDuration)) / 60.0) + "分钟";
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("queryPollingRemainTime").build(),
                    WarnMessage.build("queryPollingRemainTime", "查询商圈等待时间一长", ""),
                    taskEntity == null ? null : taskEntity.getId(), e);
        }
        return fixedContentConfigData.getRemainCallTimeDesc();
    }


    private AIBookCallDurationEntity queryDuration(Integer mtCityId, Integer mtDistrictId, Integer mtRegionId) {
        if (mtCityId <= 0 || mtDistrictId <= 0) {
            return null;
        }
        if (mtRegionId == null || mtRegionId <= 0) {
            return aiBookCallDurationDAO.getByCityDistrict(mtCityId, mtDistrictId);
        }
        return aiBookCallDurationDAO.getByCityDistrictRegion(mtCityId, mtDistrictId, mtRegionId);
    }

    private AIBookCallCntEntity queryCnt(Integer mtCityId, Integer mtDistrictId, Integer mtRegionId) {
        if (mtCityId <= 0 || mtDistrictId <= 0) {
            return null;
        }
        if (mtRegionId == null || mtRegionId <= 0) {
            return aiBookCallCntDAO.getByCityDistrict(mtCityId, mtDistrictId);
        }
        return aiBookCallCntDAO.getByCityDistrictRegion(mtCityId, mtDistrictId, mtRegionId);
    }
}
