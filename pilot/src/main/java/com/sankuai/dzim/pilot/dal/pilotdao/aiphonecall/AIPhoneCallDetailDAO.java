package com.sankuai.dzim.pilot.dal.pilotdao.aiphonecall;

import com.sankuai.dzim.pilot.dal.entity.aiphonecall.AIPhoneCallDetailEntity;
import com.sankuai.dzim.pilot.dal.entity.aiphonecall.AIPhoneCallTaskEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.security.core.parameters.P;

import java.util.List;

public interface AIPhoneCallDetailDAO {
    AIPhoneCallDetailEntity getById(@Param("id") long id);

    int updateDetailStatusById(@Param("id") Long id, @Param("status") int status);

    int insert(@Param("entity") AIPhoneCallDetailEntity entity);

    int batchInsert(@Param("list") List<AIPhoneCallDetailEntity> list);

    List<AIPhoneCallDetailEntity> getByTaskId(@Param("taskId") long taskId);

    int updateContactId(@Param("id") long id, @Param("contactId") String contactId);

    int updateExpectedCallTimeInt(@Param("entity") AIPhoneCallDetailEntity entity);

    int updateExtraData(@Param("entity") AIPhoneCallDetailEntity entity);

}
