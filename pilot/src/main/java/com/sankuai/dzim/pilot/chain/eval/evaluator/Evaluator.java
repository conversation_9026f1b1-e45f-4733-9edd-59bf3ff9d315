package com.sankuai.dzim.pilot.chain.eval.evaluator;

import com.sankuai.dzim.pilot.chain.eval.data.EvalCase;
import com.sankuai.dzim.pilot.chain.eval.data.EvalContext;
import com.sankuai.dzim.pilot.chain.eval.data.EvalCaseResult;

/**
 * @author: z<PERSON><PERSON><PERSON>
 * @date: 2025/3/26
 */
public interface Evaluator {

    boolean accept(String evaluator);

    EvalCaseResult measure(EvalContext evalContext, EvalCase evalCase);
}
