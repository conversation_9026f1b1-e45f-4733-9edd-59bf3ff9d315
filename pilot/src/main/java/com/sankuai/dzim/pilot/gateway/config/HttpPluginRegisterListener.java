package com.sankuai.dzim.pilot.gateway.config;

import com.dianping.lion.client.Lion;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfigListener;
import com.meituan.mdp.boot.starter.config.vo.ConfigEvent;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.domain.plugin.PluginRegistry;
import com.sankuai.dzim.pilot.domain.plugin.data.HttpPluginConfig;
import com.sankuai.dzim.pilot.domain.plugin.data.PluginSpecification;
import com.sankuai.dzim.pilot.domain.plugin.data.PluginTypeEnum;
import com.sankuai.dzim.pilot.domain.plugin.executor.impl.HttpPluginExecutor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Http插件注册监听器
 */
@Component
@Slf4j
public class HttpPluginRegisterListener implements ApplicationRunner {

    private static final String APPKEY = "com.sankuai.mim.pilot";

    private static final String HTTP_PLUGIN_LION_KEY = "com.sankuai.mim.pilot.http.plugin.configs";

    /**
     * 服务run前,初始化插件
     *
     * @param args
     * @throws Exception
     */
    @Override
    public void run(ApplicationArguments args) throws Exception {
        String configStr = Lion.getString(APPKEY, HTTP_PLUGIN_LION_KEY);
        List<HttpPluginConfig> httpPluginConfigs = getHttpPluginConfigs(configStr);
        if (CollectionUtils.isEmpty(httpPluginConfigs)) {
            return;
        }

        registerHttpPlugin(httpPluginConfigs);
    }

    /**
     * 监听lion配置的变化
     *
     * @param configEvent
     */
    @MdpConfigListener(HTTP_PLUGIN_LION_KEY)
    private void listenerLionChange(ConfigEvent configEvent) {
        if (configEvent == null) {
            return;
        }

        // 获取新旧配置
        List<HttpPluginConfig> oldConfigs = getHttpPluginConfigs(configEvent.getOldValue());
        List<HttpPluginConfig> newConfigs = getHttpPluginConfigs(configEvent.getNewValue());

        // 移除旧插件
        removeHttpPlugin(oldConfigs, newConfigs);

        // 注册新插件
        addHttpPlugin(oldConfigs, newConfigs);
    }

    private void removeHttpPlugin(List<HttpPluginConfig> oldConfigs, List<HttpPluginConfig> newConfigs) {
        if (CollectionUtils.isEmpty(oldConfigs)) {
            return;
        }

        for (HttpPluginConfig oldConfig : oldConfigs) {
            if (newConfigs.contains(oldConfig)) {
                continue;
            }

            PluginSpecification plugin = PluginRegistry.getPlugin(oldConfig.getName());
            if (plugin == null) {
                continue;
            }
            PluginRegistry.removePlugin(oldConfig.getName());
        }
    }

    private void addHttpPlugin(List<HttpPluginConfig> oldConfigs, List<HttpPluginConfig> newConfigs) {
        if (CollectionUtils.isEmpty(newConfigs)) {
            return;
        }

        List<HttpPluginConfig> newHttpPluginConfigs = Lists.newArrayList();

        for (HttpPluginConfig newConfig : newConfigs) {
            if (oldConfigs.contains(newConfig)) {
                continue;
            }

            newHttpPluginConfigs.add(newConfig);
        }

        if (CollectionUtils.isEmpty(newHttpPluginConfigs)) {
            return;
        }

        registerHttpPlugin(newHttpPluginConfigs);
    }

    private List<HttpPluginConfig> getHttpPluginConfigs(String configStr) {
        if (StringUtils.isBlank(configStr)) {
            return Lists.newArrayList();
        }

        return JsonCodec.converseList(configStr, HttpPluginConfig.class);
    }

    private void registerHttpPlugin(List<HttpPluginConfig> httpPluginConfigs) {
        if (CollectionUtils.isEmpty(httpPluginConfigs)) {
            return;
        }

        for (HttpPluginConfig httpPluginConfig : httpPluginConfigs) {
            PluginSpecification plugin = PluginRegistry.getPlugin(httpPluginConfig.getName());
            if (plugin != null) {
                LogUtils.logFailLog(log, TagContext.builder().action("httpPluginRegister").bizId(httpPluginConfig.getName()).build(),
                        new WarnMessage("HttpPluginRegisterListener", "http插件注册失败", "存在同名插件"), httpPluginConfig, null);
                continue;
            }

            PluginSpecification httpPlugin = getHttpPlugin(httpPluginConfig);
            if (httpPlugin == null) {
                continue;
            }
            PluginRegistry.addPlugin(httpPlugin);
        }
    }

    private PluginSpecification getHttpPlugin(HttpPluginConfig httpPluginConfig) {
        if (StringUtils.isBlank(httpPluginConfig.getUrl())) {
            LogUtils.logFailLog(log, TagContext.builder().action("httpPluginRegister").bizId(httpPluginConfig.getName()).build(),
                    new WarnMessage("HttpPluginRegisterListener", "http插件注册失败", "缺少url"), httpPluginConfig, null);
            return null;
        }

        if (StringUtils.isBlank(httpPluginConfig.getDescription())) {
            LogUtils.logFailLog(log, TagContext.builder().action("httpPluginRegister").bizId(httpPluginConfig.getName()).build(),
                    new WarnMessage("HttpPluginRegisterListener", "http插件注册失败", "缺少插件描述"), httpPluginConfig, null);
            return null;
        }

        if (httpPluginConfig.getParams() == null) {
            LogUtils.logFailLog(log, TagContext.builder().action("httpPluginRegister").bizId(httpPluginConfig.getName()).build(),
                    new WarnMessage("HttpPluginRegisterListener", "http插件注册失败", "缺少参数描述"), httpPluginConfig, null);
            return null;
        }

        return PluginSpecification.builder()
                .type(PluginTypeEnum.HTTP.getType())
                .name(httpPluginConfig.getName())
                .description(httpPluginConfig.getDescription())
                .returnDirect(httpPluginConfig.isReturnDirect())
                .params(httpPluginConfig.getParams())
                .executor(new HttpPluginExecutor(httpPluginConfig.getUrl(), httpPluginConfig.getIsPost(), httpPluginConfig.getTimeout()))
                .build();
    }
}
