package com.sankuai.dzim.pilot.process.data;

import lombok.Data;

import java.util.List;

/**
 * 门店召回数据（搜索or推荐召回）
 * <AUTHOR>
 */
@Data
public class ShopRecallData {

    /**
     * 美团门店id
     */
    private long mtShopId;

    /**
     * 点评门店id
     */
    private long dpShopId;

    /**
     * 关联团单id列表（下挂）
     */
    private List<Long> relatedDeals;

    /**
     * 关联泛商品id列表（下挂）
     */
    private List<Long> relatedDzSpus;
}
