package com.sankuai.dzim.pilot.gateway.rpc;

import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonServer;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.api.GenerativeSearchService;
import com.sankuai.dzim.pilot.api.data.search.generative.*;
import com.sankuai.dzim.pilot.api.enums.search.generative.GenerativeSearchAnswerTemplateTypeEnum;
import com.sankuai.dzim.pilot.process.GenerativeSearchProcessService;
import com.sankuai.dzim.pilot.process.ImageProcessService;
import com.sankuai.dzim.pilot.process.OfflineDataProcessService;
import com.sankuai.dzim.pilot.api.data.ImportTemplateData;
import com.sankuai.dzim.pilot.process.router.TemplateRouter;
import com.sankuai.dzim.pilot.process.router.data.RouteResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: zhouyibing
 * @date: 2024/5/23
 */
@MdpPigeonServer
@Slf4j
public class GenerativeSearchServiceImpl implements GenerativeSearchService {

    @Autowired
    private GenerativeSearchProcessService generativeSearchProcessService;

    @Autowired
    private TemplateRouter templateRouter;

    @Autowired
    private OfflineDataProcessService offlineDataProcessService;

    @Autowired
    private ImageProcessService imageProcessService;


    @Override
    public SuggestionDTO querySug(QuerySuggestionRequest request) {
        Assert.isTrue(request != null, "请求参数不能为空");
        Assert.isTrue(StringUtils.isNotBlank(request.getQuery()), "query不能为空");
        Assert.isTrue(request.getUserType() != null, "userType不能为空");
        Assert.isTrue(request.getUserId() != null, "userId不能为空");

        return generativeSearchProcessService.querySug(request);
    }

    @Override
    public BatchSuggestionDTO querySugList(QuerySuggestionRequest request) {
        Assert.isTrue(request != null, "请求参数不能为空");
        Assert.isTrue(StringUtils.isNotBlank(request.getQuery()), "query不能为空");
        Assert.isTrue(request.getUserType() != null, "userType不能为空");
        Assert.isTrue(request.getUserId() != null, "userId不能为空");

        try {
            return generativeSearchProcessService.querySugList(request);
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("querySugList").bizId(request.getQuery()).build(),
                    new WarnMessage("GenerativeSearchServiceImpl", "查询sug列表失败", null), request, null, e);
            return null;
        }
    }

    @Override
    public SuggestionAnswerDTO queryAnswer(QueryAnswerRequest request) {
        Assert.isTrue(request != null, "请求参数不能为空");
        Assert.isTrue(request.getBizType() != null, "bizType不能为空");
        Assert.isTrue(StringUtils.isNotBlank(request.getQuestion()), "question不能为空");
        Assert.isTrue(request.getUserType() != null, "userType不能为空");
        Assert.isTrue(request.getUserId() != null, "userId不能为空");

        try {
            RouteResponse routeResponse = templateRouter.route(request);
            if (routeResponse != null) {
                if(routeResponse.getTemplateType() == GenerativeSearchAnswerTemplateTypeEnum.EMPTY_TEMPLATE.getType()) {
                    return null;
                }
                return routeResponse.getSuggestionAnswerDTO();
            }
            return generativeSearchProcessService.queryAnswer(request);
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("queryAnswer").bizId(request.getQuestion()).build(),
                    new WarnMessage("GenerativeSearchServiceImpl", "查询答案失败", null), request, null, e);
            return null;
        }
    }

    @Override
    public AnswerFeedbackAddDTO addAnswerFeedback(AddAnswerFeedbackRequest request) {
        Assert.isTrue(request != null, "请求参数不能为空");
        Assert.isTrue(request.getSearchRecordId() != null, "searchRecordId不能为空");
        Assert.isTrue(request.getFeedbackType() != null, "feedbackType不能为空");

        try {
            return generativeSearchProcessService.addAnswerFeedback(request);
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("addAnswerFeedback").bizId(String.valueOf(request.getSearchRecordId())).build(),
                    new WarnMessage("GenerativeSearchServiceImpl", "点赞点踩失败", null), request, null, e);
            return null;
        }
    }

    @Override
    public ProjectRelatedSuggestionDTO queryProjectRelatedSug(QueryProjectRelatedSuggestionRequest request) {
        Assert.isTrue(request != null, "请求参数不能为空");
        Assert.isTrue(request.getSearchRecordId() != null, "searchRecordId不能为空");
        Assert.isTrue(StringUtils.isNotBlank(request.getProjectName()), "projectName不能为空");

        return generativeSearchProcessService.queryProjectRelatedSug(request);
    }

    @Override
    public boolean exportGenerativeSearchAnswer(String fileName, long minId, long maxId, int type) {
        Assert.isTrue(StringUtils.isNotBlank(fileName), "文件路径不能为空");
        Assert.isTrue(maxId - minId <= 100000, "最多导出1w条");

        return generativeSearchProcessService.exportGenerativeSearchAnswer(fileName, minId, maxId, type);
    }

    @Override
    public boolean updateGenerativeSearchAnswer(UpdateAnswerRequest request) {
        Assert.isTrue(request != null, "请求参数不能为空");
        Assert.isTrue(request.getId() != null, "id不能为空");

        return generativeSearchProcessService.updateGenerativeSearchAnswer(request);
    }

    @Override
    public int deleteGenerativeSearchAnswer(List<Long> idList) {
        Assert.isTrue(CollectionUtils.isNotEmpty(idList), "idList不能为空");

        return generativeSearchProcessService.deleteGenerativeSearchAnswer(idList);
    }

    @Override
    public int updateGenerativeSearchAnswerStatus(List<Long> idList, int status) {
        Assert.isTrue(CollectionUtils.isNotEmpty(idList), "idList不能为空");

        return generativeSearchProcessService.updateGenerativeSearchAnswerStatus(idList, status);
    }

    @Override
    public int deleteGenerativeSearchWord(List<Long> idList) {
        Assert.isTrue(CollectionUtils.isNotEmpty(idList), "idList不能为空");

        return generativeSearchProcessService.deleteGenerativeSearchWord(idList);
    }

    @Override
    public void updateRecommendReason(int minId, int maxId) {
        Assert.isTrue(minId > 0, "minId必须大于0");
        Assert.isTrue(maxId > 0, "maxId必须大于0");
        Assert.isTrue(maxId >= minId, "maxId必须大于等于minId");

        generativeSearchProcessService.updateRecommendReason(minId, maxId);
    }

    @Override
    public void batchGenerateWordByNote(int bizType, int threadNum, int minId, int maxId) {
        Assert.isTrue(bizType > 0, "bizType必须大于0");
        Assert.isTrue(minId > 0, "minId必须大于0");
        Assert.isTrue(maxId > 0, "maxId必须大于0");
        Assert.isTrue(maxId >= minId, "maxId必须大于等于minId");

        generativeSearchProcessService.batchGenerateWordByNote(bizType, threadNum, minId, maxId);
    }

    @Override
    public void importGenerativeSearchWord(String fileName) {
        Assert.isTrue(StringUtils.isNotBlank(fileName), "fileName不能为空");

        generativeSearchProcessService.importGenerativeSearchWord(fileName);
    }

    @Override
    public void offlineRecallPoiWithNote(int startWordId, int endWordId, Map<String, List<Integer>> mtCityId2DistrictIdMap, List<List<Integer>> similarCat1Ids) {
        Assert.isTrue(startWordId > 0, "startWordId必须大于0");
        Assert.isTrue(endWordId > 0, "endWordId必须大于0");
        generativeSearchProcessService.offlineRecallPoiWithNote(startWordId, endWordId, mtCityId2DistrictIdMap, similarCat1Ids);
    }

    @Override
    public void offlineGenerateExtend(int templateType, String fileName) {
        Assert.isTrue(templateType > 0, "templateType必须大于0");
        Assert.isTrue(StringUtils.isNotBlank(fileName), "fileName不能为空");
        generativeSearchProcessService.offlineGenerateFilterContent(templateType, fileName);
    }

    @Override
    public void offlineGenerateExtendWithSave(int templateType, String fileName) {
        Assert.isTrue(templateType > 0, "templateType必须大于0");
        Assert.isTrue(StringUtils.isNotBlank(fileName), "fileName不能为空");
        generativeSearchProcessService.offlineGenerateFilterContentWithSave(templateType, fileName);
    }

    @Override
    public void deleteGenerateSearchAnswerExtend(int minId, int maxId) {
        Assert.isTrue(minId > 0, "minId必须大于0");
        Assert.isTrue(maxId > 0, "maxId必须大于0");
        Assert.isTrue(maxId >= minId, "maxId必须大于等于minId");
        generativeSearchProcessService.deleteGenerativeSearchAnswerExtend(minId, maxId);
    }

    @Override
    public void batchProcessSearchAnswerExtendValue(int minId, int maxId) {
        Assert.isTrue(minId > 0, "minId必须大于0");
        Assert.isTrue(maxId > 0, "maxId必须大于0");
        Assert.isTrue(maxId >= minId, "maxId必须大于等于minId");
        generativeSearchProcessService.batchProcessSearchAnswerExtendValue(minId, maxId);
    }

    @Override
    public void batchInsertGenerateSearchAnswerWord(int templateType, String fileName) {
        Assert.isTrue(StringUtils.isNotBlank(fileName), "fileName不能为空");
        generativeSearchProcessService.batchInsertGenerateSearchAnswerExtend(templateType, fileName);
    }

    @Override
    public int choosePicWithAnswerId(List<Integer> answerIds, String type) {
        if (CollectionUtils.isEmpty(answerIds)) {
            return 0;
        }
        return generativeSearchProcessService.choosePicWithAnswerId(answerIds.stream().map(Long::valueOf).collect(Collectors.toList()), type);
    }

    @Override
    public void deleteGenerateSearchAnswerExtend(List<Long> ids) {
        Assert.isTrue(!CollectionUtils.isEmpty(ids), "id列表不能为空");
        generativeSearchProcessService.deleteGenerativeSearchAnswerExtend(ids);
    }

    @Override
    public void updateGenerativeSearchAnswerExtend(UpdateAnswerExtendRequest request) {
        Assert.isTrue(request != null, "请求参数不能为空");
        Assert.isTrue(request.getId() != null, "id不能为空");

        generativeSearchProcessService.updateGenerativeSearchAnswerExtend(request);
    }

    @Override
    public void batchGenerateBLZPicDesc(String filename) {
        Assert.isTrue(StringUtils.isNotEmpty(filename), "文件路径不允许为空");
        generativeSearchProcessService.batchGenerateBLZPicDesc(filename);
    }

    @Override
    public void batchGenerateBLZQuestion(String filename) {
        Assert.isTrue(StringUtils.isNotEmpty(filename), "文件路径不允许为空");
        generativeSearchProcessService.batchGenerateBLZQuestion(filename);
    }

    @Override
    public void batchDeleteBaiBuConflict(Long startId, Long endId, List<String> words, Integer status, Integer bizType) {
        generativeSearchProcessService.deleteBaiBuConflict(startId, endId, words, status, bizType);
    }


    @Override
    public void batchGetBLZAnswers(String context) {
        generativeSearchProcessService.batchGetBLZAnswers(context);
    }

    @Override
    public void batchInsertOrUpdateQuestionCategory(String fileName) {
        Assert.isTrue(StringUtils.isNotBlank(fileName), "fileName不能为空");
        generativeSearchProcessService.batchInsertOrUpdateQuestionCategory(fileName);
    }

    @Override
    public List<TherapyNoteDTO> queryTherapyNote(String keyword) {
        Assert.isTrue(StringUtils.isNotBlank(keyword), "搜索词不能为空");
        return generativeSearchProcessService.queryTherapyNote(keyword);
    }

    @Override
    public void importTemplateData(ImportTemplateData importTemplateData, String fileName) {
        offlineDataProcessService.importTemplateData(importTemplateData, fileName);
    }

    @Override
    public Map<String, String> imageDetect(List<String> imgUrls) {
        return imageProcessService.nailDetect(imgUrls);
    }

}
