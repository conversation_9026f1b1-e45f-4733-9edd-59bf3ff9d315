package com.sankuai.dzim.pilot.buffer.stream.build.replacetagexpt;

import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.message.common.utils.ImAccountTypeUtils;
import com.sankuai.dzim.pilot.acl.ShopAclService;
import com.sankuai.dzim.pilot.buffer.enums.ReplaceTagEnum;
import com.sankuai.dzim.pilot.buffer.enums.StreamEventCardTypeEnum;
import com.sankuai.dzim.pilot.buffer.stream.build.replacetagexpt.data.ReplaceContext;
import com.sankuai.dzim.pilot.buffer.stream.build.replacetagexpt.data.SearchWordConfig;
import com.sankuai.dzim.pilot.scene.data.AssistantSceneContext;
import com.sankuai.sinai.data.api.dto.MtPoiDTO;
import com.sankuai.sinai.data.api.util.MtPoiUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.math3.util.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class RecommendShopUrlReplaceExt implements ReplaceTagExt {

    @ConfigValue(key = "com.sankuai.mim.pilot.mt.shop.url.config", defaultValue = "imeituan://www.meituan.com/gc/poi/detail?id=%s")
    private String mtShopUrlFormat = "";

    @ConfigValue(key = "com.sankuai.mim.pilot.dp.shop.url.config", defaultValue = "dianping://shopinfo?shopid=%s")
    private String dpShopUrlFormat = "";

    @ConfigValue(key = "com.sankuai.mim.pilot.recommend.shop.unknown.shop", defaultValue = "未知商户")
    private String unknownShopName;


    @Autowired
    private ShopAclService shopAclService;


    @Override
    public boolean accept(String tag) {
        return StreamEventCardTypeEnum.RECOMMEND_SHOP_URL.getType().equals(tag);
    }

    @Override
    public String replace(ReplaceContext replaceContext) {
        try {
            String tagContent = replaceContext.getTagContent();
            String data = replaceContext.getData();
            if (StringUtils.isBlank(replaceContext.getTag().getValue())) {
                return data.replace(tagContent, unknownShopName);
            }

            AssistantSceneContext assistantContext = replaceContext.getContext();
            if (assistantContext == null) {
                return data.replace(tagContent, unknownShopName);
            }

            // 大模型返回的商户标签转换为markdown格式的跳链
            Pair<String, String> tag = replaceContext.getTag();

            long shopId = NumberUtils.toLong(tag.getValue());
            if (shopId <= 0) {
                return data.replace(tagContent, unknownShopName);
            }
            String imUserId = Optional.of(replaceContext).map(ReplaceContext::getContext).map(AssistantSceneContext::getUserId).orElse(StringUtils.EMPTY);
            long mtShopId = getMtShopId(shopId, imUserId);
            MtPoiDTO mtShopInfo = shopAclService.getMtShopInfo(mtShopId);
            String shopName = MtPoiUtil.getMtPoiName(mtShopInfo.getName(), mtShopInfo.getBranchName());
            return data.replace(tagContent, "[" + shopName + "]" + "(" + getShopUrl(shopId, imUserId) + ")");
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("RecommendShopUrlReplace").build(),
                    new WarnMessage("RecommendShopUrlReplace", "推荐门店跳链替换", ""), replaceContext, e);
            return unknownShopName;
        }
    }


    private String getShopUrl(long shopId, String imUserId) {
        if (ImAccountTypeUtils.isDpUserId(imUserId)) {
            return String.format(dpShopUrlFormat, shopId);
        }
        return String.format(mtShopUrlFormat, shopId);
    }


    private long getMtShopId(long shopId, String imUserId) {
        if (ImAccountTypeUtils.isDpUserId(imUserId)) {
            return shopAclService.loadMTShopIdByDPShopId(shopId);
        }
        return shopId;
    }
}
