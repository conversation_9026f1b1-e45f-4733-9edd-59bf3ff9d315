package com.sankuai.dzim.pilot.domain.retrieval.impl;

import com.google.common.collect.Lists;
import com.sankuai.dzim.pilot.domain.annotation.Retrieval;
import com.sankuai.dzim.pilot.domain.retrieval.RetrievalAugmentor;
import com.sankuai.dzim.pilot.domain.retrieval.data.Knowledge;
import com.sankuai.dzim.pilot.domain.retrieval.data.RetrievalContext;
import com.sankuai.dzim.pilot.domain.retrieval.data.RetrievalResult;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @since 2025/02/26 20:28
 */
@Component
@Retrieval(name = "DateRetrieval", desc = "当前时间检索器")
public class DateAugmentor implements RetrievalAugmentor {

    private static final String[] WEEK_DAYS = {"星期日(周日)", "星期一(周一)", "星期二(周二)", "星期三(周三)", "星期四(周四)", "星期五(周五)", "星期六(周六)"};

    private static final String PATTERN = "yyyy-MM-dd HH:mm:ss";

    @Override
    public String retrieval(RetrievalContext context) {
        return "现在是" + format(LocalDateTime.now(), PATTERN) + " " + getWeekDay(LocalDateTime.now()) + "\n\n";
    }

    @Override
    public RetrievalResult retrieveKnowledge(RetrievalContext context) {
        RetrievalResult result = new RetrievalResult();
        String s =  "当前时间为:" + format(LocalDateTime.now(), PATTERN) + " " + getWeekDay(LocalDateTime.now()) + "\n\n";
        s += build7DaysStr();
        Knowledge knowledge = new Knowledge();
        knowledge.setKnowledge(s);
        result.setKnowledgePrompt("以下是你当前的日期时间信息,可以根据当前时间对用户预约时间作校验:\n");
        result.setKnowledges(Lists.newArrayList(knowledge));
        return result;
    }

    private String build7DaysStr() {
        StringBuilder sb = new StringBuilder();
        sb.append("后面七天为:");
        LocalDateTime now = LocalDateTime.now();
        for (int i = 1; i <= 7; i++) {
            LocalDateTime nextDay = now.plusDays(i);
            sb.append(format(nextDay, "yyyy-MM-dd"))
              .append(" ")
              .append(getWeekDay(nextDay));
            // 添加对应的"明天"、"后天"、"大后天"标识
            if (i == 1) {
                sb.append(" (明天)");
            } else if (i == 2) {
                sb.append(" (后天)");
            } else if (i == 3) {
                sb.append(" (大后天)");
            }
            sb.append("\n");
        }

        return sb.toString();
    }

    /**
     * LocalDateTime转字符串
     * @param dateTime 日期时间
     * @param pattern 格式 例如："yyyy-MM-dd HH:mm:ss"
     * @return 格式化后的字符串
     */
    public static String format(LocalDateTime dateTime, String pattern) {
        if (dateTime == null || pattern == null) {
            return null;
        }
        return DateTimeFormatter.ofPattern(pattern).format(dateTime);
    }

    /**
     * LocalDateTime转字符串(默认格式：yyyy-MM-dd HH:mm:ss)
     * @param dateTime 日期时间
     * @return 格式化后的字符串
     */
    public static String format(LocalDateTime dateTime) {
        return format(dateTime, "yyyy-MM-dd HH:mm:ss");
    }

    /**
     * 获取星期几
     * @param dateTime 日期时间
     * @return 星期几的中文表示
     */
    public static String getWeekDay(LocalDateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        return WEEK_DAYS[dateTime.getDayOfWeek().getValue() % 7];
    }

    /**
     * LocalDateTime转字符串，包含星期几
     * @param dateTime 日期时间
     * @return 格式化后的字符串，包含星期几
     */
    public static String formatWithWeekDay(LocalDateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        return format(dateTime) + " " + getWeekDay(dateTime);
    }
}
