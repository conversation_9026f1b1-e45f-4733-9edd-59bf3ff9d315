package com.sankuai.dzim.pilot.scene.task.data;

import lombok.Getter;

/**
 * @author: zhouyi<PERSON>
 * @date: 2024/7/30
 */
public enum TaskTypeEnum {

    COMMON_STREAM_TASK("COMMON_STREAM_TASK", "通用流式输出任务"),

    RELATED_QUESTION_SUB_TASK("RELATED_QUESTION_SUB_TASK", "关联问题子任务"),

    FEEDBACK_TAIL_SUB_TASK("FEEDBACK_TAIL_SUB_TASK", "反馈tab子任务"),

    INNER_RELATED_QUESTION_SUB_TASK("INNER_RELATED_QUESTION_SUB_TASK", "内部助手关联问题子任务"),

    BEAUTY_ASK_STREAM_TASK("BEAUTY_ASK_STREAM_TASK", "小美问问流式输出任务"),
    CHANNEL_SEARCH_STREAM_TASK("CHANNEL_SEARCH_STREAM_TASK", "垂类搜索推荐流式输出任务"),

    MEDICAL_CHECKUP_ASK_STREAM_TASK("MEDICAL_CHECKUP_ASK_STREAM_TASK", "体检报告问答流式输出任务"),

    MEDICAL_CHECKUP_SUB_TASK("MEDICAL_CHECKUP_SUB_TASK", "体检报告问答子任务"),

    AI_GUIDE_STREAM_TASK("AI_GUIDE_STREAM_TASK", "AI导诊流式输出任务"),

    HAIR_CUT_BOOK_STREAM_TASK("HAIR_CUT_BOOK_STREAM_TASK", "美发预约任务"),

    MULTI_AGENT_TASK("MULTI_AGENT_STREAM_TASK", "多agent任务"),

    NAIL_TRIAL_TASK("NAIL_TRIAL_TASK", "美甲试戴任务"),

    COMMON_SEGMENT_TASK("COMMON_SEGMENT_TASK", "通用段式任务"),

    FEEDBACK_TAIL_WITH_TEXT_SUB_TASK("FEEDBACK_TAIL_WITH_TEXT_SUB_TASK", "反馈tab子任务(待文案)"),

    BEAM_FWLS_AGENT_TASK("BEAM_FWLS_AGENT_TASK", "服务零售Agent输出任务(for beam)");


    @Getter
    private String type;

    @Getter
    private String desc;

    TaskTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
