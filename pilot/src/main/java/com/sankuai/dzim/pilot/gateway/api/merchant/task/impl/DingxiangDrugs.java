package com.sankuai.dzim.pilot.gateway.api.merchant.task.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.dzim.pilot.gateway.api.merchant.task.Cleaning;
import com.sankuai.dzim.pilot.gateway.api.merchant.task.StrategyFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Component
public class DingxiangDrugs implements Cleaning {

    @Override
    public String extractInformation(String jsonString) {
        Map<String, Object> extractedInfo = new LinkedHashMap<>();
        JSONObject jsonObject = JSON.parseObject(jsonString);

        // 字段名与中文翻译的映射
        Map<String, String> fieldTranslations = new LinkedHashMap<>();
        fieldTranslations.put("drug_name", "药品名称");
        fieldTranslations.put("characters", "性状");
        fieldTranslations.put("packaging", "包装");
        fieldTranslations.put("products_approved_indications", "批准适应症");
        fieldTranslations.put("usage_and_dosage", "用法用量");
        fieldTranslations.put("overdose", "过量用药");
        fieldTranslations.put("adverse_reactions", "不良反应");
        fieldTranslations.put("contraindication", "禁忌");
        fieldTranslations.put("precautions", "注意事项");
        fieldTranslations.put("pregnant_remarks", "孕妇用药");
        fieldTranslations.put("children_medication", "儿童用药");
        fieldTranslations.put("elderly_medication", "老年用药");
        fieldTranslations.put("original_ingredient", "原料药");
        fieldTranslations.put("drug_interactions", "药物相互作用");
        fieldTranslations.put("pharmacology", "药理作用");
        fieldTranslations.put("pharmacokinetics", "药代动力学");
        fieldTranslations.put("validity", "有效期");
        fieldTranslations.put("storage_method", "贮藏方法");
        fieldTranslations.put("vendor", "生产厂家");
        fieldTranslations.put("approval_number", "批准文号");

        for (Map.Entry<String, String> entry : fieldTranslations.entrySet()) {
            String key = entry.getKey();
            String translation = entry.getValue();
            if (jsonObject.containsKey(key)) {
                String content = jsonObject.getString(key);
                if (StringUtils.isEmpty(content)) {
                    continue;
                }
                if (key.equals("drug_name")) {
                    extractedInfo.put("标题", content + "说明书");
                }
                extractedInfo.put(translation, StrategyFactory.removeATagsAndImgAndFigure(content));
            }
        }

        return JSON.toJSONString(extractedInfo);
    }

    @Override
    public List<String> extractVectorInfoList(String jsonString) {
        List<String> res = new ArrayList<>();
        JSONObject jsonObject = JSON.parseObject(jsonString);
        if (jsonObject.containsKey("标题")) {
            res.add(jsonObject.getString("标题"));
        }
        return res;
    }
}
