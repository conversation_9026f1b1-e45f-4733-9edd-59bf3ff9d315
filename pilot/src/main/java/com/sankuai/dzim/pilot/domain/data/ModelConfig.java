package com.sankuai.dzim.pilot.domain.data;

import lombok.Data;

@Data
public class ModelConfig {
    /**
     * 系统提示
     */
    private String systemPrompt;

    /**
     * 模型名字
     */
    private String model;

    /**
     * friday appid
     */
    private String appId;

    /**
     * 模型温度
     */
    private double temperature;

    /**
     * 采样概率
     */
    private double topP;

    /**
     * 是否采用json模式
     */
    private boolean jsonModel;

    /**
     * 是否是流式调用, 默认不是
     */
    private boolean stream;
}
