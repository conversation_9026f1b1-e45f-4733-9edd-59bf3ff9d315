package com.sankuai.dzim.pilot.acl.impl;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Maps;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonClient;
import com.sankuai.dealuser.price.display.api.PriceDisplayService;
import com.sankuai.dealuser.price.display.api.model.BatchPriceRequest;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PriceResponse;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.acl.PriceDisplayAclService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/8
 */
@Service
@Slf4j
public class PriceDisplayAclServiceImpl implements PriceDisplayAclService {

    @MdpPigeonClient(url = "com.sankuai.dealuser.price.display.api.PriceDisplayService", timeout = 1000, testTimeout = 5000)
    private PriceDisplayService priceDisplayService;

    @Override
    public Map<Long, List<PriceDisplayDTO>> batchQueryPriceByLongShopId(BatchPriceRequest request) {
        try {
            PriceResponse<Map<Long, List<PriceDisplayDTO>>> response = priceDisplayService.batchQueryPriceByLongShopId(request);
            log.info("batchQueryPriceByLongShopId, request = {}, response = {}", JsonCodec.encodeWithUTF8(request), JsonCodec.encodeWithUTF8(response));
            if (response == null || !response.isSuccess() || response.getData() == null) {
                log.error("batchQueryPriceByLongShopId failed, request:{}, response:{}", request, response);
                return Maps.newHashMap();
            }
            return response.getData();
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("batchQueryPriceByLongShopId").build(),
                    new WarnMessage("PriceDisplayAclService", "报价查询接口调用失败", null), request, null, e);
            return Maps.newHashMap();
        }
    }
}
