package com.sankuai.dzim.pilot.process;

import com.sankuai.dzim.pilot.dal.entity.pilot.PilotChatMessageEntity;
import com.sankuai.dzim.pilot.gateway.api.assistant.data.SendBeamMessageReq;
import com.sankuai.dzim.pilot.gateway.api.assistant.data.SendMessageReq;
import javafx.util.Pair;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * <AUTHOR>
 * @since 2024/7/31 17:23
 */
public interface MessageSendProcessService {

    /**
     * 用户发送消息
     *
     * @param sendMessageReq sendMessageReq
     * @return
     */
    boolean sendMessage(SendMessageReq sendMessageReq);


    /**
     * Beam用户发送消息
     * @param sendBeamMessageReq
     * @return
     */
    boolean sendBeamMessage(SendBeamMessageReq sendBeamMessageReq);
    /**
     * 发送消息
     * @param sseEmitter
     * @param imUserId
     * @param sendMessageReq
     * @param userMessageId
     * @return
     */
    Pair<PilotChatMessageEntity, PilotChatMessageEntity> sendMessageAfterAudit(SseEmitter sseEmitter,
                                                                               String imUserId, SendMessageReq sendMessageReq, long userMessageId);
}
