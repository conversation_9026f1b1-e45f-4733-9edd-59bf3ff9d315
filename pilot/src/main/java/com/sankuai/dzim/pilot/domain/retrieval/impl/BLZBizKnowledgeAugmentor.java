package com.sankuai.dzim.pilot.domain.retrieval.impl;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.dzim.pilot.acl.FridayAclService;
import com.sankuai.dzim.pilot.api.enums.BizKnowledgeTypeEnum;
import com.sankuai.dzim.pilot.buffer.core.PilotBufferItemDO;
import com.sankuai.dzim.pilot.buffer.utils.BufferUtils;
import com.sankuai.dzim.pilot.dal.entity.BizKnowledgeEntity;
import com.sankuai.dzim.pilot.dal.respository.BizKnowledgeRepositoryService;
import com.sankuai.dzim.pilot.domain.annotation.Retrieval;
import com.sankuai.dzim.pilot.domain.retrieval.RetrievalAugmentor;
import com.sankuai.dzim.pilot.domain.retrieval.data.Knowledge;
import com.sankuai.dzim.pilot.domain.retrieval.data.RetrievalContext;
import com.sankuai.dzim.pilot.domain.retrieval.data.RetrievalResult;
import com.sankuai.dzim.pilot.domain.retrieval.data.SourceData;
import com.sankuai.dzim.pilot.process.data.BeautyAnswerData;
import com.sankuai.dzim.pilot.process.data.BeautyImageData;
import com.sankuai.dzim.pilot.process.data.MetaData;
import com.sankuai.dzim.pilot.utils.LionConfigUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Retrieval(name = "BLZBizKnowledgeRetrieval", desc = "医美避雷针检索器")
public class BLZBizKnowledgeAugmentor implements RetrievalAugmentor {

    @Autowired
    private BizKnowledgeRepositoryService bizKnowledgeRepositoryService;

    @Autowired
    private FridayAclService fridayAclService;

    @Autowired
    private LionConfigUtil lionConfigUtil;



    @Override
    public String retrieval(RetrievalContext context) {
        Assert.isTrue(StringUtils.isNotBlank(context.getQuery()), "搜索关键词不能为空");
        BufferUtils.writeStatusBuffer(PilotBufferItemDO.builder().data("正在为您检索相关内容.....").build());

        //查询 biztype=9 的知识
        List<BizKnowledgeEntity> knowledges = bizKnowledgeRepositoryService.searchBizKnowledge(context.getQuery(), BizKnowledgeTypeEnum.YIMEI_BLZ_NEW.getType(), context.getTopK());
        if (CollectionUtils.isEmpty(knowledges)) {
            return StringUtils.EMPTY;
        }
        knowledges.sort(Comparator.comparing(BizKnowledgeEntity::getSimilarity).reversed());
        return "以下是你可以参考的知识:\n" + knowledges.stream().map(entity -> entity.getQuestion() + "\n" + entity.getAnswer()).collect(Collectors.joining("\n\n"));
    }

    @Override
    public RetrievalResult retrieveKnowledge(RetrievalContext context) {
        Assert.isTrue(StringUtils.isNotBlank(context.getQuery()), "搜索关键词不能为空");

        BufferUtils.writeStatusBuffer(PilotBufferItemDO.builder().data(context.getStartStatus()).build());
//        BufferUtils.writeStatusBuffer(PilotBufferItemDO.builder().data("正在为您检索相关知识...").build());

        RetrievalResult retrievalResult = new RetrievalResult();
        List<BizKnowledgeEntity> bizKnowledgeEntities = bizKnowledgeRepositoryService.searchBizKnowledge(context.getQuery(), BizKnowledgeTypeEnum.YIMEI_BLZ_NEW.getType(), context.getTopK());

        bizKnowledgeEntities = bizKnowledgeEntities.stream().filter(entity -> entity.getSimilarity() > 0.5).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(bizKnowledgeEntities)) {
            return null;
        }

        List<Knowledge> knowledges = bizKnowledgeEntities.stream().map(this::convert2Knowledge).collect(Collectors.toList());

        //拼url
        String preUrl = "https://g.meituan.com/arche/dzbiz/biz-content-2/beauty-avoid-pit-plus.html?guideId=";
        List<String> refUrls = knowledges.stream().map(knowledge -> preUrl + knowledge.getRelatedId()).collect(Collectors.toList());

        SourceData sourceData = new SourceData();
        sourceData.setUrl(refUrls);
        retrievalResult.setSource(sourceData);

        retrievalResult.setKnowledges(knowledges);

        retrievalResult.setKnowledgePrompt("以下是你可以参考的知识:\n" );

        BufferUtils.writeStatusBuffer(PilotBufferItemDO.builder().data(context.getEndStatus()).build());
//        BufferUtils.writeStatusBuffer(PilotBufferItemDO.builder().data("基于平台内高质量数据为您回答").build());
        return retrievalResult;
    }

    private Knowledge convert2Knowledge(BizKnowledgeEntity entity) {
        Knowledge knowledge = new Knowledge();

        String question = entity.getQuestion();
        String answer = entity.getAnswer();
        BeautyAnswerData answerData = JsonCodec.decode(answer, BeautyAnswerData.class);

        String knowledgePrompt = "你可以参考的问题是:%s，"+ "\n" +"你可以参考的正文是:%s，"+ "\n" +"你可以参考的要点是:%s。";
        StringBuilder sb = new StringBuilder();
        sb.append(String.format(knowledgePrompt, question ,answerData.getContents(),answerData.getTips()));

        for(int picId = 0; picId < answerData.getImages().size(); picId++) {
            if(CollectionUtils.isNotEmpty(answerData.getImages())){
                BeautyImageData imageData = answerData.getImages().get(picId);
                if(imageData != null) {
                    if(StringUtils.isBlank(imageData.getUrl())){
                        continue;
                    }
                    String str = "判断图片描述:%s 是否和用户的提问强相关，如果关键词和用户提问的一样就可以返回给用户，并以id-picId的格式返回，其中id是知识的编号%s，picId是这张图片是这个知识的第几张图%s。";
                    str = String.format(str, imageData.getDesc(), entity.getId(), picId);
                    sb.append(str);
                }
            }
        }

        knowledge.setKnowledge(sb.toString().replace('~','-'));
        knowledge.setSimilarity(entity.getSimilarity());
        knowledge.setKnowledgeId(entity.getId());
        knowledge.setVecId(entity.getVectorId());
        MetaData metaData = JsonCodec.decode(entity.getMeta(), MetaData.class);
        if(metaData != null) {
            knowledge.setRelatedId(String.valueOf(metaData.getRelatedId()));
        }

        BizKnowledgeTypeEnum type = BizKnowledgeTypeEnum.getByType(entity.getBizType());
        if (type != null) {
            knowledge.setSource(type.getDesc());
        }
        return knowledge;
    }
}
