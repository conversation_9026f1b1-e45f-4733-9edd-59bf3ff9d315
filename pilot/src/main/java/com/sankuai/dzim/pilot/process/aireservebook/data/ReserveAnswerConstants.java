package com.sankuai.dzim.pilot.process.aireservebook.data;

/**
 * @author: wu<PERSON><PERSON><PERSON>
 * @create: 2025-04-16
 * @description:
 */
public class ReserveAnswerConstants {

    // 门店不存在时的话术
    public static final String POI_NOT_EXIST = "poiNotExist";
    // 用户重复预约话术
    public static final String USER_DUPLICATE_RESERVE = "userDuplicateReserve";
    // 行业不支持
    public static final String SHOP_NOT_SUPPORT = "shopNotSupport";
    // 库存不足
    public static final String STOCK_NOT_ENOUGH = "stockNotEnough";
    // 预约失败兜底
    public static final String RESERVE_FAILED_DEFAULT = "reserveFailedDefault";
    // AI外呼预约失败
    public static final String AI_PHONE_RESERVE_CALL_FAILED = "aiPhoneReserveCallFailed";
    // 取消预约失败
    public static final String CANCEL_RESERVE_FAILED = "cancelReserveFailed";
    // 未查询到预约单
    public static final String RESERVE_ORDER_NOT_FOUND = "reserveOrderNotFound";
    // 预约中无法取消
    public static final String RESERVE_ASKING_CAN_NOT_CANCEL = "reserveAskingCanNotCancel";
    // 已取消订单不可取消
    public static final String RESERVE_CANCEL_CAN_NOT_CANCEL = "reserveCancelCanNotCancel";
    // 未拨通电话
    public static final String PHONE_CALL_FAILED = "phoneCallFailed";
    // 商家未营业，延迟拨打
    public static final String SHOP_NOT_OPEN_DELAY_CALL = "shopNotOpenDelayCall";
    // 取消预约成功话术
    public static final String CANCEL_RESERVE_SUCCESS = "cancelReserveSuccess";
    // beam预约卡片创建成功文案
    public static final String BEAM_BOOK_CARD_CREATE_SUCCESS = "bookCardCreateSuccess";
    // beamAI外呼卡创建成功文案
    public static final String BEAM_AI_CALL_CARD_CREATE_SUCCESS = "aiCallCardCreateSuccess";


    // 兜底回复
    public static final String FAILED_ANSWER = "failedAnswer";

    public static final String FEEDBACK_ANSWER = "抱歉，当前问题无法处理";

    public static final String LACK_RESERVE_ITEMS_FORMAT = "当前缺少的预约要素是：%s，请生成一句话对这些预约要素的提问";

    public static final String TOP_SALE_RECOMMEND_TEXT = "当前门店的以下项目%s销量最高，可以推荐用户使用";

    public static final String PROJECT_RECOMMEND_TEXT = "当前门店的以下项目%s均可预约，可以推荐用户使用";

    public static final String CANCEL_RESERVE_SUCCESS_TEXT = "取消预约成功啦~";
}
