package com.sankuai.dzim.pilot.acl.annotation;

import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.acl.data.fraiday.ModelFallbackConfig;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2024/12/23 17:00
 */
@Aspect
@Component
@Slf4j
public class ModelFallbackAspect {

    private static final String APPID_PARAMETER_NAME = "appId";

    private static final String REQUEST_PARAMETER_NAME = "request";

    private static final String MODEL_PARAMETER_NAME = "model";

    private static final String GET_BUSINESS_KEY = "getBusinessKey";

    private static final String GET_MODEL = "getModel";

    private static final String DEFAULT_BUSINESS_KEY = "default";

    private static final String MAX_TOKENS_PARAMETER_NAME = "max_tokens";

    @ConfigValue(key = "com.sankuai.mim.pilot.model.fallback.configs", defaultValue = "{}")
    private Map<String, ModelFallbackConfig> businessKey2ModelFallbackConfigs;

    private static final String WILDCARD_MATCH_STR = "*";

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    private static class FallbackConfig {
        private String model;
        private Integer maxTokens;
        private String appId;
    }

    @Pointcut(
            "@within(com.sankuai.dzim.pilot.acl.annotation.ModelFallback) || @annotation(com.sankuai.dzim.pilot.acl.annotation.ModelFallback)"
    )
    public void modelFallbackAnnotationPoint() {
    }

    @Around("modelFallbackAnnotationPoint()")
    public Object modelSwitch(ProceedingJoinPoint joinPoint) throws Throwable {
        try {
            if (MapUtils.isEmpty(businessKey2ModelFallbackConfigs)) {
                return joinPoint.proceed();
            }
            // 获取方法签名
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            // 获取方法参数名
            String[] parameterNames = signature.getParameterNames();
            // 获取方法参数值
            Object[] args = joinPoint.getArgs();

            String appId = null;
            String model = null;
            Object request = null;

            for (int i = 0; i < parameterNames.length; i++) {
                if (APPID_PARAMETER_NAME.equals(parameterNames[i])) {
                    appId = (String) args[i];
                } else if (REQUEST_PARAMETER_NAME.equals(parameterNames[i])) {
                    request = args[i];
                } else if (MODEL_PARAMETER_NAME.equals(parameterNames[i])) {
                    model = (String) args[i];
                }
            }
            if (appId == null && (request == null || model == null)) {
                LogUtils.logReturnInfo(log, TagContext.builder().action("modelFallback").build(),
                                       new WarnMessage("modelFallback", "参数不足，跳过模型切换", null), joinPoint + appId + request + model, null);
                return joinPoint.proceed();
            }

            // 从 request 中提取 businessKey 和 model
            String businessKey = getValueFromRequest(request, GET_BUSINESS_KEY);
            if (StringUtils.isBlank(model)) {
                model = getValueFromRequest(request, GET_MODEL);
            }

            // 获取降级配置
            FallbackConfig newConfig = getNewConfig(model, businessKey, appId);
            // 更新方法参数
            updateArgs(parameterNames, args, appId, model, request, newConfig);
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("modelFallback:error").build(),
                                new WarnMessage("modelFallback", "模型切换失败", null), joinPoint, null, e);
        }
        // 继续执行原方法
        return joinPoint.proceed();
    }

    private void updateArgs(Object[] parameterNames, Object[] args, String appId, String model, Object request, FallbackConfig newConfig) throws ReflectiveOperationException {
        if (newConfig == null) {
            return;
        }
        for (int i = 0; i < parameterNames.length; i++) {
            if (APPID_PARAMETER_NAME.equals(parameterNames[i])) {
                updateAppId(args, i, appId, newConfig);
            } else if (MODEL_PARAMETER_NAME.equals(parameterNames[i])) {
                updateModel(args, i, model, newConfig);
            } else if (REQUEST_PARAMETER_NAME.equals(parameterNames[i])) {
                updateRequest(args, i, model, request, newConfig);
            }
        }
    }

    private void updateAppId(Object[] args, int index, String appId, FallbackConfig newConfig) {
        if (appId != null && !appId.equals(newConfig.getAppId())) {
            args[index] = newConfig.getAppId();
            logChange("租户切换成功", appId, newConfig.getAppId());
        }
    }

    private void updateModel(Object[] args, int index, String model, FallbackConfig newConfig) {
        if (model != null && !model.equals(newConfig.getModel())) {
            args[index] = newConfig.getModel();
            logChange("模型切换成功", model, newConfig.getModel());
        }
    }

    private void updateRequest(Object[] args, int index, String model, Object request, FallbackConfig newConfig) throws ReflectiveOperationException {
        if (model != null && !model.equals(newConfig.getModel())) {
            if (updateRequestField(request, MODEL_PARAMETER_NAME, newConfig.getModel(), String.class)) {
                args[index] = request;
                logChange("模型切换成功", model, newConfig.getModel());
            }
            Object maxTokens = getValue(request, MAX_TOKENS_PARAMETER_NAME);
            Integer newMaxTokens = newConfig.getMaxTokens();
            if (newMaxTokens != null && newMaxTokens > 0) {
                if (updateRequestField(request, MAX_TOKENS_PARAMETER_NAME, newMaxTokens, Integer.class)) {
                    args[index] = request;
                    logChange("max_token修改成功", maxTokens, newMaxTokens);
                }
            }
        }
    }

    private boolean updateRequestField(Object request, String parameterName, Object value, Class<?> fieldType) {
        if (request == null || StringUtils.isBlank(parameterName) || !fieldType.isInstance(value)) {
            return false;
        }
        try {
            Object oldValue = getValue(request, parameterName);
            setValue(request, parameterName, value, fieldType);
            return oldValue == null || !oldValue.equals(value);
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("modelFallback:error").build(),
                                new WarnMessage("modelFallback", "更新请求参数失败", null), request + parameterName, null, e);
            return false;
        }
    }

    private Object getValue(Object request, String parameterName) throws ReflectiveOperationException {
        String getterName = "get" + StringUtils.capitalize(parameterName);
        Method getter = request.getClass().getMethod(getterName);
        return getter.invoke(request);
    }

    private void setValue(Object request, String parameterName, Object value, Class<?> fieldType) throws ReflectiveOperationException {
        String setterName = "set" + StringUtils.capitalize(parameterName);
        Method setter = request.getClass().getMethod(setterName, fieldType);
        setter.invoke(request, value);
    }

    private void logChange(String message, Object request, Object response) {
        LogUtils.logReturnInfo(log, TagContext.builder().action("modelFallback").build(),
                               new WarnMessage("modelFallback", message, null), request, response);
    }

    private String getValueFromRequest(Object request, String methodName) {
        if (request == null)
            return null;
        try {
            Method method = request.getClass().getMethod(methodName);
            return (String) method.invoke(request);
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("modelFallback:error").build(),
                                new WarnMessage("modelFallback", "从请求参数中获取参数值失败", null), methodName + request, null, e);
            return null;
        }
    }

    /**
     * 获取模型切换配置，如果模型切换失败，则返回原模型
     *
     * @param currentModel
     * @param businessKey
     * @param appId
     * @return
     */
    private FallbackConfig getNewConfig(String currentModel, String businessKey, String appId) {
        FallbackConfig newConfig = findAlternativeConfig(businessKey, appId, currentModel);
        if (StringUtils.isBlank(newConfig.getModel())) {
            newConfig.setModel(currentModel);
        }
        if (StringUtils.isBlank(newConfig.getAppId())) {
            newConfig.setAppId(appId);
        }
        return newConfig;
    }

    /**
     * 获取模型切换配置，如果没有匹配的模型，则返回空数据
     *
     * @param businessKey
     * @param originalAppId
     * @param originalModel
     * @return
     */
    private FallbackConfig findAlternativeConfig(String businessKey, String originalAppId, String originalModel) {
        return Optional.ofNullable(businessKey2ModelFallbackConfigs)
                       .map(configs -> configs.getOrDefault(businessKey, configs.get(DEFAULT_BUSINESS_KEY)))
                       .filter(ModelFallbackConfig::isEnable)
                       .map(modelFallbackConfig -> {
                           FallbackConfig fallbackConfig = new FallbackConfig();
                           ModelFallbackConfig.ModelFallbackData modelFallbackData = getFallbackData(originalModel, modelFallbackConfig.getFallbackModelMap());
                           if (modelFallbackData != null && StringUtils.isNotBlank(modelFallbackData.getModel())) {
                               fallbackConfig.setModel(modelFallbackData.getModel());
                           }
                           if (modelFallbackData != null) {
                               fallbackConfig.setMaxTokens(modelFallbackData.getMaxTokens());
                           }
                           fallbackConfig.setAppId(getFallbackValue(originalAppId, modelFallbackConfig.getFallbackAppIdMap()));
                           return fallbackConfig;
                       })
                       .orElse(new FallbackConfig());
    }

    private ModelFallbackConfig.ModelFallbackData getFallbackData(String originalModel, Map<String, ModelFallbackConfig.ModelFallbackData> fallbackModelMap) {
        if (StringUtils.isBlank(originalModel) || fallbackModelMap == null) {
            return null;
        }
        return fallbackModelMap.getOrDefault(originalModel, fallbackModelMap.get(WILDCARD_MATCH_STR));
    }

    /**
     * 获取降级结果，含“*”通用匹配，匹配不到返回null
     *
     * @param originalValue
     * @param fallbackMap
     * @return
     */
    private String getFallbackValue(String originalValue, Map<String, String> fallbackMap) {
        if (StringUtils.isBlank(originalValue) || fallbackMap == null) {
            return null;
        }
        String fallbackValue = fallbackMap.getOrDefault(originalValue, fallbackMap.get(WILDCARD_MATCH_STR));
        return StringUtils.isNotBlank(fallbackValue) ? fallbackValue : null;
    }

}
