package com.sankuai.dzim.pilot.process.aiphonecall.data;

import com.sankuai.dzim.pilot.enums.AIPhoneCallDialogInfoKeyEnum;
import com.sankuai.dzim.pilot.enums.AIPhoneCallDynamicParamEnum;
import com.sankuai.dzim.pilot.enums.AIPhoneCallSceneTypeEnum;
import com.sankuai.dzim.pilot.enums.AIPhoneCallSourceEnum;
import lombok.Data;

import java.util.Map;

@Data
public class AIPhoneCallBackData {

    /**
     * 外呼场景
     */
    private AIPhoneCallSceneTypeEnum aiPhoneCallSceneTypeEnum;

    /**
     * 业务标识ID
     */
    private String bizId;

    /**
     * 业务透传数据
     */
    private String bizData;

    /**
     * 外呼任务id
     */
    private long callTaskId;

    /**
     * 外呼拉黑明细
     */
    private Integer blackType;

    /**
     * 外呼来源
     */
    private AIPhoneCallSourceEnum aiPhoneCallSourceEnum;

    /**
     * 通话内容信息提取结果
     * @see AIPhoneCallDialogInfoKeyEnum
     */
    private Map<String, Object> callDialogExtractInfo;
}
