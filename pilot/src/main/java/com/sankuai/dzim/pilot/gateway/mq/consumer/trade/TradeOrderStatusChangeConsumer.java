package com.sankuai.dzim.pilot.gateway.mq.consumer.trade;

import com.dianping.vc.mq.client.api.annotation.Consumer;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.core.type.TypeReference;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mtrace.Tracer;
import com.meituan.nibtp.trade.client.buy.enums.DomainStatusTypeEnum;
import com.meituan.nibtp.trade.client.buy.enums.OrderRefundStatusEnum;
import com.meituan.nibtp.trade.client.buy.enums.OrderStatusEnum;
import com.meituan.nibtp.trade.client.common.dto.DomainStatusChangeDTO;
import com.meituan.nibtp.trade.client.event.dto.TradeEventDTO;
import com.meituan.nibtp.trade.client.event.enums.TradeEventTypeEnum;
import com.meituan.nibtp.trade.client.fulfill.enums.ConsumeItemStatusEnum;
import com.meituan.nibtp.trade.client.fulfill.enums.FulfillmentStatusEnum;
import com.meituan.nibtp.trade.client.refund.enums.RefundStatusEnum;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.enums.GroupBuyOrderBizStatusEnum;
import com.sankuai.dzim.pilot.enums.MessageBizRefTypeEnum;
import com.sankuai.dzim.pilot.process.PilotMessageBizRefProcessService;
import com.sankuai.dzim.pilot.process.data.bizref.GroupBuyOrderExtra;
import com.sankuai.dzim.pilot.process.data.bizref.MessageBizRefData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * Agent交易消息监听，包含：团购订单、预订
 * 接口文档：https://km.sankuai.com/collabpage/2706974132
 *
 * @author: zhouyibing
 * @date: 2025/4/23
 */
@Slf4j
@Component
public class TradeOrderStatusChangeConsumer {

    private static final String TRADE_BIZ_CODE = "nib.general.groupbuy";

    private static final String TRADE_BOOK_BIZ_CODE = "nib.general.shop.book";

    @Autowired
    private PilotMessageBizRefProcessService pilotMessageBizRefProcessService;

    @Consumer(nameSpace = "daozong",
            appKey = "com.sankuai.mim.pilot",
            group = "pilot.agent.trade.order.status.change.consumer",
            topic = "pilot.agent.trade.order.status.change"
    )
    public ConsumeStatus recvMessage(String mafkaMessage) {
        log.info("TradeOrderStatusChangeConsumer receive message:{}", mafkaMessage);
        TradeEventDTO<DomainStatusChangeDTO> data = JsonCodec.decode(mafkaMessage, new TypeReference<TradeEventDTO<DomainStatusChangeDTO>>() {});
        if (data == null) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        if (!Objects.equals(data.getEventType(), TradeEventTypeEnum.DOMAIN_STATUS_CHANGE.getCode())) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        if (data.getEventBody() == null) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        try {
            boolean isSuccess = false;

            //到综团购订单
            if (TRADE_BIZ_CODE.equals(data.getBizCode())) {
                isSuccess = processGroupByOrder(data);
            }

            //预订单
            if (TRADE_BOOK_BIZ_CODE.equals(data.getBizCode())) {
                isSuccess = processBookOrder(data);
            }

            if (!isSuccess) {
                LogUtils.logFailLog(log, TagContext.builder().action("TradeOrderStatusChangeConsumer").build(),
                                    WarnMessage.build("TradeOrderStatusChangeConsumer", "Agent交易事件消费失败", ""),
                                    data, false);
                return ConsumeStatus.RECONSUME_LATER;
            }
            return ConsumeStatus.CONSUME_SUCCESS;
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("TradeOrderStatusChangeConsumer").build(),
                                WarnMessage.build("TradeOrderStatusChangeConsumer", "Agent交易事件消费异常", ""),
                                data, false, e);
            return ConsumeStatus.RECONSUME_LATER;
        }
    }

    private boolean processGroupByOrder(TradeEventDTO<DomainStatusChangeDTO> data) {
        //是否需要处理
        if (!needProcessGroupByOrder(data)) {
            return true;
        }
        return pilotMessageBizRefProcessService.processGroupBuyOrderChange(data.getOrderId());
    }

    private boolean needProcessGroupByOrder(TradeEventDTO<DomainStatusChangeDTO> eventDTO) {
        DomainStatusChangeDTO statusChangeDTO = eventDTO.getEventBody();
        //仅处理特定事件类型
        if (!needProcessGroupByOrderStatusChange(statusChangeDTO)) {
            return false;
        }

        //仅处理消息关联过的订单号
        MessageBizRefData<GroupBuyOrderExtra> messageBizRef = pilotMessageBizRefProcessService.getMessageBizRef(
                MessageBizRefTypeEnum.GROUP_BUY_ORDER.getType(), String.valueOf(eventDTO.getOrderId()),
                GroupBuyOrderBizStatusEnum.PAYMENT_CARD.name(), GroupBuyOrderExtra.class);
        return messageBizRef != null;
    }

    private boolean needProcessGroupByOrderStatusChange(DomainStatusChangeDTO statusChangeDTO) {
        Integer type = statusChangeDTO.getType();

        //支付前取消
        if (type == DomainStatusTypeEnum.ORDER.getCode() && statusChangeDTO.getToStatus() == OrderStatusEnum.CANCELED.getCode()) {
            return true;
        }
        //已发货，可以展示券码
        if (type == DomainStatusTypeEnum.FULFILLMENT.getCode() && statusChangeDTO.getToStatus() == FulfillmentStatusEnum.DELIVERED.getCode()) {
            return true;
        }
        //消费成功
        if (type == DomainStatusTypeEnum.CONSUME_ITEM.getCode() && statusChangeDTO.getToStatus() == ConsumeItemStatusEnum.CONSUMED.getCode()) {
            return true;
        }
        //退款成功
        if (type == DomainStatusTypeEnum.ORDER_REFUND.getCode() && statusChangeDTO.getToStatus() == OrderRefundStatusEnum.STATUS_SUCCESS.getCode()) {
            return true;
        }

        return false;
    }

    private boolean processBookOrder(TradeEventDTO<DomainStatusChangeDTO> data) {
        DomainStatusChangeDTO statusChangeDTO = data.getEventBody();
        //是否需要处理
        Integer type = statusChangeDTO.getType();
        // 预订失败
        if (type == DomainStatusTypeEnum.FULFILLMENT.getCode() && statusChangeDTO.getToStatus() == OrderStatusEnum.CANCELED.getCode()) {
            return pilotMessageBizRefProcessService.processBookFail(data.getOrderId());
        }
        // 预订已取消
        if (type == DomainStatusTypeEnum.REFUND_REQUEST.getCode() && statusChangeDTO.getToStatus() == RefundStatusEnum.APPROVED.getCode()) {
            return pilotMessageBizRefProcessService.processCancelBookSuccess(data.getOrderId());
        }
        // 预订成功
        if (type == DomainStatusTypeEnum.FULFILLMENT.getCode() && statusChangeDTO.getToStatus() == FulfillmentStatusEnum.DELIVERED.getCode()) {
            return pilotMessageBizRefProcessService.processBookSuccess(data.getOrderId());
        }
        return true;
    }
}
