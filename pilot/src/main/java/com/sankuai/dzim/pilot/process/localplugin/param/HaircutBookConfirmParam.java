package com.sankuai.dzim.pilot.process.localplugin.param;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/02/24 17:33
 */
@Data
public class HaircutBookConfirmParam extends Param {
    @JsonPropertyDescription("预约商户位置中与行政区、商圈、地铁站等相关的信息")
    @JsonProperty(required = false)
    private String positionInfo;

    @JsonPropertyDescription("预约商户位置中与当前用户当前位置距离信息对应的distanceId")
    @JsonProperty(required = false)
    private Integer distanceId;

    @JsonPropertyDescription("预约商户位置中与具体门店名相关的信息,可能有多个门店")
    @JsonProperty(required = false)
    private List<String> shopNames;

    @JsonPropertyDescription("用户手机号")
    @JsonProperty(required = false)
    private String userPhone;

    @JsonPropertyDescription("预约时间在天的维度,只支持[\"现在\",\"今天\",\"明天\",\"后天\",\"大后天\"]")
    @JsonProperty(required = false)
    private String bookDay;

    @JsonPropertyDescription("预约时间在天的维度,但是是具体日期格式,格式为MM-dd")
    @JsonProperty(required = false)
    private String bookDate;

    @JsonPropertyDescription("预约时间在小时维度,格式为HH:mm")
    @JsonProperty(required = false)
    private String bookStartTime;

    @JsonPropertyDescription("预约结束时间,格式为HH:mm")
    @JsonProperty(required = false)
    private String bookEndTime;

    @JsonPropertyDescription("预约类型,1-剪发,2-烫发,3-染发")
    @JsonProperty(required = false)
    private int bookType;

    @JsonPropertyDescription("预约价格对应的priceId")
    @JsonProperty(required = false)
    private Integer priceId;

    @JsonPropertyDescription("信息确认话语,当必要信息齐全时,用于向用户确认信息是否正确")
    @JsonProperty(required = false)
    private String confirmInfo;


    @JsonPropertyDescription("信息缺失或错误提示话语,当必要信息缺失或其它信息错误时,用于向用户提示")
    @JsonProperty(required = false)
    private String errorInfo;
}
