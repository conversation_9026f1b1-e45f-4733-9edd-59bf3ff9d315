package com.sankuai.dzim.pilot.gateway.api.vo.medical;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @author: zhouyibing
 * @date: 2024/9/5
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@MobileDo
public class S3UploadSignVO implements Serializable {

    @MobileDo.MobileField(key = 0xa745)
    private String signature;

    @MobileDo.MobileField(key = 0xce6)
    private String awsAccessKeyId;

    @MobileDo.MobileField(key = 0x605f)
    private String policy;

    @MobileDo.MobileField(key = 0x4b71)
    private String endPoint;

    @MobileDo.MobileField(key = 0x7101)
    private String bucketName;

    @MobileDo.MobileField(key = 0xe5f9)
    private String objectName;

    @MobileDo.MobileField(key = 0xc56e)
    private String url;
}
