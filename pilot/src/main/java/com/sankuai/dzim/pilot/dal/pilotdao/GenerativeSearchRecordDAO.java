package com.sankuai.dzim.pilot.dal.pilotdao;

import com.sankuai.dzim.pilot.dal.entity.GenerativeSearchRecordEntity;
import org.apache.ibatis.annotations.Param;

/**
 * @author: zhouyibing
 * @date: 2024/5/24
 */
public interface GenerativeSearchRecordDAO {

    int insert(@Param("entity") GenerativeSearchRecordEntity entity);

    GenerativeSearchRecordEntity selectById(@Param("id") Long id);

    int updateFeedbackById(@Param("entity") GenerativeSearchRecordEntity entity);
}
