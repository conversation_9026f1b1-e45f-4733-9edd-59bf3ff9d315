package com.sankuai.dzim.pilot.domain.retrieval;

import com.alibaba.fastjson.JSON;
import com.sankuai.dzim.pilot.acl.HaimaAclService;
import com.sankuai.dzim.pilot.domain.annotation.Retrieval;
import com.sankuai.dzim.pilot.domain.retrieval.data.RetrievalContext;
import com.sankuai.dzim.pilot.domain.retrieval.data.RetrievalResult;
import com.sankuai.dzim.pilot.process.aireservebook.data.StandardReserveItem;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: wuwen<PERSON><PERSON>
 * @create: 2025-04-20
 * @description:
 */
@Component
@Retrieval(name = "PoiStandardReserveItemsRetrieval", desc = "行业标准预约要素检索，根据后台类目匹配")
public class PoiStandardReserveItemsAugmentor implements RetrievalAugmentor {

    @Autowired
    private HaimaAclService haimaAclService;

    private static final String SECOND_BACK_CATEGORY_ID = "secondBackCategoryId";

    @Override
    public String retrieval(RetrievalContext context) {
        if (context == null) {
            return null;
        }
        Integer backCategoryId = getBackCategories(context);
        if (backCategoryId == null) {
            return null;
        }
        List<StandardReserveItem> standardReserveItems = haimaAclService.queryStandardReserveItems(backCategoryId);
        if (CollectionUtils.isEmpty(standardReserveItems)) {
            return null;
        }
        return String.format("标准预约要素: %s \n\n", JSON.toJSONString(standardReserveItems));
    }

    private Integer getBackCategories(RetrievalContext context) {
        if (MapUtils.isEmpty(context.getExtraInfo()) || !context.getExtraInfo().containsKey(SECOND_BACK_CATEGORY_ID)) {
            return null;
        }
        Map<String, String> extraInfo = context.getExtraInfo();
        String backCategoryId = extraInfo.get(SECOND_BACK_CATEGORY_ID);
        if (StringUtils.isEmpty(backCategoryId)) {
            return null;
        }
        return Integer.parseInt(backCategoryId);

    }

    @Override
    public RetrievalResult retrieveKnowledge(RetrievalContext context) {
        return null;
    }
}
