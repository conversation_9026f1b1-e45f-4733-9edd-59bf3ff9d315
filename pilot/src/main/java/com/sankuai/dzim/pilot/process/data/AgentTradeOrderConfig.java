package com.sankuai.dzim.pilot.process.data;

import lombok.Data;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * @author: zhouyibing
 * @date: 2025/4/26
 */
@Data
public class AgentTradeOrderConfig {

    private Map<String, String> orderDetailPageMap;

    private Map<String, DisplayTextConfig> displayTextConfigMap;

    @Data
    public static class DisplayTextConfig {

        public String loadingStatusText;

        public String title;

        public String headInfo;

        public String tailInfo;
    }

    public String buildOrderDetailUrl(Long orderId, Integer platform) {
        if (orderId == null || platform == null) {
            return null;
        }
        String orderPageDetailFormat = MapUtils.getString(orderDetailPageMap, String.valueOf(platform));
        if (StringUtils.isBlank(orderPageDetailFormat)) {
            return null;
        }
        return String.format(orderPageDetailFormat, String.valueOf(orderId));
    }

    public DisplayTextConfig getDisplayTextConfig(Integer status) {
        return (DisplayTextConfig) MapUtils.getObject(displayTextConfigMap, String.valueOf(status));
    }
}
