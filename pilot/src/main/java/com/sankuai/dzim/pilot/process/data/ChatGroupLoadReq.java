package com.sankuai.dzim.pilot.process.data;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/08/23 10:00
 */
@Data
public class ChatGroupLoadReq {
    /**
     * 用户ID
     */
    private String imUserId;
    /**
     * 助手类型
     */
    private int assistantType;
    /**
     * 平台类型：1-点评 2-美团
     */
    private int platform;
    /**
     * 门店ID
     */
    private long shopId;
    /**
     * 团单ID
     */
    private long dealId;
    /**
     * 发送内容
     */
    private String preAsk;
    /**
     * 前置点击问题ID
     */
    private long questionId;
    /**
     * 需要补充的额外信息
     */
    private String bizParams;
}
