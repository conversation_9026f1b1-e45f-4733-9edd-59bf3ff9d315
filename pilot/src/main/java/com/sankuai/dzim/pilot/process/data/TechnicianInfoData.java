package com.sankuai.dzim.pilot.process.data;

import com.sankuai.it.xcontract.easypoi.annotation.Excel;
import lombok.Data;

@Data
public class TechnicianInfoData {

    @Excel(name = "technician_id")
    private long technicianId;

    @Excel(name = "technician_name")
    private String technicianName;

    @Excel(name = "mt_shop_id")
    private long mtShopId;

    @Excel(name = "photo_url")
    private String photo;

    @Excel(name = "title")
    private String title;

    @Excel(name = "skill")
    private String skill;

    @Excel(name = "summary")
    private String summary;
}
