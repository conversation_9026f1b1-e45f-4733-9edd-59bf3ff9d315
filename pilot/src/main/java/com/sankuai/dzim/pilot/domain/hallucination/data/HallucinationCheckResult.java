package com.sankuai.dzim.pilot.domain.hallucination.data;

import com.sankuai.dzim.pilot.chain.data.AIAnswerData;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class HallucinationCheckResult {

    /**
     * 答案是否正确
     */
    private boolean correct = false;

    /**
     * 判断的原因
     */
    private String reason;

    /**
     * 是否需要修正
     */
    private boolean needModify = false;

    /**
     * 修正后的答案
     */
    private String modifyAnswer;

    public AIAnswerData getCorrectAnswer(AIAnswerData answer) {
        if (correct) {
            return answer;
        }
        if (needModify && StringUtils.isNotBlank(modifyAnswer)) {
            return new AIAnswerData(answer.getAiAnswerType(), modifyAnswer, answer.getExtraInfo());
        }
        return new AIAnswerData(StringUtils.EMPTY);
    }
}
