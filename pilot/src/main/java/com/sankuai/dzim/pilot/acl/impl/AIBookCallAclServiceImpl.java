package com.sankuai.dzim.pilot.acl.impl;

import com.dianping.cat.Cat;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.acl.AIBookCallAclService;
import com.sankuai.meituan.wmdrecsys.aigc.api.domain.outboundcall.OutboundCallRequest;
import com.sankuai.meituan.wmdrecsys.aigc.api.domain.outboundcall.OutboundCallResponse;
import com.sankuai.meituan.wmdrecsys.aigc.api.service.iface.OutboundCallThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2025/1/9 15:35
 */
@Slf4j
@Service
public class AIBookCallAclServiceImpl implements AIBookCallAclService {

    @Resource
    private OutboundCallThriftService.Iface outboundCallThriftService;

    @Override
    public Pair<Integer, String> sendHairstylingOutboundCall(OutboundCallRequest request) {
        try {
            if (request == null) {
                return Pair.of(0, StringUtils.EMPTY);
            }
            OutboundCallResponse outboundCallResponse = outboundCallThriftService.sendHairstylingOutboundCall(request);
            if (outboundCallResponse == null || outboundCallResponse.getCode() != 0) {
                Cat.logEvent("AIBookCall", "OutboundCallFailed");
                LogUtils.logFailLog(log, TagContext.builder().action("sendHairstylingOutboundCall").build(),
                        WarnMessage.build("sendHairstylingOutboundCall", "美发不用等拨打外呼失败", ""),
                        request, outboundCallResponse);
                return Pair.of(Optional.ofNullable(outboundCallResponse).map(OutboundCallResponse::getCode).orElse(0), StringUtils.EMPTY);
            }
            LogUtils.logReturnInfo(log, TagContext.builder().action("sendHairstylingOutboundCall").build(),
                    WarnMessage.build("sendHairstylingOutboundCall", "美发不用等拨打外呼", ""),
                    request, outboundCallResponse);
            return Pair.of(outboundCallResponse.getCode(), outboundCallResponse.getContactId());
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("sendHairstylingOutboundCall").build(),
                    WarnMessage.build("sendHairstylingOutboundCall", "美发不用等拨打外呼", ""), request, e);
            return Pair.of(0, StringUtils.EMPTY);
        }
    }
}
