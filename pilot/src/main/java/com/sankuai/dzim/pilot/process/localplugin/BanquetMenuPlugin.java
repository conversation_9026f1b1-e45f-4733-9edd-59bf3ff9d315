package com.sankuai.dzim.pilot.process.localplugin;

import com.sankuai.dzim.message.dto.MessageDTO;
import com.sankuai.dzim.pilot.chain.data.AIAnswerData;
import com.sankuai.dzim.pilot.chain.data.AIServiceContext;
import com.sankuai.dzim.pilot.chain.data.RetrievalComponent;
import com.sankuai.dzim.pilot.chain.enums.AIServiceExtraKeyEnum;
import com.sankuai.dzim.pilot.chain.impl.SimpleRagAIService;
import com.sankuai.dzim.pilot.domain.annotation.LocalPlugin;
import com.sankuai.dzim.pilot.process.localplugin.param.BanquetMenuQueryParam;
import com.sankuai.dzim.pilot.utils.IMConstants;
import com.sankuai.dzim.pilot.utils.LionConfigUtil;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class BanquetMenuPlugin {

    @Autowired
    private SimpleRagAIService simpleRagAIService;

    @Autowired
    private LionConfigUtil lionConfigUtil;

    @LocalPlugin(name = "BanquetMenuQueryTool", description = "可以为你提供宴会菜单相关的信息,解答宴会菜单相关的问题", returnDirect = false)
    public AIAnswerData getBanquetMenuAnswer(BanquetMenuQueryParam banquetMenuQueryParam) {
        if (banquetMenuQueryParam == null) {
            return new AIAnswerData("参数错误");
        }
        if (banquetMenuQueryParam.getShopId() <= 0) {
            return new AIAnswerData("请正确提取出用户咨询的门店Id");
        }
        if (banquetMenuQueryParam.getQuestion().equals("")) {
            return new AIAnswerData("没有菜单相关问题请不要使用该工具");
        }

        return simpleRagAIService.execute(buildAIServiceContext(banquetMenuQueryParam));
    }

    private AIServiceContext buildAIServiceContext(BanquetMenuQueryParam banquetMenuQueryParam) {
        AIServiceContext aiServiceContext = new AIServiceContext();
        aiServiceContext.setModel(IMConstants.DEFAULT_PLUGIN_LLM_MODEL);
        aiServiceContext.setTemperature(IMConstants.DEFAULT_PLUGIN_LLM_MODEL_TEMPERATURE);
        aiServiceContext.setTopP(IMConstants.DEFAULT_PLUGIN_LLM_MODEL_TOP_P);
        aiServiceContext.setAppId(IMConstants.DEFAULT_PLUGIN_LLM_MODEL_APP_ID);
        aiServiceContext.setMessageDTO(buildMessageDTO(banquetMenuQueryParam.getQuestion()));
        aiServiceContext.setRetrievalComponents(buildRetrievalComponents(banquetMenuQueryParam));
        aiServiceContext.setSystemPrompt(lionConfigUtil.getPluginName2PluginSystemPromptMap().get("BanquetMenuPlugin"));
        return aiServiceContext;
    }

    private List<RetrievalComponent> buildRetrievalComponents(BanquetMenuQueryParam banquetMenuQueryParam) {
        List<RetrievalComponent> retrievalComponents = Lists.newArrayList();
        RetrievalComponent retrievalComponent = new RetrievalComponent();
        retrievalComponent.setName("BanquetMenuRetrieval");
        retrievalComponents.add(retrievalComponent);

        setRetrievalComponentExtraInfo(retrievalComponent, banquetMenuQueryParam);
        return retrievalComponents;
    }

    private void setRetrievalComponentExtraInfo(RetrievalComponent retrievalComponent, BanquetMenuQueryParam banquetMenuQueryParam) {
        retrievalComponent.addExtraInfo(AIServiceExtraKeyEnum.DP_SHOP_ID.getKey(), String.valueOf(banquetMenuQueryParam.getShopId()));
    }

    private MessageDTO buildMessageDTO(String question) {
        MessageDTO messageDTO = new MessageDTO();
        messageDTO.setMessage(question);
        return messageDTO;
    }
}
