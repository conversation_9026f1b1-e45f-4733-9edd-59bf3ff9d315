package com.sankuai.dzim.pilot.process.search.strategy.padding;

import com.sankuai.dzim.pilot.process.search.data.ReserveQueryContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class ReserveProductPaddingFactory {

    @Resource
    private List<ReserveProductPaddingHandler> handlers;

    public ReserveProductPaddingHandler getHandler(ReserveQueryContext queryContext) {
        for (ReserveProductPaddingHandler handler : handlers) {
            if (handler.match(queryContext)) {
                return handler;
            }
        }
        return null;
    }
}
