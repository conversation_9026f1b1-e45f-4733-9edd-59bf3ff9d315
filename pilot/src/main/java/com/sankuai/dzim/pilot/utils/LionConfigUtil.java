package com.sankuai.dzim.pilot.utils;

import com.dianping.lion.client.ConfigRepository;
import com.dianping.lion.client.Lion;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.dzim.pilot.acl.data.ChatCompletionConfig;
import com.sankuai.dzim.pilot.api.data.search.generative.AnswerItemE;
import com.sankuai.dzim.pilot.domain.data.BeamFixedContentConfig;
import com.sankuai.dzim.pilot.buffer.stream.build.ext.data.ThinkProcessData;
import com.sankuai.dzim.pilot.domain.data.ModelConfig;
import com.sankuai.dzim.pilot.domain.retrieval.data.PersonaLabelConfig;
import com.sankuai.dzim.pilot.gateway.api.aibook.data.BookingPriceDetailData;
import com.sankuai.dzim.pilot.gateway.api.aibook.data.NaviVO;
import com.sankuai.dzim.pilot.gateway.api.vo.RecommendQuestionsVO;
import com.sankuai.dzim.pilot.process.MedicalBeautyBlzConfigData;
import com.sankuai.dzim.pilot.process.aibook.data.*;
import com.sankuai.dzim.pilot.process.aiphonecall.data.AIPhoneCallConfig;
import com.sankuai.dzim.pilot.process.aiphonecall.data.IvrBotConfig;
import com.sankuai.dzim.pilot.process.data.*;
import com.sankuai.dzim.pilot.process.router.data.TemplateConfig;
import com.sankuai.dzim.pilot.scene.data.AssistantSceneConfig;
import com.sankuai.dzim.pilot.scene.task.data.TaskConfig;
import lombok.Data;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Data
@Component
public class LionConfigUtil {
    @MdpConfig("com.sankuai.mim.pilot.knowledge.embedding.appid")
    private String knowledgeEmbeddingAppId;
    @MdpConfig("com.sankuai.mim.pilot.vex.set.name")
    private String vexSetName;
    /**
     * 插件运行的最大次数，避免死循环和成本问题
     */
    @ConfigValue(key = "com.sankuai.mim.pilot.max.plugin.running.times", defaultValue = "5")
    private int maxPluginRunningTimes;
    /**
     * 推荐回复行业配置, key: chatType.categoryId0.categoryId1.categoryId2; value:行业推荐回复配置
     */
    @MdpConfig(key = "com.sankuai.mim.pilot.recommend.answer.config")
    private String recommendAnswerConfigMapString;

    public Map<String, IntelligentCustomerConfig> getChatType2RecommendAnswerConfigMap() {
        Map<String, IntelligentCustomerConfig> chatType2RecommendAnswerConfigMap = JsonCodec.converseMap(recommendAnswerConfigMapString, String.class, IntelligentCustomerConfig.class);
        return chatType2RecommendAnswerConfigMap;
    }

    @ConfigValue(key = "com.sankuai.mim.pilot.assistant.welcome.config", defaultValue = "{}")
    private Map<String, AssistantWelcomeConfigData> assistantWelcomeConfigMap;

    /**
     * LLM回复是否有效判定的关键词
     */
    @ConfigValue(key = "com.sankuai.mim.pilot.recommend.answer.notvalid.key.words", defaultValue = "[]")
    private String LLMReplyNotValidJudgeKeyWordsString;

    public List<String> getLLMReplyNotValidJudgeKeyWords() {
        return JsonCodec.converseList(LLMReplyNotValidJudgeKeyWordsString, String.class);
    }

    /**
     * 用户每天咨询推荐回复的次数，用于控制大模型成本
     */
    @ConfigValue(key = "com.sankuai.mim.pilot.max.user.day.recmd.reply.times", defaultValue = "5")
    private int maxUserDayRecmdReplyTimes;
    /**
     * 消息推送的点评门店id白名单，为空为全量，因为发消息里面没有账号id，所以没法从账号维度进行灰度,
     */
    @ConfigValue(key = "com.sankuai.mim.pilot.merchant.recmd.shop.whitelist", defaultValue = "[1]")
    private List<Long> merchantRecmdShopWhiteList;
    /**
     * Friday 单个AppId 单日最大调用次数
     */
    @ConfigValue(key = "com.sankuai.mim.pilot.friday.max.invoke.times", defaultValue = "10000")
    private int maxFridayInvokeTimes;
    /**
     * 商家每天自动回复的次数，用于控制大模型成本
     */
    @ConfigValue(key = "com.sankuai.mim.pilot.max.shop.day.auto.reply.times", defaultValue = "100")
    private int maxShopDayAutoReplyTimes;

    public boolean isShopInMerchantRecmdShopWhiteList(long dpShopId) {
        if (CollectionUtils.isEmpty(merchantRecmdShopWhiteList)) {
            return true;
        }
        return merchantRecmdShopWhiteList.contains(dpShopId);
    }

    /**
     * LLM自动回复行业配置, key: chatType.categoryId0.categoryId1.categoryId2; value:行业自动回复配置
     */
    @ConfigValue(key = "com.sankuai.mim.pilot.auto.reply.llm.config")
    private Map<String, IntelligentCustomerConfig> autoReplyLLMConfig;
    /**
     * 插件到插件系统提示词map
     */
    @ConfigValue(key = "com.sankuai.mim.pilot.plugin.system.prompt")
    private Map<String, String> pluginName2PluginSystemPromptMap;
    @ConfigValue(key = "com.sankuai.mim.pilot.auto.reply.send.leads.card.max.times", defaultValue = "2")
    private int sendLeadsCardMaxTimes;
    @ConfigValue(key = "com.sankuai.mim.pilot.auto.reply.switch", defaultValue = "false")
    private boolean autoReplySwitch;
    /**
     * templatetype 不同lion的配置
     */
    @ConfigValue(key = "com.sankuai.mim.pilot.generativesearch.templatetype.configs", defaultValue = "{}")
    public Map<Integer, TemplateTypeLionData> templateTypeLionDataMap;

    @ConfigValue(key = "com.sankuai.mim.pilot.generativesearch.biztype.configs", defaultValue = "{}")
    public Map<Integer, BizTypeLionData> bizTypeLionDataMap;

    @ConfigValue(key = "com.sankuai.mim.pilot.generativesearch.assistanttype.jumpurl", defaultValue = "[]")
    public String templateTypeJumpUrl;
    @ConfigValue(key = "com.sankuai.mim.pilot.generativesearch.biztye2assistanttype", defaultValue = "{}")
    public Map<Integer, Integer> bizType2AssistantType;
    @ConfigValue(key = "com.sankuai.mim.pilot.generativesearch.assistanttype2biztype", defaultValue = "{}")
    public Map<Integer, Integer> assistantType2BizType;
    /**
     * 内部小助手配置
     */
    @ConfigValue(key = "com.sankuai.mim.pilot.assistants", defaultValue = "{}")
    public List<AssistantData> assistantDataList;
    /**
     * 推荐问题 和 对应的小助手
     */
    @ConfigValue(key = "com.sankuai.mim.pilot.recommend.question", defaultValue = "{}")
    public List<RecommendQuestionsVO> assistantTypeRecommendQuestionList;

    public AssistantWelcomeConfigData getAssistantWelcomeConfig(String assistantType) {
        if (MapUtils.isEmpty(assistantWelcomeConfigMap) || !assistantWelcomeConfigMap.containsKey(assistantType)) {
            return null;
        }
        return assistantWelcomeConfigMap.get(assistantType);
    }

    /**
     * 小助手分场景配置(内部助手+导购助手)
     */
    @ConfigValue(key = "com.sankuai.mim.pilot.assistant.scene.config", defaultValue = "{}")
    public HashMap<Integer, AssistantSceneConfig> originConfig;


    public AssistantSceneConfig getAssistantSceneConfigMap(int assistantType) {
        if (originConfig.containsKey(assistantType)) {
            return originConfig.get(assistantType);
        }
        ConfigRepository beautyAssistantConfig = Lion.getConfigRepository("com.sankuai.mim.pilot", "assistant");
        AssistantSceneConfig config = beautyAssistantConfig.getBean("com.sankuai.mim.pilot.assistant.scene.config_" + assistantType, AssistantSceneConfig.class);
        return config;
    }

    /**
     * 小助手场景task配置，如果assistantSceneConfigMap没有对task进行配置，则使用这个默认配置
     */
    @ConfigValue(key = "com.sankuai.mim.pilot.assistant.default.task.config", defaultValue = "{}")
    public HashMap<String, TaskConfig> assistantDefaultTaskConfigMap;
    /**
     * 发送小助手欢迎语的间隔时间，单位为分钟
     */
    @ConfigValue(key = "com.sankuai.mim.pilot.assistant.welcome.interval", defaultValue = "30")
    private int sendWelcomeIntervalMinute;

    /**
     * 生成的问题禁用词，如果有对应的词，则不生成对应问题的Extend数据
     */
    @ConfigValue(key = "com.sankuai.mim.pilot.generativeQuestionForbiddenWords", defaultValue = "[]")
    private Map<String, List<String>> generativeQuestionForbiddenWords;

    /**
     * 小美问问助手类型
     */
    @ConfigValue(key = "com.sankuai.mim.pilot.beauty.ask.assistant.type.list", defaultValue = "[]")
    private List<Integer> beautyAskAssistantIds;

    /**
     * 小美问问助手欢迎语发送间隔时长配置
     */
    @ConfigValue(key = "com.sankuai.mim.pilot.beauty.ask.assistant.type.welcome.interval.map", defaultValue = "{}")
    private Map<Integer, Integer> assistantType2WelcomeInterval;

    /**
     * 频道页小助手用户画像标签配置
     */
    @ConfigValue(key = "com.sankuai.mim.pilot.assistant.channel.page.user.label.list", defaultValue = "{}")
    private List<PersonaLabelConfig> assistantChannelPageUserLabel;

    /**
     * 用户意图画像标签配置
     */
    @ConfigValue(key = "com.sankuai.mim.pilot.assistant.list.purpose.user.label", defaultValue = "{}")
    private Map<String, List<PersonaLabelConfig>> listPurposeUserLabel;

    /**
     * 足疗美发意图识别的模型配置
     */
    @ConfigValue(key = "com.sankuai.mim.pilot.agent.purpose.model.config", defaultValue = "{}")
    private Map<String, ModelConfig> agentPurposeModelConfig;

    /**
     * 足疗美发意图识别 列表页悬浮球配置
     */
    @ConfigValue(key = "com.sankuai.mim.pilot.agent.purpose.ball.config", defaultValue = "{}")
    private Map<String, Map<String, Object>> agentPurposeBallConfig;

    /**
     * categoryId 以及对应类目名称 prompt使用
     */
    @ConfigValue(key = "com.sankuai.mim.pilot.agent.categoryId.item.name", defaultValue = "{}")
    private Map<String, String> agentCategoryIdItemName;

    /**
     * 频道页列表页判断是否出悬浮球的配置，命中则出
     */
    @ConfigValue(key = "com.sankuai.mim.pilot.agent.show.entry", defaultValue = "{}")
    private Map<String, Object> agentShowEntry;

    /**
     * 频道页小助手文案关键词配置
     */
    @ConfigValue(key = "com.sankuai.mim.pilot.channel.search.default", defaultValue = "{\\\"noShopDefaultReply\\\":\\\"暂时没有找到合适的商家，请稍后再试。 \\\"}")
    private ChannelSearchAssistantConfig channelSearchAssistantConfig;

    /**
     * 生成式搜索搜索词和问题直接映射
     */
    @ConfigValue(key = "com.sankuai.mim.pilot.searchword.map.question.config", defaultValue = "{}")
    private Map<String, Map<String, SugConfig>> searchWordQuestionMap;

    /**
     * 各个biztype中和百补冲突词
     */
    @ConfigValue(key = "com.sankuai.mim.pilot.baibuconflict.word", defaultValue = "{}")
    private Map<String, List<String>> baiBuConflictSearchWords;


    @ConfigValue(key = "com.sankuai.mim.pilot.shop.detail.plugin.review.topk", defaultValue = "500")
    private int shopDetailPluginReviewTopK;

    /**
     * 小美问问的问题评分其它相关配置
     */
    @ConfigValue(key = "com.sankuai.mim.pilot.question.uv.config", defaultValue = "{\"clickRatio\":0.5,\"categoryRatio\":0.2,\"excellentRatio\":0.3,\"baseJumpUrl\":\"\",\"querySize\":10,\"pageSourceQuestionMap\":{\"2\":\"帮我总结门店亮点\"},\"excellentQuestions\":[], \"reviewLimit\": 10}")
    private QuestionUVConfig questionUVConfig;
    /**
     * querySugList根据biztype设置不同的获取sug的组合
     */
    @ConfigValue(key = "com.sankuai.mim.pilot.suglist.order", defaultValue = "{}")
    private Map<String, List<SugListStrategyData>> querySugListOrder;

    /**
     * 理疗场景下获取sug所需配置
     */
    @ConfigValue(key = "com.sankuai.mim.pilot.suglist.therapy", defaultValue = "{}")
    private TherapySugListConfigData therapySugListConfigData;

    /**
     * 理疗场景下获取answer所需配置
     */
    @ConfigValue(key = "com.sankuai.mim.pilot.answer.therapy", defaultValue = "{}")
    private List<AnswerItemE> therapyAnswerConfigData;

    /**
     * 理疗场景下获取艾灸、拔罐、刮痧项目对应的笔记id
     */
    @ConfigValue(key = "com.sankuai.mim.pilot.answer.therapy.noteids", defaultValue = "{}")
    private Map<String, List<Long>> therapyNoteIds;


    /**
     * llm质检prompt以及llm参数
     */
    @ConfigValue(key = "com.sankuai.mim.pilot.llm.qualitycheck.prompt", defaultValue = "{}")
    private LLMQualityCheckConfig llmQualityCheckPrompt;

    /**
     * 助手拉黑开关以及白名单
     */
    @ConfigValue(key = "com.sankuai.mim.pilot.llm.riskuser.block.whitelist", defaultValue = "{}")
    private BlockWhiteListConfig blockWhiteListConfig;

    @ConfigValue(key = "com.sankuai.mim.pilot.llm.riskuser.assistant.config", defaultValue = "{}")
    private Map<String, List<BlockStrategyConfig>> riskUserAssistantConfig;


    /**
     * 模板配置
     */
    @ConfigValue(key = "com.sankuai.mim.pilot.template.config", defaultValue = "")
    private Map<String, TemplateConfig> bizType2TemplateConfig;


    /**
     * 模板数据配置，针对特殊情况
     */
    @ConfigValue(key = "com.sankuai.mim.pilot.template.data.config", defaultValue = "{}")
    private Map<String, List<TemplateDataCollection>> bizTypeQuestion2TemplateData;

    /**
     * 模板数据映射，key：问题，value：映射问题
     * 所有value中的问题，都会映射到key中
     */
    @ConfigValue(key = "com.sankuai.mim.pilot.template.data.config.mapping", defaultValue = "{}")
    private Map<String, Set<String>> keyword2Question;

    /**
     * 医美大搜避雷针小助手相关配置
     */
    @ConfigValue(key = "com.sankuai.mim.pilot.medical.beauty.blz.config", defaultValue = "{}")
    private MedicalBeautyBlzConfigData medicalBeautyBlzConfig;

    /**
     * 不同状态的预约结果页固定内容（标题+副标题+提示文案）配置
     */
    @ConfigValue(key = "com.sankuai.mim.pilot.ai.book.page.fix.content.config", defaultValue = "{}")
    private BookPageFixedContentConfigData bookPageFixedContentConfig;

    /**
     * 美团订单中心跳链
     */
    @ConfigValue(key = "com.sankuai.mim.pilot.ai.book.mt.center.jump.url", defaultValue = "")
    private String mtBookCenterJumpUrl;

    /**
     * 点评订单中心跳链
     */
    @ConfigValue(key = "com.sankuai.mim.pilot.ai.book.dp.center.jump.url", defaultValue = "")
    private String dpBookCenterJumpUrl;

    /**
     * 预约时间段配置
     */
    @ConfigValue(key = "com.sankuai.mim.pilot.ai.book.time.period.config", defaultValue = "{}")
    private Map<String, List<TimePeriodConfigData>> timePeriodConfig;

    /**
     * 新版预约时间段配置
     */
    @ConfigValue(key = "com.sankuai.mim.pilot.ai.book.time.period.config.v2", defaultValue = "{}")
    private Map<String, List<TimePeriodConfigData>> timePeriodConfigV2;

    /**
     * V3版预约时间配置
     */
    @ConfigValue(key = "com.sankuai.mim.pilot.ai.book.time.period.config.v3", defaultValue = "{}")
    private Map<String, List<TimePeriodConfigData>> timePeriodConfigV3;

    /**
     * 用户每日最大预约次数
     */
    @ConfigValue(key = "com.sankuai.mim.pilot.ai.book.user.max.daily.usage.count", defaultValue = "5")
    private int userMaxDailyUsageCount;

    /**
     * 用户预约Mock数据
     */
    @ConfigValue(key = "com.sankuai.mim.pilot.ai.book.mock.config.data", defaultValue = "{}")
    private AIBookMockConfigData aiBookMockConfigData;

    @ConfigValue(key = "com.sankuai.mim.pilot.ai.book.dispatch.config", defaultValue = "{}")
    private DispatchConfigData dispatchConfigData;

    @ConfigValue(key = "com.sankuai.mim.pilot.ai.book.recommend.shop.config.data", defaultValue = "{}")
    private RecommendShopConfigData recommendShopConfigData;


    /**
     * AI预约入口配置
     */
    @ConfigValue(key = "com.sankuai.mim.pilot.ai.book.entry.config.data", defaultValue = "{}")
    private AIBookEntryConfigData aiBookEntryConfigData;

    /**
     * AI预约表单页默认选择位置
     */
    @ConfigValue(key = "com.sankuai.mim.pilot.ai.book.default.choose.position", defaultValue = "{}")
    private NaviVO defaultChoosePosition;

    @ConfigValue(key = "com.sankuai.mim.pilot.ai.book.hair.price.config", defaultValue = "{}")
    private Map<String, List<BookingPriceDetailData>> bookingPriceData;

    @ConfigValue(key = "com.sankuai.mim.pilot.ai.book.hair.endtime.delay.hour", defaultValue = "12")
    private long endTimeDelayHour;

    @ConfigValue(key = "com.sankuai.mim.pilot.ai.book.hair.public.price.switch", defaultValue = "false")
    private boolean publicHairPriceSwitch;

    @ConfigValue(key = "com.sankuai.mim.pilot.ai.book.hair.assistant.config", defaultValue = "{}")
    private AIBookAssistantConfigData aiBookAssistantConfigData;

    @ConfigValue(key = "com.sankuai.mim.pilot.tool.call.assistants", defaultValue = "[11,14,15]")
    private Set<Integer> toolCallAssistants;

    @ConfigValue(key = "com.sankuai.mim.pilot.assistant.sub.question.merge.switch", defaultValue = "false")
    private boolean subQuestionMergeSwitch;

    @ConfigValue(key = "com.sankuai.mim.pilot.log.model.output.assistant.types", defaultValue = "[10]")
    private List<Integer> logModelOutputAssistants;

    @ConfigValue(key = "com.sankuai.mim.pilot.es.embedding.thread.num", defaultValue = "30")
    private int esEmbeddingThreadNum;

    @ConfigValue(key = "com.sankuai.mim.pilot.review.embedding.appid", defaultValue = "1851616894625992794")
    private String reviewEmbeddingAppId;

    @ConfigValue(key = "com.sankuai.mim.pilot.es.cluster.name", defaultValue = "dzu_eaglenode-es-pilot_default")
    private String esClusterName;

    @ConfigValue(key = "com.sankuai.mim.pilot.ai.phone.call.dialog.post.config", defaultValue = "{}")
    private Map<String, List<String>> aiPhoneCallDialogPostConfig;

    @ConfigValue(key = "com.sankuai.mim.pilot.ai.phone.call.config", defaultValue = "{}")
    private AIPhoneCallConfig aiPhoneCallConfig;

    @ConfigValue(key = "com.sankuai.mim.pilot.ai.phone.scene.config", defaultValue = "{}")
    private Map<String, AIPhoneCallConfig> aiPhoneCallSceneConfig;

    @ConfigValue(key = "com.sankuai.mim.pilot.ai.phone.call.dialog.emotion.recognize.config", defaultValue = "{}")
    private ChatCompletionConfig emotionRecognizeConfig;

    @ConfigValue(key = "com.sankuai.mim.pilot.ai.phone.call.dialog.info.extract.config", defaultValue = "{}")
    private Map<String, ChatCompletionConfig> dialogInfoExtractConfigMap;

    @ConfigValue(key = "com.sankuai.mim.pilot.agent.trade.order.config", defaultValue = "{}")
    private AgentTradeOrderConfig agentTradeOrderConfig;

    @ConfigValue(key = "com.sankuai.mim.pilot.ai.phone.call.bot.id.config", defaultValue = "{}")
    private Map<String, IvrBotConfig> aiPhoneCallBotIdConfig;

    @ConfigValue(key = "com.sankuai.mim.pilot.ai.phone.call.mobile.mock.config", defaultValue = "{}")
    private Map<String, String> aiPhoneCallMobileMockConfig;

    @ConfigValue(key = "com.sankuai.mim.pilot.ai.phone.call.opening.text.config", defaultValue = "{}")
    private Map<String, String> aiPhoneCallOpeningTextConfig;

    @ConfigValue(key = "com.sankuai.mim.pilot.beam.summary.requirements.config", defaultValue = "{}")
    private Map<String, String> beamSummaryRequirementsConfig;

    @ConfigValue(key = "com.sankuai.mim.pilot.need.pay.button", defaultValue = "false")
    private Boolean needPayButton;

    @ConfigValue(key = "com.sankuai.mim.pilot.beam.summary.message.template", defaultValue = "")
    private String beamSummaryMessageTemplate;

    @ConfigValue(key = "com.sankuai.mim.pilot.beam.fixed.content.config", defaultValue = "{}")
    private BeamFixedContentConfig beamFixedContentConfig;

    @ConfigValue(key = "com.sankuai.mim.pilot.beam.test.shopid.replace.config", defaultValue = "{}")
    private Map<String, String> testShopIdReplaceConfig;

    @ConfigValue(key = "com.sankuai.mim.pilot.beam.test.shopid.replace.by.userid.config", defaultValue = "{}")
    private Map<String, String> testShopIdReplaceByUserIdConfig;

    @ConfigValue(key = "com.sankuai.mim.pilot.beam.product.recommend.template", defaultValue = "")
    private String beamProductRecommendTemplate;

    @ConfigValue(key = "com.sankuai.mim.pilot.beam.chat.history.sava.tool.list", defaultValue = "[]")
    private List<String> chatHistorySaveToolList;

    @ConfigValue(key = "com.sankuai.mim.pilot.beam.chat.tuan.search.tool.name", defaultValue = "easy_life_search")
    private String tuanSearchToolName;

    @ConfigValue(key = "com.sankuai.mim.pilot.multi.agent.think.process.text.config", defaultValue = "{}")
    private Map<String, ThinkProcessData> multiAgentThinkProcessDataMap;

    @ConfigValue(key = "com.sankuai.mim.pilot.multi.agent.loading.text.config", defaultValue = "{}")
    private Map<String, String> multiAgentLoadingTextMap;

    @ConfigValue(key = "com.sankuai.mim.pilot.ai.phone.call.redis.lock.config", defaultValue = "{}")
    private Map<String, Integer> aiPhoneCallRedisLockConfig;

    @ConfigValue(key = "com.sankuai.mim.pilot.message.audit.strategy.config", defaultValue = "{}")
    private Map<Integer, List<AuditStrategyConfig>> assistantType2AuditStrategyConfigs;

    @ConfigValue(key = "com.sankuai.mim.pilot.message.audit.fail.feedback", defaultValue = "{\"auditFailMessage\":\"抱歉哦，这个问题我暂时无法回答，我们换个话题聊聊吧～\"}")
    private Map<String, String> auditConfig;

    @ConfigValue(key = "com.sankuai.mim.pilot.beam.user.login.check.switch", defaultValue = "false")
    private boolean beamUserLoginCheckSwitch;

    public String getLoadingText(String key) {
        if (multiAgentLoadingTextMap.containsKey(key)) {
            return multiAgentLoadingTextMap.get(key);
        }
        return "思考中...";
    }

    public ThinkProcessData getThinkProcessData(String key) {
        if (multiAgentThinkProcessDataMap.containsKey(key)) {
            return multiAgentThinkProcessDataMap.get(key);
        }
        return null;
    }

    public String getMatchQuestion(String question) {

        for (Map.Entry<String, Set<String>> entry : keyword2Question.entrySet()) {
            if (entry.getValue().contains(question)) {
                return entry.getKey();
            }
        }
        return question;
    }

    public AIPhoneCallConfig getMatchAiPhoneCallConfig(Integer sceneType) {
        for(Map.Entry<String,  AIPhoneCallConfig>  configEntry: aiPhoneCallSceneConfig.entrySet()){
            if(configEntry.getKey().equals(String.valueOf(sceneType))){
                return configEntry.getValue();
            }
        }
        // 兜底
        return aiPhoneCallConfig;
    }

    public String getTemplateDataConfig(String question, int bizType, int searchType) {
        question = getMatchQuestion(question);
        String key = bizType + "_" + searchType + "_" + question;
        List<TemplateDataCollection> templateDataS = bizTypeQuestion2TemplateData.get(key);
        if (CollectionUtils.isEmpty(templateDataS)) {
            return null;
        }
        Random random = new Random();
        return JsonCodec.encodeWithUTF8(templateDataS.get(random.nextInt(templateDataS.size())));
    }

    public List<AuditStrategyConfig> getAuditStrategyConfigs(Integer assistantType, int direction) {
        if (assistantType2AuditStrategyConfigs.containsKey(assistantType)) {
            List<AuditStrategyConfig> auditStrategyConfigs = assistantType2AuditStrategyConfigs.get(assistantType);
            return auditStrategyConfigs.stream().filter(config -> config.getDirection() == null || config.getDirection() == direction).collect(Collectors.toList());
        }
        return null;
    }
}
