package com.sankuai.dzim.pilot.process.aireservebook.data;

import com.sankuai.dzim.pilot.process.aireservebook.enums.POIIndustryType;
import com.sankuai.dzim.pilot.process.aireservebook.enums.ShopReserveWayType;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.NormPhone;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * @author: wuwen<PERSON>ang
 * @create: 2025-04-23
 * @description: 商户预约上下文
 */
@Data
public class ShopReserveContext {
    private Long mtShopId;
    private Long dpShopId;
    private String shopName;
    private ShopReserveWayType shopReserveWayType;
    private POIIndustryType poiIndustryType;
    private Integer secondBackCategory;
    private String phone;
    private String defaultPic;

    public static ShopReserveContext buildShopReserveContext(DpPoiDTO dpPoiDTO, POIIndustryType poiIndustryType, ShopReserveWayType shopReserveWayType) {
        ShopReserveContext shopReserveContext = new ShopReserveContext();
        if (dpPoiDTO != null) {
            shopReserveContext.setMtShopId(dpPoiDTO.getMtPoiId());
            shopReserveContext.setDpShopId(dpPoiDTO.getShopId());
            shopReserveContext.setShopName(dpPoiDTO.getShopName());
            shopReserveContext.setPhone(getPhone(dpPoiDTO));
        }
        shopReserveContext.setPoiIndustryType(poiIndustryType);
        shopReserveWayType = shopReserveWayType  == null ? ShopReserveWayType.PHONE : shopReserveWayType;
        shopReserveContext.setShopReserveWayType(shopReserveWayType);
        if (poiIndustryType != null) {
            shopReserveContext.setSecondBackCategory(poiIndustryType.getSecondBackCategoryId());
        }
        return shopReserveContext;
    }

    private static String getPhone(DpPoiDTO shopInfo) {
        if (CollectionUtils.isEmpty(shopInfo.getNormPhones())) {
            return null;
        }

        NormPhone normPhone = shopInfo.getNormPhones().get(0);
        if (StringUtils.isNotBlank(normPhone.getAreaCode())) {
            return normPhone.getAreaCode() + normPhone.getEntity();
        }

        if (StringUtils.isNotBlank(normPhone.getEntity())) {
            return normPhone.getEntity();
        }

        return null;
    }
}
