package com.sankuai.dzim.pilot.domain.data;

import lombok.Getter;

public enum ChatMessageRoleEnum {
    USER("user", "代表用户发出的消息"),
    ASSISTANT("assistant", "代表机器人回复的消息"),
    SYSTEM("system", "代表系统发出的消息");

    @Getter
    private String value;

    @Getter
    private final String desc;

    ChatMessageRoleEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

}
