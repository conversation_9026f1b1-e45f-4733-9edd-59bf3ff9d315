package com.sankuai.dzim.pilot.process.data;

import com.sankuai.it.xcontract.easypoi.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class GenerativeSearchAnswerExtendData implements Serializable {
    /**
     * GenerativeSearchTypeEnum
     */
    @Excel(name = "AnswerID")
    private Long answerId;
    @Excel(name = "AttrKeyA")
    private String attrKeyA;
    @Excel(name = "AttrKeyB")
    private String attrKeyB;
    @Excel(name = "AttrKeyC")
    private String attrKeyC;
    @Excel(name = "Value")
    private String value;
    @Excel(name = "Status")
    private Integer status;
}
