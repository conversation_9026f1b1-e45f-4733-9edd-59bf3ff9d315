package com.sankuai.dzim.pilot.gateway.api.medical;

import com.dianping.gateway.enums.APIModeEnum;
import com.dianping.vc.web.api.API;
import com.dianping.vc.web.api.URL;
import com.dianping.vc.web.api.VCAssert;
import com.dianping.vc.web.biz.client.api.MTUserIdAware;
import com.dianping.vc.web.biz.client.api.UserIdAware;
import com.sankuai.dzim.pilot.gateway.api.vo.medical.MedicalCheckupReportFileVO;
import com.sankuai.dzim.pilot.process.MedicalCheckupProcessService;
import com.sankuai.dzim.pilot.utils.CatUtils;
import com.sankuai.dzim.pilot.utils.UserIdUtils;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 查询报告原件
 * @author: zhouyibing
 * @date: 2024/9/12
 */
@Setter
@Slf4j
@Service
@URL(url = "/dzim/pilot/medical/checkup/report/file/query", mode = APIModeEnum.CLIENT, methods = "GET")
public class MedicalCheckupReportFileQueryAPI implements API, UserIdAware, MTUserIdAware {

    private long mTUserId;

    private long userId;

    private int platform;

    private long recordid;

    @Autowired
    private MedicalCheckupProcessService medicalCheckupProcessService;

    @Override
    public MedicalCheckupReportFileVO execute() {
        String imUserId = UserIdUtils.getImUserId(userId, mTUserId, platform);
        VCAssert.isTrue(StringUtils.isNotBlank(imUserId), API.UNLOGIN_CODE, "未登陆");
        //请求埋点
        CatUtils.logMetricReq("MedicalCheckupReportFileQuery", null);
        MedicalCheckupReportFileVO medicalCheckupReportFileVO = medicalCheckupProcessService.queryCheckupReportFile(imUserId, recordid);
        //成功埋点
        CatUtils.logMetricSucc("MedicalCheckupReportFileQuery", null);
        return medicalCheckupReportFileVO;
    }
}
