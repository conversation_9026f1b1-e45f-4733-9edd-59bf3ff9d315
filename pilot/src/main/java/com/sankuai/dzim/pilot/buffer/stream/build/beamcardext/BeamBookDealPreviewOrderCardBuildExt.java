package com.sankuai.dzim.pilot.buffer.stream.build.beamcardext;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.frog.sdk.util.CollectionUtils;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.dzim.pilot.buffer.core.PilotBufferItemDO;
import com.sankuai.dzim.pilot.buffer.enums.StreamEventCardTypeEnum;
import com.sankuai.dzim.pilot.buffer.stream.build.beamcardext.data.BeamCardBuildContext;
import com.sankuai.dzim.pilot.buffer.stream.vo.BeamChoiceVO;
import com.sankuai.dzim.pilot.enums.BeamResourceTypeEnum;
import com.sankuai.dzim.pilot.process.aireservebook.data.BeamBookConfirmAfterBuyCard;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * @author: liujunrui
 * @date: 2025/5/15
 */
@Component
@Slf4j
public class BeamBookDealPreviewOrderCardBuildExt implements BeamCardBuildExt {

    @Override
    public boolean acceptByStreamCardType(String streamCardType) {
        return StreamEventCardTypeEnum.BEAM_BOOK_CONFIRM_AFTER_BUY.getType().equals(streamCardType);
    }

    @Override
    public boolean acceptByBeamResourceType(String resourceType) {
        return BeamResourceTypeEnum.FWLS_BOOK_DEAL_PREVIEW_ORDER.getKey().equals(resourceType);
    }

    @Override
    public List<BeamChoiceVO.DeltaResource> buildResources(BeamCardBuildContext context) {
        List<BeamChoiceVO.DeltaResource> deltaResources = Lists.newArrayList();

        List<PilotBufferItemDO> tagItems = context.getTagItems();
        if (CollectionUtils.isEmpty(tagItems)) {
            return Collections.emptyList();
        }

        PilotBufferItemDO itemDO = tagItems.get(0);
        BeamBookConfirmAfterBuyCard beamBookConfirmAfterBuyCard = JsonCodec.decode(JsonCodec.encodeWithUTF8(itemDO.getExtra()), BeamBookConfirmAfterBuyCard.class);
        BeamChoiceVO.DeltaResource deltaResource = buildDeltaResource(beamBookConfirmAfterBuyCard);
        deltaResources.add(deltaResource);
        return deltaResources;
    }

    @Override
    public String cardDecode(String cardContent) {
        try {
            BeamBookConfirmAfterBuyCard beamBookConfirmAfterBuyCard = JSONObject.parseObject(cardContent, BeamBookConfirmAfterBuyCard.class);
            StringBuilder sb = new StringBuilder();
            sb.append("以下是待预约预订确认信息，需要用户确认：").append("\n");
            sb.append("【店铺名称】").append(beamBookConfirmAfterBuyCard.getShopName()).append("\n");
            sb.append("【联系方式】").append(beamBookConfirmAfterBuyCard.getUserPhone()).append("\n");
            sb.append("【备注】").append(beamBookConfirmAfterBuyCard.getNote()).append("\n");
            sb.append("【标题】").append(beamBookConfirmAfterBuyCard.getTitle()).append("\n");
            sb.append("【副标题】").append(beamBookConfirmAfterBuyCard.getSubTitle()).append("\n");
            return sb.toString();
        } catch (Exception e) {
            log.error("cardDecode BeamBookConfirmAfterBuyCard error, cardContent:{}", cardContent, e);
            return cardContent;
        }
    }

    private BeamChoiceVO.DeltaResource buildDeltaResource(BeamBookConfirmAfterBuyCard beamBookConfirmAfterBuyCard) {
        BeamChoiceVO.DeltaResource deltaResource = new BeamChoiceVO.DeltaResource();
        deltaResource.setResource_index("FWLS-1");
        deltaResource.setResource_type(BeamResourceTypeEnum.FWLS_BOOK_DEAL_PREVIEW_ORDER.getKey());
        BeamChoiceVO.ResourceMetaData metaData = new BeamChoiceVO.ResourceMetaData();
        metaData.setBiz_data(JSON.toJSONString(beamBookConfirmAfterBuyCard));
        deltaResource.setMetadata(metaData);
        return deltaResource;
    }
}
