package com.sankuai.dzim.pilot.domain.data;

import lombok.Data;

@Data
public class BeamFixedContentConfig {

    /**
     * 任务分发失败文案
     */
    private String taskDistributionFailContent = "暂时无法处理，请稍后重试";

    /**
     * 系统异常文案
     */
    private String beamErrorContent = "暂时无法处理，请稍候重试";

    /**
     * ai外呼成功但预约失败，给beam的回调文案
     */
    private String reserveFailCallbackText = "商家该时段已订满";

    /**
     * ai外呼未拨通，给beam的回调文案
     */
    private String notCallThroughCallbackText = "商家电话未拨通";

    /**
     * ai外呼成功但商家不支持预约，给beam的回调文案
     */
    private String notSupportReserveCallbackText = "商家不支持线上电话预约";

    /**
     * ai外呼成功且可预约，但创建预约单异常，给beam的回调文案
     */
    private String createReserveFailCallbackText = "系统异常，出了点小问题";
}
