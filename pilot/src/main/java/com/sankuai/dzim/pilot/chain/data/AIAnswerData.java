package com.sankuai.dzim.pilot.chain.data;

import com.sankuai.dzim.pilot.api.data.AIAnswerTypeEnum;
import com.sankuai.dzim.pilot.chain.enums.AIAnswerDataExtraKeyEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;


@Data
public class AIAnswerData {

    /**
     * ai回复类型
     *
     * @see com.sankuai.dzim.pilot.api.data.AIAnswerTypeEnum
     */
    private int aiAnswerType;

    /**
     * 回复内容，根据回复类型，可能为文本，图片、团购id等
     */
    private String answer;

    /**
     * 推理内容
     */
    private String reason;

    /**
     * 扩展信息
     *
     * @see AIAnswerDataExtraKeyEnum
     */
    private Map<String, String> extraInfo;

    private boolean returnDirect;

    private List<ToolCallResult> toolCallResults;

    public AIAnswerData(int aiAnswerType, String answer, Map<String, String> extraInfo) {
        this.aiAnswerType = aiAnswerType;
        this.answer = answer;
        this.extraInfo = extraInfo;
    }

    public AIAnswerData(int aiAnswerType, String answer) {
        this.aiAnswerType = aiAnswerType;
        this.answer = answer;
    }

    public AIAnswerData(int aiAnswerType, String answer, boolean returnDirect) {
        this.aiAnswerType = aiAnswerType;
        this.answer = answer;
        this.returnDirect = returnDirect;
    }

    public AIAnswerData(int aiAnswerType, String answer, String reason) {
        this.aiAnswerType = aiAnswerType;
        this.answer = answer;
        this.reason = reason;
    }

    public AIAnswerData(String answer) {
        this.aiAnswerType = AIAnswerTypeEnum.TEXT.getType();
        this.answer = answer;
    }

    public AIAnswerData() {

    }

}
