package com.sankuai.dzim.pilot.process.aireservebook.data;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;

/**
 * @author: wu<PERSON><PERSON><PERSON>
 * @create: 2025-04-17
 * @description: 用户预约信息
 */
@Data
@EqualsAndHashCode
public class UserReserveInfo {
    // 已收集预约信息
    private ReserveInfo collectedReserveInfo = new ReserveInfo();
    // 预约要素是否完整
    private boolean isCompleted;
    // 用户ID
    private String imUserId;
    // 商户ID
    private Long shopId;
    // 商户名称
    private String shopName;
    /**
     * 预约类型
     * {@link com.sankuai.dzim.pilot.process.aireservebook.enums.ShopReserveWayType}
     */
    private int shopReserveWayType;
    /**
     * 任务预约类型（预订/预约）
     * {@link com.sankuai.dzim.pilot.process.aireservebook.enums.TaskReserveType}
     */
    private int taskReserveType;
    // 关联团单ID
    private Long dealId;
    // 关联订单ID（先买后约场景）
    private Long orderId;
    // 预约/预订ID
    private Long reserveId;
    // 商家备注
    private String merchantRemark;
    // 额外信息
    private Map<String, Object> extraInfo;
    // skuId，酒吧预约必填
    private Long skuId;
}
