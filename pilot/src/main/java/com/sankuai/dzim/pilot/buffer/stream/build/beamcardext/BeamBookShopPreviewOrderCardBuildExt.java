package com.sankuai.dzim.pilot.buffer.stream.build.beamcardext;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.frog.sdk.util.CollectionUtils;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.dzim.pilot.buffer.core.PilotBufferItemDO;
import com.sankuai.dzim.pilot.buffer.enums.StreamEventCardTypeEnum;
import com.sankuai.dzim.pilot.buffer.stream.build.beamcardext.data.BeamCardBuildContext;
import com.sankuai.dzim.pilot.buffer.stream.vo.BeamChoiceVO;
import com.sankuai.dzim.pilot.enums.BeamResourceTypeEnum;
import com.sankuai.dzim.pilot.process.aireservebook.data.BeamBookConfirmCard;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * @author: liujunrui
 * @date: 2025/5/15
 */
@Component
@Slf4j
public class BeamBookShopPreviewOrderCardBuildExt implements BeamCardBuildExt {

    @Override
    public boolean acceptByStreamCardType(String streamCardType) {
        return StreamEventCardTypeEnum.BEAM_BOOK_CONFIRM.getType().equals(streamCardType);
    }

    @Override
    public boolean acceptByBeamResourceType(String resourceType) {
        return BeamResourceTypeEnum.FWLS_BOOK_SHOP_PREVIEW_ORDER.getKey().equals(resourceType);
    }

    @Override
    public List<BeamChoiceVO.DeltaResource> buildResources(BeamCardBuildContext context) {
        List<BeamChoiceVO.DeltaResource> deltaResources = Lists.newArrayList();

        List<PilotBufferItemDO> tagItems = context.getTagItems();
        if (CollectionUtils.isEmpty(tagItems)) {
            return Collections.emptyList();
        }

        PilotBufferItemDO itemDO = tagItems.get(0);
        BeamBookConfirmCard beamBookConfirmCard = JsonCodec.decode(JsonCodec.encodeWithUTF8(itemDO.getExtra()), BeamBookConfirmCard.class);
        BeamChoiceVO.DeltaResource deltaResource = buildDeltaResource(beamBookConfirmCard);
        deltaResources.add(deltaResource);
        return deltaResources;
    }

    @Override
    public String cardDecode(String cardContent) {
        try {
            BeamBookConfirmCard beamBookConfirmCard = JSONObject.parseObject(cardContent, BeamBookConfirmCard.class);
            StringBuilder sb = new StringBuilder();
            sb.append("【预约预订预览卡片】：").append("\n");
            sb.append("【项目名称】").append(beamBookConfirmCard.getProject()).append("\n");
            sb.append("【联系方式】").append(beamBookConfirmCard.getUserPhone()).append("\n");
            sb.append("【备注】").append(beamBookConfirmCard.getNote()).append("\n");
            sb.append("【项目图片】").append(beamBookConfirmCard.getHeadImg()).append("\n");
            sb.append("【标题】").append(beamBookConfirmCard.getTitle()).append("\n");
            sb.append("【副标题】").append(beamBookConfirmCard.getSubTitle()).append("\n");
            return sb.toString();
        } catch (Exception e) {
            log.error("cardDecode beamBookConfirmCard error, cardContent:{}", cardContent, e);
            return cardContent;
        }
    }

    private BeamChoiceVO.DeltaResource buildDeltaResource(BeamBookConfirmCard beamBookConfirmCard) {
        BeamChoiceVO.DeltaResource deltaResource = new BeamChoiceVO.DeltaResource();
        deltaResource.setResource_index("FWLS-1");
        deltaResource.setResource_type(BeamResourceTypeEnum.FWLS_BOOK_SHOP_PREVIEW_ORDER.getKey());
        BeamChoiceVO.ResourceMetaData metaData = new BeamChoiceVO.ResourceMetaData();
        metaData.setBiz_data(JSON.toJSONString(beamBookConfirmCard));
        deltaResource.setMetadata(metaData);
        return deltaResource;
    }
}
