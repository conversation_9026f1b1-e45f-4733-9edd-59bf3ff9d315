package com.sankuai.dzim.pilot.process.router.impl;

import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.acl.AbTestAclService;
import com.sankuai.dzim.pilot.acl.data.AbTestData;
import com.sankuai.dzim.pilot.api.data.search.generative.QueryAnswerRequest;
import com.sankuai.dzim.pilot.process.data.AbContext;
import com.sankuai.dzim.pilot.process.router.TemplateRouteStrategy;
import com.sankuai.dzim.pilot.process.router.data.RouteResponse;
import com.sankuai.dzim.pilot.utils.DouHuUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/09/29 15:47
 */
@Component
@Order(2)
@Slf4j
public class AbTestRouteStrategy implements TemplateRouteStrategy {
    @Autowired
    private AbTestAclService abTestAclService;

    public static final String ROUTE = "abTestRoute";
    private static final String AI_CARD_PREFIX = "aiCard_";

    @ConfigValue(key = "com.sankuai.mim.pilot.abTestConfig.douhu.expConfig", defaultValue = "{}")
    private Map<String, String> abExpConfig;

    @Override
    public boolean match(String route) {
        return route.contains(ROUTE);
    }
    @Override
    public RouteResponse getRouteResult(QueryAnswerRequest request) {
        AbContext abContext = buildAbContext(request);
        AbTestData abTestData = null;
        try {
            String abExpId = getAbExpId(abContext);
            if (StringUtils.isBlank(abExpId)) {
                return null;
            }
            abTestData = abTestAclService.tryAb(DouHuUtils.buildDouHuRequestByDeviceIdOrUnionId(abContext, abExpId));
        }catch (Exception e){
            LogUtils.logFailLog(log, TagContext.builder().action("tryAb").build(),
                    WarnMessage.build("AbTestRouteStrategy", "获取Ab实验结果异常", ""), request, e);
            return null;
        }
        String controlType = getControlType(abTestData);
        if (StringUtils.isBlank(controlType)) {
            return null;
        }
        RouteResponse routeResponse = new RouteResponse();
        routeResponse.setControlType(controlType);
        routeResponse.setBizType(request.getBizType());
        routeResponse.setAbTestData(abTestData);
        return routeResponse;
    }

    private String getControlType(AbTestData abTestData) {
        if (abTestData == null || StringUtils.isBlank(abTestData.getStrategyKey())) {
            return null;
        }
        String[] split = abTestData.getStrategyKey().split("_");
        if (split.length < 2) {
            return null;
        }
        return split[1];
    }
    private AbContext buildAbContext(QueryAnswerRequest request) {
        AbContext abContext = new AbContext();
        abContext.setUserId(request.getUserId() == null ? null : String.valueOf(request.getUserId()));
        abContext.setDeviceId(request.getDpid() == null ? request.getUuid() : request.getDpid());
        abContext.setPlatform(request.getPlatform() == null ? 2 : request.getPlatform());
        abContext.setBizType(request.getBizType());
        abContext.setUnionId(request.getUnionID());
        abContext.setCityId(String.valueOf(request.getCityId()));
        abContext.setAppVersion(request.getAppVersion());
        abContext.setOs(request.getOs());
        return abContext;
    }

    private String getAbExpId(AbContext context) {
        if (context.getPlatform() == null) {
            LogUtils.logReturnInfo(log, TagContext.builder().action("getAbExpId: fail").build(),
                    new WarnMessage("getAbExpId", "获取实验id失败，平台信息未传入", ""), context, null);
            return null;
        }
        String platform = DouHuUtils.getPlatform(context.getPlatform());
        return MapUtils.getString(abExpConfig, AI_CARD_PREFIX + context.getBizType() + "_" + platform);
    }
}
