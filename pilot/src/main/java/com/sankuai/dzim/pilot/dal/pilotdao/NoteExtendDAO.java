package com.sankuai.dzim.pilot.dal.pilotdao;

import com.sankuai.dzim.pilot.dal.entity.NoteExtendInfoEntity;
import com.sankuai.dzim.pilot.dal.entity.NoteInfoEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface NoteExtendDAO {

    List<NoteExtendInfoEntity> batchFindByNoteIds(@Param("noteIds") List<Long> noteIds);

    int updatePicDesc(@Param("entity") NoteExtendInfoEntity noteExtendInfoEntity);
}
