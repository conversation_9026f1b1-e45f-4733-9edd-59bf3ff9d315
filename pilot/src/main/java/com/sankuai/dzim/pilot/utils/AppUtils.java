package com.sankuai.dzim.pilot.utils;

import com.sankuai.dzim.message.common.enums.AppTypeEnum;
import com.sankuai.dzim.message.common.utils.ImAccountTypeUtils;

/**
 * @author: zhouyibing
 * @date: 2025/4/25
 */
public class AppUtils {

    public static int getAppIdByUserId(String userId) {
        if (ImAccountTypeUtils.isMtUserId(userId)) {
            return AppTypeEnum.MT.getAppId();
        }
        if (ImAccountTypeUtils.isDpUserId(userId)) {
            return AppTypeEnum.DP.getAppId();
        }
        return AppTypeEnum.UNKNOW.getAppId();
    }

}
