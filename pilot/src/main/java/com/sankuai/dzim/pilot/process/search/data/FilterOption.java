package com.sankuai.dzim.pilot.process.search.data;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class FilterOption implements Serializable {

    /**
     * 筛选因素，@see com.sankuai.dzim.pilot.process.search.enums.ProductFilterOptionEnum
     */
    private String filterType;

    /**
     * 筛选值
     */
    private Map<String, Object> values;

}
