package com.sankuai.dzim.pilot.process.localplugin;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.dzim.message.common.utils.ImAccountTypeUtils;
import com.sankuai.dzim.pilot.api.data.AIAnswerTypeEnum;
import com.sankuai.dzim.pilot.buffer.core.PilotBufferItemDO;
import com.sankuai.dzim.pilot.buffer.utils.BufferUtils;
import com.sankuai.dzim.pilot.chain.data.AIAnswerData;
import com.sankuai.dzim.pilot.domain.annotation.LocalPlugin;
import com.sankuai.dzim.pilot.process.data.ProductRetrievalData;
import com.sankuai.dzim.pilot.process.localplugin.data.UserContext;
import com.sankuai.dzim.pilot.process.localplugin.param.ShopProductSearchParam;
import com.sankuai.dzim.pilot.process.search.SearchProcessService;
import com.sankuai.dzim.pilot.utils.PluginContextUtil;
import com.sankuai.dzim.pilot.utils.SupplyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 商户内商品搜索工具
 * 只搜索指定商户内的商品
 * <AUTHOR>
 */
@Component
@Slf4j
public class ShopProductSearchPlugin {

    @Resource
    private SearchProcessService searchProcessService;

    @Resource
    private SupplyUtils supplyUtils;

    @LocalPlugin(name = "ShopProductSearchTool",
            description = "Search for products in a specified shop.", returnDirect = false)
    public AIAnswerData searchShopProducts(ShopProductSearchParam param) {
        log.info("【调用商户内搜索商品的工具，ShopProductSearchPlugin】 param: {}", JsonCodec.encodeWithUTF8(param));
        if (StringUtils.isNotBlank(param.getShowText())) {
            BufferUtils.writeStatusBuffer(PilotBufferItemDO.builder().data(getShowText(param)).build());
        }

        AIAnswerData answerData = new AIAnswerData();
        answerData.setAiAnswerType(AIAnswerTypeEnum.TEXT.getType());
        answerData.setAnswer("没有搜索到相关商品");
        if(!checkAndHandleParam(param)) {
            log.info("【调用商户内搜索商品的工具，ShopProductSearchPlugin】 入参校验失败");
            return answerData;
        }

        List<ProductRetrievalData> products = searchProcessService.searchShopProducts(param);
        String searchAnswer = JsonCodec.encodeWithUTF8(products);
        String markdown = toMarkdown(products, param);
        log.info("【调用商户内搜索商品的工具，ShopProductSearchPlugin】，结果：{} markdown: {}", searchAnswer, markdown);
        answerData.setAiAnswerType(AIAnswerTypeEnum.TEXT.getType());
        answerData.setAnswer(markdown);
        return answerData;
    }

    private String toMarkdown(List<ProductRetrievalData> products, ShopProductSearchParam param) {
        if (CollectionUtils.isEmpty(products)) {
            return "未找到相关商品";
        }
        UserContext userContext = PluginContextUtil.getUserContext(param);
        boolean isMt = ImAccountTypeUtils.isMtUserId(userContext.getImUserId());

        StringBuilder markdown = new StringBuilder();
        // 使用字段注释构建表头
        markdown.append("| 商品名称 | productId | 商品标题 | 价格 | 销量 | 服务类型 | 服务项目 |\n");
        markdown.append("|---------|-----------|---------|-----|-----|---------|--------|\n");

        // 构建表格内容
        for (ProductRetrievalData product : products) {
            markdown.append(String.format("| %s | %d | %s | %s | %d | %s | %s |\n",
                    supplyUtils.escapeMarkdown(product.getTitle()),
                    isMt ? product.getMtProductId(): product.getDpProductId(),
                    supplyUtils.escapeMarkdown(product.getTitle()),
                    supplyUtils.escapeMarkdown(product.getSalePrice()),
                    product.getSalesNum(),
                    supplyUtils.escapeMarkdown(product.getServiceType()),
                    supplyUtils.escapeMarkdown(supplyUtils.buildServiceProject(product))
//                    supplyUtils.escapeMarkdown(supplyUtils.buildIntroduction(product))
                    ));
        }
        return markdown.toString();
    }

    private boolean checkAndHandleParam(ShopProductSearchParam shopMergeSearchParam) {
        if(shopMergeSearchParam == null || shopMergeSearchParam.getShopId() == null || shopMergeSearchParam.getShopId() <= 0){
            return false;
        }
        if (StringUtils.isEmpty(shopMergeSearchParam.getProductSemanticCondition())){
            shopMergeSearchParam.setProductSemanticCondition("");
        }
        return true;
    }

    private String getShowText(ShopProductSearchParam param) {
        if(param == null || StringUtils.isEmpty(param.getShowText())){
            return "正在为你检索...";
        }
        return param.getShowText();
    }

}
