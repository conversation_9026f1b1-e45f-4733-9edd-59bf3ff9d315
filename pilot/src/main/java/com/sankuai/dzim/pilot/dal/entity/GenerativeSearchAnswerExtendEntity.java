package com.sankuai.dzim.pilot.dal.entity;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.dzim.pilot.process.data.NoteRecallPoiDetailData;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;


@Data
public class GenerativeSearchAnswerExtendEntity {

    /**
     * 自增主键
     */
    private Long id;

    /**
     * 回答ID,关联answer表主键
     */
    private Long answerId;

    /**
     * 属性keyA
     */
    private String attrKeyA;

    /**
     * 属性keyB
     */
    private String attrKeyB;

    /**
     * 属性keyC
     */
    private String attrKeyC;

    /**
     * 属性值
     */
    private String value;

    /**
     * 是否有效，1-有效，0无效
     */
    private Integer status;

    /**
     * 添加时间
     */
    private Date addTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    private NoteRecallPoiDetailData noteRecallPoiDetailData;

    public void parseValue() {
        if (StringUtils.isBlank(value)) {
            return;
        }
        this.noteRecallPoiDetailData = JsonCodec.decode(value, NoteRecallPoiDetailData.class);
    }
}
