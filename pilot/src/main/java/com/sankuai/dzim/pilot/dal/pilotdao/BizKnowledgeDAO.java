package com.sankuai.dzim.pilot.dal.pilotdao;

import com.sankuai.dzim.pilot.dal.entity.BizKnowledgeEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BizKnowledgeDAO {

    int insert(@Param("entity") BizKnowledgeEntity entity);

    List<BizKnowledgeEntity> batchFindByVectorIds(@Param("vectorIds") List<Long> vectorIds);

    int batchDeleteByVectorIds(@Param("vectorIds") List<Long> vectorIds, @Param("editor") String editor);

    List<BizKnowledgeEntity> batchFindByIds(@Param("ids") List<Long> ids);

}
