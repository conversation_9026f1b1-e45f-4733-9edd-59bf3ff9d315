package com.sankuai.dzim.pilot.process.data;

import lombok.Getter;

/**
 * @author: zhouyibing
 * @date: 2024/6/14
 */
public enum SugSourceTypeEnum {

    NOTE(1, "笔记"),

    TOP_QUERY(2, "频道页top搜索词"),

    MANUAL(3, "产品指定"),

    /**
     * 聚合后的sug，来源是多篇笔记聚合，存在文件里
     */
    NOTE_FILE(4, "笔记文件")
    ;

    @Getter
    private int code;

    @Getter
    private String desc;

    SugSourceTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
