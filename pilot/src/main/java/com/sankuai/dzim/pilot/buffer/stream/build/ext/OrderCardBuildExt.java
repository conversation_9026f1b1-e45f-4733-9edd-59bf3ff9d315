package com.sankuai.dzim.pilot.buffer.stream.build.ext;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.buffer.enums.OrderCardStatusEnum;
import com.sankuai.dzim.pilot.buffer.enums.StreamEventCardTypeEnum;
import com.sankuai.dzim.pilot.buffer.stream.build.ext.data.OrderCardData;
import com.sankuai.dzim.pilot.buffer.stream.vo.StreamEventCardDataVO;
import com.sankuai.dzim.pilot.enums.GroupBuyOrderBizStatusEnum;
import com.sankuai.dzim.pilot.enums.MessageBizRefTypeEnum;
import com.sankuai.dzim.pilot.process.PilotMessageBizRefProcessService;
import com.sankuai.dzim.pilot.process.data.bizref.GroupBuyOrderExtra;
import com.sankuai.dzim.pilot.process.data.bizref.MessageBizRefData;
import com.sankuai.dzim.pilot.scene.data.AssistantSceneContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @author: zhouyibing
 * @date: 2025/4/28
 */
@Slf4j
@Component
public class OrderCardBuildExt implements CardBuildExt  {

    @Autowired
    private PilotMessageBizRefProcessService pilotMessageBizRefProcessService;

    @Override
    public boolean accept(StreamEventCardDataVO streamEventCardDataVO) {
        return streamEventCardDataVO != null && StreamEventCardTypeEnum.ORDER_CARD.getType().equals(streamEventCardDataVO.getType());
    }

    @Override
    public void padding(StreamEventCardDataVO streamEventCardDataVO, AssistantSceneContext context) {
        return;
    }

    @Override
    public String cardDecode(StreamEventCardDataVO streamEventCardDataVO) {
        try {
            Map<String, Object> cardProps = streamEventCardDataVO.getCardProps();
            OrderCardData orderCardData = JsonCodec.decode(JsonCodec.encodeWithUTF8(cardProps), OrderCardData.class);
            if (orderCardData == null || orderCardData.getStatus() == null) {
                return StreamEventCardTypeEnum.ORDER_CARD.getDesc();
            }
            //待支付
            if (OrderCardStatusEnum.WAIT_PAY.getType() == orderCardData.getStatus()) {
                return decodeWaitPay(orderCardData);
            }
            //已取消
            if (OrderCardStatusEnum.CANCELLED.getType() == orderCardData.getStatus()) {
                return decodeCancelled(orderCardData);
            }
            //已支付，待消费
            if (OrderCardStatusEnum.WAIT_CONSUME.getType() == orderCardData.getStatus()) {
                return decodeWaitConsume(orderCardData);
            }

            //已退款、已核销为通知性卡片，不需要翻译，以免造成幻觉
            return StreamEventCardTypeEnum.ORDER_CARD.getDesc();
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("cardDecode").build(),
                    new WarnMessage("OrderCardBuildExt", "团购订单卡片decode异常", ""), streamEventCardDataVO, null, e);
            return StreamEventCardTypeEnum.ORDER_CARD.getDesc();
        }
    }

    private String decodeWaitPay(OrderCardData orderCardData) {
        StringBuilder sb = new StringBuilder();
        sb.append("商品订单 待支付\n");
        sb.append("orderId：").append(orderCardData.getOrderId()).append("\n");
        sb.append("商品名称：").append(orderCardData.getOrderInfo().getTitle()).append("\n");
        sb.append("订单价格：¥").append(orderCardData.getOrderInfo().getPrice()).append("\n");
        return sb.toString();
    }

    private String decodeCancelled(OrderCardData orderCardData) {
        StringBuilder sb = new StringBuilder();
        sb.append("商品订单 取消支付\n");
        sb.append("orderId：").append(orderCardData.getOrderId()).append("\n");
        sb.append("商品名称：").append(orderCardData.getOrderInfo().getTitle()).append("\n");
        sb.append("订单价格：¥").append(orderCardData.getOrderInfo().getPrice()).append("\n");
        return sb.toString();
    }

    private String decodeWaitConsume(OrderCardData orderCardData) {
        StringBuilder sb = new StringBuilder();
        sb.append("商品订单 支付成功\n");
        sb.append("**orderId：").append(orderCardData.getOrderId()).append("**\n");
        sb.append("商品名称：").append(orderCardData.getOrderInfo().getTitle()).append("\n");
        sb.append("订单价格：¥").append(orderCardData.getOrderInfo().getPrice()).append("\n");
        sb.append("后续和订单相关的问题请使用orderId：").append(orderCardData.getOrderId()).append("\n");

        //支付成功的写入门店id和商品id，后续预约流程需要
        Long orderId = orderCardData.getOrderId();
        MessageBizRefData<GroupBuyOrderExtra> messageBizRef = pilotMessageBizRefProcessService.getMessageBizRef(
                MessageBizRefTypeEnum.GROUP_BUY_ORDER.getType(), String.valueOf(orderId),
                GroupBuyOrderBizStatusEnum.PAYMENT_CARD.name(), GroupBuyOrderExtra.class);

        if (messageBizRef != null && messageBizRef.getBizExtra() != null) {
            GroupBuyOrderExtra bizExtra = messageBizRef.getBizExtra();
            if (bizExtra.getShopId() != null) {
                sb.append("shopId：").append(bizExtra.getShopId()).append("\n");
                sb.append("productId：").append(bizExtra.getProductId()).append("\n");
            }
        }

        return sb.toString();
    }
}
