package com.sankuai.dzim.pilot.buffer.stream.vo;

import lombok.Data;

import java.util.List;

@Data
public class BeamChoiceVO {

    private long index;

    private ChoiceDelta delta;


    @Data
    public static class ChoiceDelta {

        private String type;

        private List<DeltaResource> resource_list;

        private String content;
    }

    @Data
    public static class DeltaResource {

        private String resource_index;

        private String resource_type;

        private ResourceMetaData metadata;
    }


    @Data
    public static class ResourceMetaData {
        private String biz_data;

        private String transmit_biz_data;
    }
}
