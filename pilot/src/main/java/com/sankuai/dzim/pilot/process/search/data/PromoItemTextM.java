package com.sankuai.dzim.pilot.process.search.data;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/10/29
 */
@Data
public class PromoItemTextM implements Serializable {

    private String title;

    /**
     * 优惠类型名称
     */
    private String promoName;

    /**
     * 优惠副标题
     */
    private String subTitle;

    /**
     * 优惠icon
     */
    private String icon;

    /**
     * 优惠明细氛围条优惠信息Icon
     */
    private String atmosphereBarIcon;

    /**
     * 优惠明细氛围条优惠信息文案
     */
    private String atmosphereBarText;

    /**
     * 优惠明细氛围条按钮文案
     */
    private String atmosphereBarButtonText;

    /**
     * 额外信息按钮url
     */
    private String atmosphereBarButtonUrl;

    /**
     * 优惠状态文案
     */
    private String promoStatusText;

    /**
     * com.sankuai.dealuser.price.display.api.enums.PromoShowTypeEnum
     */
    private String promoDivideType;

    /**
     * 对应promoDivideType的文案
     */
    private String promoDivideTypeDesc;
}
