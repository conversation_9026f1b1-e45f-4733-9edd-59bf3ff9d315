package com.sankuai.dzim.pilot.gateway.mq.consumer;

import com.dianping.vc.mq.client.api.annotation.Consumer;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mtrace.Tracer;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.process.aicall.AICallDispatchProcessService;
import com.sankuai.dzim.pilot.process.aicall.data.AICallCallbackDelayData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class AICallCallBackDelayConsumer {
    @Resource
    private AICallDispatchProcessService aiCallDispatchProcessService;

    @Consumer(nameSpace = "daozong",
            appKey = "com.sankuai.mim.pilot",
            group = "pilot.ai.call.callback.consumer",
            topic = "pilot.ai.call.callback.delay.topic"
    )
    public ConsumeStatus recvMessage(String mafkaMessage) {
        AICallCallbackDelayData data = JsonCodec.decode(mafkaMessage, AICallCallbackDelayData.class);
        if (data == null) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        boolean isSuccess = aiCallDispatchProcessService.processAICallCallbackTimeout(data);
        if (!isSuccess) {
            LogUtils.logFailLog(log, TagContext.builder().action("AICallCallBackDelayConsumer").build(),
                    WarnMessage.build("AICallCallBackDelayConsumer", "回调超时处理失败", ""),
                    data, false);
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
