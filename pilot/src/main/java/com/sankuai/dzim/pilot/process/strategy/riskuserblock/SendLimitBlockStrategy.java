package com.sankuai.dzim.pilot.process.strategy.riskuserblock;

import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.sankuai.dzim.pilot.dal.entity.pilot.BlockListEntity;
import com.sankuai.dzim.pilot.domain.RiskUserBlockDomainService;
import com.sankuai.dzim.pilot.gateway.enums.BlockTypeEnum;
import com.sankuai.dzim.pilot.gateway.mq.data.BlockStrategyConstant;
import com.sankuai.dzim.pilot.process.UserBlockProcessService;
import com.sankuai.dzim.pilot.process.data.BlockStrategyConfig;
import com.sankuai.dzim.pilot.gateway.mq.data.RiskBlockMessageData;
import com.sankuai.dzim.pilot.process.strategy.RiskUserBlockStrategy;
import com.sankuai.dzim.pilot.utils.LionConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class SendLimitBlockStrategy implements RiskUserBlockStrategy {
    @Resource(name = "redisClient")
    private RedisStoreClient dzimRedisClient;

    @Autowired
    private UserBlockProcessService userBlockProcessService;

    @Autowired
    private LionConfigUtil lionConfigUtil;


    @Override
    public boolean accept(String name) {
        return name.equals(BlockTypeEnum.SENDLIMIT_STRATEGY.getBlockStrategy());
    }

    @Override
    public void execute(BlockStrategyConfig strategyConfig, RiskBlockMessageData riskMsg) {
        String userId = riskMsg.getReq().getImUserId();
        StoreKey storeKey = buildKey(riskMsg.getReq().getAssistantType(), userId);
        userBlockProcessService.blockUser(storeKey, strategyConfig, riskMsg, BlockTypeEnum.SENDLIMIT_STRATEGY);
    }
    @Override
    public StoreKey buildKey(Integer assistantType, String userId) {
        return new StoreKey(BlockStrategyConstant.RISK_USER_BLOCK_KEY, BlockTypeEnum.SENDLIMIT_STRATEGY.getBlockStrategy(), assistantType, userId);
    }
}
