package com.sankuai.dzim.pilot.domain.data;

import lombok.Data;

import java.util.List;

@Data
public class EsVectorInsertResult<T> {

    boolean success;

    List<T> failInsertDataList;

    public static <T> EsVectorInsertResult fail(List<T> failInsertDataList) {
        EsVectorInsertResult result = new EsVectorInsertResult();
        result.setSuccess(true);
        result.setFailInsertDataList(failInsertDataList);
        return result;
    }

    public static EsVectorInsertResult success() {
        EsVectorInsertResult result = new EsVectorInsertResult();
        result.setSuccess(true);
        return result;
    }
}
