package com.sankuai.dzim.pilot.buffer.stream.build.beamcardext;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.frog.sdk.util.CollectionUtils;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dzim.pilot.buffer.core.PilotBufferItemDO;
import com.sankuai.dzim.pilot.buffer.enums.StreamEventCardTypeEnum;
import com.sankuai.dzim.pilot.buffer.stream.build.beamcardext.data.BeamCardBuildContext;
import com.sankuai.dzim.pilot.buffer.stream.vo.BeamChoiceVO;
import com.sankuai.dzim.pilot.enums.BeamResourceTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component
@Slf4j
public class BeamShopProductCardBuildExt implements BeamCardBuildExt {
    @Override
    public boolean acceptByStreamCardType(String streamCardType) {
        return StreamEventCardTypeEnum.BEAM_PRODUCT_CARD.getType().equals(streamCardType);
    }

    @Override
    public boolean acceptByBeamResourceType(String resourceType) {
        return BeamResourceTypeEnum.FWLS_PRE_PURCHASE_DEAL.getKey().equals(resourceType);
    }

    @Override
    public List<BeamChoiceVO.DeltaResource> buildResources(BeamCardBuildContext context) {
        List<BeamChoiceVO.DeltaResource> deltaResources = Lists.newArrayList();

        List<PilotBufferItemDO> tagItems = context.getTagItems();
        if (CollectionUtils.isEmpty(tagItems)) {
            return Collections.emptyList();
        }
        Map<String, Object> extra = tagItems.get(0).getExtra();

        // 使用正则表达式匹配所有卡片
        Pattern pattern = Pattern.compile("<card[^>]*index=([^\\s>]+)[^>]*>([^<]*)</card>");
        Matcher matcher = pattern.matcher(context.getData());
        while (matcher.find()) {
            String fullCardText = matcher.group();
            String indexValue = matcher.group(1);
            String cardContent = matcher.group(2);

            JSONObject cardContentJson = JsonCodec.decode(cardContent, JSONObject.class);
            if (cardContentJson == null) {
                continue;
            }

            String productId = cardContentJson.getString("productId");
            JSONObject productJson = (JSONObject) MapUtils.getObject(extra, productId);
            if (productJson == null) {
                continue;
            }

            BeamChoiceVO.DeltaResource deltaResource = buildDeltaResource(indexValue, productJson);
            deltaResources.add(deltaResource);
        }
        return deltaResources;
    }

    @Override
    public String cardDecode(String cardContent) {
        cardContent = StringEscapeUtils.unescapeJava(cardContent);
        JSONObject jsonObject = JSONObject.parseObject(cardContent);
        String PRODUCT_TEMPLATE = "\n商品Id(productId)：%s\n商品类型(productType)：%s。\n";
        return String.format(PRODUCT_TEMPLATE, jsonObject.getString("productId"), jsonObject.getString("productType"));
    }

    private BeamChoiceVO.DeltaResource buildDeltaResource(String index, JSONObject jsonObject) {
        BeamChoiceVO.DeltaResource deltaResource = new BeamChoiceVO.DeltaResource();
        deltaResource.setResource_index(index);
        deltaResource.setResource_type("FWLS_PRE_PURCHASE_DEAL");
        BeamChoiceVO.ResourceMetaData metaData = new BeamChoiceVO.ResourceMetaData();
        metaData.setBiz_data(JSONObject.toJSONString(jsonObject));
        deltaResource.setMetadata(metaData);
        return deltaResource;
    }
}
