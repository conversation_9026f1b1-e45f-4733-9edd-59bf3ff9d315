package com.sankuai.dzim.pilot.process.aireservebook;

import com.meituan.nibtp.trade.client.combine.response.AgentGeneralBookingOrderDTO;
import com.sankuai.dzim.pilot.gateway.api.assistant.data.SendBeamMessageReq;
import com.sankuai.dzim.pilot.process.aireservebook.data.*;
import com.sankuai.dzim.pilot.process.aireservebook.enums.ShopReserveWayType;
import com.sankuai.dzim.pilot.process.localplugin.param.Param;
import com.sankuai.dzim.pilot.scene.task.data.EnvContext;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.wpt.user.retrieve.thrift.message.UserModel;

import java.util.List;
import java.util.Map;

/**
 * @author: wuwenqiang
 * @create: 2025-04-20
 * @description:
 */
public interface AIReserveBookQueryService {

    /**
     * 查询点评门店信息
     * @param shopId
     * @param platform
     * @return
     */
    DpPoiDTO queryDpPoiDTO(Long shopId, int platform);

    /**
     * 查询门店预订/预约方式
     * @param mtShopId
     * @return 在线预约/电话预约
     */
    ShopReserveWayType queryShopReserveWayType(Long mtShopId);

    /**
     * 批量查询门店预订/预约方式
     * @param mtShopIds
     * @return 在线预约/电话预约
     */
    Map<Long, ShopReserveWayType> queryShopReserveWayTypes(List<Long> mtShopIds);

    /**
     * 查询库存（分场景）
     * 1. 足疗（均查询门店库存）
     * 2. 美发（先约后买查询门店库存）
     * 3. 美发（先买后约查询商品库存）
     * @param shopReserveContext
     * @param collectedReserveInfo
     * @param isAfterOrder
     * @param dealGroupDTO
     * @return
     */
    List<TimeSliceStockInfo> queryAvailableTimeStock(ShopReserveContext shopReserveContext, ReserveInfo collectedReserveInfo, boolean isAfterOrder, DealGroupDTO dealGroupDTO);


    /**
     * beam查询库存（分场景）
     *      *  查询门店库存场景：除商品库存场景
     *      *  查询商品库存场景：美发（先买后约）酒吧
     * @param shopReserveContext
     * @param collectedReserveInfo
     * @param isAfterOrder
     * @param dealGroupDTO
     * @return
     */
    List<TimeSliceStockInfo> beamQueryAvailableTimeStock(ShopReserveContext shopReserveContext, ReserveInfo collectedReserveInfo, boolean isAfterOrder, DealGroupDTO dealGroupDTO);


    /**
     * 查询门店库存
     * @param param
     * @return
     */
    List<TimeSliceStockInfo> queryShopAvailableTimeStock(ShopStockQueryParam param);

    /**
     * 查询商品库存
     * @param param
     * @return
     */
    List<TimeSliceStockInfo> queryProductAvailableTimeStock(ProductStockQueryParam param);

    /**
     * 查询预约/预订订单详情
     * @param envContext
     * @param userModel
     * @param orderId
     * @return
     */
    AgentGeneralBookingOrderDTO queryReserveOrder(EnvContext envContext, UserModel userModel, Long orderId, Integer taskReserveType);

    /**
     * 查询用户在某一门店下的预约信息（缓存）
     * @param userId
     * @param shopId
     * @param platform
     * @return
     */
    UserReserveInfo queryUserReserveInfo(String userId, Long shopId, int platform);

    /**
     * 查询预约/预订订单详情，beam子Agent使用
     * @param sendBeamMessageReq
     * @param orderId
     * @param taskReserveType
     * @return
     */
    AgentGeneralBookingOrderDTO queryBeamReserveOrder(SendBeamMessageReq sendBeamMessageReq, Long orderId, Integer taskReserveType);

    /**
     * 查询用户在某一门店下的预约信息（缓存），beam子Agent使用
     * @param userId
     * @param shopId
     * @param platform
     * @return
     */
    UserReserveInfo queryBeamUserReserveInfoWithOrderCheck(Param param, String userId, Long shopId, int platform);
}
