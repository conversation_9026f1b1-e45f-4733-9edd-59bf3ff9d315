package com.sankuai.dzim.pilot.process.impl.medical.workflow.data;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @author: zhouyibing
 * @date: 2024/8/23
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MedicalCheckupFlowData implements Serializable {

    // 原始文本
    private String originalText;

    // 节点信息，便于观测节点的中间输入和输出
    private List<WorkNodeData> workNodeDataList;

    // 上下文
    private Map<String, Object> context;

    // 是否是体检报告
    private boolean checkupReport;

    // 是否有健康风险
    private boolean healthRiskEmpty;

}
