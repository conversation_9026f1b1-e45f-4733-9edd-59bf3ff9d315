package com.sankuai.dzim.pilot.process.search.data;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class ReserveQueryContext implements Serializable {

    /**
     * 美团门店ID
     */
    private long mtShopId;

    /**
     * 大众点评门店ID
     */
    private long dpShopId;

    /**
     * 门店后台类目ID
     */
    private int shopBackCategoryId;

    /**
     * 用户环境参数
     */
    private UserEnvParam userEnvParam;

    /**
     * 门店下的商品id列表
     */
    private List<Long> productIds;


    /**
     * 平台标识（如美团、点评等）
     */
    private int platform;

    /**
     * 泛商品主题方案id
     */
    private String planId;

    /**
     * 查询商品id的品类分类
     */
    private Long platformCategoryId;

    /**
     * 业务id到平台id的映射
     */
    private Map<Integer, Long> bizProductId2PlatformProductId;

    /**
     * 个性化扩展信息
     */
    private Map<String, Object> extra;

    /**
     * 个性化填充参数
     */
    private Map<String, Object> customPaddingParam;

    /**
     * 个性化召回参数
     */
    private Map<String, Object> customRecallParam;

    /**
     * 筛选项
     */
    private List<FilterOption> filterOptions;

}
