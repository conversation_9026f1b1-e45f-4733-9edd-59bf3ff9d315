package com.sankuai.dzim.pilot.acl.impl;

import com.dianping.poi.mtDto.MtLocationDTO;
import com.dianping.poi.mtRgcService.MtRgcService;
import com.google.common.collect.Lists;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.acl.MtRgcAclService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2025/2/12 14:55
 */
@Slf4j
@Component
public class MtRcgAclServiceImpl implements MtRgcAclService {

    @Resource
    private MtRgcService mtRgcService;

    @Override
    public MtLocationDTO getInfoByLnglat(double lng, double lat) {
        if (lng <= 0 || lat <= 0) {
            return null;
        }
        try {
            MtLocationDTO mtLocationDTO = mtRgcService.getInfoByLnglat(lng, lat);
            return mtLocationDTO;
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("getInfoByLnglat").build(),
                    WarnMessage.build("getInfoByLnglat", "经纬度获取位置信息异常", ""), Lists.newArrayList(lng, lat), e);
        }
        return null;
    }
}
