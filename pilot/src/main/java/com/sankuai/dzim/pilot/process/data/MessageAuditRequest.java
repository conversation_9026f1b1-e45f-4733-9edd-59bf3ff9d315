package com.sankuai.dzim.pilot.process.data;

import com.sankuai.dzim.pilot.api.enums.assistant.MessageSendDirectionEnum;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025/05/19 12:12
 */
@Data
@Builder
public class MessageAuditRequest {
    private long chatGroupId;
    private Integer assistantType;
    private String text;
    private String imUserId;
    private Long messageId;
    /**
     * @see MessageSendDirectionEnum
     */
    private int direction;
}
