package com.sankuai.dzim.pilot.process.aiphonecall.data;

import lombok.Data;

@Data
public class AIPhoneCallConfig {

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 外呼重试开关
     */
    private boolean retryCallSwitch = false;

    /**
     * 外呼重试时间间隔（单位秒）
     */
    private int retryCallDelaySecond = 300;

    /**
     * 外呼重试次数上限
     */
    private int maxRetryCallCount = 1;

    /**
     * 延迟外呼的随机间隔（单位秒）
     */
    private int delayRandomIntervalSecond = 1200;

    /**
     * 外呼回调对账的时间间隔（单位秒）
     */
    private int callbackCheckIntervalSecond = 600;

    /**
     * 情绪卡控开关
     */
    private boolean sentimentCheckSwitch = false;

    /**
     * 负面情绪卡控阈值
     */
    private int negativeSentimentThreshold = 10;

    /**
     * 最大外呼频次
     */
    private int maxCallFrequency = 10;

    /**
     * 外呼手机号mock开关
     */
    private boolean mobileMockSwitch = false;

}
