package com.sankuai.dzim.pilot.chain.eval.data;

import lombok.Data;

import java.util.List;

/**
 * @author: zhouyibing
 * @date: 2025/4/17
 */
@Data
public class EvalTaskRequest {

    /**
     * 评测集执行的配置，后续通过指标自动拿
     */
    private String aiServiceConfig;

    /**
     * 评测前是否执行Agent
     * true，调用大模型获取output
     * false，评测集中已填写大模型output
     */
    private boolean executeBeforeEval = true;

    /**
     * 评测器，默认为LLMEvaluator
     */
    private String evaluator = "LLMEvaluator";

    /**
     * 指标类型，目前用于指定prompt
     */
    private String metric = "LLMCommon";

    /**
     * 执行rpm，默认30
     */
    private int rpm = 30;

    /**
     * 测试集文件路径
     */
    private String filePath;

    /**
     * 评测用例
     */
    private List<EvalCase> cases;

}