package com.sankuai.dzim.pilot.acl.impl;

import com.dianping.cat.Cat;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Maps;
import com.sankuai.athena.inf.AthenaInf;
import com.sankuai.athena.inf.rpc.CallType;
import com.sankuai.athena.inf.rpc.RpcClient;
import com.sankuai.dzim.pilot.acl.DzRenderAclService;
import com.sankuai.dzshoplist.aggregate.dzrender.api.DzRenderService;
import com.sankuai.dzshoplist.aggregate.dzrender.enums.RenderItemTypeEnum;
import com.sankuai.dzshoplist.aggregate.dzrender.request.DzRenderRequest;
import com.sankuai.dzshoplist.aggregate.dzrender.response.DzRenderResult;
import com.sankuai.dzshoplist.aggregate.dzrender.response.ProductRenderItem;
import com.sankuai.dzshoplist.aggregate.dzrender.response.ShopRenderItem;
import com.sankuai.dzshoplist.aggregate.dzrender.response.TechnicianRenderItem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 到综搜推渲染服务
 * <AUTHOR>
 */
@Component
@Slf4j
public class DzRenderAclServiceImpl implements DzRenderAclService {

    @RpcClient(url = "com.sankuai.dzshoplist.aggregate.dzrender.service.DzRenderService", callType = CallType.FUTURE, timeout = 5000)
    private DzRenderService dzRenderService;
    
    @Override
    public CompletableFuture<Map<Long, ShopRenderItem>> queryShopRenderInfo(DzRenderRequest request) {
        log.info("queryShopRenderInfo, request: {}", JsonCodec.encodeWithUTF8(request));
        if (request == null) {
            return CompletableFuture.completedFuture(Maps.newHashMap());
        }
        try {
            return AthenaInf.getRpcCompletableFuture(dzRenderService.queryRenderInfo(request))
                    .thenApply(resp -> {
                        log.info("queryShopRenderInfo, request: {}, response: {}", JsonCodec.encodeWithUTF8(request), JsonCodec.encodeWithUTF8(resp));
                        if (resp == null || !resp.isSuccess()) {
                            return new HashMap<Long, ShopRenderItem>();
                        }
                        DzRenderResult renderResult = resp.getData();
                        log.info("queryShopRenderInfo, request: {}, renderResult: {}", JsonCodec.encodeWithUTF8(request), JsonCodec.encodeWithUTF8(renderResult));
                        if (renderResult == null) {
                            return new HashMap<Long, ShopRenderItem>();
                        }
                        return renderResult.getShopRenderItems().stream()
                                .filter(item -> item != null 
                                        && item.getShopRenderId() != null 
                                        && item.getShopRenderId().getRenderId() != null
                                        && item.getShopRenderId().getRenderId().getItemType() == RenderItemTypeEnum.POI.getCode()
                                        && item.getShopRenderId().getRenderId().getItemId() != 0)
                                .collect(Collectors.toMap(
                                item -> item.getShopRenderId().getRenderId().getItemId(),
                                item -> item, (k1, k2) -> k2));
                    })
                    .exceptionally(e -> {
                        Cat.logError(e);
                        log.error("queryShopRenderInfo failed, request: {}", request, e);
                        return Maps.newHashMap();
                    });
        } catch (Exception e) {
            Cat.logError(e);
            log.error("queryShopRenderInfo failed, request: {}", request, e);
        }
        return CompletableFuture.completedFuture(Maps.newHashMap());
    }


    @Override
    public CompletableFuture<Map<Long, ProductRenderItem>> queryProductRenderInfo(DzRenderRequest request) {
        log.info("queryProductRenderInfo, request: {}", JsonCodec.encodeWithUTF8(request));
        if (request == null) {
            return CompletableFuture.completedFuture(Maps.newHashMap());
        }
        try {
            return AthenaInf.getRpcCompletableFuture(dzRenderService.queryRenderInfo(request))
                    .thenApply(resp -> {
                        if (resp == null || !resp.isSuccess()) {
                            return new HashMap<Long, ProductRenderItem>();
                        }
                        DzRenderResult renderResult = resp.getData();
                        if (renderResult == null) {
                            return new HashMap<Long, ProductRenderItem>();
                        }
                        log.info("queryProductRenderInfo, request: {}, response: {}", JsonCodec.encodeWithUTF8(request), JsonCodec.encodeWithUTF8(renderResult));
                        return renderResult.getProductRenderItems().stream()
                                .filter(item -> item != null
                                        && item.getProductRenderId() != null
                                        && item.getProductRenderId().getRenderId() != null
                                        && item.getProductRenderId().getRenderId().getItemType() == RenderItemTypeEnum.PRODUCT.getCode()
                                        && item.getProductRenderId().getRenderId().getItemId() != 0)
                                .collect(Collectors.toMap(
                                        item -> item.getProductRenderId().getRenderId().getItemId(),
                                        item -> item, (k1, k2) -> k2));
                    })
                    .exceptionally(e -> {
                        Cat.logError(e);
                        log.error("queryProductRenderInfo failed, request: {}", request, e);
                        return Maps.newHashMap();
                    });
        } catch (Exception e) {
            Cat.logError(e);
            log.error("queryProductRenderInfo failed, request: {}", request, e);
        }
        return CompletableFuture.completedFuture(Maps.newHashMap());
    }

    @Override
    public CompletableFuture<Map<Long, TechnicianRenderItem>> queryTechnicianRenderInfo(DzRenderRequest request) {
        if (request == null) {
            return CompletableFuture.completedFuture(Maps.newHashMap());
        }
        try {
            return AthenaInf.getRpcCompletableFuture(dzRenderService.queryRenderInfo(request))
                    .thenApply(resp -> {
                        if (resp == null || !resp.isSuccess()) {
                            return new HashMap<Long, TechnicianRenderItem>();
                        }
                        DzRenderResult renderResult = resp.getData();
                        if (renderResult == null) {
                            return new HashMap<Long, TechnicianRenderItem>();
                        }
                        return renderResult.getTechnicianRenderItems().stream()
                                .filter(item -> item != null
                                        && item.getTechnicianRenderId() != null
                                        && item.getTechnicianRenderId().getRenderId() != null
                                        && item.getTechnicianRenderId().getRenderId().getItemType() == RenderItemTypeEnum.PRODUCT.getCode()
                                        && item.getTechnicianRenderId().getRenderId().getItemId() != 0)
                                .collect(Collectors.toMap(
                                        item -> item.getTechnicianRenderId().getRenderId().getItemId(),
                                        item -> item, (k1, k2) -> k2));
                    })
                    .exceptionally(e -> {
                        Cat.logError(e);
                        log.error("queryProductRenderInfo failed, request: {}", request, e);
                        return Maps.newHashMap();
                    });
        } catch (Exception e) {
            Cat.logError(e);
            log.error("queryProductRenderInfo failed, request: {}", request, e);
        }
        return CompletableFuture.completedFuture(Maps.newHashMap());
    }
}
