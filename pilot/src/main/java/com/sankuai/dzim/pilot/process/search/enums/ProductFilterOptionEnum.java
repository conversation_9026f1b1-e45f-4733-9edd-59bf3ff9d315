package com.sankuai.dzim.pilot.process.search.enums;

public enum ProductFilterOptionEnum {
    DATE("DATE", "对日期进行筛选，值为「Today」 | {指定日期零点long类型时间，支持List多选}"),
    PRODUCT_SALES("PRODUCT_SALES", "对销量进行筛选"),

    ;

    private String code;
    private String desc;

    ProductFilterOptionEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}