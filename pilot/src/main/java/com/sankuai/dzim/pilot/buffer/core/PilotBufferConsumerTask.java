package com.sankuai.dzim.pilot.buffer.core;

import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.client.container.AthenaBeanFactory;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.api.enums.assistant.MetricEventNameEnum;
import com.sankuai.dzim.pilot.buffer.core.cutoff.CutOffExt;
import com.sankuai.dzim.pilot.buffer.core.cutoff.impl.CommonTagCutOffExt;
import com.sankuai.dzim.pilot.buffer.enums.BufferItemTypeEnum;
import com.sankuai.dzim.pilot.buffer.enums.StreamEventCardTypeEnum;
import com.sankuai.dzim.pilot.buffer.enums.StreamEventDataTypeEnum;
import com.sankuai.dzim.pilot.buffer.stream.build.ContentBuilder;
import com.sankuai.dzim.pilot.buffer.stream.vo.BeamChoiceVO;
import com.sankuai.dzim.pilot.buffer.stream.vo.StreamEventCardDataVO;
import com.sankuai.dzim.pilot.buffer.stream.vo.StreamEventDataVO;
import com.sankuai.dzim.pilot.buffer.stream.vo.StreamEventVO;
import com.sankuai.dzim.pilot.scene.data.AssistantSceneContext;
import com.sankuai.dzim.pilot.scene.data.MetricContext;
import com.sankuai.dzim.pilot.utils.AssistantMessageUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.math3.util.Pair;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/7/30 11:51
 */
@Slf4j
public class PilotBufferConsumerTask implements Callable<Boolean> {

    private PilotBuffer buffer;

    private SseEmitter sseEmitter;

    private AssistantSceneContext sceneContext;

    private MetricContext metricContext;

    private List<CutOffExt> cutOffExtList;

    // 当前的截流策略
    private CutOffExt curCutOff = null;

    /**
     * 发送的SSE消息序号
     */
    private int messageIndex = 0;

    private boolean hasMainText;

    public PilotBufferConsumerTask(PilotBuffer buffer, SseEmitter sseEmitter, AssistantSceneContext sceneContext,
                                   List<CutOffExt> cutOffExtList, MetricContext metricContext) {
        this.buffer = buffer;
        this.sseEmitter = sseEmitter;
        this.sceneContext = sceneContext;
        if (CollectionUtils.isEmpty(cutOffExtList)) {
            this.cutOffExtList = Lists.newArrayList(new CommonTagCutOffExt());
        } else {
            this.cutOffExtList = cutOffExtList;
        }
        this.metricContext = metricContext;
    }

    @Override
    public Boolean call() {
        if (buffer == null || sseEmitter == null) {
            LogUtils.logFailLog(log, TagContext.builder().action("PilotBufferConsumerTask").build(),
                    new WarnMessage("InValidParams", "缓冲对象或SSE连接为空", ""), buffer, sseEmitter == null);
            return true;
        }

        int tagBeginOffset = -1;
        List<PilotBufferItemDO> tagItems = Lists.newArrayList();
        int offset = 0;

        while (true) {
            List<PilotBufferItemDO> written = buffer.getWritten();
            if (offset + 1 > written.size()) {
                continue;
            }

            try {
                PilotBufferItemDO itemDO = written.get(offset);
                if (itemDO.getType() == BufferItemTypeEnum.FINISH_WRITE.getType()) {
                    processTagGroup(tagItems, true);
                    metricContext.addEvent(MetricEventNameEnum.MAIN_TEXT_END.getEventCode());
                    break;
                }

                if (itemDO.getType() == BufferItemTypeEnum.MAIN_TEXT.getType() && !hasMainText) {
                    //打点记录第一包
                    metricContext.addEvent(MetricEventNameEnum.MAIN_TEXT_BEGIN.getEventCode());
                    hasMainText = true;
                }

                // 更新截流策略
                updateCutOffStrategy(itemDO.getData());

                // 设置标签开始偏移量， 多个包可能有标签直接重置
                if (hasTag(itemDO)) {
                    tagBeginOffset = offset;
                }
                // 填充标签偏移量后n个写入项
                boolean currentAdd = hasAppendTagList(tagBeginOffset, offset, tagItems, itemDO);
                //实际处理标签队列和单个数据包
                processTags(tagItems, currentAdd, itemDO);
                // 偏移量自增
                offset++;
            } catch (Exception e) {
                LogUtils.logFailLog(log, TagContext.builder().action("PilotBufferConsumerTask").build(),
                        new WarnMessage("consumeException", "消费异常", ""), buffer, e);
                break;
            }
        }
        return true;
    }

    private void updateCutOffStrategy(String data) {
        if (curCutOff != null) {
            return;
        }

        if (StringUtils.isEmpty(data)) {
            return;
        }

        if (!hasMainText) {
            // 仅正文部分支持截断
            return;
        }

        // 按顺序判断
        for (int index = 0; index < data.length(); ++index) {
            char c = data.charAt(index);
            for (CutOffExt cutOffExt : cutOffExtList) {
                if (cutOffExt.accept(c + "")) {
                    curCutOff = cutOffExt;
                }
            }
        }
    }

    private boolean hasAppendTagList(int tagBeginOffset, int offset, List<PilotBufferItemDO> tagItems, PilotBufferItemDO itemDO) {
        int tagOffsetLimit = Lion.getInt("com.sankuai.mim.pilot", "com.sankuai.mim.pilot.buffer.consumer.tag.offset.limit", 20);
        if (tagBeginOffset >= 0 && offset - tagBeginOffset < tagOffsetLimit && curCutOff != null) {
            tagItems.add(itemDO);
            return true;
        }
        return false;
    }

    private void processTags(List<PilotBufferItemDO> tagItems, boolean currentAdd, PilotBufferItemDO itemDO) {
        // 处理已截流的，根据当前包有没有入标签队列决定是全部处理，还是部分处理，剩余继续等待截流
        if (CollectionUtils.isNotEmpty(tagItems)) {
            processTagGroup(tagItems, !currentAdd);
        }
        //当前包没有入标签，说明标签队列已满，直接处理单个写入项
        if (!currentAdd) {
            processSingleItem(itemDO);
        }
    }


    private void processTagGroup(List<PilotBufferItemDO> tagItems, boolean processAll) {
        if (processAll) {
            //已经到截流缓冲最大值或已结束，处理全部
            processTagGroupAll(tagItems, curCutOff);
            curCutOff = null;
            return;
        }
        //部分处理，可能有未闭合的剩余包，等待后续包再截流
        processTagGroupPart(tagItems);
    }

    private void processTagGroupPart(List<PilotBufferItemDO> tagItems) {
        if (CollectionUtils.isEmpty(tagItems)) {
            return;
        }
        //识别当前正文下的标签列表
        List<PilotBufferItemDO> mainTextItems = tagItems.stream().filter(item -> item.getType() ==
                BufferItemTypeEnum.MAIN_TEXT.getType()).collect(Collectors.toList());
        List<PilotBufferItemDO> otherItems = tagItems.stream().filter(item -> item.getType() !=
                BufferItemTypeEnum.MAIN_TEXT.getType()).collect(Collectors.toList());

        //拼接当前截流的正文数据
        StringBuilder sb = new StringBuilder();
        Map<String, Object> extra = Maps.newHashMap();
        for (PilotBufferItemDO itemDO : mainTextItems) {
            extra.putAll(MapUtils.isEmpty(itemDO.getExtra()) ? Maps.newHashMap() : itemDO.getExtra());
            sb.append(itemDO.getData());
        }
        String data = sb.toString();
        List<Pair<String, String>> mainTextTags = curCutOff.recognizeTags(data);

        //所有已闭合标签之后，判断是否还有未闭合的标签
        int tagIndex = 0;
        for (Pair<String, String> tag : mainTextTags) {
            String tagContent = curCutOff.getTagContent(tag);
            tagIndex = Math.max(data.indexOf(tagContent) + tagContent.length(), tagIndex);
        }
        String remainData = data.substring(tagIndex);
        processRemainData(data, remainData, tagIndex, tagItems, otherItems, extra);
    }


    private void processRemainData(String data, String remainData, int tagIndex, List<PilotBufferItemDO> tagItems,
                                   List<PilotBufferItemDO> otherItems, Map<String, Object> extra) {
        // 剩下的内容里是否还有新的截流策略
        CutOffExt oldCutOff = curCutOff;
        curCutOff = null;
        updateCutOffStrategy(remainData);
        if (curCutOff != null) {
            //还有未闭合的标签，发送已闭合的数据，未闭合后添加至标签列表
            tagItems.clear();
            PilotBufferItemDO mainTextItem = new PilotBufferItemDO();
            mainTextItem.setType(BufferItemTypeEnum.MAIN_TEXT.getType());
            mainTextItem.setData(remainData);
            mainTextItem.setExtra(extra);
            tagItems.add(mainTextItem);
            tagItems.addAll(otherItems);

            //发送前面已闭合的部分
            String closed = data.substring(0, tagIndex);
            if (StringUtils.isNotEmpty(closed)) {
                List<PilotBufferItemDO> closedItems = Lists.newArrayList();
                PilotBufferItemDO closedMainText = new PilotBufferItemDO();
                closedMainText.setType(BufferItemTypeEnum.MAIN_TEXT.getType());
                closedMainText.setData(closed);
                closedMainText.setExtra(extra);
                closedItems.add(closedMainText);
                processTagGroupAll(closedItems, oldCutOff);
            }
        } else {
            //没有未闭合的，处理全部
            processTagGroupAll(tagItems, oldCutOff);
        }
    }

    private void processTagGroupAll(List<PilotBufferItemDO> tagItems, CutOffExt cutOffExt) {
        //处理消息卡片
        ContentBuilder contentBuilder = AthenaBeanFactory.getBean(ContentBuilder.class);
        List<PilotBufferItemDO> mainTextItems = tagItems.stream().filter(item -> item.getType() ==
                BufferItemTypeEnum.MAIN_TEXT.getType()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(mainTextItems)) {
            StreamEventDataVO mainText = convertTagGroup(mainTextItems, cutOffExt);
            StreamEventVO mainTextEventVO = contentBuilder.buildStreamEventVO(mainText, sceneContext);
            mainTextEventVO.setIndex(messageIndex++);
            fillBeamMessageIndex(mainTextEventVO, messageIndex);
            sendSseMessage(mainTextEventVO);
        }
        //处理推荐卡片
        List<PilotBufferItemDO> questionItems = tagItems.stream().filter(item -> item.getType() ==
                BufferItemTypeEnum.RECOMMEND_QUESTION.getType()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(questionItems)) {
            StreamEventDataVO questionText = convertTagGroup(questionItems, cutOffExt);
            StreamEventVO questionEventVO = contentBuilder.buildStreamEventVO(questionText, sceneContext);
            questionEventVO.setIndex(messageIndex++);
            sendSseMessage(questionEventVO);
        }
        //清空标签项列表
        tagItems.clear();
    }

    private void fillBeamMessageIndex(StreamEventVO mainTextEventVO, int messageIndex) {
        if (CollectionUtils.isEmpty(mainTextEventVO.getChoices())) {
            return;
        }
        mainTextEventVO.getChoices().get(0).setIndex(messageIndex);
    }

    private void sendSseMessage(StreamEventVO streamEventVO) {
        //写入buffer converted
        List<StreamEventVO> converted = buffer.getConverted();
        converted.add(streamEventVO);

        try {
            if (buffer.isSseClose()) {
                return;
            }

            //写入SSE
            sseEmitter.send(streamEventVO);
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("PilotBufferConsumerTask").build(),
                    new WarnMessage("sendSseMessage", "发送SSE消息异常", ""), streamEventVO, e);
            // sse异常的情况下,做个标识,防止重复打error日志
            buffer.setSseClose(true);
        }
    }

    public StreamEventDataVO convertTagGroup(List<PilotBufferItemDO> tagItems, CutOffExt cutOffExt) {
        StringBuilder sb = new StringBuilder();
        Map<String, Object> extra = Maps.newHashMap();
        for (PilotBufferItemDO itemDO : tagItems) {
            extra.putAll(MapUtils.isEmpty(itemDO.getExtra()) ? Maps.newHashMap() : itemDO.getExtra());
            sb.append(itemDO.getData());
        }
        String data = sb.toString();
        List<Pair<String, String>> tags = cutOffExt == null ? Lists.newArrayList() : cutOffExt.recognizeTags(data);
        List<StreamEventCardDataVO> cardDataVOs = Lists.newArrayList();
        for (Pair<String, String> tag : tags) {
            StreamEventCardDataVO cardDataVO = new StreamEventCardDataVO();
            cardDataVO.setType(tag.getFirst());
            cardDataVO.setKey(tag.getSecond());
            cardDataVO.setCardProps(extra);
            String tagContent = cutOffExt.getTagContent(tag);
            data = replaceTag(tag, tagContent, data, tagItems);

            if (isNeedCard(tag)) {
                cardDataVOs.add(cardDataVO);
            }
        }

        // 构建Beam的Resource
        List<BeamChoiceVO.DeltaResource> resources = Lists.newArrayList();
        for (Pair<String, String> tag : tags) {
            resources = buildBeamResources(resources, tag, tagItems, data);
        }

        StreamEventDataVO streamEventDataVO = new StreamEventDataVO();
        streamEventDataVO.setContent(data);
        streamEventDataVO.setResources(resources);
        streamEventDataVO.setEvent(StreamEventDataTypeEnum.MAIN_TEXT.getType());
        streamEventDataVO.setCardsData(cardDataVOs);
        return streamEventDataVO;
    }

    private List<BeamChoiceVO.DeltaResource> buildBeamResources(List<BeamChoiceVO.DeltaResource> resources, Pair<String, String> tag, List<PilotBufferItemDO> tagItems, String data) {
        ContentBuilder contentBuilder = AthenaBeanFactory.getBean(ContentBuilder.class);
        List<BeamChoiceVO.DeltaResource> resourceBuildResult = contentBuilder.buildBeamResource(tag, tagItems, data);
        if (CollectionUtils.isEmpty(resourceBuildResult)) {
            return Lists.newArrayList();
        }

        resources.addAll(resourceBuildResult);
        // 去重
        return new ArrayList<BeamChoiceVO.DeltaResource>( resources.stream()
                .collect(Collectors.toMap(
                        resource -> new AbstractMap.SimpleEntry<String, String>(resource.getResource_index(), resource.getResource_type()),
                        resource -> resource,
                        (existing, replacement) -> existing
                )).values());
    }

    private boolean isNeedCard(Pair<String, String> tag) {

        if (tag.getFirst().equals("**")) {
            return false;
        }
        if (StreamEventCardTypeEnum.getByType(tag.getFirst()) != null && !StreamEventCardTypeEnum.getByType(tag.getFirst()).isNeedCard()) {
            return false;
        }
        return true;
    }

    private String replaceTag(Pair<String, String> tag, String tagContent, String data, List<PilotBufferItemDO> tagItems) {
        ContentBuilder contentBuilder = AthenaBeanFactory.getBean(ContentBuilder.class);
        String replaceData = contentBuilder.replaceTags(tag, tagContent, data, sceneContext, tagItems);
        // BeamTag提前跳出
        if (StreamEventCardTypeEnum.isBeamCardType(tag.getFirst())) {
            return data.replace(tagContent, replaceData);
        }

        if ("**".equals(tag.getKey())) {
            data = AssistantMessageUtils.processDoubleStar(data);
        }
        if (StringUtils.isNotEmpty(replaceData)) {
            return replaceData;
        }
        if (tag.getFirst().equals(StreamEventCardTypeEnum.PICTURE_CARD.getType())) {
            data = data.replaceAll("\\n+", "");
        }

        boolean isQuestion = buildIsQuestion(tag.getFirst());
        return data.replace(tagContent, isQuestion ? ":::{" + tagContent + "}:::" : ":::}" + tagContent + "{:::");
    }

    private boolean buildIsQuestion(String tagType) {
        StreamEventCardTypeEnum cardTypeEnum = StreamEventCardTypeEnum.getByType(tagType);
        if (cardTypeEnum == null) {
            return false;
        }
        return cardTypeEnum.isOutBubble();
    }

    public List<Pair<String, String>> recognizeTags(String data) {
        List<Integer> indexList = searchAllIndex(data);
        List<Pair<Pair<String, String>, String>> tags = Lists.newArrayList();
        for (Integer index : indexList) {
            String str = data.substring(index);
            Matcher matcher = Pattern.compile("<(.*?)>(.*?)</(.*?)>").matcher(str);
            int matcherStart = 0;
            while (matcher.find(matcherStart)) {
                String type = matcher.group(1);
                String key = matcher.group(2);
                String endType = matcher.group(3);
                Pair<String, String> types = Pair.create(type, endType);
                Pair<Pair<String, String>, String> typesAndKey = Pair.create(types, key);
                tags.add(typesAndKey);
                matcherStart = matcher.end();
            }
        }
        return tags.stream().filter(tag -> tag.getFirst().getFirst().equals(tag.getFirst().getSecond()))
                .map(tag -> Pair.create(tag.getFirst().getFirst(), tag.getSecond())).collect(Collectors.toList());
    }

    private List<Integer> searchAllIndex(String data) {
        List<Integer> indexList = Lists.newArrayList();
        int a = data.indexOf("<");
        while (a != -1) {
            indexList.add(a);
            a = data.indexOf("<", a + 1);
        }
        return indexList;
    }

    private void processSingleItem(PilotBufferItemDO itemDO) {
        if (itemDO == null || StringUtils.isEmpty(itemDO.getData())) {
            return;
        }
        StreamEventDataVO streamEventDataVO = new StreamEventDataVO();
        streamEventDataVO.setEvent(buildStreamEventType(itemDO.getType()));
        streamEventDataVO.setCardsData(buildCardsData(itemDO));
        streamEventDataVO.setContent(getSingleContent(itemDO.getData(), streamEventDataVO.getCardsData()));
        ContentBuilder contentBuilder = AthenaBeanFactory.getBean(ContentBuilder.class);
        StreamEventVO streamEventVO = contentBuilder.buildStreamEventVO(streamEventDataVO, sceneContext);
        streamEventVO.setIndex(messageIndex++);
        fillBeamMessageIndex(streamEventVO, messageIndex);
        sendSseMessage(streamEventVO);
    }

    private String getSingleContent(String data, List<StreamEventCardDataVO> cardsData) {
        if (CollectionUtils.isEmpty(cardsData)) {
            return data;
        }

        // 气泡内外的替换
        for (StreamEventCardDataVO cardDataVO : cardsData) {
            String tagContent = StreamEventCardTypeEnum.buildCardContent(cardDataVO.getType(), cardDataVO.getKey());
            StreamEventCardTypeEnum cardType = StreamEventCardTypeEnum.getByType(cardDataVO.getType());
            if (cardType == null) {
                continue;
            }

            data = data.replace(tagContent, cardType.isOutBubble() ? ":::{" + tagContent + "}:::" : ":::}" + tagContent + "{:::");
        }
        return data;
    }

    private List<StreamEventCardDataVO> buildCardsData(PilotBufferItemDO itemDO) {
        if (itemDO.getExtra() == null) {
            return null;
        }
        CommonTagCutOffExt commonTagCutOffExt = new CommonTagCutOffExt();
        List<Pair<String, String>> recognizeTags = commonTagCutOffExt.recognizeTags(itemDO.getData());
        if (CollectionUtils.isEmpty(recognizeTags)) {
            return null;
        }

        return recognizeTags.stream().map(tag -> {
            StreamEventCardDataVO cardDataVO = new StreamEventCardDataVO();
            cardDataVO.setType(tag.getFirst());
            cardDataVO.setKey(tag.getSecond());
            cardDataVO.setCardProps(itemDO.getExtra());
            return cardDataVO;
        }).collect(Collectors.toList());
    }


    private String buildStreamEventType(int bufferItemType) {
        BufferItemTypeEnum bufferType = BufferItemTypeEnum.getByType(bufferItemType);
        if (bufferType == null) {
            return StreamEventDataTypeEnum.MAIN_TEXT.getType();
        }
        switch (bufferType) {
            case LOADING_STATUS:
                return StreamEventDataTypeEnum.LOADING_STATUS.getType();
            case REASON_TEXT:
                return StreamEventDataTypeEnum.REASON_TEXT.getType();
            case THINK_PROCESS:
                return StreamEventDataTypeEnum.THINK_PROCESS.getType();
            case MAIN_TEXT:
            case RECOMMEND_QUESTION:
                return StreamEventDataTypeEnum.MAIN_TEXT.getType();
            default:
                return null;
        }
    }

    private boolean hasTag(PilotBufferItemDO itemDO) {
        if (curCutOff == null) {
            return false;
        }
        if (!hasMainText) {
            // 仅正文部分支持标签截断
            return false;
        }
        return itemDO.getData().contains(curCutOff.getCutOffCode());
    }
}
