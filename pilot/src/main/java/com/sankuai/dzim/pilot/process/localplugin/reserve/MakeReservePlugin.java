package com.sankuai.dzim.pilot.process.localplugin.reserve;

import com.alibaba.fastjson.JSON;
import com.dianping.dzim.common.enums.ImUserType;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.dp.arts.common.util.CollectionUtils;
import com.meituan.nibtp.trade.client.combine.response.AgentCreateOrderResDTO;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.acl.HaimaAclService;
import com.sankuai.dzim.pilot.acl.ProductAclService;
import com.sankuai.dzim.pilot.acl.UserAclService;
import com.sankuai.dzim.pilot.api.data.AIAnswerTypeEnum;
import com.sankuai.dzim.pilot.api.enums.assistant.PlatformEnum;
import com.sankuai.dzim.pilot.chain.data.AIAnswerData;
import com.sankuai.dzim.pilot.domain.annotation.LocalPlugin;
import com.sankuai.dzim.pilot.process.aibook.AIBookCacheProcessService;
import com.sankuai.dzim.pilot.process.aiphonecall.data.AIPhoneCallTaskCreateRes;
import com.sankuai.dzim.pilot.process.aireservebook.AIReserveBookProcessService;
import com.sankuai.dzim.pilot.process.aireservebook.AIReserveBookQueryService;
import com.sankuai.dzim.pilot.process.aireservebook.data.*;
import com.sankuai.dzim.pilot.process.aireservebook.enums.POIIndustryType;
import com.sankuai.dzim.pilot.process.aireservebook.enums.ReserveStatusType;
import com.sankuai.dzim.pilot.process.aireservebook.enums.ShopReserveWayType;
import com.sankuai.dzim.pilot.process.data.bizref.MessageBizRefData;
import com.sankuai.dzim.pilot.process.localplugin.reserve.param.ReserveConfirmParam;
import com.sankuai.dzim.pilot.scene.data.AssistantSceneContext;
import com.sankuai.dzim.pilot.scene.task.data.EnvContext;
import com.sankuai.dzim.pilot.utils.PluginContextUtil;
import com.sankuai.dzim.pilot.utils.ReserveBookCardUtils;
import com.sankuai.dzim.pilot.utils.ReserveInfoProcessUtils;
import com.sankuai.dzim.pilot.utils.context.RequestContext;
import com.sankuai.dzim.pilot.utils.context.RequestContextConstants;
import com.sankuai.dzim.pilot.utils.data.Response;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.wpt.user.retrieve.thrift.message.UserModel;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static com.sankuai.dzim.pilot.process.aireservebook.data.ReserveBookConstants.BOOK_ASKING_KEY;

/**
 * 发起预约工具
 * <AUTHOR>
 */
@Component
@Slf4j
public class MakeReservePlugin {

    @Autowired
    private AIReserveBookProcessService aiReserveBookProcessService;

    @Autowired
    private AIReserveBookQueryService aiReserveBookQueryService;

    @Autowired
    private AIBookCacheProcessService aiBookCacheProcessService;

    @Autowired
    private HaimaAclService haimaAclService;

    @Autowired
    private ReserveInfoCollectPlugin reserveInfoCollectPlugin;

    @Autowired
    private ProductAclService productAclService;

    @Autowired
    private UserAclService userAclService;

    @ConfigValue(key = "com.sankuai.mim.pilot.reserve.book.agent.feedback.config", defaultValue = "{}")
    private Map<String, String> feedBackConfig;

    @ConfigValue(key = "com.sankuai.mim.pilot.hair.assistant.card.message.config", defaultValue = "{}")
    private Map<String, String> cardMessageConfig;

    private static ThreadPool threadPool = Rhino.newThreadPool("MakeReserveToolPool",
            DefaultThreadPoolProperties.Setter().withCoreSize(50).withMaxSize(100).withMaxQueueSize(100));

    @LocalPlugin(name = "MakeReserveTool", description = "如果用户的最新消息是和发起预约相关指令且预约信息已确认收集完整，由我来处理", returnDirect = true)
    public AIAnswerData makeReserve(ReserveConfirmParam param) {
        log.info("调用插件-MakeReserveTool，入参={}", param);
        try {
            // 1. 前置判断
            // 1.1 门店ID判断
            if (!validateBasicParams(param)) {
                return ReserveBookCardUtils.generateDirectAnswer(feedBackConfig.getOrDefault(ReserveAnswerConstants.POI_NOT_EXIST, ReserveAnswerConstants.FEEDBACK_ANSWER));
            }

            // 2. 获取上下文信息
            AssistantSceneContext assistantSceneContext = RequestContext.getAttribute(RequestContextConstants.ASSISTANT_CONTEXT);
            int platform = getPlatform(assistantSceneContext);

            // 3. 获取门店信息
            DpPoiDTO dpPoiDTO = getDpShopInfo(param, platform);
            if (dpPoiDTO == null) {
                return ReserveBookCardUtils.generateDirectAnswer(feedBackConfig.getOrDefault(ReserveAnswerConstants.POI_NOT_EXIST, ReserveAnswerConstants.FEEDBACK_ANSWER));
            }

            // 4. 获取行业类型
            // 4.1 门店类目信息获取(二级主后台类目)
            POIIndustryType poiIndustryType = getPoiIndustryType(dpPoiDTO);
            if (poiIndustryType == null) {
                // 非预设行业
                return ReserveBookCardUtils.generateDirectAnswer(feedBackConfig.getOrDefault(ReserveAnswerConstants.SHOP_NOT_SUPPORT, ReserveAnswerConstants.FEEDBACK_ANSWER));
            }
            
            // 5. 获取用户预约信息
            UserReserveInfo userReserveInfo = aiReserveBookQueryService.queryUserReserveInfo(assistantSceneContext.getUserId(), param.getShopId(), platform);
            // 检查重复预约
            if (aiReserveBookProcessService.checkUserDuplicateReserve(userReserveInfo)) {
                if (aiReserveBookProcessService.checkTimeValid(userReserveInfo)) {
                    return ReserveBookCardUtils.generateDirectAnswer(feedBackConfig.getOrDefault(ReserveAnswerConstants.USER_DUPLICATE_RESERVE, ReserveAnswerConstants.FEEDBACK_ANSWER));
                }
                // 预约时间早于当前时间，则不认为是重复预约，清空记录
                userReserveInfo = null;
            }

            // 6. 发起异步调用，构建 FutureContext
            fillParam(param, userReserveInfo);
            FutureContext futureContext = buildFutureContext(assistantSceneContext, dpPoiDTO, poiIndustryType, param);

            // 7. 判断要素是否齐全
            List<StandardReserveItem> standardReserveItems = futureContext.getStandardReserveItemsFuture() == null ? null : futureContext.getStandardReserveItemsFuture().join();
            if (!validateReserveItemsCompleted(userReserveInfo, standardReserveItems)) {
                // 要素未收集齐全，应该调用要素收集工具
                return reserveInfoCollectPlugin.reserveElementCollect(param);
            }

            // 8. 获取异步执行结果，构建 ResultContext
            ResultContext resultContext = buildResultContext(futureContext, userReserveInfo, dpPoiDTO, poiIndustryType, standardReserveItems);
            log.info("[MakeReservePlugin] create reserve, shopReserveContext:{}, userReserveInfo:{}", resultContext.getShopReserveContext(), resultContext.getUserReserveInfo());
            // 9. 处理预约
            if (ShopReserveWayType.ONLINE.equals(resultContext.getShopReserveWayType())) {
                // 2.1 在线预约/预订，查询库存 + 发起预约/预订
                return processOnlineReserve(resultContext);
            }
            // 2.2 非在线预约/预订，直接发起AI外呼
            return processPhoneCallReserve(resultContext, platform);
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("MakeReserve").build(),
                    new WarnMessage("MakeReserveTool", "发起预约插件异常", ""), param, e);
            log.error("调用插件-MakeReserveTool，异常, param={}", param, e);
            return ReserveBookCardUtils.generateDirectAnswer(ReserveAnswerConstants.FEEDBACK_ANSWER);
        }
    }

    private FutureContext buildFutureContext(AssistantSceneContext assistantSceneContext, DpPoiDTO dpPoiDTO, 
            POIIndustryType poiIndustryType, ReserveConfirmParam param) {
        FutureContext context = new FutureContext();

        // 标准预约要素
        context.setStandardReserveItemsFuture(CompletableFuture.supplyAsync(
                () -> haimaAclService.queryStandardReserveItems(poiIndustryType.getSecondBackCategoryId()), 
                threadPool.getExecutor()));
        
        // 查询团单信息（若用户预约信息中含有团单ID）
        context.setDealGroupFuture(getDealGroupFuture(param));
        
        // 获取用户信息
        context.setUserModelFuture(CompletableFuture.supplyAsync(
                () -> userAclService.queryUserModel(assistantSceneContext.getUserId()), 
                threadPool.getExecutor()));
        
        // 查询门店预约方式
        context.setShopReserveWayTypeF(CompletableFuture.supplyAsync(
                () -> aiReserveBookQueryService.queryShopReserveWayType(dpPoiDTO.getMtPoiId()), 
                threadPool.getExecutor()));
        
        return context;
    }

    private ResultContext buildResultContext(FutureContext futureContext, UserReserveInfo userReserveInfo, 
            DpPoiDTO dpPoiDTO, POIIndustryType poiIndustryType, List<StandardReserveItem> standardReserveItems) {
        ResultContext context = new ResultContext();
        
        // 获取所有 future 的结果
        context.setStandardReserveItems(standardReserveItems);
        context.setDealGroupDTO(futureContext.getDealGroupFuture() == null ? null : futureContext.getDealGroupFuture().join());
        context.setUserModel(futureContext.getUserModelFuture() == null ? null : futureContext.getUserModelFuture().join());
        context.setShopReserveWayType(futureContext.getShopReserveWayTypeF().join());
        context.setUserReserveInfo(userReserveInfo);
        
        // 构建 ShopReserveContext
        ShopReserveContext shopReserveContext = ShopReserveContext.buildShopReserveContext(dpPoiDTO, poiIndustryType, context.getShopReserveWayType());
        context.setShopReserveContext(shopReserveContext);
        if (context.getShopReserveWayType() != null) {
            userReserveInfo.setShopReserveWayType(context.getShopReserveWayType().getType());
        }
        
        return context;
    }

    private void fillParam(ReserveConfirmParam param, UserReserveInfo userReserveInfo) {
        if (userReserveInfo == null || param == null) {
            return;
        }
        if (param.getProductId() == null || param.getProductId() <= 0) {
            param.setProductId(userReserveInfo.getDealId());
        }
    }

    private AIAnswerData processOnlineReserve(ResultContext resultContext) {
        // 1. 查询库存
        ValidateResult queryTimeStockValidateResult = queryTimeStock(resultContext.getShopReserveContext(), resultContext.getUserReserveInfo(), resultContext.getDealGroupDTO());
        if (!queryTimeStockValidateResult.isPass()) {
            return ReserveBookCardUtils.generateDirectAnswer(queryTimeStockValidateResult.getQuestion());
        }
        // 可用库存时间片
        List<TimeSliceStockInfo> availableTimeSliceStocks = queryTimeStockValidateResult.getExtraInfoItem("availableTimeSliceStocks");
        // 2. 发起在线预约/预订
        EnvContext envContext = PluginContextUtil.getEnvContextFromRequestContext();
        AgentCreateOrderResDTO createOrderRes = aiReserveBookProcessService.createReserve(envContext, resultContext.getUserModel(),
                resultContext.getShopReserveContext(), resultContext.getUserReserveInfo(), null, true, availableTimeSliceStocks);
        // 检查预约结果
        AIAnswerData checkResult = checkReserveResult(createOrderRes);
        if (checkResult != null) {
            LogUtils.logFailLog(log, TagContext.builder().action("MakeReservePlugin").build(),
                    WarnMessage.build("MakeReservePlugin", "发起预约/预订失败", ""),
                    String.format("envContext:%s, userReserveInfo:%s",
                            JSON.toJSONString(envContext), JSON.toJSONString(resultContext.getUserReserveInfo())), JSON.toJSONString(createOrderRes));
            return checkResult;
        }
        
        // 3. 更新用户预约信息(缓存) + 创建任务落库 + 发送卡片(预约中)
        return processReserveSuccess(createOrderRes, resultContext.getUserReserveInfo());
    }
    
    /**
     * 检查预约结果
     * @param createOrderRes 预约结果
     * @return 如果有错误则返回错误响应，否则返回null
     */
    private AIAnswerData checkReserveResult(AgentCreateOrderResDTO createOrderRes) {
        if (createOrderRes == null) {
            return ReserveBookCardUtils.generateDirectAnswer(feedBackConfig.getOrDefault(ReserveAnswerConstants.RESERVE_FAILED_DEFAULT, ReserveAnswerConstants.FEEDBACK_ANSWER));
        }
        if (createOrderRes.isSuccess() && createOrderRes.getMainOrderId() != null && createOrderRes.getMainOrderId() > 0) {
            return null;
        }
        if (StringUtils.isNotBlank(createOrderRes.getMessage())) {
            return ReserveBookCardUtils.generateModelGenerateAnswer(createOrderRes.getMessage());
        }
        return ReserveBookCardUtils.generateDirectAnswer(feedBackConfig.getOrDefault(ReserveAnswerConstants.RESERVE_FAILED_DEFAULT, ReserveAnswerConstants.FEEDBACK_ANSWER));
    }
    
    /**
     * 处理预约成功后的逻辑
     * @param createOrderRes 预约结果
     * @param userReserveInfo 用户预约信息
     * @return 预约成功的响应
     */
    private AIAnswerData processReserveSuccess(AgentCreateOrderResDTO createOrderRes, UserReserveInfo userReserveInfo) {
        // 写入预约/预订ID
        userReserveInfo.setReserveId(createOrderRes.getMainOrderId());
        try {
            AssistantSceneContext assistantSceneContext = RequestContext.getAttribute(RequestContextConstants.ASSISTANT_CONTEXT);
            // 异步更新用户预约信息
            CompletableFuture<Void> updateUserReserveInfoF = CompletableFuture.runAsync(() -> refreshUserReserveInfo(userReserveInfo, assistantSceneContext), threadPool.getExecutor())
                    .exceptionally(e -> {
                        log.error("[AIMakeReservePlugin] refreshUserReserveInfo error", e);
                        return null;
                    });

            // 创建任务落库
            MessageBizRefData<UserReserveInfo> messageBizRefData = aiReserveBookProcessService.insertMessageBizRef(userReserveInfo, ReserveStatusType.RESERVE_ASKING, 0L);

            // 处理任务落库结果
            if (messageBizRefData == null || messageBizRefData.getId() == null || messageBizRefData.getId() <= 0) {
                throw new RuntimeException("insertMessageTaskFail");
            }

            // 将MessageBizRefData写入线程变量
            RequestContext.setAttribute(RequestContextConstants.NEW_CHAT_MESSAGE_BIZ_REF, messageBizRefData);

            // 等待缓存写入成功
            updateUserReserveInfoF.join();
            return new AIAnswerData(AIAnswerTypeEnum.TEXT.getType(), generateCard(messageBizRefData.getId()));
        } catch (Exception e) {
            log.error("[AIMakeReservePlugin] processReserveSuccess error, reserveId={}", createOrderRes.getMainOrderId(), e);
        }
        return new AIAnswerData(AIAnswerTypeEnum.TEXT.getType(), generateCard(null));
    }

    private AIAnswerData processPhoneCallReserve(ResultContext resultContext, int platform) {
        // 1.异步更新用户预约信息(前置设置商户预约类型)
        AssistantSceneContext assistantSceneContext = RequestContext.getAttribute(RequestContextConstants.ASSISTANT_CONTEXT);
        CompletableFuture<Void> updateUserReserveInfoF = CompletableFuture.runAsync(() -> refreshUserReserveInfo(resultContext.getUserReserveInfo(), assistantSceneContext), threadPool.getExecutor())
                .exceptionally(e -> {
                    log.error("[AIMakeReservePlugin] refreshUserReserveInfo error", e);
                    return null;
                });
        // 2. 创建任务落库
        MessageBizRefData<UserReserveInfo> messageBizRefData = aiReserveBookProcessService.insertMessageBizRef(
                resultContext.getUserReserveInfo(), ReserveStatusType.RESERVE_ASKING, 0L);
        if (messageBizRefData == null || messageBizRefData.getId() == null || messageBizRefData.getId() <= 0) {
            return ReserveBookCardUtils.generateDirectAnswer(feedBackConfig.getOrDefault(ReserveAnswerConstants.RESERVE_FAILED_DEFAULT, ReserveAnswerConstants.FEEDBACK_ANSWER));
        }
        // 3. 将MessageBizRefData写入线程变量
        RequestContext.setAttribute(RequestContextConstants.NEW_CHAT_MESSAGE_BIZ_REF, messageBizRefData);
        // 4. 发起AI外呼
        EnvContext envContext = PluginContextUtil.getEnvContextFromRequestContext();
        Response<AIPhoneCallTaskCreateRes> callAIPhoneCreate = aiReserveBookProcessService.createAIPhoneCall(envContext, resultContext.getUserModel(),
                resultContext.getShopReserveContext(), resultContext.getUserReserveInfo(), messageBizRefData.getId(), platform);
        if (callAIPhoneCreate == null || !callAIPhoneCreate.isSuccess()) {
            return ReserveBookCardUtils.generateDirectAnswer(feedBackConfig.getOrDefault(ReserveAnswerConstants.AI_PHONE_RESERVE_CALL_FAILED, ReserveAnswerConstants.FEEDBACK_ANSWER));
        }
        // 5. 等待缓存写入成功
        try {
            updateUserReserveInfoF.join();
        } catch (Exception e) {
            log.error("[AIMakeReservePlugin] updateUserReserveInfoF error, resultContext={}", JSON.toJSONString(resultContext), e);
            return new AIAnswerData(AIAnswerTypeEnum.TEXT.getType(), generateCard(null));
        }
        log.info("调用插件-MakeReserveTool，结果={}", messageBizRefData.getId());
        return new AIAnswerData(AIAnswerTypeEnum.TEXT.getType(), generateCard(messageBizRefData.getId()));
    }

    private ValidateResult queryTimeStock(ShopReserveContext shopReserveContext, UserReserveInfo userReserveInfo, DealGroupDTO dealGroupDTO) {
        boolean isAfterOrder = userReserveInfo.getOrderId() != null && userReserveInfo.getOrderId() > 0;
        List<TimeSliceStockInfo> timeSliceStockInfos = aiReserveBookQueryService.queryAvailableTimeStock(shopReserveContext, userReserveInfo.getCollectedReserveInfo(), isAfterOrder, dealGroupDTO);
        List<TimeSliceStockInfo> availableTimeSliceStocks = ReserveInfoProcessUtils.checkStockExist(shopReserveContext.getPoiIndustryType(), userReserveInfo.getCollectedReserveInfo(), timeSliceStockInfos);
        if (CollectionUtils.isEmpty(availableTimeSliceStocks)) {
            return ValidateResult.fail(feedBackConfig.getOrDefault(ReserveAnswerConstants.STOCK_NOT_ENOUGH, ReserveAnswerConstants.FEEDBACK_ANSWER));
        }
        return ValidateResult.success("availableTimeSliceStocks", availableTimeSliceStocks);
    }

    private void refreshUserReserveInfo(UserReserveInfo userReserveInfo, AssistantSceneContext assistantSceneContext) {
        String imUserId = userReserveInfo.getImUserId();
        int platform = getPlatform(assistantSceneContext);
        Long shopId = userReserveInfo.getShopId();
        aiBookCacheProcessService.setAgentReserveAndBookInfo(imUserId, shopId, platform, userReserveInfo);
    }

    private int getPlatform(AssistantSceneContext context) {
        return context.getUserId().startsWith(ImUserType.MT.getPrefix()) ? PlatformEnum.MT.getType() : PlatformEnum.DP.getType();
    }

    private String generateCard(Long taskId) {
        return ReserveBookCardUtils.generateLoadingCard(taskId, cardMessageConfig.get(BOOK_ASKING_KEY));
    }

    private boolean validateBasicParams(ReserveConfirmParam param) {
        return param != null && param.getShopId() != null && param.getShopId() > 0;
    }

    private DpPoiDTO getDpShopInfo(ReserveConfirmParam param, int platform) {
        return aiReserveBookQueryService.queryDpPoiDTO(param.getShopId(), platform);
    }

    private POIIndustryType getPoiIndustryType(DpPoiDTO dpPoiDTO) {
        Integer secondBackCategoryId = aiReserveBookProcessService.extractMainSecondBackCategoryId(dpPoiDTO);
        return POIIndustryType.getBySecondBackCategoryId(secondBackCategoryId);
    }

    private CompletableFuture<DealGroupDTO> getDealGroupFuture(ReserveConfirmParam param) {
        if (param.getProductId() != null && param.getProductId() > 0) {
            return CompletableFuture.supplyAsync(() -> productAclService.getDealBaseInfo(param.getProductId(), true), threadPool.getExecutor());
        }
        return null;
    }

    private boolean validateReserveItemsCompleted(UserReserveInfo userReserveInfo, List<StandardReserveItem> standardReserveItems) {
        if (userReserveInfo == null || userReserveInfo.getCollectedReserveInfo() == null) {
            return false;
        }
        List<StandardReserveItem> lackReserveItems = ReserveInfoProcessUtils.checkLackReserveItems(userReserveInfo.getCollectedReserveInfo(), standardReserveItems);
        return CollectionUtils.isEmpty(lackReserveItems);
    }

    @Data
    private static class FutureContext {
        private CompletableFuture<List<StandardReserveItem>> standardReserveItemsFuture;
        private CompletableFuture<DealGroupDTO> dealGroupFuture;
        private CompletableFuture<UserModel> userModelFuture;
        private CompletableFuture<ShopReserveWayType> shopReserveWayTypeF;
    }

    @Data
    private static class ResultContext {
        private List<StandardReserveItem> standardReserveItems;
        private DealGroupDTO dealGroupDTO;
        private UserModel userModel;
        private ShopReserveWayType shopReserveWayType;
        private UserReserveInfo userReserveInfo;
        private ShopReserveContext shopReserveContext;
    }
}
