package com.sankuai.dzim.pilot.process.search.strategy.recall;

import com.sankuai.dzim.pilot.process.search.data.ReserveQueryContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class ReserveProductRecallFactory {

    @Resource
    private List<ReserveProductRecallHandler> handlers;

    public ReserveProductRecallHandler getHandler(ReserveQueryContext queryContext) {
        for (ReserveProductRecallHandler handler : handlers) {
            if (handler.match(queryContext)) {
                return handler;
            }
        }
        return null;
    }
}
