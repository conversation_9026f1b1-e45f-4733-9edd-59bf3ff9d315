package com.sankuai.dzim.pilot.process.search.data;

import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


public class ProductPromoPriceM {
    /**
     * 优惠类型, 如:玩乐卡, 普通折扣卡, 会员日折扣卡, 优惠, 立减,com.sankuai.dztheme.deal.enums.PromoTypeEnum
     */
    private int promoType;

    /**
     * skuId
     */
    private int skuId;

    /**
     * skuIdL
     */
    private long skuIdL;

    /**
     * 资源ID
     */
    private long resourceId;

    /**
     * 优惠后价格
     */
    private BigDecimal promoPrice;

    /**
     * 优惠的价格标签, 如: 已省80
     */
    private String promoTag;

    /**
     * 短优惠文案
     */
    private String shortPromoTag;

    /**
     * 优惠金额
     */
    private BigDecimal promoAmount;

    /**
     * 优惠文案前缀，比如：已优惠
     */
    private String promoTagPrefix;

    /**
     * 优惠之后价格标签
     */
    private String promoPriceTag;

    /**
     * 划线价
     */
    private String marketPrice;

    /**
     * 计算公式 = 1 - ((basePrice - promoPrice) / basePrice)
     */
    private BigDecimal discount;

    /**
     * 折扣标签
     */
    private String discountTag;

    /**
     * 可用时间
     */
    private String availableTime;

    /**
     * 用户是否持有卡
     */
    private boolean userHasCard;

    /**
     * 总共优惠多少
     */
    private BigDecimal totalPromoPrice;

    /**
     * 总共优惠多少
     */
    private String totalPromoPriceTag;

    /**
     * 优惠细节
     */
    private List<PromoItemM> promoItemList;

    /**
     * 原始优惠细节
     */
    private List<PromoItemM> originalPromoItems;

    /**
     * 券信息列表
     */
    private List<ProductCouponM> coupons;

    /**
     * 开始时间，单位是毫秒
     */
    private long startTime;

    /**
     * 结束时间，单位是毫秒
     */
    private long endTime;

    /**
     * 优惠需满足数量
     */
    private int promoQuantityLimit;

    /**
     * 优惠icon
     */
    private String icon;

    /**
     * 优惠文案类型
     *
     */
    private Integer promoTagType;

    /**
     * 神券扩展展示信息
     */
    private Map<String, String> extendDisplayInfo;

    /**
     * 额外费用（如 ktv服务费）
     * 费用类型：com.sankuai.dealuser.price.display.api.enums.FeeTypeEnum
     */
    private List<FeeItemM> feeItemList;

    /**
     * 是否展示市场价(目前KTV有服务费，可能存在到手价和市场价倒挂的场景)
     */
    private Boolean showMarketPrice;

    private static final List<Integer> MAGIC_MEMBER_ITEM_CODE = Lists.newArrayList(3, 4);

    /**
     * 价格优惠信息集合
     * 对应详情页横幅中的券包
     * key枚举 参考com.sankuai.dealuser.price.display.api.enums.PricePromoInfoTypeEnum
     */
    private Map<Integer, List<PromoItemM>> pricePromoInfoMap;

    public long getResourceId() {
        return resourceId;
    }

    public void setResourceId(long resourceId) {
        this.resourceId = resourceId;
    }

    public long getSkuIdL() {
        return skuIdL;
    }

    public void setSkuIdL(long skuIdL) {
        this.skuIdL = skuIdL;
    }

    public int getSkuId() {
        return skuId;
    }

    public void setSkuId(int skuId) {
        this.skuId = skuId;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public int getPromoQuantityLimit() {
        return promoQuantityLimit;
    }

    public void setPromoQuantityLimit(int promoQuantityLimit) {
        this.promoQuantityLimit = promoQuantityLimit;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

    public String getPromoTagPrefix() {
        return promoTagPrefix;
    }

    public void setPromoTagPrefix(String promoTagPrefix) {
        this.promoTagPrefix = promoTagPrefix;
    }

    public List<ProductCouponM> getCoupons() {
        return coupons;
    }

    public void setCoupons(List<ProductCouponM> coupons) {
        this.coupons = coupons;
    }

    public boolean isUserHasCard() {
        return userHasCard;
    }

    public void setUserHasCard(boolean userHasCard) {
        this.userHasCard = userHasCard;
    }

    public String getDiscountTag() {
        return discountTag;
    }

    public void setDiscountTag(String discountTag) {
        this.discountTag = discountTag;
    }

    public BigDecimal getDiscount() {
        return discount;
    }

    public void setDiscount(BigDecimal discount) {
        this.discount = discount;
    }

    public String getAvailableTime() {
        return availableTime;
    }

    public void setAvailableTime(String availableTime) {
        this.availableTime = availableTime;
    }

    public int getPromoType() {
        return promoType;
    }

    public void setPromoType(int promoType) {
        this.promoType = promoType;
    }

    public BigDecimal getPromoPrice() {
        return promoPrice;
    }

    public void setPromoPrice(BigDecimal promoPrice) {
        this.promoPrice = promoPrice;
    }

    public String getPromoTag() {
        return promoTag;
    }

    public void setPromoTag(String promoTag) {
        this.promoTag = promoTag;
    }

    public String getPromoPriceTag() {
        return promoPriceTag;
    }

    public void setPromoPriceTag(String promoPriceTag) {
        this.promoPriceTag = promoPriceTag;
    }

    public BigDecimal getTotalPromoPrice() {
        return totalPromoPrice;
    }

    public void setTotalPromoPrice(BigDecimal totalPromoPrice) {
        this.totalPromoPrice = totalPromoPrice;
    }

    public String getTotalPromoPriceTag() {
        return totalPromoPriceTag;
    }

    public void setTotalPromoPriceTag(String totalPromoPriceTag) {
        this.totalPromoPriceTag = totalPromoPriceTag;
    }

    public List<PromoItemM> getPromoItemList() {
        return promoItemList;
    }

    public void setPromoItemList(List<PromoItemM> promoItemList) {
        this.promoItemList = promoItemList;
    }

    public List<PromoItemM> getOriginalPromoItems() {
        return originalPromoItems;
    }

    public void setOriginalPromoItems(List<PromoItemM> originalPromoItems) {
        this.originalPromoItems = originalPromoItems;
    }

    public String getMarketPrice() {
        return marketPrice;
    }

    public void setMarketPrice(String marketPrice) {
        this.marketPrice = marketPrice;
    }

    public Integer getPromoTagType() {
        return promoTagType;
    }

    public void setPromoTagType(Integer promoTagType) {
        this.promoTagType = promoTagType;
    }

    public String getShortPromoTag() {
        return shortPromoTag;
    }

    public void setShortPromoTag(String shortPromoTag) {
        this.shortPromoTag = shortPromoTag;
    }

    public BigDecimal getPromoAmount() {
        return promoAmount;
    }

    public void setPromoAmount(BigDecimal promoAmount) {
        this.promoAmount = promoAmount;
    }

    public Map<String, String> getExtendDisplayInfo() {
        return extendDisplayInfo;
    }

    public void setExtendDisplayInfo(Map<String, String> extendDisplayInfo) {
        this.extendDisplayInfo = extendDisplayInfo;
    }

    public List<FeeItemM> getFeeItemList() {
        return feeItemList;
    }

    public void setFeeItemList(List<FeeItemM> feeItemList) {
        this.feeItemList = feeItemList;
    }

    public Map<Integer, List<PromoItemM>> getPricePromoInfoMap() {
        return pricePromoInfoMap;
    }

    public void setPricePromoInfoMap(Map<Integer, List<PromoItemM>> pricePromoInfoMap) {
        this.pricePromoInfoMap = pricePromoInfoMap;
    }

    public Boolean getShowMarketPrice() {
        return showMarketPrice;
    }

    public void setShowMarketPrice(Boolean showMarketPrice) {
        this.showMarketPrice = showMarketPrice;
    }

    public boolean hitMagicMemberPromo() {
        if (CollectionUtils.isEmpty(this.promoItemList)) {
            return Boolean.FALSE;
        }
        return this.promoItemList.stream()
                .anyMatch(this::containsMagicMemberCouponItem);
    }

    private boolean containsMagicMemberCouponItem(PromoItemM promoItemM) {
        List<Integer> explanatoryTagList = promoItemM.getPromotionExplanatoryTags();
        if (CollectionUtils.isEmpty(explanatoryTagList)){
            return Boolean.FALSE;
        }
        explanatoryTagList.retainAll(MAGIC_MEMBER_ITEM_CODE);
        return CollectionUtils.isNotEmpty(explanatoryTagList);
    }

}
