package com.sankuai.dzim.pilot.dal.pilotdao;

import com.sankuai.dzim.pilot.dal.entity.pilot.ConsultantKnowledgeCategoryEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 顾问咨询知识类目DAO
 */
public interface ConsultantKnowledgeCategoryDAO {

    /**
     * 新增知识类目
     *
     * @param entity
     * @return
     */
    int insert(@Param("entity") ConsultantKnowledgeCategoryEntity entity);

    /**
     * 查指定类目的子类目
     *
     * @param parentId 父类目id
     * @param type     父类目类型
     * @return
     */
    List<ConsultantKnowledgeCategoryEntity> findChildren(@Param("parentId") long parentId, @Param("type") int type);

    /**
     * 查指定类目的子类目ID
     *
     * @param parentIds 父类目id列表
     * @param type     父类目类型
     * @return 最多返回200个
     */
    List<Long> batchGetChildrenIds(@Param("parentIds") List<Long> parentIds, @Param("type") int type);

    /**
     * 查指定类目名称的类目
     *
     * @param name         名称
     * @param imMerchantId 所属的im顾问id
     * @return
     */
    List<ConsultantKnowledgeCategoryEntity> findByName(@Param("name") String name, @Param("imMerchantId") String imMerchantId);

    /**
     * 查指定知识库和类目名称的类目
     *
     * @param name         名称
     * @param knowledgeBaseType 知识库类型
     * @return
     */
    List<ConsultantKnowledgeCategoryEntity> findByNameAndType(@Param("name") String name, @Param("knowledgeBaseType") int knowledgeBaseType);

    /**
     * 查指定属性和级别的分类
     *
     * @param type 类目类型
     * @param level 类目层级
     * @param imMerchantId 顾问id
     * @return
     */
    List<ConsultantKnowledgeCategoryEntity> findByTypeAndLevel(@Param("type") int type, @Param("level") int level, @Param("imMerchantId") String imMerchantId);

    /**
     * 查指定知识库、属性和级别的分类
     *
     * @param type 类目类型
     * @param level 类目层级
     * @param knowledgeBaseType 知识库类型
     * @return
     */
    List<ConsultantKnowledgeCategoryEntity> findByKnowledgeBaseType(@Param("type") int type, @Param("level") int level, @Param("knowledgeBaseType") int knowledgeBaseType);

    /**
     * 批量查类目
     *
     * @param ids        类目id列表
     * @return
     */
    List<ConsultantKnowledgeCategoryEntity> batchGet(@Param("ids") List<Long> ids);
}
