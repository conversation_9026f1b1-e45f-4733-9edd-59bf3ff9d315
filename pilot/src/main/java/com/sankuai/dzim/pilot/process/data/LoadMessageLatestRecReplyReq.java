package com.sankuai.dzim.pilot.process.data;

import lombok.Data;

@Data
public class LoadMessageLatestRecReplyReq {

    private int shopAccountId;

    /**
     * im商家账号id，此处为im门店id，如s1234
     */
    private String imMerchantId;

    /**
     * im用户id，此处为im用户id，如im点评用户id，u1234，美团用户id，m1234
     */
    private String imUserId;

    /**
     * 用户最新一条消息的id
     */
    private long userLastMessageId;

    private int clientType;
}
