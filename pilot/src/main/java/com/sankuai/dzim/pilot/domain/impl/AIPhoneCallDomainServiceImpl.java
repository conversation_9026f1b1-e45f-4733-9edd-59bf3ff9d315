package com.sankuai.dzim.pilot.domain.impl;

import com.alibaba.fastjson.TypeReference;

import com.alibaba.fastjson.JSON;
import com.dianping.poi.bizhour.dto.BizForecastDTO;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.mafka.client.bean.MafkaProducer;
import com.meituan.mtrace.Tracer;
import com.sankuai.call.sdk.entity.aicall.AiCallParamBO;
import com.sankuai.call.sdk.entity.aicall.AiCallResponseDTO;
import com.sankuai.call.sdk.entity.common.ResponseDTO;
import com.sankuai.call.sdk.entity.record.QueryRecordDataDTO;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.acl.JupiterCallAclService;
import com.sankuai.dzim.pilot.acl.ShopAclService;
import com.sankuai.dzim.pilot.acl.TokenAccessAclService;
import com.sankuai.dzim.pilot.dal.entity.aiphonecall.*;
import com.sankuai.dzim.pilot.dal.pilotdao.aiphonecall.*;
import com.sankuai.dzim.pilot.domain.AIPhoneCallDomainService;
import com.sankuai.dzim.pilot.domain.ShopDomainService;
import com.sankuai.dzim.pilot.domain.data.RedisKeys;
import com.sankuai.dzim.pilot.enums.*;
import com.sankuai.dzim.pilot.gateway.mq.data.AIPhoneCallDelayData;
import com.sankuai.dzim.pilot.gateway.mq.data.AIPhoneCallRecordData;
import com.sankuai.dzim.pilot.process.aiphonecall.calldialogpost.CallDialogPostProcessor;
import com.sankuai.dzim.pilot.process.aiphonecall.data.AIPhoneCallConstants;
import com.sankuai.dzim.pilot.process.aiphonecall.impl.AIPhoneCallStateManager;
import com.sankuai.dzim.pilot.utils.*;
import lombok.extern.slf4j.Slf4j;
import com.sankuai.dzim.pilot.process.aiphonecall.data.AIPhoneCallConfig;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static org.apache.commons.lang3.time.DateUtils.isSameDay;

@Service
@Slf4j
public class AIPhoneCallDomainServiceImpl implements AIPhoneCallDomainService {

    @Autowired
    private AIPhoneCallTaskDAO aiPhoneCallTaskDAO;

    @Autowired
    private AIPhoneCallDetailDAO aiPhoneCallDetailDAO;

    @Autowired
    private AIPhoneCallStateManager stateManager;

    @Autowired
    private LionConfigUtil lionConfigUtil;

    @Resource
    private MafkaProducer aiPhoneCallDelayProducer;

    @Resource
    private MafkaProducer aiPhoneCallbackDelayProducer;

    @Resource
    private TokenAccessAclService tokenAccessAclService;

    @Resource(name = "tollPhoneRedisClient")
    private RedisStoreClient redisStoreClient;

    @Resource
    private AIPhoneCallSentimentDAO callSentimentDAO;

    @Resource
    private ShopDomainService shopDomainService;

    @Resource
    private AIPhoneCallBlacklistDAO callBlacklistDAO;

    @Resource
    private JupiterCallAclService jupiterCallAclService;

    @Resource
    private ShopAclService shopAclService;

    @Resource
    private AIPhoneCallRecordDAO callRecordDAO;

    @Resource
    private CallDialogPostProcessor callDialogPostProcessor;

    private final String[] NUMBERS = {"零", "幺", "二", "三", "四", "五", "六", "七", "八", "九"};


    /**
     * 拨通状态码
     */
    private List<String> callSuccessCode = Lists.newArrayList("1", "2");

    /**
     * 未接通-可重播 状态码
     */
    private List<String> callFailAndCanRetryCode = Lists.newArrayList("7", "9", "14", "17");

    @Override
    public boolean insertTaskAndDetail(AIPhoneCallTaskEntity callTaskEntity, List<AIPhoneCallDetailEntity> callDetailEntityList) {
        int affectTaskRows = aiPhoneCallTaskDAO.insert(callTaskEntity);
        if (affectTaskRows <= 0) {
            return false;
        }

        callDetailEntityList.stream().forEach(e -> e.setTaskId(callTaskEntity.getId()));
        int affectDetailRows = aiPhoneCallDetailDAO.batchInsert(callDetailEntityList);
        return affectDetailRows > 0;
    }

    @Override
    public void executeTask(long taskId, AIPhoneCallTaskStatusEnum oldState) {
        try {
            // 查询第一个待处理的外呼明细
            List<AIPhoneCallDetailEntity> callDetailEntities = aiPhoneCallDetailDAO.getByTaskId(taskId);
            AIPhoneCallDetailEntity firstPendingCallDetail = CollectionUtils.emptyIfNull(callDetailEntities).stream()
                    .sorted(Comparator.comparingInt(o -> o.getSequenceId()))
                    .filter(o -> o.getStatus() == AIPhoneCallDetailStatusEnum.PENDING.getStatus())
                    .findFirst().orElse(null);

            // 若无可待处理外呼明细，则判断该任务完成。
            // 注：不会存在其他未完成状态的明细，因为task状态流转前已经校验过
            if (firstPendingCallDetail == null) {
                stateManager.transitAICallTaskState(taskId, AIPhoneCallTaskStatusEnum.COMPLETED, "无可外呼目标了");
                return;
            }

            // 执行外呼明细
            stateManager.transitAICallDetailState(firstPendingCallDetail.getId(), AIPhoneCallDetailStatusEnum.PROCESSING, "开始调度外呼明细");
        } catch (Exception e) {
            log.error("executeTask error, taskId = {}, oldState = {}", taskId, oldState.getDesc(), e);
        }
    }

    @Override
    public boolean checkAllDetailDone(long taskId) {
        List<AIPhoneCallDetailEntity> callDetailEntities = aiPhoneCallDetailDAO.getByTaskId(taskId);
        if (CollectionUtils.isEmpty(callDetailEntities)) {
            return true;
        }

        for (AIPhoneCallDetailEntity callDetailEntity: callDetailEntities) {
            if (!AIPhoneCallDetailStatusEnum.isDone(callDetailEntity.getStatus())) {
                return false;
            }
        }
        return true;
    }

    @Override
    public boolean canScheduleNext(long taskId) {
        List<AIPhoneCallDetailEntity> callDetailEntities = aiPhoneCallDetailDAO.getByTaskId(taskId);
        boolean hasDoingDetail = CollectionUtils.emptyIfNull(callDetailEntities).stream()
                .sorted(Comparator.comparingInt(o -> o.getSequenceId()))
                .filter(o -> (o.getStatus() == AIPhoneCallDetailStatusEnum.PROCESSING.getStatus()
                        || o.getStatus() == AIPhoneCallDetailStatusEnum.DELAY.getStatus()
                        || o.getStatus() == AIPhoneCallDetailStatusEnum.RETRY.getStatus()
                        || o.getStatus() == AIPhoneCallDetailStatusEnum.RECALL_DELAY.getStatus()))
                .findFirst().isPresent();

        return !hasDoingDetail;
    }

    @Override
    public boolean initiateCall(AIPhoneCallDetailStatusEnum oldState, long detailId) {
        try {
            AIPhoneCallDetailEntity callDetailEntity = aiPhoneCallDetailDAO.getById(detailId);
            AIPhoneCallTaskEntity callTaskEntity = aiPhoneCallTaskDAO.getByTaskId(callDetailEntity.getTaskId());
            // 发送外呼对账延迟消息
//            boolean isSendCallCheckSuccess = ProducerUtils.sendDelayMessageWithFeedback(aiPhoneCallbackDelayProducer,
//                    buildAIPhoneCallDelayData(callDetailEntity), lionConfigUtil.getAiPhoneCallConfig().getCallbackCheckIntervalSecond() * 1000, "aiPhoneCallbackCheckProducer");
            boolean isSendCallCheckSuccess = ProducerUtils.sendDelayMessageWithFeedback(aiPhoneCallbackDelayProducer,
                    buildAIPhoneCallDelayData(callDetailEntity),
                    lionConfigUtil.getMatchAiPhoneCallConfig(callTaskEntity.getSceneType()).getCallbackCheckIntervalSecond() * 1000,
                    "aiPhoneCallbackCheckProducer");

            if (!isSendCallCheckSuccess) {
                stateManager.transitAICallDetailState(callDetailEntity.getId(), AIPhoneCallDetailStatusEnum.FAILED, "发起外呼失败，外呼对账消息发送失败");
                return false;
            }

            // 创建IVR外呼
            metricJupiterCallInvokeRequest(callTaskEntity.getSceneType(), callTaskEntity.getPlatform(), callTaskEntity.getSource());
            AiCallParamBO aiCallParamBO = buildAiCallParamBO(callDetailEntity, callTaskEntity);
            AiCallResponseDTO<String> aiCallResponseDTO = jupiterCallAclService.invokeAiCall(aiCallParamBO);
            if (aiCallResponseDTO == null || aiCallResponseDTO.getCode() != 0 || StringUtils.isEmpty(aiCallResponseDTO.getData())) {
                metricJupiterCallInvokeResult(callTaskEntity.getSceneType(), callTaskEntity.getPlatform(), callTaskEntity.getSource(), aiCallResponseDTO == null? -1: aiCallResponseDTO.getCode());
                stateManager.transitAICallDetailState(callDetailEntity.getId(), AIPhoneCallDetailStatusEnum.FAILED, "发起外呼失败，创建IVR失败");
                return false;
            }
            metricJupiterCallInvokeResult(callTaskEntity.getSceneType(), callTaskEntity.getPlatform(), callTaskEntity.getSource(), aiCallResponseDTO.getCode());
            aiPhoneCallDetailDAO.updateContactId(callDetailEntity.getId(), aiCallResponseDTO.getData());

            // 更新外呼次数
            long dpShopId = shopDomainService.getDpShopId(callDetailEntity.getShopId(), callDetailEntity.getPlatform());
            updateShopCallCount(dpShopId);
            return true;
        } catch (Exception e) {
            log.error("initiateCall error.", detailId, e);
        }
        return false;
    }

    private void metricJupiterCallInvokeRequest(int sceneType, int platform, int source) {
        Map<String, String> tags = Maps.newHashMap();
        tags.put("sceneType", String.valueOf(sceneType));
        tags.put("source", String.valueOf(source));
        tags.put("platform", String.valueOf(platform));
        CatUtils.logMetricReq("JupiterCallInvoke", tags);
    }

    private void metricJupiterCallInvokeResult(int sceneType, int platform, int source, int resultCode) {
        Map<String, String> tags = Maps.newHashMap();
        tags.put("sceneType", String.valueOf(sceneType));
        tags.put("source", String.valueOf(source));
        tags.put("platform", String.valueOf(platform));
        tags.put("resultCode", String.valueOf(resultCode));
        CatUtils.logMetricResult("JupiterCallInvoke", tags);
    }

    private AiCallParamBO buildAiCallParamBO(AIPhoneCallDetailEntity callDetailEntity, AIPhoneCallTaskEntity callTaskEntity) {
        String plainCallPhone = tokenAccessAclService.getPlainMobile(callDetailEntity.getCallPhone());
//        AIPhoneCallConfig aiPhoneCallConfig = lionConfigUtil.getAiPhoneCallConfig();
        AIPhoneCallConfig aiPhoneCallConfig = lionConfigUtil.getMatchAiPhoneCallConfig(callTaskEntity.getSceneType());
        Map<String, Object> dynamicVariableMap = JSON.parseObject(callDetailEntity.getDynamicVariable(), new TypeReference<Map<String, Object>>(){});
        fillExtraDynamicVariable(dynamicVariableMap, callDetailEntity.getShopId(), callTaskEntity.getSceneType(), callDetailEntity.getPlatform());


        Map<String,Object> bizData= new HashMap<>();
        bizData.put(AIPhoneCallConstants.CALL_TASK_ID, String.valueOf(callDetailEntity.getTaskId()));
        bizData.put(AIPhoneCallConstants.CALL_DETAIL_ID, String.valueOf(callDetailEntity.getId()));
        bizData.put(AIPhoneCallConstants.BUSINESS_KEY, AIPhoneCallConstants.FWLS_MAIN_AGENT_AIPHONE_CALL);
        bizData.put("swimlane", Tracer.getSwimlane());
        bizData.put(AIPhoneCallConstants.CALL_SOURCE, callTaskEntity.getSource());

        AiCallParamBO aiCallParamBO = new AiCallParamBO();
        aiCallParamBO.setTenantId(aiPhoneCallConfig.getTenantId());
        aiCallParamBO.setDeviceNum(plainCallPhone);
        aiCallParamBO.setBotVersion(callDetailEntity.getBotVersion());
        aiCallParamBO.setRoutePoint(callDetailEntity.getRoutePoint());
        aiCallParamBO.setBotId(callDetailEntity.getBotId());
        aiCallParamBO.setBizData(bizData);
        aiCallParamBO.setMediaTxt(dynamicVariableMap);
        // 1: 小美 2：金融催收 3：金融
        aiCallParamBO.setAiEngineType(1);
        // 手机号mock
//        fillMockMobile(aiCallParamBO, callDetailEntity.getShopId());
        fillMockMobile(aiCallParamBO, callTaskEntity.getSceneType(), callDetailEntity.getShopId());
        return aiCallParamBO;
    }

    private void fillExtraDynamicVariable(Map<String, Object> dynamicVariableMap, long shopId, int sceneType, int platform) {
        // 填充当前时间
        dynamicVariableMap.put(AIPhoneCallDynamicParamEnum.CURRENT_TIME.getKey(), DateUtils.covertDateStr(new Date()));
        // 填充自然语言预约日期
        String naturalDateDesc = convertNaturalDateDesc((String) dynamicVariableMap.get(AIPhoneCallDynamicParamEnum.RESERVATION_DATE.getKey()));
        dynamicVariableMap.put(AIPhoneCallDynamicParamEnum.RESERVATION_DATE.getKey(), naturalDateDesc);
        // 填充手机尾号
        String userPhone = MapUtils.getString(dynamicVariableMap, AIPhoneCallDynamicParamEnum.USER_PHONE.getKey()).trim();
        dynamicVariableMap.put(AIPhoneCallDynamicParamEnum.USER_PHONE_TAIL.getKey(), getLast4Digits(userPhone));
        // 填充中文手机号
        dynamicVariableMap.put(AIPhoneCallDynamicParamEnum.USER_PHONE.getKey(), addComma(numberToChinese(userPhone)));
        // 填充开场白
        dynamicVariableMap.put(AIPhoneCallDynamicParamEnum.OPENING.getKey(), buildOpeningText(dynamicVariableMap, shopId, sceneType, platform));
    }

    private String getLast4Digits(String str) {
        if (StringUtils.isEmpty(str)) {
            return "";
        }
        return numberToChinese(StringUtils.right(str, 4));
    }

    /**
     * 给11位字符串按3,3,6位置加逗号
     */
    private String addComma(String str) {
        if (StringUtils.isEmpty(str) || str.length() != 11) {
            return str;
        }

        StringBuilder sb = new StringBuilder();
        sb.append(str.substring(0, 3))
                .append(",")
                .append(str.substring(3, 7))
                .append(",")
                .append(str.substring(7));

        return sb.toString();
    }

    /**
     * 将数字字符串转换为中文
     */
    public String numberToChinese(String numStr) {
        if (StringUtils.isEmpty(numStr)) {
            return "";
        }

        StringBuilder sb = new StringBuilder();
        for (char c : numStr.toCharArray()) {
            if (c >= '0' && c <= '9') {
                sb.append(NUMBERS[c - '0']);
            }
        }

        return sb.toString();
    }

    private String buildOpeningText(Map<String, Object> dynamicVariableMap, long shopId, int sceneType, int platform) {
        long dpShopId = shopDomainService.getDpShopId(shopId, platform);
        List<Integer> backCatIds = shopDomainService.getBackCategoryIdList(dpShopId);
        backCatIds = CollectionUtils.emptyIfNull(backCatIds).stream().sorted(Comparator.reverseOrder()).collect(Collectors.toList());

        String techName=dynamicVariableMap.get(AIPhoneCallDynamicParamEnum.TECHNICIAN_NAME.getKey()).toString();
        String openingText = getOpeningText(sceneType, backCatIds, techName);
        openingText = replacePlaceHolder(openingText, dynamicVariableMap);

        return openingText;
    }

    private String replacePlaceHolder(String template, Map<String, Object> params) {
        if (StringUtils.isEmpty(template) || MapUtils.isEmpty(params)) {
            return template;
        }

        String result = template;
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            String placeholder = "{{" + entry.getKey() + "}}";
            String value = entry.getValue() == null? StringUtils.EMPTY: String.valueOf(entry.getValue());
            result = result.replace(placeholder, value);
        }
        return result;
    }

    private String getOpeningText(int sceneType, List<Integer> catIds,String techName) {
        Map<String, String> openingTextConfig = lionConfigUtil.getAiPhoneCallOpeningTextConfig();

        for (int catId: CollectionUtils.emptyIfNull(catIds)) {

            String openingTextQueryKeyWithCat;
            //无指定技师
            if(StringUtils.isEmpty(techName)){
                openingTextQueryKeyWithCat = String.format("%s_%s", sceneType, catId);
            }
            else {
                openingTextQueryKeyWithCat = String.format("%s_%s_tech", sceneType, catId);
            }
            if (openingTextConfig.containsKey(openingTextQueryKeyWithCat)) {
                return openingTextConfig.getOrDefault(openingTextQueryKeyWithCat, "");
            }
        }

        return openingTextConfig.getOrDefault(String.valueOf(sceneType), "");
    }

    private void fillMockMobile(AiCallParamBO aiCallParamBO, int sceneType, long shopId) {
//        AIPhoneCallConfig aiPhoneCallConfig = lionConfigUtil.getAiPhoneCallConfig();
        AIPhoneCallConfig aiPhoneCallConfig = lionConfigUtil.getMatchAiPhoneCallConfig(sceneType);
        if (!aiPhoneCallConfig.isMobileMockSwitch()) {
            return;
        }

        Map<String, String> mobileMockConfig = lionConfigUtil.getAiPhoneCallMobileMockConfig();
        if (MapUtils.emptyIfNull(mobileMockConfig).containsKey(String.valueOf(shopId))) {
            aiCallParamBO.setDeviceNum(mobileMockConfig.get(String.valueOf(shopId)));
        } else if (MapUtils.emptyIfNull(mobileMockConfig).containsKey("all")) {
            aiCallParamBO.setDeviceNum(mobileMockConfig.get("all"));
        }
    }

    private String convertNaturalDateDesc(String dateStr) {
        if (StringUtils.isEmpty(dateStr)) {
            return StringUtils.EMPTY;
        }

        try {
            Date date = new SimpleDateFormat("yyyy-MM-dd").parse(dateStr);
            Date now = new Date();

            // 判断是否是今天
            if (isSameDay(date, now)) {
                return "今天";
            }

            // 判断是否是明天
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(now);
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            if (isSameDay(date, calendar.getTime())) {
                return "明天";
            }

            // 其他日期返回原始字符串
            return formatDateToChinese(date);
        } catch (Exception e) {
            log.error("convertNaturalDateDesc error, dateStr = {}", dateStr, e);
            return StringUtils.EMPTY;
        }
    }

    /**
     * 将日期转换为"x号周几"格式
     */
    private String formatDateToChinese(Date date) {
        if (date == null) {
            return "";
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        // 获取日期
        int dayOfMonth = calendar.get(Calendar.DAY_OF_MONTH);
        // 获取星期
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);

        // 星期映射
        String[] weekDays = {"日", "一", "二", "三", "四", "五", "六"};

        return String.format("%d号周%s", dayOfMonth, weekDays[dayOfWeek - 1]);
    }

    public void updateShopCallCount(long dpShopId) {
        if (dpShopId <= 0) {
            return;
        }

        try {
            // 计算过期时间（秒）
            int expireSeconds = DateUtils.secondsToDayEndWithRandom();
            StoreKey callFrequencyKey = RedisKeys.getAIPhoneCallFrequencyCntKey(dpShopId);
            redisStoreClient.incrBy(callFrequencyKey, 1, expireSeconds);
        } catch (Exception e) {
            log.error("updateShopCallCount error, dpShopId: {}", dpShopId, e);
        }
    }

    /**
     * 判断当前时间商家是否营业中
     * @param dpShopId
     * @return
     */
    private boolean isAllowCallTime(long dpShopId) {
        Date now = new Date();
        BizForecastDTO bizForecast = shopAclService.getBizForecast(dpShopId, DateUtils.covertDateStr(now));
        return isShopCurrentOpen(bizForecast, now);
    }

    private boolean isShopCurrentOpen(BizForecastDTO bizForecast, Date now) {
        if (bizForecast == null || org.apache.commons.lang3.StringUtils.isEmpty(bizForecast.getToday())) {
            return false;
        }

        int halfHourIndex = getHalfHourIndex(now);
        String today = bizForecast.getToday();
        if (today.charAt(halfHourIndex) == '1') {
            return true;
        }
        return false;
    }

    private int getHalfHourIndex(Date date) {
        int hour = DateUtils.getHourOfDay(date);
        int minute = DateUtils.getMinuteOfHour(date);
        int halfHourIndex;
        if (minute < 30) {
            halfHourIndex = hour * 2;
        } else {
            halfHourIndex = hour * 2 + 1;
        }
        return halfHourIndex;
    }

    @Override
    public boolean delayCall(long detailId) {
        AIPhoneCallDetailEntity callDetailEntity = aiPhoneCallDetailDAO.getById(detailId);
        AIPhoneCallTaskEntity callTaskEntity = aiPhoneCallTaskDAO.getByTaskId(callDetailEntity.getTaskId());

        Date targetDate = getTargetCallTime(callDetailEntity);
        if (targetDate == null) {
            stateManager.transitAICallDetailState(detailId, AIPhoneCallDetailStatusEnum.FAILED, "加入延迟队列失败，无符合外呼时间");
            return false;
        }

//        long delaySeconds = TimeUtil.getDifSecond(targetDate) + new Random().nextInt(lionConfigUtil.getAiPhoneCallConfig().getDelayRandomIntervalSecond());
        long delaySeconds = TimeUtil.getDifSecond(targetDate) + new Random().nextInt(
                lionConfigUtil.getMatchAiPhoneCallConfig(callTaskEntity.getSceneType()).getDelayRandomIntervalSecond());

        boolean isDelaySuccess = ProducerUtils.sendDelayMessageWithFeedback(aiPhoneCallDelayProducer, buildAIPhoneCallDelayData(callDetailEntity),
                delaySeconds * 1000, "aiPhoneCallDelayProducer-delayCall");
        if (!isDelaySuccess) {
            stateManager.transitAICallDetailState(detailId, AIPhoneCallDetailStatusEnum.FAILED, "加入延迟队列失败，延迟消息发送失败");
        } else {
            callDetailEntity.setExpectedCallTime(DateUtils.addSeconds(new Date(), delaySeconds));
            aiPhoneCallDetailDAO.updateExpectedCallTimeInt(callDetailEntity);
            stateManager.transitAICallTaskState(callTaskEntity.getId(), AIPhoneCallTaskStatusEnum.PAUSED, "加入延迟队列成功");
        }
        return isDelaySuccess;
    }

    @Override
    public boolean callSuccessPostProcess(long detailId, long taskId) {
        callDialogPostProcessor.handleCallDialogPost(detailId);

        if (checkTaskIsComplete(taskId)) {
            stateManager.transitAICallTaskState(taskId, AIPhoneCallTaskStatusEnum.COMPLETED, "外呼明细拨打成功");
            return true;
        }
        return true;
    }

    @Override
    public boolean hasSuccessCallDetail(long taskId) {
        List<AIPhoneCallDetailEntity> callDetailEntities = aiPhoneCallDetailDAO.getByTaskId(taskId);
        return CollectionUtils.emptyIfNull(callDetailEntities).stream()
                .filter(e -> e.getStatus() == AIPhoneCallDetailStatusEnum.SUCCESS.getStatus())
                .findAny().isPresent();
    }

    @Override
    public boolean checkTaskIsComplete(long taskId) {
        AIPhoneCallTaskEntity callTaskEntity = aiPhoneCallTaskDAO.getByTaskId(taskId);
        AIPhoneCallSceneTypeConfig sceneTypeConfig = AIPhoneCallSceneTypeConfig.getBySceneType(callTaskEntity.getSceneType());

        // 任一外呼明细成功，则任务就算完成
        if (sceneTypeConfig.getMode() == AIPhoneCallModeEnum.SINGLE.getType()) {
            return hasSuccessCallDetail(taskId);
        }
        return false;
    }

    private AIPhoneCallDelayData buildAIPhoneCallDelayData(AIPhoneCallDetailEntity callDetailEntity) {
        if (callDetailEntity == null) {
            return null;
        }

        AIPhoneCallDelayData callDelayData = new AIPhoneCallDelayData();
        callDelayData.setCallDetailId(callDetailEntity.getId());
        callDelayData.setCallTaskId(callDetailEntity.getTaskId());
        return callDelayData;
    }

    private Date getTargetCallTime(AIPhoneCallDetailEntity callDetailEntity) {
        String ddlDate = callDetailEntity.getDdlDate();
        if (StringUtils.isEmpty(ddlDate)) {
            return null;
        }

        long dpShopId = shopDomainService.getDpShopId(callDetailEntity.getShopId(), callDetailEntity.getPlatform());
        Date now = new Date();
        List<String> dateStrList = DateUtils.getBetweenDates(DateUtils.formatSimpleDate(now), callDetailEntity.getDdlDate());
        for (String dateStr : CollectionUtils.emptyIfNull(dateStrList)) {
            BizForecastDTO bizForecast = shopAclService.getBizForecast(dpShopId, DateUtils.parseDateKey2DateStr(dateStr));
            if (bizForecast == null || bizForecast.getToday() == null) {
                return null;
            }
            String today = bizForecast.getToday();

            // 如果日期是今天，特殊处理
            if (dateStr.equals(DateUtils.formatSimpleDate(now))) {
                int halfHourIndex = getHalfHourIndex(now);
                for (int idx = halfHourIndex; idx < today.length(); idx++) {
                    if (today.charAt(idx) == '1') {
                        return calculateDate(dateStr, idx);
                    }
                }
            } else {
                for (int idx = 0; idx < today.length(); idx++) {
                    if (today.charAt(idx) == '1') {
                        return calculateDate(dateStr, idx);
                    }
                }
            }
        }
        return null;
    }

    /**
     * 根据日期和半小时下标计算指定时间
     * @param dateStr
     * @param halfHourIndex
     * @return
     */
    private Date calculateDate(String dateStr, int halfHourIndex) {
        if (StringUtils.isEmpty(dateStr) || halfHourIndex < 0 || halfHourIndex > 47) {
            return null;
        }

        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date date = sdf.parse(dateStr);

            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            // 设置时分秒
            calendar.set(Calendar.HOUR_OF_DAY, halfHourIndex / 2);
            calendar.set(Calendar.MINUTE, (halfHourIndex % 2) * 30);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);

            return calendar.getTime();
        } catch (Exception e) {
            log.error("calculateDate error, dateStr = {}, halfHourIndex = {}", dateStr, halfHourIndex, e);
            return null;
        }
    }

    @Override
    public boolean retryCall(long detailId,int sceneType) {
        AIPhoneCallDetailEntity callDetailEntity = aiPhoneCallDetailDAO.getById(detailId);
        // 复制一份明细
        AIPhoneCallDetailEntity newCallDetailEntity = insertRetryCallDetail(callDetailEntity,sceneType);
        boolean isRetrySuccess = false;
        if (newCallDetailEntity != null) {
//            AIPhoneCallConfig aiPhoneCallConfig = lionConfigUtil.getAiPhoneCallConfig();
            AIPhoneCallConfig aiPhoneCallConfig = lionConfigUtil.getMatchAiPhoneCallConfig(sceneType);
            isRetrySuccess = ProducerUtils.sendDelayMessageWithFeedback(aiPhoneCallDelayProducer, buildAIPhoneCallDelayData(newCallDetailEntity),
                    aiPhoneCallConfig.getRetryCallDelaySecond() * 1000L, "aiPhoneCallDelayProducer-retryCall");
            if (!isRetrySuccess) {
                stateManager.transitAICallDetailState(detailId, AIPhoneCallDetailStatusEnum.FAILED, "加入重拨队列失败");
            } else {
                // 旧外呼明细置为重播成功
                callDetailEntity.setExpectedCallTime(DateUtils.addSeconds(new Date(), aiPhoneCallConfig.getRetryCallDelaySecond()));
                aiPhoneCallDetailDAO.updateExpectedCallTimeInt(callDetailEntity);
                stateManager.transitAICallDetailState(detailId, AIPhoneCallDetailStatusEnum.RETRY_SUCCESS, "加入重拨队列成功");
            }
        }
        return isRetrySuccess;
    }

    private AIPhoneCallDetailEntity insertRetryCallDetail(AIPhoneCallDetailEntity oldCallDetailEntity,int sceneType) {
//        AIPhoneCallConfig aiPhoneCallConfig = lionConfigUtil.getAiPhoneCallConfig();
        AIPhoneCallConfig aiPhoneCallConfig = lionConfigUtil.getMatchAiPhoneCallConfig(sceneType);
        AIPhoneCallDetailEntity newCallDetailEntity = new AIPhoneCallDetailEntity();
        newCallDetailEntity.setCallPhone(oldCallDetailEntity.getCallPhone());
        newCallDetailEntity.setStatus(AIPhoneCallDetailStatusEnum.RECALL_DELAY.getStatus());
        newCallDetailEntity.setShopId(oldCallDetailEntity.getShopId());
        newCallDetailEntity.setTaskId(oldCallDetailEntity.getTaskId());
        newCallDetailEntity.setExtraData(oldCallDetailEntity.getExtraData());
        newCallDetailEntity.setSequenceId(oldCallDetailEntity.getSequenceId());
        newCallDetailEntity.setPlatform(oldCallDetailEntity.getPlatform());
        newCallDetailEntity.setDynamicVariable(oldCallDetailEntity.getDynamicVariable());
        newCallDetailEntity.setUserId(oldCallDetailEntity.getUserId());
        newCallDetailEntity.setBotId(oldCallDetailEntity.getBotId());
        newCallDetailEntity.setBotVersion(oldCallDetailEntity.getBotVersion());
        newCallDetailEntity.setRoutePoint(oldCallDetailEntity.getRoutePoint());
        newCallDetailEntity.setDdlDate(oldCallDetailEntity.getDdlDate());
        newCallDetailEntity.setExpectedCallTime(DateUtils.addSeconds(new Date(), aiPhoneCallConfig.getRetryCallDelaySecond()));
        aiPhoneCallDetailDAO.insert(newCallDetailEntity);
        if (newCallDetailEntity.getId() <= 0) {
            return null;
        }
        return newCallDetailEntity;
    }

    @Override
    public boolean preCheckBeforeRetry(long detailId,int sceneType) {
//        AIPhoneCallConfig aiPhoneCallConfig = lionConfigUtil.getAiPhoneCallConfig();
        AIPhoneCallConfig aiPhoneCallConfig = lionConfigUtil.getMatchAiPhoneCallConfig(sceneType);
        if (!aiPhoneCallConfig.isRetryCallSwitch()) {
            stateManager.transitAICallDetailState(detailId, AIPhoneCallDetailStatusEnum.FAILED, "不支持重试外呼");
            return false;
        }

        AIPhoneCallDetailEntity callDetailEntity = aiPhoneCallDetailDAO.getById(detailId);
        Long count = redisStoreClient.incrBy(RedisKeys.getAIPhoneCallRetryCntKey(callDetailEntity.getTaskId(), callDetailEntity.getShopId()),
                1, 7 * 24 * 60 * 60);
        if (count > aiPhoneCallConfig.getMaxRetryCallCount()) {
            stateManager.transitAICallDetailState(detailId, AIPhoneCallDetailStatusEnum.FAILED, "已超过重试次数上线");
            return false;
        }
        return true;
    }

    @Override
    public int getShopNegativeCallCount(long dpShopId) {
        List<AIPhoneCallSentimentEntity> callSentimentEntities = callSentimentDAO.selectByDpShopID(dpShopId, AIPhoneCallConstants.NEGATIVE_SENTIMENT_CODE);
        return CollectionUtils.emptyIfNull(callSentimentEntities).size();
    }

    @Override
    public boolean preCheckBeforeIvrCall(long callDetailId) {
        try {
            AIPhoneCallDetailEntity callDetailEntity = aiPhoneCallDetailDAO.getById(callDetailId);
            AIPhoneCallTaskEntity callTaskEntity = aiPhoneCallTaskDAO.getByTaskId(callDetailEntity.getTaskId());
            long dpShopId = shopDomainService.getDpShopId(callDetailEntity.getShopId(), callDetailEntity.getPlatform());
            // 风控校验
            Pair<Boolean, String> checkPass = riskCheckCallTarget(dpShopId, callTaskEntity.getSceneType());
            if (!checkPass.getLeft()) {
                stateManager.transitAICallDetailState(callDetailEntity.getId(), AIPhoneCallDetailStatusEnum.BLOCK, "风控校验未通过," + checkPass.getRight());
                return false;
            }
            // DdlDate校验
            boolean isOverDdlDate = checkIsOverDdlDate(callDetailEntity.getDdlDate());
            if (isOverDdlDate) {
                stateManager.transitAICallDetailState(callDetailEntity.getId(), AIPhoneCallDetailStatusEnum.FAILED, "已超过最晚拨打日期");
                return false;
            }
            // 可外呼时间校验
            boolean allowCall = isAllowCallTime(dpShopId);
            if (!allowCall) {
                stateManager.transitAICallDetailState(callDetailEntity.getId(), AIPhoneCallDetailStatusEnum.DELAY, "在不可外呼时间内，延迟外呼");
                return false;
            }
            return true;
        } catch (Exception e) {
            log.error("preCheckBeforeIvrCall error, callDetailId = {}", callDetailId, e);
        }
        return true;
    }

    private boolean checkIsOverDdlDate(String ddlDate) {
        if (StringUtils.isEmpty(ddlDate)) {
            return true;
        }

        try {
            // 获取今天零点时间
            Calendar today = Calendar.getInstance();
            today.set(Calendar.HOUR_OF_DAY, 0);
            today.set(Calendar.MINUTE, 0);
            today.set(Calendar.SECOND, 0);
            today.set(Calendar.MILLISECOND, 0);

            // 获取目标日期零点时间
            Calendar targetDate = Calendar.getInstance();
            targetDate.setTime(new SimpleDateFormat("yyyy-MM-dd").parse(ddlDate));
            targetDate.set(Calendar.HOUR_OF_DAY, 0);
            targetDate.set(Calendar.MINUTE, 0);
            targetDate.set(Calendar.SECOND, 0);
            targetDate.set(Calendar.MILLISECOND, 0);

            // 比较日期
            return today.after(targetDate);
        } catch (Exception e) {
            log.error("isOverDdlDate error, ddlDate: {}", ddlDate, e);
            return true;
        }
    }

    @Override
    public void cancelTaskDetail(long taskId) {
        List<AIPhoneCallDetailEntity> callDetailEntities = aiPhoneCallDetailDAO.getByTaskId(taskId);
        List<AIPhoneCallDetailEntity> unCompleteDetailEntities = CollectionUtils.emptyIfNull(callDetailEntities).stream()
                .filter(entity -> !AIPhoneCallDetailStatusEnum.isDone(entity.getStatus()))
                .collect(Collectors.toList());

        for (AIPhoneCallDetailEntity callDetailEntity: unCompleteDetailEntities) {
            stateManager.transitAICallDetailState(callDetailEntity.getId(), AIPhoneCallDetailStatusEnum.TERMINATED, "取消外呼任务");
        }
    }

    private Pair<Boolean, String> riskCheckCallTarget(long dpShopId, int sceneType) {
        // 取消外呼不作校验
        if (sceneType == AIPhoneCallSceneTypeEnum.CANCEL_RESERVATION.getType()) {
            return Pair.of(true, "取消外呼不用校验");
        }
        // 黑名单校验
        boolean isInBlackList = isInBlacklist(dpShopId);
        if (isInBlackList) {
            return Pair.of(false, "在黑名单中");
        }
        // 情绪识别校验
        boolean checkSentimentPass = checkSentimentRisk(dpShopId,sceneType);
        if (!checkSentimentPass) {
            return Pair.of(false, "负面情绪反馈达到阈值");
        }
        // 外呼频次校验
        boolean isExceed = exceedMaxCallFrequency(dpShopId,sceneType);
        if (isExceed) {
            return Pair.of(false, "外呼频次到达上限");
        }
        return Pair.of(true, "通过风控校验");
    }

    /**
     * 情绪风险校验
     * @param dpShopId
     * @return true：校验通过；false：校验不通过
     */
    private boolean checkSentimentRisk(long dpShopId,int sceneType) {
//        AIPhoneCallConfig aiPhoneCallConfig = lionConfigUtil.getAiPhoneCallConfig();
        AIPhoneCallConfig aiPhoneCallConfig = lionConfigUtil.getMatchAiPhoneCallConfig(sceneType);
        if (!aiPhoneCallConfig.isSentimentCheckSwitch()) {
            return true;
        }

        int negativeCallCount = getShopNegativeCallCount(dpShopId);
        return negativeCallCount < aiPhoneCallConfig.getNegativeSentimentThreshold();
    }

    public boolean isInBlacklist(long dpShopId){
        if(dpShopId <= 0){
            return true;
        }

        List<AIPhoneCallBlacklistEntity> callBlacklistEntities = callBlacklistDAO.getByDpShopId(dpShopId);
        boolean hasValidBlackRecord = CollectionUtils.emptyIfNull(callBlacklistEntities).stream()
                .filter(data -> DateUtils.isAfterNow(data.getExpireTime()))
                .findFirst().isPresent();
        return hasValidBlackRecord;
    }

    private boolean exceedMaxCallFrequency(long dpShopId,int sceneType) {
        if (dpShopId <= 0) {
            return true;
        }

        try {
            // 先从缓存中获取dpShopID门店当天的呼叫次数
            Long currentCount = redisStoreClient.get(RedisKeys.getAIPhoneCallFrequencyCntKey(dpShopId));
            if (currentCount == null) {
                return false;
            }
//            int maxCallCountPerDay = lionConfigUtil.getAiPhoneCallConfig().getMaxCallFrequency();
            int maxCallCountPerDay = lionConfigUtil.getMatchAiPhoneCallConfig(sceneType).getMaxCallFrequency();

            return currentCount > maxCallCountPerDay;
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("AICallVerificationService").build(),
                    new WarnMessage("exceedMaxCallCountPerDay", "频次校验异常", "系统异常"), dpShopId, e);
            return false;
        }
    }

    @Override
    public void processCallRecord(AIPhoneCallRecordData callRecordData) {
        // 保存记录
        String audioUrl = queryAuditUrl(callRecordData);
        boolean saveResult = saveCallRecord(callRecordData, audioUrl);
        if (!saveResult) {
            return;
        }
        Map<String, Object> bizData = callRecordData.getCustomer();
        Long callDetailId = MapUtils.getLong(bizData, AIPhoneCallConstants.CALL_DETAIL_ID);
        if (callDetailId == null) {
            return;
        }
        AIPhoneCallDetailEntity callDetailEntity = aiPhoneCallDetailDAO.getById(callDetailId);
        AIPhoneCallTaskEntity callTaskEntity = aiPhoneCallTaskDAO.getByTaskId(callDetailEntity.getTaskId());
        metricCallRecordResult(callRecordData, callTaskEntity.getSceneType(), callTaskEntity.getPlatform(), callTaskEntity.getSource());
        // 若外呼被终止，则不执行话单处理
        if (callDetailEntity.getStatus() == AIPhoneCallDetailStatusEnum.TERMINATED.getStatus()) {
            log.info("processCallRecord terminated, callDetailId = {}", callDetailId);
            return;
        }
        // 话单状态识别
        if (callSuccessCode.contains(callRecordData.getReleaseReason())) {
            // 通话成功
            stateManager.transitAICallDetailState(callDetailId, AIPhoneCallDetailStatusEnum.SUCCESS, "外呼通话成功回调");
        } else if (callFailAndCanRetryCode.contains(callRecordData.getReleaseReason())) {
            // 重新拨打
            stateManager.transitAICallDetailState(callDetailId, AIPhoneCallDetailStatusEnum.RETRY, "未拨通，重新外呼");
        }
    }

    private void metricCallRecordResult(AIPhoneCallRecordData callRecordData, int sceneType, int platform, int source) {
        Map<String, String> tags = Maps.newHashMap();
        tags.put("releaseReason", callRecordData.getReleaseReason());
        tags.put("sceneType", String.valueOf(sceneType));
        tags.put("platform", String.valueOf(platform));
        tags.put("source", String.valueOf(source));
        CatUtils.logMetricResult("AIPhoneCallRecord", tags);
    }

    private String queryAuditUrl(AIPhoneCallRecordData callRecordData) {
        if (callRecordData == null
            || StringUtils.isEmpty(callRecordData.getTenantId()) || StringUtils.isEmpty(callRecordData.getContactId())) {
            return null;
        }

        ResponseDTO<List<QueryRecordDataDTO>> responseDTO = jupiterCallAclService.queryAudioUrl(callRecordData.getTenantId(), callRecordData.getContactId());
        if (responseDTO == null || CollectionUtils.isEmpty(responseDTO.getData())) {
            return null;
        }
        return responseDTO.getData().get(0).getUrl();
    }

    private boolean saveCallRecord(AIPhoneCallRecordData callRecordData, String audioUrl) {
        try {
            AIPhoneCallRecordEntity callRecordEntity = convertToEntity(callRecordData, audioUrl);
            if (callRecordEntity == null) {
                return false;
            }
            int affectRows = callRecordDAO.insert(callRecordEntity);
            return affectRows > 0;
        } catch (Exception e) {
            log.error("saveCallRecord error, contactId = {}", callRecordData != null? callRecordData.getContactId(): 0, e);
        }
        return false;
    }

    private AIPhoneCallRecordEntity convertToEntity(AIPhoneCallRecordData data, String audioUrl) {
        if (data == null) {
            return null;
        }

        AIPhoneCallRecordEntity entity = new AIPhoneCallRecordEntity();
        entity.setContactType(data.getContactType());
        entity.setTenantId(data.getTenantId());
        entity.setContactId(data.getContactId());
        entity.setDisplayNum(data.getDisplayNum());
        entity.setOriDnis(data.getOriDnis());
        entity.setRingStartTime(data.getRingStartTime());
        entity.setRingTimeLen(data.getRingTimeLen());
        entity.setTalkingStartTime(data.getTalkingStartTime());
        entity.setTalkingTimeLen(data.getTalkingTimeLen());
        entity.setDialogRawData(JsonCodec.encodeWithUTF8(data.getDialogRawData()));
        entity.setReleaseReason(data.getReleaseReason());
        entity.setStartTime(data.getStartTime());
        entity.setEndTime(data.getEndTime());
        entity.setAudioUrl(audioUrl);
        entity.setAppCallUuid(data.getAppCallUuid());
        entity.setCustomer(JsonCodec.encodeWithUTF8(data.getCustomer()));
        entity.setKeyPressList(JsonCodec.encodeWithUTF8(data.getKeyPressList()));
        entity.setOntologyVersion(data.getOntologyVersion());
        return entity;
    }

}
