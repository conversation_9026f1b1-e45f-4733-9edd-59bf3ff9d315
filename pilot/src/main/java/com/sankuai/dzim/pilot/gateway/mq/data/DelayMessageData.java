package com.sankuai.dzim.pilot.gateway.mq.data;

import lombok.Data;

import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON> on 2020-03-25 12:43
 */
@Data
public class DelayMessageData {

    private String entrance;

    private long messageId;

    private int shopType;

    private int chatGroupId;

    private String imShopId;

    private String imUserId;

    private int messageStatus;

    /**
     * 延迟时间
     */
    private int delaySeconds;

    /**
     * 延时操作类型
     * @see com.sankuai.dzim.message.enums.DelayActionTypeEnums
     */
    private int delayActionType;

    private Map<String,String> extraInfo;
}
