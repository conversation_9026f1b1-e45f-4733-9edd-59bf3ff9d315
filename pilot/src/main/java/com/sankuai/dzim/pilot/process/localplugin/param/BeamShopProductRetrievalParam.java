package com.sankuai.dzim.pilot.process.localplugin.param;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import lombok.Data;

@Data
public class BeamShopProductRetrievalParam extends Param{

    @JsonPropertyDescription("语义条件，要查询的商品相关语义信息，e.g. 附赠小食好吃、理发师手艺好")
    @JsonProperty(required = false)
    private String productSemanticCondition;

    @JsonPropertyDescription("从用户的输入提取出用户期望的预订日期，格式为yyyy-MM-dd。e.g. 今天是2025-01-01，用户说帮我预订一个明天的商品，bookDate=2025-01-02")
    @JsonProperty(required = false)
    private String bookDate;

    @JsonPropertyDescription("搜索中的展示文案，根据用户的诉求生成。 e.g. 正在为你寻找性价比高的团购...")
    @JsonProperty(required = false)
    private String showText;
}
