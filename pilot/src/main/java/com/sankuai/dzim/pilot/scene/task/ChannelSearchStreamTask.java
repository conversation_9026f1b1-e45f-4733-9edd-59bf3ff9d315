package com.sankuai.dzim.pilot.scene.task;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.dzim.pilot.api.data.AIAnswerTypeEnum;
import com.sankuai.dzim.pilot.buffer.core.PilotBufferItemDO;
import com.sankuai.dzim.pilot.buffer.enums.BufferItemTypeEnum;
import com.sankuai.dzim.pilot.buffer.utils.BufferUtils;
import com.sankuai.dzim.pilot.chain.data.AIAnswerData;
import com.sankuai.dzim.pilot.process.data.ChannelSearchAssistantConfig;
import com.sankuai.dzim.pilot.utils.LionConfigUtil;
import com.sankuai.dzim.pilot.scene.data.AssistantExtraConstant;
import com.sankuai.dzim.pilot.scene.task.data.TaskContext;
import com.sankuai.dzim.pilot.scene.task.data.TaskResult;
import com.sankuai.dzim.pilot.scene.task.data.TaskTypeEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @since 2025/3/05 11:11
 */
@Component
public class ChannelSearchStreamTask extends CommonStreamTask implements Task {
    @Autowired
    private LionConfigUtil lionConfigUtil;

    public static final String LISTING_DATA_KEY = "listingData";
    public static final String EMPTY_SHOP_DEFAULT_REPLY = "暂时没有找到合适的商家，请稍后再试。";

    @Override
    public boolean accept(TaskContext context) {
        return TaskTypeEnum.CHANNEL_SEARCH_STREAM_TASK.getType().equals(context.getTaskType());
    }

    @Override
    public TaskResult execute(TaskContext context) {
        TaskResult sensitiveWordsResult = handleSensitiveWords(context);
        if (sensitiveWordsResult != null) {
            return sensitiveWordsResult;
        }
        AIAnswerData aiAnswerData = null;
        JSONObject bizParamsObj = JSONObject.parseObject(MapUtils.getString(context.getExtra(), AssistantExtraConstant.MESSAGE_BIZ_PARAMS));
        // 如果没有推荐门店，并且根据用户发的消息判断需要推荐门店，则返回默认回复
        if ((bizParamsObj == null || CollectionUtils.isEmpty(bizParamsObj.getJSONArray(LISTING_DATA_KEY)))
                && needRecommandShop(context)) {
            String defaultReply = EMPTY_SHOP_DEFAULT_REPLY;
            ChannelSearchAssistantConfig config = lionConfigUtil.getChannelSearchAssistantConfig();
            if (config != null && StringUtils.isNotBlank(config.getNoShopDefaultReply())) {
                defaultReply = config.getNoShopDefaultReply();
            }

            // 构造AIAnswerDat
            aiAnswerData = new AIAnswerData();
            aiAnswerData.setAiAnswerType(AIAnswerTypeEnum.TEXT.getType());
            aiAnswerData.setAnswer(defaultReply);

            // buffer中写入数据
            PilotBufferItemDO pilotBufferItemDO = new PilotBufferItemDO();
            pilotBufferItemDO.setType(BufferItemTypeEnum.MAIN_TEXT.getType());
            pilotBufferItemDO.setData(defaultReply);

            BufferUtils.writeMainTextBuffer(pilotBufferItemDO);
            return TaskResult.builder().taskContext(context).aiAnswerData(aiAnswerData).build();
        }

        // 调用chain层获取AIAnswerData
        aiAnswerData = getAIAnswerData(context);
        return TaskResult.builder().taskContext(context).aiAnswerData(aiAnswerData).build();
    }

    @Override
    public void afterExecute(TaskResult result) {

    }

    /**
     * 判断是否包含门店推荐关键词，包含的话需要 check 门店是否为空
     * @param context
     * @return
     */
    private boolean needRecommandShop(TaskContext context) {
        ChannelSearchAssistantConfig config = lionConfigUtil.getChannelSearchAssistantConfig();
        if (CollectionUtils.isEmpty(config.getKeyWords())) {
            return false;
        }

        String message = context.getMessageDTO().getMessage();
        for (String keyword : config.getKeyWords()) {
            if (message.contains(keyword)) {
                return true;
            }
        }
        return false;
    }
}
