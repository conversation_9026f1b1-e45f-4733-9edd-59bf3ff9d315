package com.sankuai.dzim.pilot.process.aibook.impl;

import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.dzim.pilot.process.aibook.AIBookCacheProcessService;
import com.sankuai.dzim.pilot.process.aibook.data.UserBookRequest;
import com.sankuai.dzim.pilot.process.localplugin.param.HaircutBookConfirmParam;
import com.sankuai.dzim.pilot.process.aireservebook.data.UserReserveInfo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2025/02/26 10:18
 */
@Service
public class AIBookCacheProcessServiceImpl implements AIBookCacheProcessService {

    private static final String ORIGIN_PREFIX = "origin_";

    private static final String BOOK_CACHE_CATEGORY = "AIBookCache";

    private static final String AGENT_RESERVE_USER_RESERVE_INFO = "agent_user_reserve_info";

    private static final String BEAM_AGENT_RESERVE_USER_RESERVE_INFO = "beam_agent_user_reserve_info";

    @Resource(name = "redisClient")
    private RedisStoreClient redisClient;

    @ConfigValue(key = "com.sankuai.mim.pilot.book.cache.expire.seconds", defaultValue = "1800")
    private int expireSeconds;

    // Todo：加一个Lion控制
    @ConfigValue(key = "com.sankuai.mim.pilot.reserve.and.book.cache.expire.seconds", defaultValue = "604800")
    private int agentReserveAndBookExpireSeconds;

    @Override
    public void setBookCache(String key, UserBookRequest userBookRequest) {
        redisClient.set(buildStoreKey(key, false), JsonCodec.encodeWithUTF8(userBookRequest), expireSeconds);
    }

    @Override
    public UserBookRequest getBookCache(String key) {
        String result = redisClient.get(buildStoreKey(key, false));
        return JsonCodec.decode(result, UserBookRequest.class);
    }

    @Override
    public Boolean containsBookCache(String key) {
        return redisClient.exists(buildStoreKey(key, false));
    }

    @Override
    public boolean containsOriginBookInfo(String imUserid) {
        return redisClient.exists(buildStoreKey(imUserid, true));
    }

    @Override
    public HaircutBookConfirmParam getOriginBookInfo(String imUserId) {
        String result = redisClient.get(buildStoreKey(imUserId, true));
        return JsonCodec.decode(result, HaircutBookConfirmParam.class);
    }

    @Override
    public void setOriginBookInfo(String userId, HaircutBookConfirmParam cacheBookInfo) {
        redisClient.set(buildStoreKey(userId, true), JsonCodec.encodeWithUTF8(cacheBookInfo), expireSeconds);
    }

    private StoreKey buildStoreKey(String key, boolean isOriginBookInfo) {
        if (isOriginBookInfo) {
            key = ORIGIN_PREFIX + key;
            return new StoreKey(BOOK_CACHE_CATEGORY, key);
        }
        return new StoreKey(BOOK_CACHE_CATEGORY, key);
    }

    @Override
    public boolean containsAgentReserveAndBookInfo(String imUserId, Long shopId, int platform) {
        return redisClient.exists(buildAgentReserveAndBookStoreKey(imUserId, shopId, platform));
    }

    @Override
    public UserReserveInfo getAgentReserveAndBookInfo(String imUserId, Long shopId, int platform) {
        String result = redisClient.get(buildAgentReserveAndBookStoreKey(imUserId, shopId, platform));
        return JsonCodec.decode(result, UserReserveInfo.class);
    }

    @Override
    public void setAgentReserveAndBookInfo(String imUserId, Long shopId, int platform, UserReserveInfo userReserveInfo) {
        redisClient.set(buildAgentReserveAndBookStoreKey(imUserId, shopId, platform), JsonCodec.encodeWithUTF8(userReserveInfo), agentReserveAndBookExpireSeconds);
    }

    @Override
    public void deleteAgentReserveAndBookInfo(String imUserId, Long shopId, int platform) {
        redisClient.delete(buildAgentReserveAndBookStoreKey(imUserId, shopId, platform));
    }

    private StoreKey buildAgentReserveAndBookStoreKey(String userId, Long shopId, int platform) {
        return new StoreKey(AGENT_RESERVE_USER_RESERVE_INFO, userId, shopId, platform);
    }

    @Override
    public boolean containsBeamAgentReserveAndBookInfo(String imUserId, Long shopId, int platform) {
        return redisClient.exists(buildBeamAgentReserveAndBookStoreKey(imUserId, shopId, platform));
    }

    @Override
    public UserReserveInfo getBeamAgentReserveAndBookInfo(String imUserId, Long shopId, int platform) {
        String result = redisClient.get(buildBeamAgentReserveAndBookStoreKey(imUserId, shopId, platform));
        return JsonCodec.decode(result, UserReserveInfo.class);
    }

    @Override
    public void setBeamAgentReserveAndBookInfo(String imUserId, Long shopId, int platform, UserReserveInfo userReserveInfo) {
        redisClient.set(buildBeamAgentReserveAndBookStoreKey(imUserId, shopId, platform), JsonCodec.encodeWithUTF8(userReserveInfo), agentReserveAndBookExpireSeconds);
    }

    @Override
    public void deleteBeamAgentReserveAndBookInfo(String imUserId, Long shopId, int platform) {
        redisClient.delete(buildBeamAgentReserveAndBookStoreKey(imUserId, shopId, platform));
    }

    private StoreKey buildBeamAgentReserveAndBookStoreKey(String userId, Long shopId, int platform) {
        return new StoreKey(BEAM_AGENT_RESERVE_USER_RESERVE_INFO, userId, shopId, platform);
    }
}
