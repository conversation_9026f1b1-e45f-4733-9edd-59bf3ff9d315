package com.sankuai.dzim.pilot.domain.data;

import lombok.Getter;

public enum GenerativeSearchAnswerStatus {
    DELETE(0, "删除"),
    VALID(1, "有效"),
    FORMAT_ERROR(2, "格式错误"),
    FORBIDDEN_WORD(3, "违规词")
    ;

    @Getter
    private int code;

    @Getter
    private String desc;

    GenerativeSearchAnswerStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
