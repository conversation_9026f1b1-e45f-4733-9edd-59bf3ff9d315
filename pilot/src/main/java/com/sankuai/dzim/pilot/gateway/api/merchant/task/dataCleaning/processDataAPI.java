package com.sankuai.dzim.pilot.gateway.api.merchant.task.dataCleaning;

import com.dianping.gateway.enums.APIModeEnum;
import com.dianping.vc.web.api.API;
import com.dianping.vc.web.api.URL;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Setter
@Service
@URL(url = "/dzim/pilot/merchant/process/data", mode = APIModeEnum.MERCHANT)
public class processDataAPI implements API {

    private int stage;

    private int start;

    private int end;

    @Autowired
    private DeleteExtraDataTask deleteExtraDataTask;

    @Autowired
    private EscapeQuotesTask escapeQuotesTask;

    private static final int BATCH_SIZE = 50;


    @Override
    public Object execute() {
        ThreadTaskTemplate task;
        switch (stage) {
            // 从id: 1057175开始删除多插入的数据
            case 1:
                processTask(deleteExtraDataTask);
                break;
            case 2:
                // 数据清洗
                processTask(escapeQuotesTask);
                break;
            default:
                return "未知的处理阶段！";
        }
        return "执行成功！";
    }

    private void processTask(ThreadTaskTemplate task) {
        int totalMysql = end - start;
        int dealThread = (int) Math.ceil(totalMysql / (double) BATCH_SIZE);
        task.multiThread(dealThread, BATCH_SIZE, start);
    }
}