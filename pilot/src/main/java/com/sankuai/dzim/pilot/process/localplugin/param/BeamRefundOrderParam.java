package com.sankuai.dzim.pilot.process.localplugin.param;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import lombok.Data;

/**
 * Beam团购退款工具
 *
 * <AUTHOR>
 * @date 2025/5/6
 */
@Data
public class BeamRefundOrderParam extends Param {

    @JsonPropertyDescription("Extract the order id from the context.")
    @JsonProperty(required = true)
    private Long orderId;


    @JsonPropertyDescription("The count of the product item is extracted from the user input. If not mentioned, the default value is 0")
    @JsonProperty(required = false)
    private Integer count;
}
