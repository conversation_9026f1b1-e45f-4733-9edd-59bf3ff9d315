package com.sankuai.dzim.pilot.process.aicall.data;

import com.sankuai.dzim.pilot.api.data.beauty.ai.BookTimePeriod;
import com.sankuai.dzim.pilot.process.aibook.data.DistanceRangeEnum;
import com.sankuai.dzim.pilot.process.aibook.data.IntentProjectCodeEnum;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;


@Data
public class UserCallRequest {

    /**
     * 平台 1-点评 2-美团
     */
    private int platform;

    /**
     * 可以传入门店id
     */
    private long shopId;

    /**
     * 用户ID，跟随平台
     */
    private long userId;

    /**
     * 城市ID，跟随平台
     */
    private int cityId;

    /**
     * 后台类目id
     */
    private Integer catId;

    /**
     * 经纬度
     */
    private Double lng;

    /**
     * 经纬度
     */
    private Double lat;
    /**
     * 活动类型
     *
     * @see ActivityTypeEnum
     */
    private Long activityType;

    /**
     * 用户手机号
     */
    private String userPhone;
    /**
     * 设备id
     * 点评: dpid
     * 美团: uuid
     * 小程序:openid
     */
    private String deviceId;

    /**
     * 场景code
     */
    private Integer sceneCode;

    /**
     * 任务来源入口
     */
    private Integer taskSource;

    /**
     * 时间段
     */
    private List<BookTimePeriod> bookTimePeriods;

    /**
     * 业务相关数据
     */
    private Map<String, String> businessData;

}
