package com.sankuai.dzim.pilot.gateway.api.aibook;

import com.dianping.vc.web.api.API;
import com.dianping.vc.web.api.URL;
import com.dianping.vc.web.api.VCAssert;
import com.dianping.vc.web.biz.client.api.AppContext;
import com.dianping.vc.web.biz.client.api.AppContextAware;
import com.dianping.vc.web.biz.client.api.MTUserIdAware;
import com.dianping.vc.web.biz.client.api.UserIdAware;
import com.sankuai.dzim.pilot.api.enums.assistant.PlatformEnum;
import com.sankuai.dzim.pilot.gateway.api.aibook.data.AIBookProgressVO;
import com.sankuai.dzim.pilot.process.aibook.AIBookProcessService;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;

@Slf4j
@URL(url = "/dzim/pilot/ai/book/progress/query", methods = "GET")
public class AIBookProgressQueryMAPI implements API, UserIdAware, MTUserIdAware, AppContextAware {

    @Setter
    private AppContext appContext;

    @Setter
    private long userId;

    @Setter
    private long mTUserId;

    @Setter
    private String version;

    @Setter
    private long taskid;

    @Resource
    private AIBookProcessService aiBookProcessService;

    @Override
    public Object execute() {
        validateParams();

        long finalUserId = appContext.getPlatform().equals(PlatformEnum.DP.getName()) ? userId : mTUserId;
        int platform = PlatformEnum.getByName(appContext.getPlatform()).getType();
        AIBookProgressVO aiBookProgressVO = aiBookProcessService.queryBookProcess(finalUserId, platform,
                StringUtils.defaultString(version, "0"), taskid);
        return aiBookProgressVO;
    }

    private void validateParams() {
        VCAssert.isTrue(userId != 0 || mTUserId != 0, API.UNLOGIN_CODE, "用户未登录");
        VCAssert.isTrue(appContext.getPlatform().equals(PlatformEnum.DP.getName()) || appContext.getPlatform().equals(PlatformEnum.MT.getName()), API.INVALID_PARAM_CODE, "平台参数不合法");
    }
}
