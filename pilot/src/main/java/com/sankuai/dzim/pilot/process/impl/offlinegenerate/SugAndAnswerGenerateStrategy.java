package com.sankuai.dzim.pilot.process.impl.offlinegenerate;

import com.sankuai.dzim.pilot.process.data.GenerativeSearchGenerateData;
import com.sankuai.dzim.pilot.process.data.OfflineGenerateAnswerType;

import java.util.List;

public class SugAndAnswerGenerateStrategy implements OfflineGenerateAnswerStrategy {
    @Override
    public boolean accept(int type) {
        return type == OfflineGenerateAnswerType.SUG_ANSWER.getCode();
    }

    @Override
    public GenerativeSearchGenerateData generate(List<GenerativeSearchGenerateData> datas) {
        // 1.生成sug

        // 2.生成答案

        // 3.生成关联项目sug

        // 4.生成关联项目sug答案
        
        return null;
    }
}
