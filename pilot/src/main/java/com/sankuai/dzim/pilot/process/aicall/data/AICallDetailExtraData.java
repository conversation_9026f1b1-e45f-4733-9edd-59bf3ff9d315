package com.sankuai.dzim.pilot.process.aicall.data;

import com.sankuai.dzim.pilot.api.data.beauty.ai.HistoryMessageDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AICallDetailExtraData {
    /**
     * 今日已外呼且有明确意图的detailId
     */
    private long existedCallDetailId;

    /**
     * 木星接通contactId
     */
    private String contactId;

    /**
     * 商户备注
     */
    private int inquiryStatus;

    /**
     * 提取用户预约时间
     */
    private Date playTime;

    /**
     * 挂断原因
     */
    private String releaseReason;


    /**
     * 取消的外呼 contactId
     */
    private String cancelContactId;


    /**
     * 通话历史消息
     */
    private List<Object> history;

    /**
     * 回调返回的预约结果 返回的code
     */
    private int callBackResult;

    /**
     * 其他备注参数
     */
    private Map<String,String> extend;

    /**
     * 业务信息
     */
    private Map<String, String> businessInfo;

}
