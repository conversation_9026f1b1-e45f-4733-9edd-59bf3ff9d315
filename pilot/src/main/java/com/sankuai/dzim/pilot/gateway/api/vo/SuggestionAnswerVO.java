package com.sankuai.dzim.pilot.gateway.api.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.sankuai.dzim.pilot.api.enums.search.generative.GenerativeSearchAnswerTemplateTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/11/12 12:58
 */
@Data
public class SuggestionAnswerVO implements Serializable {
    /**
     * 模版类型
     * @see GenerativeSearchAnswerTemplateTypeEnum
     */
    @MobileDo.MobileField
    private Integer templateType;

    /**
     * 搜索记录id, 如果是分页查询, 仅第一页会返回recordId
     */
    @MobileDo.MobileField
    private Long searchRecordId;

    /**
     * 头部信息
     */
    @MobileDo.MobileField
    private PilotHeadInfoVO headInfo;

    /**
     * tab信息
     */
    @MobileDo.MobileField
    private List<PilotTabInfoVO> tabInfo;

    /**
     * 人群id
     */
    @MobileDo.MobileField
    private String crowdId;
}
