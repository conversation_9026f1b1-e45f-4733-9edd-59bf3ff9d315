package com.sankuai.dzim.pilot.dal.pilotdao.aicall;

import com.sankuai.dzim.pilot.dal.entity.aicall.AICallTaskEntity;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface AICallTaskDAO {

    int insert(@Param("entity") AICallTaskEntity taskEntity);

    AICallTaskEntity getById(@Param("id") long id);

    int updateTaskStatus(@Param("id") long id, @Param("taskStatus") int taskStatus);

    int updateTaskCallSuccess(@Param("id") long id, @Param("taskStatus") int taskStatus);


    AICallTaskEntity queryLatestBookTask(@Param("userId") long userId, @Param("platform") int platform);

    List<AICallTaskEntity> queryCallTaskList(@Param("userId") long userId, @Param("platform") int platform);


    List<AICallTaskEntity> queryUserTasksByStatus(@Param("userId") long userId, @Param("platform") int platform,
                                                  @Param("taskStatusList") List<Integer> taskStatusList);

}
