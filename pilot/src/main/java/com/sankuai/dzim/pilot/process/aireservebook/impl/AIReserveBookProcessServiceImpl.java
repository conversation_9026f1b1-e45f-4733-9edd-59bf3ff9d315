package com.sankuai.dzim.pilot.process.aireservebook.impl;

import com.alibaba.fastjson.JSON;
import com.dianping.dzim.common.enums.ImUserType;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.nibtp.trade.client.combine.bean.WebParamDTO;
import com.meituan.nibtp.trade.client.combine.enums.AgentGeneralBookingChannel;
import com.meituan.nibtp.trade.client.combine.enums.AgentOrderType;
import com.meituan.nibtp.trade.client.combine.requset.AgentGeneralBookingReqDTO;
import com.meituan.nibtp.trade.client.combine.requset.AgentRefundApplyOrderReqDTO;
import com.meituan.nibtp.trade.client.combine.response.AgentCreateOrderResDTO;
import com.meituan.nibtp.trade.client.combine.response.AgentRefundApplyOrderResDTO;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.acl.AgentTradeAclService;
import com.sankuai.dzim.pilot.acl.ProductAclService;
import com.sankuai.dzim.pilot.acl.ShopAclService;
import com.sankuai.dzim.pilot.acl.data.ShopBackCategoryData;
import com.sankuai.dzim.pilot.api.enums.assistant.PlatformEnum;
import com.sankuai.dzim.pilot.enums.AIPhoneCallDynamicParamEnum;
import com.sankuai.dzim.pilot.enums.AIPhoneCallSceneTypeEnum;
import com.sankuai.dzim.pilot.enums.AIPhoneCallSourceEnum;
import com.sankuai.dzim.pilot.enums.MessageBizRefTypeEnum;
import com.sankuai.dzim.pilot.gateway.api.assistant.data.SendBeamMessageReq;
import com.sankuai.dzim.pilot.process.PilotMessageBizRefProcessService;
import com.sankuai.dzim.pilot.process.aiphonecall.AIPhoneCallProcessService;
import com.sankuai.dzim.pilot.process.aiphonecall.callback.impl.BeamReserveAgentPhoneCallBack;
import com.sankuai.dzim.pilot.process.aiphonecall.callback.impl.ReserveAgentPhoneCallBack;
import com.sankuai.dzim.pilot.process.aiphonecall.data.AIPhoneCallDetailCreateReq;
import com.sankuai.dzim.pilot.process.aiphonecall.data.AIPhoneCallTaskCreateReq;
import com.sankuai.dzim.pilot.process.aiphonecall.data.AIPhoneCallTaskCreateRes;
import com.sankuai.dzim.pilot.process.aireservebook.AIReserveBookProcessService;
import com.sankuai.dzim.pilot.process.aireservebook.data.ReserveInfo;
import com.sankuai.dzim.pilot.process.aireservebook.data.ShopReserveContext;
import com.sankuai.dzim.pilot.process.aireservebook.data.TimeSliceStockInfo;
import com.sankuai.dzim.pilot.process.aireservebook.data.UserReserveInfo;
import com.sankuai.dzim.pilot.process.aireservebook.enums.*;
import com.sankuai.dzim.pilot.process.data.bizref.MessageBizRefData;
import com.sankuai.dzim.pilot.scene.data.AssistantSceneContext;
import com.sankuai.dzim.pilot.scene.task.data.EnvContext;
import com.sankuai.dzim.pilot.utils.PluginContextUtil;
import com.sankuai.dzim.pilot.utils.ReserveInfoProcessUtils;
import com.sankuai.dzim.pilot.utils.TimeUtil;
import com.sankuai.dzim.pilot.utils.UuidUtil;
import com.sankuai.dzim.pilot.utils.context.RequestContext;
import com.sankuai.dzim.pilot.utils.context.RequestContextConstants;
import com.sankuai.dzim.pilot.utils.data.Response;
import com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectDTO;
import com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectGroupDTO;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.wpt.user.retrieve.thrift.message.UserModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * AIReserveBookProcessServiceImpl
 * <AUTHOR>
 */
@Component
@Slf4j
public class AIReserveBookProcessServiceImpl implements AIReserveBookProcessService {

    @Autowired
    private ShopAclService shopAclService;

    @Autowired
    private AgentTradeAclService agentTradeAclService;

    @Autowired
    private AIPhoneCallProcessService aiPhoneCallProcessService;

    @Autowired
    private ProductAclService productAclService;

    @Autowired
    private PilotMessageBizRefProcessService pilotMessageBizRefProcessService;

    @Override
    public boolean checkUserDuplicateReserve(UserReserveInfo userReserveInfo) {
        // 用户在当前门店已预约则认为存在重复预约
        return userReserveInfo != null && userReserveInfo.getReserveId() != null && userReserveInfo.getReserveId() > 0;
    }

    @Override
    public boolean checkTimeValid(UserReserveInfo userReserveInfo) {
        String startTime = userReserveInfo == null ? null : ReserveInfoProcessUtils.getStringItem(userReserveInfo.getCollectedReserveInfo(), ReserveItemType.START_TIME.getKey());
        if (StringUtils.isBlank(startTime)) {
            return true;
        }
        LocalDateTime startDateTime = TimeUtil.convertStr2LocalDateTime(startTime);
        if (startDateTime == null) {
            return true;
        }
        return startDateTime.isAfter(LocalDateTime.now());
    }

    @Override
    public Integer extractMainSecondBackCategoryId(DpPoiDTO dpPoiDTO) {
        if (dpPoiDTO == null) {
            return null;
        }
        List<ShopBackCategoryData> backCategoryDatas = shopAclService.extractShopBackMainCategoryPath(dpPoiDTO);
        if (CollectionUtils.isEmpty(backCategoryDatas)) {
            return null;
        }
        List<Integer> mainSecondBackCategoryIds =  backCategoryDatas.stream().filter(Objects::nonNull)
                .filter(category -> category.getLevel() == 2)
                .map(ShopBackCategoryData::getCategoryId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(mainSecondBackCategoryIds)) {
            return null;
        }
        return mainSecondBackCategoryIds.get(0);
    }

    @Override
    public Map<ReserveItemType, String> queryAndConvertDealDetail2ReserveItems(Long dealId, int platform) {
        if (dealId == null || dealId <= 0) {
            return Maps.newHashMap();
        }
        boolean isMt = platform == PlatformEnum.MT.getType();
        DealGroupDTO dealGroupDTO = productAclService.getDealBaseInfo(dealId, isMt);
        if (dealGroupDTO == null) {
            return Maps.newHashMap();
        }
        Map<ReserveItemType, String> result = Maps.newHashMap();
        // Todo: 观察是否是必须的，发起预约时可从其他渠道取dealId
        result.put(ReserveItemType.PROJECT_CODE, String.valueOf(dealId));
        if (dealGroupDTO.getBasic() != null) {
            DealGroupBasicDTO basicDTO = dealGroupDTO.getBasic();
            result.put(ReserveItemType.PROJECT, basicDTO.getTitle());
        }
        Integer duration = extractDuration(dealGroupDTO);
        if (duration != null) {
            result.put(ReserveItemType.DURATION, String.format("%d分钟", duration));
        }
        return result;
    }

    @Override
    public Integer extractDuration(DealGroupDTO dealGroupDTO) {
        if (dealGroupDTO == null || dealGroupDTO.getStandardServiceProject() == null) {
            return null;
        }
        StandardServiceProjectDTO standardServiceProject = dealGroupDTO.getStandardServiceProject();
        try {
            String duration = extractAttr(standardServiceProject.getMustGroups(), "serviceDurationInt");
            if (StringUtils.isBlank(duration)) {
                duration = extractAttr(standardServiceProject.getOptionalGroups(), "serviceDurationInt");
            }
            if (StringUtils.isNotBlank(duration)) {
                return Integer.parseInt(duration);
            }
        } catch (Exception e) {
            log.error("[AIReserveBookProcessServiceImpl] extractDuration error, mtDealId={}", dealGroupDTO.getMtDealGroupId(), e);
        }
        return null;
    }

    private String extractAttr(List<StandardServiceProjectGroupDTO> groups, String attrName) {
        if (CollectionUtils.isEmpty(groups) || StringUtils.isEmpty(attrName)) {
            return null;
        }
        return groups.stream()
                .filter(group -> group != null && CollectionUtils.isNotEmpty(group.getServiceProjectItems()))
                .flatMap(group -> group.getServiceProjectItems().stream())
                .filter(item -> item != null && item.getStandardAttribute() != null && CollectionUtils.isNotEmpty(item.getStandardAttribute().getAttrs()))
                .flatMap(item -> item.getStandardAttribute().getAttrs().stream())
                .filter(Objects::nonNull)
                .filter(attr -> attrName.equals(attr.getAttrName()) && CollectionUtils.isNotEmpty(attr.getAttrValues()))
                .flatMap(attr -> attr.getAttrValues().stream())
                // 取SimpleValues
                .filter(value -> value != null && CollectionUtils.isNotEmpty(value.getSimpleValues()))
                .flatMap(value -> value.getSimpleValues().stream())
                // 取第一个有值的
                .filter(StringUtils::isNotBlank)
                .findFirst().orElse(null);
    }

    @Override
    public AgentCreateOrderResDTO createReserve(EnvContext envContext, UserModel userModel, ShopReserveContext shopReserveContext, UserReserveInfo reserveInfo, String merchantRemark, boolean needStock, List<TimeSliceStockInfo> availableTimeSliceStocks) {
        if (userModel == null || shopReserveContext == null || reserveInfo == null || reserveInfo.getCollectedReserveInfo() == null) {
            return null;
        }
        // 发起预约/预订
        WebParamDTO webParam = agentTradeAclService.buildWebParam(userModel, envContext);
        AgentGeneralBookingReqDTO req = buildGeneralBookingReq(shopReserveContext, reserveInfo, merchantRemark, needStock, availableTimeSliceStocks);
        AgentCreateOrderResDTO resp = agentTradeAclService.createGeneralBookingOrder(webParam, Lists.newArrayList(req));
        if (resp == null || resp.getMainOrderId() == null) {
            LogUtils.logFailLog(log, TagContext.builder().action("AIReserveBookProcessService").build(),
                    WarnMessage.build("AIReserveBookProcessService", "调用预约预订接口失败", ""),
                    String.format("envContext:%s, shopReserveContext:%s, req:%s",
                            JSON.toJSONString(envContext), JSON.toJSONString(shopReserveContext), JSON.toJSONString(req)), JSON.toJSONString(resp));
        }
        return resp;
    }

    private AgentGeneralBookingReqDTO buildGeneralBookingReq(ShopReserveContext shopReserveContext, UserReserveInfo reserveInfo, String merchantRemark, boolean needStock, List<TimeSliceStockInfo> availableTimeSliceStocks) {
        AgentGeneralBookingReqDTO req = new AgentGeneralBookingReqDTO();
        Long secondBackCategoryId = shopReserveContext.getSecondBackCategory() == null ? null : Long.valueOf(shopReserveContext.getSecondBackCategory());
        req.setPoiCategoryList(Lists.newArrayList(secondBackCategoryId));
        req.setPoiId(shopReserveContext.getMtShopId());
        req.setBookingChannel(ShopReserveWayType.ONLINE.getType() == reserveInfo.getShopReserveWayType() ? AgentGeneralBookingChannel.ONLINE_STOCK.getCode(): AgentGeneralBookingChannel.IVR.getCode());
        // 仅先买后约设置订单ID
        if (isAfterOrder(reserveInfo)) {
            req.setOrderId(reserveInfo.getOrderId());
        }
        // 设置SKU ID，注意这里下游需要的是biz skuId，如果是平台skuId需要转换一次
        if (isSkuIdRequired(shopReserveContext)) {
            req.setBizSkuId(reserveInfo.getSkuId());
        }
        // 商家备注
        req.setMerchantRemark(StringUtils.isBlank(merchantRemark) ? StringUtils.EMPTY: merchantRemark);
        ReserveInfo collectedReserveInfo = reserveInfo.getCollectedReserveInfo();
        if (collectedReserveInfo != null) {
            // 选择数量
            req.setQuantity(ReserveInfoProcessUtils.getIntItem(collectedReserveInfo, ReserveItemType.PERSON_NUM.getKey()));
            // 项目code
            req.setProjectCode(ReserveInfoProcessUtils.getIntItem(collectedReserveInfo, ReserveItemType.PROJECT_CODE.getKey()));
            // 项目名称
            req.setProjectName(ReserveInfoProcessUtils.getStringItem(collectedReserveInfo, ReserveItemType.PROJECT.getKey()));
            // 用户备注
            req.setUserRemark(collectedReserveInfo.getRemark());
            // 开始时间
            String startTime = ReserveInfoProcessUtils.getStringItem(collectedReserveInfo, ReserveItemType.START_TIME.getKey());
            req.setArrivalTime(TimeUtil.convertStr2MillSeconds(startTime));
            // 离店时间
            req.setLeaveTime(ReserveInfoProcessUtils.getLeaveTime(collectedReserveInfo, startTime));
            if(needStock) {
                // purchaseStockDate 在线系统扣真实库存需要传，用于跨天库存的扣减，AI外呼不传
                req.setPurchaseStockDate(Lists.newArrayList(computePurchaseStockDate(startTime, availableTimeSliceStocks)));
            }
            // 用户手机号
            req.setContactMobileNo(collectedReserveInfo.getPhone());
        }
        return req;
    }

    private boolean isSkuIdRequired(ShopReserveContext shopReserveContext) {
        //目前仅有酒吧下单强依赖SKU ID
        return shopReserveContext.getPoiIndustryType() == POIIndustryType.BAR;
    }

    private Long computePurchaseStockDate(String startTime, List<TimeSliceStockInfo> availableTimeSliceStocks) {
        LocalDateTime startTimeStamp = TimeUtil.convertStr2LocalDateTime(startTime, TimeUtil.FORMAT);
        String startDay = TimeUtil.convertLocalDateTime2Str(startTimeStamp, TimeUtil.FORMAT_DAY);
        if(CollectionUtils.isEmpty(availableTimeSliceStocks)) {
            return TimeUtil.convertStr2MillSeconds(startDay);
        }
        // 只用看结束时间（最后一个时间片）
        TimeSliceStockInfo lastTimeSliceStockInfo = availableTimeSliceStocks.get(availableTimeSliceStocks.size() - 1);
        String purchaseStockDate = lastTimeSliceStockInfo.getPurchaseStockDate();
        return TimeUtil.convertStr2MillSeconds(purchaseStockDate);
    }

    private boolean isAfterOrder(UserReserveInfo reserveInfo) {
        if (reserveInfo == null) {
            return false;
        }
        return reserveInfo.getOrderId() != null && reserveInfo.getOrderId() > 0 && reserveInfo.getDealId() != null && reserveInfo.getDealId() > 0;
    }

    @Override
    public Response<AIPhoneCallTaskCreateRes> createAIPhoneCall(EnvContext envContext, UserModel userModel, ShopReserveContext shopReserveContext, UserReserveInfo reserveInfo, Long taskId, int platform) {
        if (userModel == null || shopReserveContext == null || reserveInfo == null || taskId == null) {
            return null;
        }
        AIPhoneCallTaskCreateReq req = buildAIPhoneCallCreateReq(envContext, userModel, shopReserveContext, reserveInfo, taskId, platform);
        return aiPhoneCallProcessService.createAIPhoneCallTask(req);
    }

    @Override
    public Response<AIPhoneCallTaskCreateRes> refundAIPhoneCall(EnvContext envContext, UserModel userModel, ShopReserveContext shopReserveContext, UserReserveInfo reserveInfo, Long taskId, int platform) {
        if (userModel == null || shopReserveContext == null || reserveInfo == null || taskId == null) {
            return null;
        }
        AIPhoneCallTaskCreateReq req = buildAIPhoneCallRefundReq(envContext, userModel, shopReserveContext, reserveInfo, taskId, platform);
        return aiPhoneCallProcessService.createAIPhoneCallTask(req);
    }

    private AIPhoneCallTaskCreateReq buildAIPhoneCallCreateReq(EnvContext envContext, UserModel userModel, ShopReserveContext shopReserveContext, UserReserveInfo reserveInfo, Long taskId, int platform) {
        AIPhoneCallTaskCreateReq req = buildBasePhoneCallReq(envContext, userModel, shopReserveContext, reserveInfo, taskId, platform);
        req.setAiPhoneCallSceneTypeEnum(AIPhoneCallSceneTypeEnum.RESERVATION);
        req.setAiPhoneCallSourceEnum(AIPhoneCallSourceEnum.MAIN_FWLS_AGENT);
        Map<String, String> dynamicParam = buildAIPhoneCallDynamicParam(reserveInfo, shopReserveContext);
        req.getAiPhoneCallDetailList().get(0).setDynamicParameterMap(dynamicParam);
        return req;
    }

    private AIPhoneCallTaskCreateReq buildAIPhoneCallRefundReq(EnvContext envContext, UserModel userModel, ShopReserveContext shopReserveContext, UserReserveInfo reserveInfo, Long taskId, int platform) {
        AIPhoneCallTaskCreateReq req = buildBasePhoneCallReq(envContext, userModel, shopReserveContext, reserveInfo, taskId, platform);
        req.setAiPhoneCallSceneTypeEnum(AIPhoneCallSceneTypeEnum.CANCEL_RESERVATION);
        req.setAiPhoneCallSourceEnum(AIPhoneCallSourceEnum.MAIN_FWLS_AGENT);
        Map<String, String> dynamicParam = buildAIPhoneCallRefundDynamicParam(reserveInfo, shopReserveContext);
        req.getAiPhoneCallDetailList().get(0).setDynamicParameterMap(dynamicParam);
        return req;
    }

    private AIPhoneCallTaskCreateReq buildBasePhoneCallReq(EnvContext envContext, UserModel userModel, ShopReserveContext shopReserveContext, UserReserveInfo reserveInfo, Long taskId, int platform) {
        AIPhoneCallTaskCreateReq req = new AIPhoneCallTaskCreateReq();
        Map<String, Object> bizData = Maps.newHashMap();
        // 业务标识（预约预订Agent）
        bizData.put(ReserveAgentPhoneCallBack.TYPE, ReserveAgentPhoneCallBack.RESERVE_AGENT);
        bizData.put(ReserveAgentPhoneCallBack.TASK_RESERVE_TYPE, reserveInfo.getTaskReserveType());
        bizData.put(ReserveAgentPhoneCallBack.ENV_CONTEXT, envContext);
        long userId = platform == PlatformEnum.DP.getType() ? userModel.getDpUserId2() : userModel.getId();
        bizData.put(ReserveAgentPhoneCallBack.USER_ID, userId);
        req.setBizData(JSON.toJSONString(bizData));
        // 0代表未填写，男1女2
        String userGender = userModel.getGender() == 0 ? "unknown" : (userModel.getGender() == 1 ? "male" : "female");
        req.setUserGender(userGender);
        req.setBizId(String.valueOf(taskId));
        req.setPlatform(platform);
        AIPhoneCallDetailCreateReq dealDetailReq = new AIPhoneCallDetailCreateReq();
        long shopId = platform == PlatformEnum.DP.getType() ? shopReserveContext.getDpShopId() : shopReserveContext.getMtShopId();
        dealDetailReq.setShopId(shopId);
        dealDetailReq.setCallPhone(shopReserveContext.getPhone());
        dealDetailReq.setSequenceId(1);
        dealDetailReq.setUserId(userId);
        if (reserveInfo.getCollectedReserveInfo() != null) {
            String formattedStartTime = ReserveInfoProcessUtils.getStringItem(reserveInfo.getCollectedReserveInfo(), ReserveItemType.START_TIME.getKey());
            if ("-1".equals(formattedStartTime) || StringUtils.isBlank(formattedStartTime)) {
                dealDetailReq.setDdlDate(DateFormatUtils.format(new Date(), DateFormatUtils.ISO_DATE_FORMAT.getPattern()));
            }
            dealDetailReq.setDdlDate(formattedStartTime);
        }
        req.setAiPhoneCallDetailList(Lists.newArrayList(dealDetailReq));
        return req;
    }

    private Map<String, String> buildAIPhoneCallDynamicParam(UserReserveInfo userReserveInfo, ShopReserveContext shopReserveContext) {
        Map<String, String> dynamicParam = Maps.newHashMap();
        if (userReserveInfo.getCollectedReserveInfo() == null) {
            return dynamicParam;
        }
        dynamicParam.put(AIPhoneCallDynamicParamEnum.SHOP_NAME.getKey(), shopReserveContext.getShopName());
        ReserveInfo collectedReserveInfo = userReserveInfo.getCollectedReserveInfo();
        String project = ReserveInfoProcessUtils.getStringItem(collectedReserveInfo, ReserveItemType.PROJECT.getKey());
        if (StringUtils.isNotBlank(project)) {
            dynamicParam.put(AIPhoneCallDynamicParamEnum.PROJECT_NAME.getKey(), project);
        }
        String formattedStartTime = ReserveInfoProcessUtils.getStringItem(collectedReserveInfo, ReserveItemType.START_TIME.getKey());
        String startTimeDescStr = convertStartTimeDescStr(collectedReserveInfo);
        if (StringUtils.isNotBlank(formattedStartTime)) {
            dynamicParam.put(AIPhoneCallDynamicParamEnum.RESERVATION_DATE.getKey(), formattedStartTime);
            dynamicParam.put(AIPhoneCallDynamicParamEnum.RESERVATION_TIME.getKey(), startTimeDescStr);
        }
        Long duration = ReserveInfoProcessUtils.getLongItem(collectedReserveInfo, ReserveItemType.DURATION.getKey());
        if (duration != null && duration > 0) {
            dynamicParam.put(AIPhoneCallDynamicParamEnum.Duration.getKey(), String.format("%s分钟", duration));
        }
        int personNum = ReserveInfoProcessUtils.getIntItem(collectedReserveInfo, ReserveItemType.PERSON_NUM.getKey());
        if (personNum > 0) {
            dynamicParam.put(AIPhoneCallDynamicParamEnum.PEOPLE_NUMBER.getKey(), String.valueOf(personNum));
        }
        String technician = ReserveInfoProcessUtils.getStringItem(collectedReserveInfo, ReserveItemType.TECHNICIAN.getKey());
        if (StringUtils.isNotBlank(technician)) {
            dynamicParam.put(AIPhoneCallDynamicParamEnum.TECHNICIAN_NAME.getKey(), technician);
        }
        String userPhone = collectedReserveInfo.getPhone();
        if (StringUtils.isNotBlank(userPhone)) {
            dynamicParam.put(AIPhoneCallDynamicParamEnum.USER_PHONE.getKey(), userPhone);
        }
        String remark = collectedReserveInfo.getRemark();
        if (StringUtils.isNotBlank(remark)) {
            dynamicParam.put(AIPhoneCallDynamicParamEnum.REMARK.getKey(), remark);
        }
        return dynamicParam;
    }

    private String convertStartTimeDescStr(ReserveInfo collectedReserveInfo) {
        String startTimeDescStr = ReserveInfoProcessUtils.getStringItem(collectedReserveInfo, ReserveItemType.START_TIME_DESC.getKey());
        if (StringUtils.isNotBlank(startTimeDescStr)) {
            return startTimeDescStr;
        }
        LogUtils.logFailLog(log, TagContext.builder().action("AIReserveBookProcessService").build(),
                WarnMessage.build("AIReserveBookProcessService", "提取自然语言失败", ""),
                String.format("reserveInfo:%s", JSON.toJSONString(collectedReserveInfo)), false);
        return ReserveInfoProcessUtils.getRawStringItem(collectedReserveInfo, ReserveItemType.START_TIME.getKey());
    }

    private Map<String, String> buildAIPhoneCallRefundDynamicParam(UserReserveInfo userReserveInfo, ShopReserveContext shopReserveContext) {
        Map<String, String> dynamicParam = Maps.newHashMap();
        if (userReserveInfo.getCollectedReserveInfo() == null) {
            return dynamicParam;
        }
        dynamicParam.put(AIPhoneCallDynamicParamEnum.SHOP_NAME.getKey(), shopReserveContext.getShopName());
        ReserveInfo collectedReserveInfo = userReserveInfo.getCollectedReserveInfo();
        String project = ReserveInfoProcessUtils.getStringItem(collectedReserveInfo, ReserveItemType.PROJECT.getKey());
        if (StringUtils.isNotBlank(project)) {
            dynamicParam.put(AIPhoneCallDynamicParamEnum.PROJECT_NAME.getKey(), project);
        }
        String startTime = ReserveInfoProcessUtils.getStringItem(collectedReserveInfo, ReserveItemType.START_TIME.getKey());
        if (StringUtils.isNotBlank(startTime)) {
            String[] startTimeArray = startTime.split(" ");
            dynamicParam.put(AIPhoneCallDynamicParamEnum.RESERVATION_DATE.getKey(), startTimeArray[0]);
            dynamicParam.put(AIPhoneCallDynamicParamEnum.RESERVATION_TIME.getKey(), startTimeArray[1]);
        }
        String userPhone = collectedReserveInfo.getPhone();
        if (StringUtils.isNotBlank(userPhone)) {
            dynamicParam.put(AIPhoneCallDynamicParamEnum.USER_PHONE.getKey(), userPhone);
        }
        return dynamicParam;
    }

    @Override
    public Response<Boolean> refundReserve(EnvContext envContext, UserModel userModel, UserReserveInfo reserveInfo) {
        if (userModel == null || reserveInfo == null) {
            return null;
        }
        WebParamDTO webParam = agentTradeAclService.buildWebParam(userModel, envContext);
        AgentRefundApplyOrderReqDTO req = buildRefundApplyOrderReq(reserveInfo);
        Response<List<AgentRefundApplyOrderResDTO>> refundOrders = agentTradeAclService.refundApply(webParam, Lists.newArrayList(req));
        if (refundOrders == null || !refundOrders.isSuccess() || CollectionUtils.isEmpty(refundOrders.getData())) {
            log.error("[AIReserveBookProcessService] refundReserve error, req={}, resp={}", JSON.toJSONString(req), JSON.toJSONString(refundOrders));
            String msg = refundOrders == null ? StringUtils.EMPTY : refundOrders.getMsg();
            return Response.error(msg);
        }
        return Response.success();
    }

    private AgentRefundApplyOrderReqDTO buildRefundApplyOrderReq(UserReserveInfo reserveInfo) {
        AgentRefundApplyOrderReqDTO req = new AgentRefundApplyOrderReqDTO();
        req.setOrderId(reserveInfo.getReserveId());
        Integer personNum = ReserveInfoProcessUtils.getIntItem(reserveInfo.getCollectedReserveInfo(), ReserveItemType.PERSON_NUM.getKey());
        if (personNum != null && personNum > 0) {
            req.setCount(personNum);
        }
        AgentOrderType orderType = TaskReserveType.convertTaskReserveType2OrderType(reserveInfo.getTaskReserveType());
        if (orderType != null) {
            req.setOrderType(orderType.getCode());
        }
        return req;
    }

    @Override
    public MessageBizRefData<UserReserveInfo> insertMessageBizRef(UserReserveInfo userReserveInfo, ReserveStatusType reserveStatusType, Long messageId) {
        MessageBizRefData<UserReserveInfo> messageBizRefData = buildMessageBizData(userReserveInfo, reserveStatusType, messageId);
        boolean insertSuccess = pilotMessageBizRefProcessService.insertMessageBizRef(messageBizRefData);
        if (!insertSuccess) {
            return null;
        }
        return messageBizRefData;
    }

    private MessageBizRefData<UserReserveInfo> buildMessageBizData(UserReserveInfo userReserveInfo, ReserveStatusType reserveStatusType, Long messageId) {
        MessageBizRefData<UserReserveInfo> messageBizRefData = new MessageBizRefData<>();
        // 如果预约ID为空，则随机生成一个临时的ID
        if (userReserveInfo.getReserveId() == null || userReserveInfo.getReserveId() <= 0) {
            messageBizRefData.setBizId(UuidUtil.getRandomCardKey());
        } else {
            messageBizRefData.setBizId(String.valueOf(userReserveInfo.getReserveId()));
        }
        int taskReserveType = userReserveInfo.getTaskReserveType();
        MessageBizRefTypeEnum refTypeEnum = TaskReserveType.APPOINTMENT.getType() == taskReserveType ? MessageBizRefTypeEnum.APPOINTMENT : MessageBizRefTypeEnum.BOOKING;
        messageBizRefData.setBizType(refTypeEnum.getType());
        messageBizRefData.setBizExtra(userReserveInfo);
        messageBizRefData.setBizStatus(String.valueOf(reserveStatusType.getType()));
        messageBizRefData.setMessageId(messageId);
        return messageBizRefData;
    }

    private boolean updateMessageBizData(Long taskId, ReserveStatusType reserveStatusType) {
        MessageBizRefData<UserReserveInfo> messageBizRefData = pilotMessageBizRefProcessService.getMessageBizRef(taskId, UserReserveInfo.class);
        if (messageBizRefData == null) {
            return false;
        }
        messageBizRefData.setBizStatus(String.valueOf(reserveStatusType.getType()));
        return pilotMessageBizRefProcessService.updateMessageBizRef(messageBizRefData);
    }

    @Override
    public Response<MessageBizRefData<UserReserveInfo>> processOnlineCancelReserve(UserModel userModel, UserReserveInfo userReserveInfo) {
        // 1. 创建取消任务
        EnvContext envContext = PluginContextUtil.getEnvContextFromRequestContext();
        MessageBizRefData<UserReserveInfo> messageBizRefData = insertMessageBizRef(userReserveInfo, ReserveStatusType.RESERVE_CANCEL_ASKING, 0L);
        if (messageBizRefData == null || messageBizRefData.getId() == null || messageBizRefData.getId() <= 0) {
            log.error("[AIReserveBookProcessService] insertMessageBizRef error, userReserveInfo={}, messageBizRefData={}", JSON.toJSONString(userReserveInfo), JSON.toJSONString(messageBizRefData));
            return null;
        }
        // 2. 调用取消接口
        Response<Boolean> refundResult = refundReserve(envContext, userModel, userReserveInfo);
        if (refundResult == null || !refundResult.isSuccess()) {
            log.error("[AIReserveBookProcessService] refundReserve error, userReserveInfo={}, resp={}", JSON.toJSONString(userReserveInfo), JSON.toJSONString(refundResult));
            updateMessageBizData(messageBizRefData.getId(), ReserveStatusType.RESERVE_CANCEL_FAIL);
            return refundResult != null && StringUtils.isNotBlank(refundResult.getMsg()) ? Response.error(refundResult.getMsg()) : null;
        }
        return Response.success(messageBizRefData);
    }

    @Override
    public Response<MessageBizRefData<UserReserveInfo>> processPhoneCallCancelReserve(UserModel userModel, UserReserveInfo userReserveInfo, ShopReserveContext shopReserveContext) {
        // 1. 创建取消任务
        MessageBizRefData<UserReserveInfo> messageBizRefData = insertMessageBizRef(userReserveInfo, ReserveStatusType.RESERVE_CANCEL_ASKING, 0L);
        if (messageBizRefData == null || messageBizRefData.getId() == null || messageBizRefData.getId() <= 0) {
            log.error("[AIReserveBookProcessService] insertMessageBizRef error, userReserveInfo={}, messageBizRefData={}", JSON.toJSONString(userReserveInfo), JSON.toJSONString(messageBizRefData));
            return null;
        }
        AssistantSceneContext assistantSceneContext = RequestContext.getAttribute(RequestContextConstants.ASSISTANT_CONTEXT);
        int platform = getPlatform(assistantSceneContext);
        // 2. 发起AI外呼（仅通知）
        EnvContext envContext = PluginContextUtil.getEnvContextFromRequestContext();
        Response<AIPhoneCallTaskCreateRes> response = refundAIPhoneCall(envContext, userModel, shopReserveContext, userReserveInfo, messageBizRefData.getId(), platform);
        if (!response.isSuccess()) {
            log.error("[AIReserveBookProcessService] refundAIPhoneCall error, userReserveInfo={}, resp={}", JSON.toJSONString(userReserveInfo), JSON.toJSONString(response));
            updateMessageBizData(messageBizRefData.getId(), ReserveStatusType.RESERVE_CANCEL_FAIL);
            return null;
        }
        // 3. 调用取消接口
        Response<Boolean> refundResult = refundReserve(envContext, userModel, userReserveInfo);
        if (refundResult == null || !refundResult.isSuccess()) {
            log.error("[AIReserveBookProcessService] refundReserve error, userReserveInfo={}, resp={}", JSON.toJSONString(userReserveInfo), JSON.toJSONString(refundResult));
            updateMessageBizData(messageBizRefData.getId(), ReserveStatusType.RESERVE_CANCEL_FAIL);
            return refundResult != null && StringUtils.isNotBlank(refundResult.getMsg()) ? Response.error(refundResult.getMsg()) : null;
        }
        return Response.success(messageBizRefData);
    }

    @Override
    public AgentCreateOrderResDTO createBeamReserve(SendBeamMessageReq sendBeamMessageReq, UserModel userModel, ShopReserveContext shopReserveContext, UserReserveInfo reserveInfo, String merchantRemark, boolean needStock, List<TimeSliceStockInfo> availableTimeSliceStocks) {
        if (sendBeamMessageReq == null || sendBeamMessageReq.getUser_info() == null
                || userModel == null || shopReserveContext == null || reserveInfo == null || reserveInfo.getCollectedReserveInfo() == null) {
            return null;
        }
        // 发起预约/预订
        WebParamDTO webParam = agentTradeAclService.buildWebParam(sendBeamMessageReq);
        AgentGeneralBookingReqDTO req = buildGeneralBookingReq(shopReserveContext, reserveInfo, merchantRemark, needStock, availableTimeSliceStocks);
        AgentCreateOrderResDTO resp = agentTradeAclService.createGeneralBookingOrder(webParam, Lists.newArrayList(req));
        if (resp == null || resp.getMainOrderId() == null) {
            LogUtils.logFailLog(log, TagContext.builder().action("createBeamReserve").build(),
                    WarnMessage.build("AIReserveBookProcessService", "发起预约预订接口失败", ""),
                    String.format("sendBeamMessageReq:%s, shopReserveContext:%s, req:%s",
                            JSON.toJSONString(sendBeamMessageReq), JSON.toJSONString(shopReserveContext), JSON.toJSONString(req)), JSON.toJSONString(resp));
        }
        return resp;
    }

    @Override
    public Response<Boolean> refundBeamReserve(SendBeamMessageReq sendBeamMessageReq, UserReserveInfo reserveInfo) {
        if (sendBeamMessageReq == null || reserveInfo == null) {
            return null;
        }
        WebParamDTO webParam = agentTradeAclService.buildWebParam(sendBeamMessageReq);
        AgentRefundApplyOrderReqDTO req = buildRefundApplyOrderReq(reserveInfo);
        Response<List<AgentRefundApplyOrderResDTO>> refundOrders = agentTradeAclService.refundApply(webParam, Lists.newArrayList(req));
        if (refundOrders == null || !refundOrders.isSuccess() || CollectionUtils.isEmpty(refundOrders.getData())) {
            LogUtils.logFailLog(log, TagContext.builder().action("refundBeamReserve").build(),
                    WarnMessage.build("AIReserveBookProcessService", "取消预约预订接口失败", ""),
                    String.format("sendBeamMessageReq:%s, reserveInfo:%s, req:%s",
                            JSON.toJSONString(sendBeamMessageReq), JSON.toJSONString(reserveInfo), JSON.toJSONString(req)), JSON.toJSONString(refundOrders));
            String msg = refundOrders == null ? StringUtils.EMPTY : refundOrders.getMsg();
            return Response.error(msg);
        }
        return Response.success();
    }

    @Override
    public Response<AIPhoneCallTaskCreateRes> createBeamAIPhoneCall(SendBeamMessageReq sendBeamMessageReq, UserModel userModel, ShopReserveContext shopReserveContext, UserReserveInfo reserveInfo, Long taskId, int platform) {
        if (sendBeamMessageReq == null || shopReserveContext == null || reserveInfo == null || taskId == null) {
            return null;
        }
        AIPhoneCallTaskCreateReq req = buildBeamAIPhoneCallCreateReq(sendBeamMessageReq, userModel, shopReserveContext, reserveInfo, taskId, platform);
        return aiPhoneCallProcessService.createAIPhoneCallTask(req);
    }

    @Override
    public Response<AIPhoneCallTaskCreateRes> refundBeamAIPhoneCall(SendBeamMessageReq sendBeamMessageReq, UserModel userModel, ShopReserveContext shopReserveContext, UserReserveInfo reserveInfo, Long taskId, int platform) {
        if (userModel == null || shopReserveContext == null || reserveInfo == null || taskId == null) {
            return null;
        }
        AIPhoneCallTaskCreateReq req = buildBeamAIPhoneCallRefundReq(sendBeamMessageReq, userModel, shopReserveContext, reserveInfo, taskId, platform);
        return aiPhoneCallProcessService.createAIPhoneCallTask(req);
    }

    private AIPhoneCallTaskCreateReq buildBeamAIPhoneCallCreateReq(SendBeamMessageReq sendBeamMessageReq, UserModel userModel, ShopReserveContext shopReserveContext, UserReserveInfo reserveInfo, Long taskId, int platform) {
        AIPhoneCallTaskCreateReq req = buildBeamBasePhoneCallReq(sendBeamMessageReq, userModel, shopReserveContext, reserveInfo, taskId, platform);
        req.setAiPhoneCallSceneTypeEnum(AIPhoneCallSceneTypeEnum.RESERVATION);
        Map<String, String> dynamicParam = buildAIPhoneCallDynamicParam(reserveInfo, shopReserveContext);
        req.getAiPhoneCallDetailList().get(0).setDynamicParameterMap(dynamicParam);
        return req;
    }

    private AIPhoneCallTaskCreateReq buildBeamAIPhoneCallRefundReq(SendBeamMessageReq sendBeamMessageReq, UserModel userModel, ShopReserveContext shopReserveContext, UserReserveInfo reserveInfo, Long taskId, int platform) {
        AIPhoneCallTaskCreateReq req = buildBeamBasePhoneCallReq(sendBeamMessageReq, userModel, shopReserveContext, reserveInfo, taskId, platform);
        req.setAiPhoneCallSceneTypeEnum(AIPhoneCallSceneTypeEnum.CANCEL_RESERVATION);
        Map<String, String> dynamicParam = buildAIPhoneCallRefundDynamicParam(reserveInfo, shopReserveContext);
        req.getAiPhoneCallDetailList().get(0).setDynamicParameterMap(dynamicParam);
        return req;
    }

    private AIPhoneCallTaskCreateReq buildBeamBasePhoneCallReq(SendBeamMessageReq sendBeamMessageReq, UserModel userModel, ShopReserveContext shopReserveContext, UserReserveInfo reserveInfo, Long taskId, int platform) {
        long shopId = platform == PlatformEnum.DP.getType() ? shopReserveContext.getDpShopId() : shopReserveContext.getMtShopId();
        long userId = platform == PlatformEnum.DP.getType() ? userModel.getDpUserId2() : userModel.getId();

        Map<String, Object> bizData = Maps.newHashMap();
        // 业务标识（预约预订Agent）
        bizData.put(BeamReserveAgentPhoneCallBack.TYPE, BeamReserveAgentPhoneCallBack.BEAM_RESERVE_AGENT);
        bizData.put(BeamReserveAgentPhoneCallBack.TASK_RESERVE_TYPE, reserveInfo.getTaskReserveType());
        bizData.put(BeamReserveAgentPhoneCallBack.SEND_BEAM_MESSAGE_REQ_USER_INFO, JSON.toJSONString(sendBeamMessageReq.getUser_info()));
        bizData.put(BeamReserveAgentPhoneCallBack.PLATFORM, platform);
        bizData.put(BeamReserveAgentPhoneCallBack.SHOP_ID, shopId);
        bizData.put(BeamReserveAgentPhoneCallBack.USER_ID, userId);
        bizData.put(BeamReserveAgentPhoneCallBack.IM_USER_ID, reserveInfo.getImUserId());

        AIPhoneCallTaskCreateReq req = new AIPhoneCallTaskCreateReq();
        req.setBizData(JSON.toJSONString(bizData));
        // 0代表未填写，男1女2
        String userGender = userModel.getGender() == 0 ? "unknown" : (userModel.getGender() == 1 ? "male" : "female");
        req.setUserGender(userGender);
        req.setBizId(String.valueOf(taskId));
        req.setPlatform(platform);
        req.setAiPhoneCallSourceEnum(AIPhoneCallSourceEnum.BEAM_FWLS_AGENT);
        AIPhoneCallDetailCreateReq dealDetailReq = new AIPhoneCallDetailCreateReq();
        dealDetailReq.setShopId(shopId);
        dealDetailReq.setCallPhone(shopReserveContext.getPhone());
        dealDetailReq.setSequenceId(1);
        dealDetailReq.setUserId(userId);
        if (reserveInfo.getCollectedReserveInfo() != null) {
            String formattedStartTime = ReserveInfoProcessUtils.getStringItem(reserveInfo.getCollectedReserveInfo(), ReserveItemType.START_TIME.getKey());
            dealDetailReq.setDdlDate(formattedStartTime);
        }
        req.setAiPhoneCallDetailList(Lists.newArrayList(dealDetailReq));
        return req;
    }

    private int getPlatform(AssistantSceneContext context) {
        return context.getUserId().startsWith(ImUserType.MT.getPrefix()) ? PlatformEnum.MT.getType() : PlatformEnum.DP.getType();
    }
}
