package com.sankuai.dzim.pilot.process.aireservebook.data;

import com.sankuai.dzim.pilot.buffer.stream.build.ext.data.ProductCardData;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: wu<PERSON><PERSON><PERSON>
 * @create: 2025-04-25
 * @description: 预约成功数据
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BookSuccData {
    /**
     * 预约成功说明
     */
    private String title = "预约成功啦";

    /**
     * 门店ID
     */
    private Long shopId;

    /**
     * 预约ID
     */
    private Long reserveId;

    /**
     * 预约详情页跳链
     */
    private String url;

    /**
     * 文案
     */
    private String text;

    /**
     * 团购推荐（预订没有）
     */
    private String productRecommend;

    /**
     * 见商品对象productCardData（预订没有）
     */
    private ProductCardData productInfo;

    private Boolean isGray;
}
