package com.sankuai.dzim.pilot.process.strategy.riskuserblock;

import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.sankuai.dzim.message.dto.AuditResDTO;
import com.sankuai.dzim.pilot.dal.entity.pilot.BlockListEntity;
import com.sankuai.dzim.pilot.domain.RiskUserBlockDomainService;
import com.sankuai.dzim.pilot.gateway.enums.BlockTypeEnum;
import com.sankuai.dzim.pilot.gateway.mq.data.BlockStrategyConstant;
import com.sankuai.dzim.pilot.process.UserBlockProcessService;
import com.sankuai.dzim.pilot.process.data.BlockStrategyConfig;
import com.sankuai.dzim.pilot.gateway.mq.data.RiskBlockMessageData;
import com.sankuai.dzim.pilot.process.strategy.RiskUserBlockStrategy;
import com.sankuai.dzim.pilot.utils.LionConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class AuditBlockStrategy implements RiskUserBlockStrategy {

    @Resource(name = "redisClient")
    private RedisStoreClient dzimRedisClient;

    @Autowired
    private LionConfigUtil lionConfigUtil;

    @Autowired
    private UserBlockProcessService userBlockProcessService;


    @Override
    public boolean accept(String name) {
        return name.equals(BlockTypeEnum.AUDIT_STRATEGY.getBlockStrategy());
    }

    @Override
    public void execute(BlockStrategyConfig strategyConfig, RiskBlockMessageData riskMsg) {
        AuditResDTO audit = riskMsg.getAudit();
        if (audit != null && audit.isPass()) {
            return;
        }
        StoreKey storeKey = buildKey(riskMsg.getReq().getAssistantType(),
                riskMsg.getReq().getImUserId());
        userBlockProcessService.blockUser(storeKey, strategyConfig, riskMsg, BlockTypeEnum.AUDIT_STRATEGY);
    }

    @Override
    public StoreKey buildKey(Integer assistantType, String userId) {
        return new StoreKey(BlockStrategyConstant.RISK_USER_BLOCK_KEY, BlockTypeEnum.AUDIT_STRATEGY.getBlockStrategy(), assistantType, userId);
    }
}
