package com.sankuai.dzim.pilot.process.localplugin.param;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import lombok.Data;

@Data
public class ProductDetailParam extends Param {

    @JsonPropertyDescription("需要查询的门店Id,门店Id请从消息中获取,不要主动向用户询问id,没有或者用户表示不限制具体门店可以不需要")
    @JsonProperty(required = false)
    private long shopId;

    @JsonPropertyDescription("需要查询的名称,有门店id优先使用门店id,没有门店id再使用门店名称,门店名称请从消息中获取,没有或者用户表示不限制具体门店可以不需要")
    @JsonProperty(required = false)
    private String shopName;

    @JsonPropertyDescription("表示你要查询商品的哪些类型的问题,可选项有[\"baseInfo\",\"review\"],各可选项的含义如下:\n" +
            "baseInfo:表示商品的基础信息,如:价格、优惠、包含项目、购买预约规则等等\n" +
            "review:表示商品的评价信息,如好评率、差评率、具体评价内容等,当问题是'xxx商品怎么样'、'商品效果好不好'的时候可以使用")
    @JsonProperty(required = true)
    private String queryType;

    @JsonPropertyDescription("与商品相关的具体问题,越具体越好,最好是一个问句,例如“这个套餐包含哪些服务项目”、“这个可以退款吗”等")
    @JsonProperty(required = true)
    private String question;

    @JsonPropertyDescription("表示用户咨询具体的商品Id,当用户在咨询具体的baseInfo的时候必须要有这个参数")
    @JsonProperty(required = false)
    private long productId;
}
