package com.sankuai.dzim.pilot.utils;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class StringInnerUtils {

    /**
     * 替换{{key}}占位符
     * @param template
     * @param params
     * @return
     */
    public static String replaceTemplate(String template, Map<String, String> params) {
        if (StringUtils.isEmpty(template) || MapUtils.isEmpty(params)) {
            return template;
        }

        Pattern pattern = Pattern.compile("\\{\\{(\\w+)}}");
        Matcher matcher = pattern.matcher(template);
        StringBuffer result = new StringBuffer();

        while (matcher.find()) {
            String key = matcher.group(1);
            String replacement = params.getOrDefault(key, StringUtils.EMPTY);
            matcher.appendReplacement(result, replacement);
        }
        matcher.appendTail(result);

        return result.toString();
    }
}
