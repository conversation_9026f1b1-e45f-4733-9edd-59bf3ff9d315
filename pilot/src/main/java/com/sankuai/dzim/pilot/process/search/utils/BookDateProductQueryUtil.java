package com.sankuai.dzim.pilot.process.search.utils;

import com.google.common.collect.Lists;
import com.sankuai.dzim.pilot.process.search.data.ReserveProductM;
import com.sankuai.dzim.pilot.process.search.data.ReserveQueryContext;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 泛商品预订商品查询工具类
 */
public class BookDateProductQueryUtil {

    private static ReserveProductM buildProduct(DealGroupDTO item) {
        ReserveProductM productM = new ReserveProductM();
        productM.setPlatformProductId(item.getMtDealGroupId());
        productM.setBizProductId(Optional.ofNullable(item.getBizProductId()).map(Long::intValue).orElse(null));
        return productM;
    }

    public static List<ReserveProductM> buildProductGroupM(ReserveQueryContext context,
            List<DealGroupDTO> onlineDealGroups) {
        if (CollectionUtils.isEmpty(onlineDealGroups)) {
            return Lists.newArrayList();
        }
        return onlineDealGroups.stream().filter(Objects::nonNull).map(item -> buildProduct(item))
                .collect(Collectors.toList());
    }
}
