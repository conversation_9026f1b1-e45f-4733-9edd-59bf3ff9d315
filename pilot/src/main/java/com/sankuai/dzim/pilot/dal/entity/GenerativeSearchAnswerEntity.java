package com.sankuai.dzim.pilot.dal.entity;

import lombok.Data;

import java.util.Date;

/**
 * @author: zhouyibing
 * @date: 2024/5/24
 */
@Data
public class GenerativeSearchAnswerEntity {

    /**
     * 自增主键
     */
    private Long id;

    /**
     * 搜索问题
     */
    private String question;

    /**
     * 问题类型
     */
    private int questionType;

    /**
     * 业务类型
     */
    private Integer bizType;

    /**
     * 大模型回答
     */
    private String answer;

    /**
     * 统一收敛模板数据
     */
    private String templateData;

    /**
     * 关联项目信息和推荐问题
     */
    private String relatedQuestion;

    /**
     * 是否有效，1-有效，0无效
     */
    private Integer status;

    /**
     * 添加时间
     */
    private Date addTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}
