package com.sankuai.dzim.pilot.process.router.impl;

import com.sankuai.dzim.pilot.api.data.search.generative.SuggestionAnswerDTO;
import com.sankuai.dzim.pilot.api.data.search.generative.TabInfoDTO;
import com.sankuai.dzim.pilot.api.data.search.generative.TemplateDataDTO;
import com.sankuai.dzim.pilot.api.enums.search.generative.GenerativeSearchAnswerTemplateTypeEnum;
import com.sankuai.dzim.pilot.process.router.TemplateDataProcessStrategy;
import com.sankuai.dzim.pilot.process.router.data.RouteResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * 移除相关问题
 * 下面的项目tab补全跳链、移除searchWord、移除”全部“tab
 * <AUTHOR>
 * @since 2024/09/29 17:24
 */
@Component
public class MidScienceProcessStrategy implements TemplateDataProcessStrategy {
    @Autowired
    private CommonTemplateDataProcess commonTemplateDataProcess;

    @Override
    public boolean match(RouteResponse routeResponse)  {
        return GenerativeSearchAnswerTemplateTypeEnum.UNIFIED_MID_SCIENCE.getType() == routeResponse.getTemplateType();
    }

    @Override
    public void process(RouteResponse routeResponse) {
        Optional.ofNullable(routeResponse).map(RouteResponse::getSuggestionAnswerDTO)
                .map(SuggestionAnswerDTO::getTemplateDataDTO).map(TemplateDataDTO::getTabInfo)
                .ifPresent(tabInfos -> processTabInfo(tabInfos, routeResponse.getBizType()));
        commonTemplateDataProcess.randomProjectTabIfNeed(routeResponse);
    }

    private void processTabInfo(List<TabInfoDTO> tabInfoDTOs, int bizType) {
        tabInfoDTOs.forEach(tabInfoDTO -> {
            tabInfoDTO.setRelatedQuestions(null);
            commonTemplateDataProcess.fillProjectTabsLinkAndRemoveSearchWord(tabInfoDTO.getInnerProjectTabs(), bizType);
            tabInfoDTO.setOuterProjectTabs(null);
        });
    }
}
