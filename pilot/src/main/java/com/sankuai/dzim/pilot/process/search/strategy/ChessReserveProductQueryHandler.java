package com.sankuai.dzim.pilot.process.search.strategy;

import com.dianping.vc.sdk.lang.NumberUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.dzim.pilot.process.aireservebook.enums.POIIndustryType;
import com.sankuai.dzim.pilot.process.data.ProductDetailData;
import com.sankuai.dzim.pilot.process.data.ProductRetrievalData;
import com.sankuai.dzim.pilot.process.data.ProductStockData;
import com.sankuai.dzim.pilot.process.data.ShopPackageData;
import com.sankuai.dzim.pilot.process.search.data.ProductSaleM;
import com.sankuai.dzim.pilot.process.search.data.ReserveProductM;
import com.sankuai.dzim.pilot.process.search.data.ReserveQueryContext;
import com.sankuai.dzim.pilot.process.search.data.TimeSliceM;
import com.sankuai.dzim.pilot.process.search.enums.IndustryBackendCategoryEnum;
import com.sankuai.dzim.pilot.process.search.enums.PlatformCategoryEnum;
import com.sankuai.dzim.pilot.process.search.strategy.padding.ReserveProductPaddingFactory;
import com.sankuai.dzim.pilot.process.search.strategy.padding.ReserveProductPaddingHandler;
import com.sankuai.dzim.pilot.process.search.strategy.recall.ReserveProductRecallFactory;
import com.sankuai.dzim.pilot.process.search.strategy.recall.ReserveProductRecallHandler;
import com.sankuai.dzim.pilot.process.search.utils.GenericSpaceUtil;
import com.sankuai.dztheme.generalproduct.enums.GeneralProductAttrEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.joda.time.DateTime;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class ChessReserveProductQueryHandler implements ReserveProductQueryHandler {

    private static final List<Integer> CHESS_CATEGORY_IDS = IndustryBackendCategoryEnum.CHESS.getCategoryIds();
    private final String CHESS_PLAN_ID = "10101117";
    private final String GROUP_PRODUCTS = "groupProducts";

    @Resource
    private ReserveProductRecallFactory reserveProductRecallFactory;

    @Resource
    private ReserveProductPaddingFactory reserveProductPaddingFactory;

    @Override
    public boolean match(ReserveQueryContext queryContext) {
        if (queryContext == null || queryContext.getShopBackCategoryId() <= 0) {
            return false;
        }
        return CHESS_CATEGORY_IDS.contains(queryContext.getShopBackCategoryId());
    }

    @Override
    public List<ProductRetrievalData> execute(ReserveQueryContext queryContext) {

        // 加入行业个性化上下文参数
        ReserveQueryContext ctx = fillSpecialContext(queryContext);

        // 根据poiId召回商品id
        List<ReserveProductM> recallProducts = queryShopProductIds(ctx);

        // 根据商品id列表填充商品信息
        List<ReserveProductM> paddingProducts = paddingProductInfo(recallProducts, ctx);

        // 商品分组聚合成展示商品
        List<ReserveProductM> groupProducts = reorganization(paddingProducts);

        // 构建门店预订商品返回结果
        return buildProductRetrievalDataList(queryContext, groupProducts);
    }

    private List<ReserveProductM> paddingProductInfo(List<ReserveProductM> recallProducts, ReserveQueryContext ctx) {
        ReserveProductPaddingHandler handler = reserveProductPaddingFactory.getHandler(ctx);
        if (handler == null) {
            return Lists.newArrayList();
        }
        return handler.padding(recallProducts, ctx);
    }

    /**
     * 对门店所有在线商品进行分组，分组要素：时段周几+套餐+包型
     */
    private List<ReserveProductM> reorganization(List<ReserveProductM> paddingProducts) {
        if (CollectionUtils.isEmpty(paddingProducts)) {
            return Lists.newArrayList();
        }

        // 按照指定属性进行分组
        return paddingProducts.stream().collect(Collectors.groupingBy(product -> {
            String roomName = product.getAttr(GeneralProductAttrEnum.ATTR_RESOURCE_ROOM_NAME.getKey());
            String dayOfWeek = product.getAttr(GeneralProductAttrEnum.ATTR_RESOURCE_PERIOD_DAY_OF_WEEK.getKey());
            String packageName = product.getAttr(GeneralProductAttrEnum.ATTR_RESOURCE_PACKAGE_NAME.getKey());
            return roomName + "_" + dayOfWeek + "_" + packageName;
        })).values().stream().map(this::mergeProduct).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private ReserveProductM mergeProduct(List<ReserveProductM> groupProducts) {
        if (CollectionUtils.isEmpty(groupProducts)) {
            return null;
        }

        // 找到价格最低的商品
        ReserveProductM minPriceProduct = groupProducts.stream().min((p1, p2) -> {
            if (StringUtils.isEmpty(p1.getSalePriceTag()) || StringUtils.isEmpty(p2.getSalePriceTag())) {
                return 0;
            }
            BigDecimal price1 = new BigDecimal(p1.getSalePriceTag());
            BigDecimal price2 = new BigDecimal(p2.getSalePriceTag());
            return price1.compareTo(price2);
        }).orElse(null);

        if (minPriceProduct == null) {
            return null;
        }

        List<String> salePrices = groupProducts.stream().filter(e -> StringUtils.isNotBlank(e.getSalePriceTag()))
                .map(ReserveProductM::getSalePriceTag).sorted((p1, p2) -> {
                    BigDecimal price1 = new BigDecimal(p1);
                    BigDecimal price2 = new BigDecimal(p2);
                    return price1.compareTo(price2);
                }).collect(Collectors.toList());

        String salePrice = salePrices.size() > 1 ? salePrices.get(0) + "起" : salePrices.get(0);
        minPriceProduct.setSalePriceTag(salePrice);

        // 新建商品并复制最低价格商品的数据
        ReserveProductM mergedProduct = copyProduct(minPriceProduct);

        // 将groupProducts放入新建商品的属性中
        mergedProduct.setObjAttr("groupProducts", groupProducts);

        return mergedProduct;
    }

    public ReserveProductM copyProduct(ReserveProductM sourceProduct) {
        if (sourceProduct == null) {
            return null;
        }

        ReserveProductM targetProduct = new ReserveProductM();
        targetProduct.setPlatformProductId(sourceProduct.getPlatformProductId());
        targetProduct.setSalePriceTag(sourceProduct.getSalePriceTag());
        return targetProduct;
    }

    private List<ProductRetrievalData> buildProductRetrievalDataList(ReserveQueryContext queryContext,
            List<ReserveProductM> paddingProducts) {
        if (CollectionUtils.isEmpty(paddingProducts)) {
            return Lists.newArrayList();
        }
        return paddingProducts.stream().map(reserveProductM -> buildProductRetrievalData(queryContext, reserveProductM))
                .collect(Collectors.toList());
    }

    private ProductRetrievalData buildProductRetrievalData(ReserveQueryContext queryContext,
            ReserveProductM reserveProductM) {
        ProductRetrievalData productRetrievalData = new ProductRetrievalData();
        productRetrievalData.setProductType(2);
        productRetrievalData.setCategoryId((long)POIIndustryType.CHESS.getSecondBackCategoryId());
        productRetrievalData.setMtProductId(reserveProductM.getPlatformProductId());
        productRetrievalData.setRelatedMtProductIds(buildRelatedMtProductIds(reserveProductM));
        productRetrievalData.setTitle(buildTitle(reserveProductM));
        productRetrievalData.setSubTitles(buildSubTitles(reserveProductM));
        productRetrievalData.setSalesNum(buildSalesNum(reserveProductM));
        productRetrievalData.setSalePrice(reserveProductM.getSalePriceTag());
        productRetrievalData.setStocks(buildStocks(reserveProductM));
        productRetrievalData.setProductDetails(buildProductDetails(reserveProductM));
        return productRetrievalData;
    }

    private ProductDetailData buildProductDetails(ReserveProductM reserveProductM) {
        ReserveProductM firstGroupProduct = getFirstGroupProduct(reserveProductM);

        Map<String, Object> packageName2Content = getShopPackageData(firstGroupProduct);
        Map<String, Object> roomData = getShopRoomData(firstGroupProduct);
        ProductDetailData productDetailData = new ProductDetailData();
        productDetailData.setPackageInfo(packageName2Content);
        productDetailData.setRoomInfo(roomData);
        return productDetailData;
    }

    private Map<String, Object> getShopRoomData(ReserveProductM firstGroupProduct) {
        Map<String, Object> roomInfo = new HashMap<>();
        roomInfo.put("座椅类型", firstGroupProduct.getAttr("resource_attr_chess_card_area_tableAndChairInformation"));
        roomInfo.put("面积", firstGroupProduct.getAttr("resource_attr_chess_card_area_space"));
        roomInfo.put("麻将桌新旧", firstGroupProduct.getAttr("resource_attr_chess_card_area_mahjongNewOld"));
        roomInfo.put("是否有茶台", firstGroupProduct.getAttr("resource_attr_chess_card_area_hasTeaDesk"));
        roomInfo.put("是否有独卫", firstGroupProduct.getAttr("resource_attr_chess_card_area_hasIndependentWashRoom"));
        roomInfo.put("便利设施", firstGroupProduct.getAttr("resource_attr_chess_card_area_convenienceFacilities"));
        roomInfo.put("通风设施", firstGroupProduct.getAttr("resource_attr_chess_card_area_ventilationFacilities"));
        roomInfo.put("冷暖设施", firstGroupProduct.getAttr("resource_attr_chess_card_area_heatAndCoolFacilities"));
        roomInfo.put("房间风格", firstGroupProduct.getAttr("resource_attr_chess_card_area_areaStyle"));
        roomInfo.put("麻将类型", firstGroupProduct.getAttr("resource_attr_chess_card_area_mahjongType"));
        return roomInfo;
    }

    private Map<String, Object> getShopPackageData(ReserveProductM firstGroupProduct) {
        List<ShopPackageData> shopPackageDataList = Lists.newArrayList();
        String packageName = firstGroupProduct.getAttr(GeneralProductAttrEnum.ATTR_RESOURCE_PACKAGE_NAME.getKey());
        String packageContent = firstGroupProduct.getAttr("resource_attr_chess_card_package_groupGoodsContent");
        ShopPackageData shopPackageData = buildShopPackageContent(packageName, packageContent);
        shopPackageDataList.add(shopPackageData);
        return shopPackageDataList.stream()
                .collect(Collectors.toMap(ShopPackageData::getPackageName, Function.identity()));
    }

    private ShopPackageData buildShopPackageContent(String packageName, String packageContent) {
        ShopPackageData shopPackageData = new ShopPackageData();
        shopPackageData.setPackageName(packageName);
        shopPackageData.setPackageContent(packageContent);
        return shopPackageData;
    }

    private List<Long> buildRelatedMtProductIds(ReserveProductM reserveProductM) {
        List<ReserveProductM> allGroupProduct = getAllGroupProduct(reserveProductM);
        if (CollectionUtils.isEmpty(allGroupProduct)) {
            return null;
        }
        return allGroupProduct.stream().map(ReserveProductM::getPlatformProductId).filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 主商品下任意一个套餐时间片段可订，那主商品在这个套餐就是可订的
     */
    public ProductStockData buildStocks(ReserveProductM reserveProductM) {
        List<ReserveProductM> allGroupProduct = getAllGroupProduct(reserveProductM);
        if (CollectionUtils.isEmpty(allGroupProduct)) {
            return null;
        }
        ProductStockData productStockData = new ProductStockData();

        // 预处理库存
        preHandleStock(reserveProductM, allGroupProduct);

        // 获取所有时间片段
        Map<Long, TimeSliceM> timeSliceMap = allGroupProduct.stream()
                .filter(e -> e.getBaseState() != null && e.getBaseState().getTimeSlices() != null)
                .flatMap(product -> product.getBaseState().getTimeSlices().stream())
                .collect(Collectors.toMap(TimeSliceM::getStartTime, Function.identity(), (a, b) -> {
                    if (a.isAvailable() || b.isAvailable()) {
                        return buildTimeSliceM(a.getStartTime(), true);
                    } else {
                        return buildTimeSliceM(a.getStartTime(), false);
                    }
                }));

        List<TimeSliceM> sortedTimeSlices = timeSliceMap.values().stream()
                .sorted(Comparator.comparing(TimeSliceM::getStartTime)).collect(Collectors.toList());

        productStockData.setTimeSlices(sortedTimeSlices);

        // 设置最早可订时间
        productStockData.setEarliestBookableTime(sortedTimeSlices.stream().filter(TimeSliceM::isAvailable)
                .map(TimeSliceM::getStartTime).findFirst().orElse(null));

        return productStockData;
    }

    private void preHandleStock(ReserveProductM reserveProductM, List<ReserveProductM> allGroupProduct) {
        allGroupProduct.forEach(productM -> {
            if (productM.getBaseState() == null) {
                return;
            }
            if (productM.getBaseState().getTimeSlices() == null) {
                return;
            }
            List<TimeSliceM> timeSlices = productM.getBaseState().getTimeSlices();
            if (CollectionUtils.isEmpty(timeSlices)) {
                return;
            }
            // 棋牌预订商品的库存开始时间是日期0点，转化成商品上的时段开始时间和结束时间
            convertActualPeriod(reserveProductM, timeSlices);
            productM.getBaseState().setTimeSlices(timeSlices);
        });
    }

    private void convertActualPeriod(ReserveProductM reserveProductM, List<TimeSliceM> timeSlices) {
        Long bookDateStartTime = timeSlices.get(0).getStartTime();
        ReserveProductM firstGroupProduct = getFirstGroupProduct(reserveProductM);
        String periodBeginStr = firstGroupProduct
                .getAttr(GeneralProductAttrEnum.ATTR_RESOURCE_PERIOD_BEGIN_TIME.getKey());
        String periodEndStr = firstGroupProduct.getAttr(GeneralProductAttrEnum.ATTR_RESOURCE_PERIOD_END_TIME.getKey());
        int crossDay = NumberUtils
                .toInt(firstGroupProduct.getAttr(GeneralProductAttrEnum.ATTR_RESOURCE_PERIOD_NEXT_DAY.getKey()));
        Long actualBeginTime = buildActualBeginTime(bookDateStartTime, periodBeginStr, periodEndStr, crossDay);
        Long actualEndTime = buildActualEndTime(bookDateStartTime, periodEndStr, crossDay);

        timeSlices.forEach(e -> {
            e.setStartTime(actualBeginTime);
            e.setEndTime(actualEndTime);
        });
    }

    private Long buildActualBeginTime(Long bookDateStartTime, String periodBeginStr, String periodEndStr,
            int crossDay) {
        if (crossDay == 1 && periodBeginStr.compareTo(periodEndStr) < 0) {
            DateTime dateTime = new DateTime(bookDateStartTime).withTimeAtStartOfDay().plusDays(1);
            return GenericSpaceUtil.combineDateAndTime(dateTime.getMillis(), periodBeginStr).getTime();
        }
        DateTime dateTime = new DateTime(bookDateStartTime).withTimeAtStartOfDay();
        return GenericSpaceUtil.combineDateAndTime(dateTime.getMillis(), periodBeginStr).getTime();
    }

    private Long buildActualEndTime(Long bookDateStartTime, String periodEndStr, int crossDay) {
        if (crossDay == 1) {
            DateTime dateTime = new DateTime(bookDateStartTime).withTimeAtStartOfDay().plusDays(1);
            return GenericSpaceUtil.combineDateAndTime(dateTime.getMillis(), periodEndStr).getTime();
        }
        DateTime dateTime = new DateTime(bookDateStartTime).withTimeAtStartOfDay();
        return GenericSpaceUtil.combineDateAndTime(dateTime.getMillis(), periodEndStr).getTime();
    }

    private TimeSliceM buildTimeSliceM(Long startTime, boolean available) {
        TimeSliceM timeSliceM = new TimeSliceM();
        timeSliceM.setStartTime(startTime);
        timeSliceM.setAvailable(available);
        return timeSliceM;
    }

    /**
     * 取下挂商品的销量和
     */
    private Long buildSalesNum(ReserveProductM reserveProductM) {
        List<ReserveProductM> groupProducts = getAllGroupProduct(reserveProductM);
        if (CollectionUtils.isEmpty(groupProducts)) {
            return null;
        }
        return groupProducts.stream().map(ReserveProductM::getSale).filter(Objects::nonNull).map(ProductSaleM::getSale)
                .mapToLong(Integer::longValue).sum();
    }

    private List<String> buildSubTitles(ReserveProductM reserveProductM) {
        ReserveProductM firstGroupProduct = getFirstGroupProduct(reserveProductM);
        if (firstGroupProduct == null || CollectionUtils.isEmpty(firstGroupProduct.getProductTags())) {
            return null;
        }
        return firstGroupProduct.getProductTags().stream().filter(StringUtils::isNotBlank).limit(4)
                .collect(Collectors.toList());
    }

    private String buildTitle(ReserveProductM reserveProductM) {
        if (reserveProductM == null) {
            return null;
        }
        ReserveProductM hangProductM = getFirstGroupProduct(reserveProductM);
        if (hangProductM == null) {
            return null;
        }
        String roomName = hangProductM.getAttr(GeneralProductAttrEnum.ATTR_RESOURCE_ROOM_NAME.getKey());  // 房型名称
        if (StringUtils.isNotEmpty(roomName) && roomName.endsWith("·")) {
            roomName = roomName.substring(0, roomName.length() - 1);
        }
        if (StringUtils.isEmpty(roomName)) {
            roomName = "";
        }
        List<ReserveProductM> allGroupProduct = getAllGroupProduct(reserveProductM);
        // 获取最小畅玩时长
        List<Integer> durations = allGroupProduct.stream().map(product -> {
            String curDuration = product.getAttr(GeneralProductAttrEnum.ATTR_RESOURCE_PACKAGE_DURATION.getKey());
            return NumberUtils.toInt(curDuration);
        }).distinct().sorted().collect(Collectors.toList());

        return roomName + "，" + (durations.size() > 1 ? durations.get(0) + "小时起订" : durations.get(0) + "小时");
    }

    private ReserveProductM getFirstGroupProduct(ReserveProductM reserveProductM) {
        if (reserveProductM == null) {
            return null;
        }
        List<ReserveProductM> groupProducts = reserveProductM.getObjAttr(GROUP_PRODUCTS);

        if (CollectionUtils.isEmpty(groupProducts)) {
            return null;
        }
        return groupProducts.get(0);
    }

    private List<ReserveProductM> getAllGroupProduct(ReserveProductM reserveProductM) {
        if (reserveProductM == null) {
            return null;
        }
        List<ReserveProductM> groupProducts = reserveProductM.getObjAttr(GROUP_PRODUCTS);

        if (CollectionUtils.isEmpty(groupProducts)) {
            return null;
        }
        return groupProducts;
    }

    private ReserveQueryContext fillSpecialContext(ReserveQueryContext queryContext) {
        if (queryContext == null) {
            return null;
        }
        // 泛商品主题方案id
        queryContext.setPlanId(CHESS_PLAN_ID);
        queryContext.setPlatformCategoryId(PlatformCategoryEnum.CHESS.getCode());
        queryContext.setCustomPaddingParam(buildCustomPaddingParam());
        queryContext.setExtra(buildExtra());
        return queryContext;
    }

    private Map<String, Object> buildExtra() {
        Map<String, Object> extra = Maps.newHashMap();
        extra.put("dealGroupAttrs", Sets.newHashSet("resource_attr_chess_card_period_dayOfWeek"));
        return extra;
    }

    private Map<String, Object> buildCustomPaddingParam() {
        Map<String, Object> customPaddingParam = Maps.newHashMap();
        customPaddingParam.put("saleFetcherCode", "PackageSaleOpt");
        return customPaddingParam;
    }

    private List<ReserveProductM> queryShopProductIds(ReserveQueryContext ctx) {
        ReserveProductRecallHandler handler = reserveProductRecallFactory.getHandler(ctx);
        if (handler == null) {
            return Lists.newArrayList();
        }
        return handler.recall(ctx);
    }
}
