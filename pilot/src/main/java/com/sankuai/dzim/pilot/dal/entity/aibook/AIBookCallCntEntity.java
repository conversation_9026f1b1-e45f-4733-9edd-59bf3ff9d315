package com.sankuai.dzim.pilot.dal.entity.aibook;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2025/2/12 15:07
 */
@Data
public class AIBookCallCntEntity {

    /**
     * 自增主键
     */
    private Long id;

    /**
     * 记录日期
     */
    private String logDate;

    /**
     * 美团城市ID
     */
    private Integer mtCityId;

    /**
     * 美团城市名
     */
    private String mtCityName;

    /**
     * 美团行政区ID
     */
    private Integer mtDistrictId;

    /**
     * 美团行政区名称
     */
    private String mtDistrictName;

    /**
     * 美团商圈ID
     */
    private Integer mtRegionId;

    /**
     * 美团商圈名称
     */
    private String mtRegionName;

    /**
     * 平均通话数量
     */
    private String callCnt;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date addTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
