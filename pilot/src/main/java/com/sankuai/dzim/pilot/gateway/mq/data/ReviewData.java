package com.sankuai.dzim.pilot.gateway.mq.data;

import com.sankuai.dzim.pilot.domain.data.Embeddable;
import com.sankuai.dzim.pilot.utils.DateUtils;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

import static com.sankuai.dzim.pilot.utils.EsVectorIndexNameConstants.REVIEW_VECTOR_INDEX_NAME;

@Data
public class ReviewData implements Embeddable {

    /**
     * 评价id
     */
    private Long reviewId;

    /**
     * 美团门店id
     */
    private Long mtShopId;

    /**
     * 美团用户id
     */
    private Long mtUserId;

    /**
     * 美团城市id
     */
    private Integer mtCityId;

    /**
     * 团单id
     */
    private Long dealId;

    /**
     * 手艺人id
     */
    private Long technicianId;

    /**
     * 评价标题
     */
    private String title;

    /**
     * 评价内容
     */
    private String content;

    /**
     * 后台一级类目
     */
    private Integer cat0Id;

    /**
     * 后台二级类目
     */
    private Integer cat1Id;

    /**
     * 后台三级类目
     */
    private Integer cat2Id;

    /**
     * 评价类型
     * @see https://km.sankuai.com/page/22157822
     */
    private Integer reviewType;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 星级
     */
    private Float star;

    /**
     * 好评类型，1 好评 2 差评 3 中评
     */
    private Integer starType;

    /**
     * 平台类型，0-点评，1-美团
     */
    private Integer platform;

    /**
     * 评价门店经纬度，格式：纬度,经度
     */
    private String shopGeo;

    /**
     * 评价标题+内容 复合的向量
     */
    private List<Float> fullContentVector;

    @Override
    public String embeddingContent() {
        return StringUtils.defaultString(title) + "\n" + StringUtils.defaultString(content);
    }

    @Override
    public List<Float> vector() {
        return fullContentVector;
    }

    @Override
    public void setVector(List<Float> vector) {
        this.fullContentVector = vector;
    }

    @Override
    public String indexName() {
        String suffix = DateUtils.extractYearMonth(this.createTime);
        if (StringUtils.isEmpty(suffix)) {
            return StringUtils.EMPTY;
        }
        return String.format("%s_%s", REVIEW_VECTOR_INDEX_NAME, suffix);
    }

}
