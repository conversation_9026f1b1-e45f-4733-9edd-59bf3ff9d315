package com.sankuai.dzim.pilot.scene.task;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.dzim.message.dto.MessageDTO;
import com.sankuai.dzim.pilot.api.data.AIAnswerTypeEnum;
import com.sankuai.dzim.pilot.buffer.core.PilotBufferItemDO;
import com.sankuai.dzim.pilot.buffer.enums.BufferItemTypeEnum;
import com.sankuai.dzim.pilot.buffer.utils.BufferUtils;
import com.sankuai.dzim.pilot.chain.data.AIAnswerData;
import com.sankuai.dzim.pilot.scene.task.data.TaskContext;
import com.sankuai.dzim.pilot.scene.task.data.TaskResult;
import com.sankuai.dzim.pilot.scene.task.data.TaskTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2024/8/23 14:11
 */
@Component
public class BeautyAskStreamTask extends CommonStreamTask implements Task {

    @ConfigValue(key = "com.sankuai.mim.pilot.beauty.ask.operate.question.answer", defaultValue = "{}")
    private Map<String, String> operateQuestionAnswer;

    @Override
    public boolean accept(TaskContext context) {
        return TaskTypeEnum.BEAUTY_ASK_STREAM_TASK.getType().equals(context.getTaskType());
    }

    @Override
    public TaskResult execute(TaskContext context) {
        TaskResult sensitiveWordsResult = handleSensitiveWords(context);
        if (sensitiveWordsResult != null) {
            return sensitiveWordsResult;
        }
        AIAnswerData aiAnswerData = null;
        String userQuestion = Optional.ofNullable(context).map(TaskContext::getMessageDTO).map(MessageDTO::getMessage).orElse(StringUtils.EMPTY);;
        if (!operateQuestionAnswer.containsKey(userQuestion)) {
            // 调用chain层获取AIAnswerData
            aiAnswerData = getAIAnswerData(context);
            return TaskResult.builder().taskContext(context).aiAnswerData(aiAnswerData).build();
        }
        String operateAnswer = operateQuestionAnswer.get(userQuestion);
        // 构造AIAnswerDat
        aiAnswerData = new AIAnswerData();
        aiAnswerData.setAiAnswerType(AIAnswerTypeEnum.TEXT.getType());
        aiAnswerData.setAnswer(operateAnswer);
        // buffer中写入数据
        JSONObject answerObj = JSONObject.parseObject(operateAnswer);
        PilotBufferItemDO pilotBufferItemDO = new PilotBufferItemDO();
        pilotBufferItemDO.setType(BufferItemTypeEnum.MAIN_TEXT.getType());
        pilotBufferItemDO.setData(answerObj.getString("content"));
        Map<String, Object> extra = Maps.newHashMap();
        extra.put("answerContent", answerObj.get("answerContent"));
        pilotBufferItemDO.setExtra(extra);
        BufferUtils.writeMainTextBuffer(pilotBufferItemDO);
        return TaskResult.builder().taskContext(context).aiAnswerData(aiAnswerData).build();

    }

    @Override
    public void afterExecute(TaskResult result) {

    }
}
