package com.sankuai.dzim.pilot.process.localplugin.reserve.beam;

import com.dianping.dzim.common.enums.ImUserType;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.meituan.nibtp.trade.client.combine.enums.AgentPlatformOrderStatus;
import com.meituan.nibtp.trade.client.combine.response.AgentGeneralBookingOrderDTO;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.acl.UserAclService;
import com.sankuai.dzim.pilot.api.data.AIAnswerTypeEnum;
import com.sankuai.dzim.pilot.api.enums.assistant.PlatformEnum;
import com.sankuai.dzim.pilot.chain.data.AIAnswerData;
import com.sankuai.dzim.pilot.domain.annotation.LocalPlugin;
import com.sankuai.dzim.pilot.gateway.api.assistant.data.SendBeamMessageReq;
import com.sankuai.dzim.pilot.process.aiphonecall.data.AIPhoneCallTaskCreateRes;
import com.sankuai.dzim.pilot.process.aireservebook.AIReserveBookProcessService;
import com.sankuai.dzim.pilot.process.aireservebook.AIReserveBookQueryService;
import com.sankuai.dzim.pilot.process.aireservebook.data.ReserveAnswerConstants;
import com.sankuai.dzim.pilot.process.aireservebook.data.ShopReserveContext;
import com.sankuai.dzim.pilot.process.aireservebook.data.UserReserveInfo;
import com.sankuai.dzim.pilot.process.aireservebook.data.ValidateResult;
import com.sankuai.dzim.pilot.process.aireservebook.enums.POIIndustryType;
import com.sankuai.dzim.pilot.process.aireservebook.enums.ShopReserveWayType;
import com.sankuai.dzim.pilot.process.localplugin.reserve.param.BeamReserveConfirmParam;
import com.sankuai.dzim.pilot.scene.data.AssistantSceneContext;
import com.sankuai.dzim.pilot.utils.BeamReserveBookCardUtils;
import com.sankuai.dzim.pilot.utils.LionConfigUtil;
import com.sankuai.dzim.pilot.utils.PluginContextUtil;
import com.sankuai.dzim.pilot.utils.context.RequestContext;
import com.sankuai.dzim.pilot.utils.context.RequestContextConstants;
import com.sankuai.dzim.pilot.utils.data.Response;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.wpt.user.retrieve.thrift.message.UserModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * @author: wuwenqiang
 * @create: 2025-04-25
 * @description: 取消预约工具
 */
@Component
@Slf4j
public class BeamCancelReservePlugin {

    @Autowired
    private AIReserveBookQueryService aiReserveBookQueryService;

    @Autowired
    private AIReserveBookProcessService aiReserveBookProcessService;

    @Autowired
    private UserAclService userAclService;

    @Autowired
    private LionConfigUtil lionConfigUtil;

    @Autowired
    private PluginContextUtil pluginContextUtil;

    @ConfigValue(key = "com.sankuai.mim.pilot.beam.reserve.book.agent.feedback.config", defaultValue = "{}")
    private Map<String, String> feedBackConfig;

    private static ThreadPool threadPool = Rhino.newThreadPool("BeamCancelReserveTool",
            DefaultThreadPoolProperties.Setter().withCoreSize(50).withMaxSize(100).withMaxQueueSize(100));

    @LocalPlugin(name = "BeamCancelReserveTool", description = "如果用户的最新消息是取消预约相关指令，可以由我来处理", returnDirect = false)
    public AIAnswerData cancelReserve(BeamReserveConfirmParam param) {
        log.info("调用插件-CancelReserveTool，入参={}", param);
        try {
            pluginContextUtil.replaceShopIdWithBeamContext(param, lionConfigUtil.getTestShopIdReplaceConfig());
            // 1. 前置判断
            AIAnswerData validationResult = validateInput(param);
            if (validationResult != null) {
                return validationResult;
            }
            
            AssistantSceneContext assistantSceneContext = RequestContext.getAttribute(RequestContextConstants.ASSISTANT_CONTEXT);
            int platform = getPlatform(assistantSceneContext);
            
            // 2. 并行获取所需数据
            CompletableFuture<DpPoiDTO> dpPoiDTOFuture = CompletableFuture.supplyAsync(() -> 
                aiReserveBookQueryService.queryDpPoiDTO(param.getShopId(), platform), threadPool.getExecutor());
            CompletableFuture<UserModel> userModelFuture = CompletableFuture.supplyAsync(() -> 
                userAclService.queryUserModel(assistantSceneContext.getUserId()), threadPool.getExecutor());
            CompletableFuture<UserReserveInfo> userReserveInfoFuture = CompletableFuture.supplyAsync(() -> 
                aiReserveBookQueryService.queryBeamUserReserveInfoWithOrderCheck(param, assistantSceneContext.getUserId(), param.getShopId(), platform), threadPool.getExecutor());
            
            // 3. 处理门店信息
            DpPoiDTO dpPoiDTO = dpPoiDTOFuture.join();
            // 获取行业类型
            POIIndustryType poiIndustryType = getPoiIndustryType(dpPoiDTO);
            if (poiIndustryType == null) {
                // 非预设行业
                return BeamReserveBookCardUtils.writeBeamTextAnswer(feedBackConfig.getOrDefault(ReserveAnswerConstants.SHOP_NOT_SUPPORT, ReserveAnswerConstants.FEEDBACK_ANSWER));
            }

            // 4. 验证用户预约信息是否存在
            UserReserveInfo userReserveInfo = userReserveInfoFuture.join();
            UserModel userModel = userModelFuture.join();
            ValidateResult reserveValid = validateReserveExist(param, userModel, userReserveInfo);
            if (!reserveValid.isPass()) {
                return BeamReserveBookCardUtils.writeBeamTextAnswer(reserveValid.getQuestion());
            }

            // 5. 根据预约方式处理取消预约
            AIAnswerData answer = processCancelReserve(param, userModel, userReserveInfo, poiIndustryType, dpPoiDTO);
            log.info("调用插件-CancelReserveTool，结果={}", answer.getAnswer());
            return answer;
        } catch (Exception e) {
            return handleException(param, e);
        }
    }
    
    /**
     * 验证输入参数
     */
    private AIAnswerData validateInput(BeamReserveConfirmParam param) {
        if (param == null || param.getShopId() == null || param.getShopId() <= 0) {
            return BeamReserveBookCardUtils.writeBeamTextAnswer(feedBackConfig.getOrDefault(ReserveAnswerConstants.POI_NOT_EXIST, ReserveAnswerConstants.FEEDBACK_ANSWER));
        }
        return null;
    }

    private boolean validateReserveIdExist(UserReserveInfo userReserveInfo) {
        return userReserveInfo != null && userReserveInfo.getReserveId() != null && userReserveInfo.getReserveId() > 0;
    }

    private ValidateResult validateReserveExist(BeamReserveConfirmParam param, UserModel userModel, UserReserveInfo userReserveInfo) {
        // 1.预约信息不存在，无法取消
        if (userReserveInfo == null || !validateReserveIdExist(userReserveInfo)) {
            return ValidateResult.fail(feedBackConfig.getOrDefault(ReserveAnswerConstants.RESERVE_ORDER_NOT_FOUND, ReserveAnswerConstants.FEEDBACK_ANSWER));
        }
        // 2.预约流程中不支持取消
        // 2.2 验证预约订单是否真实存在
        SendBeamMessageReq sendBeamMessageReq = PluginContextUtil.getBeamRequestContext(param);
        AgentGeneralBookingOrderDTO booingOrderDTO = aiReserveBookQueryService.queryBeamReserveOrder(sendBeamMessageReq, userReserveInfo.getReserveId(), userReserveInfo.getTaskReserveType());
        ValidateResult reserveStatus = validateReserveStatus(userReserveInfo, booingOrderDTO);
        return reserveStatus;
    }

    
    /**
     * 处理取消预约
     */
    private AIAnswerData processCancelReserve(BeamReserveConfirmParam param, UserModel userModel, UserReserveInfo userReserveInfo,
                                              POIIndustryType poiIndustryType, DpPoiDTO dpPoiDTO) {
        // Todo: 当前预约ID直接取的缓存，后续考虑直接取入参中的预约ID
        Integer shopReserveWayType = userReserveInfo.getShopReserveWayType();
        try {
            // 1. 执行取消预约
            Response<Boolean> resp;
            if (ShopReserveWayType.ONLINE.getType() == shopReserveWayType) {
                resp = processOnlineCancelReserve(param, userReserveInfo);
            } else {
                ShopReserveContext shopReserveContext = buildShopReserveContext(userReserveInfo, poiIndustryType, dpPoiDTO);
                resp = processPhoneCallCancelReserve(param, userModel, userReserveInfo, shopReserveContext);
            }
            if (resp == null || !resp.isSuccess()) {
                // 异常消息存在则需要返回给模型处理，否则直接返回兜底异常
                String msg = resp == null ? StringUtils.EMPTY : resp.getMsg();
                return StringUtils.isNotBlank(msg) ? BeamReserveBookCardUtils.writeBeamModelGenerateAnswer(msg) : BeamReserveBookCardUtils.writeBeamTextAnswer(feedBackConfig.getOrDefault(ReserveAnswerConstants.CANCEL_RESERVE_FAILED, ReserveAnswerConstants.FEEDBACK_ANSWER));
            }
            // 2. 返回文案
            return BeamReserveBookCardUtils.writeBeamTextAnswer(feedBackConfig.getOrDefault(ReserveAnswerConstants.CANCEL_RESERVE_SUCCESS, ReserveAnswerConstants.CANCEL_RESERVE_SUCCESS_TEXT));
        } catch (Exception e) {
            log.error("[CancelReservePlugin] processCancelReserve error, mtUserId={}, mtShopId={}, reserveId={}", userModel.getId(), dpPoiDTO.getMtPoiId(), userReserveInfo.getReserveId(), e);
            return BeamReserveBookCardUtils.writeBeamTextAnswer(feedBackConfig.getOrDefault(ReserveAnswerConstants.CANCEL_RESERVE_FAILED, ReserveAnswerConstants.FEEDBACK_ANSWER));
        }
    }

    private Response<Boolean> processOnlineCancelReserve(BeamReserveConfirmParam param, UserReserveInfo userReserveInfo) {
        SendBeamMessageReq sendBeamMessageReq = PluginContextUtil.getBeamRequestContext(param);
        return aiReserveBookProcessService.refundBeamReserve(sendBeamMessageReq, userReserveInfo);
    }

    private Response<Boolean> processPhoneCallCancelReserve(BeamReserveConfirmParam param, UserModel userModel, UserReserveInfo userReserveInfo, ShopReserveContext shopReserveContext) {
        AssistantSceneContext assistantSceneContext = RequestContext.getAttribute(RequestContextConstants.ASSISTANT_CONTEXT);
        int platform = getPlatform(assistantSceneContext);
        // 2. 发起AI外呼（仅通知）
        SendBeamMessageReq sendBeamMessageReq = PluginContextUtil.getBeamRequestContext(param);
        Response<AIPhoneCallTaskCreateRes> response = aiReserveBookProcessService.refundBeamAIPhoneCall(sendBeamMessageReq, userModel, shopReserveContext, userReserveInfo, 0L, platform);
        if (!response.isSuccess()) {
            LogUtils.logFailLog(log, TagContext.builder().action("processPhoneCallCancelReserve").build(),
                    WarnMessage.build("BeamCancelReservePlugin", "调用取消外呼接口失败", ""), param, response);
            return null;
        }
        // 3. 调用取消接口
        return aiReserveBookProcessService.refundBeamReserve(sendBeamMessageReq, userReserveInfo);
    }

    /**
     * 处理异常
     */
    private AIAnswerData handleException(BeamReserveConfirmParam param, Exception e) {
        AIAnswerData aiAnswerData = new AIAnswerData();
        aiAnswerData.setAiAnswerType(AIAnswerTypeEnum.TEXT.getType());
        LogUtils.logFailLog(log, TagContext.builder().action("MakeReserve").build(),
                new WarnMessage("CancelReserveTool", "发起预约插件异常", ""), param, e);
        log.error("[CancelReservePlugin] cancelReserve error, param:{}", param, e);
        log.error("调用插件-CancelReserveTool，异常, param={}", param, e);
        aiAnswerData.setAnswer(ReserveAnswerConstants.FEEDBACK_ANSWER);
        aiAnswerData.setReturnDirect(true);
        return aiAnswerData;
    }

    private POIIndustryType getPoiIndustryType(DpPoiDTO dpPoiDTO) {
        Integer secondBackCategoryId = aiReserveBookProcessService.extractMainSecondBackCategoryId(dpPoiDTO);
        return POIIndustryType.getBySecondBackCategoryId(secondBackCategoryId);
    }

    private ValidateResult validateReserveStatus(UserReserveInfo userReserveInfo, AgentGeneralBookingOrderDTO bookingOrderDTO) {
        if (userReserveInfo == null || bookingOrderDTO == null || bookingOrderDTO.getOrderId() == null
            || bookingOrderDTO.getPlatformOrderStatus() == null) {
            return ValidateResult.fail(feedBackConfig.getOrDefault(ReserveAnswerConstants.RESERVE_ORDER_NOT_FOUND, ReserveAnswerConstants.FEEDBACK_ANSWER));
        }
        Integer platformOrderStatus = bookingOrderDTO.getPlatformOrderStatus();
        // 已预约成功可取消
        if (platformOrderStatus.intValue() == AgentPlatformOrderStatus.WAIT_CONSUME.getCode()) {
            return ValidateResult.success();
        }
        // 预约中不可取消
        if (platformOrderStatus.intValue() == AgentPlatformOrderStatus.WAIT_PAY.getCode() || platformOrderStatus.intValue() == AgentPlatformOrderStatus.DELIVERING.getCode()) {
            return ValidateResult.fail(feedBackConfig.getOrDefault(ReserveAnswerConstants.RESERVE_ASKING_CAN_NOT_CANCEL, ReserveAnswerConstants.FEEDBACK_ANSWER));
        }
        // 已取消的订单其他状态不可取消
        return ValidateResult.fail(feedBackConfig.getOrDefault(ReserveAnswerConstants.RESERVE_CANCEL_CAN_NOT_CANCEL, ReserveAnswerConstants.FEEDBACK_ANSWER));
    }

    private ShopReserveContext buildShopReserveContext(UserReserveInfo userReserveInfo, POIIndustryType poiIndustryType, DpPoiDTO dpPoiDTO) {
        ShopReserveWayType shopReserveWayType = ShopReserveWayType.getByType(userReserveInfo.getShopReserveWayType());
        return ShopReserveContext.buildShopReserveContext(dpPoiDTO, poiIndustryType, shopReserveWayType);
    }

    private int getPlatform(AssistantSceneContext context) {
        return context.getUserId().startsWith(ImUserType.MT.getPrefix()) ? PlatformEnum.MT.getType() : PlatformEnum.DP.getType();
    }
}
