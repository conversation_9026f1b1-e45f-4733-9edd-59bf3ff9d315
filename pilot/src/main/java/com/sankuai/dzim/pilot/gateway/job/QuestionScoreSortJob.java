package com.sankuai.dzim.pilot.gateway.job;

import com.cip.crane.client.spring.annotation.Crane;
import com.sankuai.dzim.pilot.process.QuestionScoreProcessService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2024/08/19 19:02
 * 根据UV点击+分类分值+高优权重 每日计算至 GenerativeWord表 Score字段
 */
@Component
@Slf4j
public class QuestionScoreSortJob {
    @Autowired
    private QuestionScoreProcessService questionScoreProcessService;

    @Crane("com.sankuai.mim.pilot.generative.search.word.sort")
    public void handle() {
        questionScoreProcessService.processScoreSort();
    }

}
