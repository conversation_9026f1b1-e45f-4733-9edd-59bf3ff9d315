package com.sankuai.dzim.pilot.acl;


import com.sankuai.call.sdk.entity.aicall.AiCallParamBO;
import com.sankuai.call.sdk.entity.aicall.AiCallResponseDTO;
import com.sankuai.call.sdk.entity.common.ResponseDTO;
import com.sankuai.call.sdk.entity.record.QueryRecordDataDTO;

import java.util.List;

/**
 * 木星ACL服务
 */
public interface JupiterCallAclService {

    /**
     * 木星智能外呼（明文号码）
     * @param aiCallParamBO
     * @return
     */
    AiCallResponseDTO<String> invokeAiCall(AiCallParamBO aiCallParamBO);

    /**
     * 查询录音链接
     */
    ResponseDTO<List<QueryRecordDataDTO>> queryAudioUrl(String tenantId, String contactId);
}
