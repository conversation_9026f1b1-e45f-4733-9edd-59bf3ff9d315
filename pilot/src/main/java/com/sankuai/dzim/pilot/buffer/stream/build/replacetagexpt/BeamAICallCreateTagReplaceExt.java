package com.sankuai.dzim.pilot.buffer.stream.build.replacetagexpt;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.dzim.pilot.buffer.enums.StreamEventCardTypeEnum;
import com.sankuai.dzim.pilot.buffer.stream.build.ext.data.BeamAICallCreateData;
import com.sankuai.dzim.pilot.buffer.stream.build.replacetagexpt.data.ReplaceContext;
import com.sankuai.dzim.pilot.enums.BeamResourceTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * @author: zhouyibing
 * @date: 2025/5/13
 */
@Component
@Slf4j
public class BeamAICallCreateTagReplaceExt implements ReplaceTagExt {
    @Override
    public boolean accept(String tag) {
        return StreamEventCardTypeEnum.BEAM_AI_CALL_CREATE_CARD.getType().equals(tag);
    }

    @Override
    public String replace(ReplaceContext replaceContext) {
        String value = replaceContext.getTag().getValue();
        if (StringUtils.isBlank(value)) {
            return null;
        }
        String type = BeamResourceTypeEnum.FWLS_AI_CALL_CREATE_ORDER.getKey();
        BeamAICallCreateData aiCallCreateData = JSONObject.parseObject(value, BeamAICallCreateData.class);
        String firstTag = "<card " + "type=" + type + " index=FWLS-1 hidden=1>";
        String endTag = "</card>";
        return firstTag + value + endTag;
    }
}
