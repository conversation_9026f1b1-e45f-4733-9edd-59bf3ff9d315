package com.sankuai.dzim.pilot.acl.data.fraiday.app;

import lombok.Data;

import java.util.List;

/**
 * friday应用工厂发送消息请求
 * https://km.sankuai.com/collabpage/1934713151
 */
@Data
public class SendMessageRequest {
    /**
     * 应用Id
     */
    private String appId;

    /**
     * 用户Id
     */
    private String userId;

    /**
     * 用户类型
     */
    private String userType;

    /**
     * 发送的内容
     */
    private List<String> utterances;

    /**
     * 输入联想的查询内容
     */
    private String query;

    /**
     * 鉴权token
     */
    private String accessToken;

    /**
     * 是否流式返回
     */
    private boolean stream = false;

    /**
     * 是否开启debug
     */
    private boolean debug = false;

    /**
     * 期望使用的知识库
     */
    private List<String> datasetIds;

    /**
     * 推荐生成的suggestion数量，默认为3
     */
    private int count = 3;

    /**
     * 生成的suggestion是否以query为前缀，true: 关键词召回，false: 前缀匹配召回
     * 默认为false(即前缀召回)
     */
    private boolean usePhasePrefix = false;
}

