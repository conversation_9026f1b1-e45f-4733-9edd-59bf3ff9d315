package com.sankuai.dzim.pilot.acl.impl;

import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.acl.FridayAppAclService;
import com.sankuai.dzim.pilot.acl.data.fraiday.app.*;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;
import retrofit2.Call;
import retrofit2.Response;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @author: zhouyibing
 * @date: 2024/5/28
 */
@Service
@Slf4j
public class FridayAppAclServiceImpl implements FridayAppAclService {

    @Resource(name = "dzimRedisClient")
    private RedisStoreClient redisStoreClient;

    private static final FridayAppFactoryService FRIDAY_APP_FACTORY_API_SERVICE = new Retrofit.Builder()
            .baseUrl("https://friday.sankuai.com/")
            .addConverterFactory(GsonConverterFactory.create())
            .client(new OkHttpClient.Builder()
                    .connectTimeout(10, TimeUnit.SECONDS)
                    .readTimeout(30, TimeUnit.SECONDS)
                    .writeTimeout(30, TimeUnit.SECONDS)
                    .build())
            .build().create(FridayAppFactoryService.class);

    private static final FridayTokenApiService FRIDAY_TOKEN_API_SERVICE = new Retrofit.Builder()
            .baseUrl("https://auth-ai.vip.sankuai.com")
            .addConverterFactory(GsonConverterFactory.create())
            .build().create(FridayTokenApiService.class);

    private static final String FRIDAY_ACCESS_TOKEN_KEY = "FridayAccessToken";

    private static final int SUCCESS_CODE = 0;

    @Override
    public List<String> getSuggestion(FridayAppSendMessageReq request) {
        if (request == null) {
            return Collections.emptyList();
        }
        // 查询鉴权token
        String accessToken = getFridayAccessToken(request);
        if (StringUtils.isEmpty(accessToken)) {
            return Collections.emptyList();
        }

        // 发送消息
        SendMessageRequest sendMessageRequest = buildFridaySendMessageRequest(request, accessToken);
        Call<FridayAppResponse<SuggestionResult>> fridayAppResponseCall = FRIDAY_APP_FACTORY_API_SERVICE.getSuggestion(sendMessageRequest);;
        try {
            Response<FridayAppResponse<SuggestionResult>> response = fridayAppResponseCall.execute();
            if (!response.isSuccessful() || response.body() == null || response.body().getCode() != SUCCESS_CODE) {
                LogUtils.logFailLog(log, TagContext.builder().action("sendMessageForInputSuggestion").userId(request.getUserId()).build(),
                        new WarnMessage("FridayAppAclService", "friday应用工厂发送消息失败", ""), sendMessageRequest, JsonCodec.encodeWithUTF8(response));
                return Collections.emptyList();
            }
            LogUtils.logReturnInfo(log, TagContext.builder().action("sendMessageForInputSuggestion").userId(request.getUserId()).build(),
                    new WarnMessage("FridayAppAclService", "friday应用工厂发送消息成功", ""), sendMessageRequest, response);

            // 召回结果为空
            if (response.body().getData() == null) {
                return Collections.emptyList();
            }
            List<SuggestionData> suggestionDataList = response.body().getData().getSuggestions();
            List<String> suggestionTextList = CollectionUtils.emptyIfNull(suggestionDataList).stream()
                    .map(SuggestionData::getQuery)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            return suggestionTextList;
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("sendMessageForInputSuggestion").userId(request.getUserId()).build(),
                    new WarnMessage("FridayAppAclService", "friday应用工厂发送消息异常", ""), sendMessageRequest, e);
        }
        return Collections.emptyList();
    }

    private String getFridayAccessToken(FridayAppSendMessageReq request) {
        try {
            // 先从缓存中获取
            String token = redisStoreClient.get(getAccessTokenKey(request.getClientId()));
            if (StringUtils.isNotBlank(token)) {
                return token;
            }

            // 缓存过期重新查询token
            Pair<String, Long> tokenWithExpireTime = getAccessTokenByHttp(request);
            if (tokenWithExpireTime == null) {
                return null;
            }

            // 缓存token
            token = tokenWithExpireTime.getKey();
            int expireTime = tokenWithExpireTime.getValue().intValue() / 1000 / 2;
            if (StringUtils.isNotBlank(token) && expireTime > 0) {
                redisStoreClient.set(getAccessTokenKey(request.getClientId()), token, expireTime);
            }
            return token;
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("getFridayAccessToken").userId(request.getUserId()).build(),
                    new WarnMessage("FridayAppAclService", "friday获取鉴权Token异常", ""), request, e);
        }
        return null;
    }

    private Pair<String, Long> getAccessTokenByHttp(FridayAppSendMessageReq request) throws IOException {
        Map<String, String> fields = new HashMap<>();
        fields.put("client_id", request.getClientId());
        fields.put("client_secret", request.getClientSecret());
        fields.put("grant_type", request.getGrantType());
        Call<FridayTokenResponse<FridayAccessToken>> call = FRIDAY_TOKEN_API_SERVICE.auth(fields);
        Response<FridayTokenResponse<FridayAccessToken>> response = call.execute();
        if (!response.isSuccessful() || response.body() == null || response.body().getErrcode() != 0) {
            LogUtils.logFailLog(log, TagContext.builder().action("getFridayAccessToken").userId(request.getUserId()).build(),
                    new WarnMessage("FridayAppAclService", "friday获取鉴权Token失败", ""), request, response);
            return null;
        }
        return new ImmutablePair<>(response.body().getData().getAccess_token(), response.body().getData().getExpires_in());
    }

    private SendMessageRequest buildFridaySendMessageRequest(FridayAppSendMessageReq request, String accessToken) {
        SendMessageRequest sendMessageRequest = new SendMessageRequest();
        sendMessageRequest.setAppId(request.getAppId());
        sendMessageRequest.setUserId(request.getUserId());
        sendMessageRequest.setUserType(request.getUserType());
        sendMessageRequest.setUtterances(request.getUtterances());
        sendMessageRequest.setStream(request.isStream());
        sendMessageRequest.setDebug(request.isDebug());
        sendMessageRequest.setAccessToken(accessToken);
        sendMessageRequest.setQuery(request.getQuery());
        sendMessageRequest.setDatasetIds(request.getDatasetIds());
        sendMessageRequest.setCount(request.getCount());
        sendMessageRequest.setUsePhasePrefix(request.isUsePhasePrefix());
        return sendMessageRequest;
    }

    private StoreKey getAccessTokenKey(String fridayClientId) {
        return new StoreKey(FRIDAY_ACCESS_TOKEN_KEY, fridayClientId);
    }
}
