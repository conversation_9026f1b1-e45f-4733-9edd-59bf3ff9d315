package com.sankuai.dzim.pilot.chain.eval;

import com.alibaba.fastjson.JSON;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.mtrace.sample.rate.RateLimiter;
import com.meituan.trip.trippackage.mask.util.DeepCopyUtil;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.message.dto.MessageDTO;
import com.sankuai.dzim.pilot.chain.data.AIAnswerData;
import com.sankuai.dzim.pilot.chain.data.AIServiceContext;
import com.sankuai.dzim.pilot.chain.enums.AIServiceExtraKeyEnum;
import com.sankuai.dzim.pilot.chain.eval.data.*;
import com.sankuai.dzim.pilot.chain.eval.evaluator.Evaluator;
import com.sankuai.dzim.pilot.chain.impl.EvalAIService;
import com.sankuai.dzim.pilot.domain.message.AssistantMessage;
import com.sankuai.dzim.pilot.domain.message.Message;
import com.sankuai.dzim.pilot.domain.message.UserMessage;
import com.sankuai.dzim.pilot.process.data.AIServiceConfig;
import com.sankuai.dzim.pilot.utils.ExcelUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * @author: zhouyibing
 * @date: 2025/3/26
 */
@Component
@Slf4j
public class EvalTaskService {

    @Autowired
    private List<Evaluator> evaluators;

    @Autowired
    private EvalAIService evalAIService;

    public static ThreadPool LLM_EVALUATOR_POOL = Rhino.newThreadPool("LLM_EVALUATOR_POOL",
            DefaultThreadPoolProperties.Setter().withCoreSize(4).withMaxSize(4).withMaxQueueSize(1000));

    public EvalTaskResult execute(EvalTaskRequest request) {
        // 1. 解析测试用例
        List<EvalCase> EvalCaseList = buildEventCaseList(request);

        // 2. 执行并评测测试用例
        List<EvalCaseResult> evaluatedCases = evaluateTestCases(request, EvalCaseList);

        // 3. 生成评测报告
        log.info("EvalTaskService evalTaskResult begin evaluatedCases={}", evaluatedCases);
        EvalTaskResult evalTaskResult = generateReport(request, evaluatedCases);

        // 4. 输出excel
        log.info("EvalTaskService execute output excel beginm evalTaskResult={}", evalTaskResult);
        String outputFilePath = buildOutputFilePath(request);
        ExcelUtils.exportExcel(outputFilePath, (List<EvalCaseResult>) DeepCopyUtil.deepCopy(evalTaskResult.getTestCaseResults(), EvalCaseResult.class), EvalCaseResult.class);

        return evalTaskResult;
    }

    private List<EvalCase> buildEventCaseList(EvalTaskRequest request) {
        if (CollectionUtils.isNotEmpty(request.getCases())) {
            return request.getCases();
        }
        return getEvalCasesFromExcel(request);
    }

    private List<EvalCase> getEvalCasesFromExcel(EvalTaskRequest request) {
        List<EvalCase> evalCaseList = ExcelUtils.readFromExcel(request.getFilePath(), EvalCase.class);
        if (CollectionUtils.isEmpty(evalCaseList)) {
            return Collections.emptyList();
        }
        Long sessionId = evalCaseList.get(0).getSessionId();
        // sessionId为空，则直接返回测试用例列表。
        if (sessionId == null) {
            return evalCaseList;
        }

        // sessionId不为空，按照sessionId分组，构造消息历史记录
        Map<Long, List<EvalCase>> sessionGroups = evalCaseList.stream().collect(Collectors.groupingBy(EvalCase::getSessionId));
        return sessionGroups.entrySet().stream().sorted(Map.Entry.comparingByKey()) // 按sessionId排序
                .map(Map.Entry::getValue).map(cases -> {
                    if (cases.size() <= 1) {
                        return cases.get(0);
                    }

                    // 获取最后一条作为测试用例
                    EvalCase testCase = cases.get(cases.size() - 1);

                    // 构建chat history (前n-1条记录)
                    List<Message> chatHistory = cases.subList(0, cases.size() - 1).stream().map(historyCase -> {
                        List<Message> messages = Lists.newArrayList();
                        messages.add(UserMessage.build(historyCase.getInput()));
                        messages.add(AssistantMessage.build(historyCase.getOutput()));
                        return messages;
                    }).flatMap(List::stream).collect(Collectors.toList());

                    // 构建chatHistoryStr
                    StringBuilder chatHistoryStr = new StringBuilder();
                    for (int i = 0; i < chatHistory.size(); i += 2) {
                        UserMessage userMessage = (UserMessage) chatHistory.get(i);
                        AssistantMessage assistantMessage = (AssistantMessage) chatHistory.get(i + 1);
                        chatHistoryStr.append("Human: ").append(userMessage.getContent()).append("\n");
                        chatHistoryStr.append("Assistant: ").append(assistantMessage.getContent()).append("\n\n");
                    }

                    testCase.setChatHistoryStr(chatHistoryStr.toString());
                    testCase.setChatHistory(chatHistory);
                    return testCase;
                }).collect(Collectors.toList());
    }

    private List<EvalCaseResult> evaluateTestCases(EvalTaskRequest request, List<EvalCase> testCases) {
        if (CollectionUtils.isEmpty(testCases)) {
            return Collections.emptyList();
        }

        // 构造入参
        EvalContext evalContext = new EvalContext();
        evalContext.setMetric(request.getMetric());

        // 选择评测器，默认为llm评测器
        Evaluator evaluator = selectEvaluator(request.getEvaluator());

        // 创建限流器，转换RPM到RPS
        double rps = request.getRpm() / 60.0;
        RateLimiter rateLimiter = RateLimiter.create(rps);

        // 并发跑
        List<CompletableFuture<EvalCaseResult>> futures = testCases.stream()
                .map(testCase -> CompletableFuture.supplyAsync(
                        run(request, testCase, rateLimiter, evaluator, evalContext),
                        LLM_EVALUATOR_POOL.getExecutor()
                ))
                .collect(Collectors.toList());

        return futures.stream().map(CompletableFuture::join).collect(Collectors.toList());
    }

    @NotNull
    private Supplier<EvalCaseResult> run(EvalTaskRequest request, EvalCase testCase, RateLimiter rateLimiter, Evaluator evaluator, EvalContext evalContext) {
        return () -> {
            try {
                rateLimiter.acquire();
                //1.评测前需要先获取大模型答案
                if (request.isExecuteBeforeEval()) {
                    String output = buildTestCaseOutput(request, testCase);
                    testCase.setOutput(output);
                }
                //2.评测
                return evaluator.measure(evalContext, testCase);
            } catch (Exception e) {
                LogUtils.logFailLog(log, TagContext.builder().action("run").build(),
                        new WarnMessage("EvalTaskService", "评测执行失败", null), request, null, e);
                //异常处理
                EvalCaseResult result = new EvalCaseResult();
                BeanUtils.copyProperties(testCase, result);
                result.setReason("评测失败：" + e.getMessage());
                return result;
            }
        };
    }

    private String buildTestCaseOutput(EvalTaskRequest request, EvalCase evalCase) {
        AIServiceConfig aiServiceConfig = JSON.parseObject(request.getAiServiceConfig(), AIServiceConfig.class);
        AIServiceContext aiServiceContext = buildAIServiceContext(evalCase, aiServiceConfig);
        AIAnswerData aiAnswerData = evalAIService.execute(aiServiceContext);
        return Optional.ofNullable(aiAnswerData).map(AIAnswerData::getAnswer).orElse(null);
    }

    public AIServiceContext buildAIServiceContext(EvalCase evalCase, AIServiceConfig config) {
        MessageDTO messageDTO = new MessageDTO();
        messageDTO.setMessage(evalCase.getInput());

        AIServiceContext aIServiceContext = new AIServiceContext();
        aIServiceContext.setMessageDTO(messageDTO);
        aIServiceContext.setMemoryComponents(config.getMemoryComponents());
        aIServiceContext.setQueryUnderstandComponents(config.getQueryUnderstandComponents());
        aIServiceContext.setRetrievalComponents(config.getRetrievalComponents());
        aIServiceContext.setPluginNames(config.getPluginNames());
        aIServiceContext.setSystemPrompt(config.getSystemPrompt());
        aIServiceContext.setModel(config.getModel());
        aIServiceContext.setAppId(config.getAppId());
        aIServiceContext.setTemperature(config.getTemperature());
        aIServiceContext.setTopP(config.getTopP());
        aIServiceContext.setIsJsonModel(config.getIsJsonModel());
        aIServiceContext.setStream(config.isStream());
        aIServiceContext.setExtraInfo(Maps.newHashMap());
        aIServiceContext.setRetrievalMap(config.getRetrievalMap());
        aIServiceContext.setMaxTokens(config.getMaxTokens());

        aIServiceContext.getExtraInfo().put(AIServiceExtraKeyEnum.EVAL_CASE.getKey(), evalCase);
        return aIServiceContext;
    }

    private Evaluator selectEvaluator(String evaluator) {
        String finalEvaluator = evaluator;
        return evaluators.stream()
                .filter(e -> e.accept(finalEvaluator))
                .findFirst()
                .orElse(null);
    }

    private EvalTaskResult generateReport(EvalTaskRequest request, List<EvalCaseResult> evaluatedCases) {
        EvalTaskResult report = new EvalTaskResult();

        double averageScore = evaluatedCases.stream()
                .filter(Objects::nonNull)
                .map(EvalCaseResult::getScore)
                .filter(Objects::nonNull)
                .mapToDouble(Integer::doubleValue)
                .average()
                .orElse(0.0);
        double roundedScore = Double.parseDouble(String.format("%.2f", averageScore));
        report.setAverageScore(roundedScore);
        report.setTestCaseResults(evaluatedCases);

        return report;
    }


    private String buildOutputFilePath(EvalTaskRequest request) {
        String filePath = request.getFilePath();
        String metric = request.getMetric();

        if (StringUtils.isBlank(filePath)) {
            return null;
        }

        int dotIndex = filePath.lastIndexOf(".");
        if (dotIndex == -1) {
            return filePath + "_result";
        }

        String suffix = "_result" ;
        if (StringUtils.isNotBlank(metric)) {
            suffix += "_" + metric;
        }
        return filePath.substring(0, dotIndex) + suffix + filePath.substring(dotIndex);
    }
}
