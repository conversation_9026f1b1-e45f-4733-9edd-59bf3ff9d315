package com.sankuai.dzim.pilot.acl.impl;

import com.dianping.deal.shop.DealShopQueryService;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonClient;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.acl.DealIdAclService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Service
@Slf4j
public class DealIdAclServiceImpl implements DealIdAclService {

    @MdpPigeonClient(url = "http://service.dianping.com/tuangou/dealShopService/dealShopQueryService_1.0.0", timeout = 3000)
    private DealShopQueryService dealShopQueryService;

    @Override
    public List<Long> getSaleDealId(long shopId, int platform, int start, int limit) {
        Assert.isTrue(shopId > 0, "shopId错误");
        Assert.isTrue(platform == 100 || platform == 200, "platform必须是100或者200");
        Assert.isTrue(start >= 0, "start错误");
        Assert.isTrue(limit <= 100, "limit不能超过100");
        try {
            List<Long> dealGroupIds = dealShopQueryService.querySaleDealGroupId(shopId, platform, start, limit);
            // 查询结果为空，直接返回
            if (CollectionUtils.isEmpty(dealGroupIds)) {
                return null;
            }
            return dealGroupIds;
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("getSaleDealId").bizId(String.valueOf(shopId)).build(),
                    new WarnMessage("DealIdAclService", "查询团单ID失败", null), shopId, null, e);
        }
        return null;
    }
}
