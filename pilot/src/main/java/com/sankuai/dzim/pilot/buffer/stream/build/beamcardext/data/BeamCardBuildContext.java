package com.sankuai.dzim.pilot.buffer.stream.build.beamcardext.data;

import com.sankuai.dzim.pilot.buffer.core.PilotBufferItemDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BeamCardBuildContext {

    private String data;

    List<PilotBufferItemDO> tagItems;
}
