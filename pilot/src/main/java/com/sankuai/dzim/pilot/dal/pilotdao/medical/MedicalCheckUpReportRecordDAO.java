package com.sankuai.dzim.pilot.dal.pilotdao.medical;

import com.sankuai.dzim.pilot.dal.entity.pilot.medical.MedicalCheckUpReportRecordEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: zhouyibing
 * @date: 2024/9/9
 */
public interface MedicalCheckUpReportRecordDAO {

    int insert(@Param("entity") MedicalCheckUpReportRecordEntity pilotMedicalHealthCheckUpReportRecordEntity);

    int update(@Param("entity") MedicalCheckUpReportRecordEntity pilotMedicalHealthCheckUpReportRecordEntity);

    MedicalCheckUpReportRecordEntity queryById(@Param("id") long id);

    List<MedicalCheckUpReportRecordEntity> queryByUserId(@Param("userId") String userId,
                                                         @Param("offset") int offset, @Param("pageSize") int pageSize);

    int queryCountByUserId(@Param("userId") String userId);

    List<MedicalCheckUpReportRecordEntity> queryByIds(@Param("ids") List<Long> ids);

    int deleteById(@Param("id") long id);
}
