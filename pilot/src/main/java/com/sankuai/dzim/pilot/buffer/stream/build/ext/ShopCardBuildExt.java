package com.sankuai.dzim.pilot.buffer.stream.build.ext;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.dzim.message.common.utils.ImAccountTypeUtils;
import com.sankuai.dzim.pilot.acl.DzRenderAclService;
import com.sankuai.dzim.pilot.buffer.enums.StreamEventCardTypeEnum;
import com.sankuai.dzim.pilot.buffer.stream.build.ext.data.ShopCardData;
import com.sankuai.dzim.pilot.buffer.stream.vo.StreamEventCardDataVO;
import com.sankuai.dzim.pilot.chain.enums.AIServiceExtraKeyEnum;
import com.sankuai.dzim.pilot.scene.data.AssistantSceneContext;
import com.sankuai.dzim.pilot.scene.task.data.EnvContext;
import com.sankuai.dzshoplist.aggregate.dzrender.enums.*;
import com.sankuai.dzshoplist.aggregate.dzrender.request.DzRenderRequest;
import com.sankuai.dzshoplist.aggregate.dzrender.request.param.ClientEnv;
import com.sankuai.dzshoplist.aggregate.dzrender.request.param.DzRenderIdDTO;
import com.sankuai.dzshoplist.aggregate.dzrender.request.param.RenderIdDTO;
import com.sankuai.dzshoplist.aggregate.dzrender.response.ShopRenderItem;
import com.sankuai.dzshoplist.aggregate.dzrender.response.common.PicDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/7/29 19:40
 */
@Slf4j
@Component
public class ShopCardBuildExt implements CardBuildExt {

    @Autowired
    private DzRenderAclService dzRenderAclService;

    @Override
    public boolean accept(StreamEventCardDataVO streamEventCardDataVO) {
        if (streamEventCardDataVO == null) {
            return false;
        }
        return streamEventCardDataVO.getType().equals(StreamEventCardTypeEnum.SHOP_CARD.getType());
    }

    @Override
    public void padding(StreamEventCardDataVO streamEventCardDataVO, AssistantSceneContext context) {
        String key = streamEventCardDataVO.getKey();
        // 校验key的格式
        if (!checkKey(key)) {
            return;
        }

        // 解析key
        int index = Integer.parseInt(key.split(":")[0]);
        long shopId = Long.parseLong(key.split(":")[1]);
        String recommend = getRecommend(key);

        // 查询门店数据
        Map<Long, ShopRenderItem> shopInfoMap = dzRenderAclService.queryShopRenderInfo(buildRenderRequest(shopId, context)).join();
        if (MapUtils.isEmpty(shopInfoMap) || shopInfoMap.get(shopId) == null) {
            return;
        }

        // 拼装数据
        buildCardProps(index, recommend, shopInfoMap.get(shopId), streamEventCardDataVO);
    }

    private void buildCardProps(int index, String recommend, ShopRenderItem shopRenderItem, StreamEventCardDataVO streamEventCardDataVO) {
        Map<String, Object> cardProps = streamEventCardDataVO.getCardProps();
        cardProps.put("index", index);
        cardProps.put("shopRecommend", recommend);

        cardProps.put("shopCardData", buildShopCard(shopRenderItem));

    }

    private ShopCardData buildShopCard(ShopRenderItem shopRenderItem) {
        ShopCardData shopCardData = new ShopCardData();
        shopCardData.setShopId(shopRenderItem.getShopRenderId().getRenderId().getItemId());
        shopCardData.setShopName(shopRenderItem.getShopRenderInfo().getShopBasicInfo().getName());
        shopCardData.setArea(shopRenderItem.getShopRenderInfo().getShopBasicInfo().getAreaName());
        shopCardData.setJumpUrl(shopRenderItem.getShopRenderInfo().getShopBasicInfo().getJumpUrl());
        shopCardData.setScore(shopRenderItem.getShopRenderInfo().getShopBasicInfo().getStarScore() + "分");
        shopCardData.setAvgPrice(shopRenderItem.getShopRenderInfo().getPrice().getAvgPriceDesc());
        shopCardData.setDistance(getDistance(shopRenderItem.getShopRenderInfo().getShopBasicInfo().getDistance()));
        List<PicDTO> shopPic = shopRenderItem.getShopRenderInfo().getShopMultimedia().getShopPic();
        if (CollectionUtils.isNotEmpty(shopPic)) {
            shopCardData.setShopPics(shopPic.stream().map(PicDTO::getPicUrls).collect(Collectors.toList()));
        }
        return shopCardData;
    }

    private String getDistance(double distance) {
        if (distance < 1000) {
            return (int) distance + "m";
        }
        return String.format("%.1f", distance / 1000) + "km";
    }

    private DzRenderRequest buildRenderRequest(long shopId, AssistantSceneContext context) {
        DzRenderRequest dzRenderRequest = new DzRenderRequest();
        dzRenderRequest.setRenderIds(buildRenderId(shopId));
        dzRenderRequest.setRenderType(RenderTypeEnum.POI_PRODUCT.getType());
        dzRenderRequest.setSceneCode(SceneCodeEnum.SHOP_LIST_HAIRCUT_FOOT.getCode());
        dzRenderRequest.setSpaceKey(SpaceKeyEnum.PLATFORM_AGENT_RENDER.getCode());
        dzRenderRequest.setClientEnv(buildClientEnv(context));
        return dzRenderRequest;
    }

    private ClientEnv buildClientEnv(AssistantSceneContext context) {
        String envContextStr = Optional.ofNullable(context)
                .map(AssistantSceneContext::getExtra)
                .map(extraInfo -> MapUtils.getString(extraInfo, AIServiceExtraKeyEnum.ENV_CONTEXT.getKey(), StringUtils.EMPTY))
                .orElse(StringUtils.EMPTY);
        EnvContext envContext = JsonCodec.decode(envContextStr, EnvContext.class);

        if (envContext == null) {
            envContext = new EnvContext();
            envContext.setCityId(0);
            envContext.setLat(0.0);
            envContext.setLng(0.0);
            envContext.setAppVersion("");
            envContext.setDeviceId("");
        }

        boolean isDp = ImAccountTypeUtils.isDpUserId(context.getUserId());
        ClientEnv clientEnv = new ClientEnv();
        clientEnv.setGpsCityId(envContext.getCityId());
        clientEnv.setCityId(envContext.getCityId());
        clientEnv.setPlatform(isDp ? PlatformEnum.DP.getCode() : PlatformEnum.MT.getCode());
        clientEnv.setLng(envContext.getLng());
        clientEnv.setLat(envContext.getLat());
        clientEnv.setPageLng(envContext.getLng());
        clientEnv.setPageLat(envContext.getLat());
        clientEnv.setUuId(isDp ? "" : envContext.getDeviceId());
        clientEnv.setDpId(isDp ? envContext.getDeviceId() : "");
        clientEnv.setUserId(ImAccountTypeUtils.getAccountId(context.getUserId()));
        clientEnv.setVersion(envContext.getAppVersion());
        clientEnv.setClientType(isDp ? ClientTypeEnum.DP_APP_ANDROID.getCode() : ClientTypeEnum.MT_APP_ANDROID.getCode());
        return clientEnv;
    }

    private List<DzRenderIdDTO> buildRenderId(long shopId) {
        List<DzRenderIdDTO> renderIds = Lists.newArrayList();
        DzRenderIdDTO dzRenderIdDTO = new DzRenderIdDTO();
        RenderIdDTO renderIdDTO = new RenderIdDTO();
        renderIdDTO.setItemId(shopId);
        renderIdDTO.setItemType(RenderItemTypeEnum.POI.getCode());
        dzRenderIdDTO.setRenderId(renderIdDTO);
        renderIds.add(dzRenderIdDTO);
        return renderIds;
    }

    private String getRecommend(String key) {
        int firstColonIndex = key.indexOf(':');
        if (firstColonIndex == -1) {
            return "";
        }

        int secondColonIndex = key.indexOf(':', firstColonIndex + 1);
        if (secondColonIndex == -1) {
            return "";
        }

        return key.substring(secondColonIndex + 1);
    }

    private boolean checkKey(String key) {
        if (StringUtils.isBlank(key)) {
            return false;
        }

        String[] splitKey = key.split(":");
        if (splitKey.length < 2) {
            return false;
        }
        // 第一个和第二个需要时数字
        return NumberUtils.isDigits(splitKey[0]) && NumberUtils.isDigits(splitKey[1]);
    }

    @Override
    public String cardDecode(StreamEventCardDataVO streamEventCardDataVO) {
        Map<String, Object> cardProps = streamEventCardDataVO.getCardProps();
        // 提取字段
        int index = (int) cardProps.get("index");
        String recommend = (String) cardProps.get("shopRecommend");
        ShopCardData shopCardData = JsonCodec.decode(JsonCodec.encodeWithUTF8(cardProps.get("shopCardData")), ShopCardData.class);
        if (shopCardData == null) {
            return "";
        }
        // 构建描述性字符串
        StringBuilder description = new StringBuilder();
        description.append("为您推荐第**" + index + "**个门店:").append("\n")
                .append(shopCardData.decodeDesc()).append("\n");
        if (StringUtils.isNotBlank(recommend)) {
            description.append("推荐理由:").append(recommend).append("\n");
        }
        return description.toString();
    }
}
