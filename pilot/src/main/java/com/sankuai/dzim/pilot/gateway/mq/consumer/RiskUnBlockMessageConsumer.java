package com.sankuai.dzim.pilot.gateway.mq.consumer;

import com.dianping.vc.mq.client.api.annotation.Consumer;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.gateway.mq.data.RiskUnBlockMessageData;
import com.sankuai.dzim.pilot.process.UserBlockProcessService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * 拉黑超时解禁
 */
@Slf4j
@Component
public class RiskUnBlockMessageConsumer {
    @Autowired
    private UserBlockProcessService userBlockProcessService;

    @Consumer(nameSpace = "daozong", appKey = "com.sankuai.mim.pilot", group = "pilot.assistant.risk.user.unblock", topic = "pilot.assistant.message.delaysend")
    public ConsumeStatus recvMessage(String riskBlackMsg) {
        if(StringUtils.isEmpty(riskBlackMsg)){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        try {
            RiskUnBlockMessageData riskUnBlockMessageData = JsonCodec.decode(riskBlackMsg, RiskUnBlockMessageData.class);
            if(riskUnBlockMessageData == null){
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            userBlockProcessService.execUnBlock(riskUnBlockMessageData);
        }catch (Exception e){
            LogUtils.logFailLog(log, TagContext.builder().action("RiskUnBlockMessageConsumer").build(),
                    new WarnMessage("RiskUnBlockMessageConsumer", "用户解禁消费者执行失败", null), riskBlackMsg, null, e);
            return ConsumeStatus.RECONSUME_LATER;
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
