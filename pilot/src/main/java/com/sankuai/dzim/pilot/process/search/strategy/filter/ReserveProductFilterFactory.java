package com.sankuai.dzim.pilot.process.search.strategy.filter;

import com.sankuai.dzim.pilot.process.search.data.ReserveQueryContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
public class ReserveProductFilterFactory {

    @Resource
    private List<ReserveProductFilterHandler> handlers;

    public List<ReserveProductFilterHandler> getHandler(ReserveQueryContext queryContext) {
        List<ReserveProductFilterHandler> result = new ArrayList<>();
        for (ReserveProductFilterHandler handler : handlers) {
            if (handler.match(queryContext)) {
                result.add(handler);
            }
        }
        return result;
    }
}
