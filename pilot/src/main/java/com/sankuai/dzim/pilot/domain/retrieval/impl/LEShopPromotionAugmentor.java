package com.sankuai.dzim.pilot.domain.retrieval.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.dzim.common.helper.FutureUtils;
import com.sankuai.clr.content.process.thrift.dto.PromotionDTO;
import com.sankuai.clr.content.process.thrift.dto.ShopBookInfoProcessDTO;
import com.sankuai.dzim.pilot.acl.LeadsAclService;
import com.sankuai.dzim.pilot.acl.MerchantPlatformContentAclService;
import com.sankuai.dzim.pilot.chain.enums.AIServiceExtraKeyEnum;
import com.sankuai.dzim.pilot.domain.annotation.Retrieval;
import com.sankuai.dzim.pilot.domain.retrieval.RetrievalAugmentor;
import com.sankuai.dzim.pilot.domain.retrieval.data.Knowledge;
import com.sankuai.dzim.pilot.domain.retrieval.data.RetrievalContext;
import com.sankuai.dzim.pilot.domain.retrieval.data.RetrievalResult;
import com.sankuai.dzim.pilot.utils.IMConstants;
import com.sankuai.mpmctcontent.query.thrift.dto.search.SearchDataDetailDTO;
import com.sankuai.mpmctcontent.query.thrift.dto.search.SearchDetailResponseDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Component
@Retrieval(name = "LEShopPromotionRetrieval", desc = "LE门店优惠、礼品信息检索器，主要有预约礼、超值特惠套餐等")
public class LEShopPromotionAugmentor implements RetrievalAugmentor {

    private static final String WEDDING_MARKETING_PACKAGE_SEARCH_SCENE = "wed_banquet_daily_marketing_by_shop_id";

    /**
     * 设置50，避免查询数据过多，传给大模型的数据也过多
     */
    private static final int MAX_FIND_DATA_SIZE = 50;

    @Autowired
    private MerchantPlatformContentAclService merchantPlatformContentAclService;

    @Autowired
    private LeadsAclService leadsAclService;

    @Override
    public String retrieval(RetrievalContext context) {
        long dpShopId = context.getLongDataFromExtraInfo(AIServiceExtraKeyEnum.DP_SHOP_ID.getKey());
        if (dpShopId == 0) {
            return StringUtils.EMPTY;
        }

        // 查询数据
        CompletableFuture<SearchDetailResponseDTO> shopMarketingPackagesFuture = merchantPlatformContentAclService.findShopContents(dpShopId, WEDDING_MARKETING_PACKAGE_SEARCH_SCENE, 0, MAX_FIND_DATA_SIZE);
        CompletableFuture<ShopBookInfoProcessDTO> shopBookingGiftFuture = leadsAclService.loadShopBookInfo(dpShopId);

        return CompletableFuture.allOf(shopMarketingPackagesFuture, shopBookingGiftFuture).thenApply(aVoid -> {
            SearchDetailResponseDTO shopMarketingPackages = FutureUtils.execute(shopMarketingPackagesFuture);
            ShopBookInfoProcessDTO shopBookingGift = FutureUtils.execute(shopBookingGiftFuture);
            return convertInfo2Text(shopMarketingPackages, shopBookingGift);
        }).join();
    }

    private String convertInfo2Text(SearchDetailResponseDTO shopMarketingPackages, ShopBookInfoProcessDTO shopBookingGift) {
        String shopBookingGiftInfo = getShopBookingGiftInfo(shopBookingGift);
        String shopMarketingPackagesInfo = getShopMarketingPackageInfo(shopMarketingPackages);

        return String.format("%s。\n%s",shopBookingGiftInfo,shopMarketingPackagesInfo);
    }

    private String getShopBookingGiftInfo(ShopBookInfoProcessDTO shopBookingGift) {
        StringBuilder sb = new StringBuilder();
        sb.append("你可以参考的门店优惠和礼品信息如下：").append("\n");
        if (shopBookingGift == null || CollectionUtils.isEmpty(shopBookingGift.getPromotions())) {
            return sb.append(IMConstants.NO_PLATFORM_GIFT_ANSWER_TEXT).toString();
        }

        // 数据拼接返回
        return shopBookingGift.getPromotions().stream().map(e -> getSingleBookingGiftInfo(e)).collect(Collectors.joining("\n"));
    }

    private String getSingleBookingGiftInfo(PromotionDTO promotionDTO) {
        StringBuilder sb = new StringBuilder();
        sb.append("礼品标题: ").append(promotionDTO.getKey()).append("\n");
        sb.append("礼品内容: ").append(promotionDTO.getValue()).append("\n");
        return sb.toString();
    }

    private String getShopMarketingPackageInfo(SearchDetailResponseDTO shopMarketingPackages) {
        StringBuilder sb = new StringBuilder();
        sb.append("你可以参考的门店超值特惠套餐优惠信息如下：").append("\n");
        if (shopMarketingPackages == null || CollectionUtils.isEmpty(shopMarketingPackages.getSearchResult())) {
            return sb.append(IMConstants.NO_MARKETING_PACKAGE_ANSWER_TEXT).toString();
        }

        // 数据拼接返回
        sb.append("门店拥有的超值特惠套餐数量: ").append(shopMarketingPackages.getTotalHitCount()).append("\n");
        String shopMarketingPackagesInfo = shopMarketingPackages.getSearchResult().stream().map(e -> getSingleMarketingPackageInfo(e)).collect(Collectors.joining("\n"));
        return sb.append(shopMarketingPackagesInfo).toString();
    }

    private String getSingleMarketingPackageInfo(SearchDataDetailDTO singleMarketingPackage) {
        if (singleMarketingPackage == null) {
            return StringUtils.EMPTY;
        }

        JSONObject jsonObject = JSON.parseObject(singleMarketingPackage.getDataInfo());
        String infoDesc = String.format("%s超值特惠套餐（packageId=%s，注意packageId不要暴露给用户）的信息如下：\n", jsonObject.getString("packageName"), singleMarketingPackage.getDataId());

        StringBuilder sb = new StringBuilder();
        sb.append("超值特惠套餐标题: ").append(jsonObject.getString("packageName")).append("\n");
        sb.append("超值特惠套餐库存: ").append(jsonObject.getString("stock")).append("\n");
        sb.append("超值特惠套餐原价: ").append(jsonObject.getString("originalPrice")).append("\n");
        sb.append("超值特惠套餐特惠价格: ").append(jsonObject.getString("specialPrice")).append("\n");
        return infoDesc.concat(sb.toString());
    }

    @Override
    public RetrievalResult retrieveKnowledge(RetrievalContext context) {
        return null;
    }
}
