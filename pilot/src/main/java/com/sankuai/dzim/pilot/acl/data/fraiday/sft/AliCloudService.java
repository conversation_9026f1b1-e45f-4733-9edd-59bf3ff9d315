package com.sankuai.dzim.pilot.acl.data.fraiday.sft;

import com.sankuai.dzim.pilot.acl.data.fraiday.ChatCompletionRequest;
import com.sankuai.dzim.pilot.acl.data.fraiday.ChatCompletionResponse;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.Header;
import retrofit2.http.POST;

public interface AliCloudService {

    /**
     * 阿里云微调模型调用接口
     *
     * @param date    鉴权使用
     * @param auth    鉴权使用
     * @param request 请求参数
     * @return
     */
    @POST("/dzpilot/sft/completion")
    Call<ChatCompletionResponse> createChatCompletion(@Header("x-acs-date") String date, @Header("authorization") String auth, @Body ChatCompletionRequest request);
}
