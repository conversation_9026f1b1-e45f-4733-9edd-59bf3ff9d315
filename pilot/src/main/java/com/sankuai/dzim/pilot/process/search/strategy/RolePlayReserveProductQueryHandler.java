package com.sankuai.dzim.pilot.process.search.strategy;

import com.dianping.vc.sdk.dp.pic.PictureURLBuilders;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.dzim.pilot.process.aireservebook.enums.POIIndustryType;
import com.sankuai.dzim.pilot.process.data.ProductDetailData;
import com.sankuai.dzim.pilot.process.data.ProductPoolData;
import com.sankuai.dzim.pilot.process.data.ProductRetrievalData;
import com.sankuai.dzim.pilot.process.data.ProductStockData;
import com.sankuai.dzim.pilot.process.search.data.*;
import com.sankuai.dzim.pilot.process.search.enums.IndustryBackendCategoryEnum;
import com.sankuai.dzim.pilot.process.search.enums.PlatformCategoryEnum;
import com.sankuai.dzim.pilot.process.search.enums.ProductFilterOptionEnum;
import com.sankuai.dzim.pilot.process.search.strategy.padding.ReserveProductPaddingFactory;
import com.sankuai.dzim.pilot.process.search.strategy.padding.ReserveProductPaddingHandler;
import com.sankuai.dzim.pilot.process.search.strategy.recall.ReserveProductRecallFactory;
import com.sankuai.dzim.pilot.process.search.strategy.recall.ReserveProductRecallHandler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class RolePlayReserveProductQueryHandler implements ReserveProductQueryHandler {

    private static final List<Integer> ROLE_PLAY_CATEGORY_IDS = IndustryBackendCategoryEnum.ROLE_PLAY.getCategoryIds();
    private final String ROLE_PLAY_PLAN_ID = "10101119";

    @Resource
    private ReserveProductRecallFactory reserveProductRecallFactory;

    @Resource
    private ReserveProductPaddingFactory reserveProductPaddingFactory;

    @Override
    public boolean match(ReserveQueryContext queryContext) {
        if (queryContext == null || queryContext.getShopBackCategoryId() <= 0) {
            return false;
        }
        return ROLE_PLAY_CATEGORY_IDS.contains(queryContext.getShopBackCategoryId());
    }

    @Override
    public List<ProductRetrievalData> execute(ReserveQueryContext queryContext) {

        // 加入行业个性化上下文参数
        ReserveQueryContext ctx = fillSpecialContext(queryContext);

        // 根据poiId召回商品id
        List<ReserveProductM> recallProducts = queryShopProductIds(ctx);

        // 根据商品id列表填充商品信息
        List<ReserveProductM> paddingProducts = paddingProductInfo(recallProducts, ctx);

        // 商品分组聚合成展示商品
        List<ReserveProductM> groupProducts = reorganization(paddingProducts);

        // 构建门店预订商品返回结果
        return buildProductRetrievalDataList(queryContext, groupProducts);
    }

    private List<ReserveProductM> paddingProductInfo(List<ReserveProductM> recallProducts, ReserveQueryContext ctx) {
        ReserveProductPaddingHandler handler = reserveProductPaddingFactory.getHandler(ctx);
        if (handler == null) {
            return Lists.newArrayList();
        }
        return handler.padding(recallProducts, ctx);
    }

    /**
     * 对门店所有在线商品进行分组
     */
    private List<ReserveProductM> reorganization(List<ReserveProductM> paddingProducts) {
        // 按照销量（ReserveProductM::sale::sale）从大到小排序,如果销量对象为空，则置底
        paddingProducts.sort((p1, p2) -> {
            if (p1.getSale() == null && p2.getSale() == null) {
                return 0;
            }
            if (p1.getSale() == null) {
                return 1;
            }
            if (p2.getSale() == null) {
                return -1;
            }
            return p2.getSale().getSale() - p1.getSale().getSale();
        });
        // 至多保留销量前10的商品
        return paddingProducts.stream().limit(10).collect(Collectors.toList());
    }

    private List<ProductRetrievalData> buildProductRetrievalDataList(ReserveQueryContext queryContext,
            List<ReserveProductM> paddingProducts) {
        if (CollectionUtils.isEmpty(paddingProducts)) {
            return Lists.newArrayList();
        }
        return paddingProducts.stream().map(reserveProductM -> buildProductRetrievalData(queryContext, reserveProductM))
                .collect(Collectors.toList());
    }

    private ProductRetrievalData buildProductRetrievalData(ReserveQueryContext queryContext,
            ReserveProductM reserveProductM) {
        ProductRetrievalData productRetrievalData = new ProductRetrievalData();
        productRetrievalData.setProductType(2);
        productRetrievalData.setCategoryId((long)POIIndustryType.ROLE_PLAY.getSecondBackCategoryId());
        productRetrievalData.setMtProductId(reserveProductM.getPlatformProductId());
        productRetrievalData.setHeadPic(buildHeadPic(reserveProductM));
        productRetrievalData.setTitle(buildTitle(reserveProductM));
        productRetrievalData.setSubTitles(buildSubTitles(reserveProductM));
        productRetrievalData.setSalesNum(buildSalesNum(reserveProductM));
        productRetrievalData.setSalePrice(buildSalePrice(reserveProductM));
        productRetrievalData.setStocks(buildStocks(reserveProductM));
        productRetrievalData.setProductDetails(buildProductDetails(reserveProductM));
        productRetrievalData.setPoolData(buildPoolDate(reserveProductM));
        return productRetrievalData;
    }

    private List<ProductPoolData> buildPoolDate(ReserveProductM reserveProductM) {
        if (reserveProductM == null || CollectionUtils.isEmpty(reserveProductM.getPinPools())) {
            return Collections.emptyList();
        }

        return reserveProductM.getPinPools().stream().filter(Objects::nonNull).map(this::buildSkuPoolData)
                .collect(Collectors.toList());
    }

    private ProductPoolData buildSkuPoolData(ProductPinPoolM productPinPoolM) {
        if (productPinPoolM == null) {
            return null;
        }
        ProductPoolData productPoolData = new ProductPoolData();
        productPoolData.setDescription(productPinPoolM.getDescription());
        productPoolData.setMinNum(productPinPoolM.getMinNum());
        productPoolData.setMaxNum(productPinPoolM.getMaxNum());
        productPoolData.setCurrentNum(productPinPoolM.getCurrentNum());
        productPoolData.setPoolStatus(productPinPoolM.getPoolStatus());
        productPoolData.setPoolType(productPinPoolM.getPoolType());
        productPoolData.setBookStartTime(productPinPoolM.getBookStartTime());
        productPoolData.setCanJoin(productPinPoolM.isCanJoin());
        return productPoolData;
    }

    /**
     * 密室商品详情需要构建：商品信息、图文详情、拼场规则
     */
    private ProductDetailData buildProductDetails(ReserveProductM reserveProductM) {
        ProductDetailData productDetailData = new ProductDetailData();
        productDetailData.setBasicProductInfo(buildBasicProductInfo(reserveProductM));
        productDetailData.setDescription(buildDescription(reserveProductM));
        productDetailData.setPoolRules(buildPoolRules(reserveProductM));
        productDetailData.setSaleInfo(buildSaleInfo(reserveProductM));
        return productDetailData;
    }

    private Map<String, Object> buildSaleInfo(ReserveProductM reserveProductM) {
        if (reserveProductM == null) {
            return Collections.emptyMap();
        }

        Map<String, Object> saleInfoMap = new HashMap<>();
        saleInfoMap.put("最大开场人数", reserveProductM.getAttr("maxpeoplenum"));
        saleInfoMap.put("最小开场人数", reserveProductM.getAttr("minpeoplenum"));
        saleInfoMap.put("商品适用人数", reserveProductM.getAttr("productAdviceNum"));
        return saleInfoMap;
    }

    private Map<String, Object> buildPoolRules(ReserveProductM reserveProductM) {
        if (reserveProductM == null) {
            return Collections.emptyMap();
        }

        Map<String, Object> poolRulesMap = new HashMap<>();
        poolRulesMap.put("是否开放拼场", reserveProductM.getAttr("isOpenPool"));

        return poolRulesMap;
    }

    private Map<String, Object> buildDescription(ReserveProductM reserveProductM) {
        if (reserveProductM == null) {
            return Collections.emptyMap();
        }

        Map<String, Object> descriptionMap = new HashMap<>();
        descriptionMap.put("介绍图", reserveProductM.getAttr("piclist"));
        descriptionMap.put("剧情简介", reserveProductM.getAttr("synopsis"));
        descriptionMap.put("主题描述", reserveProductM.getAttr("desc"));

        return descriptionMap;
    }

    private Map<String, String> buildBasicProductInfo(ReserveProductM reserveProductM) {
        if (reserveProductM == null) {
            return Collections.emptyMap();
        }

        Map<String, String> basicProductInfo = new HashMap<>();
        basicProductInfo.put("主题类型", reserveProductM.getAttr("larpCategory"));
        basicProductInfo.put("难度系数", reserveProductM.getAttr("difficultyDegree"));
        basicProductInfo.put("主题标签", "");
        basicProductInfo.put("主标签", reserveProductM.getAttr("mainTagObject"));
        basicProductInfo.put("恐怖程度", reserveProductM.getAttr("terrorLevel"));
        basicProductInfo.put("有无换装", reserveProductM.getAttr("isChangeCloth"));
        basicProductInfo.put("年龄限制", reserveProductM.getAttr("ageLimit"));
        basicProductInfo.put("推荐人群", reserveProductM.getAttr("recommendedPeople"));
        basicProductInfo.put("空间面积", reserveProductM.getAttr("space_type"));
        basicProductInfo.put("多重结局", reserveProductM.getAttr("multipleEnd"));
        basicProductInfo.put("店内营业开始时间", reserveProductM.getAttr("saleBeginTime"));
        basicProductInfo.put("店内营业结束时间", reserveProductM.getAttr("saleEndTime"));
        basicProductInfo.put("主题时长", reserveProductM.getAttr("duration"));
        basicProductInfo.put("开场时间间隔", reserveProductM.getAttr("beginTimeDuration"));

        return basicProductInfo;
    }

    private String buildSalePrice(ReserveProductM reserveProductM) {
        if (Objects.isNull(reserveProductM)) {
            return null;
        }
        if (StringUtils.isNotEmpty(reserveProductM.getSalePriceTag())) {
            return reserveProductM.getSalePriceTag();
        }
        if (CollectionUtils.isEmpty(reserveProductM.getProductItemMList())) {
            return null;
        }
        List<ProductItemM> productItemMList = reserveProductM.getProductItemMList();
        // 该主题下挂场次均不可订，取最低价的场次；否则取最低价的可订场次
        boolean allUnBookable = productItemMList.stream().noneMatch(ProductItemM::getCanBook);
        ProductItemM lowestPriceItem = allUnBookable ? getLowestPriceItemIgnoringBookable(productItemMList)
                : getLowestPriceItemForBookable(productItemMList);

        return Optional.ofNullable(lowestPriceItem).map(ProductItemM::getPrice).orElse(null);
    }

    /**
     * 获取密室主题下挂场次均不可订态下的最低价场次
     */
    public static ProductItemM getLowestPriceItemIgnoringBookable(List<ProductItemM> productItemMList) {
        return productItemMList.stream()
                .filter(item -> org.apache.commons.lang3.StringUtils.isNotEmpty(item.getPrice()))
                .min(Comparator.comparing(item -> new BigDecimal(item.getPrice()))).orElse(null);
    }

    /**
     * 获取密室主题下挂可订场次的最低价场次
     */
    public static ProductItemM getLowestPriceItemForBookable(List<ProductItemM> productItemMList) {
        return productItemMList.stream().filter(ProductItemM::getCanBook)
                .filter(item -> org.apache.commons.lang3.StringUtils.isNotEmpty(item.getPrice()))
                .min(Comparator.comparing(item -> new BigDecimal(item.getPrice()))).orElse(null);
    }

    /**
     * 主商品下任意一个套餐时间片段可订，那主商品在这个套餐就是可订的
     */
    public ProductStockData buildStocks(ReserveProductM reserveProductM) {
        if (reserveProductM == null || reserveProductM.getBaseState() == null) {
            return null;
        }
        ProductBaseStateM baseState = reserveProductM.getBaseState();
        ProductStockData productStockData = new ProductStockData();
        productStockData.setEarliestBookableTime(baseState.getEarliestBookableTime());
        productStockData.setTimeSlices(mergeTimeSlice(reserveProductM));
        return productStockData;
    }

    private List<TimeSliceM> mergeTimeSlice(ReserveProductM reserveProductM) {
        ProductBaseStateM baseState = reserveProductM.getBaseState();
        List<TimeSliceM> timeSlices = baseState.getTimeSlices();
        if (CollectionUtils.isEmpty(timeSlices)) {
            return Lists.newArrayList();
        }
        if (timeSlices.size() == 1) {
            return timeSlices;
        }

        return processMerge(timeSlices);
    }

    private List<TimeSliceM> processMerge(List<TimeSliceM> timeSlices) {
        // 排序
        timeSlices.sort(Comparator.comparing(TimeSliceM::getStartTime));
        // 遍历并合并相邻的库存状态相同的时段
        List<TimeSliceM> mergedTimeSlices = new ArrayList<>();
        TimeSliceM currentSlice = timeSlices.get(0);
        mergedTimeSlices.add(currentSlice);
        for (int i = 1; i < timeSlices.size(); i++) {
            TimeSliceM nextSlice = timeSlices.get(i);
            if (nextSlice.isAvailable() == currentSlice.isAvailable()) {
                // 合并相邻的库存状态相同的时段
                currentSlice.setEndTime(nextSlice.getEndTime());
            } else {
                currentSlice = nextSlice;
                mergedTimeSlices.add(currentSlice);
            }
        }
        return mergedTimeSlices;
    }

    /**
     * 取密室主题销量
     */
    private Long buildSalesNum(ReserveProductM reserveProductM) {
        ProductSaleM productSaleM = reserveProductM.getSale();
        if (productSaleM == null || productSaleM.getSale() <= 0) {
            return 0L;
        }
        return (long)productSaleM.getSale();
    }

    private List<String> buildSubTitles(ReserveProductM productM) {
        if (productM == null) {
            return Lists.newArrayList();
        }
        String personNumTag = productM.getAttr("productAdviceNum");
        String inActiveDifficultyLevel = productM.getAttr("difficultyDegree");
        String stylesStr = productM.getAttr("rolePlayThemeTypeLimit3");
        return Lists.newArrayList(personNumTag, stylesStr, inActiveDifficultyLevel).stream()
                .filter(StringUtils::isNotBlank).collect(Collectors.toList());
    }

    private String buildTitle(ReserveProductM reserveProductM) {
        if (reserveProductM == null) {
            return null;
        }
        return reserveProductM.getTitle();
    }

    private String buildHeadPic(ReserveProductM reserveProductM) {
        if (reserveProductM == null || StringUtils.isEmpty(reserveProductM.getPicUrl())) {
            return null;
        }
        return PictureURLBuilders.toHttpsUrl(reserveProductM.getPicUrl(), 252, 252, PictureURLBuilders.ScaleType.Cut);
    }

    private ReserveQueryContext fillSpecialContext(ReserveQueryContext queryContext) {
        if (queryContext == null) {
            return null;
        }
        // 泛商品主题方案id
        queryContext.setPlanId(ROLE_PLAY_PLAN_ID);
        queryContext.setExtra(buildExtra());
        queryContext.setPlatformCategoryId(PlatformCategoryEnum.ROLE_PLAY.getCode());
        queryContext.setFilterOptions(buildFilterOptions(queryContext));
        return queryContext;
    }

    private List<FilterOption> buildFilterOptions(ReserveQueryContext queryContext) {
        if (queryContext == null) {
            return Lists.newArrayList();
        }
        List<FilterOption> filterOptions = queryContext.getFilterOptions();
        if (CollectionUtils.isEmpty(filterOptions)) {
            filterOptions = Lists.newArrayList();
        }
        filterOptions.add(buildProductSalesFilterOption());
        return filterOptions;
    }

    private FilterOption buildProductSalesFilterOption() {
        FilterOption filterOption = new FilterOption();
        filterOption.setFilterType(ProductFilterOptionEnum.PRODUCT_SALES.getCode());
        filterOption.setValues(buildSalesFilterValues());
        return filterOption;
    }

    private Map<String, Object> buildSalesFilterValues() {
        Map<String, Object> values = Maps.newHashMap();
        values.put("salesRankLimit", 10);
        return values;
    }

    private Map<String, Object> buildExtra() {
        Map<String, Object> extra = Maps.newHashMap();
        extra.put("dealAttrs", Sets.newHashSet("week"));
        return extra;
    }

    private List<ReserveProductM> queryShopProductIds(ReserveQueryContext ctx) {
        ReserveProductRecallHandler handler = reserveProductRecallFactory.getHandler(ctx);
        if (handler == null) {
            return Lists.newArrayList();
        }
        return handler.recall(ctx);
    }
}
