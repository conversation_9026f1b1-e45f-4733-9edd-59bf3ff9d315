package com.sankuai.dzim.pilot.process.impl.assistant;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.message.common.utils.ImAccountTypeUtils;
import com.sankuai.dzim.pilot.acl.ShopAclService;
import com.sankuai.dzim.pilot.acl.ProductAclService;
import com.sankuai.dzim.pilot.process.AssistantRetrievalProcessService;
import com.sankuai.dzim.pilot.process.data.ShopRetrievalData;
import com.sankuai.dzim.pilot.utils.DateUtils;
import com.sankuai.dzim.pilot.utils.DistanceUtils;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.sinai.data.api.dto.MtPoiDTO;
import com.sankuai.sinai.data.api.dto.PoiSubwayView;
import com.sankuai.sinai.data.api.dto.TypeHierarchyView;
import com.sankuai.sinai.data.api.util.MtPoiUtil;
import com.taobao.tair3.client.Result;
import com.taobao.tair3.client.error.TairFlowLimit;
import com.taobao.tair3.client.error.TairRpcError;
import com.taobao.tair3.client.error.TairTimeout;
import com.taobao.tair3.client.impl.MultiTairClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/2/24 11:05
 */
@Slf4j
@Service
public class AssistantRetrievalProcessServiceImpl implements AssistantRetrievalProcessService {

    @Resource
    private ShopAclService shopAclService;

    @Autowired
    private ProductAclService productAclService;

    @Autowired
    @Qualifier("userCellar")
    private MultiTairClient userCellar;

    //垂搜页用户行为序列前缀
    public static final String MT_USER_CUSTOM_KEY = "dz_yhtz_track_custom_mt_";

    public static final String DP_USER_CUSTOM_KEY = "dz_yhtz_track_custom_dp_";

    //todo 频道页用户行为序列前缀


    public static final short AREA_ID = 1;

    public static final String SHOP_ID_MAP_KEY = "shopidlist";

    public static final String ACTION_MAP_KEY = "actionlist";

    public static final String TYPE_MAP_KEY = "typelist";

    public static final String ITEM_ID_MAP_KEY = "itemidlist";

    public static final String KEY_WORD_LIST_KEY = "keywordlist";

    public static final String TIMESTAMP_MAP_KEY = "timestamplist";

    public static final String TYPE_MAP_DEAL = "deal";

    public static final String TYPE_MAP_POI = "poi";


    @Override
    public Map<Long, ShopRetrievalData> batchQueryShopRetrievalData(List<Long> mtShopIds, double userLng, double userLat) {
        Map<Long, ShopRetrievalData> result = Maps.newHashMap();
        try {
            Map<Long, MtPoiDTO> mtShopId2MtPoiDTO = shopAclService.batchGetMtShopInfo(mtShopIds);
            if (MapUtils.isEmpty(mtShopId2MtPoiDTO)) {
                return Maps.newHashMap();
            }
            for (Long mtShopId : mtShopIds) {
                if (!mtShopId2MtPoiDTO.containsKey(mtShopId)) {
                    continue;
                }
                MtPoiDTO mtPoiDTO = mtShopId2MtPoiDTO.get(mtShopId);
                result.put(mtShopId, buildShopRetrievalData(mtPoiDTO, userLng, userLat));
            }
            return result;
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("batchQueryShopRetrievalData").build(),
                    new WarnMessage("batchQueryShopRetrievalData", "批量查询门店检索数据失败", ""), mtShopIds, e);
            return result;
        }
    }

    @Override
    public CompletableFuture<Map<Long, ShopRetrievalData>> batchQueryShopRetrievalDataAsync(List<Long> mtShopIds, double userLng, double userLat) {
        if (CollectionUtils.isEmpty(mtShopIds)) {
            return CompletableFuture.completedFuture(Maps.newHashMap());
        }
        try {
            return shopAclService.batchGetMtShopInfoAsync(mtShopIds).thenApply(mtShopId2MtPoiDTO -> {
                if (MapUtils.isEmpty(mtShopId2MtPoiDTO)) {
                    return new HashMap<Long, ShopRetrievalData>();
                }
                Map<Long, ShopRetrievalData> result = Maps.newHashMap();
                for (Long mtShopId : mtShopIds) {
                    if (!mtShopId2MtPoiDTO.containsKey(mtShopId)) {
                        continue;
                    }
                    MtPoiDTO mtPoiDTO = mtShopId2MtPoiDTO.get(mtShopId);
                    result.put(mtShopId, buildShopRetrievalData(mtPoiDTO, userLng, userLat));
                }
                return result;
            }).exceptionally(e -> {
                LogUtils.logFailLog(log, TagContext.builder().action("batchQueryShopRetrievalDataAsync").build(),
                        new WarnMessage("batchQueryShopRetrievalDataAsync", "异步批量查询门店检索数据失败", ""), mtShopIds, e);
                return new HashMap<>();
            });
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("batchQueryShopRetrievalDataAsync").build(),
                    new WarnMessage("batchQueryShopRetrievalDataAsync", "异步批量查询门店检索数据失败", ""), mtShopIds, e);
            return CompletableFuture.completedFuture(Maps.newHashMap());
        }
    }

    /**
     * 根据 imUserId生成查询频道搜索页cellar key
     * @param imUserId
     * @return
     */
    private String genQuerySearchListingActionKey(String imUserId) {
        return ImAccountTypeUtils.isDpUserId(imUserId) ? DP_USER_CUSTOM_KEY : MT_USER_CUSTOM_KEY +
                ImAccountTypeUtils.getAccountId(imUserId);
    }

    @Override
    public List<ShopRetrievalData> queryUserOperationShopRetrievalData(String imUserId, int maxQueryCnt, double userLng, double userLat) {
        try {
            String queryKey = genQuerySearchListingActionKey(imUserId);

            Result<byte[]> result = userCellar.mapGet(AREA_ID, queryKey.getBytes(), SHOP_ID_MAP_KEY.getBytes(), null);
            if (!Result.ResultCode.OK.equals(result.getCode()) || result.getResult() == null) {
                return Lists.newArrayList();
            }
            List<Long> shopIds = JsonCodec.decode(new String(result.getResult()), new TypeReference<List<Long>>() {
            });
            if (CollectionUtils.isEmpty(shopIds)) {
                return Lists.newArrayList();
            }
            List<Long> mtShopIds = buildMtShopIds(imUserId, shopIds);
            List<Long> targetMtShopIds = mtShopIds.subList(0, Math.min(maxQueryCnt, shopIds.size()));
            Map<Long, ShopRetrievalData> mtShopId2Retrieval = batchQueryShopRetrievalData(targetMtShopIds, userLng, userLat);
            if (MapUtils.isEmpty(mtShopId2Retrieval)) {
                return Lists.newArrayList();
            }
            List<ShopRetrievalData> dataList = targetMtShopIds.stream().map(mtShopId2Retrieval::get).collect(Collectors.toList());
            //填充用户操作行为和时间
            paddingActionTime(queryKey, targetMtShopIds, dataList);
            return dataList;
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("queryUserOperationShopRetrievalData").build(),
                    new WarnMessage("queryUserOperationShopRetrievalData", "查询用户访问门店异常", ""), imUserId, e);
            return Lists.newArrayList();
        }
    }

    /**
     * 查询用户操作店铺和团单
     * @param imUserId
     * @param userLng
     * @param userLat
     * @return Pair<List<ShopRetrievalData>, List<ShopRetrievalData>> 店铺和团单 店铺在前 团单在后
     */
    @Override
    public Pair<List<ShopRetrievalData>, List<ShopRetrievalData>> queryUserOperationShopAndDealGroupDTO(String imUserId, int maxQueryCnt, double userLng, double userLat) {
        try {
            List<ShopRetrievalData> dataOp = Lists.newArrayList();

            //垂搜页查询用户行为，拿到操作店铺和团单所在店铺
            dataOp = queryUserOperationShopRetrievalData(imUserId, maxQueryCnt, userLng, userLat);
            paddingItemIdTypeKeyWord(imUserId, dataOp);

            //店铺操作信息
            List<ShopRetrievalData> shopUserActionData = dataOp.stream().filter(item -> Objects.equals(item.getItemType(), TYPE_MAP_POI)).collect(Collectors.toList());

            //团单操作信息
            List<ShopRetrievalData> dealUserActionData = dataOp.stream().filter(item -> Objects.equals(item.getItemType(), TYPE_MAP_DEAL)).collect(Collectors.toList());
            List<DealGroupDTO> dealGroupDTOS = queryUserOperationDpDealGroupDTO(imUserId, dealUserActionData);
            paddingDealGroupDTO(dealUserActionData, dealGroupDTOS);

            return Pair.of(shopUserActionData, dealUserActionData);
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("queryUserOperationShopAndDealGroupDTO").build(),
                    new WarnMessage("queryUserOperationShopAndDealGroupDTO", "查询用户访问门店和团单异常", ""), imUserId, e);
            return Pair.of(Lists.newArrayList(), Lists.newArrayList());
        }
    }

    private List<DealGroupDTO> queryUserOperationDpDealGroupDTO(String imUserId, List<ShopRetrievalData> dealRetrievalDataOp) {
        List<Long> dealGroupIds = dealRetrievalDataOp.stream().map(ShopRetrievalData::getItemId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dealGroupIds)) {
            return Collections.emptyList();
        }

        //获取DP dealID
        List<Long> dealDpGroupIds = buildDpDealIds(imUserId, dealGroupIds);
        Map<Long, DealGroupDTO> dealGroupDTOS = productAclService.batchGetDealBaseInfo(dealDpGroupIds);
        if (MapUtils.isEmpty(dealGroupDTOS)) {
            return Collections.emptyList();
        }

        return new ArrayList<>(dealGroupDTOS.values());
    }



    /**
     * 针对团单操作行为填充团单信息
     * @param dealUserActionDatas
     * @param dealGroupDTOS
     */
    private void paddingDealGroupDTO(List<ShopRetrievalData> dealUserActionDatas, List<DealGroupDTO> dealGroupDTOS) {
        Map<Long, DealGroupDTO> dealGroupDTOSMap = dealGroupDTOS.stream().collect(Collectors.toMap(DealGroupDTO::getDpDealGroupId, Function.identity()));

        for (ShopRetrievalData dealUserAction : dealUserActionDatas) {
            if (dealGroupDTOSMap.containsKey(dealUserAction.getItemId())) {
                dealUserAction.setDealGroupDTO(dealGroupDTOSMap.get(dealUserAction.getItemId()));
            }
        }
    }

    private void paddingActionTime(String queryKey, List<Long> targetMtShopIds, List<ShopRetrievalData> dataList) {
        try {
            Result<byte[]> actionResult = userCellar.mapGet(AREA_ID, queryKey.getBytes(), ACTION_MAP_KEY.getBytes(), null);
            List<String> actions = JsonCodec.decode(new String(actionResult.getResult()), new TypeReference<List<String>>() {
            });
            Result<byte[]> timeResult = userCellar.mapGet(AREA_ID, queryKey.getBytes(), TIMESTAMP_MAP_KEY.getBytes(), null);
            List<Long> times = JsonCodec.decode(new String(timeResult.getResult()), new TypeReference<List<Long>>() {
            });
            if (CollectionUtils.isEmpty(actions) || CollectionUtils.isEmpty(times)) {
                return;
            }
            for (int i = 0; i < targetMtShopIds.size(); i++) {
                String action = actions.get(i);
                Long time = times.get(i);
                ShopRetrievalData shopRetrievalData = dataList.get(i);
                if ("click".equals(action)) {
                    shopRetrievalData.setUserOperation("点击");
                }
                if ("view".equals(action)) {
                    shopRetrievalData.setUserOperation("曝光");
                }
                if (Lists.newArrayList("ord", "pay", "atc").contains(action)) {
                    shopRetrievalData.setUserOperation("交易");
                }
                shopRetrievalData.setUserOperationTime(DateUtils.covertDateStr(new Date(time)));
            }
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("paddingActionTime").build(),
                    new WarnMessage("paddingActionTime", "查询用户访问门店异常", ""), targetMtShopIds, e);
        }
    }

    /**
     * 填充商品ID、类型和关键词
     * @param imUserId String
     * @param dataList List<ShopRetrievalData>
     */
    private void paddingItemIdTypeKeyWord(String imUserId, List<ShopRetrievalData> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }
        
        String queryKey = genQuerySearchListingActionKey(imUserId);
        try {
            // 获取并解析所有需要的数据
            List<String> itemTypes = getItemTypes(queryKey);
            List<Long> itemIds = getItemIds(queryKey);
            List<String> keywords = getKeywords(queryKey);
            
            // 验证数据完整性
            if (!isDataValid(itemTypes, itemIds, keywords, dataList.size())) {
                return;
            }
            
            // 填充数据
            fillDataToShopRetrievalData(dataList, itemTypes, itemIds, keywords);
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("paddingItemIdTypeKeyWord").build(),
                    new WarnMessage("paddingItemIdTypeKeyWord", "查询用户访问门店异常", ""), dataList, e);
        }
    }
    
    /**
     * 获取商品类型列表
     */
    private List<String> getItemTypes(String queryKey) throws TairTimeout, InterruptedException, TairFlowLimit, TairRpcError {
        Result<byte[]> itemTypeResult = userCellar.mapGet(AREA_ID, queryKey.getBytes(), TYPE_MAP_KEY.getBytes(), null);
        if (itemTypeResult == null || itemTypeResult.getResult() == null) {
            return Collections.emptyList();
        }
        return JsonCodec.decode(new String(itemTypeResult.getResult()), new TypeReference<List<String>>() {});
    }
    
    /**
     * 获取商品ID列表
     */
    private List<Long> getItemIds(String queryKey) throws TairTimeout, InterruptedException, TairFlowLimit, TairRpcError {
        Result<byte[]> itemIdResult = userCellar.mapGet(AREA_ID, queryKey.getBytes(), ITEM_ID_MAP_KEY.getBytes(), null);
        if (itemIdResult == null || itemIdResult.getResult() == null) {
            return Collections.emptyList();
        }
        return JsonCodec.decode(new String(itemIdResult.getResult()), new TypeReference<List<Long>>() {});
    }
    
    /**
     * 获取关键词列表
     */
    private List<String> getKeywords(String queryKey) throws TairTimeout, InterruptedException, TairFlowLimit, TairRpcError {
        Result<byte[]> keywordResult = userCellar.mapGet(AREA_ID, queryKey.getBytes(), KEY_WORD_LIST_KEY.getBytes(), null);
        if (keywordResult == null || keywordResult.getResult() == null) {
            return Collections.emptyList();
        }
        return JsonCodec.decode(new String(keywordResult.getResult()), new TypeReference<List<String>>() {});
    }
    
    /**
     * 验证数据完整性
     */
    private boolean isDataValid(List<String> itemTypes, List<Long> itemIds, List<String> keywords, int expectedSize) {
        return !CollectionUtils.isEmpty(itemTypes) 
            && !CollectionUtils.isEmpty(itemIds) 
            && !CollectionUtils.isEmpty(keywords)
            && itemTypes.size() >= expectedSize
            && itemIds.size() >= expectedSize
            && keywords.size() >= expectedSize;
    }
    
    /**
     * 填充数据到ShopRetrievalData对象
     */
    private void fillDataToShopRetrievalData(List<ShopRetrievalData> dataList, 
                                            List<String> itemTypes, 
                                            List<Long> itemIds, 
                                            List<String> keywords) {
        for (int i = 0; i < dataList.size(); i++) {
            ShopRetrievalData shopRetrievalData = dataList.get(i);
            shopRetrievalData.setItemType(itemTypes.get(i));
            shopRetrievalData.setItemId(itemIds.get(i));
            shopRetrievalData.setKeyword(keywords.get(i));
        }
    }

    private List<Long> buildMtShopIds(String imUserId, List<Long> shopIds) {
        if (ImAccountTypeUtils.isMtUserId(imUserId)) {
            return shopIds;
        }
        Map<Long, Long> dpShopId2MtShopId = shopAclService.queryMtIdLByDpIdL(shopIds).join();
        return shopIds.stream().map(dpShopId2MtShopId::get).collect(Collectors.toList());
    }

    /**
     *
     * @param imUserId
     * @param dealIds
     * @return
     */
    private List<Long> buildDpDealIds(String imUserId, List<Long> dealIds) {
        if (ImAccountTypeUtils.isDpUserId(imUserId)) {
            return dealIds;
        }
        List<DealGroupDTO> dealGroups= productAclService.queryDpDealIdByMtDealId(dealIds);
        return dealGroups.stream().map(DealGroupDTO::getDpDealGroupId).collect(Collectors.toList());
    }

    private ShopRetrievalData buildShopRetrievalData(MtPoiDTO mtPoiDTO, double userLng, double userLat) {
        ShopRetrievalData shopRetrievalData = new ShopRetrievalData();
        shopRetrievalData.setMtShopId(mtPoiDTO.getMtPoiId());
        shopRetrievalData.setShopName(MtPoiUtil.getMtPoiName(mtPoiDTO.getName(), mtPoiDTO.getBranchName()));
        shopRetrievalData.setMtAvgPrice(mtPoiDTO.getMtAvgPrice());
        shopRetrievalData.setMtAvgScore(mtPoiDTO.getMtAvgScore());
        List<TypeHierarchyView> typeHierarchy = mtPoiDTO.getTypeHierarchy();
        shopRetrievalData.setMtCateName0(typeHierarchy.get(typeHierarchy.size() - 1).getName());
        shopRetrievalData.setMtCateName1(typeHierarchy.size() > 1 ? typeHierarchy.get(typeHierarchy.size() - 2).getName() : StringUtils.EMPTY);
        shopRetrievalData.setMtCateName2(typeHierarchy.size() > 2 ? typeHierarchy.get(typeHierarchy.size() - 3).getName() : StringUtils.EMPTY);
        shopRetrievalData.setMtCityName(mtPoiDTO.getMtCityLocationName());
        shopRetrievalData.setMtDistrictName(mtPoiDTO.getMtLocationName());
        shopRetrievalData.setMtRegionName(mtPoiDTO.getMtBareaName());
        shopRetrievalData.setShopAddress(MtPoiUtil.getMtPoiAddress(mtPoiDTO.getAddress(), mtPoiDTO.getReferenceAddress()));
        List<PoiSubwayView> poiSubways = mtPoiDTO.getPoiSubways();
        shopRetrievalData.setSubwayStation(CollectionUtils.isEmpty(poiSubways) ? StringUtils.EMPTY : poiSubways.get(0).getName());
        shopRetrievalData.setBrandName(StringUtils.isEmpty(mtPoiDTO.getBrandName()) ? StringUtils.EMPTY : mtPoiDTO.getBrandName());
        double distance = DistanceUtils.getDistance(userLng, userLat, mtPoiDTO.getLatitude(), mtPoiDTO.getLatitude());
        shopRetrievalData.setUserDistance(NumberUtils.toInt(String.valueOf(distance)));
        return shopRetrievalData;
    }
}
