package com.sankuai.dzim.pilot.process.search.strategy;

import com.sankuai.dzim.pilot.process.search.data.ReserveQueryContext;
import com.sankuai.dzim.pilot.process.search.strategy.filter.ReserveProductFilterHandler;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
public class ReserveProductQueryFactory {

    @Resource
    private List<ReserveProductQueryHandler> handlers;

    public List<ReserveProductQueryHandler> getHandler(ReserveQueryContext queryContext) {
        List<ReserveProductQueryHandler> result = new ArrayList<>();
        for (ReserveProductQueryHandler handler : handlers) {
            if (handler.match(queryContext)) {
                result.add(handler);
            }
        }
        return result;
    }
}
