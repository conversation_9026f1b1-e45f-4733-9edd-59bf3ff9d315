package com.sankuai.dzim.pilot.buffer.stream.build.beamcardext;


import com.sankuai.dzim.pilot.buffer.stream.build.beamcardext.data.BeamCardBuildContext;
import com.sankuai.dzim.pilot.buffer.stream.vo.BeamChoiceVO;

import java.util.List;

public interface BeamCardBuildExt {

    boolean acceptByStreamCardType(String streamCardType);

    boolean acceptByBeamResourceType(String resourceType);

    List<BeamChoiceVO.DeltaResource> buildResources(BeamCardBuildContext context);

    /**
     * 卡片解码(给AI看的内容)
     * @param cardContent <card>标签中间的内容.示例:{"dealId": 123}
     * @return
     */
    String cardDecode(String cardContent);
}
