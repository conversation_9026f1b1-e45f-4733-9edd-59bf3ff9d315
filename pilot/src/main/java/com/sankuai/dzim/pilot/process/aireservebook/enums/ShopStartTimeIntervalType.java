package com.sankuai.dzim.pilot.process.aireservebook.enums;

import org.apache.commons.collections.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: w<PERSON><PERSON><PERSON><PERSON>
 * @create: 2025-04-22
 * @description: 门店开始时间间隔类型
 */
public enum ShopStartTimeIntervalType {

    FIFTEEN("48", 15, "15分钟"),
    THIRTY("38,2634,225,2139,51,585,114,38", 30, "30分钟"),
    SIXTY("263", 60, "60分钟");//酒吧？

    private String backCategoryIds;

    private int startTimeInterval;

    private String desc;

    ShopStartTimeIntervalType(String backCategoryIds, int startTimeInterval, String desc) {
        this.backCategoryIds = backCategoryIds;
        this.startTimeInterval = startTimeInterval;
        this.desc = desc;
    }

    public String getBackCategoryIds() {
        return backCategoryIds;
    }

    public int getStartTimeInterval() {
        return startTimeInterval;
    }

    public static ShopStartTimeIntervalType matchByBackCategoryIds(List<Integer> backCategoryIds) {
        if (CollectionUtils.isEmpty(backCategoryIds)) {
            return null;
        }
        for (ShopStartTimeIntervalType type : ShopStartTimeIntervalType.values()) {
            List<Integer> categoryIds = Arrays.stream(type.getBackCategoryIds().split(",")).map(Integer::parseInt).collect(Collectors.toList());
            if (CollectionUtils.containsAny(categoryIds, backCategoryIds)) {
                return type;
            }
        }
        return null;
    }
}
