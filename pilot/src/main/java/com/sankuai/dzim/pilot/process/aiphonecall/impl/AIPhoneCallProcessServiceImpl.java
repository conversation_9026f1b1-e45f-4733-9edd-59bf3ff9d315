package com.sankuai.dzim.pilot.process.aiphonecall.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dzim.pilot.acl.TokenAccessAclService;
import com.sankuai.dzim.pilot.dal.entity.aiphonecall.AIPhoneCallBlacklistEntity;
import com.sankuai.dzim.pilot.dal.entity.aiphonecall.AIPhoneCallDetailEntity;
import com.sankuai.dzim.pilot.dal.entity.aiphonecall.AIPhoneCallTaskEntity;
import com.sankuai.dzim.pilot.dal.pilotdao.aiphonecall.AIPhoneCallBlacklistDAO;
import com.sankuai.dzim.pilot.dal.pilotdao.aiphonecall.AIPhoneCallDetailDAO;
import com.sankuai.dzim.pilot.dal.pilotdao.aiphonecall.AIPhoneCallTaskDAO;
import com.sankuai.dzim.pilot.domain.AIPhoneCallDomainService;
import com.sankuai.dzim.pilot.domain.ShopDomainService;
import com.sankuai.dzim.pilot.enums.*;
import com.sankuai.dzim.pilot.process.aiphonecall.AIPhoneCallProcessService;
import com.sankuai.dzim.pilot.process.aiphonecall.callback.AIPhoneCallbackProcessor;
import com.sankuai.dzim.pilot.process.aiphonecall.data.*;
import com.sankuai.dzim.pilot.utils.CatUtils;
import com.sankuai.dzim.pilot.utils.DateUtils;
import com.sankuai.dzim.pilot.utils.LionConfigUtil;
import com.sankuai.dzim.pilot.utils.data.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AIPhoneCallProcessServiceImpl implements AIPhoneCallProcessService {

    @Resource
    private AIPhoneCallTaskDAO aiPhoneCallTaskDAO;

    @Resource
    private AIPhoneCallDetailDAO aiPhoneCallDetailDAO;

    @Resource
    private AIPhoneCallbackProcessor callbackProcessor;

    @Resource
    private AIPhoneCallDomainService callDomainService;

    @Resource
    private AIPhoneCallStateManager callStateManager;

    @Resource
    private ShopDomainService shopDomainService;

    @Resource
    private LionConfigUtil lionConfigUtil;

    @Resource
    private TokenAccessAclService tokenAccessAclService;

    @Resource
    private AIPhoneCallBlacklistDAO callBlacklistDAO;


    /**
     * AI外呼调度线程池
     */
    private static ThreadPool AI_PHONE_CALL_SCHEDULE_POOL = Rhino.newThreadPool("AI_PHONE_CALL_SCHEDULE_POOL",
            DefaultThreadPoolProperties.Setter().withCoreSize(100).withMaxSize(200).withMaxQueueSize(1000));


    @Override
    public Response<AIPhoneCallTaskCreateRes> createAIPhoneCallTask(AIPhoneCallTaskCreateReq createReq) {
        try {
            logAndMetricCreateReq(createReq);
            // 参数校验
            checkParams(createReq);

            // 创建任务和明细
            AIPhoneCallTaskEntity callTaskEntity = saveCallTaskAndDetail(createReq);
            if (callTaskEntity == null) {
                return Response.error("创建任务和明细失败");
            }

            // 异步调度
            AI_PHONE_CALL_SCHEDULE_POOL.submit(() ->
                    callStateManager.transitAICallTaskState(callTaskEntity.getId(), AIPhoneCallTaskStatusEnum.PROCESSING, "开始调度外呼任务"));
            return Response.success(AIPhoneCallTaskCreateRes.builder().taskId(callTaskEntity.getId()).build());
        } catch (IllegalArgumentException e) {
            log.error("createAIPhoneCallTask check param error, req:{}", createReq, e);
            return Response.error("参数校验失败");
        } catch (Exception e) {
            log.error("createAIPhoneCallTask error, req:{}", createReq, e);
            return Response.error("系统异常");
        }
    }

    private void logAndMetricCreateReq(AIPhoneCallTaskCreateReq createReq) {
        try {
            log.info("createAIPhoneCallTask createReq = {}", JsonCodec.encodeWithUTF8(createReq));
            Map<String, String> tags = Maps.newHashMap();
            tags.put("source", String.valueOf(createReq.getAiPhoneCallSourceEnum().getSource()));
            tags.put("sceneType", String.valueOf(createReq.getAiPhoneCallSceneTypeEnum().getType()));
            tags.put("platform", String.valueOf(createReq.getPlatform()));
            CatUtils.logMetricReq("AIPhoneCall", tags);
        } catch (Exception e) {
            log.error("logAndMetricCreateReq error, req = {}", JsonCodec.encodeWithUTF8(createReq), e);
        }
    }

    private AIPhoneCallTaskEntity saveCallTaskAndDetail(AIPhoneCallTaskCreateReq createReq) {
        AIPhoneCallTaskEntity callTaskEntity = buildAIPhoneCallTask(createReq);
        List<AIPhoneCallDetailEntity> callDetailEntityList = buildAIPhoneCallDetailList(createReq);

        boolean insertResult = callDomainService.insertTaskAndDetail(callTaskEntity, callDetailEntityList);
        if (!insertResult) {
            return null;
        }
        return callTaskEntity;
    }

    private List<AIPhoneCallDetailEntity> buildAIPhoneCallDetailList(AIPhoneCallTaskCreateReq taskCreateReq) {
        List<AIPhoneCallDetailEntity> callDetailEntityList = Lists.newArrayList();
        for (AIPhoneCallDetailCreateReq detailCreateReq: taskCreateReq.getAiPhoneCallDetailList()) {
            // 根据性别+场景+行业获取botId
            IvrBotConfig ivrBotConfig = getIvrBotConfig(taskCreateReq.getAiPhoneCallSceneTypeEnum(), taskCreateReq.getUserGender(), detailCreateReq.getShopId(), taskCreateReq.getPlatform());
            String encryptCallPhone = tokenAccessAclService.getTokenMobile(detailCreateReq.getCallPhone());
            Map<String, String> dynamicParameterMap = processDynamicParameter(detailCreateReq.getDynamicParameterMap());

            AIPhoneCallDetailEntity callDetailEntity = new AIPhoneCallDetailEntity();
            callDetailEntity.setCallPhone(encryptCallPhone);
            callDetailEntity.setSequenceId(detailCreateReq.getSequenceId());
            callDetailEntity.setPlatform(taskCreateReq.getPlatform());
            callDetailEntity.setShopId(detailCreateReq.getShopId());
            callDetailEntity.setDynamicVariable(JsonCodec.encodeWithUTF8(dynamicParameterMap));
            callDetailEntity.setStatus(AIPhoneCallDetailStatusEnum.PENDING.getStatus());
            callDetailEntity.setUserId(detailCreateReq.getUserId());
            callDetailEntity.setBotId(ivrBotConfig.getBotId());
            callDetailEntity.setRoutePoint(ivrBotConfig.getRoutePoint());
            callDetailEntity.setBotVersion(ivrBotConfig.getBotVersion());
            callDetailEntity.setDdlDate(detailCreateReq.getDdlDate());
            callDetailEntity.setExpectedCallTime(new Date());
            callDetailEntityList.add(callDetailEntity);
        }
        return callDetailEntityList;
    }

    private Map<String, String> processDynamicParameter(Map<String, String> dynamicParameterMap) {
        if (dynamicParameterMap == null) {
            dynamicParameterMap = Maps.newHashMap();
        }

        for (AIPhoneCallDynamicParamEnum dynamicParamEnum: AIPhoneCallDynamicParamEnum.values()) {
            if (StringUtils.isNotEmpty(dynamicParameterMap.get(dynamicParamEnum.getKey()))) {
                continue;
            }
            dynamicParameterMap.put(dynamicParamEnum.getKey(), StringUtils.EMPTY);
        }


        return dynamicParameterMap;
    }

    private IvrBotConfig getIvrBotConfig(AIPhoneCallSceneTypeEnum aiPhoneCallSceneTypeEnum, String userGender, long shopId, int platform) {
        String botGender = buildBotGender(userGender);
        long dpShopId = shopDomainService.getDpShopId(shopId, platform);
        Map<String, IvrBotConfig> ivrBotConfigMap = MapUtils.emptyIfNull(lionConfigUtil.getAiPhoneCallBotIdConfig());
        List<Integer> backCategoryIds = shopDomainService.getBackCategoryIdList(dpShopId);
        backCategoryIds = CollectionUtils.emptyIfNull(backCategoryIds).stream().sorted(Comparator.reverseOrder()).collect(Collectors.toList());

        for (int catId: CollectionUtils.emptyIfNull(backCategoryIds)) {
            String botIdQueryKeyWithCat = String.format("%s_%s_%s", aiPhoneCallSceneTypeEnum.getType(), botGender, catId);
            if (ivrBotConfigMap.containsKey(botIdQueryKeyWithCat)) {
                return ivrBotConfigMap.get(botIdQueryKeyWithCat);
            }
        }
        String botIdQueryKey = String.format("%s_%s", aiPhoneCallSceneTypeEnum.getType(), botGender);
        return ivrBotConfigMap.get(botIdQueryKey);
    }

    private String buildBotGender(String userGender) {
        if (StringUtils.isEmpty(userGender)) {
            return "unknown";
        }
        if (userGender.equals("male")) {
            return "male";
        }
        if (userGender.equals("female")) {
            return "female";
        }
        return "unknown";
    }

    private AIPhoneCallTaskEntity buildAIPhoneCallTask(AIPhoneCallTaskCreateReq createReq) {
        AIPhoneCallTaskEntity taskEntity = new AIPhoneCallTaskEntity();
        taskEntity.setBizId(createReq.getBizId());
        taskEntity.setBizData(createReq.getBizData());
        taskEntity.setPlatform(createReq.getPlatform());
        taskEntity.setSource(createReq.getAiPhoneCallSourceEnum().getSource());
        taskEntity.setSceneType(createReq.getAiPhoneCallSceneTypeEnum().getType());
        taskEntity.setStatus(AIPhoneCallTaskStatusEnum.CREATED.getStatus());
        taskEntity.setSource(createReq.getAiPhoneCallSourceEnum().getSource());
        return taskEntity;
    }

    @Override
    public boolean addShopBlackList(long dpShopId, AIPhoneCallBlackTypeEnum blackTypeEnum, Date expireTime) {
        if (blackTypeEnum == null || dpShopId <= 0) {
            return false;
        }

        AIPhoneCallBlacklistEntity callBlacklistEntity = buildCallBlacklistEntity(dpShopId, blackTypeEnum, expireTime);
        int affectRows = callBlacklistDAO.insert(callBlacklistEntity);
        return affectRows > 0;
    }

    @Override
    public Response<Boolean> cancelAIPhoneCallTask(AIPhoneCallTaskCancelReq cancelReq) {
        List<AIPhoneCallTaskEntity> callTaskEntities = aiPhoneCallTaskDAO.getByBizId(cancelReq.getBizId(), cancelReq.getCallSceneTypeEnum().getType());
        List<AIPhoneCallTaskEntity> unCompleteTaskEntities = CollectionUtils.emptyIfNull(callTaskEntities).stream()
                .filter(entity -> !AIPhoneCallTaskStatusEnum.isDone(entity.getStatus()))
                .collect(Collectors.toList());

        for (AIPhoneCallTaskEntity taskEntity: unCompleteTaskEntities) {
            callStateManager.transitAICallTaskState(taskEntity.getId(), AIPhoneCallTaskStatusEnum.TERMINATED, "取消外呼任务");
        }
        return Response.success();
    }

    private AIPhoneCallBlacklistEntity buildCallBlacklistEntity(long dpShopId, AIPhoneCallBlackTypeEnum blackTypeEnum, Date expireTime) {
        AIPhoneCallBlacklistEntity callBlacklistEntity = new AIPhoneCallBlacklistEntity();
        callBlacklistEntity.setBlackType(blackTypeEnum.getType());
        callBlacklistEntity.setDpShopId(dpShopId);
        callBlacklistEntity.setStatus(1);
        callBlacklistEntity.setExpireTime(expireTime);
        callBlacklistEntity.setOptUser("system");
        return callBlacklistEntity;
    }

    private void checkParams(AIPhoneCallTaskCreateReq createReq) {
        Assert.isTrue(createReq.getAiPhoneCallSceneTypeEnum() != null, "外呼场景不能为空");
        Assert.isTrue(CollectionUtils.isNotEmpty(createReq.getAiPhoneCallDetailList()), "外呼明细不能为空");
    }


}
