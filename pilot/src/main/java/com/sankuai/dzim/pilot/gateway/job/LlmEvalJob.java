package com.sankuai.dzim.pilot.gateway.job;

import com.alibaba.fastjson.JSON;
import com.cip.crane.client.spring.annotation.Crane;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sankuai.dzim.pilot.acl.HaimaAclService;
import com.sankuai.dzim.pilot.chain.eval.EvalTaskService;
import com.sankuai.dzim.pilot.chain.eval.data.BeamIntentUnderStandEvalCase;
import com.sankuai.dzim.pilot.chain.eval.data.EvalCase;
import com.sankuai.dzim.pilot.chain.eval.data.EvalTaskRequest;
import com.sankuai.dzim.pilot.chain.eval.data.EvalTaskResult;
import com.sankuai.dzim.pilot.domain.message.AssistantMessage;
import com.sankuai.dzim.pilot.domain.message.Message;
import com.sankuai.dzim.pilot.domain.message.PluginCall;
import com.sankuai.dzim.pilot.domain.message.UserMessage;
import com.sankuai.dzim.pilot.gateway.job.data.LLMEvalReq;
import com.sankuai.dzim.pilot.process.data.AIServiceConfig;
import com.sankuai.dzim.pilot.utils.ExcelUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;
import com.fasterxml.jackson.databind.JsonNode;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 大模型评测用例自动执行job
 */
@Component
@Slf4j
public class LlmEvalJob {

    @Resource
    private EvalTaskService evalTaskService;

    @Resource
    private HaimaAclService haimaAclService;

    @Crane("com.sankuai.mim.pilot.llm.eval.execute")
    public void handle(LLMEvalReq llmEvalReq) {
        log.info(" 开始执行LlmEvalJob:{}", JSON.toJSONString(llmEvalReq.getFilePath()));

        AIServiceConfig config = haimaAclService.queryAIConfig("beam_fwls_agent_v2");
        EvalTaskRequest request = buildEvalTaskRequest(llmEvalReq, config);
        EvalTaskResult result = evalTaskService.execute(request);

        log.info("结束执行LlmEvalJob:{}", JSON.toJSONString(result));
    }

    private EvalTaskRequest buildEvalTaskRequest(LLMEvalReq llmEvalReq, AIServiceConfig config) {
        List<EvalCase> evalCasesFromExcel = getEvalCasesFromExcel(llmEvalReq.getFilePath());

        EvalTaskRequest request = new EvalTaskRequest();
        request.setFilePath(llmEvalReq.getFilePath());
        request.setAiServiceConfig(JSON.toJSONString(config));
        request.setEvaluator("BeamFwlsIntentUnderstandEvaluator");
        request.setCases(evalCasesFromExcel);
        request.setRpm(llmEvalReq.getRpm());
        return request;
    }

    private List<EvalCase> getEvalCasesFromExcel(String fileName) {
        List<BeamIntentUnderStandEvalCase> caseList = ExcelUtils.readFromExcel(fileName, BeamIntentUnderStandEvalCase.class);
        if (CollectionUtils.isEmpty(caseList)) {
            return Collections.emptyList();
        }
        return caseList.stream().filter(Objects::nonNull).map(this::buildEvalCases).collect(Collectors.toList());
    }

    private EvalCase buildEvalCases(BeamIntentUnderStandEvalCase beamCase) {
        // 获取最后一条作为测试用例
        EvalCase testCase = new EvalCase();

        // 构建input
        String input = beamCase.getRewriteUserQuery();

        // 构建output
        List<PluginCall> output = parseOutput(beamCase.getToolName(), beamCase.getToolParam());

        // 构建chat history
        List<Message> chatHistory =  parseMessages(beamCase.getRewriteHistory());

        testCase.setInput(input);
        testCase.setChatHistory(chatHistory);
        testCase.setPluginCalls(output);
        testCase.setContext(beamCase.getRewriteHistory());
        testCase.setTargetOutput(StringUtils.defaultString(beamCase.getToolName()) + "_" + StringUtils.defaultString(beamCase.getToolParam()));
//        testCase.setTargetOutput(output);
        return testCase;
    }

    private List<PluginCall> parseOutput(String toolName, String toolParams) {
        List<PluginCall> pluginCalls = new ArrayList<>();
        PluginCall pluginCall = new PluginCall();
        pluginCall.setPluginName(toolName);
        pluginCall.setArguments(convertJsonNode(toolParams));
        pluginCalls.add(pluginCall);
        return pluginCalls;
    }

    private JsonNode convertJsonNode(String jsonString) {
        if (StringUtils.isBlank(jsonString)) {
            return null;
        }
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.readTree(jsonString);
        } catch (Exception e) {
            e.printStackTrace();
            return null; // 或者抛出异常，根据实际需求处理
        }
    }

    private List<Message> parseMessages(String history) {
        if (StringUtils.isBlank(history)) {
            return Collections.emptyList();
        }
        List<Message> messages = new ArrayList<>();
        String[] segments = history.split("(?=user:|assistant:)");

        for (String segment : segments) {
            if (segment.startsWith("user:")) {
                String content = segment.substring("user:".length()).trim();
                messages.add(UserMessage.build(content));
            } else if (segment.startsWith("assistant:")) {
                String content = segment.substring("assistant:".length()).trim();
                messages.add(AssistantMessage.build(content));
            }
        }

        return messages;
    }

}
