package com.sankuai.dzim.pilot.gateway.api.vo.medical;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: zhouyibing
 * @date: 2024/9/10
 */
@Data
@MobileDo
public class MedicalCheckupReportHistoryVO implements Serializable {

    /**
     * 主键
     */
    @MobileDo.MobileField(key = 0x7510)
    private Long recordId;

    /**
     * 报告标题
     */
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    /**
     * 解读状态
     */
    @MobileDo.MobileField(key = 0x897e)
    private Integer analysisStatus;


    /**
     * 是否有体检报告原文
     */
    @MobileDo.MobileField(key = 0x21fd)
    private Boolean hasOriginalFiles;


    /**
     * 解读结果页地址
     */
    @MobileDo.MobileField(key = 0xf2dc)
    private String analysisUrl;

}
