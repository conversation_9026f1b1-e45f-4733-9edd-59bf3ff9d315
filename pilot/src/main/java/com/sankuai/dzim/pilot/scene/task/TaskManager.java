package com.sankuai.dzim.pilot.scene.task;

import com.google.common.collect.Lists;
import com.sankuai.dzim.pilot.scene.data.AssistantSceneContext;
import com.sankuai.dzim.pilot.scene.task.data.TaskConfig;
import com.sankuai.dzim.pilot.scene.task.data.TaskContext;
import com.sankuai.dzim.pilot.scene.task.data.TaskResult;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: zhouyibing
 * @date: 2024/7/30
 */
@Component
public class TaskManager {

    @Autowired
    private List<Task> taskList;

    public TaskResult execute(TaskContext context) {
        if (context == null) {
            return null;
        }
        for (Task task : taskList) {
            if (task.accept(context)) {
                return task.execute(context);
            }
        }
        return null;
    }

    public void afterExecute(TaskResult result, TaskResult mainTaskResult) {
        if (result == null || result.getTaskContext() == null) {
            return;
        }
        result.setMainTaskResult(mainTaskResult);
        for (Task task : taskList) {
            if (task.accept(result.getTaskContext())) {
                task.afterExecute(result);
            }
        }
    }

    public TaskContext buildMainTask(AssistantSceneContext assistantSceneContext) {
        TaskConfig mainTaskConfig = assistantSceneContext.getAssistantSceneConfig().getMainTask();
        return buildTaskContext(assistantSceneContext, mainTaskConfig);
    }

    public List<TaskContext> buildSubTasks(AssistantSceneContext assistantSceneContext) {
        List<TaskConfig> subTasks = assistantSceneContext.getAssistantSceneConfig().getSubTasks();
        if (CollectionUtils.isEmpty(subTasks)) {
            return Lists.newArrayList();
        }

        return subTasks.stream().map(subTaskConfig -> buildTaskContext(assistantSceneContext, subTaskConfig)).collect(Collectors.toList());
    }

    private TaskContext buildTaskContext(AssistantSceneContext assistantSceneContext, TaskConfig taskConfig) {
        TaskContext taskContext = new TaskContext();
        taskContext.setTaskType(taskConfig.getTaskType());
        taskContext.setTaskConfig(taskConfig);
        taskContext.setAssistantType(assistantSceneContext.getAssistantType());
        taskContext.setUserId(assistantSceneContext.getUserId());
        taskContext.setMessageDTO(assistantSceneContext.getMessageDTO());
        taskContext.setExtra(assistantSceneContext.getExtra());
        return taskContext;
    }

    public TaskResult processException(TaskContext context) {
        if (context == null) {
            return null;
        }
        for (Task task : taskList) {
            if (task.accept(context)) {
                return task.processException(context);
            }
        }
        return null;
    }
}
