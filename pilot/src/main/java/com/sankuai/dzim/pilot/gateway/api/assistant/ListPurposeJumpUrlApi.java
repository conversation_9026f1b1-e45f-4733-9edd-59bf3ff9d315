package com.sankuai.dzim.pilot.gateway.api.assistant;

import com.dianping.dzim.common.enums.ImUserType;
import com.dianping.gateway.enums.APIModeEnum;
import com.dianping.vc.web.api.API;
import com.dianping.vc.web.api.URL;
import com.dianping.vc.web.api.VCAssert;
import com.dianping.vc.web.biz.client.api.AppContext;
import com.dianping.vc.web.biz.client.api.AppContextAware;
import com.dianping.vc.web.biz.client.api.MTUserIdAware;
import com.dianping.vc.web.biz.client.api.UserIdAware;
import com.google.common.collect.Maps;
import com.sankuai.dzim.message.enums.PlatformTypeEnum;
import com.sankuai.dzim.pilot.api.data.assistant.PilotListPurposeConfigDTO;
import com.sankuai.dzim.pilot.api.enums.assistant.PlatformEnum;
import com.sankuai.dzim.pilot.gateway.api.vo.ListPurposeJumpUrlVO;
import com.sankuai.dzim.pilot.process.ListPurposeService;
import com.sankuai.dzim.pilot.process.data.AgentListPurposeJumpConfigReq;
import com.sankuai.dzim.pilot.process.data.AgentListTypeEnum;
import com.sankuai.dzim.pilot.utils.CatUtils;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

@Setter
@Slf4j
@Service
@URL(url = "/dzim/pilot/assistant/list/purpose/jumpurl", mode = APIModeEnum.CLIENT, methods = "GET")
public class ListPurposeJumpUrlApi implements API, MTUserIdAware, UserIdAware, AppContextAware {
    @Setter
    private AppContext appContext;

    @Setter
    private long mTUserId;

    @Setter
    private int platform;

    @Setter
    private String unionid;

    /**
     * 垂搜页-搜索词
     */
    @Setter
    private String currentQuery;

    /**
     * 频道页-bizCode
     */
    @Setter
    private String bizCode;

    /**
     * 列表页-类目id
     */
    @Setter
    private Long categoryId;

    @Setter
    private long userId;

    @Setter
    private String listType;


    @Setter
    private String cityId;

    @Resource
    private ListPurposeService listPurposeService;

    @Override
    public Object execute() {
        checkParam();
        metricListTypeCnt();
        PilotListPurposeConfigDTO pilotListPurposeConfigDTO = listPurposeService.getListPurposeJumpConfig(buildAgentListPurposeJumpConfigReq());
        metricListTypeSucc();
        return buildListPurposeJumpConfigVO(pilotListPurposeConfigDTO);
    }

    public void metricListTypeCnt() {
        Map<String, String> tags = Maps.newHashMap();
        tags.put("listType", String.valueOf(listType));
        CatUtils.logMetricReq("assistantListPurposeJumpurl", tags);
    }

    public void metricListTypeSucc(){
        Map<String, String> tags = Maps.newHashMap();
        tags.put("listType", String.valueOf(listType));
        CatUtils.logMetricSucc("assistantListPurposeJumpurl", tags);
    }


    private AgentListPurposeJumpConfigReq buildAgentListPurposeJumpConfigReq() {
        return AgentListPurposeJumpConfigReq.builder()
                .platform(platform)
                .listType(listType)
                .unionid(unionid)
                .bizCode(bizCode)
                .currentQuery(currentQuery)
                .categoryId(categoryId)
                .imUserId(getImUserId())
                .cityId(cityId)
                .build();
    }

    private String getImUserId() {
        if (platform == PlatformTypeEnum.DP.value) {
            return ImUserType.DP.getPrefix() + userId;
        } else {
            return ImUserType.MT.getPrefix() + mTUserId;
        }
    }

    private void checkParam() {
        platform = PlatformEnum.getByName(appContext.getPlatform()).getType();
        unionid = appContext.getUnionid();

        VCAssert.isTrue(userId > 0 || mTUserId > 0, API.UNLOGIN_CODE, "未登陆");
        VCAssert.isTrue(AgentListTypeEnum.checkExistByName(listType), API.INVALID_PARAM_CODE, "listType不合法");
        VCAssert.isTrue(platform == PlatformEnum.DP.getType() || platform == PlatformEnum.MT.getType(), API.INVALID_PARAM_CODE, "平台参数不合法");
    }

    private ListPurposeJumpUrlVO buildListPurposeJumpConfigVO(PilotListPurposeConfigDTO pilotListPurposeConfigDTO) {
        ListPurposeJumpUrlVO listPurposeJumpUrlVO = new ListPurposeJumpUrlVO();
        BeanUtils.copyProperties(pilotListPurposeConfigDTO, listPurposeJumpUrlVO);
        return listPurposeJumpUrlVO;
    }
}
