package com.sankuai.dzim.pilot.gateway.api.assistant;

import com.dianping.gateway.enums.APIModeEnum;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.dianping.vc.web.api.API;
import com.dianping.vc.web.api.URL;
import com.dianping.vc.web.api.VCAssert;
import com.dianping.vc.web.biz.client.api.MTUserIdAware;
import com.dianping.vc.web.biz.client.api.UserIdAware;
import com.sankuai.dzim.pilot.gateway.api.vo.NailTaskVO;
import com.sankuai.dzim.pilot.process.ImageProcessService;
import com.sankuai.dzim.pilot.process.PilotMessageProcessService;
import com.sankuai.dzim.pilot.utils.UserIdUtils;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/04/09 14:03
 */
@Slf4j
@URL(url = "/dzim/pilot/nail/image/edit", mode = APIModeEnum.CLIENT, methods = "GET", timeout = 100000)
@Setter
public class ImageProcessAPI implements API, UserIdAware, MTUserIdAware {
    private long userId;
    private long mTUserId;
    /**
     * 图片链接列表，逗号分隔，如果有多张，第一张是手模图
     */
    private String imgUrls;
    /**
     * 当前选中美甲图片链接
     */
    private String currentNailImgUrl;
    /**
     * 操作
     */
    private String operation;
    /**
     * 自定义操作
     */
    private String customOperation;

    /**
     * 任务Id,用于轮询task结果
     */
    private String taskId;

    private int platform;
    private long messageId;

    private List<String> imgUrlList;
    @Autowired
    private ImageProcessService imageProcessService;

    @Autowired
    private PilotMessageProcessService pilotMessageProcessService;

    @Override
    public Object execute() {
        checkParams();
        fillImgUrlList();

        // 图片编辑
        NailTaskVO nailTaskVO = imageProcessService.processImages(imgUrlList, operation, customOperation, taskId);
//        if (imgUrlList.size() == 1) {
//            result = imageProcessService.processImage(imgUrlList.get(0), operation);
//        } else {
//            operation = "将后面几张图片手指上的美甲样式应用到第一张图片的手指甲上";
//            result = imageProcessService.processImages(imgUrlList, operation);
//        }
        if (nailTaskVO != null && StringUtils.isNotBlank(nailTaskVO.getImgUrl())) {
            String imUserId = UserIdUtils.getImUserId(userId, mTUserId, platform);
            updateMessageHistory(imUserId, nailTaskVO.getImgUrl());
            log.info("图片编辑成功：操作={}, userId={}, imgUrlList={}", operation, userId, JsonCodec.encodeWithUTF8(imgUrlList));
        }

        // 结果返回
        return nailTaskVO;
    }

    private void fillImgUrlList() {
        if (StringUtils.isBlank(imgUrls)) {
            return;
        }
        imgUrlList = JsonCodec.converseList(imgUrls, String.class);
        imgUrlList = imgUrlList.stream().filter(image -> StringUtils.isNotBlank(image) && image.contains("http")).limit(3).collect(Collectors.toList());
    }

    private void updateMessageHistory(String imUserId, String result) {
        try {
            pilotMessageProcessService.nailMessageEdit(imUserId, messageId, currentNailImgUrl, operation, result);
        } catch (Exception e) {
            log.error("updateMessageHistory error, imUserId={}, messageId={}, currentNailImgUrl={}, operation={}", imUserId, messageId, currentNailImgUrl, operation, e);
        }
    }

    private void checkParams() {
        VCAssert.isTrue(userId > 0, API.UNLOGIN_CODE, "未登陆");
        VCAssert.isTrue(StringUtils.isNotBlank(imgUrls), API.INVALID_PARAM_CODE, "图片链接不能为空");
        VCAssert.isTrue(platform > 0, API.INVALID_PARAM_CODE, "平台类型不能为空");
    }
}
