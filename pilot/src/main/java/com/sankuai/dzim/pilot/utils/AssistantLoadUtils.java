package com.sankuai.dzim.pilot.utils;

import com.sankuai.dzim.pilot.process.data.AssistantData;
import com.sankuai.dzim.pilot.gateway.api.vo.RecommendQuestionsVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class AssistantLoadUtils {
    @Autowired
    private LionConfigUtil lionConfigUtil;

    public List<AssistantData> getAssistantListFromLion(){
        return lionConfigUtil.getAssistantDataList();
    }


    public List<RecommendQuestionsVO> getRecommandQuestionsFromLion(){
        return lionConfigUtil.getAssistantTypeRecommendQuestionList();
    }
}
