package com.sankuai.dzim.pilot.process.aireservebook.data;

import com.sankuai.spt.statequery.api.enums.TimeSliceTypeEnum;
import lombok.Data;

/**
 * 时间分片库存信息
 * <AUTHOR>
 */
@Data
public class TimeSliceStockInfo {
    private String startTime;
    private String endTime;
    private long stock;

    //酒吧的SkuId
    private String skuId;
    private TimeSliceTypeEnum timeSliceType;
    // 标志跨天库存扣的是哪一天，YESTERDAY要扣的是前一天
    private String purchaseStockDate;
}
