package com.sankuai.dzim.pilot.process.aicall.strategy.AICallSceneCodeHandle;

import com.dianping.cat.Cat;
import com.dianping.martgeneral.recommend.api.entity.RecommendParameters;
import com.dianping.poi.bizhour.dto.BizForecastDTO;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.dianping.vc.enums.VCPlatformEnum;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.dianping.zebra.group.router.ZebraForceMasterHelper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import com.meituan.mafka.client.bean.MafkaProducer;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.acl.RecommendAclService;
import com.sankuai.dzim.pilot.acl.ShopAclService;
import com.sankuai.dzim.pilot.api.data.beauty.ai.AICallBackReq;
import com.sankuai.dzim.pilot.api.data.beauty.ai.AICallShopResultDTO;
import com.sankuai.dzim.pilot.api.data.beauty.ai.BookTimePeriod;
import com.sankuai.dzim.pilot.api.enums.aicall.AICallC2cResultEnum;
import com.sankuai.dzim.pilot.api.enums.aicall.AICallSceneCodeEnum;
import com.sankuai.dzim.pilot.api.enums.aicall.AICallTaskStatusEnum;
import com.sankuai.dzim.pilot.api.enums.assistant.PlatformEnum;

import com.sankuai.dzim.pilot.dal.entity.aicall.AICallDetailEntity;
import com.sankuai.dzim.pilot.dal.entity.aicall.AICallTaskEntity;
import com.sankuai.dzim.pilot.dal.pilotdao.aicall.AICallDetailDAO;
import com.sankuai.dzim.pilot.dal.pilotdao.aicall.AICallTaskDAO;
import com.sankuai.dzim.pilot.process.aibook.data.AIBookConstants;
import com.sankuai.dzim.pilot.process.aibook.data.AIBookDispatchDelayData;
import com.sankuai.dzim.pilot.process.aicall.AICallDispatchProcessService;
import com.sankuai.dzim.pilot.process.aicall.data.*;
import com.sankuai.dzim.pilot.process.aicall.strategy.AICallSceneCodeHandleStrategy;
import com.sankuai.dzim.pilot.process.data.CategoryQuestion;
import com.sankuai.dzim.pilot.utils.DateUtils;
import com.sankuai.dzim.pilot.utils.LionConfigUtil;
import com.sankuai.dzim.pilot.utils.LionConfigUtilTmp;
import com.sankuai.dzim.pilot.utils.ProducerUtils;
import com.sankuai.gaigc.arrange.api.thrift.dto.request.WaihuCallRequest;
import com.sankuai.meituan.wmdrecsys.aigc.api.domain.outboundcall.Gender;
import com.sankuai.sinai.data.api.dto.MtPoiDTO;
import com.sankuai.sinai.data.api.dto.TypeHierarchyView;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.joda.time.DateTimeZone;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.sankuai.dzim.message.common.logger.LogUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class C2cHandleStrategy implements AICallSceneCodeHandleStrategy {
    @Resource
    private LionConfigUtil lionConfigUtil;

    @Autowired
    private LionConfigUtilTmp lionConfigUtilTmp;

    @Resource
    private RecommendAclService recommendAclService;

    @Resource
    private ShopAclService shopAclService;

    @Resource(name = "redisClient")
    private RedisStoreClient redisStoreClient;

    @Resource
    private AICallDetailDAO aiCallDetailDAO;

    @Resource
    private AICallTaskDAO aiCallTaskDAO;

    @Resource
    private MafkaProducer aiCallCallAgainDelayProducer;

    @Resource
    private AICallDispatchProcessService aiCallDispatchProcessService;

    private static final String DEFAULT_DISPATCH_CONFIG = "-1";


    @Override
    public boolean accept(Integer sceneCode) {
        return sceneCode != null && sceneCode == AICallSceneCodeEnum.C2C_AICALL.getSceneCode();
    }

    @Override
    public List<Long> shopRecommandAndFilter(UserCallRequest request, List<Long> recalledShops, int pageNo, int pageSize) {
        Map<String, AICallRecommandShopConfigData> aiCallRecommandShopConfigDataMap = lionConfigUtilTmp.getAiCallRecommandShopConfigDataMap();
        if (MapUtils.isEmpty(aiCallRecommandShopConfigDataMap) ||
                !aiCallRecommandShopConfigDataMap.containsKey(String.valueOf(request.getSceneCode()))) {
            return Lists.newArrayList();
        }
        AICallRecommandShopConfigData aiCallRecommandShopConfigData = aiCallRecommandShopConfigDataMap.get(String.valueOf(request.getSceneCode()));
        // 召回门店
        RecommendParameters recommendParameters = buildRecommendParameters(request, 1, aiCallRecommandShopConfigData.getPageSize());
        List<Long> shopIds = recommendAclService.getRecommendShopIds(recommendParameters);

        // 门店过滤
        recalledShops = filterShops(request, shopIds);

        return recalledShops;
    }

    @Override
    public boolean isShopCanCall(long dpShopId, long mtShopId, long detailId, AICallTaskEntity taskEntity) {
        // 门店锁
        StoreKey shopLockKey = new StoreKey(AIBookConstants.AI_BOOK_SHOP_LOCK_CATEGORY, mtShopId);
        if (redisStoreClient.exists(shopLockKey)) {
            Cat.logEvent("isShopCanCall", "DpShopLock_" + dpShopId + "MtShopLock_" + mtShopId);

            AICallDispatchConfigData aiCallDispatchConfigDataBySceneCode = getAICallDispatchConfigDataBySceneCode(taskEntity.getSceneCode());
            int delaySeconds = aiCallDispatchConfigDataBySceneCode.getShopLockCallAgainDelaySeconds();

            // 门店正在被打电话，延迟一次外呼，外呼前检查上次电话结果
            aiCallDetailDAO.updateCallResult(detailId, AICallTaskDetailStatusEnum.WAIT_CALL_BACK.getCode());
            ProducerUtils.sendDelayMessage(aiCallCallAgainDelayProducer, AICallCallAgainDelayData.builder().taskId(taskEntity.getId()).detailId(detailId).build(),
                    delaySeconds * 1000, "aiCallCallAgainDelayProducer");
            return false;
        }

        // 今天回答过，且有明确意图的商户
        AICallDetailEntity todayCallDetailEntity = getTodayCallResult(dpShopId, mtShopId, detailId);
        if (todayCallDetailEntity != null) {
            // 更新detail状态为 今日记录已存在
            int todayCallBackResult = JsonCodec.decode(todayCallDetailEntity.getExtraData(), AICallDetailExtraData.class).getCallBackResult();
            aiCallDetailDAO.updateCallResult(detailId, (todayCallBackResult == AICallC2cResultEnum.C2C_SUCCESS.getCode()) ? AICallTaskDetailStatusEnum.TODAY_RECORD_SUCCESS.getCode() : AICallTaskDetailStatusEnum.TODAY_RECORD_FAILED.getCode());
            return false;
        }
        return true;
    }

    @Override
    public WaihuCallRequest buildWaiHuRequest(String shopName, String shopMobile, int gender, String userPlainPhone, AICallTaskEntity taskEntity, AICallDetailEntity detailEntity) {
        ActivityTypeEnum activityTypeEnum = ActivityTypeEnum.getBySceneCodeAndType(taskEntity.getSceneCode(), taskEntity.getActivityType());
        if (activityTypeEnum == null) {
            return null;
        }

        AICallBizData aiCallBizData = JsonCodec.decode(taskEntity.getBizData(), AICallBizData.class);
        int genderValue = gender == Gender.OTHER.getValue() ? Gender.MALE.getValue() : Gender.findByValue(gender).getValue();
        Map<String, String> extend = Maps.newHashMap();

        // 目前只要找搭子的标签
        String partnerTagDesc = StringUtils.EMPTY;
        String timePeriodDate = StringUtils.EMPTY;
        List<List<Long>> bookTimeRange = Lists.newArrayList();
        if (aiCallBizData != null) {
            Map<String, String> businessInfoData = aiCallBizData.getBusinessData();

            List<String> partnerTags = JsonCodec.decode(businessInfoData.get("partnerTags"), new TypeReference<List<String>>() {
            });

            partnerTags = CollectionUtils.isEmpty(partnerTags) ? Lists.newArrayList() : partnerTags;
            partnerTagDesc = String.join(",", partnerTags);
            timePeriodDate = getTimePeriodDate(aiCallBizData.getTimePeriodList());
            bookTimeRange = buildBookTimeRange(aiCallBizData.getTimePeriodList());
        }

        Long botId = randomGetCallBotId(genderValue, taskEntity.getSceneCode());

        WaihuCallRequest request = new WaihuCallRequest();
        request.setFuxiBotId(botId);
        request.setDeviceNum(shopMobile);

        Map<String, String> params = new HashMap<>();
        params.put("shopName", shopName);
        params.put("playTypeEnum", String.valueOf(activityTypeEnum.getType()));
        params.put("bookingDateDesc", timePeriodDate);
        params.put("bookTimeRange", JsonCodec.encodeWithUTF8(bookTimeRange));
        params.put("targetPartnerFeature", partnerTagDesc);
        params.put("orderId", String.valueOf(detailEntity.getId()));
        params.put("extend", JsonCodec.encodeWithUTF8(extend));
        request.setBizData(params);

        return request;
    }

    @Override
    public int getDetailResult(AICallBackReq req, Integer sceneCode) {

        if (isCallResultSuccess(req)) {
            return AICallTaskDetailStatusEnum.CALL_SUCCESS.getCode();
        }
        if (req.getCode() == AICallC2cResultEnum.CALL_BACK_TIMEOUT.getCode()) {
            return AICallTaskDetailStatusEnum.TIMEOUT_CALL_BACK.getCode();
        }

        return AICallTaskDetailStatusEnum.CALL_FAILED.getCode();
    }

    @Override
    public boolean processCanCallAgain(AICallBackReq req, AICallTaskEntity taskEntity, AICallDetailEntity detailEntity) {
        if (req.getCode() == AICallC2cResultEnum.NOT_CONNECTED.getCode() && !AICallTaskStatusEnum.isBookCallEnd(taskEntity.getTaskStatus())) {
            // 未接通-可重拨，发起延时消息，待后续重拨
            StoreKey timesKey = new StoreKey(AICallConstants.AI_CALL_SHOP_CALL_AGAIN_TIME, detailEntity.getId());
            Object timesVal = redisStoreClient.get(timesKey);
            int times = timesVal == null ? 0 : NumberUtils.toInt(timesVal.toString());
            int shopCallAgainTimeLimit = getAICallDispatchConfigDataBySceneCode(taskEntity.getSceneCode()).getShopCallAgainTimeLimit();
            if (times < shopCallAgainTimeLimit) {
                ProducerUtils.sendDelayMessage(aiCallCallAgainDelayProducer, AICallCallAgainDelayData.builder()
                                .taskId(taskEntity.getId()).detailId(detailEntity.getId()).build(),
                        getAICallDispatchConfigDataBySceneCode(taskEntity.getSceneCode()).getCallAgainDelaySeconds() * 1000L,
                        "aiCallCallAgainDelayProducer");
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean processSpecificAddDailyBlackList(AICallBackReq req, AICallDetailEntity detailEntity, long dpShopId, long mtShopId, int code) {
        // 未接通-拉黑
        if (code == AICallC2cResultEnum.NOT_CONNECTED_BALCKLIST.getCode()) {
            return true;
        }
        return false;
    }

    @Override
    public boolean processSpecificAddMultiDayBlackList(AICallBackReq req, AICallDetailEntity detailEntity, long dpShopId, long mtShopId, int code) {
        // 负向情绪
        if (code == AICallC2cResultEnum.CONNECTED_BLACKLIST_REJECT.getCode()) {
            return true;
        }
        // 连续5次拒绝/未确认意向
        return continousRefuseOrVague(req.getCode(), detailEntity, dpShopId, mtShopId);
    }

    @Override
    public boolean checkRecordBeforeCallAgain(long detailId, long dpShopId, long mtShopId) {

        // 检查门店今天有没有明确的找搭子意图
        // 今天回答过，且有明确意图的商户
        AICallDetailEntity todayCallDetailEntity = getTodayCallResult(dpShopId, mtShopId, detailId);
        if (todayCallDetailEntity != null) {
            // 更新detail状态为 今日记录已存在
            int todayCallBackResult = JsonCodec.decode(todayCallDetailEntity.getExtraData(), AICallDetailExtraData.class).getCallBackResult();
            aiCallDetailDAO.updateCallResult(detailId, (todayCallBackResult == AICallC2cResultEnum.C2C_SUCCESS.getCode()) ? AICallTaskDetailStatusEnum.TODAY_RECORD_SUCCESS.getCode() : AICallTaskDetailStatusEnum.TODAY_RECORD_FAILED.getCode());

            // 手动执行回调方法
            aiCallDispatchProcessService.callbackAICall(buildAICallBackReq(detailId, todayCallDetailEntity));
            return true;
        }

        return false;
    }

    @Override
    public void executePhoneCall(AICallTaskEntity taskEntity, List<AICallDetailEntity> waitCallDetails) {
        // 逐个判断是否能外呼
        for (AICallDetailEntity detailEntity : waitCallDetails) {
            if (!aiCallDispatchProcessService.judgeShopCanCall(detailEntity.getSceneCode(), detailEntity.getShopId(), detailEntity.getPlatform(), detailEntity.getId(), taskEntity)) {
                // 检查是否是今日呼过，更新状态
                ZebraForceMasterHelper.forceMasterInLocalContext();
                detailEntity = aiCallDetailDAO.getById(detailEntity.getId());
                ZebraForceMasterHelper.clearLocalContext();

                Integer existCallStatus = getExistedCallStatusByDetailId(detailEntity.getId());
                if (existCallStatus == null &&
                        detailEntity.getCallStatus() != AICallTaskDetailStatusEnum.WAIT_CALL_BACK.getCode()) {
                    aiCallDetailDAO.updateCallResult(detailEntity.getId(), AICallTaskDetailStatusEnum.CALL_SKIP.getCode());
                }
                // 如果今日有外呼成功则把task状态设置成功
                if (existCallStatus != null && existCallStatus == AICallTaskDetailStatusEnum.TODAY_RECORD_SUCCESS.getCode()) {
                    aiCallTaskDAO.updateTaskStatus(taskEntity.getId(), AICallTaskStatusEnum.CALL_SUCCESS.getCode());
                }
            } else {
                String contactId = aiCallDispatchProcessService.executeSingleShop(taskEntity, detailEntity, 0L);
                if (StringUtils.isEmpty(contactId)) {
                    aiCallDetailDAO.updateCallResult(detailEntity.getId(), AICallTaskDetailStatusEnum.CALL_FAILED.getCode());
                } else {
                    aiCallDetailDAO.updateCallResult(detailEntity.getId(), AICallTaskDetailStatusEnum.WAIT_CALL_BACK.getCode());
                    break;
                }
            }

            //发送门店外呼结果 无法发起外呼的失败 / 今日存在的失败/今日存在的成功
            aiCallDispatchProcessService.sendAICallShopResult(detailEntity.getId());
        }
    }

    @Override
    public Object buildShopResultData(long detailId) {
        AICallDetailEntity aiCallDetailEntity = aiCallDetailDAO.getById(detailId);
        AICallTaskEntity aiCallTaskEntity = aiCallTaskDAO.getById(aiCallDetailEntity.getTaskId());

        AICallDetailExtraData aiCallDetailExtraData = JsonCodec.decode(aiCallDetailEntity.getExtraData(), AICallDetailExtraData.class);
        AICallBizData aiCallBizData = JsonCodec.decode(aiCallTaskEntity.getBizData(), AICallBizData.class);

        Map<String, String> businessInfoData = aiCallBizData.getBusinessData();

        Long activityType = aiCallDetailEntity.getActivityType();
        long postId = NumberUtils.toLong(businessInfoData.get("postId"));

        int inquiryStatus = 0;
        String successInfo = "0";
        if (aiCallDetailExtraData != null && aiCallDetailExtraData.getCallBackResult() == AICallC2cResultEnum.C2C_SUCCESS.getCode()) {
            inquiryStatus = aiCallDetailExtraData.getInquiryStatus();
            successInfo = aiCallDetailExtraData.getBusinessInfo().get("billiardSuccessInfo");
        }

        AICallShopResultDTO aiCallShopResultDTO = new AICallShopResultDTO();
        aiCallShopResultDTO.setTaskId(aiCallDetailEntity.getTaskId());
        aiCallShopResultDTO.setPlatform(aiCallDetailEntity.getPlatform());
        aiCallShopResultDTO.setShopId(aiCallDetailEntity.getShopId());
        aiCallShopResultDTO.setCallResult(aiCallDetailExtraData == null ? 0 : aiCallDetailExtraData.getCallBackResult());
        aiCallShopResultDTO.setBusinessReason(buildBusinessReason(aiCallTaskEntity.getSceneCode(), activityType, successInfo));
        aiCallShopResultDTO.setPostId(postId);
        aiCallShopResultDTO.setActivityType(activityType);

        return aiCallShopResultDTO;
    }


    private String buildBusinessReason(int sceneCode, long activityType, String successInfo) {
        String businessReason = StringUtils.EMPTY;
        Map<String, Map<String, Map<String, String>>> aiCallBusinessInfoMap = lionConfigUtilTmp.getAiCallBusinessInfoMap();
        Map<String, Map<String, String>> activityReason = aiCallBusinessInfoMap.get(String.valueOf(sceneCode));
        if (org.apache.commons.collections.MapUtils.isEmpty(activityReason)) {
            return businessReason;
        }
        Map<String, String> reason = activityReason.get(String.valueOf(activityType));
        if (org.apache.commons.collections.MapUtils.isEmpty(reason)) {
            return businessReason;
        }

        businessReason = reason.getOrDefault(String.valueOf(successInfo), StringUtils.EMPTY);
        return businessReason;
    }

    private Integer getExistedCallStatusByDetailId(long detailId) {
        ZebraForceMasterHelper.forceMasterInLocalContext();
        AICallDetailEntity detailEntity = aiCallDetailDAO.getById(detailId);
        ZebraForceMasterHelper.clearLocalContext();

        if (detailEntity == null) {
            return null;
        }

        AICallDetailExtraData aiCallDetailExtraData = JsonCodec.decode(detailEntity.getExtraData(), AICallDetailExtraData.class);
        if (aiCallDetailExtraData != null && aiCallDetailExtraData.getExistedCallDetailId() > 0L) {
            return detailEntity.getCallStatus();
        }
        return null;
    }

    // 构建callbackReq
    private AICallBackReq buildAICallBackReq(long detailId, AICallDetailEntity todayCallDetailEntity) {
        AICallDetailExtraData todayAiCallDetailExtraData = JsonCodec.decode(todayCallDetailEntity.getExtraData(), AICallDetailExtraData.class);
        if (todayAiCallDetailExtraData == null) {
            return null;
        }

        AICallBackReq aiCallBackReq = new AICallBackReq();
        aiCallBackReq.setHistory(Lists.newArrayList());
        aiCallBackReq.setInquiryStatus(todayAiCallDetailExtraData.getInquiryStatus());
        aiCallBackReq.setPlayTime(todayAiCallDetailExtraData.getPlayTime() == null ? 0L : todayAiCallDetailExtraData.getPlayTime().getTime());
        aiCallBackReq.setBusinessInfo(todayAiCallDetailExtraData.getBusinessInfo());
        aiCallBackReq.setCode(todayAiCallDetailExtraData.getCallBackResult());
        aiCallBackReq.setReleaseReason(todayAiCallDetailExtraData.getReleaseReason());
        aiCallBackReq.setOrderId(detailId);
        aiCallBackReq.setPlayTypeEnum(todayCallDetailEntity.getActivityType());
        aiCallBackReq.setExtend(todayAiCallDetailExtraData.getExtend());
        return aiCallBackReq;
    }


    private boolean continousRefuseOrVague(int code, AICallDetailEntity detailEntity, long dpShopId, long mtShopId) {
        List<AICallDetailEntity> aiCallDetailEntityList = getShopCallDetailEntityList(dpShopId, mtShopId);

        if (isExceedBlockCount(aiCallDetailEntityList, code, detailEntity.getSceneCode())) {
            return true;
        }
        return false;
    }

    private boolean isExceedBlockCount(List<AICallDetailEntity> aiCallDetailEntityList, int code, Integer sceneCode) {
        if (code != AICallC2cResultEnum.C2C_FAILED.getCode() && code != AICallC2cResultEnum.C2C_VAGUE.getCode()) {
            return false;
        }
        AICallDispatchConfigData aiCallDispatchConfigDataBySceneCode = getAICallDispatchConfigDataBySceneCode(sceneCode);

        int blockCount = code == AICallC2cResultEnum.C2C_FAILED.getCode() ?
                aiCallDispatchConfigDataBySceneCode.getContinousFailBlockCnt() : aiCallDispatchConfigDataBySceneCode.getContinousVagueBlockCnt();

        int cnt = 1;
        for (AICallDetailEntity aiCallDetailEntity : aiCallDetailEntityList) {
            AICallDetailExtraData aiCallDetailExtraData = JsonCodec.decode(aiCallDetailEntity.getExtraData(), AICallDetailExtraData.class);
            if (aiCallDetailExtraData.getCallBackResult() == code) {
                cnt++;
            } else { // 连续n次
                break;
            }
            if (cnt >= blockCount) {
                return true;
            }
        }
        return false;
    }


    private List<AICallDetailEntity> getShopCallDetailEntityList(long dpShopId, long mtShopId) {

        List<AICallDetailEntity> mtAICallDetailEntities = aiCallDetailDAO.queryMultiDaysByShopId(mtShopId, PlatformEnum.MT.getType());
        List<AICallDetailEntity> dpAICallDetailEntities = aiCallDetailDAO.queryMultiDaysByShopId(dpShopId, PlatformEnum.DP.getType());

        List<AICallDetailEntity> aiCallDetailEntities = com.google.common.collect.Lists.newArrayList();
        aiCallDetailEntities.addAll(mtAICallDetailEntities);
        aiCallDetailEntities.addAll(dpAICallDetailEntities);

        List<AICallDetailEntity> aiCallDetailEntityList = aiCallDetailEntities.stream()
                .filter(entity -> entity.getCallbackTime() != null)
                .sorted(Comparator.comparing(AICallDetailEntity::getTaskId).reversed())
                .collect(Collectors.toList());
        return aiCallDetailEntityList;
    }


    private boolean isCallResultSuccess(AICallBackReq req) {
        return req.getCode() == AICallC2cResultEnum.C2C_SUCCESS.getCode() && req.getInquiryStatus() > 0;
    }

    private List<List<Long>> buildBookTimeRange(List<BookTimePeriod> timePeriodList) {
        if (CollectionUtils.isEmpty(timePeriodList)) {
            return Lists.newArrayList();
        }
        // 如果开始时间是当天0点，结束时间是次日0点，则返回空list
        if (timePeriodList.size() == 1) {
            if (judgeIfTimeRestrict(timePeriodList.get(0).getStartTime(), timePeriodList.get(0).getEndTime())) {
                return Lists.newArrayList();
            }
        }
        List<List<Long>> bookTimeRange = Lists.newArrayList();
        for (BookTimePeriod timePeriod : timePeriodList) {
            List<Long> timeRange = Lists.newArrayList();
            timeRange.add(timePeriod.getStartTime() / 1000);
            timeRange.add(timePeriod.getEndTime() / 1000);
            bookTimeRange.add(timeRange);
        }

        return bookTimeRange;
    }

    // 判断是否是时间不限
    private boolean judgeIfTimeRestrict(long startTimeStamp, long endTimeStamp) {
        Calendar today = Calendar.getInstance();
        today.set(Calendar.HOUR_OF_DAY, 0);
        today.set(Calendar.MINUTE, 0);
        today.set(Calendar.SECOND, 0);
        today.set(Calendar.MILLISECOND, 0);

        Calendar tomorrow = (Calendar) today.clone();
        tomorrow.add(Calendar.DAY_OF_YEAR, 1);
        return today.getTimeInMillis() == startTimeStamp && tomorrow.getTimeInMillis() == endTimeStamp;
    }


    private String getTimePeriodDate(List<BookTimePeriod> timePeriodList) {
        if (CollectionUtils.isEmpty(timePeriodList)) {
            return StringUtils.EMPTY;
        }
        BookTimePeriod timePeriod = timePeriodList.stream().min(Comparator.comparing(BookTimePeriod::getStartTime)).orElse(null);
        if (timePeriod == null) {
            return StringUtils.EMPTY;
        }
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        long startTime = timePeriod.getStartTime();
        return dateFormat.format(startTime);
    }

    private Long randomGetCallBotId(int genderValue, Integer sceneCode) {
        AICallDispatchConfigData aiCallDispatchConfigDataBySceneCode = getAICallDispatchConfigDataBySceneCode(sceneCode);

        if (genderValue == Gender.MALE.getValue()) {
            List<Long> callMale = aiCallDispatchConfigDataBySceneCode.getCallMaleBots();
            if (CollectionUtils.isNotEmpty(callMale)) {
                Cat.logEvent("AISelectBot", "CallMale");
                return callMale.get(new Random().nextInt(callMale.size())); // 随机选择一个
            }
        } else {
            List<Long> callFemale = aiCallDispatchConfigDataBySceneCode.getCallFemaleBots();
            if (CollectionUtils.isNotEmpty(callFemale)) {
                Cat.logEvent("AISelectBot", "CallFemale");
                return callFemale.get(new Random().nextInt(callFemale.size())); // 随机选择一个
            }
        }
        LogUtils.logFailLog(log, TagContext.builder().action("randomGetCallBotId").build(),
                new WarnMessage("C2cHandleStrategy", "随机选取外呼机器人异常", ""),
                Arrays.asList(sceneCode, genderValue), null);
        return null;
    }

    private AICallDetailEntity getTodayCallResult(long dpShopId, long mtShopId, long detailId) {
        List<AICallDetailEntity> todayCallDetail = Lists.newArrayList();
        List<AICallDetailEntity> dpAiCallDetailEntities = aiCallDetailDAO.queryTodayCallResult(dpShopId, VCPlatformEnum.DP.getType());
        List<AICallDetailEntity> mtAiCallDetailEntities = aiCallDetailDAO.queryTodayCallResult(mtShopId, VCPlatformEnum.MT.getType());
        todayCallDetail.addAll(dpAiCallDetailEntities);
        todayCallDetail.addAll(mtAiCallDetailEntities);

        if (CollectionUtils.isEmpty(todayCallDetail)) {
            return null;
        }

        // 判断商家回复是否有明确意图
        // 找到今天有明确意图的记录
        AICallDetailEntity detailHasClearIntention = todayCallDetail.stream().filter(
                detail -> {
                    AICallDetailExtraData aiCallDetailExtraData = JsonCodec.decode(detail.getExtraData(), AICallDetailExtraData.class);
                    int callBackResult = aiCallDetailExtraData.getCallBackResult();
                    return callBackResult == AICallC2cResultEnum.C2C_SUCCESS.getCode() ||
                            callBackResult == AICallC2cResultEnum.C2C_FAILED.getCode();
                }
        ).findFirst().orElse(null);

        // 无明确意图记录
        if (detailHasClearIntention == null) {
            return null;
        }

        Long hasClearIntentionDetailId = detailHasClearIntention.getId();
        AICallDetailExtraData aiCallDetailExtraData = JsonCodec.decode(detailHasClearIntention.getExtraData(), AICallDetailExtraData.class);

        // 这通电话保存有意图电话的id和商家意图
        AICallDetailExtraData detailExtraData = new AICallDetailExtraData();
        detailExtraData.setExistedCallDetailId(hasClearIntentionDetailId);
        detailExtraData.setCallBackResult(aiCallDetailExtraData.getCallBackResult());
        detailExtraData.setInquiryStatus(aiCallDetailExtraData.getInquiryStatus());
        detailExtraData.setBusinessInfo(aiCallDetailExtraData.getBusinessInfo());

        // 更新本次外呼detailExtra 和 状态

        aiCallDetailDAO.updateExtraData(detailId, JsonCodec.encodeWithUTF8(detailExtraData));

//        return JsonCodec.decode(detailHasClearIntention.getExtraData(), AICallDetailExtraData.class).getCallBackResult();
        return detailHasClearIntention;
    }

    private List<Long> filterShops(UserCallRequest request, List<Long> shopIds) {
        long startTime = System.currentTimeMillis();
        Map<Long, Long> shopIdMap = getShopIdMap(shopIds, request.getPlatform());

        // 营业状态过滤、门店类目过滤
        filterShopByBusinessStatus(request, shopIds, shopIdMap);
        // 营业时间过滤
        if (CollectionUtils.isNotEmpty(shopIds)) {
            shopIds = filterShopByBizTime(request, shopIds, shopIdMap);
        }
        // 黑名单过滤
        if (CollectionUtils.isNotEmpty(shopIds)) {
            shopIds = filterShopByBlackList(request, shopIds, shopIdMap);
        }
        // 单日黑名单商户过滤
        if (CollectionUtils.isNotEmpty(shopIds)) {
            shopIds = filterShopDailyBlackList(request, shopIds, shopIdMap);
        }
        // 多日黑名单商户过滤
        if (CollectionUtils.isNotEmpty(shopIds)) {
            shopIds = filterShopMultiDayBlackList(request, shopIds, shopIdMap);
        }
        // 单日拨打次数过滤
        if (CollectionUtils.isNotEmpty(shopIds)) {
            shopIds = filterShopByCallCnt(request, shopIds, shopIdMap);
        }

        Cat.newCompletedTransactionWithDuration("AICallRecallShop", "filterShops", System.currentTimeMillis() - startTime);
        return shopIds;
    }

    private List<Long> filterShopByCallCnt(UserCallRequest request, List<Long> shopIds, Map<Long, Long> shopIdMap) {

        AICallDispatchConfigData aiCallDispatchConfigDataBySceneCode = getAICallDispatchConfigDataBySceneCode(request.getSceneCode());

        if (aiCallDispatchConfigDataBySceneCode == null) {
            return shopIds;
        }
        return shopIds.stream().filter(shopId -> {
            long mtShopId = shopId;
            if (request.getPlatform() == PlatformEnum.DP.getType()) {
                mtShopId = shopIdMap.getOrDefault(shopId, 0L);
            }
            StoreKey shopCallCntKey = new StoreKey(AICallConstants.AI_CALL_SHOP_CNT_CATEGORY, mtShopId, DateUtils.covertDateNumStr(new Date()));
            Object shopCallCNt = redisStoreClient.get(shopCallCntKey);
            int shopCallCnt = shopCallCNt == null ? 0 : NumberUtils.toInt(shopCallCNt.toString());
            return !(shopCallCnt > aiCallDispatchConfigDataBySceneCode.getShopCallCntLimit());
        }).collect(Collectors.toList());
    }

    private List<Long> filterShopByBizTime(UserCallRequest request, List<Long> shopIds, Map<Long, Long> shopIdMap) {
        // 计算出预约时间段内所有日期和半小时粒度索引
        Map<String, List<String>> dateKey2HalfHourIndex = getBetweenTimeHalfHourIndex(request.getBookTimePeriods());
        if (org.apache.commons.collections.MapUtils.isEmpty(dateKey2HalfHourIndex)) {
            return shopIds;
        }
        // 判断门店在预约时间内是否开门
        return shopIds.stream().filter(shopId -> {
            long dpShopId = shopId;
            if (request.getPlatform() == PlatformEnum.MT.getType()) {
                dpShopId = shopIdMap.getOrDefault(shopId, 0L);
            }
            return isShopBizTimeOpen(dpShopId, dateKey2HalfHourIndex);
        }).collect(Collectors.toList());
    }

    private boolean isShopBizTimeOpen(long dpShopId, Map<String, List<String>> dateKey2HalfHourIndex) {
        for (Map.Entry<String, List<String>> entry : dateKey2HalfHourIndex.entrySet()) {
            String dateKey = entry.getKey();
            List<String> halfHourIndex = entry.getValue();
            String dateStr = DateUtils.parseDateKey2DateStr(dateKey);
            if (StringUtils.isEmpty(dateStr)) {
                continue;
            }
            BizForecastDTO bizForecast = shopAclService.getBizForecast(dpShopId, dateStr);
            if (bizForecast == null || StringUtils.isEmpty(bizForecast.getToday())) {
                continue;
            }
            if (isShopCurrentDayOpen(bizForecast, halfHourIndex)) {
                return true;
            }
        }
        return false;
    }

    private boolean isShopCurrentDayOpen(BizForecastDTO bizForecast, List<String> halfHourIndex) {

        String today = bizForecast.getToday();
        for (String halfHour : halfHourIndex) {
            int halfHourNum = NumberUtils.toInt(halfHour);
            if (halfHourNum < 0) {
                continue;
            }
            if (today.charAt(halfHourNum) == '1') {
                return true;
            }
        }
        return false;
    }

    private Map<String, List<String>> getBetweenTimeHalfHourIndex(List<BookTimePeriod> bookTimePeriods) {
        List<Pair<Date, Date>> datePairTimePeriod = Lists.newArrayList();

        for (BookTimePeriod bookTimePeriod : bookTimePeriods) {
            Date startDate = new Date(bookTimePeriod.getStartTime());
            Date endDate = new Date(bookTimePeriod.getEndTime());
            datePairTimePeriod.add(Pair.of(startDate, endDate));
        }

        Map<String, List<String>> result = Maps.newHashMap();
        // 半小时对应的毫秒数
        long oneHalfHourInMillis = 30 * 60 * 1000L;
        for (Pair<Date, Date> timePeriodPair : datePairTimePeriod) {
            for (long currentTime = timePeriodPair.getLeft().getTime(); currentTime < timePeriodPair.getRight().getTime(); currentTime += oneHalfHourInMillis) {
                Date currentDate = new Date(currentTime);
                String dateKey = DateUtils.covertDateNumStr(currentDate);
                List<String> currentDayIndex = result.getOrDefault(dateKey, com.google.common.collect.Lists.newArrayList());
                currentDayIndex.add(String.valueOf(getHalfHourIndex(currentDate)));
                result.put(dateKey, currentDayIndex);
            }
        }

        return result;
    }

    private int getHalfHourIndex(Date date) {
        LocalDateTime currentLocalDateTime = new LocalDateTime(date, DateTimeZone.getDefault());
        int hour = currentLocalDateTime.getHourOfDay();
        int minute = currentLocalDateTime.getMinuteOfHour();
        return hour * 2 + (minute >= 30 ? 1 : 0);
    }

    private List<Long> filterShopDailyBlackList(UserCallRequest request, List<Long> shopIds, Map<Long, Long> shopIdMap) {
        return shopIds.stream().filter(shopId -> {
            long mtShopId = shopId;
            if (request.getPlatform() == PlatformEnum.DP.getType()) {
                mtShopId = shopIdMap.getOrDefault(shopId, 0L);
            }
            StoreKey shopPhoneBlackListKey = new StoreKey(AICallConstants.AI_CALL_SHOP_DAILY_BLACK_LIST, mtShopId, DateUtils.covertDateNumStr(new Date()));
            return !redisStoreClient.exists(shopPhoneBlackListKey);
        }).collect(Collectors.toList());
    }

    private List<Long> filterShopMultiDayBlackList(UserCallRequest request, List<Long> shopIds, Map<Long, Long> shopIdMap) {
        return shopIds.stream().filter(shopId -> {
            long mtShopId = shopId;
            if (request.getPlatform() == PlatformEnum.DP.getType()) {
                mtShopId = shopIdMap.getOrDefault(shopId, 0L);
            }
            StoreKey shopMultiDayListKey = new StoreKey(AICallConstants.AI_CALL_SHOP_MULTI_DAY_BLACK_LIST, mtShopId);
            return !redisStoreClient.exists(shopMultiDayListKey);
        }).collect(Collectors.toList());
    }

    private List<Long> filterShopByBlackList(UserCallRequest request, List<Long> shopIds, Map<Long, Long> shopIdMap) {

        AICallDispatchConfigData aiCallDispatchConfigData = getAICallDispatchConfigDataBySceneCode(request.getSceneCode());

        if (CollectionUtils.isEmpty(aiCallDispatchConfigData.getDpShopIdBlackList())) {
            return shopIds;
        }
        List<Long> dpShopIdBlackList = aiCallDispatchConfigData.getDpShopIdBlackList();
        return shopIds.stream().filter(shopId -> {
            long dpShopId = shopId;
            if (request.getPlatform() == PlatformEnum.MT.getType()) {
                dpShopId = shopIdMap.getOrDefault(shopId, 0L);
            }
            return !dpShopIdBlackList.contains(dpShopId);
        }).collect(Collectors.toList());
    }

    private AICallDispatchConfigData getAICallDispatchConfigDataBySceneCode(Integer sceneCode) {
        Map<String, AICallDispatchConfigData> aiCallDispatchConfigDataMap = lionConfigUtilTmp.getAiCallDispatchConfigDataMap();
        AICallDispatchConfigData defaultAICallDispatchConfig = aiCallDispatchConfigDataMap.get(DEFAULT_DISPATCH_CONFIG);

        return aiCallDispatchConfigDataMap.getOrDefault(String.valueOf(sceneCode), defaultAICallDispatchConfig);
    }

    private List<Long> filterShopByBusinessStatus(UserCallRequest request, List<Long> shopIds, Map<Long, Long> shopIdMap) {
        List<Long> mtShopIds = shopIds;
        if (request.getPlatform() == PlatformEnum.DP.getType()) {
            mtShopIds = shopIds.stream().map(dpShopId -> shopIdMap.getOrDefault(dpShopId, 0L)).filter(mtShopId -> mtShopId > 0).collect(Collectors.toList());
        }

        Map<Long, MtPoiDTO> mtShopInfos = shopAclService.batchGetMtShopInfo(mtShopIds);

        return shopIds.stream().filter(shopId -> {
            long mtShopId = shopId;
            if (request.getPlatform() == PlatformEnum.DP.getType()) {
                mtShopId = shopIdMap.getOrDefault(shopId, 0L);
            }
            MtPoiDTO mtPoiDTO = mtShopInfos.get(mtShopId);
            return isShopCateBizMatch(mtPoiDTO, request.getCatId());
        }).collect(Collectors.toList());
    }

    private boolean isShopCateBizMatch(MtPoiDTO mtPoiDTO, Integer catId) {
        if (mtPoiDTO == null) {
            return false;
        }
        // 状态判断
        if (mtPoiDTO.getCloseStatus() != 0) {
            return false;
        }
        // 类目判断
        List<TypeHierarchyView> typeHierarchy = mtPoiDTO.getTypeHierarchy();
        List<Integer> typeHierarchyIds = typeHierarchy.stream().map(TypeHierarchyView::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(typeHierarchyIds)) {
            return false;
        }
        if (catId != null && !typeHierarchyIds.contains(catId)) {
            return false;
        }
        return true;
    }

    private Map<Long, Long> getShopIdMap(List<Long> shopIds, int platform) {
        if (platform == PlatformEnum.MT.getType()) {
            return shopAclService.queryDpIdLByMtIdL(shopIds).join();
        }
        return shopAclService.queryMtIdLByDpIdL(shopIds).join();
    }

    private RecommendParameters buildRecommendParameters(UserCallRequest request, int pageNo, int pageSize) {
        AICallRecommandShopConfigData aiCallRecommandShopConfigData = lionConfigUtilTmp.getAiCallRecommandShopConfigDataMap()
                .get(String.valueOf(request.getSceneCode()));

        RecommendParameters recommendParameters = new RecommendParameters();
        recommendParameters.setBizId(aiCallRecommandShopConfigData.getBizId()); //商户推荐
        recommendParameters.setCityId(request.getCityId()); //城市id
        recommendParameters.setOriginUserId(String.valueOf(request.getUserId())); // 用户Id字符串，缺失传入null
        recommendParameters.setPlatformEnum(request.getPlatform() == PlatformEnum.DP.getType() ?
                com.dianping.martgeneral.recommend.api.enums.PlatformEnum.DP : com.dianping.martgeneral.recommend.api.enums.PlatformEnum.MT);
        recommendParameters.setLat(request.getLat());
        recommendParameters.setLng(request.getLng());
        recommendParameters.setPageSize(pageSize);
        recommendParameters.setPageNumber(pageNo);
        if (request.getPlatform() == PlatformEnum.MT.getType()) {
            recommendParameters.setUuid(StringUtils.isBlank(request.getDeviceId()) ? StringUtils.EMPTY : request.getDeviceId()); //美团侧需要uuid
        } else {
            recommendParameters.setDpid(StringUtils.isBlank(request.getDeviceId()) ? StringUtils.EMPTY : request.getDeviceId());
        }

        Map<String, Object> bizParams = new HashMap<>();
        // 设置召回类目
        bizParams.put("genericCatIds", String.valueOf(request.getCatId()));
        // 设置召回距离
        bizParams.put("distanceRange", aiCallRecommandShopConfigData.getDistanceRange());
        recommendParameters.setBizParams(bizParams);
        return recommendParameters;
    }
}
