package com.sankuai.dzim.pilot.process.data;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum ChannelSearchSourceEnum {
    EYE_GLASS("eyeglass_filterhome", "眼镜"),
    BABY_CARE("babycareindex", "亲子"),
    OVERSEA_MEDICAL("overseaMedical", "境外医疗"),
    CAR("car_filterhome", "养车用车频道页"),
    JOY_V3_HOME("joy_v3home", "休娱频道页"),
    MEDICAL_BEAUTY("medicalbeautyhome", "医美频道页"),
    MAN("manhome", " 理发男士频道页"),
    SPORT("sporthome", "运动健身频道页"),
    PET("pethome", "宠物频道页"),
    LIFE("easy_life_v2home", "生活服务频道页"),
    MEDICAL_CARE("medical_carehome", "医疗牙科频道页"),
    EDUCATION("education_v2home", "学习培训频道页"),
    DECORATE("decorate_home_v2", "家居装修频道页"),
    HEALTH_CHECK("health_checkuphome", "体检频道页"),
    HAIR_V2("nib.general.hair_v2", "旧美发频道页");


    public final String source;

    public final String name;

    ChannelSearchSourceEnum(String source, String name) {
        this.source = source;
        this.name = name;
    }

    public static  String getNameBySource(String source) {
        for (ChannelSearchSourceEnum s : ChannelSearchSourceEnum.values()) {
            if (Objects.equals(s.getSource(), source)) {
                return s.getName();
            }
        }
        return "";
    }
}