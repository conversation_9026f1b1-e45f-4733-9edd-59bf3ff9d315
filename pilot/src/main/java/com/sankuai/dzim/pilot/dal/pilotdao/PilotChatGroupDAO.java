
package com.sankuai.dzim.pilot.dal.pilotdao;

import com.sankuai.dzim.pilot.dal.entity.pilot.PilotChatGroupEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/7/29 14:20
 */
public interface PilotChatGroupDAO {
    PilotChatGroupEntity queryChatGroupByUserIdAndType(@Param("userId") String userId, @Param("assistantType") int assistantType);

    int insertChatGroup(@Param("entity") PilotChatGroupEntity pilotChatGroupEntity);

    int updateChatGroup(@Param("entity") PilotChatGroupEntity pilotChatGroupEntity);

    PilotChatGroupEntity queryChatGroupById(@Param("id") long id);

    int updateChatGroupMessage(@Param("chatGroupId") long chatGroupId, @Param("startMessageId") long startMessageId,
                        @Param("lastMessageId") long lastMessageId);

    List<PilotChatGroupEntity> queryStartMessageIdByUserId(@Param("userId") String userId,
                                                           @Param("offset") long offset, @Param("pageSize") int pageSize);

    int queryChatGroupCntByUserId(@Param("userId") String userId);
}
