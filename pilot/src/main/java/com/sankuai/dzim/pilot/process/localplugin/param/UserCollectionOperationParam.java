package com.sankuai.dzim.pilot.process.localplugin.param;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025/04/21 11:08
 */
@Data
public class UserCollectionOperationParam extends Param{
    @JsonProperty(required = true)
    @JsonPropertyDescription("要收藏的门店id，不要捏造，请从上下文中获取，获取不到不要使用该工具")
    private long shopId;

    @JsonProperty(required = true)
    @JsonPropertyDescription("收藏操作类型，1:收藏，2:取消收藏")
    private int operateType;
}