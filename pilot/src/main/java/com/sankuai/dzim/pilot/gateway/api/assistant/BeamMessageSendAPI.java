package com.sankuai.dzim.pilot.gateway.api.assistant;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.gateway.common.context.UserResolvers;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Maps;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.api.enums.assistant.AssistantTypeEnum;
import com.sankuai.dzim.pilot.api.enums.assistant.MetricEventNameEnum;
import com.sankuai.dzim.pilot.buffer.enums.StreamEventErrorTypeEnum;
import com.sankuai.dzim.pilot.buffer.stream.build.StreamEventVOBuilder;
import com.sankuai.dzim.pilot.domain.data.BeamFixedContentConfig;
import com.sankuai.dzim.pilot.gateway.api.assistant.data.SendBeamMessageReq;
import com.sankuai.dzim.pilot.gateway.api.assistant.data.SseAwareException;
import com.sankuai.dzim.pilot.process.MessageSendProcessService;
import com.sankuai.dzim.pilot.scene.data.MetricConstants;
import com.sankuai.dzim.pilot.scene.data.MetricContext;
import com.sankuai.dzim.pilot.utils.LionConfigUtil;
import com.sankuai.dzim.pilot.utils.SseHeaderUtil;
import com.sankuai.dzim.pilot.utils.ThreadPoolUtil;
import com.sankuai.dzim.pilot.utils.context.RequestContext;
import com.sankuai.dzim.pilot.utils.context.RequestContextConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

@RestController
@Slf4j
public class BeamMessageSendAPI {

    @Autowired
    private MessageSendProcessService messageSendProcessService;

    @Autowired
    private LionConfigUtil lionConfigUtil;

    @Autowired
    private UserResolvers userResolvers;

    @PostMapping(value = "/dzim/pilot/assistant/fwls/agent/chat", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter sendMessage(@RequestBody SendBeamMessageReq sendBeamMessageReq, HttpServletRequest httpServletRequest,
                                  HttpServletResponse httpServletResponse) {

        // 1.创建一个SseEmitter对象
        SseEmitter sseEmitter = new SseEmitter(600000L);

        // 2.流式输出设置响应头
        SseHeaderUtil.setSseResponseHeader(httpServletResponse);

        // 3.异步开启消息流程
        CompletableFuture.runAsync(() -> {
            try {
                doSendMessage(sseEmitter, sendBeamMessageReq, httpServletRequest);
            } catch (IOException e) {
                log.error("doSendMessage error", e);
            }
        }, ThreadPoolUtil.ASSISTANT_SSE_CONNECTION_POOL.getExecutor());

        return sseEmitter;
    }

    private void doSendMessage(SseEmitter sseEmitter, SendBeamMessageReq sendBeamMessageReq, HttpServletRequest httpServletRequest) throws IOException {
        try {

            log.info("BeamMessageSendAPI sendMessage req = {}", JsonCodec.encodeWithUTF8(sendBeamMessageReq));
            BeamFixedContentConfig beamFixedContentConfig = lionConfigUtil.getBeamFixedContentConfig();
            try {
                // 1.参数校验
                checkParam(sendBeamMessageReq);

                // 2.优化用户画像
                adjustUserProfile(sendBeamMessageReq);

                // 3.登录鉴权
                checkUserLogin(sendBeamMessageReq, httpServletRequest);

                // TEST 测试门店id替换
                replaceShopIdForTest(sendBeamMessageReq);

                // 4.写入ThreadLocal
                RequestContext.init();
                RequestContext.setAttribute(RequestContextConstants.SSE_EMITTER, sseEmitter);
                RequestContext.setAttribute(RequestContextConstants.METRIC_CONTEXT, buildMetricContext());

                // 5.发送消息
                messageSendProcessService.sendBeamMessage(sendBeamMessageReq);
            } catch (SseAwareException e) {
                LogUtils.logFailLog(log, TagContext.builder().action("sendBeamMessage").build(),
                        new WarnMessage("BeamMessageSendAPI", "发送Beam消息API异常", ""), sendBeamMessageReq, e);
                sseEmitter.send(StreamEventVOBuilder.buildBeamErrorEvent(beamFixedContentConfig.getBeamErrorContent()));
            } catch (Exception e) {
                LogUtils.logFailLog(log, TagContext.builder().action("sendBeamMessage").build(),
                        new WarnMessage("BeamMessageSendAPI", "发送Beam消息API异常", ""), sendBeamMessageReq, e);
                sseEmitter.send(StreamEventVOBuilder.buildBeamErrorEvent(beamFixedContentConfig.getBeamErrorContent()));
            }
            sseEmitter.send(StreamEventVOBuilder.buildBeamCloseEvent());
        } finally {
            // 7.关闭SseEmitter对象
            sseEmitter.complete();
            // 8.埋点统一上报
            metricReport();
            // 9.清理ThreadLocal
            RequestContext.cleanup();
        }
    }

    private void checkUserLogin(SendBeamMessageReq sendBeamMessageReq, HttpServletRequest httpServletRequest) {
        if (!lionConfigUtil.isBeamUserLoginCheckSwitch()) {
            return;
        }
        long userIdFromToken = userResolvers.loadMTUserId(httpServletRequest);
        Assert.isTrue(userIdFromToken == NumberUtils.toLong(sendBeamMessageReq.getUser_info().getUserid()), "未登录");
    }

    private void adjustUserProfile(SendBeamMessageReq sendBeamMessageReq) {
        String userProfile = sendBeamMessageReq.getContext().getUser_profile();
        sendBeamMessageReq.getContext().setUser_profile(getAdjustUserProfile(userProfile));
    }

    private String getAdjustUserProfile(String userProfile) {
        if (StringUtils.isBlank(userProfile)) {
            return "";
        }
        JSONObject userProfileJson = JSONObject.parseObject(userProfile);
        if (userProfileJson == null) {
            return "";
        }

        // 保留指定的 key
        Set<String> keysToKeep = new HashSet<>(Arrays.asList("性别", "年龄段", "职业", "购买力", "婚姻状况", "常住地", "优惠敏感度", "工作地址", "住宅地址", "近一年复购行为", "近期状态"));
        JSONObject filteredProfileJson = new JSONObject();
        for (String key : keysToKeep) {
            if (userProfileJson.containsKey(key)) {
                filteredProfileJson.put(key, userProfileJson.get(key));
            }
        }
        return filteredProfileJson.toJSONString();
    }

    private void replaceShopIdForTest(SendBeamMessageReq sendBeamMessageReq) {
        Map<String, String> testShopIdReplaceConfig = lionConfigUtil.getTestShopIdReplaceConfig();
        Map<String, String> testShopIdReplaceByUserIdConfig = lionConfigUtil.getTestShopIdReplaceByUserIdConfig();
        String originalPoiId = sendBeamMessageReq.getContext().getPoi_id();
        if (testShopIdReplaceConfig.containsKey(originalPoiId)) {
            sendBeamMessageReq.getContext().setPoi_id(testShopIdReplaceConfig.get(originalPoiId));
            return;
        }
        if (testShopIdReplaceConfig.containsKey("default")) {
            sendBeamMessageReq.getContext().setPoi_id(testShopIdReplaceConfig.get("default"));
        }

        String userId = sendBeamMessageReq.getUser_info().getUserid();
        if (testShopIdReplaceByUserIdConfig.containsKey(userId)) {
            sendBeamMessageReq.getContext().setPoi_id(testShopIdReplaceByUserIdConfig.get(userId));
        }
    }

    private void checkParam(SendBeamMessageReq sendBeamMessageReq) {
        Assert.isTrue(sendBeamMessageReq != null, "请求不能为空");
        Assert.isTrue(StringUtils.isNotEmpty(sendBeamMessageReq.getContext().getPoi_id()), "门店id不能为空");
    }

    private void metricReport() {
        MetricContext metricContext = RequestContext.getAttribute(RequestContextConstants.METRIC_CONTEXT);
        String toolCallName = RequestContext.getAttribute(RequestContextConstants.BEAM_TOOL_CALL_NAME);
        try {
            if (metricContext == null || MapUtils.isEmpty(metricContext.getEventName2EventTime())) {
                return;
            }

            int assistantType = metricContext.getAssistantType();
            Map<String, Long> eventName2Val = metricContext.getEventName2EventTime();
            for (MetricEventNameEnum eventNameEnum : MetricEventNameEnum.values()) {
                Long eventTime = eventName2Val.get(eventNameEnum.getEventCode());
                if (eventTime == null || eventTime <= 0) {
                    continue;
                }
                String eventName = eventNameEnum.getEventCode();
                if (StringUtils.isNotEmpty(toolCallName)) {
                    eventName = eventName + "_" + toolCallName;
                }
                long duration = eventTime - metricContext.getStartTimestamp();
                Cat.newCompletedTransactionWithDuration(MetricConstants.ASSISTANT_TRANSACTION_TYPE + "_" + assistantType,
                        eventName , duration);
            }
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("AssistantMetricReportError").build(),
                    WarnMessage.build("metricReport", "小助手打点上报", ""), metricContext, e);
        }
    }

    private MetricContext buildMetricContext() {
        MetricContext context = new MetricContext();
        context.setStartTimestamp(System.currentTimeMillis());
        context.setAssistantType(AssistantTypeEnum.BEAM_FWLS_AGENT.getType());
        context.setEventName2EventTime(Maps.newHashMap());
        return context;
    }
}
