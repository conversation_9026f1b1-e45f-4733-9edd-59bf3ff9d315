package com.sankuai.dzim.pilot.process.localplugin;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.meituan.nibtp.trade.client.combine.bean.WebParamDTO;
import com.meituan.nibtp.trade.client.combine.enums.AgentPlatformOrderStatus;
import com.meituan.nibtp.trade.client.combine.requset.AgentCreateOrderReqDTO;
import com.meituan.nibtp.trade.client.combine.requset.AgentQueryOrderReqDTO;
import com.meituan.nibtp.trade.client.combine.requset.AgentQueryOrderReqItemDTO;
import com.meituan.nibtp.trade.client.combine.response.AgentCreateOrderResDTO;
import com.meituan.nibtp.trade.client.combine.response.AgentOrderDTO;
import com.meituan.nibtp.trade.client.combine.response.AgentOrderItemDTO;
import com.meituan.nibtp.trade.client.combine.response.AgentQueryOrderResDTO;
import com.sankuai.dzim.pilot.acl.AgentTradeAclService;
import com.sankuai.dzim.pilot.api.data.AIAnswerTypeEnum;
import com.sankuai.dzim.pilot.buffer.core.PilotBufferItemDO;
import com.sankuai.dzim.pilot.buffer.enums.BufferItemTypeEnum;
import com.sankuai.dzim.pilot.buffer.enums.StreamEventCardTypeEnum;
import com.sankuai.dzim.pilot.buffer.stream.build.ext.data.BeamDealOrderCreateData;
import com.sankuai.dzim.pilot.buffer.stream.build.ext.data.FwslDealOrder;
import com.sankuai.dzim.pilot.buffer.stream.build.ext.data.FwslDealPreview;
import com.sankuai.dzim.pilot.buffer.utils.BufferUtils;
import com.sankuai.dzim.pilot.chain.data.AIAnswerData;
import com.sankuai.dzim.pilot.domain.annotation.LocalPlugin;
import com.sankuai.dzim.pilot.gateway.api.assistant.data.SendBeamMessageReq;
import com.sankuai.dzim.pilot.process.localplugin.param.BeamCreateOrderParam;
import com.sankuai.dzim.pilot.utils.PluginContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Beam交易下单工具
 *
 * <AUTHOR>
 * @date 2025/4/29
 */
@Component
@Slf4j
public class BeamCreateOrderPlugin {

    @Resource
    private AgentTradeAclService agentTradeAclService;

    private static final List<Integer> CANCEL_STATUS_LIST = Lists.newArrayList(
            AgentPlatformOrderStatus.CANCELLED_TIMEOUT.getCode(),
            AgentPlatformOrderStatus.CANCELLED_BY_USER.getCode()
    );

    @LocalPlugin(name = "CreateOrderTool", description = "下单支付工具。在生成交易预览后，使用该工具进行下单支付。", returnDirect = true)
    public AIAnswerData getBeamCreateOrderAnswer(BeamCreateOrderParam beamCreateOrderParam) {
        log.info("CreateOrderTool invoke param: {}", JsonCodec.encodeWithUTF8(beamCreateOrderParam));

        // 1. 基础校验
        AIAnswerData answerData = initAnswerData();
        SendBeamMessageReq sendBeamMessageReq = validateRequest(beamCreateOrderParam);
        if (sendBeamMessageReq == null) {
            return answerData;
        }

        // 2. 构建参数
        WebParamDTO webParamDTO = agentTradeAclService.buildWebParam(sendBeamMessageReq);
        if (webParamDTO == null) {
            return null;
        }

        FwslDealOrder fwslDealOrder = null;
        FwslDealPreview fwslDealPreview = null;
        if (sendBeamMessageReq.getContext() != null && sendBeamMessageReq.getContext().getBusiness_data() != null) {
            fwslDealOrder = getFwlsDealOrder(beamCreateOrderParam, sendBeamMessageReq.getContext().getBusiness_data());
            fwslDealPreview = getFwlsDealPreview(beamCreateOrderParam, sendBeamMessageReq.getContext().getBusiness_data());
        }
        log.info("CreateOrderTool, getBeamCreateOrderAnswer fwslDealOrder = {}, fwslDealPreview = {}", JsonCodec.encodeWithUTF8(fwslDealOrder), JsonCodec.encodeWithUTF8(fwslDealPreview));

        if (fwslDealOrder != null && CollectionUtils.isNotEmpty(fwslDealOrder.getOrderList())) {
            return buildCreateOrderAnswer(webParamDTO, fwslDealOrder);
        }

        if (fwslDealPreview != null && fwslDealPreview.getPreview_id() != null) {
            return buildSecondPaymentAnswer(webParamDTO, fwslDealPreview);
        }

        BufferUtils.writeMainTextBuffer(PilotBufferItemDO.builder().data("下单失败，参数异常").build());
        answerData.setAnswer("下单失败，参数异常");
        return answerData;
    }

    private AIAnswerData buildCreateOrderAnswer(WebParamDTO webParamDTO, FwslDealOrder fwslDealOrder) {
        AIAnswerData answerData = initAnswerData();
        AgentQueryOrderReqDTO agentQueryOrderReqDTO = buildAgentQueryOrderReqDTO(webParamDTO, fwslDealOrder);
        AgentQueryOrderResDTO agentQueryOrderResDTO = agentTradeAclService.queryOrder(agentQueryOrderReqDTO);
        if (agentQueryOrderResDTO == null || agentQueryOrderResDTO.isFail() || CollectionUtils.isEmpty(agentQueryOrderResDTO.getOrderList())) {
            BufferUtils.writeMainTextBuffer(PilotBufferItemDO.builder().data("下单失败，请稍候重试").build());
            log.info("调用二次支付工具 agentTradeAclService.queryOrder 失败, req = {}, resp = {}", JsonCodec.encodeWithUTF8(agentQueryOrderReqDTO), JsonCodec.encodeWithUTF8(agentQueryOrderResDTO));
            return answerData;
        }

        AgentOrderDTO agentOrderDTO = agentQueryOrderResDTO.getOrderList().get(0);
        if (AgentPlatformOrderStatus.WAIT_PAY.getCode() == agentOrderDTO.getPlatformOrderStatus()) {
            BeamDealOrderCreateData beamDealOrderCreateData = new BeamDealOrderCreateData();
            beamDealOrderCreateData.setOrderId(agentOrderDTO.getOrderId());
            beamDealOrderCreateData.setTradeNo(agentOrderDTO.getMtPayResult().getTradeNo());
            beamDealOrderCreateData.setPayToken(agentOrderDTO.getMtPayResult().getPayToken());

            PilotBufferItemDO pilotBufferItemDO = new PilotBufferItemDO();
            pilotBufferItemDO.setType(BufferItemTypeEnum.MAIN_TEXT.getType());
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("orderId", agentOrderDTO.getOrderId());
            if (CollectionUtils.isNotEmpty(agentOrderDTO.getOrderItemList())) {
                AgentOrderItemDTO orderItemDTO = agentOrderDTO.getOrderItemList().get(0);
                jsonObject.put("productId", orderItemDTO.getProductId());
                jsonObject.put("productName", orderItemDTO.getProductName());
                jsonObject.put("count", orderItemDTO.getCount());
            }

            String data = StreamEventCardTypeEnum.buildCardContent(StreamEventCardTypeEnum.BEAM_DEAL_ORDER_CREATE, JSON.toJSONString(jsonObject));
            pilotBufferItemDO.setData("请支付\n" + data);
            pilotBufferItemDO.setExtra(JsonCodec.converseMap(JsonCodec.encodeWithUTF8(beamDealOrderCreateData), String.class, Object.class));
            BufferUtils.writeMainTextBuffer(pilotBufferItemDO);
            answerData.setAnswer("二次支付成功～");
            return answerData;
        }

        if (CANCEL_STATUS_LIST.contains(agentOrderDTO.getPlatformOrderStatus())) {
            BufferUtils.writeMainTextBuffer(PilotBufferItemDO.builder().data("订单已取消").build());
            return answerData;
        }

        BufferUtils.writeMainTextBuffer(PilotBufferItemDO.builder().data("二次支付失败").build());
        return answerData;
    }

    private String buildProductNameList(AgentOrderDTO agentOrderDTO) {
        if (CollectionUtils.isEmpty(agentOrderDTO.getOrderItemList())) {
            return StringUtils.EMPTY;
        }
        List<String> productNameList = agentOrderDTO.getOrderItemList().stream().map(dto -> dto.getProductName()).collect(Collectors.toList());
        return StringUtils.join(productNameList, "； ");
    }

    private AIAnswerData buildSecondPaymentAnswer(WebParamDTO webParamDTO, FwslDealPreview fwslDealPreview) {
        AIAnswerData answerData = initAnswerData();
        AgentCreateOrderReqDTO agentCreateOrderReqDTO = buildAgentCreateOrderReqDTO(webParamDTO, fwslDealPreview);
        AgentCreateOrderResDTO agentCreateOrderResDTO = agentTradeAclService.createOrder(agentCreateOrderReqDTO);
        if (agentCreateOrderResDTO == null || agentCreateOrderResDTO.isFail()) {
            log.error("调用下单工具 agentTradeAclService.createOrder 失败");
            BufferUtils.writeMainTextBuffer(PilotBufferItemDO.builder().data("下单失败，请稍后重试～").build());
            return answerData;
        }

        AgentQueryOrderReqDTO agentQueryOrderReqDTO = buildAgentQueryOrderReqDTO(webParamDTO, agentCreateOrderResDTO);
        AgentQueryOrderResDTO agentQueryOrderResDTO = agentTradeAclService.queryOrder(agentQueryOrderReqDTO);
        AgentOrderDTO agentOrderDTO = null;
        if (agentQueryOrderResDTO != null && CollectionUtils.isNotEmpty(agentQueryOrderResDTO.getOrderList())) {
            agentOrderDTO = agentQueryOrderResDTO.getOrderList().get(0);
        }

        BeamDealOrderCreateData beamDealOrderCreateData = new BeamDealOrderCreateData();
        beamDealOrderCreateData.setOrderId(agentCreateOrderResDTO.getMainOrderId());
        beamDealOrderCreateData.setTradeNo(agentCreateOrderResDTO.getMtPayResult().getTradeNo());
        beamDealOrderCreateData.setPayToken(agentCreateOrderResDTO.getMtPayResult().getPayToken());

        PilotBufferItemDO pilotBufferItemDO = new PilotBufferItemDO();
        pilotBufferItemDO.setType(BufferItemTypeEnum.MAIN_TEXT.getType());
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("orderId", agentCreateOrderResDTO.getMainOrderId());
        if (agentOrderDTO != null && CollectionUtils.isNotEmpty(agentOrderDTO.getOrderItemList())) {
            AgentOrderItemDTO orderItemDTO = agentOrderDTO.getOrderItemList().get(0);
            jsonObject.put("productId", orderItemDTO.getProductId());
            jsonObject.put("productName", orderItemDTO.getProductName());
            jsonObject.put("count", orderItemDTO.getCount());
        }
        String data = StreamEventCardTypeEnum.buildCardContent(StreamEventCardTypeEnum.BEAM_DEAL_ORDER_CREATE, jsonObject.toJSONString());
        pilotBufferItemDO.setData("已经帮你下单成功啦\n" + data);
        pilotBufferItemDO.setExtra(JsonCodec.converseMap(JsonCodec.encodeWithUTF8(beamDealOrderCreateData), String.class, Object.class));
        BufferUtils.writeMainTextBuffer(pilotBufferItemDO);

        answerData.setAnswer("下单成功～");
        return answerData;
    }

    private AIAnswerData initAnswerData() {
        AIAnswerData answerData = new AIAnswerData();
        answerData.setAiAnswerType(AIAnswerTypeEnum.TEXT.getType());
        answerData.setAnswer("下单失败，请稍后重试～");
        return answerData;
    }

    private SendBeamMessageReq validateRequest(BeamCreateOrderParam param) {
        SendBeamMessageReq req = PluginContextUtil.getBeamRequestContext(param);
        if (req == null || req.getUser_info() == null || req.getUser_info().getUserid() == null) {
            BufferUtils.writeMainTextBuffer(PilotBufferItemDO.builder().data("下单失败，请稍后重试～").build());
            return null;
        }
        return req;
    }


    private AgentQueryOrderReqDTO buildAgentQueryOrderReqDTO(WebParamDTO webParamDTO, FwslDealOrder fwslDealOrder) {
        List<AgentQueryOrderReqItemDTO> orderItemList = fwslDealOrder.getOrderList().stream().map(FwslDealOrder.OrderInfo::getOrder_id)
                .filter(Objects::nonNull)
                .map(this::buildAgentQueryOrderReqItemDTO)
                .collect(Collectors.toList());
        AgentQueryOrderReqDTO req = new AgentQueryOrderReqDTO();
        req.setWebParam(webParamDTO);
        req.setOrderItemList(orderItemList);
        return req;
    }

    private AgentQueryOrderReqDTO buildAgentQueryOrderReqDTO(WebParamDTO webParamDTO, AgentCreateOrderResDTO resDTO) {
        AgentQueryOrderReqDTO req = new AgentQueryOrderReqDTO();
        AgentQueryOrderReqItemDTO reqItemDTO = new AgentQueryOrderReqItemDTO();
        reqItemDTO.setOrderId(resDTO.getMainOrderId());
        reqItemDTO.setOrderType(resDTO.getOrderType());
        req.setWebParam(webParamDTO);
        req.setOrderItemList(Lists.newArrayList(reqItemDTO));
        return req;
    }

    private AgentQueryOrderReqItemDTO buildAgentQueryOrderReqItemDTO(String orderId) {
        AgentQueryOrderReqItemDTO agentQueryOrderReqItemDTO = new AgentQueryOrderReqItemDTO();
        agentQueryOrderReqItemDTO.setOrderId(Long.parseLong(orderId));
        return agentQueryOrderReqItemDTO;
    }

    private AgentCreateOrderReqDTO buildAgentCreateOrderReqDTO(WebParamDTO webParamDTO, FwslDealPreview fwslDealPreview) {
        AgentCreateOrderReqDTO agentCreateOrderReqDTO = new AgentCreateOrderReqDTO();
        agentCreateOrderReqDTO.setWebParam(webParamDTO);
        agentCreateOrderReqDTO.setPreviewId(fwslDealPreview.getPreview_id());
        return agentCreateOrderReqDTO;
    }

    private FwslDealOrder getFwlsDealOrder(BeamCreateOrderParam beamCreateOrderParam, JSONObject businessData) {
        Long previewId = Optional.ofNullable(beamCreateOrderParam.getPreviewId()).orElse(0L);
        Long orderId = Optional.ofNullable(beamCreateOrderParam.getOrderId()).orElse(0L);

        // 若有提取出预览id，则不创建
        if (previewId > 0 && orderId <= 0) {
            return null;
        }

        // 若同时提取出预览id和订单id，则用上下文的
        if (previewId > 0 && orderId > 0) {
            return getFwlsDealOrderFromContext(businessData);
        }

        // 从模型提取参数中取订单id
        if (orderId > 0) {
            return createFwslDealOrder(orderId);
        }

        // 默认从上下文取订单
        return getFwlsDealOrderFromContext(businessData);
    }

    private FwslDealOrder createFwslDealOrder(Long orderId) {
        FwslDealOrder fwslDealOrder = new FwslDealOrder();
        FwslDealOrder.OrderInfo orderInfo = new FwslDealOrder.OrderInfo();
        orderInfo.setOrder_id(String.valueOf(orderId));
        fwslDealOrder.setOrderList(Lists.newArrayList(orderInfo));
        return fwslDealOrder;
    }

    // 从上下文取订单id
    private FwslDealOrder getFwlsDealOrderFromContext(JSONObject businessData) {
        if (businessData == null) {
            return null;
        }
        String fwslDealOrder = businessData.getString("fwls_deal_order");
        if (fwslDealOrder == null) {
            return null;
        }
        return JSON.parseObject(fwslDealOrder, FwslDealOrder.class);
    }

    private FwslDealPreview getFwlsDealPreview(BeamCreateOrderParam beamCreateOrderParam, JSONObject businessData) {
        Long previewId = Optional.ofNullable(beamCreateOrderParam.getPreviewId()).orElse(0L);
        Long orderId = Optional.ofNullable(beamCreateOrderParam.getOrderId()).orElse(0L);

        // 若有提取出订单id，则不创建
        if (orderId > 0 && previewId <= 0) {
            return null;
        }

        // 若同时提取出预览id和订单id，则用上下文的
        if (previewId > 0 && orderId > 0) {
            return getFwlsDealPreviewFromContext(businessData);
        }

        // 从模型提取参数中取订单id
        if (previewId > 0) {
            FwslDealPreview fwslDealPreview = new FwslDealPreview();
            fwslDealPreview.setPreview_id(String.valueOf(previewId));
            return fwslDealPreview;
        }

        // 默认从上下文取订单
        return getFwlsDealPreviewFromContext(businessData);


    }

    // 从上下文中提取预览id
    private FwslDealPreview getFwlsDealPreviewFromContext(JSONObject businessData) {
        if (businessData == null) {
            return null;
        }
        String fwslDealPreview = businessData.getString("fwls_deal_preview");
        if (fwslDealPreview == null) {
            return null;
        }
        return JSON.parseObject(fwslDealPreview, FwslDealPreview.class);
    }
}
