package com.sankuai.dzim.pilot.process.search.enums;

import com.google.common.collect.Lists;

import java.util.List;

public enum IndustryBackendCategoryEnum {
    KTV(Lists.newArrayList(51, 2159, 2161, 2467), "KTV商户后台类目"),
    BACKROOM(Lists.newArrayList(225), "密室商户后台类目"),
    ROLE_PLAY(Lists.newArrayList(2634), "剧本杀商户后台类目"),
    CHESS(Lists.newArrayList(2139), "棋牌商户后台类目"),
    BALL(Lists.newArrayList(585, 83, 288, 82, 285, 280, 287, 279, 84, 286), "球类运动商户后台类目"),
    VENUE(Lists.newArrayList(366), "场馆商户后台类目"),
    BAR(Lists.newArrayList(263, 2313, 2315, 2696, 2697, 2698, 2699, 2316, 2317), "酒吧商户后台类目"),
    PET(Lists.newArrayList(1861, 114, 453, 1863, 2384, 2385), "宠物商户后台类目"),

    ;

    private List<Integer> categoryIds;
    private String desc;

    IndustryBackendCategoryEnum(List<Integer> categoryIds, String desc) {
        this.categoryIds = categoryIds;
        this.desc = desc;
    }

    public List<Integer> getCategoryIds() {
        return categoryIds;
    }

    public String getDesc() {
        return desc;
    }
}