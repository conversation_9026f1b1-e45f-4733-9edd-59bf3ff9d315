package com.sankuai.dzim.pilot.process.localplugin;

import com.dianping.dzim.common.enums.ImUserType;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.dzim.pilot.acl.ProductAclService;
import com.sankuai.dzim.pilot.acl.ShopAclService;
import com.sankuai.dzim.pilot.acl.data.DealDetailData;
import com.sankuai.dzim.pilot.acl.data.req.ProductSearchReq;
import com.sankuai.dzim.pilot.chain.data.AIAnswerData;
import com.sankuai.dzim.pilot.domain.annotation.LocalPlugin;
import com.sankuai.dzim.pilot.process.localplugin.data.UserContext;
import com.sankuai.dzim.pilot.process.localplugin.param.ShopDetailQueryParam;
import com.sankuai.dzim.pilot.scene.data.AssistantSceneContext;
import com.sankuai.dzim.pilot.scene.task.data.EnvContext;
import com.sankuai.dzim.pilot.utils.PluginContextUtil;
import com.sankuai.dzim.pilot.utils.context.RequestContext;
import com.sankuai.dzim.pilot.utils.context.RequestContextConstants;
import com.sankuai.sinai.data.api.dto.MtPoiDTO;
import com.sankuai.sinai.data.api.util.MtPoiUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 门店详情查询
 *
 * <AUTHOR>
 * @since 2025/03/12 19:55
 */
public class ShopDetailQueryPlugin {
    @Autowired
    private PluginContextUtil pluginContextUtil;

    @Autowired
    private ShopAclService shopAclService;

    @Autowired
    private ProductAclService productAclService;

    @LocalPlugin(name = "ShopDetailQueryTool", description = "可以为你提供门店详细信息、门店团购信息等", returnDirect = false)
    public AIAnswerData queryShopDetail(ShopDetailQueryParam shopDetailQueryParam) {
        List<Long> mtShopIds = shopDetailQueryParam.getMtShopIds();
        if ( mtShopIds == null) {
            mtShopIds = Lists.newArrayList();
        }

        List<Map<String, String>> result = queryShopDetailInfo(mtShopIds, shopDetailQueryParam);
        AIAnswerData answerData = new AIAnswerData();
        answerData.setAnswer(JsonCodec.encodeWithUTF8(result));
        return answerData;
    }

    private List<Map<String, String>> queryShopDetailInfo(List<Long> mtShopIds, ShopDetailQueryParam shopDetailQueryParam) {
        if (CollectionUtils.isEmpty(mtShopIds)) {
            return Lists.newArrayList();
        }
        return mtShopIds.parallelStream()
                        .filter(Objects::nonNull)
                        .map(mtShopId -> {
                            Map<String, String> shopDetail = new HashMap<>();
                            paddingPoiInfo(mtShopId, shopDetail);
                            paddingDealInfo(mtShopId, shopDetail, shopDetailQueryParam);
                            paddingReviewInfo(mtShopId, shopDetail);
                            return shopDetail;
                        })
                        .collect(Collectors.toList());
    }

    private void paddingReviewInfo(Long mtShopId, Map<String, String> shopDetail) {
        // todo 后续查询POI页对应的几个评价即可
    }

    private void paddingDealInfo(Long mtShopId, Map<String, String> shopDetail, ShopDetailQueryParam shopDetailQueryParam) {
        // todo 后续查询更详细的Deal信息
        ProductSearchReq request = buildSearchProductRequest(mtShopId, shopDetailQueryParam);
        List<DealDetailData> dealDetails = productAclService.searchProduct(request);
        shopDetail.put("团购", JsonCodec.encodeWithUTF8(dealDetails));
    }

    private ProductSearchReq buildSearchProductRequest(Long mtShopId, ShopDetailQueryParam shopDetailQueryParam) {
        ProductSearchReq productSearchReq = new ProductSearchReq();
        EnvContext envContext = PluginContextUtil.getEnvContext(shopDetailQueryParam);
        UserContext userContext = PluginContextUtil.getUserContext(shopDetailQueryParam);
        productSearchReq.setPlanId("10002655");
        productSearchReq.setPlatform(isMt() ? 2 : 1);
        if (StringUtils.isNotBlank(userContext.getImUserId())) {
            productSearchReq.setUserId(Long.parseLong(userContext.getImUserId().substring(1)));
        }
        productSearchReq.setShopIds(Lists.newArrayList(mtShopId));
//        productSearchReq.setFilterTagIds(getProjectTagIds(bookType));
        productSearchReq.setDeviceId(envContext.getDeviceId());
        productSearchReq.setUnionId(envContext.getDeviceId());
        productSearchReq.setAppVersion(envContext.getAppVersion());
        productSearchReq.setLat(envContext.getLat());
        productSearchReq.setLng(envContext.getLng());
        productSearchReq.setCityId(envContext.getCityId());
        productSearchReq.setPageNo(1);
        productSearchReq.setPageSize(50);
        return productSearchReq;
    }

    private void paddingPoiInfo(Long mtShopId, Map<String, String> shopDetail) {
        //todo 后续可改为查商户主题
        MtPoiDTO mtPoiInfo = shopAclService.getMtShopInfo(mtShopId);
        if (mtPoiInfo == null || mtPoiInfo.getCloseStatus() != 0) {
            return;
        }
        shopDetail.put("mtShopId", String.valueOf(mtShopId));
        shopDetail.put("门店名称", mtPoiInfo.getName());
        shopDetail.put("地址", mtPoiInfo.getAddress());
        shopDetail.put("电话", MtPoiUtil.getMtPoiPhone(mtPoiInfo.getNormPhones()));
        shopDetail.put("星级", String.valueOf(mtPoiInfo.getDpStar()));
        shopDetail.put("人均价格", String.valueOf(mtPoiInfo.getMtAvgPrice()));
        if (isMt()) {
            shopDetail.put("评分", String.valueOf(mtPoiInfo.getMtAvgScore()));
        } else {
            shopDetail.put("评分", String.valueOf(mtPoiInfo.getDpFiveScore()));
        }
        shopDetail.put("营业时间", mtPoiInfo.getBusinessHours());
        shopDetail.put("分店名", mtPoiInfo.getBranchName());
        shopDetail.put("城市", mtPoiInfo.getMtCityLocationName());
    }

    private boolean isMt() {
        AssistantSceneContext assistantSceneContext = RequestContext.getAttribute(RequestContextConstants.ASSISTANT_CONTEXT);
        if (assistantSceneContext == null || StringUtils.isBlank(assistantSceneContext.getUserId())) {
            return true;
        }
        return assistantSceneContext.getUserId().startsWith(ImUserType.MT.getPrefix());
    }


}