package com.sankuai.dzim.pilot.acl;

import com.sankuai.dealuser.price.display.api.model.BatchPriceRequest;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;

import java.util.List;
import java.util.Map;

/**
 * Agent报价相关接口防腐层
 *
 * <AUTHOR>
 * @date 2025/5/8
 */
public interface PriceDisplayAclService {
    /**
     * 批量查询价格
     */
    Map<Long, List<PriceDisplayDTO>> batchQueryPriceByLongShopId(BatchPriceRequest request);
}
