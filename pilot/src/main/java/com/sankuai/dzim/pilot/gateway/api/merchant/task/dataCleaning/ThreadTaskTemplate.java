package com.sankuai.dzim.pilot.gateway.api.merchant.task.dataCleaning;

import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class ThreadTaskTemplate {

    public void multiThread(int dealThread, int pageSize, long start) {

        for (int i = 0; i < dealThread; i++) {
            start = processData(start, pageSize);
            if ((i * pageSize) % 100 == 0) {
                LogUtils.logRequestLog(log, TagContext.builder().action("success").build(), "ProcessDataService", "处理到了数据ID：" + start);
            }
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                LogUtils.logRequestLog(log, TagContext.builder().action("sleep").build(), "ProcessDataService", e.getMessage());
            }
        }
    }

    public abstract long processData(long start, int pageSize);
}
