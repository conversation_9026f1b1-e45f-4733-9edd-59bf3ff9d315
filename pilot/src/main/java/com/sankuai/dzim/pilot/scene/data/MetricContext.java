package com.sankuai.dzim.pilot.scene.data;

import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/8/26 14:16
 */
@Data
@Slf4j
public class MetricContext {

    /**
     * 开始时间戳
     */
    private long startTimestamp;

    /**
     * 小助手类型
     */
    private int assistantType;

    /**
     * key:
     */
    private Map<String, Long> eventName2EventTime;


    /**
     * 添加埋点信息
     *
     * @param eventName eventName
     */
    public void addEvent(String eventName) {
        try {
            eventName2EventTime.put(eventName, System.currentTimeMillis());
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("MetricContextAdd").build(),
                    WarnMessage.build("addEvent", "添加事件埋点失败", ""), eventName, e);
        }
    }

}


