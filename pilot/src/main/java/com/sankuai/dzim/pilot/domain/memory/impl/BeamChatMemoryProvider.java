package com.sankuai.dzim.pilot.domain.memory.impl;

import com.alibaba.fastjson.JSONObject;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.dzim.pilot.acl.data.fraiday.FridayMessage;
import com.sankuai.dzim.pilot.buffer.stream.build.ContentBuilder;
import com.sankuai.dzim.pilot.chain.data.AIServiceContext;
import com.sankuai.dzim.pilot.chain.enums.AIServiceExtraKeyEnum;
import com.sankuai.dzim.pilot.domain.annotation.Memory;
import com.sankuai.dzim.pilot.domain.memory.MemoryProvider;
import com.sankuai.dzim.pilot.domain.memory.data.QueryMemoryContext;
import com.sankuai.dzim.pilot.domain.message.AssistantMessage;
import com.sankuai.dzim.pilot.domain.message.Message;
import com.sankuai.dzim.pilot.domain.message.UserMessage;
import com.sankuai.dzim.pilot.gateway.api.assistant.data.BeamMessage;
import com.sankuai.dzim.pilot.gateway.api.assistant.data.SendBeamMessageReq;
import com.sankuai.dzim.pilot.utils.LionConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
@Memory(name = "BeamChatMemory", desc = "Beam聊天记忆查询")
public class BeamChatMemoryProvider implements MemoryProvider {

    @Resource
    private ContentBuilder contentBuilder;

    @Resource
    private LionConfigUtil lionConfigUtil;

    @Override
    public List<Message> getMemory(QueryMemoryContext context) {
        // 1.提取原始聊天记录
        AIServiceContext aiServiceContext = context.getAiServiceContext();
        SendBeamMessageReq sendBeamMessageReq = null;
        Object beamRequestContext = aiServiceContext.getExtraInfo().get(AIServiceExtraKeyEnum.BEAM_REQUEST_CONTEXT.getKey());
        if (beamRequestContext instanceof SendBeamMessageReq) {
            sendBeamMessageReq = (SendBeamMessageReq) beamRequestContext;
        } else if (beamRequestContext instanceof JSONObject) {
            sendBeamMessageReq = JsonCodec.decode(JsonCodec.encodeWithUTF8(beamRequestContext), SendBeamMessageReq.class);
        }

        if (sendBeamMessageReq == null || sendBeamMessageReq.getContext() == null
                || CollectionUtils.isEmpty(sendBeamMessageReq.getContext().getChat_history())) {
            return null;
        }

        // 2.过滤消息
        List<BeamMessage> beamMessages = sendBeamMessageReq.getContext().getChat_history();
        List<BeamMessage> filteredBeamMessages = filterBeamMessage(beamMessages);

        // 3.替换卡片内容
        List<Message> messages = Lists.newArrayList();
        for (BeamMessage beamMessage: CollectionUtils.emptyIfNull(filteredBeamMessages)) {
            String content = StringUtils.defaultString(beamMessage.getContent());
            String decodeContent = contentBuilder.decodeBeamCardMessage(content);
            if (StringUtils.isEmpty(decodeContent)) {
                continue;
            }
            Message message = convertMessage(decodeContent, beamMessage.getRole());
            messages.add(message);
        }

        // 3.取倒序的limit条
        return getReversedLimitedList(messages, context.getLimit());
    }

    private List<BeamMessage> filterBeamMessage(List<BeamMessage> beamMessages) {
        if (CollectionUtils.isEmpty(beamMessages)) {
            return Lists.newArrayList();
        }

        // 过滤非必要消息
        List<BeamMessage> filteredBeamMessages = beamMessages.stream()
                .filter(msg -> isKeepMessage(msg))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filteredBeamMessages)) {
            return Lists.newArrayList();
        }

        // 连续多条团搜消息，只保留最后一条
        return filterRedundantMtSearchMessage(filteredBeamMessages);
    }

    private List<BeamMessage> filterRedundantMtSearchMessage(List<BeamMessage> filteredBeamMessages) {
        List<BeamMessage> result = Lists.newArrayList();
        BeamMessage lastGroupSearchMessage = null;

        for (BeamMessage message : filteredBeamMessages) {
            if ("user".equals(message.getRole())) {
                // 如果是用户消息，先保存之前的团搜消息
                if (lastGroupSearchMessage != null) {
                    result.add(lastGroupSearchMessage);
                    lastGroupSearchMessage = null; // 重置
                }
                result.add(message); // 保存用户消息
            } else if (lionConfigUtil.getTuanSearchToolName().equals(message.getName())) {
                // 如果是团搜消息，记录最后一条
                lastGroupSearchMessage = message;
            } else {
                // 非团搜消息直接保存
                result.add(message);
            }
        }

        // 保存最后一条团搜消息（如果存在）
        if (lastGroupSearchMessage != null) {
            result.add(lastGroupSearchMessage);
        }

        return result;
    }

    private boolean isKeepMessage(BeamMessage beamMessage) {
        if (beamMessage.getRole().equals("user") || beamMessage.getRole().equals("assistant")) {
            return true;
        }

        return lionConfigUtil.getChatHistorySaveToolList().contains(beamMessage.getName());
    }

    private List<Message> getReversedLimitedList(List<Message> messages, int limit) {
        if (CollectionUtils.isEmpty(messages)) {
            return Collections.emptyList();
        }

        // 倒序列表
        Collections.reverse(messages);

        // 取指定数量的元素
        List<Message> limitedMessages = new ArrayList<>(messages.subList(0, Math.min(limit, messages.size())));

        // 恢复顺序
        Collections.reverse(messages); // 恢复原列表顺序
        Collections.reverse(limitedMessages); // 恢复取出的子列表顺序
        return limitedMessages;
    }


    private Message convertMessage(String message, String role) {
        if (role.equals("user")) {
            return UserMessage.build(message);
        }
        if (role.equals("assistant")) {
            return AssistantMessage.build(message);
        }
        return AssistantMessage.build(message);
    }

}
