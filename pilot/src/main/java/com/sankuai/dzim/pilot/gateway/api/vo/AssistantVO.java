package com.sankuai.dzim.pilot.gateway.api.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/07/31 15:43
 */
@Data
@MobileDo
public class AssistantVO implements Serializable {

    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    @MobileDo.MobileField(key = 0x6d6b)
    private String avatar;

    @MobileDo.MobileField(key = 0xa1e6)
    private Integer avatarHeight;

    @MobileDo.MobileField(key = 0x7cd5)
    private Integer avatarWidth;

    @MobileDo.MobileField(key = 0xd894)
    private String subTitle;

    @MobileDo.MobileField(key = 0x735a)
    private String theme;

    @MobileDo.MobileField(key = 0x91d7)
    private String extra;
}
