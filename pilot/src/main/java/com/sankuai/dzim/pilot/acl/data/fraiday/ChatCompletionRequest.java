package com.sankuai.dzim.pilot.acl.data.fraiday;


import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class ChatCompletionRequest {

    /**
     * 业务标识
     */
    private String businessKey;

    /**
     * 使用的模型
     */
    private String model;

    /**
     * 多轮对话的消息
     */
    private List<FridayMessage> messages;

    /**
     * 传输一个唯一标识的id
     */
    private String user;

    /**
     * 可以使用的工具列表
     */
    private List<Tool> tools;

    /**
     *  选择工具控制
     */
    private String tool_choice;

    /**
     * 采样温度，越高输出的值越随机
     */
    private Double temperature;

    /**
     * 只考虑具有top_p概率质量的结果
     */
    private Double top_p;

    /**
     * 输出格式
     */
    private Object response_format;

    /**
     * 是否是流式输出
     */
    private boolean stream;

    /**
     * 最大输出token
     */
    private Integer max_tokens;

    /**
     * Buffer扩展数据
     */
    private Map<String, Object> bufferExtra;
}
