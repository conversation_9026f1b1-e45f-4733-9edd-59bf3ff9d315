package com.sankuai.dzim.pilot.gateway.mq.consumer;

import com.dianping.lion.client.Lion;
import com.dianping.vc.mq.client.api.annotation.Consumer;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.message.common.utils.ImAccountTypeUtils;
import com.sankuai.dzim.message.enums.MessageSendDirectionEnum;
import com.sankuai.dzim.message.enums.MessageShieldEnum;
import com.sankuai.dzim.pilot.dal.cache.UserAIRecommendFreqRedis;
import com.sankuai.dzim.pilot.enums.AIAnswerSceneType;
import com.sankuai.dzim.pilot.gateway.mq.data.MessageSendData;
import com.sankuai.dzim.pilot.process.IntelligentCustomerProcessService;
import com.sankuai.dzim.pilot.process.data.req.IntelligentCustomerReq;
import com.sankuai.dzim.pilot.utils.LionConfigUtil;
import com.sankuai.dzim.pilot.utils.data.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 商家AI助手功能，当用户发送消息给商家后，根据用户发送的消息，使用大模型，推荐合适的回复消息给商家，供商家快捷回复用户
 * 消费者失败不需要重试，失败不影响业务
 */
@Slf4j
@Component
public class RecommendReplyConsumer {

    @Autowired
    private IntelligentCustomerProcessService intelligentCustomerProcessService;

    @Autowired
    private UserAIRecommendFreqRedis userAIRecommendFreqRedis;

    @Autowired
    private LionConfigUtil lionConfigUtil;

    @Consumer(nameSpace = "daozong", appKey = "com.sankuai.mim.pilot", group = "im.pilot.reply.recommend", topic = "im.inner.message.send")
    public void recvMessage(String mafkaMessage) {

        boolean newServiceSwitch = Lion.getBoolean("com.sankuai.mim.aiservice", "com.sankuai.mim.aiservice.new.service.switch", false);

        if (newServiceSwitch) {
            return;
        }

        try {
            MessageSendData messageSendData = JsonCodec.decode(mafkaMessage, MessageSendData.class);
            if (!isMsgNeedProcess(messageSendData)) {
                return;
            }

            IntelligentCustomerReq request = buildLoadRecommendAnswerReq(messageSendData);
            Response<Boolean> recommendResp = intelligentCustomerProcessService.loadLLMAnswerAndPush(request);
            LogUtils.logReturnInfo(log, TagContext.builder().shopId(messageSendData.getImShopId()).userId(messageSendData.getImUserId()).action("loadRecommendAnswerAndPush").build(),
                    WarnMessage.build("loadRecommendAnswerAndPush", "查询和推送推荐回复结果", ""), request, recommendResp);
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("RecommendReplyConsumer").build(),
                    new WarnMessage("RecommendReplyConsumer", "推荐回复消费者执行失败", null), mafkaMessage, null, e);
        }
    }

    private IntelligentCustomerReq buildLoadRecommendAnswerReq(MessageSendData messageSendData) {
        IntelligentCustomerReq intelligentCustomerReq = new IntelligentCustomerReq();
        intelligentCustomerReq.setMessageSendData(messageSendData);
        intelligentCustomerReq.setSceneCode(AIAnswerSceneType.MERCHANT_REPLY_RECOMMEND.getType());
        return intelligentCustomerReq;
    }

    private boolean isMsgNeedProcess(MessageSendData messageSendData) {
        if (messageSendData == null) {
            return false;
        }

        // 用户发送的消息，是发给到综商家，消息是商家可见的，才处理
        if (messageSendData.getMessageStatus() != MessageSendDirectionEnum.CLIENT_SEND.value) {
            return false;
        }
        if (!ImAccountTypeUtils.isShopId(messageSendData.getImShopId())) {
            return false;
        }
        if (messageSendData.getShieldType() != MessageShieldEnum.NOT_SHIELD.value) {
            return false;
        }
        if (!lionConfigUtil.isShopInMerchantRecmdShopWhiteList(ImAccountTypeUtils.getDpShopId(messageSendData.getImShopId()))) {
            return false;
        }

        // 用户维度天内消息推荐的频率控制，控制调用大模型成本，避免异常调用消耗成本
        long hasRecmdTimes = userAIRecommendFreqRedis.loadFrequency(messageSendData.getImUserId(), AIAnswerSceneType.MERCHANT_REPLY_RECOMMEND.getType());
        if (hasRecmdTimes >= lionConfigUtil.getMaxUserDayRecmdReplyTimes()) {
            return false;
        }
        return true;
    }
}
