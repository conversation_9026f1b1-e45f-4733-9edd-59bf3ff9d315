package com.sankuai.dzim.pilot.buffer.stream.build.beamcardext;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.frog.sdk.util.CollectionUtils;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.dzim.pilot.buffer.core.PilotBufferItemDO;
import com.sankuai.dzim.pilot.buffer.enums.StreamEventCardTypeEnum;
import com.sankuai.dzim.pilot.buffer.stream.build.beamcardext.data.BeamCardBuildContext;
import com.sankuai.dzim.pilot.buffer.stream.build.ext.data.BeamDealOrderCreateData;
import com.sankuai.dzim.pilot.buffer.stream.vo.BeamChoiceVO;
import com.sankuai.dzim.pilot.enums.BeamResourceTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/12
 */
@Component
@Slf4j
public class BeamCreateOrderCardBuildExt implements BeamCardBuildExt {

    @Override
    public boolean acceptByStreamCardType(String streamCardType) {
        return StreamEventCardTypeEnum.BEAM_DEAL_ORDER_CREATE.getType().equals(streamCardType);
    }

    @Override
    public boolean acceptByBeamResourceType(String resourceType) {
        return BeamResourceTypeEnum.FWLS_DEAL_CREATE_ORDER.getKey().equals(resourceType);
    }

    @Override
    public List<BeamChoiceVO.DeltaResource> buildResources(BeamCardBuildContext context) {
        List<BeamChoiceVO.DeltaResource> deltaResources = Lists.newArrayList();

        List<PilotBufferItemDO> tagItems = context.getTagItems();
        if (CollectionUtils.isEmpty(tagItems)) {
            return Collections.emptyList();
        }

        PilotBufferItemDO itemDO = tagItems.get(0);
        BeamDealOrderCreateData orderCardData = JsonCodec.decode(JsonCodec.encodeWithUTF8(itemDO.getExtra()), BeamDealOrderCreateData.class);
        BeamChoiceVO.DeltaResource deltaResource = buildDeltaResource(orderCardData);
        deltaResources.add(deltaResource);
        return deltaResources;
    }

    @Override
    public String cardDecode(String cardContent) {
        cardContent = StringEscapeUtils.unescapeJava(cardContent);
        JSONObject jsonObject = JSONObject.parseObject(cardContent);
        Long orderId = jsonObject.getLong("orderId");
        String productName = jsonObject.getString("productName");
        String productId = jsonObject.getString("productId");
        String count = jsonObject.getString("count");

        StringBuilder sb = new StringBuilder();
        sb.append("\n【订单卡片】\n");
        sb.append("订单Id(orderId)：").append(orderId).append("\n");
        if (StringUtils.isNotEmpty(productName)) {
            sb.append("商品名称：").append(productName).append("\n");
            sb.append("商品id(productId)：").append(productId).append("\n");
            sb.append("商品数量(quantity)：").append(count).append("\n");
            sb.append("商品类型(productType)：").append("团购").append("\n");
        }
        return sb.toString();
    }

    private BeamChoiceVO.DeltaResource buildDeltaResource(BeamDealOrderCreateData orderCardData) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("order_id", orderCardData.getOrderId().toString());
        jsonObject.put("trade_no", orderCardData.getTradeNo());
        jsonObject.put("pay_token", orderCardData.getPayToken());
        BeamChoiceVO.DeltaResource deltaResource = new BeamChoiceVO.DeltaResource();
        deltaResource.setResource_index(orderCardData.getOrderId().toString());
        deltaResource.setResource_type(BeamResourceTypeEnum.FWLS_DEAL_CREATE_ORDER.getKey());
        BeamChoiceVO.ResourceMetaData metaData = new BeamChoiceVO.ResourceMetaData();
        metaData.setBiz_data(JSON.toJSONString(jsonObject));
        deltaResource.setMetadata(metaData);
        return deltaResource;
    }
}
