package com.sankuai.dzim.pilot.process.aibook.data;

import com.sankuai.dzim.pilot.utils.DateUtils;
import lombok.Data;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Data
public class TimePeriodConfigData {

    private int id;

    /**
     * 选项文案
     */
    private String desc;

    /**
     * 完整文案
     */
    private String fullDesc;

    private TimePeriodSpecificConfigData beginTimeConfig;

    private TimePeriodSpecificConfigData endTimeConfig;

    private List<TimePeriodConfigData> subs;


    public Date getBeginTime(Date now, int minuteOffset) {
        if (beginTimeConfig == null) {
            beginTimeConfig = new TimePeriodSpecificConfigData();
        }
        return getSpecificTime(now, beginTimeConfig, minuteOffset);
    }

    public Date getBeginTimeV2(Date now, int minuteOffset) {
        if (beginTimeConfig == null || isCurrentTimeOverTargetBeginTime(now, minuteOffset)) {
            beginTimeConfig = new TimePeriodSpecificConfigData();
        }
        return getSpecificTime(now, beginTimeConfig, minuteOffset);
    }

    private boolean isCurrentTimeOverTargetBeginTime(Date now, int minuteOffset) {
        if (beginTimeConfig == null
                || beginTimeConfig.getTargetHour() < 0 || beginTimeConfig.getPlusDay() > 0) {
            return false;
        }

        // 计算以当前时间往后取整后的时间
        int finalTargetHour = getFinalTargetHour(now, -1, 0, minuteOffset);
        int finalTargetMinute = getFinalTargetMinute(now, -1, minuteOffset);

        if (finalTargetHour > beginTimeConfig.getTargetHour() ||
                (finalTargetHour == beginTimeConfig.getTargetHour() && finalTargetMinute > 0)) {
            return true;
        }
        return false;
    }

    public Date getEndTime(Date now, int minuteOffset) {
        if (endTimeConfig == null) {
            endTimeConfig = new TimePeriodSpecificConfigData();
        }
        return getSpecificTime(now, endTimeConfig, minuteOffset);
    }

    private Date getSpecificTime(Date now, TimePeriodSpecificConfigData timeSpecificConfigData, int minuteOffset) {
        int plusDay = timeSpecificConfigData.getPlusDay();
        int targetHour = timeSpecificConfigData.getTargetHour();
        int plusHour = timeSpecificConfigData.getPlusHour();

        int finalTargetHour = getFinalTargetHour(now, targetHour, plusHour, minuteOffset);
        int finalTargetMinute = getFinalTargetMinute(now, targetHour, minuteOffset);

        Date specificTime = new Date(now.getTime());
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(specificTime);
        calendar.add(Calendar.DAY_OF_MONTH, plusDay + (finalTargetHour / 24));
        calendar.set(Calendar.HOUR_OF_DAY, finalTargetHour % 24);
        calendar.set(Calendar.MINUTE, finalTargetMinute);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 若未指定目标小时数，则根据（当前分钟数+分钟偏移量）归整到0或30。
     * 0 -> 0
     * [1, 30] -> 30
     * [31, 59] -> 0
     */
    private int getFinalTargetMinute(Date now, int targetHour, int minuteOffset) {
        if (targetHour >= 0) {
            return 0;
        }

        int currentMinute = DateUtils.getMinuteOfHour(now);
        int targetMinute = (currentMinute + minuteOffset) % 60;
        if (targetMinute > 0 && targetMinute <= 30) {
            return 30;
        }
        return 0;
    }

    /**
     * 如果设置了targetHour，就使用targetHour。否则根据当前时间的分钟数额外添加plusHourFromMinute。
     * [0, 30]分钟，plusHourFromMinute = 0
     * [31, 90]分钟，plusHourFromMinute = 1
     * 以此类推
     */
    private int getFinalTargetHour(Date now, int targetHour, int plusHour, int minuteOffset) {
        if (targetHour >= 0) {
            return targetHour;
        }

        int currentHour = DateUtils.getHourOfDay(now);
        int currentMinute = DateUtils.getMinuteOfHour(now);
        int minuteWithOffset = currentMinute + minuteOffset;
        int plusHourFromMinute = (minuteWithOffset + 30 - 1) / 60;
        return currentHour + plusHour + plusHourFromMinute;
    }

}
