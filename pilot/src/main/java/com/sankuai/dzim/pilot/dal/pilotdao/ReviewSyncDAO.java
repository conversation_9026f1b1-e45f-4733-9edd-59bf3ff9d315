package com.sankuai.dzim.pilot.dal.pilotdao;

import com.sankuai.dzim.pilot.dal.entity.pilot.ReviewSyncEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ReviewSyncDAO {

    int insert(@Param("entity") ReviewSyncEntity entity);

    int delete(@Param("id") long id);

    ReviewSyncEntity findByReviewId(@Param("reviewId") long reviewId);

    List<ReviewSyncEntity> findByVectorId(@Param("vectorIds") List<Long> vectorIds);

    int countByShopId(@Param("mtShopId") long mtShopId);

    List<ReviewSyncEntity> batchFindByTechnicianIds(@Param("technicianIds") List<Long> technicianIds);

    List<Long> queryMtShopIDFilterByCount(@Param("threshold") int threshold);
}

