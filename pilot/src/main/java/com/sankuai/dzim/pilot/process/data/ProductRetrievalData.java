package com.sankuai.dzim.pilot.process.data;

import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import lombok.Data;

import java.util.List;

/**
 * 商品检索数据
 * <AUTHOR>
 */
@Data
public class ProductRetrievalData {

    /**
     * mt商品id
     */
   private long mtProductId;

   /**
    * dp商品id
    */
   private long dpProductId;

    /**
     * 商品类型
     * 1-团购；2-泛商品
     */
   private int productType;

   /**
    * 团购商品查询中心实体
    */
   private DealGroupDTO dealGroupDTO;

    /**
     * 套餐信息(sku)
     */
   private List<DealGroupDealDTO> deals;

    /**
     * 团单二级分类id
     */
    private Long categoryId;

    /**
     * 团单服务类型
     * 按摩
     */
    private String serviceType;

    /**
     * 团单服务类型
     * 12312
     */
    private Long serviceTypeId;

    /**
     * 标题
     */
    private String title;

    /**
     * 副标题
     */
    private List<String> subTitles;

    /**
     * 头图
     */
    private String headPic;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 标题描述
     */
    private String titleDesc;

    /**
     * 套餐售价
     */
    private String salePrice;

    /**
     * 套餐市场价
     */
    private String marketPrice;


    /**
     * 展示销量数值，salesNum = originSalesNum - cheatSalesNum
     */
    private Long salesNum;

    /**
     * 销量展示标签。如"已订30000+"
     */
    private String salesTag;

    /**
     * 商品关联的mt商户id
     */
    private long relatedMtShopId;

   /**
    * 关联门店
    */
   private ShopRetrievalData relatedShop;

    /**
     * 折扣标签
     */
   private List<String> discountTags;

    /**
     * 跳链
     */
   private String jumpUrl;
}
