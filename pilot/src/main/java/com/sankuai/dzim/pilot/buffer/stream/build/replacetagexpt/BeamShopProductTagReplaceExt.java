package com.sankuai.dzim.pilot.buffer.stream.build.replacetagexpt;

import com.alibaba.fastjson.JSONObject;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.dzim.pilot.buffer.enums.StreamEventCardTypeEnum;
import com.sankuai.dzim.pilot.buffer.stream.build.replacetagexpt.data.ReplaceContext;
import com.sankuai.dzim.pilot.enums.BeamResourceTypeEnum;
import com.sankuai.dzim.pilot.process.aireservebook.enums.POIIndustryType;
import com.sankuai.it.iam.common.base.gson.bridge.JSON;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class BeamShopProductTagReplaceExt implements ReplaceTagExt {

    @Override
    public boolean accept(String tag) {
        return StreamEventCardTypeEnum.BEAM_PRODUCT_CARD.getType().equals(tag);
    }

    /**
     * 把<BeamProductCard>content</BeamProductCard> 替换成 <card>content</card>
     *
     */
    @Override
    public String replace(ReplaceContext replaceContext) {
        try {
            if (StringUtils.isBlank(replaceContext.getTag().getValue())) {
                return null;
            }
            String type = BeamResourceTypeEnum.FWLS_PRE_PURCHASE_DEAL.getKey();
            // <BeamProductCard>${index}:${productId}:${recommendReason}</BeamProductCard>
            List<String> cardContentElements = Arrays
                    .asList(StringUtils.split(replaceContext.getTag().getValue(), ":"));
            if (CollectionUtils.isEmpty(cardContentElements) || cardContentElements.size() != 3) {
                return StringUtils.EMPTY;
            }

            String index = cardContentElements.get(0);
            String productId = cardContentElements.get(1);
            String recommendReason = cardContentElements.get(2);
            // 从扩展数据取数
            Map<String, Object> extra = replaceContext.getItemDOs().get(0).getExtra();
            JSONObject productJson = (JSONObject)extra.get(String.valueOf(productId));
            if (productJson == null) {
                return StringUtils.EMPTY;
            }
            // 1-团购，2-泛商品（预订）
            Integer productType = productJson.getInteger("productType");
            String productTypeDesc = productType == 1 ? "团购" : "预订";

            if (isReserveProduct(productType)) {
                return buildReserveCard(type, index, productId, productTypeDesc, productJson, recommendReason);
            }
            return buildDealCard(type, index, productId, productTypeDesc, productJson);
        } catch (Exception e) {
            log.error("BeamShopProductTagReplaceExt replace error, replaceContext = {}",
                    JsonCodec.encodeWithUTF8(replaceContext), e);
        }
        return StringUtils.EMPTY;
    }

    private String buildReserveCard(String type, String index, String productId, String productType,
            JSONObject productJson, String recommendReason) {
        // 密室、剧本杀行业，只推荐1个商品，索引数>1，直接返回null
        if (justDisplayOneProduct(productJson, index)) {
            return StringUtils.EMPTY;
        }

        String productName = productJson.getString("dealName");
        List<String> dealImages = (List<String>)productJson.get("dealImages");
        List<Long> relatedMtProductIds = convertToLongList(productJson.getString("relatedProductIdList"));

        // 构造beam卡片要素
        String fullProductInfo = "\n\n**" + index + ". " + productName + "**"
                + joinReserveRecommendReason(productJson, recommendReason);
        String firstTag = "<card " + "type=" + type + " index=" + "FWLS-" + index + " hidden="
                + (needDisplayHeadPic(productJson, dealImages) ? 0 : 1) + ">";
        String endTag = "</card>";

        JSONObject beamCardContent = new JSONObject();
        beamCardContent.put("productId", productId);
        beamCardContent.put("productType", productType);
        beamCardContent.put("relatedProductIdList", relatedMtProductIds);
        return fullProductInfo + firstTag + beamCardContent.toJSONString() + endTag;
    }

    private boolean isReserveProduct(Integer productType) {
        return productType == 2;
    }

    private List<Long> convertToLongList(String jsonString) {
        if (StringUtils.isBlank(jsonString)) {
            return Collections.emptyList();
        }
        return JSON.parseArray(jsonString, Long.class);
    }

    private List<String> convertToList(String jsonString) {
        if (StringUtils.isBlank(jsonString)) {
            return Collections.emptyList();
        }
        return JSON.parseArray(jsonString, String.class);
    }

    private String joinReserveRecommendReason(JSONObject productJson, String recommendReason) {
        if (productJson == null) {
            return StringUtils.EMPTY;
        }

        String salePrice = productJson.getString("finalPrice");
        List<String> subTitles = convertToList(productJson.getString("subTitle"));

        StringBuilder recommendReasonBuilder = new StringBuilder();

        if (CollectionUtils.isNotEmpty(subTitles)) {
            recommendReasonBuilder.append("\n\n副标题：").append(String.join("|", subTitles));
        }
        if (StringUtils.isNotEmpty(salePrice)) {
            recommendReasonBuilder.append("\n\n价格：¥").append(salePrice);
        }
        if (needDisplayRecommendReason(productJson, recommendReason)) {
            recommendReasonBuilder.append("\n\n推荐理由：").append(recommendReason);
        }
        return recommendReasonBuilder.toString();
    }

    private boolean needDisplayRecommendReason(JSONObject productJson, String recommendReason) {
        boolean rightCategory = isBackRoomOrRolePlay(productJson);
        boolean hasRecommendReason = StringUtils.isNotEmpty(recommendReason);
        return rightCategory && hasRecommendReason;
    }

    private boolean needDisplayHeadPic(JSONObject productJson, List<String> headPics) {
        boolean rightCategory = isBackRoomOrRolePlay(productJson);
        boolean hasHeadPic = CollectionUtils.isNotEmpty(headPics);
        return rightCategory && hasHeadPic;
    }

    private boolean justDisplayOneProduct(JSONObject productJson, String index) {
        boolean rightCategory = isBackRoomOrRolePlay(productJson);
        boolean oneMore = NumberUtils.toInt(index) > 1;
        return rightCategory && oneMore;
    }

    private boolean isBackRoomOrRolePlay(JSONObject productJson) {
        Long categoryId = productJson.getLong("productCategoryId");
        if (categoryId == null) {
            return false;
        }
        return categoryId.intValue() == POIIndustryType.BACKROOM.getSecondBackCategoryId()
                || categoryId.intValue() == POIIndustryType.ROLE_PLAY.getSecondBackCategoryId();
    }

    private String buildDealCard(String type, String index, String productId, String productType,
            JSONObject productJson) {
        String productName = productJson.getString("dealName");

        // 构造beam卡片要素
        String fullProductInfo = "\n\n**" + index + ". " + productName + "**" + joinRecommendReason(productJson);
        String firstTag = "<card " + "type=" + type + " index=" + "FWLS-" + index + " hidden=1" + ">";
        String endTag = "</card>";

        JSONObject beamCardContent = new JSONObject();
        beamCardContent.put("productId", productId);
        beamCardContent.put("productType", productType);
        return fullProductInfo + firstTag + beamCardContent.toJSONString() + endTag;
    }

    private String joinRecommendReason(JSONObject productJson) {
        if (productJson == null) {
            return StringUtils.EMPTY;
        }

        String salePrice = productJson.getString("finalPrice");
        List<String> subTitles = convertToList(productJson.getString("subTitle"));

        StringBuilder recommendReasonBuilder = new StringBuilder();

        if (StringUtils.isNotEmpty(salePrice)) {
            recommendReasonBuilder.append("\n\n价格：¥").append(salePrice);
        }
        if (CollectionUtils.isNotEmpty(subTitles)) {
            recommendReasonBuilder.append("\n\n内容：").append(String.join("|", subTitles));
        }
        return recommendReasonBuilder.toString();
    }
}
