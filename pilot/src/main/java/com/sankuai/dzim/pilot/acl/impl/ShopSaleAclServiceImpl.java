package com.sankuai.dzim.pilot.acl.impl;

import com.dianping.joy.order.dto.soldcount.BatchQuerySoldCountBySpuIdsReqDTO;
import com.dianping.joy.order.dto.soldcount.QuerySoldCountByShopReqDTO;
import com.dianping.joy.order.dto.soldcount.QuerySoldCountByShopRespDTO;
import com.dianping.joy.order.service.SoldCountService;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Maps;
import com.meituan.inf.xmdlog.XMDLogFormat;
import com.sankuai.athena.inf.AthenaInf;
import com.sankuai.athena.inf.rpc.RpcClient;
import com.sankuai.dzim.pilot.acl.ShopSaleAclService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Service
public class ShopSaleAclServiceImpl implements ShopSaleAclService {

    @RpcClient(url = "http://service.dianping.com/joy-order-service/SoldCountService_1.0.0")
    private SoldCountService soldCountService;

    @Override
    public Integer loadSoldCountByVenueShop(long dpShopId) {
        if (dpShopId <= 0) {
            return 0;
        }
        QuerySoldCountByShopReqDTO reqDTO = new QuerySoldCountByShopReqDTO();
        reqDTO.setLongShopID(dpShopId);
        QuerySoldCountByShopRespDTO respDTO = soldCountService.querySoldCountByShop(reqDTO);
        return respDTO == null || respDTO.getShopSoldCount() == null ? 0 : respDTO.getShopSoldCount();

    }

    @Override
    public CompletableFuture<Map<Integer, Integer>> multiGetBookingProductSales(BatchQuerySoldCountBySpuIdsReqDTO reqDTO) {
        return AthenaInf.getRpcCompletableFuture(soldCountService.batchQuerySoldCountBySpuIds(reqDTO)).exceptionally(e -> {
            log.error(XMDLogFormat.build()
                    .putTag("scene", "themeAtomService")
                    .putTag("method", "multiGetBookingProductSales")
                    .message(String.format("multiGetBookingProductSales error, request : %s", JsonCodec.encode(reqDTO))));
            return null;
        }).thenApply(result -> result == null ? Maps.newHashMap() : result.getSpuToSoldCountMap());
    }


}
