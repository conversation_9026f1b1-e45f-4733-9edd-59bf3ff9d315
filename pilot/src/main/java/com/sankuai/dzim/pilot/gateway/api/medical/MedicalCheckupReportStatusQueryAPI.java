package com.sankuai.dzim.pilot.gateway.api.medical;

import com.dianping.gateway.enums.APIModeEnum;
import com.dianping.vc.web.api.API;
import com.dianping.vc.web.api.URL;
import com.dianping.vc.web.api.VCAssert;
import com.dianping.vc.web.biz.client.api.MTUserIdAware;
import com.dianping.vc.web.biz.client.api.UserIdAware;
import com.google.common.collect.Maps;
import com.sankuai.dzim.pilot.gateway.api.vo.medical.MedicalCheckupReportStatusVO;
import com.sankuai.dzim.pilot.process.MedicalCheckupProcessService;
import com.sankuai.dzim.pilot.utils.CatUtils;
import com.sankuai.dzim.pilot.utils.UserIdUtils;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 查询体检报告解读状态
 * @author: zhouyibing
 * @date: 2024/9/6
 */
@Setter
@Slf4j
@Service
@URL(url = "/dzim/pilot/medical/checkup/report/status/query", mode = APIModeEnum.CLIENT, methods = "GET")
public class MedicalCheckupReportStatusQueryAPI implements API, UserIdAware, MTUserIdAware {

    @Setter
    private long mTUserId;

    @Setter
    private long userId;

    @Setter
    private int platform;

    @Setter
    private long recordid;

    @Autowired
    private MedicalCheckupProcessService medicalCheckupProcessService;

    @Override
    public Object execute() {
        String imUserId = UserIdUtils.getImUserId(userId, mTUserId, platform);
        //请求埋点
        CatUtils.logMetricReq("MedicalCheckupReportStatusQuery", null);
        VCAssert.isTrue(StringUtils.isNotBlank(imUserId), API.UNLOGIN_CODE, "未登陆");
        MedicalCheckupReportStatusVO medicalCheckupReportStatusVO = medicalCheckupProcessService.queryCheckupReportStatus(imUserId, recordid);
        //成功埋点
        CatUtils.logMetricSucc("MedicalCheckupReportStatusQuery", null);
        return medicalCheckupReportStatusVO;
    }
}
