package com.sankuai.dzim.pilot.buffer.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: zhouyibing
 * @date: 2024/8/1
 */
@Getter
@AllArgsConstructor
public enum StreamEventTypeEnum {

    OPEN("open", "连接打开"),

    CLOSE("close", "连接关闭"),

    ERROR("error", "异常"),

    MESSAGE("message", "消息体"),

    ;

    private final String type;
    private final String desc;

    public static StreamEventTypeEnum getByType(String type) {
        for (StreamEventTypeEnum typeEnum : values()) {
            if (typeEnum.getType().equals(type)) {
                return typeEnum;
            }
        }
        return null;
    }

}
