package com.sankuai.dzim.pilot.domain.retrieval.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.meituan.data.ups.thrift.LabelData;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.message.common.utils.ImAccountTypeUtils;
import com.sankuai.dzim.pilot.acl.UserAclService;

import com.sankuai.dzim.pilot.domain.annotation.Retrieval;
import com.sankuai.dzim.pilot.domain.retrieval.RetrievalAugmentor;
import com.sankuai.dzim.pilot.domain.retrieval.data.*;
import com.sankuai.dzim.pilot.process.AssistantRetrievalProcessService;
import com.sankuai.dzim.pilot.process.data.ChannelSearchSourceEnum;
import com.sankuai.dzim.pilot.process.data.PersonaLabelEnum;
import com.sankuai.dzim.pilot.process.data.ShopRetrievalData;
import com.sankuai.dzim.pilot.scene.data.AssistantExtraConstant;
import com.sankuai.dzim.pilot.scene.data.AssistantSceneContext;
import com.sankuai.dzim.pilot.utils.LionConfigUtil;
import com.sankuai.dzim.pilot.utils.context.RequestContext;
import com.sankuai.dzim.pilot.utils.context.RequestContextConstants;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.net.URLDecoder;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 频道页搜索增强器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@Retrieval(name = "ChannelPageSearchRetrieval", desc = "频道页搜索增强器")
public class ChannelPageSearchAugmentor implements RetrievalAugmentor {
    @Autowired
    private LionConfigUtil lionConfigUtil;
    @Autowired
    private UserAclService userAclService;
    @Autowired
    private AssistantRetrievalProcessService assistantRetrievalProcessService;

    public static final int QUERY_SHOP_MAX_CNT = 10;


    @Override
    public String retrieval(RetrievalContext context) {

        return null;
    }

    @Override
    public RetrievalResult retrieveKnowledge(RetrievalContext context) {
        RetrievalResult retrievalResult = new RetrievalResult();
        List<Knowledge> knowledgeList = Lists.newArrayList();
        AssistantSceneContext assistantSceneContext = RequestContext.getAttribute(RequestContextConstants.ASSISTANT_CONTEXT);
        Assert.isTrue(assistantSceneContext != null, "用户商户上下文不能为空");

        String bizParamsStr = MapUtils.getString(assistantSceneContext.getExtra(), AssistantExtraConstant.MESSAGE_BIZ_PARAMS);

        Knowledge knowledge = getKnowledge(assistantSceneContext, bizParamsStr);

        knowledgeList.add(knowledge);
        retrievalResult.setKnowledges(knowledgeList);
        return retrievalResult;
    }

    private Knowledge convertStr2Knowledge(String knowledgeStr) {
        Knowledge knowledge = new Knowledge();
        knowledge.setKnowledge(knowledgeStr);
        return knowledge;
    }

    private Knowledge getKnowledge(AssistantSceneContext assistantSceneContext, String bizParamsStr) {
        //https://km.sankuai.com/collabpage/2704271231
        Assert.isTrue(StringUtils.isNotBlank(bizParamsStr), "用户商户信息不能为空");

        //1，解析json
        JSONObject bizParams = JSONObject.parseObject(bizParamsStr);
        Double lng = Optional.ofNullable(bizParams).map(obj -> obj.getJSONObject("pos")).map(obj -> obj.getDouble("lng")).orElse(0D);//经度
        Double lat = Optional.ofNullable(bizParams).map(obj -> obj.getJSONObject("pos")).map(obj -> obj.getDouble("lat")).orElse(0D);//纬度


        //2，用户行为
        //获取用户画像标签信息
        long mtUserId = ImAccountTypeUtils.getAccountId(assistantSceneContext.getUserId());
        Map<Integer, String> portrait = getPersonaLabel(mtUserId);

        //获取用户最近最近点击商家数据
        List<ShopRetrievalData> shopRetrievalDataOp = assistantRetrievalProcessService.queryUserOperationShopRetrievalData(assistantSceneContext.getUserId(), QUERY_SHOP_MAX_CNT, lng, lat);

        //3，根据前端传的 listData 获取商户信息
        List<JSONObject> listingDataJsonObjs = Optional.ofNullable(bizParams).
                map(obj -> obj.getJSONArray("listingData")).
                map(obj -> obj.toJavaList(JSONObject.class)).orElse(Lists.newArrayList());
        //过滤非商户
        Map<Long, JSONObject> listingDataFeMap = new HashMap<>();
        for (JSONObject listingDataObj : listingDataJsonObjs) {
            if (listingDataObj == null || listingDataObj.getJSONObject("shopCardInfo") == null) {
                continue;
            }
            String poiIdStr = Optional.ofNullable(listingDataObj).map(obj -> obj.getJSONObject("shopCardInfo")).
                    map(obj -> obj.getJSONObject("baseInfoVO")).
                    map(obj -> obj.getString("poiId")).orElse("");
            if (StringUtils.isBlank(poiIdStr)) {
                continue;
            }
            listingDataFeMap.put(Long.parseLong(poiIdStr), listingDataObj);
        }

        Map<Long, ShopRetrievalData> shopRetrievalDataFe = new HashMap<>();
        if (MapUtils.isNotEmpty(listingDataFeMap)) {
            shopRetrievalDataFe = assistantRetrievalProcessService.batchQueryShopRetrievalData(new ArrayList<>(listingDataFeMap.keySet()), lng, lat);
        }

        String knowledgeStr = mergeKnowledge(portrait, shopRetrievalDataOp, shopRetrievalDataFe, listingDataFeMap, bizParams);
        return convertStr2Knowledge(knowledgeStr);
    }

    private StringBuilder joinUserTag(StringBuilder sb, Map<Integer, String> portrait) {
        //1，拼接用户标签
        sb.append("你可以参考的用户基本信息如下:").append("\n");
        for (PersonaLabelEnum personaLabelEnum : PersonaLabelEnum.values()) {
            if (!portrait.containsKey(personaLabelEnum.getType()) || StringUtils.isBlank(portrait.get(personaLabelEnum.getType()))) {
                continue;
            }
            sb.append(personaLabelEnum.getName()).append("为").append(portrait.get(personaLabelEnum.getType())).append("\n");
        }

        return sb;
    }

    private StringBuilder joinUserAction(StringBuilder sb, JSONObject bizParams, List<ShopRetrievalData> shopRetrievalDataOp) {
        //2，拼接用户行为
        String currentQuery = "未知";
        try {
            currentQuery = URLDecoder.decode(Optional.ofNullable(bizParams).map(obj -> obj.getString("currentQuery")).orElse("未知"), "UTF-8");
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("decodeUrlParamQuery").build(),
                    new WarnMessage("URLDecoder", "解析url参数失败", null), bizParams, null, e);
        }
        sb.append("用户当前时间进行搜索的搜索词为:").append(currentQuery)
                .append("，历史搜索词为:").append(Optional.ofNullable(bizParams).map(obj -> obj.getString("historyQuery")).orElse("未知"))
                .append("，当前所处频道为").append(convertSourcePageEnum2Value(Optional.ofNullable(bizParams).map(obj -> obj.getString("sourcePage")).orElse("未知")))
                .append("\n");
        if (!CollectionUtils.isEmpty(shopRetrievalDataOp)) {
            sb.append("用户历史点击或交易的商户为:\n");
            sb.append(transShopRetrievalDatas2Knowledge(shopRetrievalDataOp));
        }

        return sb;
    }

    public String transShopRetrievalDatas2Knowledge(List<ShopRetrievalData> shopRetrievalDataOp) {
        StringBuilder sb = new StringBuilder();
        for (ShopRetrievalData shopRetrievalData : shopRetrievalDataOp) {
            if (shopRetrievalData == null) {
                continue;
            }
            sb.append("商户名称为:").append(shopRetrievalData.getShopName())
                    .append(",美团星级为:").append(formatMtScore(shopRetrievalData.getMtAvgScore()))
                    .append(",美团人均消费(单位元)价格为:").append(shopRetrievalData.getMtAvgPrice())
                    .append(",美团一级后台类目为:").append(shopRetrievalData.getMtCateName0())
                    .append(",美团二级后台类目为:").append(shopRetrievalData.getMtCateName1())
                    .append(",美团三级后台类目为:").append(shopRetrievalData.getMtCateName2())
                    .append(",美团后台城市名称为:").append(shopRetrievalData.getMtCityName())
                    .append(",美团后台行政区名称为:").append(shopRetrievalData.getMtDistrictName())
                    .append(",美团后台商圈名称为:").append(shopRetrievalData.getMtRegionName())
                    .append(",商户地址为:").append(shopRetrievalData.getShopAddress())
                    .append(",用户操作为:").append(shopRetrievalData.getUserOperation())
                    .append(",是否为品牌名称为:").append(StringUtils.isNotBlank(shopRetrievalData.getBrandName()) ? "是" : "否")
                    .append(",用户操作时间为:").append(shopRetrievalData.getUserOperationTime());
            if (shopRetrievalData.getUserDistance() != -1) {
                sb.append(",用户离商户的距离(单位米)为:").append(shopRetrievalData.getUserDistance());
            }

            if (StringUtils.isNotBlank(shopRetrievalData.getSubwayStation())) {
                sb.append(",商户附近地铁站为:").append(shopRetrievalData.getSubwayStation());
            }

            if (StringUtils.isNotBlank(shopRetrievalData.getBrandName())) {
                sb.append(",品牌名称为:").append(shopRetrievalData.getBrandName());
            }
            sb.append("\n");
        }
        return sb.toString();
    }

    private StringBuilder joinShopCandidate(StringBuilder sb, Map<Long, ShopRetrievalData> shopRetrievalDataFe, Map<Long, JSONObject> listingDataFeMap) {
        sb.append("以下是你可以选择推荐的商户信息:\n");
        for (Map.Entry<Long, JSONObject> entry : listingDataFeMap.entrySet()) {
            Long shopId = entry.getKey();
            JSONObject shopInfoObj = entry.getValue();

            if (shopInfoObj == null || !shopRetrievalDataFe.containsKey(shopId)) {
                continue;
            }
            ShopRetrievalData shopRetrieval = shopRetrievalDataFe.get(shopId);

            sb.append("商户名称为").append(shopRetrieval.getShopName())
                    .append(",美团评论数为:").append(Optional.ofNullable(shopInfoObj).
                            map(obj -> obj.getJSONObject("shopCardInfo")).
                            map(obj -> obj.getJSONObject("baseInfoVO")).
                            map(obj -> obj.getString("poiConsume")).orElse("未知"))
                    .append(",商户品类为:").append(Optional.ofNullable(shopInfoObj).
                            map(obj -> obj.getJSONObject("shopCardInfo")).
                            map(obj -> obj.getJSONObject("baseInfoVO")).
                            map(obj -> obj.getString("categoryName")).orElse("未知"))
                    .append(",商户标签为:").append(decodeShopTagFromListingData(shopInfoObj))
                    .append(",商户评价为:").append(decodeShopRecommendFromListingData(shopInfoObj))
                    .append(",商户下挂团单信息为:").append(decodeHangInfoFromListingData(shopInfoObj))
                    .append(",美团星级为:").append(formatMtScore(shopRetrieval.getMtAvgScore()))
                    .append(",美团人均消费(单位元)价格为:").append(shopRetrieval.getMtAvgPrice())
                    .append(",美团后台商圈名称为:").append(shopRetrieval.getMtRegionName())
                    .append(",美团后台城市名称为:").append(shopRetrieval.getMtCityName())
                    .append(",美团后台行政区名称为:").append(shopRetrieval.getMtDistrictName())
                    .append(",是否为品牌名称为:").append(StringUtils.isNotBlank(shopRetrieval.getBrandName()) ? "是" : "否")
                    .append(",商户地址为:").append(shopRetrieval.getShopAddress())
                    .append(",美团一级后台类目为:").append(shopRetrieval.getMtCateName0())
                    .append(",美团二级后台类目为:").append(shopRetrieval.getMtCateName1())
                    .append(",美团三级后台类目为:").append(shopRetrieval.getMtCateName2());
            if (shopRetrieval.getUserDistance() != -1) {
                sb.append(",用户离商户的距离(单位米)为").append(shopRetrieval.getUserDistance());
            }
            if (StringUtils.isNotBlank(shopRetrieval.getSubwayStation())) {
                sb.append(",商户附近地铁站为").append(shopRetrieval.getSubwayStation());
            }
            if (StringUtils.isNotBlank(shopRetrieval.getBrandName())) {
                sb.append(",品牌名称为:").append(shopRetrieval.getBrandName());
            }

            sb.append("\t商户的门店 ID为").append(shopId).append("\t跳转链接为:")
                    .append(Optional.ofNullable(shopInfoObj).
                            map(obj -> obj.getJSONObject("shopCardInfo")).
                            map(obj -> obj.getJSONObject("baseInfoVO")).
                            map(obj -> obj.getString("shopUrl")).orElse("未知"))
                    .append("\n");
        }

        return sb;
    }

    private String mergeKnowledge(Map<Integer, String> portrait,                     //用户画像
                                  List<ShopRetrievalData> shopRetrievalDataOp,       //用户点击操作的商户
                                  Map<Long, ShopRetrievalData> shopRetrievalDataFe,  //前端传的商户后续查接口信息
                                  Map<Long, JSONObject> listingDataFeMap,            //前端传的商户原始信息
                                  JSONObject bizParams) {
        StringBuilder sb = new StringBuilder();
        //1，拼接用户标签
        sb = joinUserTag(sb, portrait);

        //2，拼接用户行为
        sb = joinUserAction(sb, bizParams, shopRetrievalDataOp);

        //3，拼接候选店铺信息
        sb = joinShopCandidate(sb, shopRetrievalDataFe, listingDataFeMap);
        return sb.toString();
    }

    /**
     * 从 shopInfoObj解析出下挂团单信息
     *
     * @param shopInfoObj
     * @return
     */
    private String decodeHangInfoFromListingData(JSONObject shopInfoObj) {
        StringBuilder sb = new StringBuilder();
        JSONArray hangList = Optional.ofNullable(shopInfoObj).
                map(obj -> obj.getJSONObject("shopCardInfo")).
                map(obj -> obj.getJSONObject("hangInfoVO")).
                map(obj -> obj.getJSONArray("hangList")).orElse(new JSONArray());
        for (int i = 0; i < hangList.size(); i++) {
            JSONObject hang = hangList.getJSONObject(i);
            if (hang == null || hang.isEmpty()) {
                continue;
            }
            sb.append("下挂团单名称为").append(hang.getString("title"))
                    .append(",折扣信息为").append(hang.getString("discount"))
                    .append(",价格为").append(hang.getString("price"))
                    .append(",销量为").append(hang.getString(" sale")).append("\n");
        }

        if (sb.length() == 0) {
            return "暂无";
        }
        return sb.toString();
    }

    /**
     * 从 ShopInfoObj解析出标签
     *
     * @param shopInfoObj
     * @return
     */
    private String decodeShopTagFromListingData(JSONObject shopInfoObj) {
        StringBuilder sb = new StringBuilder();
        JSONArray tagList = Optional.ofNullable(shopInfoObj).
                map(obj -> obj.getJSONObject("shopCardInfo")).
                map(obj -> obj.getJSONObject("tagInfoVO")).
                map(obj -> obj.getJSONArray("tagList")).orElse(new JSONArray());
        for (int i = 0; i < tagList.size(); i++) {
            JSONObject tag = tagList.getJSONObject(i);
            if (tag == null || tag.getInteger("unityType") != 26) {
                continue;
            }
            JSONArray textArr = tag.getJSONArray("text");
            if (textArr == null) {
                continue;
            }
            for (int j = 0; j < textArr.size(); j++) {
                if (textArr.getJSONObject(j) == null || StringUtils.isBlank(textArr.getJSONObject(j).getString("text"))) {
                    continue;
                }
                sb.append(textArr.getJSONObject(j).getString("text")).append("、");
            }
        }

        //去掉最后的顿号
        if (sb.length() > 0) {
            sb.deleteCharAt(sb.length() - 1);
        } else {
            sb.append("暂无");
        }

        return sb.toString();
    }

    /**
     * 从 listingDataObj解析出用户评价
     *
     * @param listingData
     * @return
     */
    private String decodeShopRecommendFromListingData(JSONObject listingData) {
        StringBuilder sb = new StringBuilder();
        JSONArray recommendList = Optional.ofNullable(listingData).
                map(obj -> obj.getJSONObject("shopCardInfo")).
                map(obj -> obj.getJSONObject("recommendInfoVO")).
                map(obj -> obj.getJSONArray("recommendList")).orElse(new JSONArray());
        for (int i = 0; i < recommendList.size(); i++) {
            if (recommendList.getJSONObject(i) == null || StringUtils.isBlank(recommendList.getJSONObject(i).getString("text"))) {
                continue;
            }
            sb.append(recommendList.getJSONObject(i).getString("text")).append("、");
        }

        //去掉最后的顿号
        if (sb.length() > 0) {
            sb.deleteCharAt(sb.length() - 1);
        } else {
            sb.append("暂无");
        }

        return sb.toString();
    }

    private String convertSourcePageEnum2Value(String sourcePageEnum) {
        try {
            for (ChannelSearchSourceEnum channelSearchSourceEnum : ChannelSearchSourceEnum.values()) {
                if (sourcePageEnum.equals(channelSearchSourceEnum.getSource())) {
                    return channelSearchSourceEnum.getName();
                }
            }
        } catch (NumberFormatException e) {
        }
        return "未知";
    }

    /**
     * 查询用户标签
     *
     * @param mtUserId
     * @return
     */
    private Map<Integer, String> getPersonaLabel(long mtUserId) {
        List<PersonaLabelConfig> personaLabelConfigs = lionConfigUtil.getAssistantChannelPageUserLabel();
        List<Integer> labelsIds = personaLabelConfigs.stream().map(PersonaLabelConfig::getLabelId).collect(Collectors.toList());
        List<LabelData> labelDatas = userAclService.getUserProfile(mtUserId, labelsIds);
        Map<Integer, LabelData> labelDataMap = labelDatas.stream().collect(Collectors.toMap(LabelData::getId, Function.identity(), (o1, o2) -> o1));

        Map<Integer, String> portrait = new HashMap<>();
        for (PersonaLabelConfig config : personaLabelConfigs) {
            int labelId = config.getLabelId();
            if (!labelDataMap.containsKey(labelId)) {
                continue;
            }

            //标签实际存储数据
            String actualTagVal = labelDataMap.get(labelId).getValue();

            //有些标签存储实际数据不是 0,1这种映射，直接用，如济南这种，所以lion配置不配 map，默认值使用实际存储值
            portrait.put(config.getPersonaLabelType(), config.getValue(actualTagVal, String.class, "未知"));
        }
        return portrait;
    }

    /**
     * 格式化美团星级 46 -> 4.6
     * @param mtScore int
     * @return float
     */
    private double formatMtScore(int mtScore) {
        BigDecimal bigDecimalNumber = BigDecimal.valueOf(mtScore);
        BigDecimal result = bigDecimalNumber.divide(BigDecimal.valueOf(10), 1, RoundingMode.HALF_UP);
        return result.doubleValue();
    }
}
