package com.sankuai.dzim.pilot.process.aireservebook;

import com.meituan.nibtp.trade.client.combine.response.AgentCreateOrderResDTO;
import com.sankuai.dzim.pilot.gateway.api.assistant.data.SendBeamMessageReq;
import com.sankuai.dzim.pilot.process.aiphonecall.data.AIPhoneCallTaskCreateRes;
import com.sankuai.dzim.pilot.process.aireservebook.data.ShopReserveContext;
import com.sankuai.dzim.pilot.process.aireservebook.data.TimeSliceStockInfo;
import com.sankuai.dzim.pilot.process.aireservebook.data.UserReserveInfo;
import com.sankuai.dzim.pilot.process.aireservebook.enums.ReserveItemType;
import com.sankuai.dzim.pilot.process.aireservebook.enums.ReserveStatusType;
import com.sankuai.dzim.pilot.process.data.bizref.MessageBizRefData;
import com.sankuai.dzim.pilot.scene.task.data.EnvContext;
import com.sankuai.dzim.pilot.utils.data.Response;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.wpt.user.retrieve.thrift.message.UserModel;

import java.util.List;
import java.util.Map;

/**
 * @author: wuwenqiang
 * @create: 2025-04-21
 * @description:
 */
public interface AIReserveBookProcessService {

    /**
     * 校验用户是否重复预约
     * @param userReserveInfo
     * @return
     */
    boolean checkUserDuplicateReserve(UserReserveInfo userReserveInfo);

    /**
     * 校验预约时间是否有效
     * @param userReserveInfo
     * @return
     */
    boolean checkTimeValid(UserReserveInfo userReserveInfo);

    /**
     * 获取门店后台二级主类目
     * @param dpPoiDTO
     * @return
     */
    Integer extractMainSecondBackCategoryId(DpPoiDTO dpPoiDTO);

    /**
     * 将团购详情转换为预约要素
     * @param dealId 团购ID
     * @param platform 端，MT/DP
     * @return 预约要素Map
     */
    Map<ReserveItemType, String> queryAndConvertDealDetail2ReserveItems(Long dealId, int platform);

    /**
     * 提取团单服务时长
     * @param dealGroupDTO
     * @return
     */
    Integer extractDuration(DealGroupDTO dealGroupDTO);

    /**
     * 创建预约/预订单
     * @param envContext 环境参数
     * @param userModel 美团用户信息
     * @param shopReserveContext 商户上下文
     * @param reserveInfo 预约信息
     * @param merchantRemark 商户备注
     * @param needStock 是否需要扣实际库存
     * @param availableTimeSliceStocks 可用库存（AI外呼不需要）
     * @return AgentCreateOrderResDTO
     */
    AgentCreateOrderResDTO createReserve(EnvContext envContext, UserModel userModel, ShopReserveContext shopReserveContext, UserReserveInfo reserveInfo, String merchantRemark, boolean needStock, List<TimeSliceStockInfo> availableTimeSliceStocks);

    /**
     * 创建AI外呼
     * @param userModel 用户信息
     * @param shopReserveContext 商户上下文
     * @param reserveInfo 预约信息
     * @param platform 端，MT/DP
     * @return
     */
    Response<AIPhoneCallTaskCreateRes> createAIPhoneCall(EnvContext envContext, UserModel userModel, ShopReserveContext shopReserveContext, UserReserveInfo reserveInfo, Long taskId, int platform);

    /**
     * AI外呼取消预约
     * @param userModel 用户信息
     * @param shopReserveContext 商户上下文
     * @param reserveInfo 预约信息
     * @param taskId 任务ID
     * @param platform 端，MT/DP
     * @return
     */
    Response<AIPhoneCallTaskCreateRes> refundAIPhoneCall(EnvContext envContext, UserModel userModel, ShopReserveContext shopReserveContext, UserReserveInfo reserveInfo, Long taskId, int platform);

    /**
     * 取消预约/预订单
     * @param envContext 环境参数
     * @param userModel 用户信息
     * @param reserveInfo 预约信息
     * @return
     */
    Response<Boolean> refundReserve(EnvContext envContext, UserModel userModel, UserReserveInfo reserveInfo);

    /**
     * 插入消息业务数据
     * @param userReserveInfo 用户预约信息
     * @param reserveStatusType 预约状态类型
     * @return
     */
    MessageBizRefData<UserReserveInfo> insertMessageBizRef(UserReserveInfo userReserveInfo, ReserveStatusType reserveStatusType, Long messageId);

    /**
     * 处理在线取消预约
     * @param userModel
     * @param userReserveInfo
     * @return
     */
    Response<MessageBizRefData<UserReserveInfo>> processOnlineCancelReserve(UserModel userModel, UserReserveInfo userReserveInfo);

    /**
     * 处理电话取消预约
     * @param userModel
     * @param userReserveInfo
     * @param shopReserveContext
     * @return
     * @throws Exception
     */
    Response<MessageBizRefData<UserReserveInfo>> processPhoneCallCancelReserve(UserModel userModel, UserReserveInfo userReserveInfo, ShopReserveContext shopReserveContext);

    /**
     * 【Beam】创建预约/预订单
     * @param sendBeamMessageReq
     * @param userModel
     * @param shopReserveContext
     * @param userReserveInfo
     * @param merchantRemark
     * @return
     */
    AgentCreateOrderResDTO createBeamReserve(SendBeamMessageReq sendBeamMessageReq, UserModel userModel, ShopReserveContext shopReserveContext, UserReserveInfo userReserveInfo, String merchantRemark, boolean needStock, List<TimeSliceStockInfo> availableTimeSliceStocks);

    /**
     * 【Beam】取消预约/预订单
     * @param sendBeamMessageReq
     * @param reserveInfo 预约信息
     * @return
     */
    Response<Boolean> refundBeamReserve(SendBeamMessageReq sendBeamMessageReq, UserReserveInfo reserveInfo);

    /**
     * 【Beam】创建AI外呼
     * @param sendBeamMessageReq
     * @param shopReserveContext 商户上下文
     * @param reserveInfo 预约信息
     * @param platform 端，MT/DP
     * @return
     */
    Response<AIPhoneCallTaskCreateRes> createBeamAIPhoneCall(SendBeamMessageReq sendBeamMessageReq, UserModel userModel, ShopReserveContext shopReserveContext, UserReserveInfo reserveInfo, Long taskId, int platform);

    /**
     * 【Beam】AI外呼取消预约
     * @param sendBeamMessageReq
     * @param userModel 用户信息
     * @param shopReserveContext 商户上下文
     * @param reserveInfo 预约信息
     * @param taskId 任务ID
     * @param platform 端，MT/DP
     * @return
     */
    Response<AIPhoneCallTaskCreateRes> refundBeamAIPhoneCall(SendBeamMessageReq sendBeamMessageReq, UserModel userModel, ShopReserveContext shopReserveContext, UserReserveInfo reserveInfo, Long taskId, int platform);
}
