package com.sankuai.dzim.pilot.acl;

import com.sankuai.clr.content.process.thrift.dto.ShopBookInfoProcessDTO;
import com.sankuai.clr.content.process.thrift.dto.leads.dto.LeadsInfoDTO;
import com.sankuai.dzim.consult.leads.dto.LeadsRecordDTO;
import com.sankuai.dzim.pilot.acl.data.LeadsProjectInfo;
import com.sankuai.dzim.pilot.acl.data.req.CreateBookReq;
import com.sankuai.dzim.pilot.acl.data.req.LoadShopLeadsInfoReq;
import com.sankuai.leads.process.thrift.dto.leads.GetLeadsRespDTO;
import com.sankuai.leads.process.thrift.dto.leads.LeadsDTO;
import com.sankuai.leads.process.thrift.resv.user.request.CreateUserResvOrderReqDTO;
import com.sankuai.leads.process.thrift.resv.user.request.LeadsContextReqDTO;
import com.sankuai.leads.process.thrift.resv.user.request.LeadsUserCommonReqDTO;
import com.sankuai.leads.process.thrift.resv.user.response.CreateUserResvOrderRespDTO;

import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 客资领域相关服务
 */
public interface LeadsAclService {

    /**
     * 查询门店预约信息，包含预约礼信息
     *
     * @param dpShopId
     * @return
     */
    CompletableFuture<ShopBookInfoProcessDTO> loadShopBookInfo(long dpShopId);

    /**
     * 查询留资信息，主要用户判断门店是否有留资权益，有才能走留资新接口
     *
     * @param loadShopLeadsInfoReq
     * @return
     */
    LeadsInfoDTO loadShopLeadsInfo(LoadShopLeadsInfoReq loadShopLeadsInfoReq);

    /**
     * 登记留资机器人服务记录
     *
     * @param imAccountId
     * @param imUserId
     * @return
     */
    boolean logLeadsRobotServerRec(String imAccountId, String imUserId);

    /**
     * 按条件查询用户留资记录
     *
     * @param imAccountId IM门店id
     * @param imUserId    IM用户id
     * @param beginTime   开始时间
     * @param endTime     结束时间
     * @return 符合条件的留资记录列表
     */
    LeadsRecordDTO queryUserLeadsRecord(String imAccountId, String imUserId, Date beginTime, Date endTime);

    Long createBook(CreateBookReq createBookReq);

    LeadsDTO getLeads(long bookLeadsId, long mtShopId);

    boolean bookFailedPush(int platform, long userId, String userPlainPhone);

    /**
     * 查询门店可预约项目
     */
    List<LeadsProjectInfo> queryCanReserveProjects(long mtShopId, Integer technicianId);


}
