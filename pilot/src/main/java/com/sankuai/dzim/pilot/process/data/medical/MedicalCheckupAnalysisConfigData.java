package com.sankuai.dzim.pilot.process.data.medical;

import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 体检报告解读配置
 * @author: zhouyi<PERSON>
 * @date: 2024/9/12
 */
@Data
public class MedicalCheckupAnalysisConfigData {

    private String titleImageUrl;

    private String subTitle;

    private String backgroundImageUrl;

    private String maleBodyImageUrl;

    private String femaleBodyImageUrl;

    private HashMap<String, MedicalCheckupAnalysisRiskGroupConfigData> riskGroupConfigMap;

    private String tipsCardTitleIcon;

    private String tipsCardTitle;

    private String tipsCardSubTitle;

    private HashMap<String, MedicalCheckupAnalysisAdviceConfigData> adviceConfigMap;

    private String assistantLinkIcon;

    private String assistantLinkUrl;

    private String analysisDetailUrl;

    private String analysisFailDetailUrl;

    private Map<String, MedicalCheckupAssistantUrl> assistantUrlMap;

    private String diseaseDetailUrl;

    private List<String> forbiddenUseKnowledgeWords;

    private String indicatorTitle;

    private String indicatorAnalysisTitle;

    private String indicatorTableValueUpIcon;

    private String indicatorTableValueDownIcon;

    private Map<String, String> personalInfoRegularExpressions;

    private String diseaseDetailShowNameFormat;

    private String moreAskQuestionShowName;

    private String moreAskQuestionFormat;

}
