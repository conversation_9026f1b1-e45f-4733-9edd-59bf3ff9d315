package com.sankuai.dzim.pilot.dal.pilotdao;

import com.sankuai.dzim.pilot.dal.entity.GenerativeSearchAnswerExtendEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface GenerativeSearchAnswerExtendDAO {

    int insert(@Param("entity") GenerativeSearchAnswerExtendEntity entity);

    int batchInsert(@Param("entities") List<GenerativeSearchAnswerExtendEntity> entities);

    List<GenerativeSearchAnswerExtendEntity> findByAnswerIdAndAttr(@Param("answerId") long answerId, @Param("attrMap") Map<String, Object> attrMap);

    int deleteByAnswerId(@Param("answerId") long answerId);

    int batchDeleteById(@Param("ids") List<Long> ids);

    List<Map<String, Object>> countByQuestionAndAttr(@Param("bizType") int bizType, @Param("questionList") List<String> questionList,
                                                     @Param("attrMap") Map<String, Object> attrMap);

    List<GenerativeSearchAnswerExtendEntity> rangeFindById(@Param("minId") int minId, @Param("maxId") int maxId);

    int updateValue(@Param("entity") GenerativeSearchAnswerExtendEntity entity);

    List<GenerativeSearchAnswerExtendEntity> paginateFindByAnswerId(@Param("answerId") long answerId, @Param("offset") int offset, @Param("limit") int limit);

    GenerativeSearchAnswerExtendEntity findById(@Param("id") Long id);

    int deleteBaiBuConfilct(@Param("ids") List<Long> ids, @Param("status") Integer status);
}
