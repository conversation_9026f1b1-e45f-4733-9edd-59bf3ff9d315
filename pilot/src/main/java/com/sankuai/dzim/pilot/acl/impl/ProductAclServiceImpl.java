package com.sankuai.dzim.pilot.acl.impl;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.deal.sales.common.datatype.SalesDisplayInfoDTO;
import com.dianping.deal.sales.common.datatype.SalesDisplayQueryRequest;
import com.dianping.deal.sales.common.datatype.SalesOption;
import com.dianping.deal.sales.common.datatype.SalesSubjectParam;
import com.dianping.deal.sales.display.api.service.SalesDisplayQueryService;
import com.dianping.general.unified.search.api.dealgroupsearch.GeneralDealGroupSearchService;
import com.dianping.general.unified.search.api.dealgroupsearch.dto.BaseSearchOption;
import com.dianping.general.unified.search.api.dealgroupsearch.dto.DealGroupSearchDTO;
import com.dianping.general.unified.search.api.dealgroupsearch.dto.PageOption;
import com.dianping.general.unified.search.api.dealgroupsearch.dto.SortOption;
import com.dianping.general.unified.search.api.dealgroupsearch.enums.DealGroupSearchIdPlatformEnum;
import com.dianping.general.unified.search.api.dealgroupsearch.enums.DealGroupSortFieldEnum;
import com.dianping.general.unified.search.api.dealgroupsearch.enums.DealGroupSortOrderEnum;
import com.dianping.general.unified.search.api.dealgroupsearch.enums.DealGroupStatusEnum;
import com.dianping.general.unified.search.api.dealgroupsearch.request.GeneralDealGroupSearchRequest;
import com.dianping.general.unified.search.api.dealgroupsearch.response.GeneralDealGroupSearchResponse;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.dianping.tpfun.product.api.common.IResponse;
import com.dianping.tpfun.product.api.sku.VenueProductService;
import com.dianping.tpfun.product.api.sku.booktable.UnifiedBookTableService;
import com.dianping.tpfun.product.api.sku.booktable.dto.PeriodStock;
import com.dianping.tpfun.product.api.sku.booktable.request.QueryBookTableRequest;
import com.dianping.tpfun.product.api.sku.dto.venue.*;
import com.dianping.vc.enums.VCClientTypeEnum;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.SettableFuture;
import com.meituan.inf.xmdlog.XMDLogFormat;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonClient;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.meituan.service.mobile.mtthrift.netty.ContextStore;
import com.sankuai.athena.client.executor.Futures;
import com.sankuai.athena.inf.AthenaInf;
import com.sankuai.athena.inf.rpc.RpcClient;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.acl.ProductAclService;
import com.sankuai.dzim.pilot.acl.data.DealDetailData;
import com.sankuai.dzim.pilot.acl.data.ProductTimeStateQueryItem;
import com.sankuai.dzim.pilot.acl.data.ShopTimeStateQueryItem;
import com.sankuai.dzim.pilot.acl.data.req.ProductSearchReq;
import com.sankuai.dzim.pilot.api.enums.assistant.PlatformEnum;
import com.sankuai.dzim.pilot.utils.CompletableFutureExpandUtils;
import com.sankuai.dztheme.deal.DealProductService;
import com.sankuai.dztheme.deal.dto.DealProductDTO;
import com.sankuai.dztheme.deal.dto.DealProductPromoDTO;
import com.sankuai.dztheme.deal.req.DealProductRequest;
import com.sankuai.dztheme.deal.res.DealProductResult;
import com.sankuai.dztheme.generalproduct.GeneralProductService;
import com.sankuai.dztheme.generalproduct.req.GeneralProductRequest;
import com.sankuai.dztheme.generalproduct.res.GeneralProductResult;
import com.sankuai.general.product.query.center.client.builder.model.*;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.AttrSubjectEnum;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.enums.TradeTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.general.product.query.center.client.request.QueryDealGroupIdByShopRequest;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupIdListResponse;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupIdListResult;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResponse;
import com.sankuai.general.product.query.center.client.service.DealGroupQueryService;
import com.sankuai.spt.statequery.api.dto.BaseStateDTO;
import com.sankuai.spt.statequery.api.dto.BaseStateQueryItemDTO;
import com.sankuai.spt.statequery.api.dto.ShopReserveModeDTO;
import com.sankuai.spt.statequery.api.dto.StateSubjectDTO;
import com.sankuai.spt.statequery.api.enums.BaseStateQueryConditionKeyEnum;
import com.sankuai.spt.statequery.api.enums.SubjectTypeEnum;
import com.sankuai.spt.statequery.api.request.QueryBaseStateRequest;
import com.sankuai.spt.statequery.api.request.QueryShopReserveModeRequest;
import com.sankuai.spt.statequery.api.response.QueryBaseStateResponse;
import com.sankuai.spt.statequery.api.response.QueryShopReserveModeResponse;
import com.sankuai.spt.statequery.api.service.BaseStateQueryService;
import com.sankuai.spt.statequery.api.service.ReserveModeQueryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Nonnull;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class ProductAclServiceImpl implements ProductAclService {

    @MdpThriftClient(timeout = 5000, testTimeout = 5000, remoteAppKey = "com.sankuai.productuser.query.center", async = false)
    private DealGroupQueryService dealGroupQueryService;

    @MdpThriftClient(remoteAppKey = "com.sankuai.productuser.query.center", timeout = 5000, testTimeout = 5000, async = true)
    private DealGroupQueryService dealGroupQueryServiceAsync;

    @MdpPigeonClient(url = "com.dianping.general.unified.search.api.dealgroupsearch.GeneralDealGroupSearchService", timeout = 5000)
    private GeneralDealGroupSearchService generalDealGroupSearchService;

    @MdpPigeonClient(url = "com.sankuai.dztheme.dealproduct.DealProductService", timeout = 5000)
    private DealProductService dealProductService;

    @RpcClient(timeout = 200, url = "com.dianping.deal.sales.display.api.service.SalesDisplayQueryService")
    private SalesDisplayQueryService salesDisplayQueryService;

    @MdpThriftClient(remoteAppKey = "com.sankuai.spt.statequery", timeout = 1000, testTimeout = 5000)
    private ReserveModeQueryService reserveModeQueryService;

    @MdpThriftClient(remoteAppKey = "com.sankuai.spt.statequery", timeout = 1000, testTimeout = 5000)
    private BaseStateQueryService baseStateQueryService;

    @RpcClient(url = "com.sankuai.dztheme.generalproduct.GeneralProductService", timeout = 50000)
    private GeneralProductService generalProductService;

    @RpcClient(url = "http://service.dianping.com/tpfunProductService/VenueProductService_1.0.0")
    private VenueProductService venueProductService;

    @RpcClient(url = "http://service.dianping.com/tpfunService/unifiedbooktableservice_1.0.0")
    private UnifiedBookTableService unifiedBookTableService;

    private static ThreadPool dealThemePool = Rhino.newThreadPool("DealThemePool",
            DefaultThreadPoolProperties.Setter().withCoreSize(5).withMaxSize(10).withMaxQueueSize(100));


    @Override
    public QueryDealGroupIdListResult queryProductByShopId(QueryDealGroupIdByShopRequest request) {

        try {
            QueryDealGroupIdListResponse listResponse = dealGroupQueryService.queryDealGroupIdByShop(request);
            if (listResponse == null || CollectionUtils.isEmpty(listResponse.getData())) {
                return null;
            }
            return listResponse.getData().get(0);
        } catch (Exception e) {
            log.error("queryProductByShopId error, request={}", JSON.toJSONString(request), e);
        }
        return null;
    }

    @Override
    public Map<Integer, Map<String, List<ProductDTO>>> getVenueProductByShop(GetVenueProductByShopReqDTO reqDTO) {
        IResponse<GetVenueProductByShopRespDTO> result = venueProductService.getVenueProductByShop(reqDTO);
        if (result == null || !result.isSuccess()) {
            return Maps.newHashMap();
        }
        GetVenueProductByShopRespDTO responseDTO = result.getResult();
        if (responseDTO == null || MapUtils.isEmpty(responseDTO.getTypeDayProducts())) {
            return Maps.newHashMap();
        }
        return responseDTO.getTypeDayProducts();
    }

    @Override
    public List<PeriodStock> batchQueryBookTableStock(List<QueryBookTableRequest> queryBookTableRequests) {
        if (CollectionUtils.isEmpty(queryBookTableRequests)) {
            return Lists.newArrayList();
        }
        List<List<PeriodStock>> periodStockList = queryBookTableRequests.stream()
                .map(request -> queryBookTableStock(request)).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(periodStockList)) {
            return Lists.newArrayList();
        }

        return periodStockList.stream().filter(Objects::nonNull)
                        .filter(CollectionUtils::isNotEmpty)
                        .flatMap(List::stream)
                        .collect(Collectors.toList());

    }

    private List<PeriodStock> queryBookTableStock(QueryBookTableRequest bookTableRequest) {
        IResponse<List<PeriodStock>> response = unifiedBookTableService.queryBookTable(bookTableRequest);
        if (Objects.isNull(response) || !response.isSuccess() || CollectionUtils.isEmpty(response.getResult())) {
            return null;
        }
        return response.getResult();
    }

    @Override
    public List<DealGroupDTO> queryReserveProductBaseInfo(List<Long> platformProductIds, Long platformCategoryId, Set<String> dealGroupAttrs, Set<String> dealAttrs) {
        if (Objects.isNull(platformProductIds)) {
            return null;
        }

        // 分批处理，每批最多50个
        List<List<Long>> partitions = Lists.partition(new ArrayList<>(platformProductIds), 50);
        List<DealGroupDTO> result = new ArrayList<>();

        for (List<Long> partition : partitions) {
            QueryByDealGroupIdRequest request = QueryByDealGroupIdRequestBuilder.builder()
                    .dealGroupIds(new HashSet<>(partition), IdTypeEnum.MT)
                    .dealGroupId(DealGroupIdBuilder.builder().all())
                    .category(DealGroupCategoryBuilder.builder().all())
                    .dealId(DealIdBuilder.builder().bizDealId())
                    .dealBasicInfo(DealBasicInfoBuilder.builder().status())
                    .basicInfo(DealGroupBasicInfoBuilder.builder())
                    .attrsByKey(AttrSubjectEnum.DEAL_GROUP, dealGroupAttrs)
                    .attrsByKey(AttrSubjectEnum.DEAL, dealAttrs)
                    .needQueryFunProduct()
                    .build();
            try {
                QueryDealGroupListResponse response = dealGroupQueryService.queryByDealGroupIds(request);
                if (Objects.isNull(response) || Objects.isNull(response.getData())) {
                    return null;
                }
                List<DealGroupDTO> dealGroupDTOList = response.getData().getList();
                if (CollectionUtils.isEmpty(dealGroupDTOList)) {
                    return null;
                }
                List<DealGroupDTO> groupDTOList = dealGroupDTOList.stream().filter(e -> e.getCategory() != null && platformCategoryId.equals(e.getCategory().getPlatformCategoryId())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(groupDTOList)) {
                    return null;
                }
                result.addAll(groupDTOList);
            } catch (Exception e) {
                log.error("queryReserveProductBaseInfo error, request={}", JSON.toJSONString(request), e);
            }
        }
        return result;
    }

    @Override
    public CompletableFuture<GeneralProductResult> queryGeneralProductTheme(GeneralProductRequest generalProductRequest) {
        return AthenaInf.getRpcCompletableFuture(generalProductService.query(generalProductRequest))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build().putTag("scene", "compositeAtomService")
                            .putTag("method", "queryGeneralProductTheme")
                            .message(String.format("查询泛商品主题异常，request: %s", JsonCodec.encode(generalProductRequest))));
                    return null;
                });
    }

    @Override
    public DealGroupDTO getDealBaseInfo(long dealId) {
        return getDealBaseInfo(dealId, false);
    }

    @Override
    public DealGroupDTO getDealBaseInfo(long dealId, boolean isMt) {
        IdTypeEnum idTypeEnum = isMt ? IdTypeEnum.MT : IdTypeEnum.DP;
        QueryByDealGroupIdRequest request = QueryByDealGroupIdRequestBuilder.builder()
                .dealGroupIds(Sets.newHashSet(dealId), idTypeEnum)
                .basicInfo(DealGroupBasicInfoBuilder.builder().all())
                .image(DealGroupImageBuilder.builder().all())
                .dealGroupId(DealGroupIdBuilder.builder().all())
                .dealGroupPrice(DealGroupPriceBuilder.builder().all())
                .category(DealGroupCategoryBuilder.builder().all())
                .rule(DealGroupRuleBuilder.builder().bookingRule().buyRule().refundRule().buyRule())
                .serviceProject(ServiceProjectBuilder.builder().all())
                .detail(DealGroupDetailBuilder.builder().all())
                .dealBasicInfo(DealBasicInfoBuilder.builder().all())
                .dealPrice(DealPriceBuilder.builder().all())
                .channel(DealGroupChannelBuilder.builder().all())
                .dealId(DealIdBuilder.builder().id())
                .dealDelivery(DealDeliveryBuilder.builder().all())
                .attrsByKey(AttrSubjectEnum.DEAL_GROUP, "pay_method")
                .build();

        try {
            QueryDealGroupListResponse queryDealGroupListResponse = dealGroupQueryService.queryByDealGroupIds(request);
            log.info("getDealBaseInfo return request = {}, response = {}", JsonCodec.encodeWithUTF8(request), JsonCodec.encodeWithUTF8(queryDealGroupListResponse));
            if (queryDealGroupListResponse != null && queryDealGroupListResponse.getData() != null
                    && CollectionUtils.isNotEmpty(queryDealGroupListResponse.getData().getList())) {
                return queryDealGroupListResponse.getData().getList().get(0);
            }
            return null;
        } catch (TException e) {
            LogUtils.logFailLog(log, TagContext.builder().action("getDealBaseInfo").bizId(String.valueOf(dealId)).build(),
                    new WarnMessage("ProductAclService", "团购信息查询失败", null), request, null, e);
            return null;
        }
    }

    @Override
    public Map<Long, DealGroupDTO> batchGetDealBaseInfo(List<Long> dealIds) {
        return batchGetDealBaseInfo(dealIds, false);
    }

    @Override
    public Map<Long, DealGroupDTO> batchGetDealBaseInfo(List<Long> dealIds, boolean isMt) {
        if (CollectionUtils.isEmpty(dealIds)) {
            return Maps.newHashMap();
        }

        dealIds = dealIds.stream().distinct().collect(Collectors.toList());

        List<List<Long>> partitionDeal = Lists.partition(dealIds, 20);
        Map<Long, DealGroupDTO> dealGroupDTOMap = Maps.newHashMap();
        try {
            for (List<Long> dealIdList : partitionDeal) {
                QueryByDealGroupIdRequest request = QueryByDealGroupIdRequestBuilder.builder()
                        .dealGroupIds(Sets.newHashSet(dealIdList), isMt ? IdTypeEnum.MT : IdTypeEnum.DP)
                        .basicInfo(DealGroupBasicInfoBuilder.builder().all())
                        .image(DealGroupImageBuilder.builder().all())
                        .dealGroupId(DealGroupIdBuilder.builder().all())
                        .dealGroupPrice(DealGroupPriceBuilder.builder().all())
                        .category(DealGroupCategoryBuilder.builder().all())
                        .rule(DealGroupRuleBuilder.builder().bookingRule().buyRule().refundRule().buyRule())
                        .serviceProject(ServiceProjectBuilder.builder().all())
                        .detail(DealGroupDetailBuilder.builder().all())
                        .dealBasicInfo(DealBasicInfoBuilder.builder().all())
                        .dealPrice(DealPriceBuilder.builder().all())
                        .channel(DealGroupChannelBuilder.builder().all())
                        .dealId(DealIdBuilder.builder().id())
                        .build();

                QueryDealGroupListResponse queryDealGroupListResponse = dealGroupQueryService.queryByDealGroupIds(request);
                if (queryDealGroupListResponse != null && queryDealGroupListResponse.getData() != null
                        && CollectionUtils.isNotEmpty(queryDealGroupListResponse.getData().getList())) {
                    for (DealGroupDTO dealGroupDTO : queryDealGroupListResponse.getData().getList()) {
                        if (dealGroupDTOMap.containsKey(dealGroupDTO.getMtDealGroupId()) || dealGroupDTO.getMtDealGroupId() == null) {
                            continue;
                        }
                        dealGroupDTOMap.put(dealGroupDTO.getMtDealGroupId(), dealGroupDTO);
                    }
                }
            }
            return dealGroupDTOMap;
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("batchGetDealBaseInfo").bizId(String.valueOf(dealIds)).build(),
                    new WarnMessage("ProductAclService", "批量团购信息查询失败", null), dealIds, null, e);
            return null;
        }
    }

    @Override
    public CompletableFuture<Map<Long, DealGroupDTO>> batchGetDealBaseInfoAsync(List<Long> dealIds, boolean isMt) {
        if(CollectionUtils.isEmpty(dealIds)) {
            return CompletableFuture.completedFuture(Maps.newHashMap());
        }
        // 分批
        List<List<Long>> partition = Lists.partition(dealIds, 20);
        List<CompletableFuture<Map<Long, DealGroupDTO>>> listCf = partition.stream().map(partitionDealIds ->
                doBatchGetDealBaseInfoAsync(partitionDealIds, isMt)).collect(Collectors.toList());
        return CompletableFutureExpandUtils.eachMapList(listCf);
    }

    @SuppressWarnings({"unchecked", "UnstableApiUsage"})
    public CompletableFuture<Map<Long, DealGroupDTO>> doBatchGetDealBaseInfoAsync(List<Long> dealIds, boolean isMt) {
        if(CollectionUtils.isEmpty(dealIds)) {
            return CompletableFuture.completedFuture(Maps.newHashMap());
        }
        try {
            CompletableFuture<Map<Long, DealGroupDTO>> cf = new CompletableFuture<>();
            QueryByDealGroupIdRequest request = QueryByDealGroupIdRequestBuilder.builder()
                    .dealGroupIds(Sets.newHashSet(dealIds), isMt ? IdTypeEnum.MT : IdTypeEnum.DP)
                    .basicInfo(DealGroupBasicInfoBuilder.builder().all())
                    .image(DealGroupImageBuilder.builder().all())
                    .dealGroupId(DealGroupIdBuilder.builder().all())
                    .dealGroupPrice(DealGroupPriceBuilder.builder().all())
                    .category(DealGroupCategoryBuilder.builder().all())
                    .rule(DealGroupRuleBuilder.builder().bookingRule().buyRule().refundRule().buyRule())
                    .serviceProject(ServiceProjectBuilder.builder().all())
                    .detail(DealGroupDetailBuilder.builder().all())
                    .dealBasicInfo(DealBasicInfoBuilder.builder().all())
                    .dealPrice(DealPriceBuilder.builder().all())
                    .channel(DealGroupChannelBuilder.builder().all())
                    .dealId(DealIdBuilder.builder().id())
                    .build();
            dealGroupQueryService.queryByDealGroupIds(request);
            SettableFuture<QueryDealGroupListResponse> queryByDealGroupIdsFuture =
                    (SettableFuture<QueryDealGroupListResponse>) ContextStore.getSettableFuture();
            com.google.common.util.concurrent.Futures.addCallback(queryByDealGroupIdsFuture, new FutureCallback<QueryDealGroupListResponse>() {
                @Override
                public void onSuccess(QueryDealGroupListResponse response) {
                    if(response == null || response.getData() == null
                            || CollectionUtils.isEmpty(response.getData().getList())) {
                        cf.complete(Maps.newHashMap());
                        return;
                    }
                    List<DealGroupDTO> dealGroupDtoList = response.getData().getList();
                    Map<Long, DealGroupDTO> res = Maps.newHashMap();
                    dealGroupDtoList.forEach(dealGroupDTO -> {
                        if(dealGroupDTO == null || dealGroupDTO.getBasic() == null) {
                            return;
                        }
                        long dealGroupId = isMt ?
                                dealGroupDTO.getMtDealGroupId() : dealGroupDTO.getDpDealGroupId();
                        res.put(dealGroupId, dealGroupDTO);
                    });
                    cf.complete(res);
                }

                @Override
                public void onFailure(@Nonnull Throwable throwable) {
                    Cat.logError(throwable);
                    log.error("queryDealInfoByDealGroupIds onFailure, dealGroupIds:{}, isMt: {}", dealIds, isMt);
                    cf.complete(Maps.newHashMap());
                }
            });
            return cf;
        } catch (Exception e) {
            Cat.logError(e);
            log.error("queryDealInfoByDealGroupIds error, dealGroupIds:{}, isMt: {}", dealIds, isMt, e);
            return CompletableFuture.completedFuture(Maps.newHashMap());
        }
    }

    @Override
    public List<DealGroupDTO> batchQueryDealBaseInfo(List<Long> dealIds) {
        if (CollectionUtils.isEmpty(dealIds)) {
            return null;
        }
        HashSet<Long> dealGroupIds = new HashSet<>(dealIds);
        Assert.isTrue(dealGroupIds.size() <= 20, "dealIds长度不能超过20");
        QueryByDealGroupIdRequest request = QueryByDealGroupIdRequestBuilder.builder()
                .dealGroupIds(dealGroupIds, IdTypeEnum.DP)
                .basicInfo(DealGroupBasicInfoBuilder.builder().all())
                .build();

        try {
            QueryDealGroupListResponse queryDealGroupListResponse = dealGroupQueryService.queryByDealGroupIds(request);
            if (queryDealGroupListResponse != null && queryDealGroupListResponse.getData() != null
                    && CollectionUtils.isNotEmpty(queryDealGroupListResponse.getData().getList())) {
                return queryDealGroupListResponse.getData().getList();
            }
            return null;
        } catch (TException e) {
            LogUtils.logFailLog(log, TagContext.builder().action("batchQueryDealBaseInfo").bizId(dealIds.toString()).build(),
                    new WarnMessage("batchQueryDealBaseInfo", "批量团购信息查询失败", null), request, null, e);
            return null;
        }
    }

    @Override
    public List<DealDetailData> searchProduct(ProductSearchReq req) {
        try {
            // 搜索商品
            GeneralDealGroupSearchRequest request = builderGeneralDealGroupSearchRequest(req);
            GeneralDealGroupSearchResponse generalDealGroupSearchResponse = generalDealGroupSearchService.searchDealGroups(request);
            if (generalDealGroupSearchResponse == null || CollectionUtils.isEmpty(generalDealGroupSearchResponse.getResult())) {
                LogUtils.logFailLog(log, TagContext.builder().action("searchProduct").build(),
                        new WarnMessage("ProductAclService", "搜索团购失败", null), req, generalDealGroupSearchResponse);
                return Lists.newArrayList();
            }

            // 团购主题
            List<Long> dealIds = generalDealGroupSearchResponse.getResult().stream().map(DealGroupSearchDTO::getDealGroupId).distinct().collect(Collectors.toList());
            List<DealProductDTO> dealProductDTOS = getDealDetail(req, dealIds);
            if (CollectionUtils.isEmpty(dealProductDTOS)) {
                return Lists.newArrayList();
            }

            List<DealDetailData> dealDetailDataList = buildDealDetailData(dealProductDTOS);
            LogUtils.logReturnInfo(log, TagContext.builder().action("searchProductSuccess").build(),
                    new WarnMessage("ProductAclService", "搜索团购成功", null), req, dealDetailDataList);
            return dealDetailDataList;
        } catch (Exception e) {
            log.error("searchProduct exception, req:{}", JsonCodec.encode(req), e);
            return Lists.newArrayList();
        }
    }

    /**
     * 根据美团团单id查询点评团单id
     * @param mtDealIds 单次批量查询当前限制最大团单数量20 https://km.sankuai.com/collabpage/1603747248
     * @return
     */
    @Override
    public List<DealGroupDTO> queryDpDealIdByMtDealId(List<Long> mtDealIds) {
        if (CollectionUtils.isEmpty(mtDealIds)) {
            return Collections.emptyList();
        }
        List<DealGroupDTO> dealGroupDTOS = Lists.newArrayList();
        List<List<Long>> partitionMtDealIds = Lists.partition(mtDealIds, 20);
        try {
            for (List<Long> dealIdList : partitionMtDealIds) {
                HashSet<Long> mtDealGroupIds = new HashSet<>(dealIdList);
                QueryByDealGroupIdRequest request = QueryByDealGroupIdRequestBuilder.builder()
                        .dealGroupIds(mtDealGroupIds, IdTypeEnum.MT)
                        .dealGroupId(DealGroupIdBuilder.builder().dpDealGroupId())
                        .build();
                QueryDealGroupListResponse queryDealGroupListResponse = dealGroupQueryService.queryByDealGroupIds(request);
                if (queryDealGroupListResponse != null
                        && queryDealGroupListResponse.getData() != null
                        && CollectionUtils.isNotEmpty(queryDealGroupListResponse.getData().getList())) {
                    dealGroupDTOS.addAll(queryDealGroupListResponse.getData().getList());
                }
            }
            return dealGroupDTOS;
        } catch (TException e) {
            LogUtils.logFailLog(log, TagContext.builder().action("queryDpDealIdByMtDealId").bizId(mtDealIds.toString()).build(),
                    new WarnMessage("queryDpDealIdByMtDealId", "批量查询点评团购id信息失败", null), null, e);
            return null;
        }
    }

    private List<DealDetailData> buildDealDetailData(List<DealProductDTO> dealProductDTOS) {
        List<DealDetailData> dealDetailDataList = Lists.newArrayList();
        for (DealProductDTO dealProductDTO : dealProductDTOS) {
            DealDetailData dealDetailData = new DealDetailData();
            dealDetailData.setDealId(dealProductDTO.getProductId());
            dealDetailData.setDealName(dealProductDTO.getName());
            dealDetailData.setHeadPic(dealProductDTO.getHeadPic());
            dealDetailData.setDetailUrl(dealProductDTO.getDetailUr());
            dealDetailData.setMarketPrice(dealProductDTO.getMarketPriceTag());
            dealDetailData.setPromoPrice(formatPrice(dealProductDTO.getPromoPrice()));
            if (CollectionUtils.isNotEmpty(dealProductDTO.getSales())) {
                dealDetailData.setSaleTag(dealProductDTO.getSales().get(0).getSaleTag());
                dealDetailData.setSale(dealProductDTO.getSales().get(0).getSale());
            }
            if (CollectionUtils.isNotEmpty(dealProductDTO.getPromoPrices()) && dealProductDTO.getPromoPrices().get(0).getDiscount() != null) {
                dealDetailData.setDiscountTag(getDiscountTag(dealProductDTO.getPromoPrices().get(0)));
            }
            dealDetailDataList.add(dealDetailData);
        }
        return dealDetailDataList;
    }

    private String formatPrice(BigDecimal price) {
        if (price == null) {
            return "";
        }
        String formattedPrice = price.setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
        return formattedPrice.endsWith(".") ? formattedPrice.substring(0, formattedPrice.length() - 1) : formattedPrice;
    }

    private String getDiscountTag(DealProductPromoDTO dealProductPromoDTO) {
        String discount = dealProductPromoDTO.getDiscount().multiply(BigDecimal.TEN).setScale(1, RoundingMode.HALF_UP).toString();
        return discount + "折";
    }

    private DealProductRequest builderDealThemeReq(ProductSearchReq req, List<Long> dealIds) {
        DealProductRequest dealProductRequest = new DealProductRequest();
        dealProductRequest.setPlanId(req.getPlanId());
        dealProductRequest.setExtParams(buildExtParams(req, dealIds));
        return dealProductRequest;
    }

    private Map<String, Object> buildExtParams(ProductSearchReq req, List<Long> dealIds) {
        Map<String, Object> extParams = Maps.newHashMap();
        extParams.put("dealIds", dealIds.stream().map(Long::intValue).collect(Collectors.toList()));
        extParams.put("clientType", getDealThemeClientType(req));
        extParams.put("platform", req.getPlatform());
        extParams.put("userId", req.getUserId());
        extParams.put("lat", req.getLat());
        extParams.put("lng", req.getLng());
        if (req.getCityId() > 0) {
            extParams.put("cityId", req.getCityId());
        }
        extParams.put("dealId2ShopIdForLong", getDealId2ShopIdForLong(req.getShopIds().get(0), dealIds));
        if (StringUtils.isNotBlank(req.getDeviceId())) {
            extParams.put("deviceId", req.getDeviceId());
        }
        if (StringUtils.isNotBlank(req.getUnionId())) {
            extParams.put("unionId", req.getUnionId());
        }
        if (StringUtils.isNotBlank(req.getAppVersion())) {
            extParams.put("appVersion", req.getAppVersion());
        }
        extParams.put("directPromoSceneCode", 400200);
        return extParams;
    }

    private Object getDealId2ShopIdForLong(Long ShopId, List<Long> dealIds) {
        Map<Integer, Long> dealId2ShopId = Maps.newHashMap();
        for (Long dealId : dealIds) {
            dealId2ShopId.put(dealId.intValue(), ShopId);
        }
        return dealId2ShopId;
    }

    private Object getDealThemeClientType(ProductSearchReq req) {
        if (req.getPlatform() == PlatformEnum.DP.getType()) {
            return VCClientTypeEnum.DP_APP.getCode();
        }
        return VCClientTypeEnum.MT_APP.getCode();
    }

    private List<DealProductDTO> getDealDetail(ProductSearchReq req, List<Long> dealIds) {
        if (dealIds.size() <= 20) {
            DealProductRequest dealProductRequest = builderDealThemeReq(req, dealIds);
            DealProductResult result = dealProductService.query(dealProductRequest);
            if (result == null || CollectionUtils.isEmpty(result.getDeals())) {
                LogUtils.logFailLog(log, TagContext.builder().action("getDealDetailFail").bizId(dealIds.toString()).build(),
                        new WarnMessage("ProductAclService", "查询团购主题失败", null), dealProductRequest, result);
                return Lists.newArrayList();
            }

            return result.getDeals();
        }


        // 超过20个,异步分批查询
        List<List<Long>> dealPartitions = Lists.partition(dealIds, 20);
        List<DealProductDTO> dealProductDTOS = Lists.newArrayList();
        List<Future<Void>> futures = new ArrayList<>();
        for (List<Long> partitionDealId : dealPartitions) {
            Future future = dealThemePool.submit(() -> {
                DealProductRequest partitionRequest = builderDealThemeReq(req, partitionDealId);
                DealProductResult result = dealProductService.query(partitionRequest);
                if (result != null && CollectionUtils.isNotEmpty(result.getDeals())) {
                    dealProductDTOS.addAll(result.getDeals());
                } else {
                    LogUtils.logFailLog(log, TagContext.builder().action("getDealDetailFail").bizId(partitionDealId.toString()).build(),
                            new WarnMessage("ProductAclService", "查询团购主题失败", null), partitionRequest, result);
                }
                return null;
            });
            futures.add(future);
        }

        // 等待所有任务完成
        for (Future<Void> future : futures) {
            try {
                Futures.get(future);
            } catch (Exception e) {
                log.error("Error while waiting for deal detail query task", e);
            }
        }

        return dealProductDTOS;
    }

    private GeneralDealGroupSearchRequest builderGeneralDealGroupSearchRequest(ProductSearchReq req) {
        GeneralDealGroupSearchRequest request = new GeneralDealGroupSearchRequest();
        request.setPlatform(buildDealPlatform(req));
        request.setBaseSearchOption(buildBaseSearchOption(req));
        request.setPageOption(buildPageOption(req));
        request.setSortOptions(buildSortOptions(req));

        return request;
    }

    private List<SortOption> buildSortOptions(ProductSearchReq req) {
        SortOption sortOption = new SortOption();
        if (req.getPlatform() == PlatformEnum.DP.getType()) {
            sortOption.setSortField(DealGroupSortFieldEnum.DP_SALES_VOLUME);
            sortOption.setSortOrder(DealGroupSortOrderEnum.DESC);
            return Lists.newArrayList(sortOption);
        }

        // 美团暂不支持按销量排序
        return null;
    }


    private DealGroupSearchIdPlatformEnum buildDealPlatform(ProductSearchReq req) {
        if (req.getPlatform() == PlatformEnum.DP.getType()) {
            return DealGroupSearchIdPlatformEnum.DP;
        }
        return DealGroupSearchIdPlatformEnum.MT;
    }

    private BaseSearchOption buildBaseSearchOption(ProductSearchReq req) {
        BaseSearchOption baseSearchOption = new BaseSearchOption();
        baseSearchOption.setTradeTypeList(Lists.newArrayList(TradeTypeEnum.GROUPBUY_PAY.getCode()));
        if (CollectionUtils.isNotEmpty(req.getShopIds())) {
            baseSearchOption.setShopIds(req.getShopIds());
        }
        if (CollectionUtils.isNotEmpty(req.getFilterTagIds())) {
            baseSearchOption.setTagIds(req.getFilterTagIds());
        }
        baseSearchOption.setStatusIds(Lists.newArrayList(DealGroupStatusEnum.ONLINE));
        baseSearchOption.setInSale(true);
        baseSearchOption.setVisible(true);
        return baseSearchOption;
    }


    private PageOption buildPageOption(ProductSearchReq req) {
        PageOption pageOption = new PageOption();
        pageOption.setPageNo(req.getPageNo());
        pageOption.setPageSize(req.getPageSize());
        return pageOption;
    }

    @Override
    public CompletableFuture<Map<Long, SalesDisplayInfoDTO>> batchQueryReserveSales(List<Long> productIds, boolean isMt) {
        Map<Long, SalesDisplayInfoDTO> productId2Sales = Maps.newHashMap();

        if (CollectionUtils.isEmpty(productIds)) {
            return CompletableFuture.completedFuture(productId2Sales);
        }

        // 上限一次查50个
        List<List<Long>> dealIdPartitions = Lists.partition(productIds, 50);
        List<CompletableFuture<Map<SalesSubjectParam, SalesDisplayInfoDTO>>> collect =
                dealIdPartitions.stream().map(partition -> batchQuerySales(buildReserveSalesDisplayQueryRequest(partition, isMt))).collect(Collectors.toList());

        CompletableFuture<Map<SalesSubjectParam, SalesDisplayInfoDTO>> dealSalesCf = CompletableFutureExpandUtils.eachMapList(collect);
        return dealSalesCf.thenApply(salesMap -> {
            if (MapUtils.isEmpty(salesMap)) {
                return productId2Sales;
            }
            salesMap.forEach((key, value) -> productId2Sales.put(key.getProductId(), value));
            return productId2Sales;
        }).exceptionally(e -> {
            Cat.logError(e);
            log.error("batchQueryDealSales exception, dealIds:{}", productIds, e);
            return productId2Sales;
        });
    }

    private SalesDisplayQueryRequest buildReserveSalesDisplayQueryRequest(List<Long> productIds, boolean isMt) {
        SalesDisplayQueryRequest request = new SalesDisplayQueryRequest();
        List<SalesSubjectParam> salesSubjectParams = productIds.stream().map(productId -> isMt ?
                        SalesSubjectParam.bizGeneralProduct(productId) : SalesSubjectParam.bizGeneralProduct(productId))
                .collect(Collectors.toList());
        request.setSubjectParams(salesSubjectParams);
        request.setOption(SalesOption.defaultOption());
        return request;
    }


    private CompletableFuture<Map<SalesSubjectParam, SalesDisplayInfoDTO>> batchQuerySales(SalesDisplayQueryRequest request) {
        return AthenaInf.getRpcCompletableFuture(salesDisplayQueryService.batchQuerySales(request))
                .exceptionally(e -> {
                    log.error("batchQuerySales exception, request:{}", JsonCodec.encode(request), e);
                    return null;
                }).thenApply(resp -> {
                    if (resp == null || MapUtils.isEmpty(resp.getData())) {
                        return Collections.emptyMap();
                    }
                    return resp.getData();
                });
    }

    @Override
    public CompletableFuture<Map<Long, SalesDisplayInfoDTO>> batchQueryDealSales(List<Long> dealIds, boolean isMt) {
        Map<Long, SalesDisplayInfoDTO> dealId2Sales = Maps.newHashMap();

        if (CollectionUtils.isEmpty(dealIds)) {
            return CompletableFuture.completedFuture(dealId2Sales);
        }

        // 上限一次查50个
        List<List<Long>> dealIdPartitions = Lists.partition(dealIds, 50);
        List<CompletableFuture<Map<SalesSubjectParam, SalesDisplayInfoDTO>>> collect =
                dealIdPartitions.stream().map(partition -> batchQuerySales(buildSalesDisplayQueryRequest(partition, isMt))).collect(Collectors.toList());

        CompletableFuture<Map<SalesSubjectParam, SalesDisplayInfoDTO>> dealSalesCf = CompletableFutureExpandUtils.eachMapList(collect);
        return dealSalesCf.thenApply(salesMap -> {
            if (MapUtils.isEmpty(salesMap)) {
                return dealId2Sales;
            }
            salesMap.forEach((key, value) -> dealId2Sales.put(key.getProductId(), value));
            return dealId2Sales;
        }).exceptionally(e -> {
            Cat.logError(e);
            log.error("batchQueryDealSales exception, dealIds:{}", dealIds, e);
            return dealId2Sales;
        });
    }

    private SalesDisplayQueryRequest buildSalesDisplayQueryRequest(List<Long> dpDealIds, boolean isMt) {
        SalesDisplayQueryRequest request = new SalesDisplayQueryRequest();
        List<SalesSubjectParam> salesSubjectParams = dpDealIds.stream().map(dealId -> isMt ?
                SalesSubjectParam.ptDealGroup(dealId) : SalesSubjectParam.bizDealGroup(dealId))
                .collect(Collectors.toList());
        request.setSubjectParams(salesSubjectParams);
        request.setOption(SalesOption.defaultOption());
        return request;
    }

    @Override
    public Map<Long, ShopReserveModeDTO> batchQueryShopReserveMode(List<Long> mtShopIds) {
        if (CollectionUtils.isEmpty(mtShopIds)) {
            return null;
        }
        // 上限1次查20个
        List<List<Long>> shopIdPartitions = Lists.partition(mtShopIds, 20);
        List<CompletableFuture<Map<Long, ShopReserveModeDTO>>> futures = Lists.newArrayList();
        for (List<Long> partition : shopIdPartitions) {
            CompletableFuture<Map<Long, ShopReserveModeDTO>> future = CompletableFuture.supplyAsync(() -> queryShopReserveModeLimit(partition), dealThemePool.getExecutor())
                    .exceptionally(e -> {
                        log.error("batchQueryShopReserveMode exception, mtShopIds:{}", mtShopIds, e);
                        return null;
                    });
            futures.add(future);
        }
        return mergeAsyncResults(futures);
    }

    private Map<Long, ShopReserveModeDTO> queryShopReserveModeLimit(List<Long> mtShopIds) {
        if (CollectionUtils.isEmpty(mtShopIds)) {
            return null;
        }
        QueryShopReserveModeRequest req = buildQueryShopReserveModeReq(mtShopIds);
        QueryShopReserveModeResponse resp = reserveModeQueryService.queryShopReserveMode(req);
        if (resp == null || resp.getData() == null || CollectionUtils.isEmpty(resp.getData().getShopReserveModes())) {
            return null;
        }
        List<ShopReserveModeDTO> shopReserveModes = resp.getData().getShopReserveModes();
        return shopReserveModes.stream().filter(shopReserveMode -> shopReserveMode.getMtShopId() != null)
                .collect(Collectors.toMap(ShopReserveModeDTO::getMtShopId, Function.identity(), (a, b) -> a));
    }

    private QueryShopReserveModeRequest buildQueryShopReserveModeReq(List<Long> mtShopIds) {
        QueryShopReserveModeRequest req = new QueryShopReserveModeRequest();
        req.setMtShopIds(mtShopIds);
        return req;
    }

    @Override
    public Map<ShopTimeStateQueryItem, BaseStateDTO> batchQueryShopCanBookingState(List<ShopTimeStateQueryItem> queryItems, Map<BaseStateQueryConditionKeyEnum, String> condition) {
        if (CollectionUtils.isEmpty(queryItems)) {
            return null;
        }
        // 上限1次查20个
        List<List<ShopTimeStateQueryItem>> queryItemPartitions = Lists.partition(queryItems, 20);
        List<CompletableFuture<Map<ShopTimeStateQueryItem, BaseStateDTO>>> futures = Lists.newArrayList();
        for (List<ShopTimeStateQueryItem> partition : queryItemPartitions) {
            CompletableFuture<Map<ShopTimeStateQueryItem, BaseStateDTO>> future = CompletableFuture.supplyAsync(() -> queryShopCanBookingStateLimit(partition, condition), dealThemePool.getExecutor())
                    .exceptionally(e -> {
                        log.error("queryShopCanBookingState exception, queryItems:{}, condition={}", JSON.toJSONString(queryItems), JSON.toJSONString(condition), e);
                        return null;
                    });
            futures.add(future);
        }
        return mergeAsyncResults(futures);
    }

    private Map<ShopTimeStateQueryItem, BaseStateDTO> queryShopCanBookingStateLimit(List<ShopTimeStateQueryItem> queryItems, Map<BaseStateQueryConditionKeyEnum, String> condition) {
        QueryBaseStateRequest req = buildShopQueryBaseStateReq(queryItems, condition);
        QueryBaseStateResponse resp = baseStateQueryService.queryBaseState(req);
        log.info("[ProductAclService] 查询门店库存 req={}, resp={}", JSON.toJSONString(req), JSON.toJSONString(resp));
        if (resp == null || resp.getData() == null || CollectionUtils.isEmpty(resp.getData().getBaseStates())) {
            return null;
        }
        List<BaseStateDTO> baseStateList = resp.getData().getBaseStates();
        return baseStateList.stream().filter(baseState -> convert2ShopTimeStateQueryItem(baseState) != null)
                .collect(Collectors.toMap(baseState -> convert2ShopTimeStateQueryItem(baseState), Function.identity(), (a, b) -> a));
    }

    private QueryBaseStateRequest buildShopQueryBaseStateReq(List<ShopTimeStateQueryItem> queryItems, Map<BaseStateQueryConditionKeyEnum, String> condition) {
        QueryBaseStateRequest req = new QueryBaseStateRequest();
        req.setQuerySubjectType(SubjectTypeEnum.SHOP);
        List<BaseStateQueryItemDTO> items = Lists.newArrayList();
        for (ShopTimeStateQueryItem queryItem : queryItems) {
            BaseStateQueryItemDTO item = new BaseStateQueryItemDTO();
            Map<com.sankuai.spt.statequery.api.enums.IdTypeEnum, Long> subjectIdMap = Maps.newHashMap();
            subjectIdMap.put(com.sankuai.spt.statequery.api.enums.IdTypeEnum.SHOP_ID, queryItem.getMtShopId());
            item.setSubjectId(subjectIdMap);
            item.setDay(queryItem.getDay());
            items.add(item);
        }
        req.setQueryItems(items);
        req.setConditionMap(condition);
        return req;
    }

    private ShopTimeStateQueryItem convert2ShopTimeStateQueryItem(BaseStateDTO baseStateDTO) {
        if (baseStateDTO == null || baseStateDTO.getSubject() == null || StringUtils.isBlank(baseStateDTO.getDay())) {
            return null;
        }
        StateSubjectDTO subject = baseStateDTO.getSubject();
        if (MapUtils.isEmpty(subject.getSubjectId())) {
            return null;
        }
        Long mtShopId = subject.getSubjectId().get(com.sankuai.spt.statequery.api.enums.IdTypeEnum.SHOP_ID);
        if (mtShopId == null) {
            return null;
        }
        ShopTimeStateQueryItem queryItem = new ShopTimeStateQueryItem();
        queryItem.setMtShopId(mtShopId);
        queryItem.setDay(baseStateDTO.getDay());
        return queryItem;
    }

    @Override
    public Map<ProductTimeStateQueryItem, BaseStateDTO> batchQueryProductCanBookingState(List<ProductTimeStateQueryItem> queryItems) {
        if (CollectionUtils.isEmpty(queryItems)) {
            return null;
        }
        // 上限1次查20个
        List<List<ProductTimeStateQueryItem>> queryItemPartitions = Lists.partition(queryItems, 20);
        List<CompletableFuture<Map<ProductTimeStateQueryItem, BaseStateDTO>>> futures = Lists.newArrayList();
        for (List<ProductTimeStateQueryItem> partition : queryItemPartitions) {
            CompletableFuture<Map<ProductTimeStateQueryItem, BaseStateDTO>> future = CompletableFuture.supplyAsync(() -> queryProductCanBookingStateLimit(partition, null), dealThemePool.getExecutor())
                    .exceptionally(e -> {
                        log.error("batchQueryProductCanBookingState exception, queryItems:{}", JSON.toJSONString(queryItems), e);
                        return null;
                    });
            futures.add(future);
        }
        return mergeAsyncResults(futures);
    }

    private Map<ProductTimeStateQueryItem, BaseStateDTO> queryProductCanBookingStateLimit(List<ProductTimeStateQueryItem> queryItems, Map<BaseStateQueryConditionKeyEnum, String> condition) {
        QueryBaseStateRequest req = buildProductQueryBaseStateReq(queryItems, condition);
        QueryBaseStateResponse resp = baseStateQueryService.queryBaseState(req);
        log.info("[ProductAclService] 查询商品库存 req={}, resp={}", JSON.toJSONString(req), JSON.toJSONString(resp));
        if (resp == null || resp.getData() == null || CollectionUtils.isEmpty(resp.getData().getBaseStates())) {
            return null;
        }
        List<BaseStateDTO> baseStateList = resp.getData().getBaseStates();
        return baseStateList.stream().filter(baseState -> convert2ProductTimeStateQueryItem(baseState) != null)
                .collect(Collectors.toMap(baseState -> convert2ProductTimeStateQueryItem(baseState), Function.identity(), (a, b) -> a));
    }

    private QueryBaseStateRequest buildProductQueryBaseStateReq(List<ProductTimeStateQueryItem> queryItems, Map<BaseStateQueryConditionKeyEnum, String> condition) {
        QueryBaseStateRequest req = new QueryBaseStateRequest();
        req.setQuerySubjectType(SubjectTypeEnum.PRODUCT_SHOP);
        List<BaseStateQueryItemDTO> items = Lists.newArrayList();
        for (ProductTimeStateQueryItem queryItem : queryItems) {
            BaseStateQueryItemDTO item = new BaseStateQueryItemDTO();
            Map<com.sankuai.spt.statequery.api.enums.IdTypeEnum, Long> subjectIdMap = Maps.newHashMap();
            subjectIdMap.put(com.sankuai.spt.statequery.api.enums.IdTypeEnum.PRODUCT_ID, queryItem.getMtProductId());
            subjectIdMap.put(com.sankuai.spt.statequery.api.enums.IdTypeEnum.SHOP_ID, queryItem.getMtShopId());
            item.setSubjectId(subjectIdMap);
            item.setDay(queryItem.getDay());
            items.add(item);
        }
        req.setQueryItems(items);
        req.setConditionMap(condition);
        return req;
    }

    private ProductTimeStateQueryItem convert2ProductTimeStateQueryItem(BaseStateDTO baseStateDTO) {
        if (baseStateDTO == null || baseStateDTO.getSubject() == null || StringUtils.isBlank(baseStateDTO.getDay())) {
            return null;
        }
        StateSubjectDTO subject = baseStateDTO.getSubject();
        if (MapUtils.isEmpty(subject.getSubjectId())) {
            return null;
        }
        Long mtProductId = subject.getSubjectId().get(com.sankuai.spt.statequery.api.enums.IdTypeEnum.PRODUCT_ID);
        Long mtShopId = subject.getSubjectId().get(com.sankuai.spt.statequery.api.enums.IdTypeEnum.SHOP_ID);
        if (mtProductId == null || mtShopId == null) {
            return null;
        }
        ProductTimeStateQueryItem queryItem = new ProductTimeStateQueryItem();
        queryItem.setMtProductId(mtProductId);
        queryItem.setMtShopId(mtShopId);
        queryItem.setDay(baseStateDTO.getDay());
        return queryItem;
    }

    private <T, E> Map<T, E> mergeAsyncResults(List<CompletableFuture<Map<T, E>>> futures) {
        CompletableFuture<Map<T, E>> allResult = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).thenApply(v -> {
            Map<T, E> result = Maps.newHashMap();
            for (CompletableFuture<Map<T, E>> future : futures) {
                if (future == null) {
                    continue;
                }
                Map<T, E> map = future.join();
                if (MapUtils.isNotEmpty(map)) {
                    result.putAll(map);
                }
            }
            return result;
        });
        return allResult == null ? null : allResult.join();
    }

    @Override
    public boolean isSpecialDealGroup(DealGroupDTO dealGroupDTO) {
        // 1.非正常货架链路可卖的团购（排除渠道单）
        if (dealGroupDTO.getBasic().getStatus() != com.sankuai.general.product.query.center.client.enums.DealGroupStatusEnum.VISIBLE_ONLINE.getCode()) {
            return true;
        }

        // 2.不可以正常到店核销的团购（排除配送团购、需要补差价商品）
        if (isDeliveryDealGroup(dealGroupDTO) || isNotFullPaymentDealGroup(dealGroupDTO)) {
            return true;
        }

        // 3.多规格（SKU）团购
        boolean hasMultiSku = Optional.ofNullable(dealGroupDTO.getDeals())
                .filter(CollectionUtils::isNotEmpty)
                .map(deals -> deals.stream().filter(e -> e.getBasic() != null && Objects.equals(e.getBasic().getStatus(), 1)).count() != 1)
                .orElse(false);
        if (hasMultiSku) {
            return true;
        }

        // 4.三方直连团购（三方券）
        boolean hasThirdPartyId = Optional.ofNullable(dealGroupDTO.getDeals())
                .filter(CollectionUtils::isNotEmpty)
                .map(deals -> deals.stream().anyMatch(e -> e.getBasic() != null && e.getBasic().getThirdPartyId() != null && e.getBasic().getThirdPartyId() > 0))
                .orElse(false);
        if (hasThirdPartyId) {
            return true;
        }

        return false;
    }

    private boolean isDeliveryDealGroup(DealGroupDTO dealGroupDTO) {
        return Optional.ofNullable(dealGroupDTO.getDeals())
                .filter(CollectionUtils::isNotEmpty)
                .map(deals -> deals.stream().anyMatch(deal -> deal.getDealDelivery() != null
                        && Boolean.TRUE.equals(deal.getDealDelivery().getSupportDelivery())))
                .orElse(false);
    }

    private boolean isNotFullPaymentDealGroup(DealGroupDTO dealGroupDTO) {
        int payMethod = Optional.ofNullable(dealGroupDTO)
                .map(DealGroupDTO::getAttrs)
                .orElse(new ArrayList<>())
                .stream()
                .filter(attrDTO -> "pay_method".equalsIgnoreCase(attrDTO.getName()))
                .findFirst()
                .map(AttrDTO::getValue)
                .orElse(Lists.newArrayList())
                .stream()
                .findFirst()
                .map(NumberUtils::toInt)
                .orElse(0);
        return payMethod != 0;
    }

}
