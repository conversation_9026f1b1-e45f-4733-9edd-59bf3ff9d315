package com.sankuai.dzim.pilot.process.search.data;

import lombok.Data;

/**
 * <AUTHOR> wangyonghao02
 * @version : 0.1
 * @since : 2023/8/31
 */
@Data
public class PriceProtectionInfoM {
    /**
     * 业务skuId
     */
    private Integer skuId;

    /**
     * 平台skuId
     */
    private Integer platformSkuId;

    /**
     * 价保标签是否有效
     */
    private boolean valid;

    /**
     * 下单后X天有效，如果为null则不需要展示具体天数
     */
    private Integer validityDays;

    /**
     * "是否在买贵必赔活动中。只要命中任意一活动就在活动中"
     */
    private boolean hasValidGuaranteeActivity;
}
