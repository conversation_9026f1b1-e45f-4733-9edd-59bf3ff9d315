package com.sankuai.dzim.pilot.gateway.mq.consumer;

import com.dianping.vc.mq.client.api.annotation.Consumer;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.process.aibook.AIBookDispatchProcessService;
import com.sankuai.dzim.pilot.process.aibook.data.AIBookCallAgainDelayData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2025/1/10 14:57
 */

@Slf4j
@Component
public class AIBookCallAgainDelayConsumer {

    @Resource
    private AIBookDispatchProcessService aiBookDispatchProcessService;

    @Consumer(nameSpace = "daozong",
            appKey = "com.sankuai.mim.pilot",
            group = "pilot.ai.book.call.again.consumer",
            topic = "pilot.ai.book.call.again.delay.topic"
    )
    public ConsumeStatus recvMessage(String mafkaMessage) {
        AIBookCallAgainDelayData data = JsonCodec.decode(mafkaMessage, AIBookCallAgainDelayData.class);
        if (data == null) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        boolean isSuccess = aiBookDispatchProcessService.processCallAgain(data);
        if (!isSuccess) {
            LogUtils.logFailLog(log, TagContext.builder().action("AIBookCallAgainDelayConsumer").build(),
                    WarnMessage.build("AIBookCallAgainDelayConsumer", "重拨延时处理失败", ""),
                    data, false);
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
