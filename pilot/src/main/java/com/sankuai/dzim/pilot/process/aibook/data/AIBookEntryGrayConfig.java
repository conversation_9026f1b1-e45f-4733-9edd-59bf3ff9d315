package com.sankuai.dzim.pilot.process.aibook.data;

import com.sankuai.dzim.pilot.api.enums.assistant.PlatformEnum;
import com.sankuai.dzim.pilot.utils.DateUtils;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
public class AIBookEntryGrayConfig {

    /**
     * 全局开关
     */
    boolean globalSwitch;

    /**
     * 灰度日期
     */
    List<Map<String, String>> grayDateList;

    /**
     * 灰度美团城市
     */
    List<Integer> grayMtCityIds;

    /**
     * 灰度点评城市
     */
    List<Integer> grayDpCityIds;

    /**
     * 商家权益-产品分类id
     */
    List<Integer> productClassifyIds;

    public boolean isInGrayDate() {
        if (CollectionUtils.isEmpty(grayDateList)) {
            return false;
        }

        Date now = new Date();
        return grayDateList.stream()
                .anyMatch(grayDateMap -> isDateInRange(now, grayDateMap));
    }

    private boolean isDateInRange(Date date, Map<String, String> grayDateMap) {
        String beginDateStr = MapUtils.getString(grayDateMap, "beginDate");
        String endDateStr = MapUtils.getString(grayDateMap, "endDate");

        Date beginDate = DateUtils.getStartOfDay(beginDateStr);
        Date endDate = DateUtils.getStartOfNextDay(endDateStr);
        if (beginDate == null || endDate == null) {
            return false;
        }

        return !date.before(beginDate) && date.before(endDate);
    }

    public boolean isInGrayCity(int cityId, int platform) {
        if (platform == PlatformEnum.DP.getType()) {
            return CollectionUtils.isEmpty(grayDpCityIds) || grayDpCityIds.contains(cityId);
        }

        if (platform == PlatformEnum.MT.getType()) {
            return CollectionUtils.isEmpty(grayMtCityIds) || grayMtCityIds.contains(cityId);
        }
        return false;
    }

}
