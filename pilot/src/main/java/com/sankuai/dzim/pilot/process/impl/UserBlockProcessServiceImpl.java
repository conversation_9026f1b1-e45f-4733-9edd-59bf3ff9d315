package com.sankuai.dzim.pilot.process.impl;

import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.sankuai.dzim.pilot.dal.entity.pilot.BlockListEntity;

import com.sankuai.dzim.pilot.domain.RiskUserBlockDomainService;
import com.sankuai.dzim.pilot.enums.BlockStatusEnum;
import com.sankuai.dzim.pilot.enums.CheckQualityReasonEnum;
import com.sankuai.dzim.pilot.gateway.enums.BlockTypeEnum;
import com.sankuai.dzim.pilot.gateway.mq.data.BlockStrategyConstant;
import com.sankuai.dzim.pilot.gateway.mq.data.RiskBlockMessageData;
import com.sankuai.dzim.pilot.gateway.mq.data.RiskUnBlockMessageData;
import com.sankuai.dzim.pilot.process.LLMQualityCheckProcessService;
import com.sankuai.dzim.pilot.process.data.BlockStrategyConfig;
import com.sankuai.dzim.pilot.process.strategy.RiskUserBlockStrategy;
import com.sankuai.dzim.pilot.process.UserBlockProcessService;
import com.sankuai.dzim.pilot.utils.LionConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class UserBlockProcessServiceImpl implements UserBlockProcessService {
    @Autowired
    private RiskUserBlockDomainService riskUserBlockDomainService;

    @Autowired
    private LLMQualityCheckProcessService llmQualityCheckProcessService;

    @Resource(name = "redisClient")
    private RedisStoreClient redisStoreClient;

    @Autowired
    private LionConfigUtil lionConfigUtil;

    @Autowired
    private List<RiskUserBlockStrategy> riskUserBlockStrategyList;

    @Override
    public boolean batchBlockByUserIds(List<String> userIds, Integer assistantType, Integer blockStatus) {
        BlockStatusEnum blockEnumByStatus = BlockStatusEnum.getBlockEnumByStatus(blockStatus);
        if (blockEnumByStatus == null) {
            return false;
        }
        Boolean isSuccess = riskUserBlockDomainService.batchBlockRiskUsers(userIds, BlockTypeEnum.MANUALLY_STRATEGY.getBlockStrategy(),
                assistantType, BlockTypeEnum.MANUALLY_STRATEGY.getVal(),
                blockEnumByStatus.getStatus());
        return isSuccess;
    }

    @Override
    public boolean batchUnBlockByUserIds(List<String> userIds, Integer assistantType) {
        // 人工解锁后，把所有的缓存全删了
        for (RiskUserBlockStrategy riskUserBlockStrategy : riskUserBlockStrategyList) {
            for (String userId : userIds) {
                StoreKey storeKey = riskUserBlockStrategy.buildKey(assistantType, userId);
                redisStoreClient.delete(storeKey);
            }
        }
        riskUserBlockDomainService.batchUnBlockRiskUsers(userIds, assistantType);
        return true;
    }

    @Override
    public void execBlockStrategy(RiskBlockMessageData riskMsg) {
        List<BlockStrategyConfig> blockStrategy = getBlockStrategy(riskMsg.getReq().getAssistantType());
        if (CollectionUtils.isEmpty(blockStrategy)) {
            return;
        }
        // 遍历策略
        for (BlockStrategyConfig strategyConfig : blockStrategy) {
            BlockTypeEnum blockTypeEnum = BlockTypeEnum.getByName(strategyConfig.getBlockStrategy());
            // 大模型拉黑等质检消息消费结束后单独执行
            if (blockTypeEnum == BlockTypeEnum.MANUALLY_STRATEGY || blockTypeEnum == BlockTypeEnum.LLMBLOCK_STRATEGY) {
                continue;
            }
            for (RiskUserBlockStrategy riskUserBlockStrategy : riskUserBlockStrategyList) {
                if (riskUserBlockStrategy.accept(strategyConfig.getBlockStrategy())) {
                    riskUserBlockStrategy.execute(strategyConfig, riskMsg);
                }
            }
        }
    }

    @Override
    public void execUnBlock(RiskUnBlockMessageData riskUnBlockMessageData) {
        String strategy = riskUnBlockMessageData.getStrategy();

        BlockTypeEnum blockTypeEnum = BlockTypeEnum.getByName(strategy);
        if (blockTypeEnum == null) {
            return;
        }

        RiskUserBlockStrategy bean = null;
        for (RiskUserBlockStrategy riskUserBlockStrategy : riskUserBlockStrategyList) {
            if (riskUserBlockStrategy.accept(strategy)) {
                bean = riskUserBlockStrategy;
            }
        }
        if (bean == null) {
            return;
        }
        StoreKey storeKey = bean.buildKey(riskUnBlockMessageData.getAssistantType(), riskUnBlockMessageData.getUserId());

        // 解禁之前删除缓存
        redisStoreClient.delete(storeKey);
        // 修改拉黑表
        BlockListEntity blockListEntity = riskUserBlockDomainService.selectUserByIdAndAssistantType(riskUnBlockMessageData.getUserId(), riskUnBlockMessageData.getAssistantType());

        blockListEntity.setBlockedStatus(BlockStatusEnum.NORMAL.getStatus());
        riskUserBlockDomainService.update(blockListEntity);
    }


    @Override
    public void blockUser(StoreKey storeKey, BlockStrategyConfig strategyConfig, RiskBlockMessageData riskMsg, BlockTypeEnum blockTypeEnum) {
        // 检查是否llm拉黑
        if(blockTypeEnum == BlockTypeEnum.LLMBLOCK_STRATEGY){
            strategyConfig = getLLMBlockConfig(strategyConfig, riskMsg, blockTypeEnum);
            storeKey = buildKey(riskMsg.getReq().getAssistantType(), riskMsg.getReq().getImUserId());
        }

        if(storeKey == null || strategyConfig == null){
            return;
        }
        // 不存在新建key，存在redis+1
        increaseAuditCnt(storeKey, strategyConfig.getMinimunTimePeriod());
        // 超过阈值 且历史拉黑次数+1超过阈值永久拉黑
        BlockListEntity blockListEntity = riskUserBlockDomainService.selectUserByIdAndAssistantType(riskMsg.getReq().getImUserId(),
                riskMsg.getReq().getAssistantType());

        long cnt = getRedisValue(storeKey);
        int blockedCnt = blockListEntity == null ? 0 : blockListEntity.getBlockedCnt();
        if (riskUserBlockDomainService.checkPermanentlyBlock(cnt, strategyConfig, blockedCnt, blockListEntity, riskMsg, blockTypeEnum.getVal())) {
            return;
        }
        // 超过阈值拉黑 发送延时解除mq
        riskUserBlockDomainService.checkBlock(cnt, blockedCnt, strategyConfig, blockListEntity, riskMsg, blockTypeEnum.getVal());
    }

    public BlockStrategyConfig getLLMBlockConfig(BlockStrategyConfig strategyConfig, RiskBlockMessageData riskMsg, BlockTypeEnum blockTypeEnum) {
        if(strategyConfig != null){
            return strategyConfig;
        }
        List<BlockStrategyConfig> blockStrategy = getBlockStrategy(riskMsg.getReq().getAssistantType());
        if (CollectionUtils.isEmpty(blockStrategy) || blockTypeEnum != BlockTypeEnum.LLMBLOCK_STRATEGY || riskMsg.getLlmResult() == null) {
            return null;
        }
        for (BlockStrategyConfig blockStrategyConfig : blockStrategy) {
            if (blockStrategyConfig.getBlockStrategy().equals(BlockTypeEnum.LLMBLOCK_STRATEGY.getBlockStrategy())) {
                strategyConfig = blockStrategyConfig;
            }
        }
        if (strategyConfig == null) {
            return null;
        }

        if (riskMsg.getLlmResult() == CheckQualityReasonEnum.PROMPT_ERROR.getVal() || riskMsg.getLlmResult() == CheckQualityReasonEnum.YELLOW_GAMBLING_DRUG_POLITICS.getVal()){
            return strategyConfig;
        }
        return null;
    }

    public StoreKey buildKey(Integer assistantType, String userId) {
        return new StoreKey(BlockStrategyConstant.RISK_USER_BLOCK_KEY, BlockTypeEnum.LLMBLOCK_STRATEGY.getBlockStrategy(), assistantType, userId);
    }

    public List<BlockStrategyConfig> getBlockStrategy(Integer assistantType) {
        Map<String, List<BlockStrategyConfig>> riskUserAssistantConfig = lionConfigUtil.getRiskUserAssistantConfig();
        List<BlockStrategyConfig> blockStrategyConfig = riskUserAssistantConfig.get(String.valueOf(assistantType));
        if (CollectionUtils.isEmpty(blockStrategyConfig)) {
            return null;
        }
        return blockStrategyConfig;
    }

    public void increaseAuditCnt(StoreKey storeKey, Integer minimunTimePeriod) {
        redisStoreClient.incrBy(storeKey, 1, minimunTimePeriod);
    }

    public long getRedisValue(StoreKey storeKey) {
        long cnt = redisStoreClient.get(storeKey);
        return cnt;
    }
}
