package com.sankuai.dzim.pilot.gateway.api.aibook.data;

import com.sankuai.dzim.pilot.process.aibook.data.DealVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DealResultVO implements Serializable {

    private List<DealVO> dealList;
}
