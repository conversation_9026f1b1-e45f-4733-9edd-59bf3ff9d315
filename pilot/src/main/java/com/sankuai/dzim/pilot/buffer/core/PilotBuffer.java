package com.sankuai.dzim.pilot.buffer.core;

import com.google.common.collect.Lists;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.api.enums.assistant.MessageTypeEnum;
import com.sankuai.dzim.pilot.buffer.enums.BufferItemTypeEnum;
import com.sankuai.dzim.pilot.buffer.enums.StreamEventDataTypeEnum;
import com.sankuai.dzim.pilot.buffer.stream.build.ContentBuilder;
import com.sankuai.dzim.pilot.buffer.stream.vo.*;
import com.sankuai.dzim.pilot.utils.context.RequestContext;
import com.sankuai.dzim.pilot.utils.context.RequestContextConstants;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/7/29 15:57
 */
@Slf4j
@Data
public class PilotBuffer {

    /**
     * 已写入缓冲池
     */
    private List<PilotBufferItemDO> written = new CopyOnWriteArrayList<>();

    /**
     * 写入数据转换后的
     */
    private List<StreamEventVO> converted = new CopyOnWriteArrayList<>();

    /**
     * 消费任务，用于判断任务是否结束
     */
    Future<Boolean> taskFuture;

    /**
     * sse链接是否关闭
     */
    private boolean sseClose = false;


    private volatile int messageType = MessageTypeEnum.CARD.value;


    public boolean writeBufferMessageType(int messageType) {
        this.messageType = messageType;
        return true;
    }

    public boolean writeBufferData(List<PilotBufferItemDO> bufferItems) {
        //从当前ThreadLocal 中获取buffer
        PilotBuffer buffer = RequestContext.getAttribute(RequestContextConstants.PILOT_BUFFER);
        if (buffer == null) {
            LogUtils.logFailLog(log, TagContext.builder().action("writeBufferData").build(),
                    new WarnMessage("writeBufferDataError", "buffer为空写入失败", ""), bufferItems, null);
            return false;
        }
        synchronized (buffer) {
            List<PilotBufferItemDO> written = buffer.getWritten();
            for (PilotBufferItemDO item : bufferItems) {
                if (item == null || BufferItemTypeEnum.getByType(item.getType()) == null) {
                    continue;
                }
                written.add(item);
            }
        }
        return true;
    }

    public BufferMergedVO getBufferMerged() {
        if (taskFuture == null) {
            return BufferMergedVO.builder().messageType(messageType).streamEventVOs(Lists.newArrayList()).build();
        }
        //等待消费任务完成
        try {
            taskFuture.get();
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("getBufferMerged").build(),
                    new WarnMessage("getBufferMergedException", "buffer合并内容异常", ""), null, e);
        }
        //合并转换后的数据
        if (CollectionUtils.isEmpty(converted)) {
            return BufferMergedVO.builder().messageType(messageType).streamEventVOs(Lists.newArrayList()).build();
        }
        //所有转换后的包分组
        Map<String, List<StreamEventVO>> eventType2VO = converted.stream().filter(eventVO -> eventVO.getData() != null).collect(Collectors.groupingBy(eventVO -> eventVO.getData().getEvent()));

        //状态包取最后一个
        List<StreamEventVO> statusEventVOs = eventType2VO.get(StreamEventDataTypeEnum.LOADING_STATUS.getType());
        StreamEventVO lastStatusVO = CollectionUtils.isEmpty(statusEventVOs) ? null :
                statusEventVOs.stream().max(Comparator.comparingInt(StreamEventVO::getIndex)).orElse(null);

        //推理包合并
        List<StreamEventVO> reasonTextEventVOs = Optional.ofNullable(eventType2VO.get(StreamEventDataTypeEnum.REASON_TEXT.getType())).orElse(Lists.newArrayList());
        StreamEventVO mergedReasonText = mergeReason(reasonTextEventVOs);

        //思考过程包合并
        List<StreamEventVO> thinkProcessEventVOs = Optional.ofNullable(eventType2VO.get(StreamEventDataTypeEnum.THINK_PROCESS.getType())).orElse(Lists.newArrayList());
        StreamEventVO mergeThinkProcess = mergeThinkProcess(thinkProcessEventVOs);

        //正文包合并
        List<StreamEventVO> mainTextEventVOs = Optional.ofNullable(eventType2VO.get(StreamEventDataTypeEnum.MAIN_TEXT.getType())).orElse(Lists.newArrayList());
        StreamEventVO mergedMainText = mergeMainText(mainTextEventVOs);

        // 执行合并操作
        List<StreamEventVO> result = Lists.newArrayList();
        paddingResult(result, reasonTextEventVOs, mergedReasonText, thinkProcessEventVOs, mergeThinkProcess, mainTextEventVOs, mergedMainText, lastStatusVO);
        return BufferMergedVO.builder().messageType(messageType).streamEventVOs(result).build();
    }

    private void paddingResult(List<StreamEventVO> result, List<StreamEventVO> reasonTextEventVOs, StreamEventVO mergedReasonText,
                               List<StreamEventVO> thinkProcessEventVOs, StreamEventVO mergeThinkProcess, List<StreamEventVO> mainTextEventVOs, StreamEventVO mergedMainText, StreamEventVO lastStatusVO) {
        if (lastStatusVO != null) {
            result.add(lastStatusVO);
        }
        if (CollectionUtils.isNotEmpty(thinkProcessEventVOs)) {
            result.add(mergeThinkProcess);
        }
        if (CollectionUtils.isNotEmpty(reasonTextEventVOs)) {
            result.add(mergedReasonText);
        }
        if (CollectionUtils.isNotEmpty(mainTextEventVOs)) {
            result.add(mergedMainText);
        }
    }

    private StreamEventVO mergeReason(List<StreamEventVO> reasonTextEventVOs) {
        StreamEventVO mergedReasonText = new StreamEventVO();
        StreamEventDataVO mergedReasonTextData = new StreamEventDataVO();
        mergedReasonText.setType(ContentBuilder.MESSAGE_TYPE);
        mergedReasonText.setData(mergedReasonTextData);
        mergedReasonTextData.setEvent(StreamEventDataTypeEnum.REASON_TEXT.getType());
        mergedReasonTextData.setContent(StringUtils.EMPTY);
        mergedReasonTextData.setCardsData(Lists.newArrayList());
        for (StreamEventVO reasonTextEventVO : reasonTextEventVOs) {
            if (reasonTextEventVO == null || reasonTextEventVO.getData() == null) {
                continue;
            }
            String content = mergedReasonTextData.getContent();
            mergedReasonTextData.setContent(StringUtils.isBlank(content) ? reasonTextEventVO.getData().getContent() :
                    content + reasonTextEventVO.getData().getContent());
            List<StreamEventCardDataVO> cardsDataList = mergedReasonTextData.getCardsData();
            cardsDataList.addAll(Optional.ofNullable(reasonTextEventVO.getData().getCardsData()).orElse(Lists.newArrayList()));
        }
        return mergedReasonText;
    }

    private StreamEventVO mergeThinkProcess(List<StreamEventVO> thinkProcessEventVOs) {
        StreamEventVO mergedThinkProcess = new StreamEventVO();
        StreamEventDataVO mergedThinkProcessData = new StreamEventDataVO();
        mergedThinkProcess.setType(ContentBuilder.MESSAGE_TYPE);
        mergedThinkProcess.setData(mergedThinkProcessData);
        mergedThinkProcessData.setEvent(StreamEventDataTypeEnum.THINK_PROCESS.getType());
        mergedThinkProcessData.setContent(StringUtils.EMPTY);
        mergedThinkProcessData.setCardsData(Lists.newArrayList());
        for (StreamEventVO thinkProcessVO : thinkProcessEventVOs) {
            if (thinkProcessVO == null || thinkProcessVO.getData() == null) {
                continue;
            }
            String content = mergedThinkProcessData.getContent();
            mergedThinkProcessData.setContent(StringUtils.isBlank(content) ? thinkProcessVO.getData().getContent() :
                    content + thinkProcessVO.getData().getContent());
            List<StreamEventCardDataVO> cardsDataList = mergedThinkProcessData.getCardsData();
            cardsDataList.addAll(Optional.ofNullable(thinkProcessVO.getData().getCardsData()).orElse(Lists.newArrayList()));
        }
        return mergedThinkProcess;
    }


    private StreamEventVO mergeMainText(List<StreamEventVO> mainTextEventVOs) {
        StreamEventVO mergedMainText = new StreamEventVO();
        StreamEventDataVO mergedMainTextData = new StreamEventDataVO();
        mergedMainText.setType(ContentBuilder.MESSAGE_TYPE);
        mergedMainText.setData(mergedMainTextData);
        mergedMainTextData.setEvent(StreamEventDataTypeEnum.MAIN_TEXT.getType());
        mergedMainTextData.setContent(StringUtils.EMPTY);
        mergedMainTextData.setResources(Lists.newArrayList());
        mergedMainTextData.setCardsData(Lists.newArrayList());
        for (StreamEventVO mainTextEventVO : mainTextEventVOs) {
            if (mainTextEventVO == null || mainTextEventVO.getData() == null) {
                continue;
            }
            String content = mergedMainTextData.getContent();
            mergedMainTextData.setContent(StringUtils.isBlank(content) ? mainTextEventVO.getData().getContent() :
                    content + mainTextEventVO.getData().getContent());
            List<StreamEventCardDataVO> cardsDataList = mergedMainTextData.getCardsData();
            cardsDataList.addAll(Optional.ofNullable(mainTextEventVO.getData().getCardsData()).orElse(Lists.newArrayList()));
        }
        // BEAM 消息聚合
        for (StreamEventVO mainTextEventVO : mainTextEventVOs) {
            if (mainTextEventVO == null || CollectionUtils.isEmpty(mainTextEventVO.getChoices())) {
                continue;
            }
            String content = StringUtils.defaultString(mergedMainTextData.getContent());
            List<BeamChoiceVO.DeltaResource> resources = mergedMainTextData.getResources();

            content = aggregateContent(mainTextEventVO, content);
            aggregateResources(mainTextEventVO, resources);

            mergedMainTextData.setContent(content);
        }

        return mergedMainText;
    }

    private String aggregateContent(StreamEventVO mainTextEventVO, String currentContent) {
        StringBuilder contentBuilder = new StringBuilder(currentContent);
        for (BeamChoiceVO choiceVO : mainTextEventVO.getChoices()) {
            if (choiceVO.getDelta() != null) {
                contentBuilder.append(org.apache.commons.lang3.StringUtils.defaultString(choiceVO.getDelta().getContent()));
            }
        }
        return contentBuilder.toString();
    }

    private void aggregateResources(StreamEventVO mainTextEventVO, List<BeamChoiceVO.DeltaResource> resources) {
        for (BeamChoiceVO choiceVO : mainTextEventVO.getChoices()) {
            if (choiceVO.getDelta() != null) {
                resources.addAll(org.apache.commons.collections4.CollectionUtils.emptyIfNull(
                        choiceVO.getDelta().getResource_list()
                ));
            }
        }
    }

    public void finishBufferConsume() {
        List<PilotBufferItemDO> finished = Lists.newArrayList();
        PilotBufferItemDO finishedItem = new PilotBufferItemDO();
        finishedItem.setType(BufferItemTypeEnum.FINISH_WRITE.getType());
        finished.add(finishedItem);
        writeBufferData(finished);
    }


}
