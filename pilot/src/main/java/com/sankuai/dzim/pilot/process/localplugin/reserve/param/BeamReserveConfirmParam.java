package com.sankuai.dzim.pilot.process.localplugin.reserve.param;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import com.sankuai.dzim.pilot.process.localplugin.param.Param;
import lombok.Data;

/**
 * @author: z<PERSON><PERSON><PERSON>
 * @date: 2025/5/9
 */
@Data
public class BeamReserveConfirmParam extends Param {
    @JsonPropertyDescription("shopId请从历史消息中获取,取最近的一个门店id，没有可以不需要")
    @JsonProperty(required = false)
    private Long shopId;

    @JsonPropertyDescription("extract product id from context")
    @JsonProperty(required = false)
    private Long productId;

    @JsonPropertyDescription("extract order id from context")
    @JsonProperty(required = false)
    private Long orderId;
}
