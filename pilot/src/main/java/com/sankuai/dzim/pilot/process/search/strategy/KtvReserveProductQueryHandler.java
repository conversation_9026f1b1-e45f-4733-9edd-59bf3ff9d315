package com.sankuai.dzim.pilot.process.search.strategy;

import com.dianping.vc.sdk.lang.NumberUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.dzim.pilot.process.aireservebook.enums.POIIndustryType;
import com.sankuai.dzim.pilot.process.data.ProductDetailData;
import com.sankuai.dzim.pilot.process.data.ProductRetrievalData;
import com.sankuai.dzim.pilot.process.data.ProductStockData;
import com.sankuai.dzim.pilot.process.data.ShopPackageData;
import com.sankuai.dzim.pilot.process.search.data.ProductSaleM;
import com.sankuai.dzim.pilot.process.search.data.ReserveProductM;
import com.sankuai.dzim.pilot.process.search.data.ReserveQueryContext;
import com.sankuai.dzim.pilot.process.search.data.TimeSliceM;
import com.sankuai.dzim.pilot.process.search.enums.IndustryBackendCategoryEnum;
import com.sankuai.dzim.pilot.process.search.enums.PlatformCategoryEnum;
import com.sankuai.dzim.pilot.process.search.strategy.padding.ReserveProductPaddingFactory;
import com.sankuai.dzim.pilot.process.search.strategy.padding.ReserveProductPaddingHandler;
import com.sankuai.dzim.pilot.process.search.strategy.recall.ReserveProductRecallFactory;
import com.sankuai.dzim.pilot.process.search.strategy.recall.ReserveProductRecallHandler;
import com.sankuai.dzim.pilot.process.search.utils.GenericSpaceUtil;
import com.sankuai.dztheme.generalproduct.enums.GeneralProductAttrEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.joda.time.DateTime;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class KtvReserveProductQueryHandler implements ReserveProductQueryHandler {

    private static final List<Integer> KTV_CATEGORY_IDS = IndustryBackendCategoryEnum.KTV.getCategoryIds();
    private final String KTV_PLAN_ID = "10101115";
    private final String GROUP_PRODUCTS = "groupProducts";

    private final String includeSingleOnly = "纯欢唱，无套餐";

    private final String showMorePKGSaleTips = "可选%d个特惠套餐";

    @Resource
    private ReserveProductRecallFactory reserveProductRecallFactory;

    @Resource
    private ReserveProductPaddingFactory reserveProductPaddingFactory;

    @Override
    public boolean match(ReserveQueryContext queryContext) {
        if (queryContext == null || queryContext.getShopBackCategoryId() <= 0) {
            return false;
        }
        return KTV_CATEGORY_IDS.contains(queryContext.getShopBackCategoryId());
    }

    @Override
    public List<ProductRetrievalData> execute(ReserveQueryContext queryContext) {

        // 加入行业个性化上下文参数
        ReserveQueryContext ctx = fillSpecialContext(queryContext);

        // 根据poiId召回门店所有在线预订商品id
        List<ReserveProductM> recallProducts = queryShopProductIds(ctx);

        // 根据商品id列表填充商品信息
        List<ReserveProductM> paddingProducts = paddingProductInfo(recallProducts, ctx);

        // 商品分组聚合成展示商品
        List<ReserveProductM> groupProducts = reorganization(paddingProducts);

        // 构建门店预订商品返回结果
        return buildProductRetrievalDataList(queryContext, groupProducts);
    }

    private List<ReserveProductM> paddingProductInfo(List<ReserveProductM> recallProducts, ReserveQueryContext ctx) {
        ReserveProductPaddingHandler handler = reserveProductPaddingFactory.getHandler(ctx);
        if (handler == null) {
            return Lists.newArrayList();
        }
        return handler.padding(recallProducts, ctx);
    }

    /**
     * 对门店所有在线商品进行分组，分组要素：时段周几+时段开始+结束时间+包型
     */
    private List<ReserveProductM> reorganization(List<ReserveProductM> paddingProducts) {
        if (CollectionUtils.isEmpty(paddingProducts)) {
            return Lists.newArrayList();
        }

        // 按照指定属性进行分组
        return paddingProducts.stream().collect(Collectors.groupingBy(product -> {
            String roomName = product.getAttr(GeneralProductAttrEnum.ATTR_RESOURCE_ROOM_NAME.getKey());
            String dayOfWeek = product.getAttr(GeneralProductAttrEnum.ATTR_RESOURCE_PERIOD_DAY_OF_WEEK.getKey());
            String beginTime = product.getAttr(GeneralProductAttrEnum.ATTR_RESOURCE_PERIOD_BEGIN_TIME.getKey());
            String endTime = product.getAttr(GeneralProductAttrEnum.ATTR_RESOURCE_PERIOD_END_TIME.getKey());
            String nextDay = product.getAttr(GeneralProductAttrEnum.ATTR_RESOURCE_PERIOD_NEXT_DAY.getKey());
            return roomName + "_" + dayOfWeek + "_" + beginTime + "_" + endTime + "_" + nextDay;
        })).values().stream().map(this::mergeProduct).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private ReserveProductM mergeProduct(List<ReserveProductM> groupProducts) {
        if (CollectionUtils.isEmpty(groupProducts)) {
            return null;
        }

        // 找到价格最低的商品
        ReserveProductM minPriceProduct = groupProducts.stream().min((p1, p2) -> {
            if (StringUtils.isEmpty(p1.getSalePriceTag()) || StringUtils.isEmpty(p2.getSalePriceTag())) {
                return 0;
            }
            BigDecimal price1 = new BigDecimal(p1.getSalePriceTag());
            BigDecimal price2 = new BigDecimal(p2.getSalePriceTag());
            return price1.compareTo(price2);
        }).orElse(null);

        if (minPriceProduct == null) {
            return null;
        }

        List<String> salePrices = groupProducts.stream().map(ReserveProductM::getSalePriceTag)
                .filter(StringUtils::isNotBlank).sorted((p1, p2) -> {
                    BigDecimal price1 = new BigDecimal(p1);
                    BigDecimal price2 = new BigDecimal(p2);
                    return price1.compareTo(price2);
                }).collect(Collectors.toList());

        String salePrice = salePrices.size() > 1 ? salePrices.get(0) + "起" : salePrices.get(0);
        minPriceProduct.setSalePriceTag(salePrice);

        // 新建商品并复制最低价格商品的数据
        ReserveProductM mergedProduct = copyProduct(minPriceProduct);

        // 将groupProducts放入新建商品的属性中
        mergedProduct.setObjAttr("groupProducts", groupProducts);

        return mergedProduct;
    }

    public ReserveProductM copyProduct(ReserveProductM sourceProduct) {
        if (sourceProduct == null) {
            return null;
        }

        ReserveProductM targetProduct = new ReserveProductM();
        targetProduct.setPlatformProductId(sourceProduct.getPlatformProductId());
        targetProduct.setSalePriceTag(sourceProduct.getSalePriceTag());
        return targetProduct;
    }

    private List<ProductRetrievalData> buildProductRetrievalDataList(ReserveQueryContext queryContext,
            List<ReserveProductM> paddingProducts) {
        if (CollectionUtils.isEmpty(paddingProducts)) {
            return Lists.newArrayList();
        }
        return paddingProducts.stream().map(reserveProductM -> buildProductRetrievalData(queryContext, reserveProductM))
                .collect(Collectors.toList());
    }

    private ProductRetrievalData buildProductRetrievalData(ReserveQueryContext queryContext,
            ReserveProductM reserveProductM) {
        ProductRetrievalData productRetrievalData = new ProductRetrievalData();
        productRetrievalData.setProductType(2);
        productRetrievalData.setCategoryId((long)POIIndustryType.KTV.getSecondBackCategoryId());
        productRetrievalData.setMtProductId(reserveProductM.getPlatformProductId());
        productRetrievalData.setRelatedMtProductIds(buildRelatedMtProductIds(reserveProductM));
        productRetrievalData.setTitle(buildTitle(reserveProductM));
        productRetrievalData.setSubTitles(buildSubTitles(reserveProductM));
        productRetrievalData.setSalesNum(buildSalesNum(reserveProductM));
        productRetrievalData.setSalePrice(reserveProductM.getSalePriceTag());
        productRetrievalData.setStocks(buildStocks(reserveProductM));
        productRetrievalData.setProductDetails(buildProductDetails(reserveProductM));
        return productRetrievalData;
    }

    private ProductDetailData buildProductDetails(ReserveProductM reserveProductM) {
        ProductDetailData productDetailData = new ProductDetailData();

        List<ShopPackageData> shopPackageDataList = Lists.newArrayList();
        List<ReserveProductM> allGroupProducts = getAllGroupProduct(reserveProductM);

        Map<String, Object> packageNameToPackageContent = getStringShopPackageDataMap(shopPackageDataList,
                allGroupProducts);
        Map<String, Object> saleInfoMap = buildSaleInfo(allGroupProducts);

        productDetailData.setPackageInfo(packageNameToPackageContent);
        productDetailData.setSaleInfo(saleInfoMap);
        return productDetailData;
    }

    private Map<String, Object> buildSaleInfo(List<ReserveProductM> allGroupProducts) {
        if (CollectionUtils.isEmpty(allGroupProducts)) {
            return Maps.newHashMap();
        }
        ReserveProductM firstProductM = allGroupProducts.get(0);
        Map<String, Object> saleInfoMap = Maps.newHashMap();
        int minPersonNum = NumberUtils
                .toInt(firstProductM.getAttr(GeneralProductAttrEnum.ATTR_RESOURCE_ROOM_MIN_CAPACITY.getKey()));
        int maxPersonNum = NumberUtils
                .toInt(firstProductM.getAttr(GeneralProductAttrEnum.ATTR_RESOURCE_ROOM_CAPACITY.getKey()));
        saleInfoMap.put("商品适用人数", minPersonNum + "-" + maxPersonNum + "人");
        return saleInfoMap;
    }

    private Map<String, Object> getStringShopPackageDataMap(List<ShopPackageData> shopPackageDataList,
            List<ReserveProductM> allGroupProducts) {
        for (ReserveProductM allGroupProduct : allGroupProducts) {
            String packageName = allGroupProduct.getAttr(GeneralProductAttrEnum.ATTR_RESOURCE_PACKAGE_NAME.getKey());
            String packageContent = allGroupProduct
                    .getAttr(GeneralProductAttrEnum.ATTR_RESOURCE_PACKAGE_CONTENT.getKey());
            String packageStructContent = allGroupProduct
                    .getAttr(GeneralProductAttrEnum.ATTR_RESOURCE_STRUCT_PACKAGE_CONTENT.getKey());
            ShopPackageData shopPackageData = buildShopPackageContent(packageName, packageContent,
                    packageStructContent);
            shopPackageDataList.add(shopPackageData);
        }
        Map<String, Object> packageNameToPackageContent = shopPackageDataList.stream().filter(Objects::nonNull)
                .collect(Collectors.toMap(ShopPackageData::getPackageName, e -> e, (a, b) -> a));
        return packageNameToPackageContent;
    }

    private ShopPackageData buildShopPackageContent(String packageName, String packageContent,
            String packageStructContent) {
        ShopPackageData shopPackageData = new ShopPackageData();
        shopPackageData.setPackageName(packageName);
        shopPackageData.setPackageContent(
                StringUtils.isNotBlank(packageStructContent) ? packageStructContent : packageContent);
        return shopPackageData;
    }

    private List<Long> buildRelatedMtProductIds(ReserveProductM reserveProductM) {
        List<ReserveProductM> allGroupProduct = getAllGroupProduct(reserveProductM);
        if (CollectionUtils.isEmpty(allGroupProduct)) {
            return null;
        }
        return allGroupProduct.stream().map(ReserveProductM::getPlatformProductId).filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 主商品下任意一个套餐时间片段可订，那主商品在这个套餐就是可订的
     */
    public ProductStockData buildStocks(ReserveProductM reserveProductM) {
        List<ReserveProductM> allGroupProduct = getAllGroupProduct(reserveProductM);
        if (CollectionUtils.isEmpty(allGroupProduct)) {
            return null;
        }
        // 预处理库存
        preHandleStock(reserveProductM, allGroupProduct);

        ProductStockData productStockData = new ProductStockData();

        // 获取所有时间片段
        Map<Long, TimeSliceM> timeSliceMap = allGroupProduct.stream()
                .filter(e -> e.getBaseState() != null && e.getBaseState().getTimeSlices() != null)
                .flatMap(product -> product.getBaseState().getTimeSlices().stream())
                .collect(Collectors.toMap(TimeSliceM::getStartTime, Function.identity(), (a, b) -> {
                    if (a.isAvailable() || b.isAvailable()) {
                        return buildTimeSliceM(a.getStartTime(), true);
                    } else {
                        return buildTimeSliceM(a.getStartTime(), false);
                    }
                }));

        List<TimeSliceM> sortedTimeSlices = timeSliceMap.values().stream()
                .sorted(Comparator.comparing(TimeSliceM::getStartTime)).collect(Collectors.toList());

        productStockData.setTimeSlices(sortedTimeSlices);

        // 设置最早可订时间
        productStockData.setEarliestBookableTime(sortedTimeSlices.stream().filter(TimeSliceM::isAvailable)
                .map(TimeSliceM::getStartTime).findFirst().orElse(null));

        return productStockData;
    }

    private void preHandleStock(ReserveProductM reserveProductM, List<ReserveProductM> allGroupProduct) {
        allGroupProduct.forEach(productM -> {
            if (productM.getBaseState() == null) {
                return;
            }
            if (productM.getBaseState().getTimeSlices() == null) {
                return;
            }
            List<TimeSliceM> timeSlices = productM.getBaseState().getTimeSlices();
            if (CollectionUtils.isEmpty(timeSlices)) {
                return;
            }
            // ktv预订商品的库存开始时间是日期0点，转化成商品上的时段开始时间和结束时间
            convertActualPeriod(reserveProductM, timeSlices);
            productM.getBaseState().setTimeSlices(timeSlices);
        });
    }

    private void convertActualPeriod(ReserveProductM reserveProductM, List<TimeSliceM> timeSlices) {
        Long bookDateStartTime = timeSlices.get(0).getStartTime();
        ReserveProductM firstGroupProduct = getFirstGroupProduct(reserveProductM);
        String periodBeginStr = firstGroupProduct
                .getAttr(GeneralProductAttrEnum.ATTR_RESOURCE_PERIOD_BEGIN_TIME.getKey());
        String periodEndStr = firstGroupProduct.getAttr(GeneralProductAttrEnum.ATTR_RESOURCE_PERIOD_END_TIME.getKey());
        int crossDay = NumberUtils
                .toInt(firstGroupProduct.getAttr(GeneralProductAttrEnum.ATTR_RESOURCE_PERIOD_NEXT_DAY.getKey()));
        Long actualBeginTime = buildActualBeginTime(bookDateStartTime, periodBeginStr, periodEndStr, crossDay);
        Long actualEndTime = buildActualEndTime(bookDateStartTime, periodEndStr, crossDay);

        timeSlices.forEach(e -> {
            e.setStartTime(actualBeginTime);
            e.setEndTime(actualEndTime);
        });
    }

    private Long buildActualBeginTime(Long bookDateStartTime, String periodBeginStr, String periodEndStr,
            int crossDay) {
        if (crossDay == 1 && periodBeginStr.compareTo(periodEndStr) < 0) {
            DateTime dateTime = new DateTime(bookDateStartTime).withTimeAtStartOfDay().plusDays(1);
            return GenericSpaceUtil.combineDateAndTime(dateTime.getMillis(), periodBeginStr).getTime();
        }
        DateTime dateTime = new DateTime(bookDateStartTime).withTimeAtStartOfDay();
        return GenericSpaceUtil.combineDateAndTime(dateTime.getMillis(), periodBeginStr).getTime();
    }

    private Long buildActualEndTime(Long bookDateStartTime, String periodEndStr, int crossDay) {
        if (crossDay == 1) {
            DateTime dateTime = new DateTime(bookDateStartTime).withTimeAtStartOfDay().plusDays(1);
            return GenericSpaceUtil.combineDateAndTime(dateTime.getMillis(), periodEndStr).getTime();
        }
        DateTime dateTime = new DateTime(bookDateStartTime).withTimeAtStartOfDay();
        return GenericSpaceUtil.combineDateAndTime(dateTime.getMillis(), periodEndStr).getTime();
    }

    private TimeSliceM buildTimeSliceM(Long startTime, boolean available) {
        TimeSliceM timeSliceM = new TimeSliceM();
        timeSliceM.setStartTime(startTime);
        timeSliceM.setAvailable(available);
        return timeSliceM;
    }

    /**
     * 取下挂商品的销量和
     */
    private Long buildSalesNum(ReserveProductM reserveProductM) {
        List<ReserveProductM> groupProducts = getAllGroupProduct(reserveProductM);
        if (CollectionUtils.isEmpty(groupProducts)) {
            return null;
        }
        return groupProducts.stream().map(ReserveProductM::getSale).filter(Objects::nonNull).map(ProductSaleM::getSale)
                .mapToLong(Integer::longValue).sum();
    }

    private List<String> buildSubTitles(ReserveProductM reserveProductM) {
        List<ReserveProductM> groupProducts = getAllGroupProduct(reserveProductM);
        if (CollectionUtils.isEmpty(groupProducts)) {
            return null;
        }
        ReserveProductM firstProductM = groupProducts.get(0);

        // 根据套餐数量决定是否展示X个套餐文案
        if ((groupProducts.size() == 1 && isFirstPkgSingOnly(firstProductM))) {
            return Lists.newArrayList(includeSingleOnly);
        } else {
            return Lists.newArrayList(String.format(showMorePKGSaleTips, groupProducts.size()));
        }
    }

    private boolean isFirstPkgSingOnly(ReserveProductM firstProductM) {
        String attr = firstProductM.getAttr(GeneralProductAttrEnum.ATTR_RESOURCE_PACKAGE_ISSINGINGONLY.getKey());
        if (StringUtils.isEmpty(attr)) {
            return false;
        }
        return Boolean.parseBoolean(attr);
    }

    private String buildTitle(ReserveProductM reserveProductM) {
        ReserveProductM firstProductM = getFirstGroupProduct(reserveProductM);
        int singHours = NumberUtils
                .toInt(firstProductM.getAttr(GeneralProductAttrEnum.ATTR_RESOURCE_PACKAGE_DURATION.getKey()));
        int crossDay = NumberUtils
                .toInt(firstProductM.getAttr(GeneralProductAttrEnum.ATTR_RESOURCE_PERIOD_NEXT_DAY.getKey()));
        int minPersonNum = NumberUtils
                .toInt(firstProductM.getAttr(GeneralProductAttrEnum.ATTR_RESOURCE_ROOM_MIN_CAPACITY.getKey()));
        int maxPersonNum = NumberUtils
                .toInt(firstProductM.getAttr(GeneralProductAttrEnum.ATTR_RESOURCE_ROOM_CAPACITY.getKey()));

        String roomName = firstProductM.getAttr(GeneralProductAttrEnum.ATTR_RESOURCE_ROOM_NAME.getKey());

        String periodBeginStr = firstProductM.getAttr(GeneralProductAttrEnum.ATTR_RESOURCE_PERIOD_BEGIN_TIME.getKey());
        String periodEndStr = firstProductM.getAttr(GeneralProductAttrEnum.ATTR_RESOURCE_PERIOD_END_TIME.getKey());
        String actualPeriodBeginStr = formatPeriodWithCrossDay(periodBeginStr, periodEndStr, crossDay);
        String actualPeriodEndStr = getActualPeriodEndStr(crossDay, periodEndStr);
        String titleTemplate = NumberUtils
                .toInt(firstProductM.getAttr(GeneralProductAttrEnum.ATTR_RESOURCE_PERIOD_TYPE.getKey())) == 1
                        ? "%s(%d-%d人)，%s至%s内，任选%d小时" : "%s(%d-%d人)，%s至%s内开唱，唱%d小时";
        return String.format(titleTemplate, roomName, minPersonNum, maxPersonNum, actualPeriodBeginStr,
                actualPeriodEndStr, singHours);
    }

    public static String formatToHourMinute(String periodStr) {
        if (StringUtils.isEmpty(periodStr)) {
            return null;
        }
        // 使用正则表达式提取时分部分
        return periodStr.split(":")[0] + ":" + periodStr.split(":")[1];
    }

    private String getActualPeriodEndStr(int crossDay, String periodEndStr) {
        return crossDay == 1 ? "次日" + periodEndStr : periodEndStr;
    }

    /**
     * 判断开始时间点是否为次日时间点，并加上"次日"前缀
     *
     * @param periodBeginStr 时段开始时间，格式为"HH:mm"
     * @param periodEndStr 时段结束时间，格式为"HH:mm"
     * @param crossDay 是否跨日，1表示跨日
     * @return 加上"次日"前缀的时间点
     */
    public static String formatPeriodWithCrossDay(String periodBeginStr, String periodEndStr, int crossDay) {
        if (crossDay == 1 && periodBeginStr.compareTo(periodEndStr) < 0) {
            return "次日" + formatToHourMinute(periodBeginStr);
        }
        return formatToHourMinute(periodBeginStr);
    }

    private ReserveProductM getFirstGroupProduct(ReserveProductM reserveProductM) {
        if (reserveProductM == null) {
            return null;
        }
        List<ReserveProductM> groupProducts = reserveProductM.getObjAttr(GROUP_PRODUCTS);

        if (CollectionUtils.isEmpty(groupProducts)) {
            return null;
        }
        return groupProducts.get(0);
    }

    private List<ReserveProductM> getAllGroupProduct(ReserveProductM reserveProductM) {
        if (reserveProductM == null) {
            return Lists.newArrayList();
        }
        List<ReserveProductM> groupProducts = reserveProductM.getObjAttr(GROUP_PRODUCTS);

        if (CollectionUtils.isEmpty(groupProducts)) {
            return Lists.newArrayList();
        }
        return groupProducts;
    }

    private ReserveQueryContext fillSpecialContext(ReserveQueryContext queryContext) {
        if (queryContext == null) {
            return null;
        }
        // 泛商品主题方案id
        queryContext.setPlanId(KTV_PLAN_ID);
        queryContext.setPlatformCategoryId(PlatformCategoryEnum.KTV.getCode());
        queryContext.setCustomPaddingParam(buildCustomPaddingParam());
        queryContext.setExtra(buildExtra());
        return queryContext;
    }

    private Map<String, Object> buildExtra() {
        Map<String, Object> extra = Maps.newHashMap();
        extra.put("dealGroupAttrs", Sets.newHashSet("resource_attr_ktv_period_dayOfWeek"));
        return extra;
    }

    private Map<String, Object> buildCustomPaddingParam() {
        Map<String, Object> customPaddingParam = Maps.newHashMap();
        customPaddingParam.put("saleFetcherCode", "PackageSaleOpt");
        customPaddingParam.put("bookTableType", 3);
        return customPaddingParam;
    }

    private List<ReserveProductM> queryShopProductIds(ReserveQueryContext ctx) {
        ReserveProductRecallHandler handler = reserveProductRecallFactory.getHandler(ctx);
        if (handler == null) {
            return Lists.newArrayList();
        }
        return handler.recall(ctx);
    }
}
