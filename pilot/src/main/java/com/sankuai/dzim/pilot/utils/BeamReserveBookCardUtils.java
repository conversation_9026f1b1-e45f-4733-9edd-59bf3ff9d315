package com.sankuai.dzim.pilot.utils;

import com.alibaba.fastjson.JSON;
import com.dianping.dzim.common.enums.ImUserType;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.meituan.nibtp.trade.client.combine.response.AgentCreateOrderResDTO;
import com.sankuai.dzim.pilot.api.data.AIAnswerTypeEnum;
import com.sankuai.dzim.pilot.api.enums.assistant.PlatformEnum;
import com.sankuai.dzim.pilot.buffer.core.PilotBufferItemDO;
import com.sankuai.dzim.pilot.buffer.enums.StreamEventCardTypeEnum;
import com.sankuai.dzim.pilot.buffer.stream.build.ext.data.BeamAICallCreateData;
import com.sankuai.dzim.pilot.buffer.stream.build.ext.data.BeamBookCreateData;
import com.sankuai.dzim.pilot.buffer.utils.BufferUtils;
import com.sankuai.dzim.pilot.chain.data.AIAnswerData;
import com.sankuai.dzim.pilot.process.aiphonecall.data.AIPhoneCallTaskCreateRes;
import com.sankuai.dzim.pilot.process.aireservebook.data.*;
import com.sankuai.dzim.pilot.process.aireservebook.enums.*;
import com.sankuai.dzim.pilot.process.localplugin.data.ReserveReplyAnswerData;
import com.sankuai.dzim.pilot.scene.data.AssistantSceneContext;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.it.iam.common_base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;

import java.util.Map;

import static com.sankuai.dzim.pilot.utils.ReserveInfoProcessUtils.BEAM_FORCE_NEED_DEFAULT_PERSON_NUM;
import static com.sankuai.dzim.pilot.utils.ReserveInfoProcessUtils.BEAM_TRADE_NEED_DEFAULT_PERSON_NUM;

/**
 * <AUTHOR>
 */
@Slf4j
public class BeamReserveBookCardUtils {

    /**
     * 预约确认卡片
     *
     * @param userReserveInfo
     * @param shopReserveContext
     * @return
     */
    public static String generateConfirmCard(UserReserveInfo userReserveInfo, ShopReserveContext shopReserveContext, DealGroupDTO dealGroupDTO, AssistantSceneContext assistantSceneContext, boolean isAfterOrder) {
        //买后约卡片
        if (isAfterOrder) {
            return generateAfterOrderCard(userReserveInfo, shopReserveContext, dealGroupDTO, assistantSceneContext);
        }
        //非 买后约卡片
        return generateOrderCard(userReserveInfo, shopReserveContext, dealGroupDTO, assistantSceneContext);
    }

    private static String generateAfterOrderCard(UserReserveInfo userReserveInfo, ShopReserveContext shopReserveContext, DealGroupDTO dealGroupDTO, AssistantSceneContext context) {
        BeamBookConfirmAfterBuyCard beamBookConfirmAfterBuyCard = buildBookConfirmAfterBuyCard(userReserveInfo, shopReserveContext, dealGroupDTO);
        String cardTag = StreamEventCardTypeEnum.buildCardContent(StreamEventCardTypeEnum.BEAM_BOOK_CONFIRM_AFTER_BUY, JSON.toJSONString(beamBookConfirmAfterBuyCard));
        Map<String, Object> extraInfo = JsonCodec.converseMap(JsonCodec.encodeWithUTF8(beamBookConfirmAfterBuyCard), String.class, Object.class);
        ReserveReplyAnswerData result = ReserveReplyAnswerData.generateCard(cardTag, extraInfo);
        return JSON.toJSONString(result);
    }

    private static String generateOrderCard(UserReserveInfo userReserveInfo, ShopReserveContext shopReserveContext, DealGroupDTO dealGroupDTO, AssistantSceneContext context) {
        BeamBookConfirmCard beamBookConfirmCard = buildBookConfirmCard(userReserveInfo, shopReserveContext, dealGroupDTO);
        String cardTag = StreamEventCardTypeEnum.buildCardContent(StreamEventCardTypeEnum.BEAM_BOOK_CONFIRM, JSON.toJSONString(beamBookConfirmCard));
        Map<String, Object> extraInfo = JsonCodec.converseMap(JsonCodec.encodeWithUTF8(beamBookConfirmCard), String.class, Object.class);
        ReserveReplyAnswerData result = ReserveReplyAnswerData.generateCard(cardTag, extraInfo);
        return JSON.toJSONString(result);
    }

    private static BeamBookConfirmAfterBuyCard buildBookConfirmAfterBuyCard(UserReserveInfo userReserveInfo, ShopReserveContext shopReserveContext, DealGroupDTO dealGroupDTO) {
        BeamBookConfirmAfterBuyCard card = new BeamBookConfirmAfterBuyCard();
        buildBookConfirmBaseCard(card, userReserveInfo, shopReserveContext, dealGroupDTO);

        //项目头图
        if(dealGroupDTO != null && dealGroupDTO.getImage() != null && dealGroupDTO.getImage().getDefaultPicPath() != null)  {
            card.setHeadImg(dealGroupDTO.getImage().getDefaultPicPath());
        }

        //店铺名称 from dto
        card.setShopName(shopReserveContext.getShopName());
        //title买后约 用团购名
        card.setTitle(getDealGroupBaseTitle(dealGroupDTO, shopReserveContext));
        return card;
    }

    private static String getDealGroupBaseTitle(DealGroupDTO dealGroupDTO, ShopReserveContext shopReserveContext) {
        try {
            return dealGroupDTO.getBasic().getTitle();
        } catch (Exception e) {
            log.error("getDealGroupDefaultTitle error, dealGroupDTO:{}, shopReserveContext:{}", JsonCodec.encode(dealGroupDTO), JsonCodec.encode(shopReserveContext), e);
        }
        return shopReserveContext.getShopName();
    }

    private static BeamBookConfirmCard buildBookConfirmCard(UserReserveInfo userReserveInfo, ShopReserveContext shopReserveContext, DealGroupDTO dealGroupDTO) {
        BeamBookConfirmCard card = new BeamBookConfirmCard();
        buildBookConfirmBaseCard(card, userReserveInfo, shopReserveContext, dealGroupDTO);

        //项目头图
        card.setHeadImg(shopReserveContext.getDefaultPic());
        //预约项目 from 收集预约信息
        String project = ReserveInfoProcessUtils.getStringItem(userReserveInfo.getCollectedReserveInfo(), ReserveItemType.PROJECT.getKey());
        card.setProject(project);
        //title 暂也用店名
        card.setTitle(shopReserveContext.getShopName());
        return card;
    }


    //预约卡片通用参数
    private static void buildBookConfirmBaseCard(BeamBookConfirmBaseCard card, UserReserveInfo userReserveInfo, ShopReserveContext shopReserveContext, DealGroupDTO dealGroupDTO) {
        //联系方式 fillCompleteCollectedReserveInfo放的，没收集到拿UserModel的手机号
        String mobile = userReserveInfo.getCollectedReserveInfo().getPhone();
        card.setUserPhone(mobile);

        //备注
        String remark = userReserveInfo.getCollectedReserveInfo().getRemark();
        if (StringUtils.isNotBlank(remark)) {
            card.setNote(remark);
        }

        //2025.10.03 到店
        String startTimeText = generateStartTime(userReserveInfo, shopReserveContext);
        String personNumText = generatePersonNumber(userReserveInfo, shopReserveContext);
        card.setSubTitle(startTimeText + personNumText);
    }

    private static String generatePersonNumber(UserReserveInfo userReserveInfo, ShopReserveContext shopReserveContext) {
        //卡片上不需要展示人数的行业
        if (BEAM_TRADE_NEED_DEFAULT_PERSON_NUM.contains(shopReserveContext.getPoiIndustryType())) {
            return "";
        }

        //默认一人的行业：1美发；2足疗
        Integer personNum = ReserveInfoProcessUtils.getIntItem(userReserveInfo.getCollectedReserveInfo(), ReserveItemType.PERSON_NUM.getKey());
        if (personNum == null || personNum <= 0) {
            //强制需要1兜底的
            if (BEAM_FORCE_NEED_DEFAULT_PERSON_NUM.contains(shopReserveContext.getPoiIndustryType())) {
                return " 1人";
            }
            //不需要兜底，不展示
            return "";
        }
        return (String.format(" %d人", personNum));
    }
    // 人数

    private static BookConfirmCard buildBookConfirmCard(UserReserveInfo userReserveInfo, ShopReserveContext shopReserveContext, String title, String confirmText) {
        BookConfirmCard card = new BookConfirmCard();
        card.setTitle(title);
        // 预约门店
        card.setShopId(userReserveInfo.getShopId());
        card.setShopName(userReserveInfo.getShopName());
        // 预约时间
        card.setBookTime(generateStartTime(userReserveInfo, shopReserveContext));
        // 预约项目
        String project = ReserveInfoProcessUtils.getStringItem(userReserveInfo.getCollectedReserveInfo(), ReserveItemType.PROJECT.getKey());
        Long duration = ReserveInfoProcessUtils.getLongItem(userReserveInfo.getCollectedReserveInfo(), ReserveItemType.DURATION.getKey());
        card.setProject(mergeProjectAndDuration(shopReserveContext.getPoiIndustryType(), project, duration));
        // 人数
        Integer personNum = ReserveInfoProcessUtils.getIntItem(userReserveInfo.getCollectedReserveInfo(), ReserveItemType.PERSON_NUM.getKey());
        personNum = personNum == null || personNum <= 0 ? 1 : personNum;
        card.setPeopleNum(String.format("%d人", personNum));
        // 联系方式
        String mobile = userReserveInfo.getCollectedReserveInfo().getPhone();
        card.setPhone(mobile);
        // 备注
        String remark = userReserveInfo.getCollectedReserveInfo().getRemark();
        String technician = ReserveInfoProcessUtils.getStringItem(userReserveInfo.getCollectedReserveInfo(), ReserveItemType.TECHNICIAN.getKey());

        // 生成备注和技师信息
        String finalRemark = generateRemarkWithTechnician(remark, technician, shopReserveContext.getPoiIndustryType());
        if (StringUtils.isNotBlank(finalRemark)) {
            card.setRemark(finalRemark);
        }
        card.setConfirmText(confirmText);
        return card;
    }

    private static String generateStartTime(UserReserveInfo userReserveInfo, ShopReserveContext shopReserveContext) {
        // 在线系统
        String startTimeStr = ReserveInfoProcessUtils.getStringItem(userReserveInfo.getCollectedReserveInfo(), ReserveItemType.START_TIME.getKey());
        LocalDateTime startTime = TimeUtil.convertStr2LocalDateTime(startTimeStr);
        if (startTime == null) {
            return StringUtils.EMPTY;
        }

        if (POIIndustryType.BAR.equals(shopReserveContext.getPoiIndustryType())) {
            return generateBarStartTime(startTimeStr, userReserveInfo);
        }

        ShopReserveWayType shopReserveWayType = shopReserveContext.getShopReserveWayType();
        if (ShopReserveWayType.ONLINE.equals(shopReserveWayType)) {
            return String.format("%s 到店", TimeUtil.formatTimeToFriendlyString(startTime));
        }
        // 电话外呼
        String startTimeDescStr = ReserveInfoProcessUtils.getStringItem(userReserveInfo.getCollectedReserveInfo(), ReserveItemType.START_TIME_DESC.getKey());
        if (StringUtils.isBlank(startTimeDescStr)) {
            return String.format("%s 到店", TimeUtil.formatTimeToFriendlyString(startTime));
        }
        String date = startTime.toString("yyyy-MM-dd");
        return String.format("%s %s 到店", date, startTimeDescStr);
    }

    //     酒吧预约卡片酒吧时间 yyyy-MM-dd
    private static String generateBarStartTime(String startTimeStr, UserReserveInfo userReserveInfo) {

        try {
            String timeStr = StringUtil.isNotBlank(startTimeStr) ? startTimeStr : ReserveInfoProcessUtils.getStringItem(userReserveInfo.getCollectedReserveInfo(), ReserveItemType.START_TIME_DESC.getKey());
            if (StringUtils.isBlank(timeStr)) {
                return StringUtils.EMPTY;
            }
            String format = "yyyy-MM-dd";
            LocalDate oneDay = TimeUtil.convertStr2LocalDateTime(timeStr, format).toLocalDate();
            return StringUtil.isNotBlank(oneDay.toString()) ? oneDay + " 到店" : "";
        } catch (Exception e) {
            log.error("generateBarStartTime error, startTimeStr:{}, userReserveInfo:{}", startTimeStr, userReserveInfo, e);
        }
        return StringUtils.EMPTY;
    }

    private static String generateRemarkWithTechnician(String remark, String technician, POIIndustryType poiIndustryType) {
        StringBuilder sb = new StringBuilder();

        // 优先加入原始备注
        if (StringUtils.isNotBlank(remark)) {
            sb.append(remark);
        }

        // 如果有技师信息，判断行业类型
        if (StringUtils.isNotBlank(technician)) {
            // 只对美发和足疗行业处理技师信息
            String technicianPrefix = getTechPrefix(poiIndustryType);
            if (StringUtils.isBlank(technicianPrefix)) {
                return sb.length() > 0 ? sb.toString() : null;
            }
            String technicianInfo = technicianPrefix + technician;

            // 如果已有备注，添加分隔符
            if (sb.length() > 0) {
                sb.append("，");
            }
            sb.append(technicianInfo);
        }

        // 返回合成后的备注，如果没有内容则返回null
        return sb.length() > 0 ? sb.toString() : null;
    }

    private static String getTechPrefix(POIIndustryType poiIndustryType) {
        if (POIIndustryType.HAIR.equals(poiIndustryType)) {
            return "发型师是";
        } else if (POIIndustryType.FOOT_MASSAGE.equals(poiIndustryType)) {
            return "技师是";
        }
        return null;
    }

    private static String mergeProjectAndDuration(POIIndustryType poiIndustryType, String project, Long duration) {
        if (poiIndustryType == null || duration == null || duration <= 0) {
            return project;
        }
        // 美发不需要拼接时长
        if (POIIndustryType.HAIR.equals(poiIndustryType)) {
            return project;
        }
        // project不包含xx分钟，xxmin，xx小时,xxh，需要在project加上duration分钟
        if (!project.matches(".*\\d+分钟.*") && !project.matches(".*\\d+min.*") && !project.matches(".*\\d+小时.*") && !project.matches(".*\\d+h.*")) {
            return String.format("%d分钟 %s", duration, project);
        }
        return project;
    }

    private static int getPlatform(AssistantSceneContext context) {
        return context.getUserId().startsWith(ImUserType.MT.getPrefix()) ? PlatformEnum.MT.getType() : PlatformEnum.DP.getType();
    }

    public static AIAnswerData writeBeamBookCreateCard(String bookSuccessText, AgentCreateOrderResDTO createOrderRes, UserReserveInfo userReserveInfo) {
        BeamBookCreateData beamBookCreateData = new BeamBookCreateData();
        beamBookCreateData.setShopId(String.valueOf(userReserveInfo.getShopId()));
        beamBookCreateData.setOrderId(String.valueOf(createOrderRes.getMainOrderId()));
        beamBookCreateData.setType(String.valueOf(createOrderRes.getOrderType()));
        String cardTag = StreamEventCardTypeEnum.buildCardContent(StreamEventCardTypeEnum.BEAM_BOOK_CREATE_CARD, JSON.toJSONString(beamBookCreateData));

        //写文案
        writeBeamTextAnswer(bookSuccessText);
        //写卡片
        return writeBeamCardAnswer(cardTag, JsonCodec.converseMap(JsonCodec.encodeWithUTF8(beamBookCreateData), String.class, Object.class));
    }

    public static AIAnswerData writeBeamAICallCreateCard(String aiCallSuccessText, AIPhoneCallTaskCreateRes createTaskRes, UserReserveInfo userReserveInfo, ShopReserveContext shopReserveContext) {
        BeamAICallCreateData beamAICallCreateData = new BeamAICallCreateData();
        beamAICallCreateData.setShopId(String.valueOf(userReserveInfo.getShopId()));
        beamAICallCreateData.setTaskId(String.valueOf(createTaskRes.getTaskId()));
        beamAICallCreateData.setType(null);
        beamAICallCreateData.setTitle("待商家确认");
        beamAICallCreateData.setSubTitle(buildAICallCreateCardSubTitle(userReserveInfo, shopReserveContext));
        beamAICallCreateData.setHeadImg(shopReserveContext.getDefaultPic());

        String cardTag = StreamEventCardTypeEnum.buildCardContent(StreamEventCardTypeEnum.BEAM_AI_CALL_CREATE_CARD, JSON.toJSONString(beamAICallCreateData));
        //写文案
        writeBeamTextAnswer(aiCallSuccessText);
        //写卡片
        return writeBeamCardAnswer(cardTag, JsonCodec.converseMap(JsonCodec.encodeWithUTF8(beamAICallCreateData), String.class, Object.class));
    }

    private static String buildAICallCreateCardSubTitle(UserReserveInfo userReserveInfo, ShopReserveContext shopReserveContext) {
        String startTimeText = generateStartTime(userReserveInfo, shopReserveContext);
        String personNumText = generatePersonNumber(userReserveInfo, shopReserveContext);
        return startTimeText + personNumText;
    }

    public static AIAnswerData writeBeamCardAnswer(String cardTag, Map<String, Object> extra) {
        AIAnswerData aiAnswerData = new AIAnswerData();
        aiAnswerData.setAiAnswerType(AIAnswerTypeEnum.TEXT.getType());
        aiAnswerData.setAnswer(cardTag);
        aiAnswerData.setReturnDirect(true);

        //写buffer
        BufferUtils.writeMainTextBuffer(PilotBufferItemDO.builder().data(cardTag).extra(extra).build());
        return aiAnswerData;
    }

    public static AIAnswerData writeBeamTextAnswer(String answer) {
        AIAnswerData aiAnswerData = new AIAnswerData();
        aiAnswerData.setAiAnswerType(AIAnswerTypeEnum.TEXT.getType());
        aiAnswerData.setAnswer(answer);
        aiAnswerData.setReturnDirect(true);

        //写buffer
        BufferUtils.writeMainTextBuffer(PilotBufferItemDO.builder().data(answer).build());
        return aiAnswerData;
    }

    public static AIAnswerData writeBeamModelGenerateAnswer(String answer) {
        AIAnswerData aiAnswerData = new AIAnswerData();
        aiAnswerData.setAiAnswerType(AIAnswerTypeEnum.TEXT.getType());
        aiAnswerData.setAnswer(answer);
        aiAnswerData.setReturnDirect(false);
        return aiAnswerData;
    }
}
