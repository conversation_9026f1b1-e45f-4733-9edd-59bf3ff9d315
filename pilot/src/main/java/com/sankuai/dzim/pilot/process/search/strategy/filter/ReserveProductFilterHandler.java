package com.sankuai.dzim.pilot.process.search.strategy.filter;

import com.sankuai.dzim.pilot.process.search.data.ReserveQueryContext;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;

import java.util.List;


public interface ReserveProductFilterHandler {


    /**
     * 过滤留下符合筛选项条件的商品
     */
    List<DealGroupDTO> filter(ReserveQueryContext context, List<DealGroupDTO> dealGroupDTOList);

    /**
     * 是否匹配当前策略
     */
    boolean match(ReserveQueryContext queryContext);
}
