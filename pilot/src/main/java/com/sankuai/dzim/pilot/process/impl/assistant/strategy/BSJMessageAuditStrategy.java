package com.sankuai.dzim.pilot.process.impl.assistant.strategy;

import com.sankuai.dzim.message.dto.AuditResDTO;
import com.sankuai.dzim.pilot.acl.AuditAclService;
import com.sankuai.dzim.pilot.process.data.AuditResult;
import com.sankuai.dzim.pilot.process.data.MessageAuditRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025/05/19 11:36
 * 保时洁消息审核策略
 */
@Component
@Order(2)
public class BSJMessageAuditStrategy implements MessageAuditStrategy {
    @Autowired
    private AuditAclService auditAclService;

    @Override
    public AuditResult auditText(MessageAuditRequest request) {
        AuditResDTO auditResDTO = auditAclService.audit(request.getMessageId(), request.getImUserId(), request.getAssistantType(), request.getText());
        if (auditResDTO != null && !auditResDTO.isPass()) {
            return AuditResult.builder().pass(false).reason(StringUtils.join(",", auditResDTO.getAuditRecords())).build();
        }
        return AuditResult.auditPass();
    }
}
