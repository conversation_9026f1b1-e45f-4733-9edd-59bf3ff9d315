package com.sankuai.dzim.pilot.process.data;

import com.sankuai.it.xcontract.easypoi.annotation.Excel;
import lombok.Data;

@Data
public class AddProductKnowledgeReq {

    /**
     * 商户Id
     */
    private long shopId;

    /**
     * 商品类型
     * @see com.sankuai.dzim.pilot.api.enums.ProductTypeEnum
     */
    private int productType;

    /**
     * 主体Id（团购/预付Id）
     */
    private String subjectId;

    /**
     * 商品标题
     */
    private String title;

    /**
     * 商品描述
     */
    private String description;
}
