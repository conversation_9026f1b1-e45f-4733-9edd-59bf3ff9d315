package com.sankuai.dzim.pilot.process.localplugin.beta;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.dzim.message.dto.MessageDTO;
import com.sankuai.dzim.message.enums.AuditStatusEnum;
import com.sankuai.dzim.pilot.acl.MapAclService;
import com.sankuai.dzim.pilot.acl.ProductAclService;
import com.sankuai.dzim.pilot.acl.ShopAclService;
import com.sankuai.dzim.pilot.api.enums.assistant.MessageSendDirectionEnum;
import com.sankuai.dzim.pilot.api.enums.assistant.MessageTypeEnum;
import com.sankuai.dzim.pilot.buffer.core.PilotBufferItemDO;
import com.sankuai.dzim.pilot.buffer.utils.BufferUtils;
import com.sankuai.dzim.pilot.chain.data.AIAnswerData;
import com.sankuai.dzim.pilot.chain.data.AIServiceContext;
import com.sankuai.dzim.pilot.dal.entity.pilot.PilotChatGroupEntity;
import com.sankuai.dzim.pilot.dal.entity.pilot.PilotChatMessageEntity;
import com.sankuai.dzim.pilot.dal.entity.pilot.ReviewSyncEntity;
import com.sankuai.dzim.pilot.dal.entity.pilot.TechnicianSyncEntity;
import com.sankuai.dzim.pilot.dal.pilotdao.PilotChatGroupDAO;
import com.sankuai.dzim.pilot.dal.pilotdao.PilotChatGroupMessageDAO;
import com.sankuai.dzim.pilot.dal.pilotdao.TechnicianSyncDAO;
import com.sankuai.dzim.pilot.dal.respository.ReviewRepositoryService;
import com.sankuai.dzim.pilot.domain.annotation.LocalPlugin;
import com.sankuai.dzim.pilot.process.data.AssistantConstant;
import com.sankuai.dzim.pilot.process.localplugin.param.ProductDetailParam;
import com.sankuai.dzim.pilot.process.localplugin.param.ShopDetailParam;
import com.sankuai.dzim.pilot.utils.IDGenerateUtil;
import com.sankuai.dzim.pilot.utils.IMConstants;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.detail.DealGroupTemplateDetailDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.MustServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.OptionalServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectDTO;
import com.sankuai.sinai.data.api.dto.MtPoiDTO;
import com.sankuai.sinai.data.api.util.MtPoiUtil;
import lombok.extern.slf4j.Slf4j;
import mtmap.geoinfo.geoinfo_base.Poi;
import mtmap.geoinfo.geoinfo_base.SearchServiceRequest;
import mtmap.geoinfo.geoinfo_base.SearchServiceResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class ProductDetailPlugin {

    private static List<String> QUERY_TYPE = Lists.newArrayList("baseInfo", "select", "review");

    @Autowired
    private ReviewRepositoryService reviewRepositoryService;

    @Autowired
    private ProductAclService productAclService;

    @Autowired
    private MapAclService mapAclService;

    @Resource
    private PilotChatGroupDAO pilotChatGroupDAO;

    @Resource
    private PilotChatGroupMessageDAO pilotChatGroupMessageDAO;

    @Autowired
    private IDGenerateUtil idGenerateUtil;

    @MdpConfig("com.sankuai.mim.pilot.plugin.deal.detail.type2prompt:{}")
    private HashMap<String, String> type2PromptMap;

    @MdpConfig("com.sankuai.mim.pilot.plugin.shop.detail.map.search.key:\"\"")
    private String mapSearchKey;

    @LocalPlugin(name = "ProductDetailTool", description = "可以为你提供商品基础信息查询、商品评价查询, 当你需要查询商品的信息时可以使用", returnDirect = false)
    public AIAnswerData getProductDetail(ProductDetailParam productDetailParam) {
        if (productDetailParam == null) {
            return new AIAnswerData("参数错误");
        }

        if (productDetailParam.getShopId() <= 0 && StringUtils.isBlank(productDetailParam.getShopName())) {
            return new AIAnswerData("请正确提取出用户咨询的门店");
        }

        // 从门店名称中解析出门店id
        if (StringUtils.isNotBlank(productDetailParam.getShopName()) && productDetailParam.getShopId() <= 0) {
            List<Poi> pois = searchShop(productDetailParam);
            if (CollectionUtils.isEmpty(pois)) {
                return new AIAnswerData("没有查询到门店信息,请让用户告知准确的门店名称");
            }
            if (pois.size() > 1) {
                return needConfirmShop(pois);
            }

            // 有确定的门店ID,插入一条systemMessage
            insertSystemMessage(productDetailParam.getAiServiceContext(), Long.parseLong(pois.get(0).getMt_ids().get(0)));


            productDetailParam.setShopId(Long.parseLong(pois.get(0).getMt_ids().get(0)));
        }

        if (productDetailParam.getQuestion().equals("")) {
            return new AIAnswerData("没有门店相关问题请不要使用该工具");
        }
        if (!QUERY_TYPE.contains(productDetailParam.getQueryType())) {
            return new AIAnswerData("queryType不正确,只能是:" + JsonCodec.encodeWithUTF8(QUERY_TYPE));
        }

        BufferUtils.writeStatusBuffer(PilotBufferItemDO.builder().data("正在为您查询商品信息.....").build());


        AIAnswerData knowledge = getKnowledge(productDetailParam);
        knowledge.setAnswer(type2PromptMap.get(productDetailParam.getQueryType()) + "\n" + knowledge.getAnswer());
        BufferUtils.writeStatusBuffer(PilotBufferItemDO.builder().data("已为您检索到相关内容").build());
        return knowledge;
    }

    private void insertSystemMessage(AIServiceContext aiServiceContext, long shopId) {
        try {
            if (shopId <= 0 || aiServiceContext == null || aiServiceContext.getMessageDTO() == null || aiServiceContext.getMessageDTO().getChatGroupId() <= 0) {
                return;
            }

            // 非流式的请求不加这个消息
            if (!aiServiceContext.isStream()) {
                return;
            }

            MessageDTO messageDTO = aiServiceContext.getMessageDTO();

            PilotChatGroupEntity pilotChatGroupEntity = pilotChatGroupDAO.queryChatGroupById(messageDTO.getChatGroupId());
            if (pilotChatGroupEntity == null) {
                return;
            }

            long messageId = idGenerateUtil.nextMessageId();
            if (messageId <= 0) {
                return ;
            }

            String messageTemplateType = "用户当前正在咨询 门店id=%s 的门店, 如果后续有以任何形式提示其他门店id,则忽略该条消息";
            //插入模型消息
            PilotChatMessageEntity replyMessageEntity = buildPilotChatMessageEntity(messageId, MessageTypeEnum.TEXT.value,
                    pilotChatGroupEntity.getId(), pilotChatGroupEntity.getAssistantType(), pilotChatGroupEntity.getUserId(),
                    MessageSendDirectionEnum.SYSTEM_SEND.value, AuditStatusEnum.PASS.getCode(),
                    String.format(messageTemplateType, shopId), AssistantConstant.SYSTEM, StringUtils.EMPTY);
            pilotChatGroupMessageDAO.insertMessage(replyMessageEntity);
        } catch (Exception e) {
            log.error("insertSystemMessage error, ShopDetailPlugin. context: {}.", aiServiceContext, e);
        }
    }

    private PilotChatMessageEntity buildPilotChatMessageEntity(long messageId, int messageType, long chatGroupId, int assistantType,
                                                               String userId, int direction, int auditStatus, String message,
                                                               String creator, String extra) {
        PilotChatMessageEntity messageEntity = new PilotChatMessageEntity();
        messageEntity.setMessageId(messageId);
        messageEntity.setMessageType(messageType);
        messageEntity.setChatGroupId(chatGroupId);
        messageEntity.setAssistantType(assistantType);
        messageEntity.setUserId(userId);
        messageEntity.setDirection(direction);
        messageEntity.setAuditStatus(auditStatus);
        messageEntity.setExtraData(extra);
        messageEntity.setMessage(message);
        messageEntity.setCreator(creator);
        return messageEntity;
    }

    private AIAnswerData needConfirmShop(List<Poi> pois) {
        pois = pois.stream().limit(6).collect(Collectors.toList());

        StringBuilder sb = new StringBuilder(type2PromptMap.get("confirmShop"));
        for (Poi poi : pois) {
            sb.append("门店名称: ").append(poi.getName()).append("   门店ID：").append(Long.parseLong(poi.getMt_ids().get(0))).append("\n");
        }

        return new AIAnswerData(sb.toString());
    }

    private List<Poi> searchShop(ProductDetailParam productDetailParam) {
        SearchServiceRequest searchServiceRequest = new SearchServiceRequest();
        searchServiceRequest.setKey(mapSearchKey);
        searchServiceRequest.setKeywords(productDetailParam.getShopName());
        searchServiceRequest.setMt_open_cityid("10");
        searchServiceRequest.setAdvanced_filter("new_type_code,eq,2$3$22$27$29$20007$20178$20179$20252$20274$20285$20691");

        SearchServiceResponse searchServiceResponse = mapAclService.searchShop(searchServiceRequest);
        if (searchServiceResponse != null) {
            return searchServiceResponse.getPois();
        }
        return Lists.newArrayList();
    }

    private AIAnswerData getKnowledge(ProductDetailParam productDetailParam) {
        if (productDetailParam.getQueryType().equals("baseInfo")) {
            return getBaseInfo(productDetailParam);
        }

        if (productDetailParam.getQueryType().equals("review")) {
            return getReview(productDetailParam);
        }

        if (productDetailParam.getQueryType().equals("select")) {
            return recommendProduct(productDetailParam);
        }

        return new AIAnswerData("没有相关内容");
    }

    private AIAnswerData recommendProduct(ProductDetailParam productDetailParam) {
        List<ReviewSyncEntity> reviewSyncEntities = getReviewSyncEntities(productDetailParam, 200);
        reviewSyncEntities = reviewSyncEntities.stream().filter(r -> r.getDealId() > 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(reviewSyncEntities)) {
            return new AIAnswerData("暂无团购推荐");
        }

        return buildReviewAndDealInfo(reviewSyncEntities);
    }

    private AIAnswerData getBaseInfo(ProductDetailParam productDetailParam) {
        return getDeal(productDetailParam);
    }

    private AIAnswerData getReview(ProductDetailParam productDetailParam) {
        if (StringUtils.isBlank(productDetailParam.getQuestion())) {
            return new AIAnswerData("没有查询到相关信息");
        }

        //向量检索评价
        List<ReviewSyncEntity> reviewSyncEntities = getReviewSyncEntities(productDetailParam, 400);
        if (CollectionUtils.isEmpty(reviewSyncEntities)) {
            return new AIAnswerData("没有查询到门店评价信息");
        }

        reviewSyncEntities = reviewSyncEntities.stream().filter(r -> r.getDealId() > 0).limit(300).collect(Collectors.toList());

        return buildReviewAndDealInfo(reviewSyncEntities);
    }

    @NotNull
    private AIAnswerData buildReviewAndDealInfo(List<ReviewSyncEntity> reviewSyncEntities) {
        Map<Long, List<ReviewSyncEntity>> deal2ReviewMap = reviewSyncEntities.stream().collect(Collectors.groupingBy(ReviewSyncEntity::getDealId));
        StringBuilder sb = new StringBuilder();

        for (Map.Entry<Long, List<ReviewSyncEntity>> deal2Review : deal2ReviewMap.entrySet()) {
            List<ReviewSyncEntity> reviews = deal2Review.getValue();
            Long dealId = deal2Review.getKey();
            appendDealInfo(sb, dealId);
            sb.append("#### 团购关联的评价:\n");
            for (ReviewSyncEntity entity : reviews) {
                addStarType(sb, entity);
                sb.append(entity.getContent()).append("\n");
            }
        }

        return new AIAnswerData(sb.toString());
    }

    private void appendDealInfo(StringBuilder sb, Long dealId) {
        DealGroupDTO dealBaseInfo = productAclService.getDealBaseInfo(dealId);
        if (dealBaseInfo == null) {
            return;
        }
        sb.append("### 关联的团购信息(团购id = ").append(dealId).append(")\n");
        sb.append(convertDealGroup2Text(dealBaseInfo));
    }

    private String convertDealGroup2Text(DealGroupDTO dealGroupDTO) {
        StringBuilder sb = new StringBuilder();
        sb.append("商品名称:").append(getTitle(dealGroupDTO)).append("\n");
        sb.append("商品价格:").append(getPrice(dealGroupDTO)).append("\n");
        sb.append("商品服务项:").append(getServiceProject(dealGroupDTO)).append("\n");
        sb.append("商品介绍:").append(getIntroduction(dealGroupDTO)).append("\n");

        return sb.toString();
    }

    private String getIntroduction(DealGroupDTO dealGroupDTO) {
        if (dealGroupDTO.getDetail() == null || CollectionUtils.isEmpty(dealGroupDTO.getDetail().getTemplateDetailDTOs())) {
            return "暂无商品介绍信息";
        }

        StringBuilder sb = new StringBuilder();
        for (DealGroupTemplateDetailDTO templateDetailDTO : dealGroupDTO.getDetail().getTemplateDetailDTOs()) {
            if (templateDetailDTO.getTitle().equals("产品介绍")) {
                sb.append("\n\t产品介绍:").append(parseHtml(templateDetailDTO.getContent(), "\n\t\t"));
            }
            if (templateDetailDTO.getTitle().equals("团购详情")) {
                sb.append("\n\t补充信息:").append(parseHtml(templateDetailDTO.getContent(), "\n\t\t"));
            }
            if (templateDetailDTO.getTitle().equals("购买须知")) {
                sb.append("\n\t购买须知:").append(parseHtml(templateDetailDTO.getContent(), "\n\t\t"));
            }
        }

        return sb.toString();
    }

    private String parseHtml(String content, String prefix) {
        StringBuilder stringBuilder = new StringBuilder();

        Document doc = Jsoup.parse(content);
        Elements paragraphs = doc.select("p");
        for (Element paragraph : paragraphs) {
            if (StringUtils.isNotBlank(paragraph.text())) {
                stringBuilder.append(prefix).append(paragraph.text());
            }
        }

        return stringBuilder.toString();
    }

    private String getServiceProject(DealGroupDTO dealGroupDTO) {
        if (dealGroupDTO.getServiceProject() == null) {
            return "暂无服务项目信息";
        }
        StringBuilder serviceSb = new StringBuilder();

        // 全部可享
        if (CollectionUtils.isNotEmpty(dealGroupDTO.getServiceProject().getMustGroups())) {
            serviceSb.append("\n\t【全部可享】服务包含:");
            for (MustServiceProjectGroupDTO serviceGroup : dealGroupDTO.getServiceProject().getMustGroups()) {
                if (CollectionUtils.isEmpty(serviceGroup.getGroups())) {
                    continue;
                }
                for (ServiceProjectDTO serviceProjectDTO : serviceGroup.getGroups()) {
                    serviceSb.append("\n\t\t").append(parseServiceProject(serviceProjectDTO));
                }
            }
        }

        // 部分可享
        if (CollectionUtils.isNotEmpty(dealGroupDTO.getServiceProject().getOptionGroups())) {
            serviceSb.append("\n\t【部分可享】服务包含:");
            for (OptionalServiceProjectGroupDTO serviceGroup : dealGroupDTO.getServiceProject().getOptionGroups()) {
                if (CollectionUtils.isEmpty(serviceGroup.getGroups())) {
                    continue;
                }
                String optionTip = String.format("%s选%s", serviceGroup.getGroups().size(), serviceGroup.getOptionalCount());
                serviceSb.append("\n\t\t").append(optionTip).append(":");
                for (ServiceProjectDTO serviceProjectDTO : serviceGroup.getGroups()) {
                    serviceSb.append("\n\t\t\t").append(parseServiceProject(serviceProjectDTO));
                }
            }
        }
        return serviceSb.toString();
    }

    private String parseServiceProject(ServiceProjectDTO serviceProjectDTO) {
        StringBuilder sb = new StringBuilder();
        sb.append("服务名称:").append(serviceProjectDTO.getName());
        if (StringUtils.isNotBlank(serviceProjectDTO.getMarketPrice())) {
            sb.append(" ( ").append("价值:").append(serviceProjectDTO.getMarketPrice()).append("元").append(" )");
        }
        if (CollectionUtils.isEmpty(serviceProjectDTO.getAttrs())) {
            return sb.toString();
        }

        for (int index = 0; index < serviceProjectDTO.getAttrs().size(); ++index) {
            ServiceProjectAttrDTO attrDTO = serviceProjectDTO.getAttrs().get(index);
            sb.append(" ( ").append(attrDTO.getChnName()).append(":").append(attrDTO.getAttrValue());
            if (index != serviceProjectDTO.getAttrs().size() - 1) {
                sb.append(", ");
            } else {
                sb.append(" )");
            }
        }
        return sb.toString();
    }

    private String getPrice(DealGroupDTO dealGroupDTO) {
        if (dealGroupDTO.getPrice() == null) {
            return "暂无价格信息," + IMConstants.ANSWER_PROBLEM_NO_REPLY;
        }

        return dealGroupDTO.getPrice().getSalePrice() + "元";
    }

    private String getTitle(DealGroupDTO dealGroupDTO) {
        String title = dealGroupDTO.getBasic().getTitle();
        String titleDesc = dealGroupDTO.getBasic().getTitleDesc();
        if (StringUtils.isNotBlank(titleDesc)) {
            return title + "( " + titleDesc + " )";
        }
        return title;
    }

    private void addStarType(StringBuilder sb, ReviewSyncEntity entity) {
        String star = entity.getStar();
        Map<String, String> starMap = JsonCodec.converseMap(star, String.class, String.class);
        if (starMap.containsKey("starType")) {
            String starType = starMap.get("starType");
            switch (starType) {
                case "1":
                    sb.append("好评: ");
                    break;
                case "2":
                    sb.append("差评: ");
                    break;
                case "3":
                    sb.append("中评: ");
                    break;
            }
        }
    }

    private AIAnswerData getDeal(ProductDetailParam productDetailParam) {
        StringBuilder sb = new StringBuilder();
        // 有团购id,出团购信息和团购关联的评价
        if (productDetailParam.getProductId() > 0) {
            DealGroupDTO dealBaseInfo = productAclService.getDealBaseInfo(productDetailParam.getProductId());
            if (dealBaseInfo != null) {
                sb.append("## 团购信息(团购id = ").append(productDetailParam.getProductId()).append(")\n");
                sb.append(convertDealGroup2Text(dealBaseInfo));
            }

            if (StringUtils.isNotBlank(productDetailParam.getQuestion())) {
                appendDealReview(sb, productDetailParam);
            }

            if (StringUtils.isNotBlank(sb.toString())) {
                return new AIAnswerData(sb.toString());
            }
        }

        // 没有团购id,但是有question,出和question相关的评价
        if (StringUtils.isNotBlank(productDetailParam.getQuestion())) {
            return getReview(productDetailParam);
        }

        return new AIAnswerData("没有团购信息" + IMConstants.ANSWER_PROBLEM_NO_REPLY);
    }

    private void appendDealReview(StringBuilder sb, ProductDetailParam productDetailParam) {
        List<ReviewSyncEntity> reviewSyncEntities = getReviewSyncEntities(productDetailParam, 100);
        if (reviewSyncEntities == null) {
            return;
        }

        sb.append("以下是团购关联的一些评价: \n");
        for (ReviewSyncEntity entity : reviewSyncEntities) {
            addStarType(sb, entity);
            sb.append(entity.getContent()).append("\n");
        }
    }

    private List<ReviewSyncEntity> getReviewSyncEntities(ProductDetailParam productDetailParam, int topK) {
        Map<String, String> scalarFilter = Maps.newHashMap();
        if (productDetailParam.getShopId() > 0) {
            scalarFilter.put("mtShopId", String.valueOf(productDetailParam.getShopId()));
        }
        if (productDetailParam.getProductId() > 0) {
            scalarFilter.put("dealId", String.valueOf(productDetailParam.getProductId()));
        }
        List<ReviewSyncEntity> reviewSyncEntities = reviewRepositoryService.searchReview(productDetailParam.getQuestion(), scalarFilter, topK);
        if (CollectionUtils.isEmpty(reviewSyncEntities)) {
            return null;
        }
        return reviewSyncEntities;
    }

}
