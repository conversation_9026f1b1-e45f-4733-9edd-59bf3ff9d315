package com.sankuai.dzim.pilot.process;

import com.sankuai.dzim.pilot.api.data.ImportTemplateData;

import java.util.List;

public interface OfflineDataProcessService {
    /**
     * 导入模板数据
     * @param importTemplateData
     * @param fileName
     */
    void importTemplateData(ImportTemplateData importTemplateData, String fileName);

    /**
     * 导出模板数据
     * @param fileName
     * @param bizType
     * @param questions
     */
    void exportTemplateData(String fileName, Integer bizType, List<String> questions);
}
