package com.sankuai.dzim.pilot;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.sankuai.athena.client.container.AthenaBeanFactory;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.utils.SsoParseUtils;
import com.sankuai.it.sso.sdk.enums.AuthFailedCodeEnum;
import com.sankuai.it.sso.sdk.listener.SSOListener;
import com.sankuai.meituan.auth.vo.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * Created by FanJiangQi on 2021/7/5.
 */
@Slf4j
@Component
public class MySSOListener implements SSOListener {

    private String redirectUrl = Lion.getString("com.sankuai.mim.pilot", "com.sankuai.dzim.pilot.sso.redirectUrl");


    @Autowired
    private SsoParseUtils ssoParseUtils;

    @Override
    public String onRedirectingToSSOLogin(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, String s) {
        Cat.logEvent("sso", "onRedirectingToSSOLogin");
        return null;
    }

    @Override
    public String onRedirectingToOriginalUrl(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, String ssoid, String originalUrl) {
        Cat.logEvent("sso", "onRedirectingToOriginalUrl");
        if (ssoParseUtils == null) {
            ssoParseUtils = AthenaBeanFactory.getBean(SsoParseUtils.class);
        }
        int consultantAccountId = ssoParseUtils.parseSsoUserId(ssoid);
        LogUtils.logReturnInfo(log, TagContext.builder().action("ssoLogin").imAccountId(String.valueOf(consultantAccountId)).build(),
                new WarnMessage("onRedirectingToOriginalUrl", "", "MySSOListener login"), consultantAccountId, null);
        return redirectUrl;
    }

    @Override
    public boolean onSSOAuthed(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, User user) {
        Cat.logEvent("sso", "onSSOAuthed");
        return true;
    }

    @Override
    public boolean onSSOAuthFailed(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, AuthFailedCodeEnum authFailedCodeEnum) {
        Cat.logEvent("sso", "onSSOAuthFailed");
        return false;
    }

    @Override
    public void onSSOLogouted(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, User user) {
        Cat.logEvent("sso", "onSSOLogouted");
        LogUtils.logReturnInfo(log, TagContext.builder().action("ssoLogin").imAccountId(String.valueOf(user.getId())).build(),
                new WarnMessage("onSSOLogouted", "", "MySSOListener logout"), user.getId(), null);
    }
}
