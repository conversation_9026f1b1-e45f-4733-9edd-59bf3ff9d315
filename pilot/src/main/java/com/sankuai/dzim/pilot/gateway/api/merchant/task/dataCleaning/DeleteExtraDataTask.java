package com.sankuai.dzim.pilot.gateway.api.merchant.task.dataCleaning;

import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.pilot.dal.entity.MedicalCrawlingKnowledgeEntity;
import com.sankuai.dzim.pilot.dal.pilotdao.MedicalCrawlingKnowledgeDAO;
import com.sankuai.dzim.pilot.gateway.api.merchant.task.Cleaning;
import com.sankuai.dzim.pilot.gateway.api.merchant.task.Strategy;
import com.sankuai.dzim.pilot.gateway.api.merchant.task.StrategyFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class DeleteExtraDataTask extends ThreadTaskTemplate {

    @Autowired
    private MedicalCrawlingKnowledgeDAO medicalCrawlingKnowledgeDAO;

    @Override
    public long processData(long start, int pageSize) {
        try {
            for (long i = start; i < start + pageSize; i++) {
                medicalCrawlingKnowledgeDAO.deleteById(i);
            }
        } catch (Exception e) {
            LogUtils.logRequestLog(log, TagContext.builder().action("dataCleaning").build(), "ProcessDataService", e.getMessage());
            return start;
        }
        return start + pageSize;
    }
}
