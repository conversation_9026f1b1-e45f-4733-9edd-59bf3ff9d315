
package com.sankuai.dzim.pilot.process.data;

import com.sankuai.dzim.pilot.process.search.data.TimeSliceM;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 商品库存信息
 */
@Data
public class ProductStockData implements Serializable {

    /**
     * 时间片列表，时间片排序为TimeSliceDTO::startTime字段升序
     */
    private List<TimeSliceM> timeSlices;

    /**
     * 最早可订时间, 时分级格式为"yyyy-MM-dd HH:mm"
     */
    private Long earliestBookableTime;

}
