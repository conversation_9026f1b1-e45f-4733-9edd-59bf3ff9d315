package com.sankuai.dzim.pilot.dal.respository;

import com.sankuai.dzim.pilot.dal.entity.NoteInfoEntity;

import java.util.List;
import java.util.Map;

public interface NoteRepositoryService {

    /**
     * 添加笔记
     *
     * @param noteInfoEntity
     * @return
     */
    long addNoteInfo(NoteInfoEntity noteInfoEntity);

    /**
     * 批量删除笔记
     *
     * @param noteIds
     * @return
     */
    int batchDeleteNote(List<Long> noteIds);

    /**
     * 搜索业务知识
     *
     * @param query        搜索关键词
     * @param scalarFilter 用于标量过滤
     * @param topK         返回结果数量
     * @return
     */
    List<NoteInfoEntity> searchNoteInfo(String query, Map<String, String> scalarFilter, int topK);

    /**
     * 更新笔记，只更新扩展字段，没有更新向量库
     * @param noteInfoEntity
     * @return
     */
    int updateNoteInfo(NoteInfoEntity noteInfoEntity);

    /**
     * 范围按id查询笔记
     * @param startId
     * @param endId
     * @return
     */
    List<NoteInfoEntity> rangeFindNoteInfo(long startId, long endId);
}
