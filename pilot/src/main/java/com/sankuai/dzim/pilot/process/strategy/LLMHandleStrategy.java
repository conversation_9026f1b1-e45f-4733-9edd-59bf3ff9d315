package com.sankuai.dzim.pilot.process.strategy;

import com.google.common.collect.Maps;
import com.sankuai.dzim.pilot.process.data.IntelligentCustomerConfig;
import com.sankuai.dzim.pilot.process.data.IntelligentCustomerContext;
import com.sankuai.dzim.pilot.process.data.req.IntelligentCustomerReq;
import com.sankuai.dzim.pilot.utils.data.Response;

import java.util.Map;

public interface LLMHandleStrategy {

    boolean accept(IntelligentCustomerReq intelligentCustomerReq);

    /**
     * 是否需要生成答案
     * @return
     */
    default Response<Boolean> isNeedGenerateAnswer(IntelligentCustomerContext intelligentCustomerContext) {
        return Response.success();
    };

    /**
     * 获取llm配置
     * @return
     */
    default Map<String, IntelligentCustomerConfig> getChatType2ConfigMap() {return Maps.newHashMap();}

    /**
     * 保存LLM生成的答案到DB
     * @return
     */
    default Response<Boolean> saveLLMGenerateAnswerToDB(IntelligentCustomerContext intelligentCustomerContext) {return Response.success();}

    /**
     * 后置处理
     * @return
     */
    Response<Boolean> postProcess(IntelligentCustomerContext intelligentCustomerContext);
}
