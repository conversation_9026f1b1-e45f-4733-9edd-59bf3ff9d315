package com.sankuai.dzim.pilot.process.search.data;

import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
public class ProductItemM {
    /**
     * skuId
     */
    private int productItemId;

    /**
     * sku的标题
     */
    private String title;

    /**
     * spuId
     */
    private int productId;

    /**
     * 周几「预订特有」
     */
    private int week;

    /**
     * 时段信息
     */
    private String period;

    /**
     * sku基准价，不包含优惠（预订价）
     */
    private BigDecimal originalSalePrice;

    /**
     * sku优惠信息
     */
    private List<PromoPriceM> skuPromos = new ArrayList<>();

    /**
     * sku未来优惠信息,足疗场景下是会员优惠
     */
    private List<PromoPriceM> futureSkuPromos = new ArrayList<>();

    /**
     * 价格信息：实际售价
     */
    private String price;

    /**
     * 价格信息：原价（市场价）
     */
    private String marketPrice;

    /**
     * 卡ICON
     */
    private String cardIcon;

    /**
     * 立减标签
     */
    private String promoTag;

    /**
     * 按钮文案
     */
    private String actionText;

    /**
     * 按钮跳转链接
     */
    private String actionUrl;

    /**
     * 是否可订
     */
    private Boolean canBook;

    /**
     * 扩展属性
     */
    private List<AttrM> extAttrs;

    /**
     * 数量，如：几次、几人
     */
    private int quantity;

    /**
     * 预付款
     */
    private String prePayPrice;

    /**
     * 尾付款
     */
    private String finalPayPrice;

    /**
     * 次数文案，比如：单次、3次等
     */
    private String countTag;

    /**
     * 价格描述，比如：限首单、X人团等
     */
    private String priceDesc;

    /**
     * 价格文案，比如： ￥xxx/次等
     */
    private String priceTag;

    /**
     * 折扣
     */
    private String discountTag;

    /**
     * 关联itemId的商品ID，如拼团的skuId
     */
    private int relatedProductId;

    /**
     * 关联relatedProductId的itemId，如：拼团的商品Id
     */
    private int relatedProductItemId;

    /**
     * 价格力时间 比如30天 60天 90天
     */
    private int lowestPriceDayLength;

    /**
     * 融合优惠价格
     */
    private List<ProductPromoPriceM> promoPrices;

    /**
     * 商品价格力标签-全部
     */
    private List<ProductPricePowerM> allPricePowerTag;

    /**
     * 预订时段库存
     */
    private List<PeriodStockM> bookPeriodStocks;

    /**
     * 价保信息
     */
    private PriceProtectionInfoM priceProtectionInfo;

    /**
     * 库存数量
     */
    private int stock;

    /**
     * sku跳转链接
     */
    private String detailUrl;

    /**
     * 子商品信息
     */
    private List<ProductItemM> children;


    /**
     * 根据属性名获取属性值
     *
     * @param attrName
     * @return
     */
    public String getAttr(String attrName) {
        if (CollectionUtils.isEmpty(this.getExtAttrs())) {
            return null;
        }
        AttrM attrM = this.getExtAttrs().stream().filter(attr -> attr.getName().equals(attrName)).findFirst().orElse(null);
        return attrM == null ? null : attrM.getValue();
    }

    /**
     * 填充属性值
     *
     * @param attrName
     * @return
     */
    public void setAttr(String attrName, String attrValue) {
        if (CollectionUtils.isEmpty(this.getExtAttrs())) {
            this.setExtAttrs(Lists.newArrayList(new AttrM(attrName, attrValue)));
            return;
        }
        this.getExtAttrs().add(new AttrM(attrName, attrValue));
    }

    /**
     * 查询指定类型的优惠价格
     *
     * @param type
     * @return
     */
    public ProductPromoPriceM getPromoPrice(int type) {
        if (CollectionUtils.isEmpty(this.getPromoPrices())) {
            return null;
        }
        return this.promoPrices.stream().filter(productPromoPriceM -> productPromoPriceM.getPromoType() == type).findFirst().orElse(null);
    }
}
