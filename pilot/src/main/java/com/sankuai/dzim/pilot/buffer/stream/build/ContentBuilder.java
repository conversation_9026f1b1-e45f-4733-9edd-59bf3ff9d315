package com.sankuai.dzim.pilot.buffer.stream.build;

import com.google.common.collect.Lists;
import com.sankuai.dzim.pilot.api.enums.assistant.AssistantTypeEnum;
import com.sankuai.dzim.pilot.buffer.core.PilotBufferItemDO;
import com.sankuai.dzim.pilot.buffer.enums.StreamEventDataTypeEnum;
import com.sankuai.dzim.pilot.buffer.stream.build.beamcardext.BeamCardBuildExt;
import com.sankuai.dzim.pilot.buffer.stream.build.beamcardext.data.BeamCardBuildContext;
import com.sankuai.dzim.pilot.buffer.stream.build.ext.CardBuildExt;
import com.sankuai.dzim.pilot.buffer.stream.build.replacetagexpt.ReplaceTagExt;
import com.sankuai.dzim.pilot.buffer.stream.build.replacetagexpt.data.ReplaceContext;
import com.sankuai.dzim.pilot.buffer.stream.vo.BeamChoiceVO;
import com.sankuai.dzim.pilot.buffer.stream.vo.StreamEventCardDataVO;
import com.sankuai.dzim.pilot.buffer.stream.vo.StreamEventDataVO;
import com.sankuai.dzim.pilot.buffer.stream.vo.StreamEventVO;
import com.sankuai.dzim.pilot.scene.data.AssistantExtraConstant;
import com.sankuai.dzim.pilot.scene.data.AssistantSceneContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.math3.util.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @since 2024/7/29 19:40
 * <p>
 * 消息构建主流程
 */
@Slf4j
@Component
public class ContentBuilder {

    @Autowired
    private List<CardBuildExt> cardBuildExtList;

    @Autowired
    private List<ReplaceTagExt> replaceTagExtList;

    @Autowired
    private List<BeamCardBuildExt> beamCardBuildExtList;

    public static final String MESSAGE_TYPE = "message";

    public StreamEventVO buildStreamEventVO(StreamEventDataVO streamEventDataVO, AssistantSceneContext context) {
        if (streamEventDataVO == null) {
            return null;
        }
        // 构造beam消息
        if (context.getAssistantType() == AssistantTypeEnum.BEAM_FWLS_AGENT.getType()) {
            return buildBeamStreamEventVO(streamEventDataVO, context);
        }

        //填充卡片属性
        paddingCardProps(streamEventDataVO, context);
        //构建消息体
        StreamEventVO streamEventVO = new StreamEventVO();
        streamEventVO.setType(MESSAGE_TYPE);
        streamEventVO.setData(streamEventDataVO);
        return streamEventVO;
    }

    private StreamEventVO buildBeamStreamEventVO(StreamEventDataVO streamEventDataVO, AssistantSceneContext context) {
        BeamChoiceVO.ChoiceDelta choiceDelta = new BeamChoiceVO.ChoiceDelta();
        choiceDelta.setContent(streamEventDataVO.getContent());
        choiceDelta.setType(streamEventDataVO.getEvent().equals(StreamEventDataTypeEnum.LOADING_STATUS.getType())? "INTERMEDIATE": "ANSWER");
        choiceDelta.setResource_list(streamEventDataVO.getResources());

        BeamChoiceVO beamChoiceVO = new BeamChoiceVO();
        beamChoiceVO.setDelta(choiceDelta);

        StreamEventDataVO oldDataVO = new StreamEventDataVO();
        oldDataVO.setEvent(StreamEventDataTypeEnum.MAIN_TEXT.getType());

        return StreamEventVO.builder().choices(Lists.newArrayList(beamChoiceVO)).data(oldDataVO).build();
    }

    public List<BeamChoiceVO.DeltaResource> buildBeamResource(Pair<String, String> tag, List<PilotBufferItemDO> tagItems, String data) {
        if (tag == null || StringUtils.isEmpty(tag.getKey())) {
            return Lists.newArrayList();
        }

        BeamCardBuildExt beamCardBuildExt = beamCardBuildExtList.stream()
                .filter(ext -> ext.acceptByStreamCardType(tag.getKey()))
                .findFirst()
                .orElse(null);

        if (beamCardBuildExt == null) {
            return Lists.newArrayList();
        }

        BeamCardBuildContext context = BeamCardBuildContext.builder().tagItems(tagItems).data(data).build();
        // 不同卡片填充对应的resource
        return beamCardBuildExt.buildResources(context);
    }

    public String decodeBeamCardMessage(String originalMessage) {
        try {
            // 使用正则表达式匹配所有卡片
            Pattern pattern = Pattern.compile("<card[^>]*type=([^\\s>]+)[^>]*>([^<]*)</card>");
            Matcher matcher = pattern.matcher(originalMessage);

            while (matcher.find()) {
                String fullCardText = matcher.group();  // 获取完整的卡片文本
                String typeValue = matcher.group(1);    // 获取type值
                String cardContent = matcher.group(2);  // 获取标签中间的内容
                BeamCardBuildExt cardBuildExt = getBeamCardBuildExt(typeValue);
                if (cardBuildExt == null) {
                    continue;
                }

                String decodeCardText = cardBuildExt.cardDecode(cardContent);
                originalMessage = originalMessage.replace(fullCardText, decodeCardText);
            }
            return originalMessage;
        } catch (Exception e) {
            log.error("decodeBeamCardMessage error, msg = {}", originalMessage, e);
        }
        return StringUtils.EMPTY;
    }

    private BeamCardBuildExt getBeamCardBuildExt(String resourceType) {
        if (StringUtils.isEmpty(resourceType)) {
            return null;
        }
        return beamCardBuildExtList.stream()
                .filter(ext -> ext.acceptByBeamResourceType(resourceType))
                .findFirst()
                .orElse(null);
    }

    public String replaceTags(Pair<String, String> tag, String tagContent, String data, AssistantSceneContext context, List<PilotBufferItemDO> tagItems) {
        if (tag == null || StringUtils.isEmpty(tag.getKey())) {
            return StringUtils.EMPTY;
        }

        ReplaceTagExt replaceTagExt = replaceTagExtList.stream()
                .filter(ext -> ext.accept(tag.getKey()))
                .findFirst()
                .orElse(null);

        if (replaceTagExt == null) {
            return StringUtils.EMPTY;
        }

        ReplaceContext replaceContext = new ReplaceContext();
        replaceContext.setContext(context);
        replaceContext.setTagContent(tagContent);
        replaceContext.setData(data);
        replaceContext.setTag(tag);
        replaceContext.setItemDOs(tagItems);
        // TAG替换，不同卡片实现不同Ext
        return replaceTagExt.replace(replaceContext);
    }
    private void paddingCardProps(StreamEventDataVO streamEventDataVO, AssistantSceneContext context) {
        if (CollectionUtils.isEmpty(streamEventDataVO.getCardsData())) {
            return;
        }
        for (StreamEventCardDataVO cardDataVO : streamEventDataVO.getCardsData()) {
            // 缓存
            if (shouldSkipBuild(cardDataVO)) {
                continue;
            }
            CardBuildExt cardBuildExt = getCardBuildExt(cardDataVO);
            if (cardBuildExt == null) {
                continue;
            }
            cardBuildExt.padding(cardDataVO, context);
        }
    }

    private boolean shouldSkipBuild(StreamEventCardDataVO cardDataVO) {
        return cardDataVO.getCardProps() != null &&
                cardDataVO.getCardProps().containsKey(AssistantExtraConstant.MESSAGE_NEED_BUILD) &&
                !((boolean) cardDataVO.getCardProps().get(AssistantExtraConstant.MESSAGE_NEED_BUILD));
    }

    private CardBuildExt getCardBuildExt(StreamEventCardDataVO cardDataVO) {
        if (cardDataVO == null) {
            return null;
        }
        return cardBuildExtList.stream()
                .filter(ext -> ext.accept(cardDataVO))
                .findFirst()
                .orElse(null);
    }
}
