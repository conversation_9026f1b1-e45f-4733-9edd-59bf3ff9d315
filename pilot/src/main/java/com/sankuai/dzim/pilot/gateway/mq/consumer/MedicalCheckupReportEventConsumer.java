package com.sankuai.dzim.pilot.gateway.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.dianping.vc.mq.client.api.annotation.Consumer;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.acl.HorusOcrAclService;
import com.sankuai.dzim.pilot.acl.data.OcrData;
import com.sankuai.dzim.pilot.acl.data.OcrRequest;
import com.sankuai.dzim.pilot.api.data.medical.MedicalCheckupNotifyEvent;
import com.sankuai.dzim.pilot.api.enums.medical.MedicalCheckupAnalysisFlowStatusEnum;
import com.sankuai.dzim.pilot.api.enums.medical.MedicalCheckupAnalysisTypeEnum;
import com.sankuai.dzim.pilot.api.enums.medical.MedicalCheckupEventTypeEnum;
import com.sankuai.dzim.pilot.dal.entity.pilot.medical.MedicalCheckUpReportRecordEntity;
import com.sankuai.dzim.pilot.dal.pilotdao.medical.MedicalCheckUpReportRecordDAO;
import com.sankuai.dzim.pilot.process.data.medical.MedicalCheckupAnalysisData;
import com.sankuai.dzim.pilot.process.data.medical.MedicalCheckupAnalysisItemData;
import com.sankuai.dzim.pilot.process.impl.medical.MedicalCheckupProcessServiceImpl;
import com.sankuai.dzim.pilot.process.impl.medical.workflow.MedicalCheckupAnalysisFlow;
import com.sankuai.dzim.pilot.process.impl.medical.workflow.data.MedicalCheckupFlowData;
import com.sankuai.dzim.pilot.process.impl.medical.workflow.data.MedicalCheckupFlowException;
import com.sankuai.dzim.pilot.utils.CatUtils;
import com.sankuai.dzim.pilot.utils.S3PlusUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * @author: zhouyibing
 * @date: 2024/9/14
 */
@Slf4j
@Component
public class MedicalCheckupReportEventConsumer {

    @Autowired
    private MedicalCheckUpReportRecordDAO medicalCheckUpReportRecordDAO;

    @Autowired
    private MedicalCheckupAnalysisFlow medicalCheckupAnalysisFlow;

    @Autowired
    private HorusOcrAclService horusOcrAclService;

    @Autowired
    private MedicalCheckupProcessServiceImpl medicalCheckupProcessService;

    @Autowired
    private S3PlusUtils s3PlusUtils;

    @Consumer(nameSpace = "daozong", appKey = "com.sankuai.mim.pilot", group = "pilot.medical.checkup.event.notify.consumer", topic = "pilot.medical.checkup.event.notify")
    public void recvMessage(String mafkaMessage) {
        try {
            long startTime = System.currentTimeMillis();
            //请求埋点
            CatUtils.logMetricReq("MedicalCheckupReportEventConsumer", null);
            MedicalCheckupNotifyEvent notifyEvent = JsonCodec.decode(mafkaMessage, MedicalCheckupNotifyEvent.class);

            if (!isMsgNeedProcess(notifyEvent)) {
                return;
            }

            MedicalCheckUpReportRecordEntity medicalCheckUpReportRecordEntity = medicalCheckUpReportRecordDAO.queryById(notifyEvent.getRecordId());
            if (medicalCheckUpReportRecordEntity == null || StringUtils.isBlank(medicalCheckUpReportRecordEntity.getReportUrl())) {
                return;
            }
            //分析流程
            doAnalysis(notifyEvent.getRecordId());

            //总耗时
            CatUtils.catMethodTransaction("MedicalCheckupReportEventConsumerTotal", startTime);
            LogUtils.logReturnInfo(log, TagContext.builder().bizId(String.valueOf(notifyEvent.getRecordId())).action(String.valueOf(notifyEvent.getEventType())).build(),
                    WarnMessage.build("MedicalCheckupReportEventConsumer", "体检报告解读mq消费", ""), notifyEvent, null);
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("MedicalCheckupReportEventConsumer").build(),
                    new WarnMessage("MedicalCheckupReportEventConsumer", "体检报告解读mq消费异常", null), mafkaMessage, null, e);
        }
    }

    private boolean isMsgNeedProcess(MedicalCheckupNotifyEvent notifyEvent) {
        if (notifyEvent == null) {
            return false;
        }
        if (notifyEvent.getEventType() != MedicalCheckupEventTypeEnum.UPLOAD.getType()) {
            return false;
        }
        return true;
    }

    private void doAnalysis(Long recordId) {
        try {
            //1.OCR流程
            medicalCheckupProcessService.updateRecordAnalysisFlowStatus(recordId, MedicalCheckupAnalysisFlowStatusEnum.OCR_ING.getType());
            String ocrResult = ocr(recordId);
            //2.隐私数据擦除
            String cleanOcrResult = medicalCheckupProcessService.cleanOcrResult(ocrResult);
            //3.解读流程
            medicalCheckupProcessService.updateRecordAnalysisFlowStatus(recordId, MedicalCheckupAnalysisFlowStatusEnum.LLM_ANALYSIS_ING.getType());
            MedicalCheckupFlowData analysisData = medicalCheckupAnalysisFlow.process(recordId, cleanOcrResult);
            //解读成功
            updateRecord(recordId, analysisData);
        } catch (MedicalCheckupFlowException e) {
            LogUtils.logFailLog(log, TagContext.builder().action("MedicalCheckupReportEventConsumer").build(),
                    new WarnMessage("doAnalysis", "体检报告解读流程异常", null), recordId, null, e);
            updateRecord(recordId, MedicalCheckupAnalysisTypeEnum.ANALYSIS_FAIL.getType(), e.getMessage());
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("MedicalCheckupReportEventConsumer").build(),
                    new WarnMessage("doAnalysis", "体检报告解读流程异常", null), recordId, null, e);
            //解读失败
            updateRecord(recordId, MedicalCheckupAnalysisTypeEnum.ANALYSIS_FAIL.getType(), e.getMessage());
        }
    }

    private void updateRecord(Long recordId, MedicalCheckupFlowData flowData) {
        //解读成功埋点
        CatUtils.logMetricSucc("MedicalCheckupReportEventConsumer", null);
        MedicalCheckUpReportRecordEntity dbReportRecordEntity = medicalCheckUpReportRecordDAO.queryById(recordId);

        //健康风险不为空，更新AnalysisResult
        if (!flowData.isHealthRiskEmpty()) {
            String diseaseDetailList = JSON.toJSONString(flowData.getContext().get("diseaseDetailList"));
            List<MedicalCheckupAnalysisItemData> itemDataList = StringUtils.isNotBlank(diseaseDetailList) ? JSON.parseArray(diseaseDetailList, MedicalCheckupAnalysisItemData.class) : Lists.newArrayList();
            MedicalCheckupAnalysisData medicalCheckupAnalysisData = JSON.parseObject(dbReportRecordEntity.getAnalysisResult(), MedicalCheckupAnalysisData.class);
            medicalCheckupAnalysisData = medicalCheckupAnalysisData != null ? medicalCheckupAnalysisData : new MedicalCheckupAnalysisData() ;
            medicalCheckupAnalysisData.setItemDataList(itemDataList);
            dbReportRecordEntity.setAnalysisResult(JSON.toJSONString(medicalCheckupAnalysisData));
            //有体检异常
            CatUtils.logMetricSucc("hasHealthRisk",null);
        }
        else {
            //无体检异常
            CatUtils.logMetricSucc("noHealthRisk",null);
        }

        //扩展字段
        Map<String, Object> extraData = JSON.parseObject(dbReportRecordEntity.getExtraData());
        extraData = extraData != null ? extraData : Maps.newHashMap();
        extraData.put("workNodeDataList", flowData.getWorkNodeDataList());

        //更新字段
        dbReportRecordEntity.setAnalysisStatus(MedicalCheckupAnalysisTypeEnum.ANALYSIS_SUCCESS.getType());
        dbReportRecordEntity.setExtraData(JSON.toJSONString(extraData));

        medicalCheckUpReportRecordDAO.update(dbReportRecordEntity);
    }

    private void updateRecord(Long recordId, Integer status, String errorMessage) {
        //解读失败埋点
        CatUtils.logMetricFail("MedicalCheckupReportEventConsumer", null);
        MedicalCheckUpReportRecordEntity dbReportRecordEntity = medicalCheckUpReportRecordDAO.queryById(recordId);

        //失败原因存扩展字段
        if (StringUtils.isNotBlank(errorMessage)) {
            Map<String, Object> extraData = JSON.parseObject(dbReportRecordEntity.getExtraData());
            extraData = extraData != null ? extraData : Maps.newHashMap();
            extraData.put("errorMessage", errorMessage);
            dbReportRecordEntity.setExtraData(JSON.toJSONString(extraData));
        }

        dbReportRecordEntity.setId(recordId);
        dbReportRecordEntity.setAnalysisStatus(status);

        medicalCheckUpReportRecordDAO.update(dbReportRecordEntity);
    }

    private String ocr(Long recordId) {
        long startTime = System.currentTimeMillis();
        //1.获取报告授权访问地址
        MedicalCheckUpReportRecordEntity entity = medicalCheckUpReportRecordDAO.queryById(recordId);
        List<String> objectNames = Lists.newArrayList(Splitter.on(";").split(entity.getReportUrl()));
        String objectDataType = objectNames.get(0).endsWith("pdf") ? "pdf" : "image";

        OcrRequest request = new OcrRequest();
        request.setImageUrls(objectNames);
        request.setDataType(objectDataType);

        //2.调用OCR接口
        List<OcrData> ocrDataList = horusOcrAclService.medicalHeathCheckupOcrRecognition(request);
        if (CollectionUtils.isEmpty(ocrDataList)) {
            CatUtils.logMetricReq("EmptyOCRContent", null);
            throw new MedicalCheckupFlowException("OCR识别结果为空");
        }

        //3.pdf的图片地址转存，图片则直接用原地址
        List<String> imageObjectNames = objectDataType.equals("pdf") ? buildPdfObjectNames(ocrDataList) : objectNames;

        //4.保存OCR图片地址和OCR结果，更新OCR状态
        String combineOrcResult = ocrDataList.stream().reduce(new StringBuilder(), (sb, ocrData) -> sb.append("\n").append(ocrData.getOcrResult()), StringBuilder::append).toString();
        updateOcrInfo(recordId, combineOrcResult, imageObjectNames);

        //OCR耗时
        CatUtils.catMethodTransaction("MedicalCheckupReportEventConsumerOCR", startTime);
        if (StringUtils.isEmpty(combineOrcResult) || combineOrcResult.length() <= 50) {
            CatUtils.logMetricReq("ShortOCRContent", null);
            throw new MedicalCheckupFlowException("OCR识别结果过短");
        }

        return combineOrcResult;
    }

    private List<String> buildPdfObjectNames(List<OcrData> ocrDataList) {
        return ocrDataList.get(0).getImageUrl();
    }

    private void updateOcrInfo(Long recordId, String combineOrcResult, List<String> objectUrls) {
        MedicalCheckUpReportRecordEntity entity = new MedicalCheckUpReportRecordEntity();
        entity.setId(recordId);
        entity.setOcrResult(combineOrcResult);
        entity.setReportImageUrl(StringUtils.join(objectUrls, ","));
        medicalCheckUpReportRecordDAO.update(entity);
    }


}
