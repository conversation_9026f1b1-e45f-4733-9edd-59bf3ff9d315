package com.sankuai.dzim.pilot.domain.query.data;

import com.sankuai.dzim.pilot.domain.message.Message;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class QueryHandleContext {

    /**
     * 类型
     */
    private String type;

    /**
     * query
     */
    private String query;

    /**
     * 历史记录
     */
    private List<Message> histories;

    /**
     * 额外信息
     */
    private Map<String, Object> extraInfo;
}
