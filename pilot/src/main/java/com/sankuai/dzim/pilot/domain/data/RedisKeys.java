package com.sankuai.dzim.pilot.domain.data;

import com.dianping.squirrel.client.StoreKey;

public class RedisKeys {

    public static final String AI_PHONE_CALL_RETRY_CNT_KEY = "AIPhoneCallRetryCnt";

    public static final String AI_PHONE_CALL_FREQUENCY_CNT_KEY = "AIPhoneCallFrequencyCnt";

    public static final String AIGC_WELCOME_CONTENT = "aigc_welcome_content";

    public static final String AIGC_EMBEDDING = "aigc_embedding";

    public static final String AGENT_SEARCH_ENTRANCE_KEYWORD = "agent_search_entrance_keyword";

    private static final String AI_PHONE_CALL_TASK_LOCK_KEY = "AIPhoneCallTaskLockKey";

    private static final String AI_PHONE_CALL_DETAIL_LOCK_KEY = "AIPhoneCallDetailLockKey";

    public static StoreKey getAIPhoneCallTaskLock(long taskId) {
        return new StoreKey(AI_PHONE_CALL_TASK_LOCK_KEY, taskId);
    }

    public static StoreKey getAIPhoneCallDetailLock(long detailId) {
        return new StoreKey(AI_PHONE_CALL_DETAIL_LOCK_KEY, detailId);
    }

    public static StoreKey getAIPhoneCallRetryCntKey(long taskId, long shopId) {
        return new StoreKey(AI_PHONE_CALL_RETRY_CNT_KEY, String.format("%s_%s", taskId, shopId));
    }

    public static StoreKey getAIPhoneCallFrequencyCntKey(long dpShopId) {
        return new StoreKey(AI_PHONE_CALL_FREQUENCY_CNT_KEY, dpShopId);
    }

    public static StoreKey getAigcWelcomeContent(String imUserId, int platform, String listType) {
        return new StoreKey(AIGC_WELCOME_CONTENT, imUserId, platform, listType);
    }

    public static StoreKey getAigcEmbeddingKey(String model, String content) {
        return new StoreKey(AIGC_EMBEDDING, model, content);
    }

    public static StoreKey getAgentSearchEntranceKeywordKey(String keyword) {
        return new StoreKey(AGENT_SEARCH_ENTRANCE_KEYWORD, keyword);
    }
}
