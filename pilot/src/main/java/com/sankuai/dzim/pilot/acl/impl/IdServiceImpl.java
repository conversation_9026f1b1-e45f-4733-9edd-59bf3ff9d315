package com.sankuai.dzim.pilot.acl.impl;

import com.sankuai.dzim.pilot.acl.IdService;
import com.sankuai.mpproduct.idservice.api.enums.BizSkuIdType;
import com.sankuai.mpproduct.idservice.sdk.SkuCommonConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class IdServiceImpl implements IdService {
    @Override
    public Map<Long, Long> convertSkuIdsToBizSkuIds(BizSkuIdType bizSkuIdType, List<Long> skuIds) throws TException {
        Map<Long, Long> res = new HashMap<>();
        try {
            res = SkuCommonConverter.convertSkuIdsToBizSkuIds(bizSkuIdType, skuIds);
        } catch (Exception e) {
            log.error("convertSkuIdsToBizSkuIds error, bizSkuIdType:{}, skuIds:{}", bizSkuIdType, skuIds, e);
        }
        return res;
    }
}
