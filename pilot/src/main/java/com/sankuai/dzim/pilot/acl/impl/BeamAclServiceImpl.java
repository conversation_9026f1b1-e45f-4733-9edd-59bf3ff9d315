package com.sankuai.dzim.pilot.acl.impl;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.sankuai.beam.beam.chat.api.request.DzBookCallReq;
import com.sankuai.beam.beam.chat.api.response.CallResult;
import com.sankuai.beam.beam.chat.api.service.DzBookCallService;
import com.sankuai.dzim.pilot.acl.BeamAclService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class BeamAclServiceImpl implements BeamAclService {

    @MdpThriftClient(remoteAppKey = "com.sankuai.beam.chat", timeout = 2000)
    private DzBookCallService dzBookCallService;

    @Override
    public void bookAIPhoneCallback(DzBookCallReq dzBookCallReq) {
        try {
            CallResult callResult = dzBookCallService.bookCallback(dzBookCallReq);
            log.info("bookAIPhoneCallback return, req = {}, resp = {}", JsonCodec.encodeWithUTF8(dzBookCallReq), JsonCodec.encodeWithUTF8(callResult));
        } catch (Exception e) {
            log.error("bookAIPhoneCallback error, req = {}", JsonCodec.encodeWithUTF8(dzBookCallReq), e);
        }
    }
}
