package com.sankuai.dzim.pilot.process.aiphonecall.callback.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.dzim.common.enums.ImUserType;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.meituan.nibtp.trade.client.combine.response.AgentCreateOrderResDTO;
import com.sankuai.beam.beam.chat.api.request.DzBookCallReq;
import com.sankuai.dzim.pilot.acl.BeamAclService;
import com.sankuai.dzim.pilot.acl.UserAclService;
import com.sankuai.dzim.pilot.api.enums.assistant.PlatformEnum;
import com.sankuai.dzim.pilot.domain.ShopDomainService;
import com.sankuai.dzim.pilot.domain.data.BeamFixedContentConfig;
import com.sankuai.dzim.pilot.enums.AIPhoneCallBlackTypeEnum;
import com.sankuai.dzim.pilot.enums.AIPhoneCallDialogInfoKeyEnum;
import com.sankuai.dzim.pilot.enums.AIPhoneCallSceneTypeEnum;
import com.sankuai.dzim.pilot.enums.AIPhoneCallSourceEnum;
import com.sankuai.dzim.pilot.gateway.api.assistant.data.BeamUserInfo;
import com.sankuai.dzim.pilot.gateway.api.assistant.data.SendBeamMessageReq;
import com.sankuai.dzim.pilot.process.aibook.AIBookCacheProcessService;
import com.sankuai.dzim.pilot.process.aiphonecall.AIPhoneCallProcessService;
import com.sankuai.dzim.pilot.process.aiphonecall.callback.AIPhoneCallback;
import com.sankuai.dzim.pilot.process.aiphonecall.data.AIPhoneCallBackData;
import com.sankuai.dzim.pilot.process.aireservebook.AIReserveBookProcessService;
import com.sankuai.dzim.pilot.process.aireservebook.AIReserveBookQueryService;
import com.sankuai.dzim.pilot.process.aireservebook.data.ReserveInfo;
import com.sankuai.dzim.pilot.process.aireservebook.data.ShopReserveContext;
import com.sankuai.dzim.pilot.process.aireservebook.data.UserReserveInfo;
import com.sankuai.dzim.pilot.process.aireservebook.enums.POIIndustryType;
import com.sankuai.dzim.pilot.process.aireservebook.enums.ReserveItemType;
import com.sankuai.dzim.pilot.process.aireservebook.enums.ShopReserveWayType;
import com.sankuai.dzim.pilot.utils.LionConfigUtil;
import com.sankuai.dzim.pilot.utils.ReserveInfoProcessUtils;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.wpt.user.retrieve.thrift.message.UserModel;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

@Component
@Slf4j
public class BeamReserveAgentPhoneCallBack implements AIPhoneCallback {

    public static final String SUCCESS = "1";
    public static final String FAILED = "2";
    public static final String CANCEL = "3";

    @Autowired
    private ShopDomainService shopDomainService;

    @Autowired
    private AIPhoneCallProcessService aiPhoneCallProcessService;

    @Autowired
    private BeamAclService beamAclService;

    @Autowired
    private AIReserveBookProcessService aiReserveBookProcessService;

    @Autowired
    private AIReserveBookQueryService aiReserveBookQueryService;

    @Autowired
    private AIBookCacheProcessService aiBookCacheProcessService;

    @Autowired
    private UserAclService userAclService;

    @Autowired
    private LionConfigUtil lionConfigUtil;

    public static final String BEAM_RESERVE_AGENT = "beamReserveAgent";
    public static final String TYPE = "type";
    public static final String TASK_RESERVE_TYPE = "taskReserveType";
    public static final String SEND_BEAM_MESSAGE_REQ_USER_INFO = "SendBeamMessageReqUserInfo";
    public static final String PLATFORM = "platform";
    public static final String SHOP_ID = "shopId";
    public static final String USER_ID = "userId";

    public static final String IM_USER_ID = "imUserId";

    private static ThreadPool threadPool = Rhino.newThreadPool("ReserveAgentPhoneCallBackPool",
            DefaultThreadPoolProperties.Setter().withCoreSize(50).withMaxSize(100).withMaxQueueSize(100));

    @Override
    public boolean accept(AIPhoneCallBackData callBackData) {
        // 线上DB增加source字段（赋默认值）
        if (callBackData == null || StringUtils.isEmpty(callBackData.getBizData())
                || callBackData.getAiPhoneCallSceneTypeEnum() == null || callBackData.getAiPhoneCallSourceEnum() == null) {
            return false;
        }
        AIPhoneCallSceneTypeEnum sceneTypeEnum = callBackData.getAiPhoneCallSceneTypeEnum();
        AIPhoneCallSourceEnum sourceEnum = callBackData.getAiPhoneCallSourceEnum();
        return sceneTypeEnum == AIPhoneCallSceneTypeEnum.RESERVATION && sourceEnum == AIPhoneCallSourceEnum.BEAM_FWLS_AGENT;
    }

    @Override
    public void onSuccess(AIPhoneCallBackData callBackData) {
        // 1 判断门店是否支持预约
        Map<String, Object> callDialogExtractInfo = callBackData.getCallDialogExtractInfo();
        // 1.1 若不支持预约，拉黑+失败回调
        if(!validateShopSupportReserve(callDialogExtractInfo, callBackData.getBizData())){
            DzBookCallReq dzBookCallReq = buildNotSupportReserveFailedBookCallReq(callBackData);
            beamAclService.bookAIPhoneCallback(dzBookCallReq);
            return;
        }

        // 2. 门店支持预约的情况
        // 2.1 预约失败，失败回调
        if(!isReserveSuccess(callDialogExtractInfo)){
            DzBookCallReq dzBookCallReq = buildReserveFailedBookCallReq(callBackData);
            beamAclService.bookAIPhoneCallback(dzBookCallReq);
            return;
        }
        // 2.2 预约成功，创建预约
        AgentCreateOrderResDTO resp = createOrder(callBackData);
        if(validateCreateOrderSuccess(resp)){
            // 2.2.1 创建成功，进行成功回调
            DzBookCallReq dzBookCallReq = buildSuccessBookCallReq(callBackData,resp);
            beamAclService.bookAIPhoneCallback(dzBookCallReq);
        } else {
            // 2.2.2 创建失败，失败回调
            DzBookCallReq dzBookCallReq = buildCreateReserveFailedBookCallReq(callBackData);
            beamAclService.bookAIPhoneCallback(dzBookCallReq);
        }
    }

    @Override
    public void onFail(AIPhoneCallBackData callBackData) {
        // 失败回调
        log.info("[BeamReservePhoneCallBack] onFail, input={}", JSON.toJSONString(callBackData));
        DzBookCallReq dzBookCallReq = buildNotCallThroughFailedBookCallReq(callBackData);
        beamAclService.bookAIPhoneCallback(dzBookCallReq);
    }

    @Override
    public void onDelay(AIPhoneCallBackData callBackData) {

    }

    @Override
    public void onDelayWakeUp(AIPhoneCallBackData callBackData) {

    }

    @Override
    public void onCancel(AIPhoneCallBackData callBackData) {
        // 取消回调
        log.info("[BeamReservePhoneCallBack] onCancel, input={}", JSON.toJSONString(callBackData));
        DzBookCallReq dzBookCallReq = buildCancelBookCallReq(callBackData);
        beamAclService.bookAIPhoneCallback(dzBookCallReq);
    }

    private boolean validateShopSupportReserve(Map<String, Object> callDialogExtractInfo, String bizData) {
        if (MapUtils.isEmpty(callDialogExtractInfo)) {
            return false;
        }

        // 默认支持预约
        Boolean isSupportReserve = (Boolean) Optional.ofNullable(callDialogExtractInfo.get(AIPhoneCallDialogInfoKeyEnum.IS_SUPPORT_RESERVATION.getKey())).orElse(true);
        if (isSupportReserve) {
            return true;
        }

        // 不支持预约门店加入黑名单
        JSONObject bizDataJsonObject = JsonCodec.decode(bizData, JSONObject.class);
        if (bizDataJsonObject == null) {
            return isSupportReserve;
        }
        Long shopId = bizDataJsonObject.getLong(BeamReserveAgentPhoneCallBack.SHOP_ID);
        Integer platform = bizDataJsonObject.getInteger(BeamReserveAgentPhoneCallBack.PLATFORM);
        addShop2Blacklist(shopId, platform);
        return isSupportReserve;
    }

    /**
     * 将门店加入黑名单，默认设置7天
     * @param shopId
     * @param platform
     */
    private void addShop2Blacklist(Long shopId, Integer platform) {
        if (shopId == null || platform == null) {
            return;
        }
        long dpShopId = shopDomainService.getDpShopId(shopId, platform);
        aiPhoneCallProcessService.addShopBlackList(dpShopId, AIPhoneCallBlackTypeEnum.NOT_SUPPORT_RESERVATION, getExpirationDate());
    }

    public Date getExpirationDate() {
        LocalDateTime localDateTime = LocalDateTime.now().plusDays(7);
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    private boolean isReserveSuccess(Map<String, Object> callDialogExtractInfo) {
        if (MapUtils.isEmpty(callDialogExtractInfo)) {
            return false;
        }
        return (Boolean) Optional.ofNullable(callDialogExtractInfo.get(AIPhoneCallDialogInfoKeyEnum.RESERVATION_RESULT.getKey())).orElse(false);
    }

    private DzBookCallReq buildSuccessBookCallReq(AIPhoneCallBackData callBackData,AgentCreateOrderResDTO resp) {
        if (callBackData == null) {
            return null;
        }
        DzBookCallReq dzBookCallReq = new DzBookCallReq();
        dzBookCallReq.setTaskId(String.valueOf(callBackData.getCallTaskId()));
        dzBookCallReq.setTaskStatus(SUCCESS);
        dzBookCallReq.setType(String.valueOf(resp.getOrderType()));
        dzBookCallReq.setOrderId(String.valueOf(resp.getMainOrderId()));
        return dzBookCallReq;
    }
    private DzBookCallReq buildNotCallThroughFailedBookCallReq(AIPhoneCallBackData callBackData) {
        if (callBackData == null) {
            return null;
        }
        BeamFixedContentConfig beamFixedContentConfig = lionConfigUtil.getBeamFixedContentConfig();
        DzBookCallReq dzBookCallReq = new DzBookCallReq();
        dzBookCallReq.setTaskId(String.valueOf(callBackData.getCallTaskId()));
        if (callBackData.getBlackType() != null && callBackData.getBlackType() == AIPhoneCallBlackTypeEnum.NOT_SUPPORT_RESERVATION.getType()) {
            dzBookCallReq.setFailedReason(beamFixedContentConfig.getNotSupportReserveCallbackText());
        } else {
            dzBookCallReq.setFailedReason(beamFixedContentConfig.getNotCallThroughCallbackText());
        }
        dzBookCallReq.setTaskStatus(FAILED);
        return dzBookCallReq;
    }

    private DzBookCallReq buildNotSupportReserveFailedBookCallReq(AIPhoneCallBackData callBackData) {
        if (callBackData == null) {
            return null;
        }
        BeamFixedContentConfig beamFixedContentConfig = lionConfigUtil.getBeamFixedContentConfig();
        DzBookCallReq dzBookCallReq = new DzBookCallReq();
        dzBookCallReq.setTaskId(String.valueOf(callBackData.getCallTaskId()));
        dzBookCallReq.setFailedReason(beamFixedContentConfig.getNotSupportReserveCallbackText());
        dzBookCallReq.setTaskStatus(FAILED);
        return dzBookCallReq;
    }

    private DzBookCallReq buildReserveFailedBookCallReq(AIPhoneCallBackData callBackData) {
        if (callBackData == null) {
            return null;
        }
        BeamFixedContentConfig beamFixedContentConfig = lionConfigUtil.getBeamFixedContentConfig();
        DzBookCallReq dzBookCallReq = new DzBookCallReq();
        dzBookCallReq.setTaskId(String.valueOf(callBackData.getCallTaskId()));
        dzBookCallReq.setFailedReason(beamFixedContentConfig.getReserveFailCallbackText());
        dzBookCallReq.setTaskStatus(FAILED);
        return dzBookCallReq;
    }

    private DzBookCallReq buildCreateReserveFailedBookCallReq(AIPhoneCallBackData callBackData) {
        if (callBackData == null) {
            return null;
        }
        BeamFixedContentConfig beamFixedContentConfig = lionConfigUtil.getBeamFixedContentConfig();
        DzBookCallReq dzBookCallReq = new DzBookCallReq();
        dzBookCallReq.setTaskId(String.valueOf(callBackData.getCallTaskId()));
        dzBookCallReq.setFailedReason(beamFixedContentConfig.getCreateReserveFailCallbackText());
        dzBookCallReq.setTaskStatus(FAILED);
        return dzBookCallReq;
    }

    private DzBookCallReq buildCancelBookCallReq(AIPhoneCallBackData callBackData) {
        if (callBackData == null) {
            return null;
        }
        DzBookCallReq dzBookCallReq = new DzBookCallReq();
        dzBookCallReq.setTaskId(String.valueOf(callBackData.getCallTaskId()));
        dzBookCallReq.setTaskStatus(CANCEL);
        return dzBookCallReq;
    }

    private boolean validateCreateOrderSuccess(AgentCreateOrderResDTO createOrderRes) {
        return createOrderRes != null && createOrderRes.isSuccess() && createOrderRes.getMainOrderId() != null && createOrderRes.getMainOrderId() > 0;
    }

    private AgentCreateOrderResDTO createOrder(AIPhoneCallBackData callBackData) {
        Map<String, Object> bizData = JSON.parseObject(callBackData.getBizData(), Map.class);

        // 1. 获取前置信息
        String imUserId = MapUtils.getString(bizData, BeamReserveAgentPhoneCallBack.IM_USER_ID);
        Long shopId = MapUtils.getLong(bizData, BeamReserveAgentPhoneCallBack.SHOP_ID);
        int platform = MapUtils.getInteger(bizData, BeamReserveAgentPhoneCallBack.PLATFORM);
        UserReserveInfo userReserveInfo = aiBookCacheProcessService.getBeamAgentReserveAndBookInfo(imUserId, shopId, platform);
        if (userReserveInfo == null) {
            return null;
        }
        ShopReserveWayType shopReserveWayType = getShopReserveWayType(userReserveInfo);
        CompletableFuture<ShopReserveContext> shopReserveContextF = getShopReserveContext(shopId, platform, shopReserveWayType);
        CompletableFuture<UserModel> userModelF = getUserModelFuture(imUserId);
        ShopReserveContext shopReserveContext = shopReserveContextF == null ? null : shopReserveContextF.join();
        UserModel userModel = userModelF == null ? null : userModelF.join();
        // 1.1 商家备注
        String merchantRemark = getMerchantRemark(callBackData);
        updateUserReserveInfo(userReserveInfo, callBackData.getCallDialogExtractInfo(), merchantRemark);
        // 1.2 构造beam请求参数
        SendBeamMessageReq beamMessageReq = buildSendBeamMessageReq(MapUtils.getString(bizData, BeamReserveAgentPhoneCallBack.SEND_BEAM_MESSAGE_REQ_USER_INFO));
        // 2. 发起预约
        AgentCreateOrderResDTO createOrderRes = aiReserveBookProcessService.createBeamReserve(beamMessageReq, userModel, shopReserveContext, userReserveInfo, merchantRemark, false, Lists.newArrayList());
        if (validateCreateOrderSuccess(createOrderRes)) {
            // 2.1 预约成功后刷新缓存
            userReserveInfo.setReserveId(createOrderRes.getMainOrderId());
            refreshUserReserveInfo(userReserveInfo);
        }
        return createOrderRes;
    }

    private SendBeamMessageReq buildSendBeamMessageReq(String sendBeamMessageReqUserInfo) {
        // 下单只用到了user_info构造webParam
        BeamUserInfo beamUserInfo = JsonCodec.decode(sendBeamMessageReqUserInfo, BeamUserInfo.class);
        SendBeamMessageReq sendBeamMessageReq = new SendBeamMessageReq();
        sendBeamMessageReq.setUser_info(beamUserInfo);
        return sendBeamMessageReq;
    }

    private String getMerchantRemark(AIPhoneCallBackData callBackData) {
        Map<String, Object> callDialogExtractInfo = callBackData.getCallDialogExtractInfo();
        return (String) Optional.ofNullable(callDialogExtractInfo.get(AIPhoneCallDialogInfoKeyEnum.RESERVATION_SUCCESS_REMARK.getKey())).orElse("");
    }

    private CompletableFuture<UserModel> getUserModelFuture(String imUserId) {
        return CompletableFuture.supplyAsync(() -> userAclService.queryUserModel(imUserId), threadPool.getExecutor());
    }

    private void updateUserReserveInfo(UserReserveInfo userReserveInfo, Map<String, Object> callDialogExtractInfo, String merchantRemark) {
        if (userReserveInfo == null || MapUtils.isEmpty(callDialogExtractInfo)) {
            return;
        }
        ReserveInfo reserveInfo = userReserveInfo.getCollectedReserveInfo();
        // 修改预约开始时间
        String startTimeStr = (String) Optional.ofNullable(callDialogExtractInfo.get(AIPhoneCallDialogInfoKeyEnum.RESERVATION_BEGIN_TIME.getKey())).orElse(null);
        if (StringUtils.isNotBlank(startTimeStr)) {
            ReserveInfoProcessUtils.updateItem(reserveInfo, ReserveItemType.START_TIME.getKey(), startTimeStr);
        }
        // 修改预约时长
        String durationStr = String.valueOf(Optional.ofNullable(callDialogExtractInfo.get(AIPhoneCallDialogInfoKeyEnum.RESERVATION_DURATION.getKey())).orElse(""));
        if (StringUtils.isNotBlank(durationStr)) {
            ReserveInfoProcessUtils.updateItem(reserveInfo, ReserveItemType.DURATION.getKey(), durationStr);
        }
        // 增加商家备注
        if (StringUtils.isNotBlank(merchantRemark)) {
            userReserveInfo.setMerchantRemark(merchantRemark);
        }
    }

    private ShopReserveWayType getShopReserveWayType(UserReserveInfo userReserveInfo) {
        if (userReserveInfo == null) {
            return null;
        }
        return ShopReserveWayType.getByType(userReserveInfo.getShopReserveWayType());
    }

    private CompletableFuture<ShopReserveContext> getShopReserveContext(Long shopId, int platform, ShopReserveWayType shopReserveWayType) {
        return CompletableFuture.supplyAsync(() -> {
                    if (shopId == null || shopId <= 0 || shopReserveWayType == null) {
                        return null;
                    }
                    DpPoiDTO dpPoiDTO = aiReserveBookQueryService.queryDpPoiDTO(shopId, platform);
                    if (dpPoiDTO == null) {
                        return null;
                    }
                    Integer secondBackCategoryId = aiReserveBookProcessService.extractMainSecondBackCategoryId(dpPoiDTO);
                    POIIndustryType poiIndustryType = POIIndustryType.getBySecondBackCategoryId(secondBackCategoryId);
                    return ShopReserveContext.buildShopReserveContext(dpPoiDTO, poiIndustryType, shopReserveWayType);
                }, threadPool.getExecutor())
                .exceptionally(e -> {
                    log.error("[ReserveAgentPhoneCallBack] getShopReserveContext error, shopId:{}, platform:{}, shopReserveWayType:{}", shopId, platform, shopReserveWayType, e);
                    return null;
                });
    }

    private void refreshUserReserveInfo(UserReserveInfo userReserveInfo) {
        String imUserId = userReserveInfo.getImUserId();
        int platform = getPlatform(imUserId);
        Long shopId = userReserveInfo.getShopId();
        aiBookCacheProcessService.setBeamAgentReserveAndBookInfo(imUserId, shopId, platform, userReserveInfo);
    }

    private int getPlatform(String imUserId) {
        return imUserId.startsWith(ImUserType.MT.getPrefix()) ? PlatformEnum.MT.getType() : PlatformEnum.DP.getType();
    }
}
