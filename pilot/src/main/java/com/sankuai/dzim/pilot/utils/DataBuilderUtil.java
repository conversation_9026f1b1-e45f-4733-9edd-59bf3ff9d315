package com.sankuai.dzim.pilot.utils;

import com.sankuai.dzim.message.common.utils.ImAccountTypeUtils;
import com.sankuai.dzim.message.dto.sendmessage.SingleSendMessageRequest;
import com.sankuai.dzim.message.enums.MessageCreatorEnum;
import com.sankuai.dzim.message.enums.MessageSendDirectionEnum;
import com.sankuai.dzim.message.enums.MessageSendEntranceEnum;
import com.sankuai.dzim.pilot.gateway.mq.data.MessageSendData;

public class DataBuilderUtil {

    public static SingleSendMessageRequest buildSendMessageRequest(MessageSendData messageSendData, int messageType, String message) {
        SingleSendMessageRequest sendMessageRequest = new SingleSendMessageRequest();
        sendMessageRequest.setCUserType(ImAccountTypeUtils.parseAccountType(messageSendData.getImUserId()));
        sendMessageRequest.setCUserId(ImAccountTypeUtils.getClientId(messageSendData.getImUserId()));
        sendMessageRequest.setBUserType(ImAccountTypeUtils.parseAccountType(messageSendData.getImShopId()));
        sendMessageRequest.setShopType(messageSendData.getShopType());

        sendMessageRequest.setMerchantId(String.valueOf(ImAccountTypeUtils.getDpShopId(messageSendData.getImShopId())));
        sendMessageRequest.setDirection(MessageSendDirectionEnum.MERCHANT_SEND.value);
        sendMessageRequest.setEntrance(MessageSendEntranceEnum.DEFAULT.value);
        sendMessageRequest.setCreator(MessageCreatorEnum.LLM_INTELLIGENT_ROBOT.value);
        sendMessageRequest.setMessageType(messageType);
        sendMessageRequest.setMessage(message);
        return sendMessageRequest;
    }
}
