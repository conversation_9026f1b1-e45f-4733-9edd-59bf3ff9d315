package com.sankuai.dzim.pilot.enums;

import lombok.Getter;

@Getter
public enum BeamResourceTypeEnum {

    FWLS_PRE_PURCHASE_DEAL("FWLS_PRE_PURCHASE_DEAL", "团购卡片"),

    FWLS_DEAL_PREVIEW_ORDER("FWLS_DEAL_PREVIEW_ORDER", "团购订单预览卡片"),

    FWLS_DEAL_CREATE_ORDER("FWLS_DEAL_CREATE_ORDER", "团购下单交易事件"),

    FWLS_PRE_PURCHASE_RESERVE("FWLS_PRE_PURCHASE_RESERVE", "预订卡片"),

    FWLS_BOOK_CREATE_ORDER("FWLS_BOOK_CREATE_ORDER", "预约创建成功卡片"),

    FWLS_AI_CALL_CREATE_ORDER("FWLS_AI_CALL_CREATE_ORDER", "外呼待确认卡片"),

    FWLS_BOOK_DEAL_PREVIEW_ORDER("FWLS_BOOK_DEAL_PREVIEW_ORDER", "团购预约预览卡片"),

    FWLS_BOOK_SHOP_PREVIEW_ORDER("FWLS_BOOK_SHOP_PREVIEW_ORDER", "门店预约预览卡片"),

    ;

    private String key;
    private String desc;

    BeamResourceTypeEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }
}
