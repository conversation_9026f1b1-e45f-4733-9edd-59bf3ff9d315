package com.sankuai.dzim.pilot.process.localplugin;

import com.dianping.cat.Cat;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.inf.AthenaInf;
import com.sankuai.dzim.pilot.chain.data.AIAnswerData;
import com.sankuai.dzim.pilot.domain.annotation.LocalPlugin;
import com.sankuai.dzim.pilot.gateway.api.assistant.data.SendBeamMessageReq;
import com.sankuai.dzim.pilot.process.EsSearchProcessService;
import com.sankuai.dzim.pilot.process.localplugin.param.BeamShopProductRetrievalParam;
import com.sankuai.dzim.pilot.process.localplugin.param.BeamSupplyDetailQueryParam;
import com.sankuai.dzim.pilot.process.localplugin.param.ShopProductSearchParam;
import com.sankuai.dzim.pilot.utils.PluginContextUtil;
import com.sankuai.dzim.pilot.utils.SupplyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @since 2025/04/17 11:19
 * 供给详情查询
 * 1. 根据各id查询对应供给详情，并附有5条评价信息
 * 2. 没有门店id，有门店名，如果门店名查到唯一结果，则使用对应门店id查询，否则交给找店找品
 */
@Component
@Slf4j
public class BeamSupplyDetailQueryPlugin {

    @Autowired
    private SupplyUtils supplyUtils;
    @Autowired
    private EsSearchProcessService esSearchProcessService;
    @Autowired
    private ShopProductSearchPlugin shopProductSearchPlugin;

    private static ThreadPool supplyInfoQueryPool = Rhino.newThreadPool("SupplyInfoQueryPool",
            DefaultThreadPoolProperties.Setter().withCoreSize(20).withMaxSize(50).withMaxQueueSize(100));

    @LocalPlugin(name = "SupplyInfoQueryTool", description = "查询单个门店或单个商品的具体信息", returnDirect = false)
    public AIAnswerData querySupplyDetail(BeamSupplyDetailQueryParam param) {
        log.info("调用插件-SupplyInfoQueryTool，入参={}", param);
        SendBeamMessageReq sendBeamMessageReq = PluginContextUtil.getBeamRequestContext(param);
        long shopId = NumberUtils.toLong(sendBeamMessageReq.getContext().getPoi_id());
        AIAnswerData answerData = new AIAnswerData();
        answerData.setAnswer("没有找到目标供给,请向用户确认具体咨询的供给是哪一个");
        try {
            // 查门店
            CompletableFuture<String> shopDetailFuture = CompletableFuture.supplyAsync(() -> queryShopDetail(param, shopId), supplyInfoQueryPool.getExecutor()).exceptionally(e -> {
                log.error("queryShopDetail failed, param: {}, shopId: {}", param, shopId, e);
                return StringUtils.EMPTY;
            });
            // 查商品
            CompletableFuture<String> productDetailFuture = CompletableFuture.supplyAsync(() -> queryDealDetail(param), supplyInfoQueryPool.getExecutor()).exceptionally(e -> {
                log.error("queryDealDetail failed, param: {}, shopId: {}", param, shopId, e);
                return StringUtils.EMPTY;
            });

            String shopDetail = shopDetailFuture.join();
            String productDetail = productDetailFuture.join();
            log.info("SupplyInfoQueryTool, querySupplyDetail shopDetail = {}", shopDetail);
            log.info("SupplyInfoQueryTool, querySupplyDetail productDetail = {}", productDetail);

            StringBuilder res = new StringBuilder();
            if (StringUtils.isNotBlank(shopDetail)) {
                res.append("相关门店信息为: \n").append(shopDetail).append("\n");
            }
            if (StringUtils.isNotBlank(productDetail)) {
                res.append("相关商品信息为: \n").append(productDetail).append("\n");
            }

            if (StringUtils.isBlank(res.toString())) {
                return answerData;
            }
            res.append("请基于以上信息无法用户的问题,你的答案和格式必须来自于上面的信息,请不要随意捏造,如果无法回答用户的问题,请礼貌的告知用户暂时回答不了,可以在门店详情页内直接向商家咨询.");
            answerData.setAnswer(res.toString());

        } catch (Exception e) {
            log.error("调用插件-SupplyInfoQueryTool, param:{}", param, e);
            answerData.setAnswer("抱歉，我暂时无法提供该供给的详细信息。");
        }
        log.info("调用插件-SupplyInfoQueryTool，结果={}", answerData.getAnswer().replaceAll("\n", "↵"));
        return answerData;
    }


    private String queryShopDetail(BeamSupplyDetailQueryParam param, long shopId) {
        if (shopId <= 0) {
            return null;
        }

        if (shopId > 0) {
            String info = queryShopDetail(shopId, param.getSemanticCondition(), param);
            if (StringUtils.isNotBlank(info)) {
                return info;
            }
        }
        return null;
    }

    private String queryShopDetail(long mtShopId, String condition, BeamSupplyDetailQueryParam param) {
        Map<Long, String> mtShopId2ShopNL = supplyUtils.batchTransferShop2NL(Lists.newArrayList(mtShopId));
        if (MapUtils.isEmpty(mtShopId2ShopNL)) {
            return StringUtils.EMPTY;
        }
        StringBuilder res = new StringBuilder();
        String shopInfo = mtShopId2ShopNL.get(mtShopId);
        if (StringUtils.isBlank(shopInfo)) {
            return StringUtils.EMPTY;
        }
        res.append(shopInfo);
        res.append("\n");
//        if (StringUtils.isNotBlank(condition)) {
//            List<String> reviews = esSearchProcessService.searchShopReview(mtShopId, condition, 5);
//            if (CollectionUtils.isNotEmpty(reviews)) {
//                res.append("相关的门店评价信息为:\n");
//                res.append(String.join("\n", reviews));
//                res.append("\n\n");
//            }
//        }

        // 查询高销团购
        AIAnswerData aiAnswerData = shopProductSearchPlugin.searchShopProducts(buildShopProductSearchParam(mtShopId, param));
        if (aiAnswerData != null && StringUtils.isNotBlank(aiAnswerData.getAnswer())) {
            res.append("门店相关的商品信息为:\n").append(aiAnswerData.getAnswer()).append("\n");
        }
        return res.toString();
    }

    private ShopProductSearchParam buildShopProductSearchParam(long mtShopId, BeamSupplyDetailQueryParam param) {
        ShopProductSearchParam shopProductSearchParam = new ShopProductSearchParam();
        shopProductSearchParam.setShopId(mtShopId);
        shopProductSearchParam.setProductSemanticCondition(param.getSemanticCondition());
        shopProductSearchParam.setAiServiceContext(param.getAiServiceContext());
        return shopProductSearchParam;
    }

    private String queryDealDetail(BeamSupplyDetailQueryParam param) {
        if (param.getProductId() <= 0) {
            return null;
        }
        StringBuilder res = new StringBuilder();
        Map<Long, String> dealId2DealNL = supplyUtils.batchTransferDeal2NL(Lists.newArrayList(param.getProductId()));
        if (MapUtils.isEmpty(dealId2DealNL)) {
            return StringUtils.EMPTY;
        }
        String shopInfo = dealId2DealNL.get(param.getProductId());
        if (StringUtils.isBlank(shopInfo)) {
            return StringUtils.EMPTY;
        }
        res.append(shopInfo);
        res.append("\n");
//        if (StringUtils.isNotBlank(param.getSemanticCondition())) {
//            List<String> reviews = esSearchProcessService.searchProductReview(param.getProductId(), param.getSemanticCondition(), 5);
//            if (CollectionUtils.isNotEmpty(reviews)) {
//                res.append("相关的评价信息为:\n");
//                res.append(String.join("\n", reviews));
//                res.append("\n\n");
//            }
//        }
        return res.toString();
    }


}
