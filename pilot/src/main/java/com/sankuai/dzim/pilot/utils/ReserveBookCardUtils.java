package com.sankuai.dzim.pilot.utils;

import com.alibaba.fastjson.JSON;
import com.dianping.dzim.common.enums.ImUserType;
import com.google.common.collect.Maps;
import com.sankuai.dzim.pilot.api.data.AIAnswerTypeEnum;
import com.sankuai.dzim.pilot.api.enums.assistant.PlatformEnum;
import com.sankuai.dzim.pilot.chain.data.AIAnswerData;
import com.sankuai.dzim.pilot.process.aireservebook.data.*;
import com.sankuai.dzim.pilot.process.aireservebook.enums.*;
import com.sankuai.dzim.pilot.process.localplugin.data.ReserveReplyAnswerData;
import com.sankuai.dzim.pilot.scene.data.AssistantSceneContext;
import com.sankuai.dzim.pilot.utils.context.RequestContext;
import com.sankuai.dzim.pilot.utils.context.RequestContextConstants;
import org.apache.commons.lang.StringUtils;
import org.joda.time.LocalDateTime;

import java.util.Map;

/**
 * @author: wuwenqiang
 * @create: 2025-05-01
 * @description:
 */
public class ReserveBookCardUtils {

    /**
     * 预约确认卡片
     * @param userReserveInfo
     * @param shopReserveContext
     * @param context
     * @param title
     * @param confirmText
     * @return
     */
    public static String generateConfirmCard(UserReserveInfo userReserveInfo, ShopReserveContext shopReserveContext, AssistantSceneContext context, String title, String confirmText, String confirmSendText, String confirmButtonText) {
        String cardTag = String.format("<BookConfirm>%s_%s_%s</BookConfirm>", context.getUserId(), userReserveInfo.getShopId(), getPlatform(context));
        Map<String, Object> extraInfo = Maps.newHashMap();
        BookConfirmCard bookConfirmCard = buildBookConfirmCard(userReserveInfo, shopReserveContext, title, confirmText, confirmSendText, confirmButtonText);
        extraInfo.put("bookConfirm", bookConfirmCard);
        ReserveReplyAnswerData result = ReserveReplyAnswerData.generateCard(cardTag, extraInfo);
        return JSON.toJSONString(result);
    }

    private static BookConfirmCard buildBookConfirmCard(UserReserveInfo userReserveInfo, ShopReserveContext shopReserveContext, String title, String confirmText, String confirmSendText, String confirmButtonText) {
        BookConfirmCard card = new BookConfirmCard();
        card.setTitle(title);
        // 预约门店
        card.setShopId(userReserveInfo.getShopId());
        card.setShopName(userReserveInfo.getShopName());
        // 预约时间
        card.setBookTime(generateStartTime(userReserveInfo, shopReserveContext.getShopReserveWayType()));
        // 预约项目
        String project = ReserveInfoProcessUtils.getStringItem(userReserveInfo.getCollectedReserveInfo(), ReserveItemType.PROJECT.getKey());
        Long duration = ReserveInfoProcessUtils.getLongItem(userReserveInfo.getCollectedReserveInfo(), ReserveItemType.DURATION.getKey());
        card.setProject(mergeProjectAndDuration(shopReserveContext.getPoiIndustryType(), project, duration));
        // 人数
        Integer personNum = ReserveInfoProcessUtils.getIntItem(userReserveInfo.getCollectedReserveInfo(), ReserveItemType.PERSON_NUM.getKey());
        personNum = personNum == null || personNum <= 0 ? 1 : personNum;
        card.setPeopleNum(String.format("%d人", personNum));
        // 联系方式
        String mobile = userReserveInfo.getCollectedReserveInfo().getPhone();
        card.setPhone(mobile);
        // 备注
        String remark = userReserveInfo.getCollectedReserveInfo().getRemark();
        String technician = ReserveInfoProcessUtils.getStringItem(userReserveInfo.getCollectedReserveInfo(), ReserveItemType.TECHNICIAN.getKey());

        // 生成备注和技师信息
        String finalRemark = generateRemarkWithTechnician(remark, technician, shopReserveContext.getPoiIndustryType());
        if (StringUtils.isNotBlank(finalRemark)) {
            card.setRemark(finalRemark);
        }
        AssistantSceneContext context = RequestContext.getAttribute(RequestContextConstants.ASSISTANT_CONTEXT);
        card.setConfirmSendText(confirmSendText.contains("%s") && context != null ? String.format(confirmSendText, context.getReplyMessageId()) : confirmSendText);
        card.setConfirmText(confirmText);
        card.setConfirmButtonText(confirmButtonText);
        return card;
    }

    private static String generateStartTime(UserReserveInfo userReserveInfo, ShopReserveWayType shopReserveWayType) {
        // 在线系统
        String startTimeStr = ReserveInfoProcessUtils.getStringItem(userReserveInfo.getCollectedReserveInfo(), ReserveItemType.START_TIME.getKey());
        LocalDateTime startTime = TimeUtil.convertStr2LocalDateTime(startTimeStr);
        if (startTime == null) {
            return StringUtils.EMPTY;
        }
        if (ShopReserveWayType.ONLINE.equals(shopReserveWayType)) {
            return String.format("%s 到店", TimeUtil.formatTimeToFriendlyString(startTime));
        }
        // 电话外呼
        String startTimeDescStr = ReserveInfoProcessUtils.getStringItem(userReserveInfo.getCollectedReserveInfo(), ReserveItemType.START_TIME_DESC.getKey());
        if (StringUtils.isBlank(startTimeDescStr)) {
            return String.format("%s 到店", TimeUtil.formatTimeToFriendlyString(startTime));
        }
        String date = startTime.toString("yyyy-MM-dd");
        return String.format("%s %s 到店", date, startTimeDescStr);
    }

    private static String generateRemarkWithTechnician(String remark, String technician, POIIndustryType poiIndustryType) {
        StringBuilder sb = new StringBuilder();

        // 优先加入原始备注
        if (StringUtils.isNotBlank(remark)) {
            sb.append(remark);
        }

        // 如果有技师信息，判断行业类型
        if (StringUtils.isNotBlank(technician)) {
            // 只对美发和足疗行业处理技师信息
            String technicianPrefix = getTechPrefix(poiIndustryType);
            if (StringUtils.isBlank(technicianPrefix)) {
                return sb.length() > 0 ? sb.toString() : null;
            }
            String technicianInfo = technicianPrefix + technician;

            // 如果已有备注，添加分隔符
            if (sb.length() > 0) {
                sb.append("，");
            }
            sb.append(technicianInfo);
        }

        // 返回合成后的备注，如果没有内容则返回null
        return sb.length() > 0 ? sb.toString() : null;
    }

    private static String getTechPrefix(POIIndustryType poiIndustryType) {
        if (POIIndustryType.HAIR.equals(poiIndustryType)) {
            return "发型师是";
        } else if (POIIndustryType.FOOT_MASSAGE.equals(poiIndustryType)) {
            return "技师是";
        }
        return null;
    }

    private static String mergeProjectAndDuration(POIIndustryType poiIndustryType, String project, Long duration) {
        if (poiIndustryType == null || duration == null || duration <= 0) {
            return project;
        }
        // 美发不需要拼接时长
        if (POIIndustryType.HAIR.equals(poiIndustryType)) {
            return project;
        }
        // project不包含xx分钟，xxmin，xx小时,xxh，需要在project加上duration分钟
        if (!project.matches(".*\\d+分钟.*") && !project.matches(".*\\d+min.*") && !project.matches(".*\\d+小时.*") && !project.matches(".*\\d+h.*")) {
            return String.format("%d分钟 %s", duration, project);
        }
        return project;
    }

    private static int getPlatform(AssistantSceneContext context) {
        return context.getUserId().startsWith(ImUserType.MT.getPrefix()) ? PlatformEnum.MT.getType() : PlatformEnum.DP.getType();
    }

    /**
     * 构建询问中卡片
     * @param taskId
     * @param loadingText
     * @return
     */
    public static String generateLoadingCard(Long taskId, String loadingText) {
        String cardTag = String.format("<BookCard>%s</BookCard>", taskId);
        Map<String, Object> extraInfo = Maps.newHashMap();
        extraInfo.put("BookCard", buildBookCard(loadingText));
        ReserveReplyAnswerData result = ReserveReplyAnswerData.generateCard(cardTag, extraInfo);
        return JSON.toJSONString(result);
    }

    private static BookCard buildBookCard(String loadingText) {
        BookCard bookCard = new BookCard();
        bookCard.setTaskStatus(TaskStatusType.HAS_TASK_ASKING.getType());
        BookProcessData bookProcessData = new BookProcessData();
        bookProcessData.setLoadingText(loadingText);
        bookCard.setBookProcessData(bookProcessData);
        return bookCard;
    }

    public static AIAnswerData generateDirectAnswer(String answer) {
        AIAnswerData aiAnswerData = new AIAnswerData();
        aiAnswerData.setAiAnswerType(AIAnswerTypeEnum.TEXT.getType());
        ReserveReplyAnswerData result = new ReserveReplyAnswerData();
        result.setReserveReplyType(ReserveReplyType.DIRECT.getType());
        result.setAnswer(answer);
        aiAnswerData.setAnswer(JSON.toJSONString(result));
        return aiAnswerData;
    }

    public static AIAnswerData generateModelGenerateAnswer(String answer) {
        AIAnswerData aiAnswerData = new AIAnswerData();
        aiAnswerData.setAiAnswerType(AIAnswerTypeEnum.TEXT.getType());
        ReserveReplyAnswerData result = new ReserveReplyAnswerData();
        result.setReserveReplyType(ReserveReplyType.GENERATE.getType());
        result.setAnswer(answer);
        aiAnswerData.setAnswer(JSON.toJSONString(result));
        return aiAnswerData;
    }
}
