package com.sankuai.dzim.pilot.chain.data;

import com.google.common.collect.Maps;
import lombok.Data;
import org.apache.commons.collections4.MapUtils;

import java.util.List;
import java.util.Map;

@Data
public class RetrievalComponent {

    /**
     * 检索组件名称
     */
    private String name;

    private List<String> retrievals;

    /**
     * 知识类型和主题ID映射map
     */
    private Map<Integer, String> type2SubjectIdMap;

    /**
     * topk条知识
     */
    private int topK;

    /**
     * 额外信息
     */
    private Map<String, String> extraInfo;

    private String startStatus;

    private String endStatus;

    public void addExtraInfo(String key, String value) {
        if (MapUtils.isEmpty(extraInfo)) {
            extraInfo = Maps.newHashMap();
        }
        extraInfo.put(key, value);
    }
}
