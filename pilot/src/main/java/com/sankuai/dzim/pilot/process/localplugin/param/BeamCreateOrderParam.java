package com.sankuai.dzim.pilot.process.localplugin.param;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import lombok.Data;

/**
 * Beam团购下单工具
 *
 * <AUTHOR>
 * @date 2025/5/6
 */
@Data
public class BeamCreateOrderParam extends Param {

    @JsonPropertyDescription("If the user wants to confirm the preview of the order, extract the preview id from context. If the preview id cannot be recognized, it defaults to 0.")
    @JsonProperty(required = false)
    private Long previewId;

    @JsonPropertyDescription("If the user wants to pay for an order, extract the order id from context. If the order id cannot be recognized, it defaults to 0.")
    @JsonProperty(required = false)
    private Long orderId;

}
