package com.sankuai.dzim.pilot.acl;

import com.meituan.nibtp.trade.client.combine.bean.WebParamDTO;
import com.meituan.nibtp.trade.client.combine.requset.*;
import com.meituan.nibtp.trade.client.combine.response.*;
import com.sankuai.dzim.pilot.gateway.api.assistant.data.BeamUserInfo;
import com.sankuai.dzim.pilot.gateway.api.assistant.data. SendBeamMessageReq;
import com.sankuai.dealuser.price.display.api.model.BatchPriceRequest;
import com.sankuai.dzim.pilot.scene.task.data.EnvContext;
import com.sankuai.dzim.pilot.utils.data.Response;
import com.sankuai.wpt.user.retrieve.thrift.message.UserModel;

import java.util.List;

/**
 * Agent交易相关接口防腐层
 *
 * @author: zhouyibing
 * @date: 2025/4/24
 */
public interface AgentTradeAclService {

    /**
     * 订单预览
     */
    AgentPreviewResDTO preview(AgentPreviewReqDTO req);

    /**
     * 订单查询
     */
    AgentQueryOrderResDTO queryOrder(AgentQueryOrderReqDTO req);


    /**
     * 单个订单查询接口
     *
     * @param mtUserId
     * @param orderId
     * @return
     */
    AgentOrderDTO queryOrderSingle(Long mtUserId, Long orderId);


    /**
     * 下单
     *
     * @param req
     * @return
     */
    AgentCreateOrderResDTO createOrder(AgentCreateOrderReqDTO req);

    /**
     * 退款申请
     */
    AgentRefundApplyResDTO refundApply(AgentRefundApplyReqDTO req);

    /**
     * 查询预订单详情
     * @param webParam
     * @param orderReq
     * @return
     */
    Response<List<AgentGeneralBookingOrderDTO>> queryGeneralBookingOrder(WebParamDTO webParam, List<AgentQueryGeneralBookingOrderItemReqDTO> orderReq);

    /**
     * 创建预约/预订订单
     * @param webParam
     * @param bookReq
     * @return
     */
    AgentCreateOrderResDTO createGeneralBookingOrder(WebParamDTO webParam, List<AgentGeneralBookingReqDTO> bookReq);

    /**
     * 取消订单（团购订单、预约/预订订单）
     * @param webParam
     * @param refundApplyReq
     * @return
     */
    Response<List<AgentRefundApplyOrderResDTO>> refundApply(WebParamDTO webParam, List<AgentRefundApplyOrderReqDTO> refundApplyReq);

    /**
     * 构建环境参数
     * @return
     */
    WebParamDTO buildWebParam(UserModel userModel, EnvContext envContext);

    /**
     * Bean场景，构建环境参数
     * @return
     */
    WebParamDTO buildWebParam(SendBeamMessageReq sendBeamMessageReq);

    /**
     * Bean场景，构建环境参数
     * @return
     */
    WebParamDTO buildWebParamFromUserInfo(BeamUserInfo beamUserInfo);

    /**
     * 构建领券参数
     * @return
     */
    List<AssignPromotionItemDTO> buildAssignPromotionItemList(BatchPriceRequest batchPriceRequest);
}
