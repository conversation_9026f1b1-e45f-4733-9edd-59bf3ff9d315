package com.sankuai.dzim.pilot.process.data;

import com.sankuai.it.xcontract.easypoi.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class GenerativeSearchGenerateData {

    /**
     * GenerativeSearchTypeEnum
     */
    @Excel(name = "bizType")
    private int bizType;

    /**
     * 要生成答案的内容
     */
    @Excel(name = "question")
    private String question;

    /**
     * 数据类型 0-普通 1-基于推荐问题列表生成答案,该类型不生成关联项目
     */
    @Excel(name = "dataType")
    @Deprecated
    private int dataType;

    @Excel(name = "questionType")
    private int questionType;

    @Excel(name = "title")
    private String title;

    @Excel(name = "answer")
    private String answer;

    @Excel(name = "projects")
    private String projects;

    @Excel(name = "originProjects")
    private String originProjects;

    @Excel(name = "modifyProjects")
    private String modifyProjects;

    @Excel(name = "relatedQuestion")
    private String relatedQuestion;

    @Excel(name = "remark")
    private String remark;

    @Excel(name = "tags")
    private String tags;

    @Excel(name = "answerId")
    private long answerId;
}
