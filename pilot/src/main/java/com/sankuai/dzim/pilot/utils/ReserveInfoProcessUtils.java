package com.sankuai.dzim.pilot.utils;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dzim.pilot.process.aireservebook.data.*;
import com.sankuai.dzim.pilot.process.aireservebook.enums.POIIndustryType;
import com.sankuai.dzim.pilot.process.aireservebook.enums.ReserveItemType;
import com.sankuai.dzim.pilot.process.aireservebook.enums.ShopReserveWayType;
import com.sankuai.dzim.pilot.process.aireservebook.enums.ShopStartTimeIntervalType;
import com.sankuai.it.iam.common_base.utils.StringUtil;
import com.sankuai.spt.statequery.api.enums.TimeSliceTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;
import org.joda.time.LocalTime;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: wuwenqiang
 * @create: 2025-04-24
 * @description: 预约信息处理工具类
 */
@Slf4j
public class ReserveInfoProcessUtils {

    public static final String RESERVE_PHONE_CALL_TYPE = "reservePhoneCallType";

    //beam中要素收集阶段不需要收集人数，但是下单需要人数的行业，给默认值1，卡片不展示
    public static final List<POIIndustryType> BEAM_TRADE_NEED_DEFAULT_PERSON_NUM = Lists.newArrayList(POIIndustryType.BAR, POIIndustryType.CHESS, POIIndustryType.PET_SHOP, POIIndustryType.KTV, POIIndustryType.BALL, POIIndustryType.GYM); ;

    //beam要素收集阶段需要收集人数，没有则默认1的行业
    public static final List<POIIndustryType> BEAM_FORCE_NEED_DEFAULT_PERSON_NUM = Lists.newArrayList(POIIndustryType.HAIR, POIIndustryType.FOOT_MASSAGE);

    /**
     * 判断收集的预约要素中缺少哪些要素
     *
     * @param collectedReserveInfo 收集的预约要素
     * @param standardReserveItems 标准预约要素
     * @return 返回空则说明收集的预约要素齐全，否则将输出缺少的要素
     */
    public static List<StandardReserveItem> checkLackReserveItems(ReserveInfo collectedReserveInfo, List<StandardReserveItem> standardReserveItems) {
        if (CollectionUtils.isEmpty(standardReserveItems)) {
            return null;
        }
        List<StandardReserveItem> mustNeedReserveItems = standardReserveItems.stream().filter(StandardReserveItem::isMustNeed).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(mustNeedReserveItems)) {
            return null;
        }
        if (collectedReserveInfo == null || MapUtils.isEmpty(collectedReserveInfo.getCollectedReserveItemMap())) {
            return mustNeedReserveItems;
        }
        Map<String, ReserveItemData> collectedReserveItemMap = collectedReserveInfo.getCollectedReserveItemMap();
        return getLackReserveItems(mustNeedReserveItems, collectedReserveItemMap);
    }

    private static List<StandardReserveItem> getLackReserveItems(List<StandardReserveItem> mustNeedReserveItems, Map<String, ReserveItemData> collectedReserveItemMap) {
        List<StandardReserveItem> lackReserveItems = Lists.newArrayList();
        for (StandardReserveItem mustNeedReserveItem : mustNeedReserveItems) {
            if (mustNeedReserveItem == null) {
                continue;
            }
            ReserveItemData reserveItemData = collectedReserveItemMap.get(mustNeedReserveItem.getKey());
            if (reserveItemData == null || (StringUtils.isBlank(reserveItemData.getConvertedValue()) && StringUtils.isBlank(reserveItemData.getRawValue()))) {
                lackReserveItems.add(mustNeedReserveItem);
            }
        }
        if (CollectionUtils.isEmpty(lackReserveItems)) {
            return null;
        }
        return lackReserveItems;
    }

    /**
     * 获取预约信息中的数字类型数据
     *
     * @param reserveInfo
     * @param key
     * @return
     */
    public static Integer getIntItem(ReserveInfo reserveInfo, String key) {
        ReserveItemData itemData = extractItem(reserveInfo, key);
        if (itemData == null) {
            return null;
        }
        try {
            return Integer.valueOf(itemData.getConvertedValue());
        } catch (Exception e) {
            log.error("[ReserveItemUtils] getNumItem error, item={}, key={}", JSON.toJSONString(itemData), key, e);
            return null;
        }
    }

    /**
     * 获取预约信息中的字符串类型数据
     *
     * @param reserveInfo
     * @param key
     * @return
     */
    public static String getStringItem(ReserveInfo reserveInfo, String key) {
        ReserveItemData itemData = extractItem(reserveInfo, key);
        if (itemData == null) {
            return null;
        }
        try {
            return itemData.getConvertedValue();
        } catch (Exception e) {
            log.error("[ReserveItemUtils] getString error, item={}, key={}", JSON.toJSONString(itemData), key, e);
            return null;
        }
    }

    /**
     * 获取预约信息中的原始字符串数据
     *
     * @param reserveInfo
     * @param key
     * @return
     */
    public static String getRawStringItem(ReserveInfo reserveInfo, String key) {
        ReserveItemData itemData = extractItem(reserveInfo, key);
        if (itemData == null) {
            return null;
        }
        try {
            return itemData.getRawValue();
        } catch (Exception e) {
            log.error("[ReserveItemUtils] getString error, item={}, key={}", JSON.toJSONString(itemData), key, e);
            return null;
        }
    }

    /**
     * 获取预约信息中的long类型数据
     *
     * @param reserveInfo
     * @param key
     * @return
     */
    public static Long getLongItem(ReserveInfo reserveInfo, String key) {
        ReserveItemData itemData = extractItem(reserveInfo, key);
        if (itemData == null) {
            return null;
        }
        try {
            return Long.valueOf(itemData.getConvertedValue());
        } catch (Exception e) {
            log.error("[ReserveItemUtils] getLongItem error, item={}, key={}", JSON.toJSONString(itemData), key, e);
            return null;
        }
    }

    /**
     * 获取离开时间(毫秒时间戳)
     *
     * @param reserveInfo
     * @param startTime
     * @return
     */
    public static Long getLeaveTime(ReserveInfo reserveInfo, String startTime) {
        Integer duration = getIntItem(reserveInfo, ReserveItemType.DURATION.getKey());
        if (StringUtils.isBlank(startTime) || duration == null || duration <= 0) {
            return 0L;
        }
        return TimeUtil.convertStr2MillSeconds(startTime) + duration * 60 * 1000;
    }

    /**
     * 获取离开时间（按照format格式输出）
     *
     * @param reserveInfo
     * @param startTime
     * @param format
     * @return
     */
    public static String getLeaveTimeWithFormat(ReserveInfo reserveInfo, String startTime, String format) {
        Integer duration = getIntItem(reserveInfo, ReserveItemType.DURATION.getKey());
        if (StringUtils.isBlank(startTime) || duration == null || duration <= 0) {
            return null;
        }
        LocalDateTime startTimeFormat = TimeUtil.convertStr2LocalDateTime(startTime);
        if (startTimeFormat == null) {
            return null;
        }
        return startTimeFormat.plusMinutes(duration).toString(format);
    }

    private static ReserveItemData extractItem(ReserveInfo reserveInfo, String key) {
        if (reserveInfo == null || MapUtils.isEmpty(reserveInfo.getCollectedReserveItemMap())) {
            return null;
        }
        ReserveItemData itemData = reserveInfo.getCollectedReserveItemMap().get(key);
        return itemData;
    }

    /**
     * 判断库存是否存在，存在返回可用的时间片，否则返回空列表
     */
    public static List<TimeSliceStockInfo> checkStockExist(POIIndustryType poiIndustryType, ReserveInfo collectedReserveInfo, List<TimeSliceStockInfo> timeSliceStockInfos) {
        try {
            // 1. 参数校验
            if (!isValidInput(collectedReserveInfo, timeSliceStockInfos)) {
                return Lists.newArrayList();
            }

            // 2. 获取开始时间和预约人数
            String startTime = getStringItem(collectedReserveInfo, ReserveItemType.START_TIME.getKey());
            Integer personNum = getIntItem(collectedReserveInfo, ReserveItemType.PERSON_NUM.getKey());
            personNum = personNum == null ? 1 : personNum;

            // 3. 根据不同行业类型检查库存
            //3.1 只要求开始时间 美发宠物
            if (isSimpleStartTimeCheck(poiIndustryType)) {
                return checkSimpleTimeStock(timeSliceStockInfos, startTime, personNum);
            }

            //3.2 要求开始时间（日期），酒吧比较特殊，时间片分开始时间和 几点前到两种，单独处理
            if (isBarCheck(poiIndustryType)) {
                return checkBarStock(timeSliceStockInfos, startTime);
            }

            log.info("checkStockExist, poiIndustryType:{}, collectedReserveInfo:{}, timeSliceStockInfos:{}",
                    poiIndustryType, collectedReserveInfo, timeSliceStockInfos);
            //3.3 要求开始时间和结束时间 足疗，棋牌，ktv，球类
            return checkDurationStock(timeSliceStockInfos, startTime, collectedReserveInfo, personNum);
        } catch (Exception e) {
            log.error("checkStockExist error, poiIndustryType:{}, collectedReserveInfo:{}, timeSliceStockInfos:{}",
                    poiIndustryType, collectedReserveInfo, timeSliceStockInfos, e);
        }
        return Lists.newArrayList();
    }

    //  判断开始时间检查行业类型，美发，宠物
    private static boolean isSimpleStartTimeCheck(POIIndustryType poiIndustryType) {
        return POIIndustryType.HAIR.equals(poiIndustryType) || POIIndustryType.PET_SHOP.equals(poiIndustryType);
    }


    //酒吧比较特殊，时间片分开始时间和 几点前到，单独处理
    private static boolean isBarCheck(POIIndustryType poiIndustryType) {
        return POIIndustryType.BAR.equals(poiIndustryType);
    }

    private static List<TimeSliceStockInfo> checkSimpleTimeStock(List<TimeSliceStockInfo> timeSliceStockInfos, String startTime, int personNum) {
        TimeSliceStockInfo slice = findStartTimeExistStockIndex(timeSliceStockInfos, startTime, personNum);
        if (ObjectUtils.isEmpty(slice)) {
            return Lists.newArrayList();
        }
        return Lists.newArrayList(slice);
    }

    private static List<TimeSliceStockInfo> checkDurationStock(List<TimeSliceStockInfo> timeSliceStockInfos, String startTime,
                                                               ReserveInfo collectedReserveInfo, Integer personNum) {
        String endTime = getLeaveTimeWithFormat(collectedReserveInfo, startTime, "yyyy-MM-dd HH:mm");
        return checkStartAndEndTimeStockExist(timeSliceStockInfos, startTime, endTime, personNum);
    }

    private static List<TimeSliceStockInfo> checkBarStock(List<TimeSliceStockInfo> timeSliceStockInfos, String startTime) {
        TimeSliceStockInfo timeSliceStockInfo = findStartDayExistStockIndex(timeSliceStockInfos, startTime);
        if (ObjectUtils.isEmpty(timeSliceStockInfo)) {
            return Lists.newArrayList();
        }
        return Lists.newArrayList(timeSliceStockInfo);
    }

    /**
     * 判断库存是否存在
     * 检查从开始时间到结束时间的时间段内，是否有足够的库存满足预约人数需求
     */
    private static List<TimeSliceStockInfo> checkStartAndEndTimeStockExist(List<TimeSliceStockInfo> timeSliceStockInfos, String startTime, String endTime, Integer personNum) {
        // 1. 找到包含开始时间的时间片
        int startIndex = findStartTimeSliceIndex(timeSliceStockInfos, startTime);
        if (startIndex == -1) {
            return Lists.newArrayList();
        }
        // 2. 检查从开始时间片到结束时间的所有连续时间片是否都有足够的库存
        return checkConsecutiveTimeSlices(timeSliceStockInfos, startIndex, endTime, personNum);
    }

    /**
     * 验证输入参数是否有效
     */
    private static boolean isValidInput(ReserveInfo collectedReserveInfo, List<TimeSliceStockInfo> timeSliceStockInfos) {
        return collectedReserveInfo != null && !CollectionUtils.isEmpty(timeSliceStockInfos);
    }

    /**
     * 查找包含指定开始时间的时间片索引
     *
     * @return 找到的时间片索引，如果未找到返回-1
     */
    private static int findStartTimeSliceIndex(List<TimeSliceStockInfo> timeSliceStockInfos, String startTime) {
        for (int i = 0; i < timeSliceStockInfos.size(); i++) {
            TimeSliceStockInfo slice = timeSliceStockInfos.get(i);
            if (slice.getStartTime().compareTo(startTime) <= 0 && slice.getEndTime().compareTo(startTime) > 0) {
                return i;
            }
        }
        return -1;
    }

    /**
     * 查找包含指定开始时间且存在库存的时间片索引
     *
     * @param timeSliceStockInfos
     * @param startTime
     * @param personNum
     * @return
     */
    private static TimeSliceStockInfo findStartTimeExistStockIndex(List<TimeSliceStockInfo> timeSliceStockInfos, String startTime, int personNum) {
        if (StringUtils.isBlank(startTime)) {
            return null;
        }
        for (TimeSliceStockInfo slice : timeSliceStockInfos) {
            if (slice.getStartTime().compareTo(startTime) <= 0 && slice.getEndTime().compareTo(startTime) > 0 && slice.getStock() >= personNum) {
                return slice;
            }
        }
        return null;
    }

    //酒吧找当天的库存，没有人数要求
    public static TimeSliceStockInfo findStartDayExistStockIndex(List<TimeSliceStockInfo> timeSliceStockInfos, String startTime) {
        if (StringUtils.isBlank(startTime)) {
            return null;
        }
        for (TimeSliceStockInfo slice : timeSliceStockInfos) {
            TimeSliceTypeEnum timeSliceType = slice.getTimeSliceType();
            if (timeSliceType == null) {
                continue;
            }
            LocalDateTime currentTime = LocalDateTime.now();
            String sliceStartTime = "";
            //开始时间,大于现在
            if (TimeSliceTypeEnum.START_TIME.equals(timeSliceType)) {
                sliceStartTime  = slice.getStartTime();
            } else if (TimeSliceTypeEnum.BEFORE_END_TIME.equals(timeSliceType)) {
                sliceStartTime = StringUtil.isBlank(slice.getEndTime()) ? slice.getStartTime() : slice.getEndTime();
            }

            LocalDateTime beforeEndTime = TimeUtil.convertStr2LocalDateTime(sliceStartTime);
            if (currentTime.isBefore(beforeEndTime)) {
                return slice;
            }
        }
        return null;
    }

    /**
     * 检查连续时间片的库存是否满足需求
     * 从开始时间片开始，检查每个时间片直到结束时间，确保每个时间片都有足够的库存
     */
    private static List<TimeSliceStockInfo> checkConsecutiveTimeSlices(List<TimeSliceStockInfo> timeSliceStockInfos, int startIndex, String endTime, int personNum) {
        for (int i = startIndex; i < timeSliceStockInfos.size(); i++) {
            TimeSliceStockInfo slice = timeSliceStockInfos.get(i);
            // 如果当前时间片库存不足，直接返回false
            if (slice.getStock() < personNum) {
                return Lists.newArrayList();
            }
            // 如果已经覆盖到结束时间，说明所有需要的时间片库存都充足
            if (slice.getEndTime().compareTo(endTime) >= 0) {
                // 子列表左开右闭
                List<TimeSliceStockInfo> stockInfos = timeSliceStockInfos.subList(startIndex, i + 1);

                // 判断purchaseStockDate是否都是一天
                if (checkSamePurchaseStockDate(stockInfos)) {
                    return stockInfos;
                }
                return Lists.newArrayList();
            }
        }
        return Lists.newArrayList();
    }

    /**
     * /**
     * 判断purchaseStockDate是否都是一天
     *
     * @param timeSliceStockInfos timeSliceStockInfos
     * @return 是否都是一天
     */
    private static boolean checkSamePurchaseStockDate(List<TimeSliceStockInfo> timeSliceStockInfos) {
        if (CollectionUtils.isEmpty(timeSliceStockInfos)) {
            return false;
        }
        String firstDate = timeSliceStockInfos.get(0).getPurchaseStockDate();
        for (TimeSliceStockInfo stockInfo : timeSliceStockInfos) {
            if (!firstDate.equals(stockInfo.getPurchaseStockDate())) {
                return false;
            }
        }
        return true;
    }

    /**
     * 查询最近可用的开始时间
     * 从当前时间开始，查找第一个满足预约人数和时长要求的时间段
     */
    public static String findNearestAvailableTime(ReserveInfo collectedReserveInfo, List<TimeSliceStockInfo> timeSliceStockInfos) {
        // 1. 参数校验
        if (!isValidInput(collectedReserveInfo, timeSliceStockInfos)) {
            return null;
        }

        // 2. 获取预约人数和时长
        Integer personNum = ReserveInfoProcessUtils.getIntItem(collectedReserveInfo, ReserveItemType.PERSON_NUM.getKey());
        personNum = personNum == null ? 1 : personNum;
        Integer duration = ReserveInfoProcessUtils.getIntItem(collectedReserveInfo, ReserveItemType.DURATION.getKey());
        if (duration == null || duration <= 0) {
            return null;
        }

        // 3. 查找可用的时间片
        return findAvailableTimeSlot(timeSliceStockInfos, collectedReserveInfo, personNum);
    }

    public static String beamFindNearestAvailableTime(ReserveInfo collectedReserveInfo, List<TimeSliceStockInfo> timeSliceStockInfos, ShopReserveContext shopReserveContext) {
        // 1. 参数校验
        if (!isValidInput(collectedReserveInfo, timeSliceStockInfos)) {
            return null;
        }

        // 2. 获取预约人数和时长
        Integer personNum = beamGetPersonNumByPoiIndustry(collectedReserveInfo, shopReserveContext);
        personNum = personNum == null ? 1 : personNum;
        Integer duration = ReserveInfoProcessUtils.getIntItem(collectedReserveInfo, ReserveItemType.DURATION.getKey());
        if (duration == null || duration <= 0) {
            return null;
        }

        // 3. 查找可用的时间片
        return findAvailableTimeSlot(timeSliceStockInfos, collectedReserveInfo, personNum);
    }


    public static Integer beamGetPersonNumByPoiIndustry(ReserveInfo collectedReserveInfo, ShopReserveContext shopReserveContext) {
        POIIndustryType poiIndustryType = shopReserveContext.getPoiIndustryType();
        //酒吧和宠物店上限1人
        if(POIIndustryType.BAR.equals(poiIndustryType) || POIIndustryType.PET_SHOP.equals(poiIndustryType)){
            return 1;
        }

        //美发足疗
        if(POIIndustryType.HAIR.equals(poiIndustryType) || POIIndustryType.FOOT_MASSAGE.equals(poiIndustryType)){
            Integer personNum = ReserveInfoProcessUtils.getIntItem(collectedReserveInfo, ReserveItemType.PERSON_NUM.getKey());
            return personNum == null ? 1 : personNum;
        }
        return 1;
    }




    public static String findNearestAvailableDay(ReserveInfo collectedReserveInfo, List<TimeSliceStockInfo> timeSliceStockInfos) {
        // 1. 参数校验
        if (!isValidInput(collectedReserveInfo, timeSliceStockInfos)) {
            return null;
        }

        // 2. 查找可用的时间片
        return findAvailableDaySlot(timeSliceStockInfos, collectedReserveInfo);
    }

    /**
     * 查找可用的时间片
     * 遍历所有时间片，找到第一个满足预约人数和时长要求的时间段
     */
    private static String findAvailableDaySlot(List<TimeSliceStockInfo> timeSliceStockInfos, ReserveInfo collectedReserveInfo) {
        for (int i = 0; i < timeSliceStockInfos.size(); i++) {
            TimeSliceStockInfo currentSlice = timeSliceStockInfos.get(i);
            String startTime = ReserveInfoProcessUtils.getStringItem(collectedReserveInfo, ReserveItemType.START_TIME.getKey());
            LocalDate startDate = TimeUtil.convertStr2LocalDateTime(startTime).toLocalDate();
            LocalDate startDateCandi = TimeUtil.convertStr2LocalDateTime(currentSlice.getStartTime()).toLocalDate();
            if (startDateCandi.equals(startDate)) {
                return currentSlice.getStartTime();
            }
        }
        return null;
    }

    /**
     * 查找可用的时间片
     * 遍历所有时间片，找到第一个满足预约人数和时长要求的时间段
     */
    private static String findAvailableTimeSlot(List<TimeSliceStockInfo> timeSliceStockInfos, ReserveInfo collectedReserveInfo, int personNum) {
        for (int i = 0; i < timeSliceStockInfos.size(); i++) {
            TimeSliceStockInfo currentSlice = timeSliceStockInfos.get(i);
            // 如果当前时间片库存不足，跳过
            if (currentSlice.getStock() < personNum) {
                continue;
            }

            // 计算当前时间片开始时间加上预约时长后的结束时间
            String currentStartTime = currentSlice.getStartTime();
            String expectedEndTime = getLeaveTimeWithFormat(collectedReserveInfo, currentStartTime, "yyyy-MM-dd HH:mm");

            // 检查从当前时间片开始是否有足够的时间片满足预约时长
            if (isTimeSlotAvailable(timeSliceStockInfos, i, expectedEndTime, personNum)) {
                return currentStartTime;
            }
        }
        return null;
    }

    /**
     * 检查时间片是否可用
     * 检查从指定时间片开始，是否有足够的时间片覆盖到预期结束时间
     */
    private static boolean isTimeSlotAvailable(List<TimeSliceStockInfo> timeSliceStockInfos, int startIndex, String expectedEndTime, int personNum) {
        // 检查当前时间片是否可以直接覆盖到结束时间
        String currentEndTime = timeSliceStockInfos.get(startIndex).getEndTime();
        if (currentEndTime.compareTo(expectedEndTime) >= 0) {
            return true;
        }

        // 如果当前时间片不够长，检查后续时间片
        for (int j = startIndex + 1; j < timeSliceStockInfos.size(); j++) {
            TimeSliceStockInfo nextSlice = timeSliceStockInfos.get(j);
            // 如果后续时间片库存不足，说明无法满足预约需求
            if (nextSlice.getStock() < personNum) {
                break;
            }
            // 如果已经覆盖到结束时间，说明时间片可用
            if (nextSlice.getEndTime().compareTo(expectedEndTime) >= 0) {
                return true;
            }
        }
        return false;
    }

    public static void updateItem(ReserveInfo reserveInfo, String key, String value) {
        if (reserveInfo == null) {
            return;
        }
        if (MapUtils.isEmpty(reserveInfo.getCollectedReserveItemMap())) {
            reserveInfo.setCollectedReserveItemMap(Maps.newHashMap());
        }
        Map<String, ReserveItemData> collectedReserveItemMap = reserveInfo.getCollectedReserveItemMap();
        ReserveItemType reserveItemType = ReserveItemType.getByKey(key);
        collectedReserveItemMap.put(key, new ReserveItemData(reserveItemType, value));
    }

    /**
     * 校验时间
     *
     * @param reserveItemDataMap 预约要素
     * @param shopReserveContext 商户上下文信息
     * @return
     */
    public static ValidateResult validateStartTime(Map<String, ReserveItemData> reserveItemDataMap, ShopReserveContext shopReserveContext) {
        if (MapUtils.isEmpty(reserveItemDataMap)) {
            return ValidateResult.success();
        }
        ReserveItemData startTimeItem = reserveItemDataMap.get(ReserveItemType.START_TIME.getKey());
        if (startTimeItem == null) {
            return ValidateResult.success();
        }
        String startTimeStr = startTimeItem.getConvertedValue();
        if (StringUtils.isEmpty(startTimeStr)) {
            return ValidateResult.success();
        }

        //酒吧行业时间校验
        if (shopReserveContext != null && shopReserveContext.getPoiIndustryType() == POIIndustryType.BAR) {
            return validateTimeResultForBar(startTimeStr);
        }
        // 在线系统校验
        if (shopReserveContext != null && ShopReserveWayType.ONLINE.equals(shopReserveContext.getShopReserveWayType())) {
            return validateStartTimeOnline(startTimeStr, shopReserveContext);
        }
        // 非在线系统校验
        return validateResultForPhone(startTimeStr);
    }

    private static ValidateResult validateResultForPhone(String startTimeStr) {
        LocalDateTime startTime = TimeUtil.convertStr2LocalDateTime(startTimeStr);
        if (startTime == null) {
            return ValidateResult.success();
        }
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        // 1.检查时间是否早于当天
        if (validateEarlierThanToday(now, startTime)) {
            return ValidateResult.fail("抱歉，预约时间不能早于当天，请重新选择时间");
        }
        // 2.检查时间是否在7天内
        if (validateMoreThan7Days(now, startTime)) {
            return ValidateResult.fail("预约时间不能超过7天");
        }
        return ValidateResult.success();
    }

    private static ValidateResult validateTimeResultForBar(String startTimeStr) {
        LocalDateTime startTime = TimeUtil.convertStr2LocalDateTime(startTimeStr);
        if (startTime == null) {
            return ValidateResult.success();
        }
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        // 1.检查时间是否早于当天
        if (validateEarlierThanToday(now, startTime)) {
            return ValidateResult.fail("抱歉，预约时间不能早于当天，请重新选择时间");
        }
        // 2.检查时间是否在7天内
        if (validateMoreThan7Days(now, startTime)) {
            return ValidateResult.fail("预约时间不能超过7天");
        }
        return ValidateResult.success();
    }

    private static ValidateResult validateStartTimeOnline(String startTimeStr, ShopReserveContext shopReserveContext) {
        // 1. 时间不明确的标识
        // 在线系统不允许时间不明确
        if (startTimeStr.equals("-1")) {
            return ValidateResult.fail("当前描述的时间不够具体，请输入一个具体的时间点，比如今天下午2点");
        }
        LocalDateTime startTime = TimeUtil.convertStr2LocalDateTime(startTimeStr);
        if (startTime == null) {
            return ValidateResult.success();
        }
        // 2. 获取当前时间
        LocalDateTime now = LocalDateTime.now();

        // 3. 检查时间是否早于当前时间
        if (startTime.isBefore(now)) {
            return ValidateResult.fail("抱歉，预约时间不能早于当前时间，请重新选择时间");
        }

        // 4. 检查时间是否在7天内
        if (validateMoreThan7Days(now, startTime)) {
            return ValidateResult.fail("预约时间不能超过7天");
        }
        // 5. 检查时间间隔是否符合要求
        int minutes = startTime.getMinuteOfHour();
        POIIndustryType poiIndustryType = shopReserveContext.getPoiIndustryType();
        List<Integer> secondBackCategoryIds = poiIndustryType != null ? Lists.newArrayList(poiIndustryType.getSecondBackCategoryId()) : null;
        ShopStartTimeIntervalType shopStartTimeIntervalType = ShopStartTimeIntervalType.matchByBackCategoryIds(secondBackCategoryIds);
        int timeInterval = shopStartTimeIntervalType == null ? 0 : shopStartTimeIntervalType.getStartTimeInterval();

        if (timeInterval != 0 && minutes % timeInterval != 0) {
            // 计算最近的合法时间点
            LocalDateTime nearestTime = TimeUtil.calculateNearestValidTime(startTime, timeInterval);
            String nearestTimeStr = TimeUtil.formatTimeToFriendlyString(nearestTime);
            return ValidateResult.fail(String.format("当前时间不可约，是否可以使用%s", nearestTimeStr));
        }
        return ValidateResult.success();
    }

    private static boolean validateMoreThan7Days(LocalDateTime now, LocalDateTime startTime) {
        LocalDateTime maxTime = now.plusDays(7);
        return startTime.isAfter(maxTime);
    }

    public static boolean validateEarlierThanToday(LocalDateTime now, LocalDateTime startTime) {
        LocalDateTime nowDate = now.withTime(0, 0, 0, 0);
        LocalDateTime startTimeDate = startTime.withTime(0, 0, 0, 0);
        return startTimeDate.isBefore(nowDate);
    }
}
