package com.sankuai.dzim.pilot.process;

import com.sankuai.dzim.pilot.gateway.api.vo.NailTaskVO;
import com.sankuai.dzim.pilot.process.data.NailInfo;

import java.util.List;
import java.util.Map;

public interface ImageProcessService {
    /**
     * llm处理一张图片
     * @param imgUrl
     * @param prompt
     * @param customOperation
     * @return 处理后的图片URL
     */
    String processImage(String imgUrl, String prompt, String customOperation);

    /**
     * llm处理多张图片
     * @param imgUrls
     * @param operation
     * @param customOperation
     * @return
     */
    NailTaskVO processImages(List<String> imgUrls, String operation, String customOperation, String taskId);

    /**
     * 检测图片美甲位置
     *
     * @param imgUrls
     * @return
     */
    Map<String, String> nailDetect(List<String> imgUrls);

    /**
     * 查询相似的美甲款式，文搜图
     * @param query
     * @return
     */
    NailInfo querySimilarNailByText(String query);

    /**
     * 查询相似款式，图片搜图
     * @param imgUrl
     * @return
     */
    NailInfo querySimilarNailByImage(String imgUrl);

    /**
     * 抠图
     * @param imgUrl
     * @return
     */
    String cutouts(String imgUrl);
}
