package com.sankuai.dzim.pilot.utils;

import com.dianping.lion.client.Lion;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.math.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.FastDateFormat;
import org.joda.time.DateTime;
import org.joda.time.DateTimeConstants;
import org.joda.time.LocalDateTime;
import org.joda.time.Seconds;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalQueries;
import java.time.temporal.TemporalQuery;
import java.util.*;

/**
 * Created by liuman04 on 2021/10/18
 */
@Slf4j
public class DateUtils {

    private static final DateTimeFormatter DATE_NUM_FORMAT = DateTimeFormatter.ofPattern("yyyyMMdd");

    private static final DateTimeFormatter YEAR_MONTH_FORMAT = DateTimeFormatter.ofPattern("yyyyMM");

    private static final DateTimeFormatter SIMPLE_DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private static final DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public static Date getStartOfDay(String dateString) {
        if (StringUtils.isEmpty(dateString)) {
            return null;
        }
        try {
            LocalDate date = LocalDate.parse(dateString, SIMPLE_DATE_FORMAT);
            return Date.from(date.atStartOfDay(ZoneId.systemDefault()).toInstant());
        } catch (Exception e) {
            log.error("Error parsing date string: {}", dateString, e);
            return null;
        }
    }

    public static Date getStartOfNextDay(String dateString) {
        if (StringUtils.isEmpty(dateString)) {
            return null;
        }
        try {
            LocalDate date = LocalDate.parse(dateString, SIMPLE_DATE_FORMAT);
            LocalDate nextDate = date.plusDays(1);
            return Date.from(nextDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
        } catch (Exception e) {
            log.error("Error parsing date string: {}", dateString, e);
            return null;
        }
    }

    public static int secondsToDayEndWithRandom() {
        DateTime currentDateTime = new DateTime();
        DateTime tomorrow = currentDateTime.withMillisOfDay(0).plusDays(1);
        return Seconds.secondsBetween(DateTime.now(), tomorrow).getSeconds() + RandomUtils.nextInt(10 * 60);
    }

    public static String covertDateNumStr(Date date) {
        if (date == null) {
            return Strings.EMPTY;
        }
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DATE_NUM_FORMAT);
    }

    /**
     * 将Date转换为yyyy-MM-dd格式的字符串
     * @param date 日期
     * @return yyyy-MM-dd格式的日期字符串，如果date为null则返回空字符串
     */
    public static String formatSimpleDate(Date date) {
        if (date == null) {
            return StringUtils.EMPTY;
        }
        return date.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate()
                .format(SIMPLE_DATE_FORMAT);
    }

    public static String parseDateKey2DateStr(String dateKey) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        try {
            Date date = simpleDateFormat.parse(dateKey);
            return covertDateStr(LocalDateTime.fromDateFields(date).withHourOfDay(0).withMinuteOfHour(0).withSecondOfMinute(0).toDate());
        } catch (Exception e) {
            log.error("convertDateKey2tDateStr error: dateKey: {}", dateKey, e);
            return StringUtils.EMPTY;
        }
    }

    /**
     * @param startDate 开始日期 格式：yyyy-MM-dd
     * @param endDate 结束日期 格式：yyyy-MM-dd
     * @return 日期列表
     */
    public static List<String> getBetweenDates(String startDate, String endDate) {
        List<String> result = new ArrayList<>();
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date start = sdf.parse(startDate);
            Date end = sdf.parse(endDate);

            Calendar tempStart = Calendar.getInstance();
            tempStart.setTime(start);

            Calendar tempEnd = Calendar.getInstance();
            tempEnd.setTime(end);
            tempEnd.add(Calendar.DATE, 1); // 增加一天，以便包含结束日期

            while (tempStart.before(tempEnd)) {
                result.add(sdf.format(tempStart.getTime()));
                tempStart.add(Calendar.DATE, 1);
            }
        } catch (Exception e) {
            log.error("getBetweenDates error, startDate = {}, endDate = {}", startDate, endDate, e);
        }
        return result;
    }

    public static String extractYearMonth(String dateStr) {
        if (StringUtils.isEmpty(dateStr)) {
            return StringUtils.EMPTY;
        }
        try {
            java.time.LocalDateTime dateTime = java.time.LocalDateTime.parse(dateStr, DATE_FORMAT);
            return dateTime.format(YEAR_MONTH_FORMAT);
        } catch (Exception e) {
            log.error("Error extracting year month from date string: {}", dateStr, e);
            return StringUtils.EMPTY;
        }
    }

    /**
     * 判断指定时间是否超过当前时间
     * @param targetTime 指定时间 (格式:yyyy-MM-dd HH:mm:ss)
     * @return true 如果指定时间超过当前时间
     */
    public static boolean isAfterNow(Date targetTime) {
        if (targetTime == null) {
            return false;
        }

        try {
            Date now = new Date();
            return targetTime.after(now);
        } catch (Exception e) {
            // 解析失败返回 false
            log.error("DateUtils isAfter error, timeStr = {}", targetTime.toString());
            return false;
        }
    }

    public static String covertDateStr(Date date) {
        if (date == null) {
            return Strings.EMPTY;
        }

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return dateFormat.format(date);
    }

    public static String covertDateStrToSimpleDate(Date date) {
        if (date == null) {
            return Strings.EMPTY;
        }

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        return dateFormat.format(date);
    }

    public static String covertDateStr(Date date, String pattern) {
        if (date == null) {
            return Strings.EMPTY;
        }

        SimpleDateFormat dateFormat = new SimpleDateFormat(pattern);
        return dateFormat.format(date);
    }

    public static String getDateDesc(Date date) {
        Date now = new Date();
        int dayDiff = DateUtils.calculateDayDiff(now, date);

        if (dayDiff == 0) {
            return "今天";
        } else if (dayDiff == 1) {
            return "明天";
        } else if (dayDiff == 2) {
            return "后天";
        }
        return covertDateStr(date, "MM-dd");
    }

    public static int calculateDayDiff(Date firstDate, Date secondDate) {
        if (firstDate == null || secondDate == null) {
            throw new IllegalArgumentException("Dates must not be null");
        }

        // 将 Date 转换为 LocalDate，这样就会忽略时分秒
        LocalDate firstLocalDate = firstDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate secondLocalDate = secondDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

        // 计算日期差，使用 Math.abs 确保结果为正数
        return (int) java.time.temporal.ChronoUnit.DAYS.between(firstLocalDate, secondLocalDate);
    }

    public static Date getToday() {
        // 获取当前日期和时间
        Calendar calendar = Calendar.getInstance();

        // 将时间部分设置为零
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        // 获取日期对象
        return calendar.getTime();
    }

    public static int getHourOfDay(Date date) {
        if (date == null) {
            throw new IllegalArgumentException("Date must not be null");
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.HOUR_OF_DAY);
    }

    /**
     * 根据指定时间和秒数，计算未来的时间
     * @param date 指定时间
     * @param seconds 需要增加的秒数
     * @return 未来的时间
     */
    public static Date addSeconds(Date date, long seconds) {
        if (date == null) {
            return null;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.SECOND, (int) seconds);
        return calendar.getTime();
    }

    public static int getMinuteOfHour(Date date) {
        if (date == null) {
            throw new IllegalArgumentException("Date must not be null");
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.MINUTE);
    }

    public static Date getSpecialDate(int year, int month, int day) {
        // 创建一个 LocalDate 实例
        LocalDate localDate = LocalDate.of(year, month, day);

        // 将 LocalDate 转换为 java.util.Date
        return Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }

    public static String formatChineseDate(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("M月d日 HH:mm", Locale.CHINESE);
        return sdf.format(date);
    }
}