package com.sankuai.dzim.pilot.utils;

import com.meituan.image.client.ImageUploadClient;
import com.meituan.image.client.impl.ImageUploadClientImpl;
import com.meituan.image.client.pojo.ImageRequest;
import com.meituan.image.client.pojo.ImageResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.Base64;

/**
 * <AUTHOR>
 * @since 2025/04/10 17:32
 */
@Slf4j
public class VenusUtil {
    private static ImageUploadClient client;

    private static final String bucket = "pilotimages";
    private static final String client_id = "gcsdw9n4prdbf4f20000000000712728";
    private static final String client_secret = "kdrc22b49k9tkjhd8zjvm8p8p7h44xcq";
    // 线下：

//    private static final String client_id = "n2kx8fwzlzrgldk60000000000b46266";
//    private static final String client_secret = "rmczmttsbvdgssc5xphnj8jxk2vqttmc";

    private static ImageUploadClient getClient() {
        if (client == null) {
            client = new ImageUploadClientImpl(bucket, client_id, client_secret);
            client.setEnv(ImageUploadClient.Environment.PROD);
        }
        client.setEnv(ImageUploadClient.Environment.PROD);
        return client;
    }

    public static String uploadImage(String inputUrl) {
        byte[] inputData = getByteArrayFromURL(inputUrl);
        if (inputData == null) {
            return StringUtils.EMPTY;
        }
        ImageRequest request = new ImageRequest(ImageRequest.UploadTypeBytes, inputData, UuidUtil.getRandomCardKey() + ".jpg", false);
        ImageResult res = getClient().postImage(request);
        String originalLink = res.getOriginalLink();
        if (StringUtils.isNotBlank(originalLink)) {
            originalLink = originalLink.replace("\"", "");
        }
        return originalLink;
    }

    public static String uploadBase64Image(String base64Image, String format) {
        try {
            // 解码 Base64 图像字符串到字节数组
            byte[] imageBytes = Base64.getDecoder().decode(base64Image);
            ImageRequest request = new ImageRequest(ImageRequest.UploadTypeBytes, imageBytes, UuidUtil.getRandomCardKey() + "." + format, false);
            ImageResult res = getClient().postImage(request);
            return res.getOriginalLink();
        } catch (Exception e) {
            // 处理可能的异常
            log.error("uploadBase64Image 失败", e);
            return "";
        }
    }

    public static String uploadImage(InputStream inputStream, String format) {
        try (ByteArrayOutputStream output = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[4096];
            int n;
            while (-1 != (n = inputStream.read(buffer))) {
                output.write(buffer, 0, n);
            }
            ImageRequest request = new ImageRequest(ImageRequest.UploadTypeBytes, buffer, UuidUtil.getRandomCardKey() + "." + format, false);
            ImageResult res = getClient().postImage(request);
            return res.getOriginalLink();
        } catch (IOException e) {
            log.error("读取输入流失败", e);
            return StringUtils.EMPTY;
        }
    }

    public static byte[] getByteArrayFromURL(String urlString) {
        try {
            URL url = new URL(urlString);
            try (ByteArrayOutputStream output = new ByteArrayOutputStream()) {
                try (InputStream inputStream = url.openStream()) {
                    byte[] buffer = new byte[4096];
                    int n;
                    while (-1 != (n = inputStream.read(buffer))) {
                        output.write(buffer, 0, n);
                    }
                }
                return output.toByteArray();
            }
        } catch (IOException e) {
            log.error("读取网络链接失败, url={}", urlString, e);
        }
        return null;
    }
}