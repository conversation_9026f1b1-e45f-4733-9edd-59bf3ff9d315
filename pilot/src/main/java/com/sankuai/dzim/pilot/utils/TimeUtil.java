package com.sankuai.dzim.pilot.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.LocalDateTime;
import org.joda.time.Seconds;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import java.util.Calendar;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2024/8/23 11:39
 */
@Slf4j
public class TimeUtil {

    public static final String FORMAT = "yyyy-MM-dd HH:mm:ss";
    public static final String FORMAT_WITHOUT_SECOND = "yyyy-MM-dd HH:mm";
    public static final String FORMAT_DAY = "yyyy-MM-dd";

    public static Date getCurrentTimePlusOffsetMinute(int  offset) {
        LocalDateTime now = LocalDateTime.now();
        int minuteOfHour = now.getMinuteOfHour();
        /*
         * 当前时间向未来取整半小时
         * 0 -> 0
         * [1, 30] -> 30
         * [31, 59] -> 0
         */
        LocalDateTime modifiedNow;
        if (minuteOfHour >= 1 && minuteOfHour <= 30) {
            modifiedNow = now.withMinuteOfHour(30).withSecondOfMinute(0).withMillisOfSecond(0);
        } else if (minuteOfHour >= 31 && minuteOfHour <= 59) {
            modifiedNow = now.plusHours(1).withMinuteOfHour(0).withSecondOfMinute(0).withMillisOfSecond(0);
        } else {
            modifiedNow = now.withSecondOfMinute(0).withMillisOfSecond(0);
        }
        return modifiedNow.plusMinutes(offset).toDate();
    }

    /*
     判断当前时间是否在指定时间限制范围内
     */
    public static boolean isWithinLimitMinutesFromNow(Date dateToCompare, int limitMinutes) {
        Date now = new Date();
        long difference = now.getTime() - dateToCompare.getTime();
        if (difference < 0) {
            return false;
        }
        long differenceMinutes = difference / (60 * 1000);
        return differenceMinutes <= limitMinutes;
    }

    public static Date getSpecificTime(Integer interval) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MINUTE, interval);
        return calendar.getTime();
    }


    public static long getDifSecond(Date targetDate) {
        return Math.max(0, Seconds.secondsBetween(DateTime.now(), new DateTime(targetDate)).getSeconds());
    }

    public static String convertLocalDateTime2Str(LocalDateTime localDateTime) {
        return convertLocalDateTime2Str(localDateTime, FORMAT);
    }

    public static String convertLocalDateTime2Str(LocalDateTime localDateTime, String format) {
        try {
            if (localDateTime == null) {
                return null;
            }
            DateTimeFormatter formatter = DateTimeFormat.forPattern(format);
            return localDateTime.toString(formatter);
        } catch (Exception e) {
            log.error("[TimeUtil] convertLocalDateTime2Str error, localDateTime={}", localDateTime, e);
        }
        return null;
    }

    public static LocalDateTime convertStr2LocalDateTime(String dateStr) {
        LocalDateTime result = convertStr2LocalDateTime(dateStr, FORMAT);
        if (result != null) {
            return result;
        }
        result = convertStr2LocalDateTime(dateStr, FORMAT_WITHOUT_SECOND);
        if (result != null) {
            return result;
        }
        return convertStr2LocalDateTime(dateStr, FORMAT_DAY);
    }

    public static LocalDateTime convertStr2LocalDateTime(String dateStr, String format) {
        try {
            if (StringUtils.isEmpty(dateStr)) {
                return null;
            }
            DateTimeFormatter formatter = DateTimeFormat.forPattern(format);
            return LocalDateTime.parse(dateStr, formatter);
        } catch (Exception e) {
            log.error("[TimeUtil] convertStr2LocalDateTime error, dateStr={}", dateStr, e);
        }
        return null;
    }

    public static Long convertStr2MillSeconds(String dateStr) {
        if (StringUtils.isEmpty(dateStr)) {
            return null;
        }
        LocalDateTime localDateTime = convertStr2LocalDateTime(dateStr);
        return convertLocalDateTime2MillSeconds(localDateTime);
    }

    public static Long convertLocalDateTime2MillSeconds(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return null;
        }
        return localDateTime.toDate().getTime();
    }

    /**
     * 计算最近的合法时间点
     * @param time 当前时间
     * @param interval 时间间隔（分钟）
     * @return 最近的合法时间点
     */
    public static LocalDateTime calculateNearestValidTime(LocalDateTime time, int interval) {
        int minutes = time.getMinuteOfHour();
        int remainder = minutes % interval;
        LocalDateTime nearestTime;
        if (remainder < interval / 2) {
            // 向下取整到最近的间隔时间
            nearestTime = time.minusMinutes(remainder);
        } else {
            // 向上取整到最近的间隔时间
            nearestTime = time.plusMinutes(interval - remainder);
        }
        // 如果计算出的时间早于当前时间，则向上取整到下一个间隔时间
        LocalDateTime now = LocalDateTime.now();
        if (nearestTime.isBefore(now)) {
            nearestTime = now.plusMinutes(interval - (now.getMinuteOfHour() % interval));
        }
        return nearestTime;
    }

    /**
     * 将时间格式化为友好的字符串
     * 今天、明天、后天显示为对应文字，其他日期显示具体日期和星期
     */
    public static String formatTimeToFriendlyString(LocalDateTime time) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime tomorrow = now.plusDays(1);
        LocalDateTime dayAfterTomorrow = now.plusDays(2);

        String dateStr;
        if (time.toLocalDate().equals(now.toLocalDate())) {
            dateStr = String.format("%s（今天）", time.toString("yyyy.MM.dd"));
        } else if (time.toLocalDate().equals(tomorrow.toLocalDate())) {
            dateStr = String.format("%s（明天）", time.toString("yyyy.MM.dd"));
        } else if (time.toLocalDate().equals(dayAfterTomorrow.toLocalDate())) {
            dateStr = String.format("%s（后天）", time.toString("yyyy.MM.dd"));
        } else {
            dateStr = time.toString("yyyy.MM.dd");
            dateStr += "（周" + getChineseWeekDay(time) + "）";
        }

        return dateStr + " " + time.toString("HH:mm");
    }

    /**
     * 获取中文星期几
     */
    public static String getChineseWeekDay(LocalDateTime time) {
        int dayOfWeek = time.getDayOfWeek();
        switch (dayOfWeek) {
            case 1: return "一";
            case 2: return "二";
            case 3: return "三";
            case 4: return "四";
            case 5: return "五";
            case 6: return "六";
            case 7: return "日";
            default: return "";
        }
    }

    /**
     * 睡眠
     * @param initTime 初始时间
     * @param base 基数
     * @param maxDelayTime 指数
     */
    public static void sleepDelay(int initTime, int base, int maxDelayTime) {
        try {
            long delay = (long) Math.min(initTime * Math.pow(base, maxDelayTime), Long.MAX_VALUE);
            Thread.sleep(delay);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("[TimeUtil] sleepDelay error", e);
        }
    }
}
