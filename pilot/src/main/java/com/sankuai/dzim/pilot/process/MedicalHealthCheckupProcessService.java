package com.sankuai.dzim.pilot.process;

import com.sankuai.dzim.pilot.dal.entity.pilot.medical.DiseaseScienceInfoEntity;

/**
 * @author: zhouyibing
 * @date: 2024/9/6
 */
public interface MedicalHealthCheckupProcessService {
    /**
     * 根据疾病名称和知识生成页面内容
     *
     * @param diseaseName 疾病名称
     * @param knowledge   知识内容
     * @return 生成的页面内容
     */
    String generateContent(String diseaseName, String knowledge);

    /**
     * 根据疾病名称和知识生成页面内容
     *
     * @param entity 疾病信息实体
     * @return 生成的页面内容
     */
    String generateQuestion(DiseaseScienceInfoEntity entity);

    /**
     * 将生成的内容保存到数据库
     *
     * @param entity 生成的内容
     * @return 记录的主键ID， -1表示保存失败
     */
    DiseaseScienceInfoEntity insertContentToDatabase(DiseaseScienceInfoEntity entity);

    /**
     * 将疾病名插入向量数据库
     *
     * @param diseaseName 疾病名
     * @param id      数据库中对应记录的主键ID
     * @return 向量ID
     */
    long insertIntoVectorDatabase(String diseaseName, long id, String source);

    /**
     * 更新数据库中的向量ID
     *
     * @param id       记录主键
     * @param vectorId 向量ID
     */
    void updateDatabaseWithVectorId(long id, long vectorId);

    /**
     * 将生成的内容更新到数据库
     *
     * @param entity 生成的内容
     */
    void updateDatabaseWithEntity(DiseaseScienceInfoEntity entity);

    /**
     * 根据疾病名称检索知识
     *
     * @param diseaseName 疾病名称
     * @param topK        返回的最大记录数
     * @param source      知识来源
     * @return 检索到的知识
     */
    String retrievalKnowledge(String diseaseName, int topK, String source);
}
