package com.sankuai.dzim.pilot.process.search.strategy.filter;

import com.google.common.collect.Maps;
import com.sankuai.dzim.pilot.process.search.data.FilterOption;
import com.sankuai.dzim.pilot.process.search.data.ReserveQueryContext;
import com.sankuai.dzim.pilot.process.search.enums.IndustryBackendCategoryEnum;
import com.sankuai.dzim.pilot.process.search.enums.ProductFilterOptionEnum;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.joda.time.DateTime;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class ReserveProductDateFilterHandler implements ReserveProductFilterHandler {

    @Override
    public boolean match(ReserveQueryContext context) {
        if (context == null || CollectionUtils.isEmpty(context.getFilterOptions())) {
            return false;
        }
        List<String> filterTypeList = context.getFilterOptions().stream().map(FilterOption::getFilterType)
                .collect(Collectors.toList());
        return filterTypeList.contains(ProductFilterOptionEnum.DATE.getCode());
    }

    @Override
    public List<DealGroupDTO> filter(ReserveQueryContext context, List<DealGroupDTO> dealGroupDTOList) {
        // 获取日期具体筛选条件
        FilterOption filterOption = context.getFilterOptions().stream()
                .filter(option -> ProductFilterOptionEnum.DATE.getCode().equals(option.getFilterType())).findFirst()
                .orElse(null);
        long bookDate = isSpecificDate(filterOption) ? getSpecificDate(context) : DateTime.now().withTimeAtStartOfDay().getMillis();
        int weekDay = convertWeekDay(bookDate);

        // 将用户筛选的日期填充入上下文
        paddingBookDate(context, bookDate);

        return filterDateProduct(dealGroupDTOList, context, weekDay);
    }

    private void paddingBookDate(ReserveQueryContext context, long bookDate) {
        Map<String, Object> customPaddingParam = context.getCustomPaddingParam();
        if (customPaddingParam == null) {
            customPaddingParam = Maps.newHashMap();
            customPaddingParam.put("selectDate", new Date(bookDate));
            customPaddingParam.put("beginDateTime", bookDate);
            customPaddingParam.put("endDateTime", bookDate);
            context.setCustomPaddingParam(customPaddingParam);
            return;
        }
        customPaddingParam.put("selectDate", new Date(bookDate));
        customPaddingParam.put("beginDateTime", bookDate);
        customPaddingParam.put("endDateTime", bookDate);
    }

    private int convertWeekDay(Long specificDate) {
        if (specificDate == null) {
            return DateTime.now().getDayOfWeek();
        }
        // 将准确的时间戳转化成未来一周的周几列表，这是连续的日期列表
        return new DateTime(specificDate).getDayOfWeek();
    }

    private Long getSpecificDate(ReserveQueryContext context) {
        if (context == null || CollectionUtils.isEmpty(context.getFilterOptions())) {
            return null;
        }
        Map<String, Object> values = context.getFilterOptions().stream()
                .filter(option -> ProductFilterOptionEnum.DATE.getCode().equals(option.getFilterType()))
                .map(FilterOption::getValues).findFirst().orElse(null);
        if (MapUtils.isEmpty(values)) {
            return null;
        }
        return (Long)values.get("bookDate");
    }

    /**
     * 是否需要指定日期的预订商品
     */
    private boolean isSpecificDate(FilterOption filterOption) {
        if (filterOption == null) {
            return false;
        }
        Map<String, Object> values = filterOption.getValues();
        if (MapUtils.isEmpty(values)) {
            return false;
        }
        return Objects.equals(values.get("filterSpecificDate"), Boolean.TRUE);
    }

    private List<DealGroupDTO> filterDateProduct(List<DealGroupDTO> dealGroupDTOList, ReserveQueryContext context,
            int weekToday) {
        if (CollectionUtils.isEmpty(dealGroupDTOList)) {
            return dealGroupDTOList;
        }
        // 获取今天的weekday，星期一至星期天
        // 过滤留下今天对应的的product
        List<DealGroupDTO> filteredTodayProducts = dealGroupDTOList.stream().filter(dealGroupDTO -> {
            if (dealGroupDTO.getAttrs() == null) {
                return true;
            }
            AttrDTO attrDTO = dealGroupDTO.getAttrs().stream()
                    .filter(attr -> attr.getName().equals(buildWeekDayAttrName(context))).findFirst().orElse(null);
            if (attrDTO == null) {
                return true;
            }
            return attrDTO.getValue().contains(String.valueOf(weekToday));
        }).collect(Collectors.toList());

        // 过滤留下今天对应的sku
        filteredTodayProducts.forEach(dealGroupDTO -> {
            if (CollectionUtils.isEmpty(dealGroupDTO.getDeals())) {
                return;
            }
            dealGroupDTO.getDeals().removeIf(deal -> {
                boolean invalidResult = isInvalidResult(deal);
                if (invalidResult) {
                    return true;
                }
                AttrDTO attrDTO = deal.getAttrs().stream().filter(attr -> attr.getName().equals("week")).findFirst()
                        .orElse(null);
                if (attrDTO == null) {
                    return true;
                }
                return !attrDTO.getValue().contains(String.valueOf(weekToday));
            });
        });

        return filteredTodayProducts;
    }

    private boolean isInvalidResult(DealGroupDealDTO deal) {
        if (CollectionUtils.isEmpty(deal.getAttrs())) {
            return true;
        }
        if (deal.getBasic() == null) {
            return true;
        }
        return deal.getBasic().getStatus() != 1;
    }

    private String buildWeekDayAttrName(ReserveQueryContext context) {
        if (IndustryBackendCategoryEnum.KTV.getCategoryIds().contains(context.getShopBackCategoryId())) {
            return "resource_attr_ktv_period_dayOfWeek";
        } else if (IndustryBackendCategoryEnum.CHESS.getCategoryIds().contains(context.getShopBackCategoryId())) {
            return "resource_attr_chess_card_period_dayOfWeek";
        }
        return "week";
    }

}
