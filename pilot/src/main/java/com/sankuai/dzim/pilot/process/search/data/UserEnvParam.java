package com.sankuai.dzim.pilot.process.search.data;

import lombok.Data;

import java.io.Serializable;

@Data
public class UserEnvParam implements Serializable {
    /**
     * 城市id
     */
    private int cityId;

    /**
     * 客户端类型 @see com.sankuai.dzshoplist.search.mtservice.aggregate.render.enums.ClientTypeEnum
     */
    private int clientType;

    /**
     * 纬度
     */
    private Double lat;

    /**
     * 经度
     */
    private Double lng;

    /**
     * 美团设备Id
     */
    private String uuId;

    /**
     * 点评侧设备Id
     */
    private String dpId;

    /**
     * 用户Id
     */
    private long userId;

    /**
     * 客户端版本
     */
    private String version;

}
