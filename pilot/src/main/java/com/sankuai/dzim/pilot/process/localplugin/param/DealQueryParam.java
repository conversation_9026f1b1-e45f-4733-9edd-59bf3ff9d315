package com.sankuai.dzim.pilot.process.localplugin.param;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import lombok.Data;

@Data
public class DealQueryParam {

    @JsonPropertyDescription("需要查询的团购Id,团购Id请从系统消息中获取")
    @JsonProperty(required = true)
    private long dealGroupId;

    @JsonPropertyDescription("与团购相关的具体问题,越具体越好,最好是一个问句,例如“价格是多少?”、“限购吗?”等")
    @JsonProperty(required = true)
    private String question;
}
