package com.sankuai.dzim.pilot.process.search.strategy.padding;

import com.sankuai.dzim.pilot.process.search.data.ReserveProductM;
import com.sankuai.dzim.pilot.process.search.data.ReserveQueryContext;

import java.util.List;


public interface ReserveProductPaddingHandler {

    /**
     * 填充商品信息
     */
    List<ReserveProductM> padding(List<ReserveProductM> products, ReserveQueryContext queryContext);

    /**
     * 是否匹配当前策略
     */
    boolean match(ReserveQueryContext queryContext);
}
