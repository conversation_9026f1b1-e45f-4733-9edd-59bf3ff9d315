package com.sankuai.dzim.pilot.process.localplugin.data;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.sankuai.dzim.pilot.domain.message.AssistantMessage;
import com.sankuai.dzim.pilot.domain.message.Message;
import com.sankuai.dzim.pilot.domain.message.PluginResultMessage;
import com.sankuai.dzim.pilot.domain.message.SystemMessage;
import com.sankuai.dzim.pilot.domain.message.UserMessage;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/03/14 10:41
 * 插件使用过程中用户上下文
 */
@Data
public class UserContext {
    public UserContext(){}
    public UserContext(String imUserId, String userQuery, long chatGroupId, int assistantType, String userIp) {
        this.imUserId = imUserId;
        this.userQuery = userQuery;
        this.chatGroupId = chatGroupId;
        this.assistantType = assistantType;
        this.userIp = userIp;
        this.userId = parseUserId(imUserId);
    }
    private String imUserId;
    private long userId;
    // 用户原始输入
    private String userQuery;
    // 当前会话
    private long chatGroupId;
    // 助手类型
    private int assistantType;

    // 插件查到的门店id
    private List<Long> shopIds;
    // 插件查到的团购id
    private List<Long> dealIds;
    // 历史消息
    @JsonTypeInfo(use = JsonTypeInfo.Id.CLASS, include = JsonTypeInfo.As.PROPERTY, property = "@class")
    @JsonSubTypes({
            @JsonSubTypes.Type(value = AssistantMessage.class),
            @JsonSubTypes.Type(value = UserMessage.class),
            @JsonSubTypes.Type(value = SystemMessage.class),
            @JsonSubTypes.Type(value = PluginResultMessage.class)
    })
    private List<Message> memory;
    // 用户ip
    private String userIp;

    private long parseUserId(String imUserId) {
        if (StringUtils.isBlank(imUserId)) {
            return 0L;
        }
        String userIdStr = imUserId.substring(1);
        return NumberUtils.toLong(userIdStr, 0L);
    }
}
