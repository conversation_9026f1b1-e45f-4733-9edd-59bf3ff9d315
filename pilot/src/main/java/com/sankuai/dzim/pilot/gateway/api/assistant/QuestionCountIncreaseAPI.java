package com.sankuai.dzim.pilot.gateway.api.assistant;

import com.dianping.gateway.enums.APIModeEnum;
import com.dianping.vc.web.api.API;
import com.dianping.vc.web.api.URL;
import com.dianping.vc.web.api.VCAssert;
import com.dianping.vc.web.biz.client.api.MTUserIdAware;
import com.dianping.vc.web.biz.client.api.UserIdAware;
import com.sankuai.dzim.pilot.gateway.api.vo.MessageOperateVO;
import com.sankuai.dzim.pilot.process.QuestionScoreProcessService;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/08/20 12:56
 * 移动之家: https://mobile.sankuai.com/studio/api/22002/index?tab=management
 */
@URL(url = "/dzim/pilot/assistant/question/count/increase", mode = APIModeEnum.CLIENT, methods = "POST")
@Slf4j
@Service
public class QuestionCountIncreaseAPI implements API, UserIdAware, MTUserIdAware {

    @Setter
    private long questionId;

    @Setter
    private int type;

    @Setter
    private long mTUserId;

    @Setter
    private long userId;

    @Autowired
    private QuestionScoreProcessService questionScoreProcessService;

    @Override
    @Deprecated
    public Object execute() {
        /**
         * 废弃，以SSE接口统一上报
         */
        checkParam();
        return MessageOperateVO.builder().isSuccess(1).build();
    }

    private void checkParam() {
        VCAssert.isTrue(userId > 0 || mTUserId > 0, API.UNLOGIN_CODE, "未登陆");
        VCAssert.isTrue(questionId > 0, API.INVALID_PARAM_CODE, "questionId不能为空");
    }
}
