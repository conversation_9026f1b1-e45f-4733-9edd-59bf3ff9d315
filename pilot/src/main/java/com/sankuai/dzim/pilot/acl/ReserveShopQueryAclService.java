package com.sankuai.dzim.pilot.acl;

import com.dianping.tpfun.product.api.sku.booktable.dto.ProductBookTable;
import com.dianping.tpfun.product.api.sku.booktable.request.QueryBookTableByShopRequest;
import com.sankuai.dztheme.generalproduct.req.GeneralProductRequest;
import com.sankuai.dztheme.generalproduct.res.GeneralProductResult;
import com.sankuai.general.product.query.center.client.request.QueryDealGroupIdByShopRequest;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupIdListResult;

import java.util.List;
import java.util.concurrent.CompletableFuture;


public interface ReserveShopQueryAclService {


    /**
     * 查询门店在线预订商品
     */
    List<QueryDealGroupIdListResult> queryProductByShopId(QueryDealGroupIdByShopRequest request);

    /**
     * 查询泛商品主题服务获取商品信息
     */
    CompletableFuture<GeneralProductResult> queryGeneralProductTheme(GeneralProductRequest request);
}
