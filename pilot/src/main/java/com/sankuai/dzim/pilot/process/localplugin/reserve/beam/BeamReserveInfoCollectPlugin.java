package com.sankuai.dzim.pilot.process.localplugin.reserve.beam;

import com.alibaba.fastjson.JSON;
import com.dianping.dzim.common.enums.ImUserType;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.clr.content.process.common.enums.CustomProjectCodeEnum;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.acl.*;
import com.sankuai.dzim.pilot.acl.data.LeadsProjectInfo;
import com.sankuai.dzim.pilot.api.data.AIAnswerTypeEnum;
import com.sankuai.dzim.pilot.api.enums.ProductTypeEnum;
import com.sankuai.dzim.pilot.api.enums.assistant.PlatformEnum;
import com.sankuai.dzim.pilot.buffer.core.PilotBufferItemDO;
import com.sankuai.dzim.pilot.buffer.enums.BufferItemTypeEnum;
import com.sankuai.dzim.pilot.buffer.utils.BufferUtils;
import com.sankuai.dzim.pilot.chain.data.AIAnswerData;
import com.sankuai.dzim.pilot.domain.annotation.LocalPlugin;
import com.sankuai.dzim.pilot.domain.retrieval.data.RetrievalContext;
import com.sankuai.dzim.pilot.domain.retrieval.impl.DateAugmentor;
import com.sankuai.dzim.pilot.process.aibook.AIBookCacheProcessService;
import com.sankuai.dzim.pilot.process.aireservebook.AIReserveBookProcessService;
import com.sankuai.dzim.pilot.process.aireservebook.AIReserveBookQueryService;
import com.sankuai.dzim.pilot.process.aireservebook.data.*;
import com.sankuai.dzim.pilot.process.aireservebook.enums.*;
import com.sankuai.dzim.pilot.process.data.ProductRetrievalData;
import com.sankuai.dzim.pilot.process.data.ProductStockData;
import com.sankuai.dzim.pilot.process.localplugin.data.ReserveReplyAnswerData;
import com.sankuai.dzim.pilot.process.localplugin.param.ShopProductSearchParam;
import com.sankuai.dzim.pilot.process.localplugin.reserve.param.BeamReserveConfirmParam;
import com.sankuai.dzim.pilot.process.search.SearchProcessService;
import com.sankuai.dzim.pilot.process.search.data.TimeSliceM;
import com.sankuai.dzim.pilot.scene.data.AssistantSceneContext;
import com.sankuai.dzim.pilot.utils.*;
import com.sankuai.dzim.pilot.utils.context.RequestContext;
import com.sankuai.dzim.pilot.utils.context.RequestContextConstants;
import com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.MtPoiDTO;
import com.sankuai.wpt.user.retrieve.thrift.message.UserModel;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.sankuai.mpproduct.idservice.api.enums.BizSkuIdType.DP_FUN_SKU_ID;


/**
 * beam预约信息收集工具
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class BeamReserveInfoCollectPlugin {

    @Autowired
    private AIBookCacheProcessService aiBookCacheProcessService;

    @Resource
    private SupplyUtils supplyUtils;

    @Autowired
    private IdService idService;

    @Autowired
    private ShopAclService shopAclService;

    @Autowired
    private DateAugmentor dateAugmentor;

    @Autowired
    private HaimaAclService haimaAclService;

    @Autowired
    private LionConfigUtil lionConfigUtil;

    @Autowired
    private PluginContextUtil pluginContextUtil;

    @Autowired
    private LeadsAclService leadsAclService;

    @Autowired
    private AIReserveBookQueryService aiReserveBookQueryService;

    @Autowired
    private AIReserveBookProcessService aiReserveBookProcessService;

    @Autowired
    private UserAclService userAclService;

    @Autowired
    private ProductAclService productAclService;

    @Autowired
    private SearchProcessService searchProcessService;

    @Autowired
    private AIChatUtil aiChatUtil;

    @ConfigValue(key = "com.sankuai.mim.pilot.beam.reserve.book.agent.feedback.config", defaultValue = "{}")
    private Map<String, String> beamFeedBackConfig;

    @ConfigValue(key = "com.sankuai.mim.pilot.beam.reserve.info.collect.summary.prompt", defaultValue = "{}")
    private String beamReserveInfoCollectSummary;

    @ConfigValue(key = "com.sankuai.mim.pilot.beam.reserve.info.collect.summary.text", defaultValue = "{}")
    private Map<String, String> beamReserveInfoCollectText;

    private static ThreadPool threadPool = Rhino.newThreadPool("BeamReserveInfoCollectToolPool",
            DefaultThreadPoolProperties.Setter().withCoreSize(50).withMaxSize(100).withMaxQueueSize(100));

    private static final DateTimeFormatter formatterWithoutHM = DateTimeFormat.forPattern("yyyy-MM-dd");
    private static final int PET_DEFAULT_SERVICE_TIME = 60;
    private static final String AI_PHONE_DEFAULT_SERVICE_TIME = "60";

    @LocalPlugin(name = "BeamReserveElementCollectTool", description = "如果用户最新消息是输入/修改预约信息（即使已完成预约，修改预约信息也由我处理），由我来处理", returnDirect = false)
    public AIAnswerData beamReserveElementCollect(BeamReserveConfirmParam param) {
        log.info("调用插件-BeamReserveElementCollectTool，入参={}", param);
        try {
            // 1. 基础校验
            pluginContextUtil.replaceShopIdWithBeamContext(param, lionConfigUtil.getTestShopIdReplaceConfig());

            if (!validateBasicParams(param)) {
                return BeamReserveBookCardUtils.writeBeamTextAnswer(beamFeedBackConfig.getOrDefault(ReserveAnswerConstants.POI_NOT_EXIST, ReserveAnswerConstants.FEEDBACK_ANSWER));
            }

            // 2. 获取上下文信息
            AssistantSceneContext assistantSceneContext = RequestContext.getAttribute(RequestContextConstants.ASSISTANT_CONTEXT);
            int platform = getPlatform(assistantSceneContext);

            // 3. 获取门店信息
            DpPoiDTO dpPoiDTO = getDpShopInfo(param, platform);
            if (dpPoiDTO == null) {
                return BeamReserveBookCardUtils.writeBeamTextAnswer(beamFeedBackConfig.getOrDefault(ReserveAnswerConstants.POI_NOT_EXIST, ReserveAnswerConstants.FEEDBACK_ANSWER));
            }

            // 4. 获取行业类型
            POIIndustryType poiIndustryType = getPoiIndustryType(dpPoiDTO, param);
            if (poiIndustryType == null) {
                return BeamReserveBookCardUtils.writeBeamTextAnswer(beamFeedBackConfig.getOrDefault(ReserveAnswerConstants.SHOP_NOT_SUPPORT, ReserveAnswerConstants.FEEDBACK_ANSWER));
            }

            // 5. 获取前置信息
            FutureContext futureContext = buildFutureContext(assistantSceneContext, dpPoiDTO, poiIndustryType, param);
            // 获取用户预约信息
            UserReserveInfo userReserveInfo = aiReserveBookQueryService.queryBeamUserReserveInfoWithOrderCheck(param,
                    assistantSceneContext.getUserId(), param.getShopId(), platform);
            // 检查重复预约
            if (aiReserveBookProcessService.checkUserDuplicateReserve(userReserveInfo)) {
                if (aiReserveBookProcessService.checkTimeValid(userReserveInfo)) {
                    return BeamReserveBookCardUtils.writeBeamTextAnswer(beamFeedBackConfig.getOrDefault(ReserveAnswerConstants.USER_DUPLICATE_RESERVE, ReserveAnswerConstants.FEEDBACK_ANSWER));
                }
                // 预约时间早于当前时间，则不认为是重复预约，清空记录
                userReserveInfo = null;
            }

            boolean isAfterOrder = validAndSetAfterOrder(param, userReserveInfo);
            if (isAfterOrder) {
                addDealGroupInfoFuture(futureContext, param);
            }

            // 构建结果Context
            ResultContext resultContext = buildResultContext(futureContext, dpPoiDTO, poiIndustryType);
            log.info("调用插件-BeamReserveElementCollectTool_resultContext={}", JSON.toJSONString(resultContext));
            addUserReserveInfo(resultContext, userReserveInfo);

            // 6. 构建Prompt并执行
            String retrievalContext = buildRetrievalContext(param, resultContext, isAfterOrder);
            log.info("调用插件-BeamReserveElementCollectTool_retrievalContext, param={}, retrievalContext={}", param, retrievalContext);
            AIAnswerData answer = aiChatUtil.chat("beam_reserve_element_collect", assistantSceneContext.getMessageDTO().getMessage(),
                    assistantSceneContext.getExtra(), retrievalContext);
            log.info("调用插件-BeamReserveElementCollectTool_answer={}, param={}", answer, param);
            if (answer == null || StringUtils.isEmpty(answer.getAnswer())) {
                return BeamReserveBookCardUtils.writeBeamTextAnswer(beamFeedBackConfig.getOrDefault(ReserveAnswerConstants.FAILED_ANSWER, ReserveAnswerConstants.FEEDBACK_ANSWER));
            }

            // 7. 处理大模型回复
            AIAnswerData aiAnswerData = processAnswer(answer, param, resultContext, isAfterOrder);

            //8. 总结一下，在工具内发送buffer，外面不发了
            AIAnswerData summaryAiAnswerData = summaryBookAnswer(param, aiAnswerData);
            return summaryAiAnswerData;
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("beamReserveElementCollect").build(),
                    new WarnMessage("BeamReserveElementCollectTool", "beam预约要素追问插件异常", ""), param, e);
            log.error("调用插件-BeamReserveElementCollectTool，异常，param={}", param, e);
            return BeamReserveBookCardUtils.writeBeamTextAnswer(ReserveAnswerConstants.FEEDBACK_ANSWER);
        }
    }

    private AIAnswerData summaryBookAnswer(BeamReserveConfirmParam param, AIAnswerData answerData) {
        ReserveReplyAnswerData reserveReplyAnswerData = com.dianping.degrade.util.JsonCodec.decode(answerData.getAnswer(), ReserveReplyAnswerData.class);
        if (reserveReplyAnswerData == null) {
            log.error("调用插件-BeamReserveElementCollectTool，解析reserveReplyAnswerData异常，param={}, answerData={}", param, answerData);
            return BeamReserveBookCardUtils.writeBeamTextAnswer("预约预订失败,请稍后重试～");
        }

        // 直接回复
        if (ReserveReplyType.DIRECT.getType() == reserveReplyAnswerData.getReserveReplyType()) {
            return BeamReserveBookCardUtils.writeBeamTextAnswer(reserveReplyAnswerData.getAnswer());
        }

        // 缺乏某些要素，追问
        if (ReserveReplyType.GENERATE.getType() == reserveReplyAnswerData.getReserveReplyType()) {
            String instruction = String.format(beamReserveInfoCollectSummary, reserveReplyAnswerData.getAnswer());
            return BeamReserveBookCardUtils.writeBeamModelGenerateAnswer(instruction);
        }

        // 直接回复，写入卡片消息
        if (ReserveReplyType.CARD.getType() == reserveReplyAnswerData.getReserveReplyType()) {
            // 预约确认\开始预约\取消预约
            answerData.setReturnDirect(true);
            BufferUtils.writeMainTextBuffer(PilotBufferItemDO.builder().data("没问题，现在帮你预约吗？").build());
            BufferUtils.writeMainTextBuffer(buildPilotBufferItemDO(reserveReplyAnswerData.getCardTag(), reserveReplyAnswerData.getExtraInfo()));
            return answerData;
        }

        return BeamReserveBookCardUtils.writeBeamTextAnswer("预约预订失败,请稍后重试～～");
    }

    private PilotBufferItemDO buildPilotBufferItemDO(String beamCardContent, Map<String, Object> extraInfo) {
        PilotBufferItemDO pilotBufferItemDO = new PilotBufferItemDO();
        pilotBufferItemDO.setData(beamCardContent);
        pilotBufferItemDO.setExtra(extraInfo);
        pilotBufferItemDO.setType(BufferItemTypeEnum.MAIN_TEXT.getType());
        return pilotBufferItemDO;
    }

    private FutureContext buildFutureContext(AssistantSceneContext assistantSceneContext, DpPoiDTO dpPoiDTO,
                                             POIIndustryType poiIndustryType, BeamReserveConfirmParam param) {
        FutureContext context = new FutureContext();

        // 5.1 门店预约方式
        CompletableFuture<ShopReserveWayType> shopReserveWayTypeF = CompletableFuture.supplyAsync(
                () -> aiReserveBookQueryService.queryShopReserveWayType(dpPoiDTO.getMtPoiId()),
                threadPool.getExecutor());

        // 5.2 标准预约要素
        CompletableFuture<List<StandardReserveItem>> standardReserveItemsF = CompletableFuture.supplyAsync(
                () -> haimaAclService.queryBeamStandardReserveItems(poiIndustryType.getSecondBackCategoryId()),
                threadPool.getExecutor());

        // 5.3 用户信息
        CompletableFuture<UserModel> userModelF = CompletableFuture.supplyAsync(
                () -> userAclService.queryUserModel(assistantSceneContext.getUserId()),
                threadPool.getExecutor());

        // 5.4 门店可约项目信息（目前仅美发）
        CompletableFuture<List<LeadsProjectInfo>> leadsProjectInfoF = getLeadsProjectInfo(poiIndustryType, dpPoiDTO);

        //5.5 门店可预约泛商品（目前仅酒吧、宠物）
        CompletableFuture<List<ProductRetrievalData>> shopReserveProductF = getShopReserveProduct(poiIndustryType, param);

        // 5.6 获取团购高销商品
        CompletableFuture<ProductRetrievalData> topProductF = getTopProduct(poiIndustryType, param);

        // 设置上下文信息
        context.setUserModelF(userModelF);
        context.setStandardReserveItemsF(standardReserveItemsF);
        context.setLeadsProjectInfoF(leadsProjectInfoF);
        context.setTopProductF(topProductF);
        context.setShopReserveProductsF(shopReserveProductF);
        context.setShopReserveWayTypeF(shopReserveWayTypeF);

        return context;
    }

    private ResultContext buildResultContext(FutureContext futureContext, DpPoiDTO dpPoiDTO, POIIndustryType poiIndustryType) {
        ResultContext context = new ResultContext();

        // 获取join后的结果
        ShopReserveWayType shopReserveWayType = futureContext.getShopReserveWayTypeF() != null ?
                futureContext.getShopReserveWayTypeF().join() : ShopReserveWayType.PHONE;
        List<StandardReserveItem> standardReserveItems = futureContext.getStandardReserveItemsF().join();
        UserModel userModel = futureContext.getUserModelF().join();
        List<LeadsProjectInfo> leadsProjectInfos = futureContext.getLeadsProjectInfoF() != null ?
                futureContext.getLeadsProjectInfoF().join() : null;
        ProductRetrievalData topProduct = futureContext.getTopProductF() != null ?
                futureContext.getTopProductF().join() : null;
        List<ProductRetrievalData> shopReserveProducts = futureContext.getShopReserveProductsF() != null ?
                futureContext.getShopReserveProductsF().join() : null;
        DealGroupDTO dealGroupDTO = futureContext.getDealGroupDTOF() != null ? futureContext.getDealGroupDTOF().join() : null;

        // 设置结果
        context.setUserModel(userModel);
        context.setStandardReserveItems(standardReserveItems);
        context.setLeadsProjectInfos(leadsProjectInfos);
        context.setTopProduct(topProduct);
        context.setDealGroupDTO(dealGroupDTO);
        context.setShopReserveProducts(shopReserveProducts);
        context.setPoiIndustryType(poiIndustryType);

        // 构建门店预约上下文
        ShopReserveContext shopReserveContext = ShopReserveContext.buildShopReserveContext(dpPoiDTO, poiIndustryType, shopReserveWayType);
        fillShopReserveContextDefaultPic(shopReserveContext, dpPoiDTO);
        context.setShopReserveContext(shopReserveContext);

        return context;
    }

    private void fillShopReserveContextDefaultPic(ShopReserveContext shopReserveContext, DpPoiDTO dpPoiDTO) {
        try {
            if(shopReserveContext == null || shopReserveContext.getDefaultPic() != null) {
                return;
            }

            MtPoiDTO mtShopInfo = shopAclService.getMtShopInfo(dpPoiDTO.getMtPoiId());
            if (mtShopInfo == null) {
                return;
            }
            shopReserveContext.setDefaultPic(mtShopInfo.getFrontImg());

        } catch (Exception e) {
            log.error("fillShopReserveContextDefaultPic error", e);
        }
    }

    private void addUserReserveInfo(ResultContext context, UserReserveInfo userReserveInfo) {
        if (userReserveInfo == null) {
            userReserveInfo = new UserReserveInfo();
        }
        context.setUserReserveInfo(userReserveInfo);
    }

    private void addDealGroupInfoFuture(FutureContext context, BeamReserveConfirmParam param) {
        AssistantSceneContext assistantSceneContext = RequestContext.getAttribute(RequestContextConstants.ASSISTANT_CONTEXT);
        CompletableFuture<DealGroupDTO> dealGroupFuture = CompletableFuture.supplyAsync(
                () -> queryDealGroupInfo(param, getPlatform(assistantSceneContext)),
                threadPool.getExecutor()).exceptionally(e -> {
            log.error("[ReserveInfoCollectPlugin] queryDealGroupInfo error", e);
            return null;
        });
        context.setDealGroupDTOF(dealGroupFuture);
    }

    private boolean validAndSetAfterOrder(BeamReserveConfirmParam param, UserReserveInfo userReserveInfo) {
        if (param.getOrderId() != null && param.getOrderId() > 0 && param.getProductId() != null && param.getProductId() > 0) {
            return true;
        }
        if (userReserveInfo != null && userReserveInfo.getOrderId() != null && userReserveInfo.getOrderId() > 0) {
            param.setProductId(userReserveInfo.getDealId());
            param.setOrderId(userReserveInfo.getOrderId());
            return true;
        }
        return false;
    }

    private AIAnswerData processAnswer(AIAnswerData answer, BeamReserveConfirmParam param, ResultContext resultContext, boolean isAfterOrder) {
        AIAnswerData aiAnswerData = new AIAnswerData();
        aiAnswerData.setAiAnswerType(AIAnswerTypeEnum.TEXT.getType());

        ReserveInfo collectedReserveInfo = JsonCodec.decode(answer.getAnswer(), ReserveInfo.class);
        UserReserveInfo userReserveInfo = resultContext.getUserReserveInfo();

        // 1.要素完整性判断，要素完整且是在线系统则查库存
        ValidateResult completeJudgeAnswer = generateAnswer(collectedReserveInfo, userReserveInfo,
                resultContext, param, isAfterOrder);
        // 2.刷新已收集信息
        refreshCollectedReserveInfo(userReserveInfo, resultContext.getShopReserveContext());

        // 3.生成回答
        if (completeJudgeAnswer.isPass()) {
            aiAnswerData.setAnswer(generateCard(userReserveInfo, resultContext.getShopReserveContext(), resultContext.getDealGroupDTO(), isAfterOrder));
        } else {
            aiAnswerData.setAnswer(generateMessage(completeJudgeAnswer.getQuestion(), true));
        }
        log.info("调用插件-beamReserveElementCollectTool，结果={}", aiAnswerData.getAnswer());
        return aiAnswerData;
    }

    private boolean validateBasicParams(BeamReserveConfirmParam param) {
        if (MapUtils.isEmpty(beamFeedBackConfig)) {
            return false;
        }
        return param != null && param.getShopId() != null && param.getShopId() > 0;
    }

    private DpPoiDTO getDpShopInfo(BeamReserveConfirmParam param, int platform) {
        DpPoiDTO dpPoiDTO = aiReserveBookQueryService.queryDpPoiDTO(param.getShopId(), platform);
        return dpPoiDTO;
    }

    private POIIndustryType getPoiIndustryType(DpPoiDTO dpPoiDTO, BeamReserveConfirmParam param) {
        Integer secondBackCategoryId = aiReserveBookProcessService.extractMainSecondBackCategoryId(dpPoiDTO);
        return POIIndustryType.getBySecondBackCategoryId(secondBackCategoryId);
    }

    /**
     * 获取门店可约项目信息 目前仅仅美发查可约项目
     *
     * @param poiIndustryType
     * @param dpPoiDTO
     * @return
     */
    private CompletableFuture<List<LeadsProjectInfo>> getLeadsProjectInfo(POIIndustryType poiIndustryType, DpPoiDTO dpPoiDTO) {
        if (!POIIndustryType.HAIR.equals(poiIndustryType)) {
            return CompletableFuture.completedFuture(null);
        }
        return CompletableFuture.supplyAsync(() -> queryCanReserveProjectInfos(dpPoiDTO), threadPool.getExecutor());
    }

    /**
     * 获取门店可预约泛商品 目前仅酒吧
     *
     * @param poiIndustryType
     * @param beamReserveConfirmParam
     * @return
     */
    private CompletableFuture<List<ProductRetrievalData>> getShopReserveProduct(POIIndustryType poiIndustryType, BeamReserveConfirmParam beamReserveConfirmParam) {
        if (!(POIIndustryType.BAR.equals(poiIndustryType) || POIIndustryType.PET_SHOP.equals(poiIndustryType))) {
            return CompletableFuture.completedFuture(null);
        }

        return CompletableFuture.supplyAsync(() -> {
            ShopProductSearchParam param = new ShopProductSearchParam();
            param.setShopId(beamReserveConfirmParam.getShopId());
            param.setAiServiceContext(beamReserveConfirmParam.getAiServiceContext());
            List<ProductRetrievalData> productRetrievalDataList = searchProcessService.searchShopReserveProducts(param);
            if (CollectionUtils.isEmpty(productRetrievalDataList) || productRetrievalDataList.get(0) == null) {
                return null;
            }
            return productRetrievalDataList;
        }, threadPool.getExecutor());
    }

    private CompletableFuture<ProductRetrievalData> getTopProduct(POIIndustryType poiIndustryType, BeamReserveConfirmParam beamReserveConfirmParam) {
        // 美发不查询高销团购项目
        if (POIIndustryType.HAIR.equals(poiIndustryType)) {
            return CompletableFuture.completedFuture(null);
        }
        return CompletableFuture.supplyAsync(() -> {
            ShopProductSearchParam param = new ShopProductSearchParam();
            param.setShopId(beamReserveConfirmParam.getShopId());
            param.setAiServiceContext(beamReserveConfirmParam.getAiServiceContext());
            List<ProductRetrievalData> productRetrievalDataList = searchProcessService.searchShopProducts(param);
            if (CollectionUtils.isEmpty(productRetrievalDataList) || productRetrievalDataList.get(0) == null) {
                return null;
            }
            return productRetrievalDataList.get(0);
        }, threadPool.getExecutor());
    }

    private String buildRetrievalContext(BeamReserveConfirmParam param, ResultContext resultContext, boolean isAfterOrder) {
        AssistantSceneContext context = RequestContext.getAttribute(RequestContextConstants.ASSISTANT_CONTEXT);
        List<CompletableFuture<String>> futureList = Lists.newArrayList();
        // 查询当前时间
        futureList.add(CompletableFuture.supplyAsync(() -> getCurrentTime(), threadPool.getExecutor()));
        if (isAfterOrder) {
            // 先买后约场景
            futureList.add(CompletableFuture.supplyAsync(() -> queryReserveElementFromDeal(param.getProductId(), getPlatform(context)), threadPool.getExecutor()));
        }
        // 商户基本信息
        String basicShopInfo = buildBasicShopInfo(resultContext.getShopReserveContext(), getPlatform(context));
        // 标准预约要素表述转换
        String standardReserveElementDesc = queryStandardReserveElement(resultContext.getStandardReserveItems());
        // 商户预约方式类型转换
        String shopReserveWayTypeDesc = convertShopReserveWayType(resultContext.getShopReserveContext());
        // 美发行业需要输入门店可预约项目信息
        String leadsProjectInfoDesc = resultContext.getLeadsProjectInfos() == null ? null : convertLeadsProjectInfo(resultContext.getLeadsProjectInfos());
        //酒吧、宠物行业需要输入门店可购买泛商品信息
        String shopReserveProjectInfoDesc = resultContext.getShopReserveProducts() == null ? null : convertShopReserveProjectInfo(resultContext.getShopReserveContext(), resultContext.getShopReserveProducts());
        return CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).thenApply(v -> {
            // 添加商户基本信息
            StringBuilder sb = new StringBuilder();
            sb.append(basicShopInfo);
            if (resultContext.getShopReserveContext().getPoiIndustryType() != null) {
                String shopReserveType = POIIndustryType.HAIR.equals(resultContext.getShopReserveContext().getPoiIndustryType()) ? "预约" : "预订";
                sb.append(String.format("商户预约/预订类型: %s \n\n", shopReserveType));
            }
            sb.append(standardReserveElementDesc);
            if (StringUtils.isNotBlank(shopReserveWayTypeDesc)) {
                sb.append(shopReserveWayTypeDesc);
            }
            if (StringUtils.isNotBlank(leadsProjectInfoDesc)) {
                sb.append(leadsProjectInfoDesc);
            }
            if (StringUtils.isNotBlank(shopReserveProjectInfoDesc)) {
                sb.append(shopReserveProjectInfoDesc);
            }
            // 添加其他前置信息
            for (CompletableFuture<String> future : futureList) {
                sb.append(future.join());
            }
            return sb.toString();
        }).join();
    }

    private String convertShopReserveWayType(ShopReserveContext shopReserveContext) {
        if (shopReserveContext == null || shopReserveContext.getShopReserveWayType() == null) {
            return null;
        }
        return String.format("商户预约方式：%s \n\n", shopReserveContext.getShopReserveWayType().getDesc());
    }

    private String buildBasicShopInfo(ShopReserveContext context, int platform) {
        if (context == null) {
            return null;
        }
        long shopId = platform == PlatformEnum.MT.getType() ? context.getMtShopId() : context.getDpShopId();
        return String.format("商户ID：%s，商户名称：%s， 商户行业: %s \n\n", shopId, context.getShopName(), context.getPoiIndustryType().getDesc());
    }

    private String queryStandardReserveElement(List<StandardReserveItem> standardReserveItems) {
        if (CollectionUtils.isEmpty(standardReserveItems)) {
            return null;
        }
        return String.format("标准预约要素: %s \n\n", JSON.toJSONString(standardReserveItems));
    }

    private List<LeadsProjectInfo> queryCanReserveProjectInfos(DpPoiDTO dpPoiDTO) {
        if (dpPoiDTO == null || dpPoiDTO.getMtPoiId() == null || dpPoiDTO.getMtPoiId() <= 0) {
            return null;
        }
        return leadsAclService.queryCanReserveProjects(dpPoiDTO.getMtPoiId(), null);
    }

    private List<LeadsProjectInfo> filterReserveProjectInfos(List<LeadsProjectInfo> leadsProjectInfos) {
        if (CollectionUtils.isEmpty(leadsProjectInfos)) {
            return leadsProjectInfos;
        }
        return leadsProjectInfos.stream().filter(leadsProjectInfo -> leadsProjectInfo.getProjectCode() != null && leadsProjectInfo.getProjectCode() > 0
                        && CustomProjectCodeEnum.OTHER.getValue() != leadsProjectInfo.getProjectCode())
                .collect(Collectors.toList());
    }

    private String convertLeadsProjectInfo(List<LeadsProjectInfo> leadsProjectInfos) {
        if (CollectionUtils.isEmpty(leadsProjectInfos)) {
            return null;
        }
        StringBuilder sb = new StringBuilder("当前门店可预约项目: ");
        for (LeadsProjectInfo leadsProjectInfo : leadsProjectInfos) {
            if (leadsProjectInfo == null) {
                continue;
            }
            sb.append(String.format("项目名称: %s, 项目代码: %s", leadsProjectInfo.getProjectName(), leadsProjectInfo.getProjectCode()));
            String duration = extractDuration(leadsProjectInfo);
            if (StringUtils.isNotBlank(duration)) {
                sb.append(String.format("项目时长: %s", duration));
            }
            sb.append("\n\n");
        }
        return sb.toString();
    }

    private String convertShopReserveProjectInfo(ShopReserveContext shopReserveContext, List<ProductRetrievalData> shopReserveProducts) {
        if (CollectionUtils.isEmpty(shopReserveProducts)) {
            return null;
        }
        StringBuilder sb = new StringBuilder("当前门店可预订泛商品项目: \n");
        sb.append("| 项目名称 | 项目代码 | 项目描述 | 额外信息 |\n");
        sb.append("|-----------|--------|---------|---------|\n");
        for (ProductRetrievalData productRetrievalData : shopReserveProducts) {
            if (productRetrievalData == null) {
                continue;
            }
            paddingProductRetrievalData(shopReserveContext, productRetrievalData);
            sb.append(String.format("| %s | %d | %s | %s|\n",
                    supplyUtils.escapeMarkdown(productRetrievalData.getTitle()),
                    productRetrievalData.getMtProductId(),
                    supplyUtils.escapeMarkdown(extractProductDesc(productRetrievalData)),
                    supplyUtils.escapeMarkdown(fillPoiIndustryOtherInfo(shopReserveContext, productRetrievalData))));
        }
        return sb.toString();
    }

    private void paddingProductRetrievalData(ShopReserveContext shopReserveContext, ProductRetrievalData productRetrievalData) {
        //宠物增加服务时长
        if (POIIndustryType.PET_SHOP.equals(shopReserveContext.getPoiIndustryType())) {
            productRetrievalData.setDuration(extractPetServiceTime(productRetrievalData));
        }
    }

    private static String fillPoiIndustryOtherInfo(ShopReserveContext shopReserveContext, ProductRetrievalData productRetrievalData) {
        //酒吧增加库存信息
        if (POIIndustryType.BAR.equals(shopReserveContext.getPoiIndustryType())) {
            return (JSON.toJSONString(buildStockDisplayInfo(productRetrievalData.getStocks())));
        }
        return "";
    }

    //宠物服务时长
    private static int extractPetServiceTime(ProductRetrievalData productRetrievalData) {
        try {
            return Integer.parseInt(productRetrievalData.getProductDetails().getBasicProductInfo().get("服务用时"));
        } catch (Exception e) {
            log.error("extractServiceTime error, productRetrievalData:{}", productRetrievalData, e);
            return 30;
        }
    }

    private static Map<String, Object> buildStockDisplayInfo(ProductStockData stockData) {
        Map<String, Object> stockDisplayInfo = new HashMap<>();
        if (stockData == null || CollectionUtils.isEmpty(stockData.getTimeSlices())) {
            return stockDisplayInfo;
        }
        if (stockData.getEarliestBookableTime() == null || stockData.getEarliestBookableTime() <= 0) {
            return stockDisplayInfo;
        }
        stockDisplayInfo.put("最早可订日期", formatterWithoutHM.print(new DateTime(stockData.getEarliestBookableTime())));
        if (CollectionUtils.isNotEmpty(stockData.getTimeSlices())) {
            stockDisplayInfo.put("可订日期列表:", buildTimeSliceDisplayInfo(stockData.getTimeSlices()));
        }
        return stockDisplayInfo;
    }

    private static List<String> buildTimeSliceDisplayInfo(List<TimeSliceM> timeSlices) {
        List<String> timeSliceDisplayInfo = new ArrayList<>();
        for (TimeSliceM timeSlice : timeSlices) {
            if (!timeSlice.isAvailable() || timeSlice.getStartTime() == null || timeSlice.getStartTime() <= 0) {
                continue;
            }
            String day = formatterWithoutHM.print(new DateTime(timeSlice.getStartTime()));
            if (!timeSliceDisplayInfo.contains(day)) {
                timeSliceDisplayInfo.add(day);
            }
        }
        return timeSliceDisplayInfo;
    }

    private String extractProductDesc(ProductRetrievalData productRetrievalData) {
        if (productRetrievalData.getProductDetails() == null || productRetrievalData.getProductDetails().getBasicProductInfo() == null) {
            return null;
        }
        Map<String, String> basicProductInfo = productRetrievalData.getProductDetails().getBasicProductInfo();

        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> entry : basicProductInfo.entrySet()) {
            if (StringUtils.isBlank(entry.getValue())) {
                continue;
            }
            sb.append(String.format("%s: %s，", entry.getKey(), entry.getValue()));
        }
        return sb.toString();
    }

    private String extractDuration(LeadsProjectInfo leadsProjectInfo) {
        if (leadsProjectInfo == null || leadsProjectInfo.getProjectTime() == null) {
            return null;
        }
        LeadsProjectInfo.ProjectTime projectTime = leadsProjectInfo.getProjectTime();
        Integer timeType = projectTime.getTimeType();
        if (timeType == 1 && projectTime.getTimeValue() != null) {
            return projectTime.getTimeValue() + "分钟";
        }
        return null;
    }

    private String getCurrentTime() {
        RetrievalContext context = new RetrievalContext();
        return dateAugmentor.retrieval(context);
    }

    private String queryReserveElementFromDeal(Long dealId, int platform) {
        Map<ReserveItemType, String> reserveItems = aiReserveBookProcessService.queryAndConvertDealDetail2ReserveItems(dealId, platform);
        if (MapUtils.isEmpty(reserveItems)) {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        List<String> reserveItemKeys = Lists.newArrayList(ReserveItemType.PROJECT.getKey(), ReserveItemType.PROJECT_CODE.getKey(), ReserveItemType.DURATION.getKey());
        for (String reserveItemKey : reserveItemKeys) {
            ReserveItemType reserveItemType = ReserveItemType.getByKey(reserveItemKey);
            if (reserveItemType != null && reserveItems.get(reserveItemType) != null) {
                sb.append(String.format("%s: %s ", reserveItemType.getName(), reserveItems.get(reserveItemType)));
            }
        }
        if (sb.length() > 0) {
            return String.format("当前用户输入的最新预约要素: %s \n\n", sb);
        }
        return null;
    }

    private ValidateResult generateAnswer(ReserveInfo collectedReserveInfo, UserReserveInfo userReserveInfo, ResultContext resultContext,
                                          BeamReserveConfirmParam param, boolean isAfterOrder) {
        List<StandardReserveItem> standardReserveItems = resultContext.getStandardReserveItems();
        List<LeadsProjectInfo> leadsProjectInfos = resultContext.getLeadsProjectInfos();
        List<ProductRetrievalData> reserveProducts = resultContext.getShopReserveProducts();
        ProductRetrievalData topProduct = resultContext.getTopProduct();
        ShopReserveContext shopReserveContext = resultContext.getShopReserveContext();
        UserModel userModel = resultContext.getUserModel();
        // 1.预约要素为空，需要生成推荐文本
        if (collectedReserveInfo == null || MapUtils.isEmpty(collectedReserveInfo.getCollectedReserveItemMap())) {
            return ValidateResult.fail(generateRecommendText(standardReserveItems, shopReserveContext, leadsProjectInfos, topProduct, reserveProducts));
        }
        // 2.预约要素不为空，校验是否完整，不完整需要生成追问
        // 2.1 校验预约要素，校验失败则直接返回
        // 先买后约场景需要查询团单信息
        DealGroupDTO dealGroupDTO = null;
        if (isAfterOrder) {
            dealGroupDTO = resultContext.getDealGroupDTO();
        }
        ValidateResult validateResult = validateReserveItems(collectedReserveInfo, shopReserveContext, leadsProjectInfos, isAfterOrder, dealGroupDTO);
        if (!validateResult.isPass()) {
            return validateResult;
        }

        // 2.2 比较要素完整性，生成追问 遍历mustNeed，看看是否缺乏要素
        ValidateResult judgeElementCompleteResult = judgeReserveElementComplete(collectedReserveInfo, standardReserveItems, shopReserveContext, leadsProjectInfos, topProduct, reserveProducts);

        // 2.3 在线系统：当信息充足且是在线系统时，查询当前库存，如果没有库存则要求用户重新输入
        if (judgeElementCompleteResult.isPass() && ShopReserveWayType.ONLINE.equals(shopReserveContext.getShopReserveWayType())) {
            ValidateResult judgeStockResult = judgeStock(collectedReserveInfo, userReserveInfo, resultContext.getShopReserveProducts(), shopReserveContext, isAfterOrder, dealGroupDTO);
            if (!judgeStockResult.isPass()) {
                return judgeStockResult;
            }
        }

        // 3.更新预约要素
        ReserveInfo userRemoteReserveInfo = userReserveInfo.getCollectedReserveInfo();
        BeanUtils.copyProperties(collectedReserveInfo, userRemoteReserveInfo);
        // 更新预约信息中的订单和商品信息
        fillOrderAndProduct(userReserveInfo, param);

        // 当要素完整时，填充所有缺省信息
        if (judgeElementCompleteResult.isPass()) {
            fillCompleteCollectedReserveInfo(userReserveInfo, userModel, shopReserveContext);
        }
        //当要素不完整时，返回当前问题
        if (!judgeElementCompleteResult.isPass()) {
            return ValidateResult.fail(judgeElementCompleteResult.getQuestion());
        }
        return ValidateResult.success();
    }

    private DealGroupDTO queryDealGroupInfo(BeamReserveConfirmParam param, int platform) {
        if (param == null || param.getProductId() == null) {
            return null;
        }
        boolean isMt = PlatformEnum.MT.getType() == platform;
        return productAclService.getDealBaseInfo(param.getProductId(), isMt);
    }

    private String generateRecommendText(List<StandardReserveItem> lackReserveItems, ShopReserveContext shopReserveContext, List<LeadsProjectInfo> leadsProjectInfos, ProductRetrievalData topProduct, List<ProductRetrievalData> reserveProducts) {
        String lackReserveItemText = convertLackReserveItemText(lackReserveItems);
        // 没有行业则直接返回缺失要素文案
        if (shopReserveContext == null || shopReserveContext.getPoiIndustryType() == null) {
            return lackReserveItemText;
        }
        //美发行业推荐文本
        if (POIIndustryType.HAIR.equals(shopReserveContext.getPoiIndustryType())) {
            return generateHairRecommendText(lackReserveItems, leadsProjectInfos);
        }
        //生成通用推品文案
        return generateGeneralMassageRecommendText(lackReserveItemText, topProduct, reserveProducts);
    }

    private String convertLackReserveItemText(List<StandardReserveItem> reserveItems) {
        if (CollectionUtils.isEmpty(reserveItems)) {
            return null;
        }
        List<String> lackReserveItems = reserveItems.stream()
                .filter(item -> item != null && item.isMustNeed())
                .map(StandardReserveItem::getName)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(lackReserveItems)) {
            return null;
        }
        return String.format(beamReserveInfoCollectText.get("LACK_RESERVE_ITEMS_FORMAT"), String.join("、", lackReserveItems));
    }

    private String generateGeneralMassageRecommendText(String lackReserveItemText, ProductRetrievalData topProduct, List<ProductRetrievalData> reserveProducts) {
        //优先推预约预定
        topProduct = CollectionUtils.isEmpty(reserveProducts) ? topProduct : reserveProducts.get(0);
        String topProductRecommendText = getTopProductRecommendText(topProduct);
        if (StringUtils.isNotEmpty(topProductRecommendText)) {
            return String.format("%s \n %s", lackReserveItemText, topProductRecommendText);
        }
        return lackReserveItemText;
    }

    private String generateHairRecommendText(List<StandardReserveItem> lackReserveItems, List<LeadsProjectInfo> leadsProjectInfos) {
        // 过滤时长要素
        String lackReserveItemText = filterDurationLackReserveItem(lackReserveItems);
        String leadsProjectName = null;
        if (CollectionUtils.isNotEmpty(leadsProjectInfos)) {
            leadsProjectName = String.join("、", leadsProjectInfos.stream()
                    .filter(Objects::nonNull)
                    .map(LeadsProjectInfo::getProjectName)
                    .collect(Collectors.toList()));
        }
        if (StringUtils.isNotEmpty(leadsProjectName)) {
            String recommendText = String.format(beamReserveInfoCollectText.get("PROJECT_RECOMMEND_TEXT"), leadsProjectName);
            return String.format("%s \n %s", lackReserveItemText, recommendText);
        }
        return lackReserveItemText;
    }

    private String filterDurationLackReserveItem(List<StandardReserveItem> lackReserveItems) {
        if (CollectionUtils.isEmpty(lackReserveItems)) {
            return null;
        }
        List<String> filterItems = lackReserveItems.stream()
                .filter(item -> item != null && !ReserveItemType.DURATION.getKey().equals(item.getKey()))
                .filter(item -> item != null && item.isMustNeed())
                .map(StandardReserveItem::getName)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(lackReserveItems)) {
            return null;
        }
        return String.format(beamReserveInfoCollectText.get("LACK_RESERVE_ITEMS_FORMAT"), String.join("、", filterItems));
    }

    private String getTopProductRecommendText(ProductRetrievalData topProduct) {
        if (topProduct == null || StringUtils.isBlank(topProduct.getTitle())) {
            return null;
        }
        String projectName = topProduct.getTitle();
        Integer duration = aiReserveBookProcessService.extractDuration(topProduct.getDealGroupDTO());
        if (duration != null && duration > 0) {
            projectName = String.format("%s分钟%s", duration, projectName);
        }
        return String.format(beamReserveInfoCollectText.get("TOP_SALE_RECOMMEND_TEXT"), projectName);
    }

    private ValidateResult validateReserveItems(ReserveInfo collectedReserveInfo, ShopReserveContext shopReserveContext,
                                                List<LeadsProjectInfo> leadsProjectInfos, boolean isAfterOrder,
                                                DealGroupDTO dealGroupDTO) {
        if (collectedReserveInfo == null || MapUtils.isEmpty(collectedReserveInfo.getCollectedReserveItemMap())) {
            return ValidateResult.success();
        }
        Map<String, ReserveItemData> reserveItemDataMap = collectedReserveInfo.getCollectedReserveItemMap();
        if (MapUtils.isEmpty(reserveItemDataMap)) {
            return ValidateResult.success();
        }
        // 校验项目：1、先买后约，把商品信息挂上；2美发，一定要给到projectCode
        ValidateResult validateProject = validateProject(collectedReserveInfo, shopReserveContext, leadsProjectInfos, isAfterOrder, dealGroupDTO);
        if (!validateProject.isPass()) {
            return validateProject;
        }

        // 校验开始时间 开始时间是否合法，以及在线系统检验是否是合法时间间隔
        ValidateResult validStartTime = ReserveInfoProcessUtils.validateStartTime(reserveItemDataMap, shopReserveContext);
        if (!validStartTime.isPass()) {
            return validStartTime;
        }
        // 校验数值格式
        ValidateResult validateNumFormat = validateIntNum(reserveItemDataMap);
        if (!validateNumFormat.isPass()) {
            return validateNumFormat;
        }
        return ValidateResult.success();
    }

    private ValidateResult validateProject(ReserveInfo collectedReserveInfo, ShopReserveContext shopReserveContext,
                                           List<LeadsProjectInfo> leadsProjectInfos, boolean isAfterOrder, DealGroupDTO dealGroupDTO) {
        ReserveItemData projectItemData = collectedReserveInfo.getCollectedReserveItemMap().get(ReserveItemType.PROJECT.getKey());
        ReserveItemData projectCodeItemData = collectedReserveInfo.getCollectedReserveItemMap().get(ReserveItemType.PROJECT_CODE.getKey());
        // 先买后约场景，强制手动设置商品
        if (isAfterOrder) {
            AssistantSceneContext context = RequestContext.getAttribute(RequestContextConstants.ASSISTANT_CONTEXT);
            int platform = getPlatform(context);
            fillProductInfo(collectedReserveInfo, dealGroupDTO, platform);
            return ValidateResult.success();
        }
        // 美发
        if (!isAfterOrder && POIIndustryType.HAIR.equals(shopReserveContext.getPoiIndustryType())) {
            // 项目为空则无需校验
            if (projectItemData == null) {
                return ValidateResult.success();
            }
            // 先约后买场景，如果门店无可约项目，则设置为其他
            if (CollectionUtils.isEmpty(leadsProjectInfos)) {
                collectedReserveInfo.getCollectedReserveItemMap().put(ReserveItemType.PROJECT_CODE.getKey(),
                        new ReserveItemData(ReserveItemType.PROJECT_CODE, String.valueOf(CustomProjectCodeEnum.OTHER.getValue())));
                return ValidateResult.success();
            }
            // 如果项目code不在可约项目中，则将项目code设置为其他
            Integer projectCode = getProjectCode(projectCodeItemData);
            boolean projectCodeExist = leadsProjectInfos.stream().filter(Objects::nonNull).anyMatch(leadsProjectInfo -> leadsProjectInfo.getProjectCode().equals(projectCode));
            if (!projectCodeExist) {
                collectedReserveInfo.getCollectedReserveItemMap().put(ReserveItemType.PROJECT_CODE.getKey(),
                        new ReserveItemData(ReserveItemType.PROJECT_CODE, String.valueOf(CustomProjectCodeEnum.OTHER.getValue())));
                return ValidateResult.success();
            }
        }

        //酒吧且在线预订需要匹配到商品（匹配到SKUID给下游）
        if (POIIndustryType.BAR.equals(shopReserveContext.getPoiIndustryType()) && ShopReserveWayType.ONLINE.equals(shopReserveContext.getShopReserveWayType())) {
            ValidateResult validateBarProductRes = validateBarProduct(collectedReserveInfo, shopReserveContext);
            if (!validateBarProductRes.isPass()) {
                return validateBarProductRes;
            }
        }
        return ValidateResult.success();
    }

    private static ValidateResult validateBarProduct(ReserveInfo collectedReserveInfo, ShopReserveContext shopReserveContext) {
        ReserveItemData projectCodeData = collectedReserveInfo.getCollectedReserveItemMap().get(ReserveItemType.PROJECT_CODE.getKey());
        if (projectCodeData == null || StringUtils.isBlank(projectCodeData.getConvertedValue())) {
            log.warn("[ReserveInfoCollectPlugin] validateBarProduct projectCode is null, reserveInfo={}, shopReserve={}", collectedReserveInfo, shopReserveContext);
            return ValidateResult.fail("抱歉，暂无可预约项目～");
        }
        return ValidateResult.success();
    }

    private Integer getProjectCode(ReserveItemData projectCodeItemData) {
        if (projectCodeItemData == null) {
            return null;
        }
        try {
            return Integer.parseInt(projectCodeItemData.getConvertedValue());
        } catch (Exception e) {
            log.error("[ReserveInfoCollectPlugin] getProjectCode error, item={}", projectCodeItemData, e);
        }
        return null;
    }

    private ValidateResult validateIntNum(Map<String, ReserveItemData> reserveItemDataMap) {
        List<ReserveItemType> reserveItemTypes = Lists.newArrayList(ReserveItemType.DURATION, ReserveItemType.PERSON_NUM);
        for (ReserveItemType reserveItemType : reserveItemTypes) {
            ValidateResult validateResult = validateIntNum(reserveItemDataMap.get(reserveItemType.getKey()), reserveItemType);
            if (!validateResult.isPass()) {
                return validateResult;
            }
        }
        return ValidateResult.success();
    }

    private ValidateResult validateIntNum(ReserveItemData itemData, ReserveItemType type) {
        if (itemData == null) {
            return ValidateResult.success();
        }
        String numStr = itemData.getConvertedValue();
        if (StringUtils.isBlank(numStr)) {
            return ValidateResult.success();
        }
        try {
            int num = Integer.parseInt(numStr);
            if (num <= 0) {
                return ValidateResult.fail(String.format("抱歉，预约%s必须大于0，请重新提供", type.getName()));
            }
        } catch (NumberFormatException e) {
            log.error("[ReserveInfoCollectPlugin] validateIntNum error itemData={}, type={}", JSON.toJSONString(itemData), type);
            return ValidateResult.fail(String.format("抱歉，预约%s必须为整数，请重新提供", type.getName()));
        }
        return ValidateResult.success();
    }

    private ValidateResult judgeReserveElementComplete(ReserveInfo collectedReserveInfo, List<StandardReserveItem> standardReserveItems,
                                                       ShopReserveContext shopReserveContext, List<LeadsProjectInfo> leadsProjectInfos,
                                                       ProductRetrievalData topProduct, List<ProductRetrievalData> reserveProducts) {
        List<StandardReserveItem> lackReserveItems = ReserveInfoProcessUtils.checkLackReserveItems(collectedReserveInfo, standardReserveItems);
        if (CollectionUtils.isEmpty(lackReserveItems)) {
            return ValidateResult.success();
        }
        boolean lackProject = lackReserveItems.stream().anyMatch(item -> ReserveItemType.PROJECT.getKey().equals(item.getKey()));
        List<String> lackReserveItemNames = lackReserveItems.stream().map(StandardReserveItem::getName).collect(Collectors.toList());
        String lackReserveItemText = String.format(beamReserveInfoCollectText.get("LACK_RESERVE_ITEMS_FORMAT"), String.join("、", lackReserveItemNames));
        // 缺少项目信息，则需要向用户推荐高销项目或门店可约项目
        if (lackProject) {
            String recommendText = generateRecommendText(lackReserveItems, shopReserveContext, leadsProjectInfos, topProduct, reserveProducts);
            return ValidateResult.fail(recommendText);
        }
        return ValidateResult.fail(lackReserveItemText);
    }

    private ValidateResult judgeStock(ReserveInfo collectedReserveInfo, UserReserveInfo userReserveInfo, List<ProductRetrievalData> shopReserveProducts, ShopReserveContext shopReserveContext, boolean isAfterOrder, DealGroupDTO dealGroupDTO) {
        ValidateResult judgeTimeStockResult = judgeTimeStock(collectedReserveInfo, userReserveInfo, shopReserveProducts, shopReserveContext, isAfterOrder, dealGroupDTO);
        if (!judgeTimeStockResult.isPass()) {
            return ValidateResult.fail(judgeTimeStockResult.getQuestion());
        }
        return ValidateResult.success();
    }

    private ValidateResult judgeTimeStock(ReserveInfo collectedReserveInfo, UserReserveInfo userReserveInfo, List<ProductRetrievalData> shopReserveProducts, ShopReserveContext shopReserveContext, boolean isAfterOrder, DealGroupDTO dealGroupDTO) {
        //0. 塞进去Duration, 不然查不了库存
        fillUserReserveInfoDuration(collectedReserveInfo, shopReserveContext, shopReserveProducts);
        // 1. 查询可用时间片
        List<TimeSliceStockInfo> timeSliceStockInfos = aiReserveBookQueryService.beamQueryAvailableTimeStock(shopReserveContext, collectedReserveInfo, isAfterOrder, dealGroupDTO);
        // 2. 返回校验文案
        // 2.1 库存存在则校验通过
        List<TimeSliceStockInfo> timeSliceStockChecks = ReserveInfoProcessUtils.checkStockExist(shopReserveContext.getPoiIndustryType(), collectedReserveInfo, timeSliceStockInfos);
        if (CollectionUtils.isNotEmpty(timeSliceStockChecks)) {
            fillTimeStockInfo(userReserveInfo, timeSliceStockChecks, shopReserveContext);
            return ValidateResult.success();
        }
        // 2.2 库存不存在，则告知用户最近可用时间
        String nearestTime;
        //酒吧找当天的库存
        if (POIIndustryType.BAR.equals(shopReserveContext.getPoiIndustryType())) {
            nearestTime = ReserveInfoProcessUtils.findNearestAvailableDay(collectedReserveInfo, timeSliceStockInfos);
        } else {
            nearestTime = ReserveInfoProcessUtils.beamFindNearestAvailableTime(collectedReserveInfo, timeSliceStockInfos, shopReserveContext);
        }

        if (StringUtils.isNotBlank(nearestTime)) {
            return ValidateResult.fail(String.format("当前时间库存不足，请重新选择时间, 最近可选时间为%s", nearestTime));
        }
        return ValidateResult.fail("您预约的时间已无可用库存，请重新选择其他时间");
    }

    //不同行业，查到库存后补一些信息
    private void fillTimeStockInfo(UserReserveInfo userReserveInfo, List<TimeSliceStockInfo> timeSliceStockInfos, ShopReserveContext shopReserveContext) {
        if (POIIndustryType.BAR.equals(shopReserveContext.getPoiIndustryType())) {
            //酒吧补个skuid、默认时长
            transAndSetPlatformSkuId2BizSkuId(userReserveInfo, timeSliceStockInfos, shopReserveContext);
        }
    }

    //todo 需要查商品匹配时长
    private void fillUserReserveInfoDuration(ReserveInfo collectedReserveInfo, ShopReserveContext shopReserveContext, List<ProductRetrievalData> shopReserveProducts) {
        try {
            int duration = getUserReserveInfoDuration(collectedReserveInfo, shopReserveContext, shopReserveProducts);
            if (duration <= 0) {
                return;
            }
            Map<String, ReserveItemData> data = collectedReserveInfo.getCollectedReserveItemMap();
            if (MapUtils.isEmpty(data)) {
                data = Maps.newHashMap();
                collectedReserveInfo.setCollectedReserveItemMap(data);
            }
            data.put(ReserveItemType.DURATION.getKey(), new ReserveItemData(ReserveItemType.DURATION, String.valueOf(duration)));
        } catch (Exception e) {
            log.error("fillUserReserveInfoDuration error userReserveInfo:{}, shopReserveContext:{}", collectedReserveInfo, shopReserveContext, e);
        }
    }

    private int getUserReserveInfoDuration(ReserveInfo collectedReserveInfo, ShopReserveContext shopReserveContext, List<ProductRetrievalData> shopReserveProducts) {
        //酒吧60分钟写死
        if (POIIndustryType.BAR.equals(shopReserveContext.getPoiIndustryType())) {
            return ShopStartTimeIntervalType.SIXTY.getStartTimeInterval();
        }
        //宠物根据模型匹配的项目代码，去上下文查该项目的时长
        if (POIIndustryType.PET_SHOP.equals(shopReserveContext.getPoiIndustryType())) {
            return getDurationFromPetReserveProduct(collectedReserveInfo, shopReserveProducts);
        }

        return -1;
    }

    private static int getDurationFromPetReserveProduct(ReserveInfo collectedReserveInfo, List<ProductRetrievalData> shopReserveProducts) {
        try {
            ReserveItemData projectCodeItemData = collectedReserveInfo.getCollectedReserveItemMap().get(ReserveItemType.PROJECT_CODE.getKey());
            if (projectCodeItemData == null || projectCodeItemData.getConvertedValue() == null) {
                return PET_DEFAULT_SERVICE_TIME;
            }

            String productId = projectCodeItemData.getConvertedValue();
            for (ProductRetrievalData shopReserveProduct : shopReserveProducts) {
                if (shopReserveProduct.getMtProductId() != Long.parseLong(productId)) {
                    continue;
                }

                int duration = shopReserveProduct.getDuration();
                return duration <= 0 ? PET_DEFAULT_SERVICE_TIME : duration;
            }

        } catch (Exception e) {
            log.error("getDurationFromPetReserveProduct error, shopReserveProducts:{}", shopReserveProducts, e);
        }
        return PET_DEFAULT_SERVICE_TIME;
    }

    private void transAndSetPlatformSkuId2BizSkuId(UserReserveInfo userReserveInfo, List<TimeSliceStockInfo> timeSliceStockInfos, ShopReserveContext shopReserveContext) {
        try {
            //酒吧补个skuid
            if (POIIndustryType.BAR.equals(shopReserveContext.getPoiIndustryType())) {
                TimeSliceStockInfo timeSliceStockInfo = timeSliceStockInfos.get(0);
                if (timeSliceStockInfo == null || timeSliceStockInfo.getSkuId() == null) {
                    return;
                }
                Map<Long, Long> res = idService.convertSkuIdsToBizSkuIds(DP_FUN_SKU_ID, buildIdServiceRequest(timeSliceStockInfo));
                if (MapUtils.isEmpty(res) || !res.containsKey(Long.valueOf(timeSliceStockInfo.getSkuId()))) {
                    return;
                }
                userReserveInfo.setSkuId(res.get(Long.valueOf(timeSliceStockInfo.getSkuId())));
            }
        } catch (Exception e) {
            log.error("fillTimeStockInfo error", e);
        }
    }

    //酒吧skuid转换 平台转业务
    private List<Long> buildIdServiceRequest(TimeSliceStockInfo timeSliceStockInfo) {
        String skuId = timeSliceStockInfo.getSkuId();
        if (StringUtils.isBlank(skuId)) {
            return new ArrayList<>();
        }
        Long skuidLong = Long.valueOf(skuId);
        return Lists.newArrayList(skuidLong);
    }

    private void fillOrderAndProduct(UserReserveInfo userReserveInfo, BeamReserveConfirmParam param) {
        if (param.getProductId() != null && param.getProductId() > 0 && param.getOrderId() != null && param.getOrderId() > 0) {
            userReserveInfo.setDealId(param.getProductId());
            userReserveInfo.setOrderId(param.getOrderId());
        }
    }

    private void fillCompleteCollectedReserveInfo(UserReserveInfo userReserveInfo,
                                                  UserModel userModel, ShopReserveContext shopReserveContext) {
        AssistantSceneContext assistantSceneContext = RequestContext.getAttribute(RequestContextConstants.ASSISTANT_CONTEXT);
        int platform = getPlatform(assistantSceneContext);
        userReserveInfo.setCompleted(true);
        // 填充默认预约信息
        ReserveInfo userRemoteReserveInfo = userReserveInfo.getCollectedReserveInfo();
        // 如果手机号不存在则替换默认手机号
        if (StringUtils.isBlank(userRemoteReserveInfo.getPhone())) {
            userRemoteReserveInfo.setPhone(userModel.getMobile());
        }

        // 人数填充
        fillPersonNumUserReserveInfo(userReserveInfo);
        // 时长填充
        fillDurationUserReserveInfo(userReserveInfo);
        // 其他填充
        fillOtherUserReserveInfo(userReserveInfo, shopReserveContext, platform);
    }

    private void fillDurationUserReserveInfo(UserReserveInfo userReserveInfo) {
        ReserveInfo userRemoteReserveInfo = userReserveInfo.getCollectedReserveInfo();
        Integer duration = ReserveInfoProcessUtils.getIntItem(userRemoteReserveInfo, ReserveItemType.DURATION.getKey());
        if (duration == null || duration <= 0) {
            if (userRemoteReserveInfo.getCollectedReserveItemMap() == null) {
                userRemoteReserveInfo.setCollectedReserveItemMap(Maps.newHashMap());
            }
            userRemoteReserveInfo.getCollectedReserveItemMap().put(ReserveItemType.DURATION.getKey(),
                    new ReserveItemData(ReserveItemType.DURATION, AI_PHONE_DEFAULT_SERVICE_TIME));
        }
    }

    private void fillPersonNumUserReserveInfo(UserReserveInfo userReserveInfo) {
        ReserveInfo userRemoteReserveInfo = userReserveInfo.getCollectedReserveInfo();
        Integer personNum = ReserveInfoProcessUtils.getIntItem(userRemoteReserveInfo, ReserveItemType.PERSON_NUM.getKey());
        if (personNum == null || personNum <= 0) {
            if (userRemoteReserveInfo.getCollectedReserveItemMap() == null) {
                userRemoteReserveInfo.setCollectedReserveItemMap(Maps.newHashMap());
            }

            userRemoteReserveInfo.getCollectedReserveItemMap().put(ReserveItemType.PERSON_NUM.getKey(),
                    new ReserveItemData(ReserveItemType.PERSON_NUM, "1"));
        }
    }

    private void refreshCollectedReserveInfo(UserReserveInfo userReserveInfo,
                                             ShopReserveContext shopReserveContext) {
        AssistantSceneContext assistantSceneContext = RequestContext.getAttribute(RequestContextConstants.ASSISTANT_CONTEXT);
        String userId = assistantSceneContext.getUserId();
        int platform = getPlatform(assistantSceneContext);
        try {
            Long shopId = platform == PlatformEnum.DP.getType() ? shopReserveContext.getDpShopId() : shopReserveContext.getMtShopId();
            aiBookCacheProcessService.setBeamAgentReserveAndBookInfo(userId, shopId, platform, userReserveInfo);
        } catch (Exception e) {
            log.error("[ReserveInfoCollectPlugin] updateCollectedReserveInfo error, userId={}, dpShopId={}, userReserveInfo", userId, JSON.toJSONString(shopReserveContext), JSON.toJSONString(userReserveInfo), e);
        }
    }

    private void fillOtherUserReserveInfo(UserReserveInfo userReserveInfo, ShopReserveContext shopReserveContext, int platform) {
        if (shopReserveContext == null) {
            return;
        }
        AssistantSceneContext assistantSceneContext = RequestContext.getAttribute(RequestContextConstants.ASSISTANT_CONTEXT);
        userReserveInfo.setImUserId(assistantSceneContext.getUserId());
        userReserveInfo.setShopId(platform == PlatformEnum.DP.getType() ? shopReserveContext.getDpShopId() : shopReserveContext.getMtShopId());
        userReserveInfo.setShopName(shopReserveContext.getShopName());
        if (shopReserveContext.getShopReserveWayType() != null) {
            userReserveInfo.setShopReserveWayType(shopReserveContext.getShopReserveWayType().getType());
        }
        if (shopReserveContext.getPoiIndustryType() != null) {
            POIIndustryType poiIndustryType = shopReserveContext.getPoiIndustryType();
            if (POIIndustryType.HAIR.equals(poiIndustryType)) {
                userReserveInfo.setTaskReserveType(TaskReserveType.APPOINTMENT.getType());
            } else {
                userReserveInfo.setTaskReserveType(TaskReserveType.BOOKING.getType());
            }
        }
    }

    private void fillProductInfo(ReserveInfo collectedReserveInfo, DealGroupDTO dealGroupDTO, int platform) {
        if (dealGroupDTO == null || collectedReserveInfo == null || MapUtils.isEmpty(collectedReserveInfo.getCollectedReserveItemMap())) {
            return;
        }
        Map<String, ReserveItemData> collectedReserveItems = collectedReserveInfo.getCollectedReserveItemMap();
        ReserveItemData projectItemData = collectedReserveInfo.getCollectedReserveItemMap().get(ReserveItemType.PROJECT.getKey());

        if (dealGroupDTO.getBasic() != null && (projectItemData == null || StringUtils.isBlank(projectItemData.getConvertedValue()))) {
            DealGroupBasicDTO basicDTO = dealGroupDTO.getBasic();
            collectedReserveItems.put(ReserveItemType.PROJECT.getKey(), new ReserveItemData(ReserveItemType.PROJECT, basicDTO.getTitle()));
        }
        Long dealId = platform == PlatformEnum.DP.getType() ? dealGroupDTO.getDpDealGroupId() : dealGroupDTO.getMtDealGroupId();
        if (dealId != null) {
            collectedReserveItems.put(ReserveItemType.PROJECT_CODE.getKey(), new ReserveItemData(ReserveItemType.PROJECT_CODE, String.valueOf(dealId)));
        }
    }

    private int getPlatform(AssistantSceneContext context) {
        return context.getUserId().startsWith(ImUserType.MT.getPrefix()) ? PlatformEnum.MT.getType() : PlatformEnum.DP.getType();
    }

    private String generateMessage(String answer, boolean returnDirect) {
        ReserveReplyAnswerData result = new ReserveReplyAnswerData();
        if (returnDirect) {
            result.setReserveReplyType(ReserveReplyType.DIRECT.getType());
        } else {
            result.setReserveReplyType(ReserveReplyType.GENERATE.getType());
        }
        result.setAnswer(answer);
        return JSON.toJSONString(result);
    }

    private String generateCard(UserReserveInfo userReserveInfo, ShopReserveContext shopReserveContext, DealGroupDTO dealGroupDTO, boolean isAfterOrder) {
        AssistantSceneContext context = RequestContext.getAttribute(RequestContextConstants.ASSISTANT_CONTEXT);
        return BeamReserveBookCardUtils.generateConfirmCard(userReserveInfo, shopReserveContext, dealGroupDTO, context, isAfterOrder);
    }

    // 用于存储CompletableFuture的Context
    @Data
    private static class FutureContext {
        private CompletableFuture<UserModel> userModelF;
        private CompletableFuture<List<StandardReserveItem>> standardReserveItemsF;
        private CompletableFuture<List<LeadsProjectInfo>> leadsProjectInfoF;
        private CompletableFuture<ProductRetrievalData> topProductF;
        private CompletableFuture<List<ProductRetrievalData>> shopReserveProductsF;
        private CompletableFuture<ShopReserveWayType> shopReserveWayTypeF;
        private CompletableFuture<DealGroupDTO> dealGroupDTOF;
    }

    // 用于存储join后结果的Context
    @Data
    private static class ResultContext {
        private UserReserveInfo userReserveInfo;
        private UserModel userModel;
        private List<StandardReserveItem> standardReserveItems;
        private List<LeadsProjectInfo> leadsProjectInfos;
        private List<ProductRetrievalData> shopReserveProducts;
        private ProductRetrievalData topProduct;
        private ShopReserveContext shopReserveContext;
        private DealGroupDTO dealGroupDTO;
        private POIIndustryType poiIndustryType;
    }
}
