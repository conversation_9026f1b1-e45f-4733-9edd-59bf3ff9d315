package com.sankuai.dzim.pilot.domain.memory.data;

import com.sankuai.dzim.pilot.chain.data.AIServiceContext;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class QueryMemoryContext {

    /**
     * 最后一条消息Id
     */
    private long lastMessageId;

    /**
     * 起始消息Id
     */
    private long startMessageId;

    /**
     * im商家Id
     */
    private String imMerchantId;

    /**
     * im顾客Id
     */
    private String imClientId;

    /**
     * 会话Id
     */
    private long chatGroupId;

    /**
     * 消息数限制
     */
    private int limit;

    /**
     * 历史记录查询时间 30分钟
     */
    private int interval = 30;

    /**
     * 大模型服务上下文
     */
    private AIServiceContext aiServiceContext;
}
