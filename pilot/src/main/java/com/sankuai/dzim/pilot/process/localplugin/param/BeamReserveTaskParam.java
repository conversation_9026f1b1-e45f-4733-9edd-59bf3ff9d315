package com.sankuai.dzim.pilot.process.localplugin.param;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import lombok.Data;

/**
 * @author: zhouyibing
 * @date: 2025/5/14
 */
@Data
public class BeamReserveTaskParam extends Param{

    @JsonProperty(required = true)
    @JsonPropertyDescription("任务描述，描述用户诉求对应的要执行的预约任务内容。如：发起预约/确认预约/取消预约")
    private String taskDescription;
}
