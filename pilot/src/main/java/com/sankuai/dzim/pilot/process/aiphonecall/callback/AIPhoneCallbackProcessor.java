package com.sankuai.dzim.pilot.process.aiphonecall.callback;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dianping.cat.Cat;
import com.meituan.mtrace.Tracer;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.dzim.pilot.dal.entity.aiphonecall.AIPhoneCallDetailEntity;
import com.sankuai.dzim.pilot.dal.entity.aiphonecall.AIPhoneCallTaskEntity;
import com.sankuai.dzim.pilot.dal.pilotdao.aiphonecall.AIPhoneCallDetailDAO;
import com.sankuai.dzim.pilot.dal.pilotdao.aiphonecall.AIPhoneCallTaskDAO;
import com.sankuai.dzim.pilot.domain.AIPhoneCallDomainService;
import com.sankuai.dzim.pilot.enums.*;
import com.sankuai.dzim.pilot.process.aiphonecall.data.AIPhoneCallBackData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class AIPhoneCallbackProcessor {

    @Autowired
    private List<AIPhoneCallback> callbacks;

    @Autowired
    private AIPhoneCallTaskDAO callTaskDAO;

    @Autowired
    private AIPhoneCallDetailDAO callDetailDAO;

    @Autowired
    private AIPhoneCallDomainService callDomainService;

    public void onComplete(long taskId) {
        try {
            AIPhoneCallTaskEntity callTaskEntity = callTaskDAO.getByTaskId(taskId);
            AIPhoneCallBackData callBackData = buildCallbackData(callTaskEntity);
            completeCallback(callTaskEntity, callBackData);
        } catch (Exception e) {
            log.error("onComplete error, taskId = {}", taskId, e);

        }
    }

    private void completeCallback(AIPhoneCallTaskEntity callTaskEntity, AIPhoneCallBackData callBackData) {
        AIPhoneCallback callback = getMatchedCallback(callBackData);
        if (callback == null) {
            return;
        }

        int callMode = AIPhoneCallSceneTypeConfig.getBySceneType(callTaskEntity.getSceneType()).getMode();
        if (callMode == AIPhoneCallModeEnum.SINGLE.getType()) {
            // 若不存在外呼成功的明细，则调用onFail回调
            if (!callDomainService.hasSuccessCallDetail(callTaskEntity.getId())) {
                callback.onFail(callBackData);
                return;
            }

            fillDialogExtractInfo(callBackData, callTaskEntity, callMode);
            callback.onSuccess(callBackData);
            return;
        }
    }

    private void fillDialogExtractInfo(AIPhoneCallBackData callBackData, AIPhoneCallTaskEntity callTaskEntity, int callMode) {
        try {
            if (callMode == AIPhoneCallModeEnum.SINGLE.getType()) {
                // 从外呼成功的明细中获取对话信息
                List<AIPhoneCallDetailEntity> callDetailEntities = callDetailDAO.getByTaskId(callTaskEntity.getId());
                AIPhoneCallDetailEntity callDetailEntity = CollectionUtils.emptyIfNull(callDetailEntities).stream()
                        .filter(e -> e.getStatus() == AIPhoneCallDetailStatusEnum.SUCCESS.getStatus())
                        .findAny().orElse(null);
                if (callDetailEntity == null) {
                    return;
                }

                Object dialogExtractContentObject = callDetailEntity.getExtraDataByKey(AIPhoneCallExtraKeyEnum.CALL_DIALOG_EXTRACT_INFO);
                if (dialogExtractContentObject == null) {
                    return;
                }
                Map<String, Object> callDialogExtractInfo = JSON.parseObject(String.valueOf(dialogExtractContentObject), new TypeReference<Map<String, Object>>() {});
                callBackData.setCallDialogExtractInfo(callDialogExtractInfo);
            }
        } catch (Exception e) {
            Cat.logError(e);
            log.error("fillCallDialogExtractInfo error, taskId = {}", callTaskEntity.getId());
        }
    }

    private AIPhoneCallBackData buildCallbackData(AIPhoneCallTaskEntity callTaskEntity) {
        AIPhoneCallBackData callBackData = new AIPhoneCallBackData();
        callBackData.setAiPhoneCallSceneTypeEnum(AIPhoneCallSceneTypeEnum.getByType(callTaskEntity.getSceneType()));
        callBackData.setBizId(callTaskEntity.getBizId());
        callBackData.setBizData(callTaskEntity.getBizData());
        callBackData.setAiPhoneCallSourceEnum(AIPhoneCallSourceEnum.getBySource(callTaskEntity.getSource()));
        return callBackData;
    }

    public void onFail(long taskId) {
        try {
            AIPhoneCallTaskEntity callTaskEntity = callTaskDAO.getByTaskId(taskId);
            AIPhoneCallBackData callBackData = buildCallbackData(callTaskEntity);
            AIPhoneCallback callback = getMatchedCallback(callBackData);
            if (callback == null) {
                return;
            }
            callback.onFail(callBackData);
        } catch (Exception e){
            log.error("onFail error, taskId = {}", taskId, e);

        }
    }

    public void onDelay(long taskId) {
        try {
            AIPhoneCallTaskEntity callTaskEntity = callTaskDAO.getByTaskId(taskId);
            AIPhoneCallBackData callBackData = buildCallbackData(callTaskEntity);
            AIPhoneCallback callback = getMatchedCallback(callBackData);
            if (callback == null) {
                return;
            }
            callback.onDelay(callBackData);
        } catch (Exception e) {
            log.error("onDelay error, taskId = {}", taskId, e);

        }
    }

    public void onDelayWakeUp(long taskId) {
        try {
            AIPhoneCallTaskEntity callTaskEntity = callTaskDAO.getByTaskId(taskId);
            AIPhoneCallBackData callBackData = buildCallbackData(callTaskEntity);
            AIPhoneCallback callback = getMatchedCallback(callBackData);
            if (callback == null) {
                return;
            }
            callback.onDelayWakeUp(callBackData);
        } catch (Exception e) {
            log.error("onDelayWakeUp error, taskId = {}", taskId, e);
        }
    }

    private AIPhoneCallback getMatchedCallback(AIPhoneCallBackData callBackData) {
        AIPhoneCallback callback = CollectionUtils.emptyIfNull(callbacks).stream()
                .filter(cb -> cb.accept(callBackData))
                .findFirst()
                .orElse(null);

        return callback;
    }
}
