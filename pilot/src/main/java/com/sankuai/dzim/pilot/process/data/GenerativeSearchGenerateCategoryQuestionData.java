package com.sankuai.dzim.pilot.process.data;

import com.sankuai.it.xcontract.easypoi.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/08/13 20:40
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GenerativeSearchGenerateCategoryQuestionData {
    @Excel(name = "query")
    private String query;
    @Excel(name = "category")
    private String category;
    @Excel(name = "industry")
    private String industry;
    @Excel(name = "bizType")
    private int bizType;
    @Excel(name = "promptType")
    private int promptType;
    @Excel(name = "questionType")
    private int questionType;
    @Excel(name = "question")
    private String question;
}
