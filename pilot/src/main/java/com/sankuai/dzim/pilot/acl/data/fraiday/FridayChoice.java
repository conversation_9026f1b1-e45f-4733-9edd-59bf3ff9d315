package com.sankuai.dzim.pilot.acl.data.fraiday;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class FridayChoice {

    /**
     * 答案生成结束的原因
     */
    private String finish_reason;

    /**
     * 索引
     */
    private int index;

    /**
     * 生成的答案
     */
    private FridayMessage message;

    /**
     * 推理信息
     */
    private FridayMessage reasoning_content;

    /**
     * 流式输出增量数据
     */
    private BulkingData delta;
}
