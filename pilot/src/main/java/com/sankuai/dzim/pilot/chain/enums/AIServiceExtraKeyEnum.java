package com.sankuai.dzim.pilot.chain.enums;

import lombok.Getter;

@Getter
public enum AIServiceExtraKeyEnum {

    IM_SHOP_ID("imShopId", "im门店id，也是im账号id"),
    DP_SHOP_ID("dpShopId", "点评门店id"),
    BANQUET_HALL_ID("banquetHallId", "宴会厅id"),
    MESSAGE_INPUT_SOURCE("messageInputSource", "消息输入来源"),
    BIZ_PARAMS("bizParams", "业务参数"),
    USER_CONTEXT("userContext", "用户上下文"),
    ENV_CONTEXT("envContext", "环境上下文"),
    IMAGE_URLS("imageUrls", "图片链接"),
    EVAL_CASE("evaluationData", "评测数据"),
    CHAT_GROUP_EXP("chatGroupExp", "会话group接口透传字段"),
    BEAM_REQUEST_CONTEXT("beamRequestContext", "Beam请求上下文"),
    BUFFER_EXTRA("bufferExtra", "Buffer扩展数据")
    ;

    private String key;

    private String desc;

    AIServiceExtraKeyEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }
}
