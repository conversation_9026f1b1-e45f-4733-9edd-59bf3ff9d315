package com.sankuai.dzim.pilot.buffer.stream.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/7/29 19:02
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StreamEventDataVO {

    private String event;
    private String content;
    private List<StreamEventCardDataVO> cardsData;

    // BEAM消息的资源内容
    private List<BeamChoiceVO.DeltaResource> resources;

}
