package com.sankuai.dzim.pilot.acl.data;

import lombok.Data;

@Data
public class ChatCompletionConfig {

    /**
     * 应用id
     */
    private String appId;

    /**
     * 模型名称
     */
    private String model;

    /**
     * 采样温度，越高输出的值越随机
     */
    private Double temperature;

    /**
     * 只考虑具有top_p概率质量的结果
     */
    private Double topP;

    /**
     * 输出格式
     */
    private boolean jsonFormat;

    /**
     * 最大输出token
     */
    private Integer maxTokens;

    /**
     * 系统提示词
     */
    private String systemPrompt;

}
