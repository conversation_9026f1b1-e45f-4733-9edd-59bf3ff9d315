package com.sankuai.dzim.pilot.domain.plugin.data;

import com.fasterxml.jackson.databind.JsonNode;
import com.sankuai.dzim.pilot.domain.plugin.data.PluginTypeEnum;
import com.sankuai.dzim.pilot.domain.plugin.executor.PluginExecutor;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class PluginSpecification {

    /**
     * 插件类型
     * @see PluginTypeEnum
     */
    private int type;

    /**
     * 插件名称
     */
    private String name;

    /**
     * 插件描述
     */
    private String description;

    /**
     * 插件参数
     */
    private JsonNode params;

    /**
     * 是否直接返回
     */
    private boolean returnDirect;

    /**
     * 对于本地插件,有LionKey,直接使用lion中的配置
     */
    private String lionKey;

    /**
     * 执行器
     */
    private PluginExecutor executor;
}
