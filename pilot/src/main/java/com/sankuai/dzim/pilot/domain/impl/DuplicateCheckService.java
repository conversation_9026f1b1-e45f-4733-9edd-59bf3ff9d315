package com.sankuai.dzim.pilot.domain.impl;

import co.elastic.clients.elasticsearch._types.FieldValue;
import co.elastic.clients.elasticsearch._types.query_dsl.TermQuery;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.dzim.pilot.domain.data.Embeddable;
import com.sankuai.dzim.pilot.gateway.mq.data.ReviewData;
import com.sankuai.meituan.poros.client.PorosApiClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class DuplicateCheckService {

    @Resource
    private PorosApiClient esClient;

    /**
     * ES通用重复检查
     */
    public <T extends Embeddable> boolean checkDuplicate(T data, String uniqueField, Object uniqueValue) {
        SearchRequest searchRequest = SearchRequest.of(s -> s.index(data.indexName())
                .query(q -> q.term(TermQuery.of(t -> t.field(uniqueField).value(FieldValue.of(uniqueValue)))))
        );

        try {
            SearchResponse searchResponse = esClient.search(searchRequest, data.getClass());
            return searchResponse != null &&
                    searchResponse.hits() != null &&
                    CollectionUtils.isNotEmpty(searchResponse.hits().hits());
        } catch (Exception e) {
            log.error("duplicate check error, data = {}", JsonCodec.encodeWithUTF8(data), e);
            return false;
        }
    }

    /**
     * Review数据专用重复检查
     */
    public boolean checkReviewDataDuplicate(ReviewData data) {
        return checkDuplicate(data, "reviewId", data.getReviewId());
    }
}
