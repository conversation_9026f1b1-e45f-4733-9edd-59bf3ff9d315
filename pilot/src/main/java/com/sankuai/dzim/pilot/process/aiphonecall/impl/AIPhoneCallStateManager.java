package com.sankuai.dzim.pilot.process.aiphonecall.impl;

import com.dianping.cat.Cat;
import com.sankuai.dzim.pilot.dal.entity.aiphonecall.AIPhoneCallDetailEntity;
import com.sankuai.dzim.pilot.dal.entity.aiphonecall.AIPhoneCallStateTransitLogEntity;
import com.sankuai.dzim.pilot.dal.entity.aiphonecall.AIPhoneCallTaskEntity;
import com.sankuai.dzim.pilot.dal.pilotdao.aiphonecall.AIPhoneCallDetailDAO;
import com.sankuai.dzim.pilot.dal.pilotdao.aiphonecall.AIPhoneCallStateTransitLogDAO;
import com.sankuai.dzim.pilot.dal.pilotdao.aiphonecall.AIPhoneCallTaskDAO;
import com.sankuai.dzim.pilot.domain.AIPhoneCallDomainService;
import com.sankuai.dzim.pilot.enums.AIPhoneCallDetailStatusEnum;
import com.sankuai.dzim.pilot.enums.AIPhoneCallTaskStatusEnum;
import com.sankuai.dzim.pilot.process.aiphonecall.AIPhoneCallProcessService;
import com.sankuai.dzim.pilot.process.aiphonecall.callback.AIPhoneCallbackProcessor;
import com.sankuai.dzim.pilot.process.aiphonecall.calldialogpost.CallDialogPostProcessor;
import com.sankuai.dzim.pilot.process.aiphonecall.exception.StateTransitionException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import javax.annotation.Resource;
import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

@Component
@Slf4j
public class AIPhoneCallStateManager {

    @Resource
    private AIPhoneCallTaskDAO aiPhoneCallTaskDAO;

    @Resource
    private AIPhoneCallDetailDAO aiPhoneCallDetailDAO;

    @Autowired
    private AIPhoneCallDomainService callDomainService;

    @Autowired
    private AIPhoneCallProcessService callProcessService;

    @Autowired
    @Qualifier("mdpTM2")
    private PlatformTransactionManager transactionManager;

    @Autowired
    private AIPhoneCallStateTransitLogDAO callStateTransitLogDAO;

    @Autowired
    private AIPhoneCallbackProcessor callbackProcessor;

    private static final Map<AIPhoneCallDetailStatusEnum, Set<AIPhoneCallDetailStatusEnum>> DETAIL_VALID_TRANSITIONS = new HashMap<>();

    static {
        // PENDING状态可转换的状态
        DETAIL_VALID_TRANSITIONS.put(AIPhoneCallDetailStatusEnum.PENDING,
                EnumSet.of(AIPhoneCallDetailStatusEnum.PROCESSING,
                        AIPhoneCallDetailStatusEnum.TERMINATED,
                        AIPhoneCallDetailStatusEnum.BLOCK,
                        AIPhoneCallDetailStatusEnum.DELAY,
                        AIPhoneCallDetailStatusEnum.FAILED));

        // PROCESSING状态可转换的状态
        DETAIL_VALID_TRANSITIONS.put(AIPhoneCallDetailStatusEnum.PROCESSING,
                EnumSet.of(AIPhoneCallDetailStatusEnum.RETRY,
                        AIPhoneCallDetailStatusEnum.FAILED,
                        AIPhoneCallDetailStatusEnum.SUCCESS,
                        AIPhoneCallDetailStatusEnum.TERMINATED));

        // DELAY状态可转换的状态
        DETAIL_VALID_TRANSITIONS.put(AIPhoneCallDetailStatusEnum.DELAY,
                EnumSet.of(AIPhoneCallDetailStatusEnum.FAILED,
                        AIPhoneCallDetailStatusEnum.TERMINATED,
                        AIPhoneCallDetailStatusEnum.PENDING));

        // RETRY状态可转换的状态
        DETAIL_VALID_TRANSITIONS.put(AIPhoneCallDetailStatusEnum.RETRY,
                EnumSet.of(AIPhoneCallDetailStatusEnum.FAILED,
                        AIPhoneCallDetailStatusEnum.RETRY_SUCCESS,
                        AIPhoneCallDetailStatusEnum.TERMINATED));

        // RECALL_DELAY状态可转换的状态
        DETAIL_VALID_TRANSITIONS.put(AIPhoneCallDetailStatusEnum.RECALL_DELAY,
                EnumSet.of(AIPhoneCallDetailStatusEnum.FAILED,
                        AIPhoneCallDetailStatusEnum.PENDING,
                        AIPhoneCallDetailStatusEnum.TERMINATED));
    }

    private static final Map<AIPhoneCallTaskStatusEnum, Set<AIPhoneCallTaskStatusEnum>> VALID_TRANSITIONS = new HashMap<>();

    static {
        // CREATED状态可以转换到的状态
        VALID_TRANSITIONS.put(AIPhoneCallTaskStatusEnum.CREATED,
                EnumSet.of(AIPhoneCallTaskStatusEnum.PROCESSING, AIPhoneCallTaskStatusEnum.TERMINATED,
                        AIPhoneCallTaskStatusEnum.FAILED));

        // PAUSED状态可以转换到的状态
        VALID_TRANSITIONS.put(AIPhoneCallTaskStatusEnum.PAUSED,
                EnumSet.of(AIPhoneCallTaskStatusEnum.PROCESSING, AIPhoneCallTaskStatusEnum.TERMINATED,
                        AIPhoneCallTaskStatusEnum.FAILED));

        // PROCESSING状态可以转换到的状态
        VALID_TRANSITIONS.put(AIPhoneCallTaskStatusEnum.PROCESSING,
                EnumSet.of(AIPhoneCallTaskStatusEnum.PROCESSING, AIPhoneCallTaskStatusEnum.TERMINATED,
                        AIPhoneCallTaskStatusEnum.COMPLETED, AIPhoneCallTaskStatusEnum.PAUSED,
                        AIPhoneCallTaskStatusEnum.FAILED));
    }


    /**
     * 任务状态转换
     */
    public void transitAICallTaskState(long taskId, AIPhoneCallTaskStatusEnum newState, String reason) {
        if (newState == null || taskId <= 0) {
            return;
        }

        try {
            // 1. 查询任务当前状态
            AIPhoneCallTaskEntity callTaskEntity = aiPhoneCallTaskDAO.getByTaskId(taskId);
            AIPhoneCallTaskStatusEnum oldState = AIPhoneCallTaskStatusEnum.getByStatus(callTaskEntity.getStatus());
            if (oldState == null || AIPhoneCallTaskStatusEnum.isDone(oldState.getStatus())) {
                return;
            }
            saveAndLogTaskStateTransit(taskId, oldState, newState, reason);

            // 2. 验证状态转换是否合法
            if (!isValidTransition(oldState, newState)) {
                log.error("非法的任务状态转换, taskId = {}, from {} to {}", taskId, oldState.getStatus(), newState.getStatus());
                Cat.logEvent("InvalidTaskTransition", String.format("%s_%s", oldState.getStatus(), newState.getStatus()));
                throw new RuntimeException("任务状态转换不合法");
            }

            // 3. 执行状态转换
            doTransitTaskState(callTaskEntity, oldState, newState);
        } catch (Exception e) {
            log.error("transitAICallTaskState taskId = {}, newState = {}, reason = {}", taskId, newState.getStatus(), reason, e);
            if (newState == AIPhoneCallTaskStatusEnum.FAILED) {
                Cat.logEvent("TaskTransitionDeadLock", String.valueOf(taskId));
                return;
            }
            transitAICallTaskState(taskId, AIPhoneCallTaskStatusEnum.FAILED, "状态转换执行失败导致失败，"+ e.getMessage());
        }
    }

    private void doTransitTaskState(AIPhoneCallTaskEntity callTaskEntity, AIPhoneCallTaskStatusEnum oldState, AIPhoneCallTaskStatusEnum newState) {
        // 1. 执行状态转换前的操作
        try {
            boolean continueTransition = beforeStateTransition(callTaskEntity, oldState, newState);
            if (!continueTransition) {
                return;
            }
        } catch (Exception e) {
            Cat.logError(e);
            throw new StateTransitionException("任务状态转换前检查异常, " + e.getMessage());
        }

        // 2. 更新状态（事务）
        DefaultTransactionDefinition transDef = new DefaultTransactionDefinition();
        transDef.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
        TransactionStatus status = transactionManager.getTransaction(transDef);
        try {
            callTaskEntity.setStatus(newState.getStatus());
            aiPhoneCallTaskDAO.updateTaskStatusById(callTaskEntity.getId(), callTaskEntity.getStatus());
            transactionManager.commit(status);
        } catch (Exception e) {
            Cat.logError(e);
            transactionManager.rollback(status);
            throw new StateTransitionException("任务状态更新异常, " + e.getMessage());
        }

        // 3. 执行状态转换后的操作
        try {
            afterStateTransition(callTaskEntity, oldState, newState);
        } catch (Exception e) {
            Cat.logError(e);
            throw new StateTransitionException("任务状态转换后处理异常, " + e.getMessage());
        }
    }

    private void saveAndLogTaskStateTransit(long taskId, AIPhoneCallTaskStatusEnum oldState, AIPhoneCallTaskStatusEnum newState, String reason) {
        log.info("task state transit, taskId = {}, from {} to {}, reason = {}", taskId, oldState.getStatus(), newState.getStatus(), reason);

        AIPhoneCallStateTransitLogEntity callStateTransitLogEntity = new AIPhoneCallStateTransitLogEntity();
        callStateTransitLogEntity.setNewState(newState.getStatus());
        callStateTransitLogEntity.setOldState(oldState.getStatus());
        callStateTransitLogEntity.setTaskId(taskId);
        callStateTransitLogEntity.setReason(reason);
        callStateTransitLogDAO.insert(callStateTransitLogEntity);
    }

    /**
     * 外呼明细状态转换
     */
    public void transitAICallDetailState(long callDetailId, AIPhoneCallDetailStatusEnum newState, String reason) {
        if (newState == null || callDetailId <= 0) {
            return;
        }

        try {
            // 1. 查询外呼明细当前状态
            AIPhoneCallDetailEntity callDetailEntity = aiPhoneCallDetailDAO.getById(callDetailId);
            AIPhoneCallTaskEntity callTaskEntity = aiPhoneCallTaskDAO.getByTaskId(callDetailEntity.getTaskId());
            AIPhoneCallDetailStatusEnum oldState = AIPhoneCallDetailStatusEnum.getByStatus(callDetailEntity.getStatus());
            if (oldState == null || AIPhoneCallDetailStatusEnum.isDone(oldState.getStatus())) {
                return;
            }
            saveAndLogDetailStateTransit(callDetailEntity, oldState, newState, reason);

            // 2. 验证状态转换是否合法
            if (!isValidTransition(oldState, newState)) {
                log.error("非法的明细状态转换, callDetailId = {}, from {} to {}", callDetailId, oldState.getStatus(), newState.getStatus());
                Cat.logEvent("InvalidDetailTransition", String.format("%s_%s", oldState.getStatus(), newState.getStatus()));
                throw new RuntimeException("任务明细状态转换不合法");
            }

            // 3. 执行状态转换
            doTransitDetailState(callTaskEntity, callDetailEntity, oldState, newState);
        } catch (Exception e) {
            Cat.logError(e);
            log.error("transitAICallTaskState detailId = {}, newState = {}, reason = {}", callDetailId, newState.getStatus(), reason, e);
            if (newState == AIPhoneCallDetailStatusEnum.FAILED) {
                Cat.logEvent("DetailTransitionDeadLock", String.valueOf(callDetailId));
                return;
            }
            transitAICallDetailState(callDetailId, AIPhoneCallDetailStatusEnum.FAILED, "明细状态转换执行失败导致失败，" + e.getMessage());
        }

    }

    private void doTransitDetailState(AIPhoneCallTaskEntity callTaskEntity, AIPhoneCallDetailEntity callDetailEntity, AIPhoneCallDetailStatusEnum oldState, AIPhoneCallDetailStatusEnum newState) {
        // 1. 执行状态转换前的操作
        try {
            boolean continueTransition = beforeStateTransition(callDetailEntity, oldState, newState, callTaskEntity);
            if (!continueTransition) {
                return;
            }
        } catch (Exception e) {
            Cat.logError(e);
            throw new StateTransitionException("任务明细状态转换前检查异常, " + e.getMessage());
        }

        // 2. 更新状态
        DefaultTransactionDefinition transDef = new DefaultTransactionDefinition();
        transDef.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
        TransactionStatus status = transactionManager.getTransaction(transDef);
        try {
            callDetailEntity.setStatus(newState.getStatus());
            aiPhoneCallDetailDAO.updateDetailStatusById(callDetailEntity.getId(), callDetailEntity.getStatus());
            transactionManager.commit(status);
        } catch (Exception e) {
            Cat.logError(e);
            transactionManager.rollback(status);
            throw new StateTransitionException("任务明细状态更新异常, " + e.getMessage());
        }

        // 3. 执行状态转换后的操作
        try {
            afterStateTransition(callDetailEntity, oldState, newState, callTaskEntity);
        } catch (Exception e) {
            Cat.logError(e);
            throw new StateTransitionException("任务明细状态转换后处理异常, " + e.getMessage());
        }
    }

    private void saveAndLogDetailStateTransit(AIPhoneCallDetailEntity callDetailEntity, AIPhoneCallDetailStatusEnum oldState, AIPhoneCallDetailStatusEnum newState, String reason) {
        log.info("detail state transit, callDetailId = {}, from {} to {}, reason = {}", callDetailEntity.getId(), oldState.getStatus(), newState.getStatus(), reason);

        AIPhoneCallStateTransitLogEntity callStateTransitLogEntity = new AIPhoneCallStateTransitLogEntity();
        callStateTransitLogEntity.setNewState(newState.getStatus());
        callStateTransitLogEntity.setOldState(oldState.getStatus());
        callStateTransitLogEntity.setTaskId(callDetailEntity.getTaskId());
        callStateTransitLogEntity.setReason(reason);
        callStateTransitLogEntity.setDetailId(callDetailEntity.getId());
        callStateTransitLogEntity.setSequenceId(callDetailEntity.getSequenceId());
        callStateTransitLogDAO.insert(callStateTransitLogEntity);
    }

    private boolean beforeStateTransition(AIPhoneCallDetailEntity callDetailEntity, AIPhoneCallDetailStatusEnum oldState, AIPhoneCallDetailStatusEnum newState, AIPhoneCallTaskEntity callTaskEntity) {
        switch (newState) {
            case PROCESSING:
                // IVR外呼前校验
                return callDomainService.preCheckBeforeIvrCall(callDetailEntity.getId());
            case RETRY:
                // 重新外呼前校验
                return callDomainService.preCheckBeforeRetry(callDetailEntity.getId());
        }
        return true;
    }

    private void afterStateTransition(AIPhoneCallDetailEntity callDetailEntity, AIPhoneCallDetailStatusEnum oldState, AIPhoneCallDetailStatusEnum newState, AIPhoneCallTaskEntity callTaskEntity) {
        AIPhoneCallDetailStatusEnum newDetailState = AIPhoneCallDetailStatusEnum.getByStatus(callDetailEntity.getStatus());
        switch (newDetailState) {
            case PROCESSING:
                // 开始发起IVR外呼
                callDomainService.initiateCall(oldState, callDetailEntity.getId());
                break;
            case SUCCESS:
                // 通话记录后置处理
                callDomainService.callSuccessPostProcess(callDetailEntity.getId(), callTaskEntity.getId());
                break;
            case DELAY:
                // 延迟调度
                callDomainService.delayCall(callDetailEntity.getId());
                break;
            case RETRY:
                // 重试
                callDomainService.retryCall(callDetailEntity.getId());
                break;
            case FAILED:
            case BLOCK:
                // 继续调度下一个
                transitAICallTaskState(callDetailEntity.getTaskId(), AIPhoneCallTaskStatusEnum.PROCESSING, "该明细外呼失败，继续外呼下一个");
                break;
        }
    }


    private boolean isValidTransition(AIPhoneCallTaskStatusEnum from, AIPhoneCallTaskStatusEnum to) {
        // 合法的任务状态转换
        Set<AIPhoneCallTaskStatusEnum> validStates = VALID_TRANSITIONS.get(from);
        return validStates != null && validStates.contains(to);
    }

    private boolean isValidTransition(AIPhoneCallDetailStatusEnum from, AIPhoneCallDetailStatusEnum to) {
        Set<AIPhoneCallDetailStatusEnum> validStates = DETAIL_VALID_TRANSITIONS.get(from);
        return validStates != null && validStates.contains(to);
    }

    private boolean beforeStateTransition(AIPhoneCallTaskEntity callTaskEntity, AIPhoneCallTaskStatusEnum oldState, AIPhoneCallTaskStatusEnum newState) {
//        switch (newState) {
//            case PROCESSING:
//                return callDomainService.canScheduleNext(callTaskEntity.getId());
//            case COMPLETED:
//                return callDomainService.checkTaskIsComplete(callTaskEntity.getId());
//        }
        return true;
    }
    
    private void afterStateTransition(AIPhoneCallTaskEntity task, AIPhoneCallTaskStatusEnum oldState, AIPhoneCallTaskStatusEnum newState) {
        switch (newState) {
            case TERMINATED:
                callDomainService.cancelTaskDetail(task.getId());
                break;
            case FAILED:
                callDomainService.cancelTaskDetail(task.getId());
                callbackProcessor.onFail(task.getId());
                break;
            case PAUSED:
                callbackProcessor.onDelay(task.getId());
                break;
            case PROCESSING:
                // 开始执行任务
                callDomainService.executeTask(task.getId(), oldState);
                break;
            case COMPLETED:
                // 回调给业务
                callbackProcessor.onComplete(task.getId());
                break;
        }
    }

}
