package com.sankuai.dzim.pilot.acl.impl;

import com.dianping.pigeon.remoting.common.codec.SerializerType;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonClient;
import com.sankuai.beautycontent.beautytagapi.dto.tag.BaseTagMetaDTO;
import com.sankuai.beautycontent.beautytagapi.service.BaseTagManageFacade;
import com.sankuai.beautycontent.intention.api.IntentionAdminService;
import com.sankuai.beautycontent.intention.dto.SynonymDTO;
import com.sankuai.beautycontent.intention.dto.SynonymResponse;
import com.sankuai.beautycontent.intention.request.SynonymAdminDTO;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.acl.MedicalBeautyAclService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: zhouyibing
 * @date: 2024/5/27
 */
@Service
@Slf4j
public class MedicalBeautyAclServiceImpl implements MedicalBeautyAclService {

    @MdpPigeonClient(url = "com.sankuai.beautycontent.intention.api.IntentionAdminService", serialize = SerializerType.THRIFT, timeout = 5000)
    private IntentionAdminService intentionAdminService;

    @MdpPigeonClient(url = "com.sankuai.beautycontent.beautytagapi.service.BaseTagManageFacade", timeout = 5000)
    private BaseTagManageFacade baseTagManageFacade;

    @MdpConfig("com.sankuai.mim.pilot.medical.beauty.rewrite.word.map")
    private HashMap<String, String> medicalBeautyRewriteWordMap;

    @MdpConfig("com.sankuai.mim.pilot.medical.beauty.forbidden.word.set")
    private HashSet<String> medicalBeautyForbiddenWordSet;

    private static final int MEDICAL_BEAUTY_SYNONYM_BIZ_TYPE = 1;

    private static final String MEDICAL_BEAUTY_SYNONYM_LIB_NAME = "医美同义词";

    private static final int MEDICAL_BEAUTY_PROJECT_WORD_BIZ_TYPE = 1;

    private static final int MEDICAL_BEAUTY_EFFICACY_WORD_BIZ_TYPE = 2;

    private Map<String, SynonymDTO> synonymMap = Maps.newHashMap();

    private Set<String> efficacyWordSet = Sets.newHashSet();

    private Set<String> projectWordSet = Sets.newHashSet();

    @PostConstruct
    public void init() {
        initSynonymMap();
        initEfficacyWordSet();
        initProjectWordSet();
    }

    @Override
    public String getStandardWord(String word) {
        initSynonymMap();
        SynonymDTO synonymDTO = synonymMap.get(word);
        if (synonymDTO == null) {
            return null;
        }
        return synonymDTO.getEntityWord();
    }


    @Override
    public boolean isEfficacyWord(String word) {
        initEfficacyWordSet();
        return efficacyWordSet.contains(word);
    }

    @Override
    public boolean isProjectWord(String word) {
        initProjectWordSet();
        return projectWordSet.contains(word);
    }

    @Override
    public boolean containsForbiddenWord(String text) {
        for (String value : medicalBeautyForbiddenWordSet) {
            if (text.contains(value)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public String replaceRewriteWord(String text) {
        for (Map.Entry<String, String> entry : medicalBeautyRewriteWordMap.entrySet()) {
            if (text.contains(entry.getKey())) {
                text = text.replace(entry.getKey(), entry.getValue());
            }
        }
        return text;
    }

    private void initSynonymMap() {
        if (MapUtils.isEmpty(synonymMap)) {
            synchronized (this) {
                if (MapUtils.isEmpty(synonymMap)) {
                    try {
                        SynonymAdminDTO synonymAdminDTO = new SynonymAdminDTO();
                        synonymAdminDTO.setBizType(MEDICAL_BEAUTY_SYNONYM_BIZ_TYPE);
                        synonymAdminDTO.setLibName(MEDICAL_BEAUTY_SYNONYM_LIB_NAME);
                        SynonymResponse synonymResponse = intentionAdminService.querySynonymFromCache(synonymAdminDTO);
                        if (synonymResponse.getData() != null && CollectionUtils.isNotEmpty(synonymResponse.getData().getSynonyms())) {
                            List<SynonymDTO> synonymDTOList = synonymResponse.getData().getSynonyms();
                            this.synonymMap = buildSynonymMap(synonymDTOList);
                        }
                    } catch (Exception e) {
                        LogUtils.logFailLog(log, TagContext.builder().action("initSynonymMap").build(),
                                new WarnMessage("MedicalBeautyAclService", "初始化医美同义词失败", ""), null, null, e);
                    }
                }
            }
        }
    }

    private Map<String, SynonymDTO> buildSynonymMap(List<SynonymDTO> synonymDTOList) {
        //标准词预处理
        preProcessEntityWord(synonymDTOList);
        //标准词列表
        Set<String> entityWordList = synonymDTOList.stream().map(SynonymDTO::getEntityWord).collect(Collectors.toSet());
        Map<String, SynonymDTO> synonymMap = Maps.newHashMap();
        for (SynonymDTO synonymDTO : synonymDTOList) {
            List<String> synonymWords = synonymDTO.getSynonymWords();
            //同义词
            for (String synonymWord : synonymWords) {
                //防止一个词即使标准词，又是同义词，被替换掉
                if (entityWordList.contains(synonymWord)) {
                    continue;
                }
                synonymMap.putIfAbsent(synonymWord, synonymDTO);
            }
            //标准词
            synonymMap.putIfAbsent(synonymDTO.getEntityWord(), synonymDTO);
        }
        return synonymMap;
    }

    private void preProcessEntityWord(List<SynonymDTO> synonymDTOList) {
        //特殊规则，临时解决同义词库标准词中表达不合规的问题，长期看应该直接优化修改同义词库
        for (SynonymDTO synonymDTO : synonymDTOList) {
            String entityWord = synonymDTO.getEntityWord();
            if (entityWord.contains("水光针")) {
                synonymDTO.setEntityWord("水光补水");
                continue;
            }
            if (entityWord.contains("肉毒素")) {
                synonymDTO.setEntityWord(entityWord.replace("肉毒素", "注射"));
                continue;
            }
            if (entityWord.contains("肉毒")) {
                synonymDTO.setEntityWord(entityWord.replace("肉毒", "注射"));
                continue;
            }
        }
    }

    private void initEfficacyWordSet() {
        if (CollectionUtils.isEmpty(efficacyWordSet)) {
            synchronized (this) {
                if (CollectionUtils.isEmpty(efficacyWordSet)) {
                    this.efficacyWordSet = buildWordSet(MEDICAL_BEAUTY_EFFICACY_WORD_BIZ_TYPE);
                }
            }
        }
    }

    private void initProjectWordSet() {
        if (CollectionUtils.isEmpty(efficacyWordSet)) {
            synchronized (this) {
                if (CollectionUtils.isEmpty(efficacyWordSet)) {
                    this.projectWordSet = buildWordSet(MEDICAL_BEAUTY_PROJECT_WORD_BIZ_TYPE);
                }
            }
        }
    }

    private Set<String> buildWordSet(int wordBizType) {
        try {
            List<BaseTagMetaDTO> baseTagMetaDTOS = baseTagManageFacade.queryBaseTags(wordBizType);
            Set<String> wordSet = Sets.newHashSet();
            if (CollectionUtils.isNotEmpty(baseTagMetaDTOS)) {
                for (BaseTagMetaDTO baseTagMetaDTO : baseTagMetaDTOS) {
                    wordSet.add(baseTagMetaDTO.getName());
                }
            }
            return wordSet;
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("buildWordSet").build(),
                    new WarnMessage("MedicalBeautyAclService", "初始化医美词性列表失败", ""), null, null, e);
            return Sets.newHashSet();
        }
    }
}
