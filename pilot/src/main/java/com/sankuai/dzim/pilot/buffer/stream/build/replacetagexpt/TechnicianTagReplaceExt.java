package com.sankuai.dzim.pilot.buffer.stream.build.replacetagexpt;

import com.sankuai.dzim.pilot.buffer.enums.StreamEventCardTypeEnum;
import com.sankuai.dzim.pilot.buffer.stream.build.replacetagexpt.data.ReplaceContext;
import com.sankuai.dzim.pilot.dal.entity.pilot.TechnicianSyncEntity;
import com.sankuai.dzim.pilot.dal.pilotdao.TechnicianSyncDAO;
import com.sankuai.dzim.pilot.process.localplugin.beta.RecommendPlugin;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 内部助手测试使用,c端助手请使用CardBuild逻辑
 */
@Component
@Slf4j
public class TechnicianTagReplaceExt implements ReplaceTagExt {

    private static String SHOP_TABLE_TEMPLATE = "| **手艺人ID**     | %s           |\n" +
            "| -------- | ------------------ |\n" +
            "| **名称**     | %s       |\n" +
            "| **职位**     | %s       |\n" +
            "| **头像**     | %s       |\n" +
            "| **技能**     | %s        |\n" +
            "| **简介** | %s    |\n";

    @Autowired
    private TechnicianSyncDAO technicianSyncDAO;

    @Autowired
    private RecommendPlugin recommendPlugin;

    @Override
    public boolean accept(String tag) {
        return StreamEventCardTypeEnum.TECHNICIAN_CARD.getType().equals(tag);
    }

    /**
     * 把<shop>shopId<shop/> 替换成markdown表格
     *
     * @param replaceContext
     * @return
     */
    @Override
    public String replace(ReplaceContext replaceContext) {
        if (StringUtils.isBlank(replaceContext.getTag().getValue())) {
            return null;
        }

        String technicianStr = replaceContext.getTag().getValue();
        if (StringUtils.isEmpty(technicianStr)) {
            return null;
        }

        long technicianId = Long.parseLong(technicianStr);
        TechnicianSyncEntity technicianSyncEntity = technicianSyncDAO.findByTechnicianId(technicianId);
        if (technicianSyncEntity == null) {
            return null;
        }

        recommendPlugin.addRecommendObject(replaceContext.getContext().getUserId(), "technician", technicianId);

        String name = technicianSyncEntity.getTechnicianName();
        String avatar = StringUtils.isEmpty(technicianSyncEntity.getPhoto()) ? "-" : "![头像](" + technicianSyncEntity.getPhoto() + ")";
        String title = StringUtils.isEmpty(technicianSyncEntity.getTitle()) ? "-" : technicianSyncEntity.getTitle();
        String skill = StringUtils.isEmpty(technicianSyncEntity.getSkill()) ? "-" : technicianSyncEntity.getSkill();
        String summary = StringUtils.isEmpty(technicianSyncEntity.getSummary()) ? "-" : technicianSyncEntity.getSummary();

        return replaceContext.getData().replace(replaceContext.getTagContent(), String.format(SHOP_TABLE_TEMPLATE, technicianId, name, title, avatar, skill, summary));
    }
}
