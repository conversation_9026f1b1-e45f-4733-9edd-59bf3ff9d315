package com.sankuai.dzim.pilot.acl;

import com.sankuai.dzim.message.dto.AuditReqDTO;
import com.sankuai.dzim.message.dto.AuditResDTO;

/**
 * <AUTHOR>
 * @since 2024/7/31 19:22
 */
public interface AuditAclService {

    /**
     * 用户消息同步审核
     *
     * @param messageId     messageId
     * @param userId        userId
     * @param assistantType assistantType
     * @param content       content
     * @return
     */
    AuditResDTO audit(long messageId, String userId, int assistantType, String content);
}
