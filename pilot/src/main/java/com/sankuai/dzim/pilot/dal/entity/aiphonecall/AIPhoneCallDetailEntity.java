package com.sankuai.dzim.pilot.dal.entity.aiphonecall;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Maps;
import com.sankuai.dzim.pilot.enums.AIPhoneCallExtraKeyEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Slf4j
public class AIPhoneCallDetailEntity {

    private long id;

    private long shopId;

    private long taskId;

    /**
     * 加密手机号
     */
    private String callPhone;

    private int status;

    private String extraData;

    private int sequenceId;

    private int platform;

    private Date expectedCallTime;

    private String dynamicVariable;

    private long userId;

    private String botId;

    private String botVersion;

    private String routePoint;

    private String ddlDate;

    private String contactId;

    private Date addTime;

    private Date updateTime;

    public void putExtraDataByKey(AIPhoneCallExtraKeyEnum keyEnum, Object value) {
        if (keyEnum == null) {
            return;
        }
        try {
            Map<String, Object> extraInfoMap = JSON.parseObject(extraData, new TypeReference<Map<String, Object>>(){});
            if (extraInfoMap == null) {
                extraInfoMap = Maps.newConcurrentMap();
            }
            extraInfoMap.put(keyEnum.getKey(), value);
            this.extraData = JsonCodec.encodeWithUTF8(extraInfoMap);
        } catch (Exception e) {
            log.error("putExtraDataByKey error, extraData = {}, key = {}, value = {}", extraData, keyEnum.getKey(), JsonCodec.encodeWithUTF8(value), e);
        }
    }

    public Object getExtraDataByKey(AIPhoneCallExtraKeyEnum keyEnum) {
        if (keyEnum == null) {
            return null;
        }
        try {
            Map<String, Object> extraInfoMap = JSON.parseObject(extraData, new TypeReference<Map<String, Object>>(){});
            if (extraInfoMap == null) {
                return null;
            }
            return extraInfoMap.get(keyEnum.getKey());
        } catch (Exception e) {
            log.error("getExtraDataByKey error, extraData = {}, key = {}", extraData, keyEnum.getKey(), e);
        }
        return null;
    }
}
