package com.sankuai.dzim.pilot.process.localplugin;

import com.google.common.collect.Maps;
import com.sankuai.dzim.message.dto.MessageDTO;
import com.sankuai.dzim.pilot.api.data.AIAnswerTypeEnum;
import com.sankuai.dzim.pilot.api.enums.ProductTypeEnum;
import com.sankuai.dzim.pilot.chain.data.AIAnswerData;
import com.sankuai.dzim.pilot.chain.data.AIServiceContext;
import com.sankuai.dzim.pilot.chain.data.RetrievalComponent;
import com.sankuai.dzim.pilot.chain.impl.SimpleRagAIService;
import com.sankuai.dzim.pilot.domain.annotation.LocalPlugin;
import com.sankuai.dzim.pilot.process.localplugin.param.ProductQueryParam;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public class ProductRecommendPlugin {

    @Autowired
    private SimpleRagAIService simpleRagAIService;

    /**
     * 商品推荐由于是定制化的卡片，因此直接返回
     * @param productQueryParam
     * @return
     */
    @LocalPlugin(name = "ProductRecommendTool", description = "可以进行团购商品推荐", returnDirect = true)
    public AIAnswerData getProductAnswer(ProductQueryParam productQueryParam) {
        if (productQueryParam == null) {
            return new AIAnswerData(AIAnswerTypeEnum.PRODUCT_RECOMMEND.getType(), "参数错误");
        }
        if (productQueryParam.getShopId() <= 0) {
            return new AIAnswerData(AIAnswerTypeEnum.PRODUCT_RECOMMEND.getType(), "请正确提取出用户咨询的门店Id");
        }
        if (productQueryParam.getQuestion().equals("")) {
            return new AIAnswerData(AIAnswerTypeEnum.PRODUCT_RECOMMEND.getType(), "没有购买意图请不要使用该工具");
        }

        AIAnswerData answer = simpleRagAIService.execute(buildAIServiceContext(productQueryParam));
        return answer;
    }

    private AIServiceContext buildAIServiceContext(ProductQueryParam productQueryParam) {
        AIServiceContext aiServiceContext = new AIServiceContext();
        aiServiceContext.setModel("gpt-3.5-turbo-1106");
        aiServiceContext.setTemperature(0.2);
        aiServiceContext.setTopP(1);
        aiServiceContext.setMessageDTO(buildMessageDTO(productQueryParam));
        aiServiceContext.setRetrievalComponents(buildRetrievalComponents(productQueryParam));
        aiServiceContext.setSystemPrompt("你是一个严格遵守规则的客服,请你参考给定的商品信息给出相关的推荐购买语\n比如：\"亲亲，根据您的诉求以及选择，这边特定为您推荐了这款xx团购，里面包含xx服务，特别符合您的要求\"\n如果没有合适的商品，则输出\"\"");
        aiServiceContext.setAppId("1658315950980091959");
        aiServiceContext.setExtraInfo(buildContextExtraInfoMap(productQueryParam));
        return aiServiceContext;
    }

    private MessageDTO buildMessageDTO(ProductQueryParam productQueryParam) {
        MessageDTO messageDTO = new MessageDTO();
        messageDTO.setMessage(productQueryParam.getQuestion());
        return messageDTO;
    }

    private List<RetrievalComponent> buildRetrievalComponents(ProductQueryParam productQueryParam) {
        List<RetrievalComponent> retrievalComponents = Lists.newArrayList();
        RetrievalComponent retrievalComponent = new RetrievalComponent();
        retrievalComponent.setName("ProductKnowledgeRetrieval");
        retrievalComponent.setType2SubjectIdMap(buildType2SubjectIdMap());
        retrievalComponent.setExtraInfo(buildRetrievalExtraInfoMap(productQueryParam));
        retrievalComponent.setTopK(1);
        retrievalComponents.add(retrievalComponent);
        return retrievalComponents;
    }

    private Map<Integer, String> buildType2SubjectIdMap() {
        Map<Integer, String> type2SubjectIdMap = Maps.newHashMap();
        type2SubjectIdMap.put(1, String.valueOf(ProductTypeEnum.DEAL.getType()));
        return type2SubjectIdMap;
    }

    private Map<String, String> buildRetrievalExtraInfoMap(ProductQueryParam productQueryParam) {
        Map<String, String> extraInfoMap = Maps.newHashMap();
        extraInfoMap.put("shopId", String.valueOf(productQueryParam.getShopId()));
        extraInfoMap.put("platform", "100");
        return extraInfoMap;
    }

    private Map<String, Object> buildContextExtraInfoMap(ProductQueryParam productQueryParam) {
        Map<String, Object> extraInfoMap = Maps.newHashMap();
        extraInfoMap.put("productQueryParam", productQueryParam);
        return extraInfoMap;
    }
}
