package com.sankuai.dzim.pilot.buffer.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: zhouyibing
 * @date: 2024/8/1
 */
@Getter
@AllArgsConstructor
public enum StreamEventErrorTypeEnum {

    UN_LOGIN("unLogin", "未登录"),

    PARAM_ERROR("paramError", "参数异常"),

    NO_RIGHT("noRight", "无权限"),

    SERVER_ERROR("serverError", "服务异常"),

    AUDIT_ERROR("auditError", "审核异常")

    ;

    private final String type;

    private final String desc;

    public static StreamEventErrorTypeEnum getByErrorCode(int code) {
        if (code == 450) {
            return AUDIT_ERROR;
        }
        return SERVER_ERROR;
    }
}
