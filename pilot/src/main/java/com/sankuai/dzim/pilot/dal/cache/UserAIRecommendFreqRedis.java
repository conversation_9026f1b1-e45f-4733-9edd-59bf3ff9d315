package com.sankuai.dzim.pilot.dal.cache;

import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.dianping.squirrel.client.impl.redis.StoreBoundResponse;
import com.dianping.vc.sdk.lang.DateUtils;
import com.google.common.collect.Lists;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.acl.data.fraiday.ChatCompletionRequest;
import com.sankuai.dzim.pilot.utils.LionConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Random;

/**
 * 用户AI推荐频次缓存，用于限制推荐频次，以控制推荐成本，可以复用
 */
@Slf4j
@Component
public class UserAIRecommendFreqRedis {

    private static final String CATE_NAME_USER_AI_RECOMMEND_FREQUENCY = "UserAIRecommendFreq";

    private static final String FRIDAY_FREQUENCY_LIMIT_CATEGORY = "FridayFrequencyLimit";

    @Resource(name = "dzimRedisClient")
    private RedisStoreClient dzimRedisClient;

    @Autowired
    private LionConfigUtil lionConfigUtil;

    public boolean addFrequency(String imUserId, int recommendType) {
        StoreKey storeKey = new StoreKey(CATE_NAME_USER_AI_RECOMMEND_FREQUENCY, imUserId, recommendType);
        try {
            StoreBoundResponse storeBoundResponse = dzimRedisClient.incrWithUpperBound(storeKey, 1, lionConfigUtil.getMaxUserDayRecmdReplyTimes(), 0, calculateExpireSeconds());
            return storeBoundResponse.isOperated();
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("UserAIRecommendFreqRedis").userId(imUserId).build(),
                    new WarnMessage("addFrequency", "用户推送频次增加失败", null), imUserId, null, e);
            return false;
        }
    }

    private int calculateExpireSeconds() {
        // 有效期1个自然日
        // 加个60秒内的随机数，避免一下子集中失效，缓存清理大量数据
        int randomSecs = new Random(System.currentTimeMillis()).nextInt(60);
        Date nextDate = DateUtils.getDayBegin(DateUtils.nextDay());
        return DateUtils.DIFF_IN_SECONDS.getTimeDiff(new Date(), nextDate) + randomSecs;
    }

    public long loadFrequency(String imUserId, int recommendType) {
        StoreKey storeKey = new StoreKey(CATE_NAME_USER_AI_RECOMMEND_FREQUENCY, imUserId, recommendType);
        Long res = dzimRedisClient.get(storeKey);
        if (res == null) {
            return 0;
        }
        return res;
    }

    public boolean fridayFrequencyLimit(String appId, ChatCompletionRequest request) {
        int maxFridayInvokeTimes = lionConfigUtil.getMaxFridayInvokeTimes();
        try {
            StoreKey storeKey = new StoreKey(FRIDAY_FREQUENCY_LIMIT_CATEGORY, appId, com.sankuai.dzim.pilot.utils.DateUtils.covertDateNumStr(new Date()));
            Long cnt = dzimRedisClient.incrBy(storeKey, 1, com.sankuai.dzim.pilot.utils.DateUtils.secondsToDayEndWithRandom());
            if (cnt > maxFridayInvokeTimes) {
                LogUtils.logFailLog(log, TagContext.builder().action("FridayFrequencyLimit").build(),
                        new WarnMessage("FridayAclService", "Friday调用频次被限制",
                                "maxFridayInvokeTimes: " + maxFridayInvokeTimes), request, appId);
                return false;
            }
            return true;
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("FridayFrequencyLimit").build(),
                    new WarnMessage("FridayAclService", "Friday调用频次限制执行异常",
                            "maxFridayInvokeTimes: " + maxFridayInvokeTimes), Lists.newArrayList(request, appId), e);
            return true;
        }
    }
}
