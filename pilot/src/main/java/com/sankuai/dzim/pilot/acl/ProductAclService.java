package com.sankuai.dzim.pilot.acl;

import com.dianping.deal.sales.common.datatype.SalesDisplayInfoDTO;
import com.dianping.tpfun.product.api.sku.booktable.dto.PeriodStock;
import com.dianping.tpfun.product.api.sku.booktable.request.QueryBookTableRequest;
import com.dianping.tpfun.product.api.sku.dto.venue.GetVenueProductByShopReqDTO;
import com.dianping.tpfun.product.api.sku.dto.venue.ProductDTO;
import com.sankuai.dzim.pilot.acl.data.DealDetailData;
import com.sankuai.dzim.pilot.acl.data.ProductTimeStateQueryItem;
import com.sankuai.dzim.pilot.acl.data.ShopTimeStateQueryItem;
import com.sankuai.dzim.pilot.acl.data.req.ProductSearchReq;
import com.sankuai.dztheme.generalproduct.req.GeneralProductRequest;
import com.sankuai.dztheme.generalproduct.res.GeneralProductResult;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.request.QueryDealGroupIdByShopRequest;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupIdListResult;
import com.sankuai.spt.statequery.api.dto.BaseStateDTO;
import com.sankuai.spt.statequery.api.dto.ShopReserveModeDTO;
import com.sankuai.spt.statequery.api.enums.BaseStateQueryConditionKeyEnum;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

public interface ProductAclService {

    /**
     * 查询团购基础信息
     *
     * @param dealId
     * @return
     */
    DealGroupDTO getDealBaseInfo(long dealId);

    /**
     * 查询团购基础信息
     * @param dealId 团单id
     * @param isMt 是否美团
     * @return
     */
    DealGroupDTO getDealBaseInfo(long dealId, boolean isMt);

    Map<Long, DealGroupDTO> batchGetDealBaseInfo(List<Long> dealIds);

    Map<Long, DealGroupDTO> batchGetDealBaseInfo(List<Long> dealIds, boolean isMt);

    /**
     * 异步批量查询团购基础信息
     * >20个分批
     * @param dealIds dealIds
     * @return dealId -> DealGroupDTO
     */
    CompletableFuture<Map<Long, DealGroupDTO>> batchGetDealBaseInfoAsync(List<Long> dealIds, boolean isMt);

    /**
     * 批量查询团购基础信息
     *
     * @param dealIds
     * @return
     */
    List<DealGroupDTO> batchQueryDealBaseInfo(List<Long> dealIds);

    List<DealDetailData> searchProduct(ProductSearchReq req);

    /**
     *将美团团单id转换为点评团单id
     * @param mtDealIds 单次批量查询当前限制最大团单数量20 https://km.sankuai.com/collabpage/1603747248
     * @return
     */
    List<DealGroupDTO> queryDpDealIdByMtDealId(List<Long> mtDealIds);

    /**
     * 批量查询泛商品（预订）销量
     */
    CompletableFuture<Map<Long, SalesDisplayInfoDTO>> batchQueryReserveSales(List<Long> productIds, boolean isMt);

    /**
     * 批量查询团单销量
     * 同一门店下团单
     * 销量默认口径--年销量
     * @param dealIds dealIds
     * @param isMt 是否美团
     * @return dealId -> SalesDisplayInfoDTO
     */
    CompletableFuture<Map<Long, SalesDisplayInfoDTO>> batchQueryDealSales(List<Long> dealIds, boolean isMt);

    /**
     * 批量查询门店预约方式
     * @param mtShopIds
     * @return
     */
    Map<Long, ShopReserveModeDTO> batchQueryShopReserveMode(List<Long> mtShopIds);

    /**
     * 批量查询门店可预约状态
     * @param queryItems 商户查询参数
     * @param condition 查询条件（足疗传服务时长，美发传项目code）
     * @return 商户可预约状态，key: 商户查询参数, value: baseStateDTO
     */
    Map<ShopTimeStateQueryItem, BaseStateDTO> batchQueryShopCanBookingState(List<ShopTimeStateQueryItem> queryItems, Map<BaseStateQueryConditionKeyEnum, String> condition);

    /**
     * 批量查询团单可预约状态
     * @param queryItems 团单查询参数
     * @return 商户可预约状态，key: 团单查询参数, value: baseStateDTO
     */
    Map<ProductTimeStateQueryItem, BaseStateDTO> batchQueryProductCanBookingState(List<ProductTimeStateQueryItem> queryItems);

    /**
     * 查询单个门店在线预订商品
     */
    QueryDealGroupIdListResult queryProductByShopId(QueryDealGroupIdByShopRequest request);

    /**
     * 查询平台预订商品基本信息
     */
    List<DealGroupDTO> queryReserveProductBaseInfo(List<Long> platformProductIds, Long platformCategoryId, Set<String> dealGroupAttrs, Set<String> strings);

    /**
     * 查询泛商品主题服务获取商品信息
     */
    CompletableFuture<GeneralProductResult> queryGeneralProductTheme(GeneralProductRequest request);

    /**
     * 获取场馆门店指定运动类型的商品信息，
     * 场地类型、天、关联产品映射
     */
    Map<Integer, Map<String, List<ProductDTO>>> getVenueProductByShop(GetVenueProductByShopReqDTO reqDTO);


    /**
     * 获取商品库存信息
     */
    List<PeriodStock> batchQueryBookTableStock(List<QueryBookTableRequest> queryBookTableRequests);

    /**
     * 判断是否是特殊团购
     */
    boolean isSpecialDealGroup(DealGroupDTO dealGroupDTO);

}
