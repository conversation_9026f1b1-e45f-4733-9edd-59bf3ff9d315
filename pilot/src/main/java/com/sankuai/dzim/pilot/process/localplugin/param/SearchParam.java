package com.sankuai.dzim.pilot.process.localplugin.param;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import lombok.Data;

@Data
public class SearchParam {

    @JsonPropertyDescription("你想搜索的一些问题,需要基于用户当前的问题和历史消息,把问题改写的仅可能晚上和具体,如:'上海现在的天气是什么状况','巴黎奥运会最新的金牌榜'等")
    @JsonProperty(required = true)
    private String question;
}
