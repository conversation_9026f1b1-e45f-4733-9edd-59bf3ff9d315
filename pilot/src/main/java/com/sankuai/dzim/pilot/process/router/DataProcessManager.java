package com.sankuai.dzim.pilot.process.router;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.api.data.TabCardData;
import com.sankuai.dzim.pilot.api.data.search.generative.*;
import com.sankuai.dzim.pilot.api.enums.assistant.PlatformEnum;
import com.sankuai.dzim.pilot.buffer.stream.build.replacetagexpt.data.SearchWordConfig;
import com.sankuai.dzim.pilot.process.data.TemplateBaseInfo;
import com.sankuai.dzim.pilot.process.data.TemplateData;
import com.sankuai.dzim.pilot.process.data.TemplateDataCollection;
import com.sankuai.dzim.pilot.process.router.data.RouteResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/10/09 15:18
 */
@Component
@Slf4j
public class DataProcessManager {
    /**
     * 模板数据后置处理策略
     */
    @Autowired
    private List<TemplateDataProcessStrategy> templateProcessStrategies;

    @ConfigValue(
            key = "com.sankuai.mim.pilot.template.base.config",
            defaultValue = "{\"2\":{\"headInfo\":{\"titleInfo\":{\"title\":\"根据症状选项目\",\"icon\":\"默认-icon\",\"link\":\"默认-link\"},\"moreInfo\":{\"title\":\"更多\",\"icon\":\"默认-更多-icon\",\"link\":\"默认-更多-link\"}},\"themeInfo\":{},\"relatedQuestionBaseLink\":\"相关问题跳链\",\"projectTabBaseLink\":\"项目跳链\"}}"
    )
    private Map<Integer, TemplateBaseInfo> bizType2TemplateBaseInfo;

    @MdpConfig("com.sankuai.mim.pilt.assistant.replace.tag.search.word:{}")
    private HashMap<Integer, ArrayList<SearchWordConfig>> bizType2searchWordConfigs;

    @ConfigValue(
            key = "com.sankuai.mim.pilot.assistant.replace.tag.url.format",
            defaultValue = "{\"1\":\"imeituan://www.meituan.com/gc/mrn?mrn_biz=gcbu&mrn_entry=searchpage&mrn_component=mrn-gc-searchresult&categoryid=%s&keyword=%s&subbizid=7&source=manhome\"}"
    )
    private Map<Integer, String> bizType2UrlFormat;

    @ConfigValue(key = "com.sankuai.mim.pilot.assistant.more.link.jon.str", defaultValue = "[\"&preAsk=\"]")
    private Set<String> moreLinkJointStr;

    /**
     * 数据后置处理，根据不同的模板类型执行不同的后置处理策略
     * 
     * @param routeResponse
     */
    public RouteResponse processTemplateData(RouteResponse routeResponse) {
        if (routeResponse.getSuggestionAnswerDTO() == null) {
            return routeResponse;
        }
        templateProcessStrategies.stream().filter(strategy -> strategy.match(routeResponse)).findFirst()
                .ifPresent(strategy -> strategy.process(routeResponse));
        Optional.ofNullable(routeResponse.getSuggestionAnswerDTO()).map(SuggestionAnswerDTO::getTemplateDataDTO)
                .ifPresent(templateDataDTO -> {
                    if (!CollectionUtils.isEmpty(routeResponse.getCrowdIds())) {
                        templateDataDTO.setCrowdId(routeResponse.getCrowdIds().get(0));
                    }
                });
        processContent(routeResponse);
        return routeResponse;
    }

    /**
     * 解析并根据Lion配置填充没有配置的模板数据
     * 
     * @param templateData 模板数据JSON字符串
     * @param bizType 业务类型
     * @return 解析后的SuggestionAnswerDTO
     */
    public SuggestionAnswerDTO parseAndFillTemplateData(QueryAnswerRequest request, String templateData, Integer bizType, Integer templateType) {
        SuggestionAnswerDTO suggestionAnswerDTO = convert2SuggestionAnswerData(templateData, templateType);
        if (suggestionAnswerDTO == null) {
            return null;
        }
        // 封装头部数据
        fillHeadInfo(request, suggestionAnswerDTO.getTemplateDataDTO(), bizType);
        // 封装主题数据
        fillThemeInfo(suggestionAnswerDTO.getTemplateDataDTO(), bizType);
        // 封装tab中的一些数据
        fillTabInfo(request, suggestionAnswerDTO.getTemplateDataDTO(), bizType);
        return suggestionAnswerDTO;
    }

    private SuggestionAnswerDTO convert2SuggestionAnswerData(String answer, Integer templateType) {
        TemplateDataCollection collection = JsonCodec.decode(answer, TemplateDataCollection.class);
        List<TemplateData> templateDataS = collection.getTemplateDataList();
        Random random = new Random();
        templateDataS = templateDataS.stream().filter(iterm -> iterm.getTemplateType().equals(templateType))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(templateDataS)) {
            LogUtils.logFailLog(log, TagContext.builder().build(),
                    WarnMessage.build("convert2SuggestionAnswerData", "获取模板类型对应的数据失败", ""), answer + templateType,
                    null);
            return null;
        }
        TemplateData templateData = templateDataS.get(random.nextInt(templateDataS.size()));
        SuggestionAnswerDTO suggestionAnswerDTO = new SuggestionAnswerDTO();
        TemplateDataDTO templateDataDTO = new TemplateDataDTO();
        suggestionAnswerDTO.setTemplateDataDTO(templateDataDTO);
        templateDataDTO.setTabInfo(
                templateData.getTabCards().stream().map(this::convert2TabInfoDTO).collect(Collectors.toList()));
        templateDataDTO.setHeadInfo(templateData.getHeadInfo());
        return suggestionAnswerDTO;
    }

    private TabInfoDTO convert2TabInfoDTO(TabCardData tabCardData) {
        TabInfoDTO tabInfoDTO = new TabInfoDTO();
        BeanUtils.copyProperties(tabCardData, tabInfoDTO);
        tabInfoDTO.setContentInfo(tabCardData.getContent());
        Optional.ofNullable(tabCardData.getProjectTabs()).ifPresent(projectTabs -> {
            tabInfoDTO.setInnerProjectTabs(projectTabs);
            tabInfoDTO.setOuterProjectTabs(projectTabs);
        });
        return tabInfoDTO;
    }

    private void fillThemeInfo(TemplateDataDTO templateDataDTO, Integer bizType) {
        ThemeInfoDTO themeInfo = Optional.ofNullable(bizType2TemplateBaseInfo.get(bizType))
                .map(TemplateBaseInfo::getThemeInfo).orElse(null);
        templateDataDTO.setThemeInfo(themeInfo);
    }

    private void fillHeadInfo(QueryAnswerRequest request, TemplateDataDTO templateDataDTO, Integer bizType) {
        HeadInfoDTO headInfo = Optional.ofNullable(templateDataDTO.getHeadInfo()).orElse(new HeadInfoDTO());
        // 填充左边的Title
        Optional<HeadInfoDTO.TitleInfoDTO> titleInfoOpt = Optional.ofNullable(bizType2TemplateBaseInfo.get(bizType))
                .map(TemplateBaseInfo::getHeadInfo).map(HeadInfoDTO::getTitleInfo);
        fillHeadTitleInfo(request, headInfo.getTitleInfo(), titleInfoOpt);
        // 填充右边的More
        Optional<HeadInfoDTO.MoreInfoDTO> moreInfoOpt = Optional.ofNullable(bizType2TemplateBaseInfo.get(bizType))
                .map(TemplateBaseInfo::getHeadInfo).map(HeadInfoDTO::getMoreInfo);
        fillHeadMoreInfo(request, headInfo.getMoreInfo(), moreInfoOpt);
        templateDataDTO.setHeadInfo(headInfo);
    }

    private void fillTabInfo(QueryAnswerRequest request, TemplateDataDTO templateDataDTO, Integer bizType) {
        Optional.ofNullable(templateDataDTO.getTabInfo()).ifPresent(tabInfoDTOS -> {
            tabInfoDTOS.forEach(tabInfoDTO -> {
                Optional.ofNullable(tabInfoDTO.getRelatedQuestions())
                        .ifPresent(relatedQuestions -> relatedQuestions.forEach(relatedQuestion -> {
                            if (StringUtils.isBlank(relatedQuestion.getLink())) {
                                Optional.ofNullable(bizType2TemplateBaseInfo.get(bizType))
                                        .map(TemplateBaseInfo::getRelatedQuestionBaseLink).ifPresent(
                                                link -> {
                                                    if (request.getPlatform() == PlatformEnum.DP.getType()) {
                                                        link = link.replace("imeituan://www.meituan.com/", "dianping://");
                                                    }
                                                    relatedQuestion.setLink(link + relatedQuestion.getQuestion());
                                                });
                            }

                        }));
            });
        });
    }

    private void fillHeadTitleInfo(QueryAnswerRequest request, HeadInfoDTO.TitleInfoDTO headTitleInfo,
            Optional<HeadInfoDTO.TitleInfoDTO> headTitleInfoOpt) {
        if (StringUtils.isEmpty(headTitleInfo.getIcon())) {
            headTitleInfo.setIcon(headTitleInfoOpt.map(HeadInfoDTO.TitleInfoDTO::getIcon).orElse(StringUtils.EMPTY));
        }
        if (StringUtils.isEmpty(headTitleInfo.getLink())) {
            headTitleInfo.setLink(headTitleInfoOpt.map(HeadInfoDTO.TitleInfoDTO::getLink).orElse(StringUtils.EMPTY));
            if (request.getPlatform() == PlatformEnum.DP.getType()) {
                headTitleInfo.setLink(headTitleInfo.getLink().replace("imeituan://www.meituan.com/", "dianping://"));
            }
        }
        if (StringUtils.isEmpty(headTitleInfo.getTitle())) {
            headTitleInfo.setTitle(headTitleInfoOpt.map(HeadInfoDTO.TitleInfoDTO::getTitle).orElse(StringUtils.EMPTY));
        }
        if (StringUtils.isEmpty(headTitleInfo.getSubTitle())) {
            headTitleInfo
                    .setSubTitle(headTitleInfoOpt.map(HeadInfoDTO.TitleInfoDTO::getSubTitle).orElse(StringUtils.EMPTY));
        }

    }

    private void fillHeadMoreInfo(QueryAnswerRequest request, HeadInfoDTO.MoreInfoDTO headMoreInfo,
            Optional<HeadInfoDTO.MoreInfoDTO> headMoreInfoOpt) {
        if (StringUtils.isEmpty(headMoreInfo.getIcon())) {
            headMoreInfo.setIcon(headMoreInfoOpt.map(HeadInfoDTO.MoreInfoDTO::getIcon).orElse(StringUtils.EMPTY));
        }
        if (StringUtils.isEmpty(headMoreInfo.getLink())) {
            headMoreInfo.setLink(headMoreInfoOpt.map(HeadInfoDTO.MoreInfoDTO::getLink).orElse(StringUtils.EMPTY));
            headMoreInfoOpt.map(HeadInfoDTO.MoreInfoDTO::getLink)
                    .ifPresent(link -> {
                        for (String joinStr : moreLinkJointStr) {
                            if (link.contains(joinStr)) {
                                headMoreInfo.setLink(link + request.getQuestion());
                                break;
                            }
                        }
                    });
            if (request.getPlatform() == PlatformEnum.DP.getType()) {
                headMoreInfo.setLink(headMoreInfo.getLink().replace("imeituan://www.meituan.com/", "dianping://"));
            }
        }
        if (StringUtils.isEmpty(headMoreInfo.getTitle())) {
            headMoreInfo.setTitle(headMoreInfoOpt.map(HeadInfoDTO.MoreInfoDTO::getTitle).orElse(StringUtils.EMPTY));
        }
    }

    private void processContent(RouteResponse routeResponse) {
        Optional.ofNullable(routeResponse).map(RouteResponse::getSuggestionAnswerDTO)
                .map(SuggestionAnswerDTO::getTemplateDataDTO).map(TemplateDataDTO::getTabInfo)
                .ifPresent(tabInfoDTOS -> {
                    tabInfoDTOS.forEach(tabInfoDTO -> {
                        processContentSearchWord(tabInfoDTO.getContentInfo(), routeResponse.getBizType());
                    });
                });
    }

    private void processContentSearchWord(TabInfoDTO.ContentDTO contentInfo, Integer bizType) {
        if (!bizType2UrlFormat.containsKey(bizType) || !bizType2searchWordConfigs.containsKey(bizType)) {
            return;
        }
        Pattern pattern = Pattern.compile("\\*\\*(.*?)\\*\\*");
        Optional.ofNullable(contentInfo).map(TabInfoDTO.ContentDTO::getContent).ifPresent(content -> {
            Matcher matcher = pattern.matcher(content);
            StringBuffer result = new StringBuffer();
            while (matcher.find()) {
                String keyword = matcher.group(1);
                SearchWordConfig searchWordConfig = getWordConfig(keyword, bizType);
                if (searchWordConfig != null) {
                    String url = replaceUrl(searchWordConfig, bizType);
                    matcher.appendReplacement(result, "[" + searchWordConfig.getStandWord() + "](" + url + ")");
                } else {
                    matcher.appendReplacement(result, matcher.group(0));
                }
            }
            matcher.appendTail(result);
            contentInfo.setContent(result.toString());
        });
    }

    private String replaceUrl(SearchWordConfig searchWordConfig, Integer bizType) {
        return String.format(bizType2UrlFormat.get(bizType), searchWordConfig.getCategoryId(),
                searchWordConfig.getStandWord());
    }

    private SearchWordConfig getWordConfig(String keyword, Integer bizType) {
        List<SearchWordConfig> searchWordConfigs = bizType2searchWordConfigs.get(bizType);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(searchWordConfigs)) {
            return null;
        }

        SearchWordConfig result = null;
        for (SearchWordConfig config : searchWordConfigs) {
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(config.getAcceptWords())
                    && config.getAcceptWords().contains(keyword)) {
                result = config;
                break;
            }
            if (config.getStandWord().equals(keyword)) {
                result = config;
                break;
            }
        }

        LogUtils.logReturnInfo(log, TagContext.builder()
                .action(result == null ? "SearchTagReplace::Replace" : "SearchTagReplace::Miss").bizId(keyword).build(),
                new WarnMessage("SearchTagReplace", "搜索词替换", ""), keyword, result);
        return result;
    }

}
