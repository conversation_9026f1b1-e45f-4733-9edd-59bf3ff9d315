package com.sankuai.dzim.pilot.process.aiphonecall.calldialogpost;

import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.google.common.collect.Maps;
import com.sankuai.dzim.pilot.acl.FridayAclService;
import com.sankuai.dzim.pilot.acl.data.ChatCompletionConfig;
import com.sankuai.dzim.pilot.acl.data.fraiday.ChatCompletionRequest;
import com.sankuai.dzim.pilot.acl.data.fraiday.ChatCompletionResponse;
import com.sankuai.dzim.pilot.acl.data.fraiday.FridayMessage;
import com.sankuai.dzim.pilot.acl.data.fraiday.FridayMessageRoleEnum;
import com.sankuai.dzim.pilot.dal.entity.aiphonecall.AIPhoneCallSentimentEntity;
import com.sankuai.dzim.pilot.dal.pilotdao.aiphonecall.AIPhoneCallSentimentDAO;
import com.sankuai.dzim.pilot.domain.ShopDomainService;
import com.sankuai.dzim.pilot.process.aiphonecall.data.AIPhoneCallDialogPostContext;
import com.sankuai.dzim.pilot.utils.JsonUtils;
import com.sankuai.dzim.pilot.utils.LionConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Slf4j
@Component
@CallDialogPost(name = "SentimentRecognizePostExtPt")
public class SentimentRecognizePostExtPt implements CallDialogPostExtPt {

    /**
     * AI外呼情绪识别线程池
     */
    private static ThreadPool AI_PHONE_CALL_SENTIMENT_RECOGNIZE_POOL = Rhino.newThreadPool("AI_PHONE_CALL_SENTIMENT_RECOGNIZE_POOL",
            DefaultThreadPoolProperties.Setter().withCoreSize(20).withMaxSize(100).withMaxQueueSize(1000));


    @Autowired
    private LionConfigUtil lionConfigUtil;

    @Autowired
    private FridayAclService fridayAclService;

    @Autowired
    private AIPhoneCallSentimentDAO callSentimentDAO;

    @Autowired
    private ShopDomainService shopDomainService;

    @Override
    public void handle(AIPhoneCallDialogPostContext context) {
        AI_PHONE_CALL_SENTIMENT_RECOGNIZE_POOL.submit(() -> asyncSentimentRecognize(context));
    }

    private void asyncSentimentRecognize(AIPhoneCallDialogPostContext context) {
        try {
            ChatCompletionConfig chatCompletionConfig = lionConfigUtil.getEmotionRecognizeConfig();
            ChatCompletionRequest request = buildChatCompletionRequest(context.getCallDialog(), chatCompletionConfig);
            ChatCompletionResponse chatCompletionResponse = fridayAclService.chatCompletion(chatCompletionConfig.getAppId(), request);
            if (chatCompletionResponse == null || CollectionUtils.isEmpty(chatCompletionResponse.getChoices())) {
                return;
            }
            String sentimentStr = JsonUtils.getValFromJsonString(chatCompletionResponse.getChoices().get(0).getMessage().getContent(), "sentiment", String.class);
            // -1:消极；0:中立；1:积极
            int sentiment = Integer.valueOf(sentimentStr);

            AIPhoneCallSentimentEntity sentimentEntity = buildAIPhoneCallSentimentEntity(context, sentiment);
            callSentimentDAO.insert(sentimentEntity);
        } catch (Exception e) {
            log.error("asyncEmotionRecognize error, callDetailId = {}.", context.getDetailId(), e);
        }
    }

    private AIPhoneCallSentimentEntity buildAIPhoneCallSentimentEntity(AIPhoneCallDialogPostContext context, int sentiment) {
        AIPhoneCallSentimentEntity entity = new AIPhoneCallSentimentEntity();
        entity.setSentiment(sentiment);
        entity.setCallDetailId(context.getDetailId());
        entity.setStatus(1);
        entity.setDpShopId(shopDomainService.getDpShopId(context.getShopId(), context.getPlatform()));
        entity.setSceneType(context.getAiPhoneCallSceneTypeEnum().getType());
        return entity;
    }

    private ChatCompletionRequest buildChatCompletionRequest(String naturalCallDialog, ChatCompletionConfig chatCompletionConfig) {
        ChatCompletionRequest request = new ChatCompletionRequest();
        request.setMax_tokens(chatCompletionConfig.getMaxTokens());
        request.setModel(chatCompletionConfig.getModel());
        request.setTop_p(chatCompletionConfig.getTopP());
        request.setMessages(buildMessages(naturalCallDialog, chatCompletionConfig.getSystemPrompt()));
        request.setTemperature(chatCompletionConfig.getTemperature());
        if (chatCompletionConfig.isJsonFormat()) {
            Map<String, String> responseFormatMap = Maps.newHashMap();
            responseFormatMap.put("type", "json_object");
            request.setResponse_format(responseFormatMap);
        }
        return request;
    }

    private List<FridayMessage> buildMessages(String naturalCallDialog, String systemPrompt) {
        List<FridayMessage> fridayMessages = Lists.newArrayList();
        FridayMessage systemMessage = new FridayMessage();
        systemMessage.setContent(systemPrompt);
        systemMessage.setRole(FridayMessageRoleEnum.SYSTEM.getValue());
        fridayMessages.add(systemMessage);

        FridayMessage userMessage = new FridayMessage();
        userMessage.setContent(naturalCallDialog);
        userMessage.setRole(FridayMessageRoleEnum.USER.getValue());
        fridayMessages.add(userMessage);
        return fridayMessages;
    }
}
