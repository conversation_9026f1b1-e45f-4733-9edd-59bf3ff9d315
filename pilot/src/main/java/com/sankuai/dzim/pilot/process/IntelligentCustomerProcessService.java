package com.sankuai.dzim.pilot.process;

import com.sankuai.dzim.pilot.gateway.mq.data.DelayMessageData;
import com.sankuai.dzim.pilot.process.data.LoadMessageLatestRecReplyReq;
import com.sankuai.dzim.pilot.process.data.RecommendRecordData;
import com.sankuai.dzim.pilot.process.data.req.IntelligentCustomerReq;
import com.sankuai.dzim.pilot.utils.data.Response;

/**
 * 智能客服处理流程层，包含智能客服的读写操作
 */
public interface IntelligentCustomerProcessService {

    /**
     * 生成LLM回复并推送
     * @param request
     * @return
     */
     Response<Boolean> loadLLMAnswerAndPush(IntelligentCustomerReq request);

    /**
     * 查询消息最新的推荐回复
     * @return
     */
     Response<RecommendRecordData> loadMessageLatestRecommendReply(LoadMessageLatestRecReplyReq request);

    /**
    * 是否需要发送用户沉默唤醒消息
    * @param userAwakeDelayMessage 用户沉默唤醒延迟消息
    * @return 是否需要发送
    */
    boolean needSendUserAwakeMessage(DelayMessageData userAwakeDelayMessage);

    /**
     * 发送用户沉默唤醒消息并记录缓存
     * @param userAwakeDelayMessage 用户沉默唤醒延迟消息
     * @return 是否成功
     */
    boolean sendUserAwakeMessage(DelayMessageData userAwakeDelayMessage);
}
