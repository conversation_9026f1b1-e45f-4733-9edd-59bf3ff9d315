package com.sankuai.dzim.pilot.dal.entity;

import lombok.Data;

import java.util.Date;

@Data
public class MerchantKnowledgeEntity {

    /**
     * 主键Id
     */
    private long id;

    /**
     * 类型
     * @see com.sankuai.dzim.pilot.api.enums.MerchantKnowledgeTypeEnum
     */
    private int type;

    /**
     * 主体Id,商品/门店
     */
    private String subjectId;

    /**
     * 对应向量数据的主键Id
     */
    private long vectorId;

    /**
     * 问题文本
     */
    private String question;

    /**
     * 答案文本
     */
    private String answer;

    /**
     * 编辑者
     */
    private String editor;

    /**
     * 与query的相似度
     */
    private double similarity;

    /**
     * 添加时间
     */
    private Date addTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除
     */
    private int isDelete;
}
