package com.sankuai.dzim.pilot.scene.task;

import com.sankuai.dzim.pilot.chain.data.AIAnswerData;
import com.sankuai.dzim.pilot.scene.task.data.TaskContext;
import com.sankuai.dzim.pilot.scene.task.data.TaskResult;
import com.sankuai.dzim.pilot.scene.task.data.TaskTypeEnum;
import org.springframework.stereotype.Component;

/**
 * 服务零售Agent(for beam)
 */
@Component
public class BeamFWSLAgentTask extends CommonTask implements Task{
    @Override
    public boolean accept(TaskContext context) {
        return TaskTypeEnum.BEAM_FWLS_AGENT_TASK.getType().equals(context.getTaskType());
    }

    @Override
    public TaskResult execute(TaskContext context) {
        if (context == null || context.getTaskConfig() == null) {
            return TaskResult.builder().build();
        }
        AIAnswerData answerData = getAIAnswerData(context);
        return TaskResult.builder().aiAnswerData(answerData).build();
    }

    @Override
    public void afterExecute(TaskResult result) {

    }
}
