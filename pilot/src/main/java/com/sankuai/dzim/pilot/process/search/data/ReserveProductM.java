package com.sankuai.dzim.pilot.process.search.data;

import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Objects;

@Data
public class ReserveProductM {

    /**
     * 综业务商品id
     */
    private Integer bizProductId;

    /**
     * 美团平台商品id
     */
    private Long platformProductId;

    /**
     * 泛商品id
     */
    private List<Long> skuIds;

    /**
     * 扩展属性(value是对象)
     */
    private List<ObjAttrM> extObjAttrs;

    /**
     * 商品名称
     */
    private String title;

    /**
     * 商品头图
     */
    private String picUrl;

    /**
     * 到手价
     */
    private String salePriceTag;

    /**
     * 商品副标题
     */
    private List<String> productTags;

    /**
     * 商品销量
     */
    private ProductSaleM sale;

    /**
     * 商品拼场信息列表
     */
    private List<ProductPinPoolM> pinPools;

    /**
     * 扩展属性
     */
    private List<AttrM> extAttrs;

    /**
     * 子商品列表
     */
    private List<ProductItemM> productItemMList;

    /**
     * 商品优惠信息
     */
    private List<ProductPromoPriceM> promoPrices;

    /**
     * 商品状态信息
     */
    private ProductBaseStateM baseState;

    /**
     * 商品关联门店
     */
    private List<ShopM> shopMs;

    public String getAttr(String attrName) {
        if (CollectionUtils.isEmpty(this.getExtAttrs())) {
            return null;
        }
        AttrM attrM = this.getExtAttrs().stream()
                .filter(Objects::nonNull)
                .filter(attr -> attr.getName().equals(attrName))
                .findFirst()
                .orElse(null);
        return attrM == null ? null : attrM.getValue();
    }

    /**
     * 填充属性值
     *
     */
    public void setAttr(String attrName, String attrValue) {
        if (CollectionUtils.isEmpty(this.getExtAttrs())) {
            this.setExtAttrs(Lists.newArrayList(new AttrM(attrName, attrValue)));
            return;
        }
        this.getExtAttrs().add(new AttrM(attrName, attrValue));
    }

    public void setObjAttr(String attrName, Object attrValue) {
        if (CollectionUtils.isEmpty(this.getExtObjAttrs())) {
            this.setExtObjAttrs(Lists.newArrayList(new ObjAttrM(attrName, attrValue)));
            return;
        }
        this.getExtObjAttrs().add(new ObjAttrM(attrName, attrValue));
    }

    public <T> T getObjAttr(String attrName) {
        if (CollectionUtils.isEmpty(this.getExtObjAttrs())) {
            return null;
        }
        ObjAttrM attrM = this.getExtObjAttrs().stream()
                .filter(Objects::nonNull)
                .filter(attr -> attr.getName().equals(attrName))
                .findFirst()
                .orElse(null);
        if (attrM == null) {
            return null;
        }
        return (T) attrM.getValue();
    }
}
