package com.sankuai.dzim.pilot.utils;

import com.dianping.cat.Cat;
import com.dianping.lion.client.util.MurmurHash;
import com.meituan.inf.xmdlog.ConfigUtil;

import java.util.Random;
import java.util.UUID;

/**
 * 搜索queryId 工具类
 * <AUTHOR>
 */
public final class SearchTraceUtil {

    public static String buildQueryId(String ip) {
        return hash(ip);
    }

    public static String buildGlobalId(String ip) {
        return hash(ip);
    }

    /**
     * ip + 时间戳 + 随机数 取绝对值
     * @param ip ip
     * @return hash
     */
    public static String hash(String ip){
        try {
            long ts = System.currentTimeMillis();
            Random ran = new Random();
            int rd = ran.nextInt();
            String k = ip.replace(".","") + ts + rd;
            long hash = Math.abs(MurmurHash.hash64A(k.getBytes(),64));
            return Long.toString(hash);
        } catch (Exception e) {
            Cat.logError(e);
            return ConfigUtil.getTraceID();
        }
    }

    public static String buildQueryId() {
        UUID id = UUID.randomUUID();
        return id.toString();
    }
}
