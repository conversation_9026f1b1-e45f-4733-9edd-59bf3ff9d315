package com.sankuai.dzim.pilot.gateway.mq.consumer;

import co.elastic.clients.elasticsearch._types.query_dsl.TermQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.TermsQuery;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import com.dianping.cat.Cat;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mdp.boot.starter.mafka.consumer.anno.MdpMafkaMsgReceive;
import com.sankuai.dzim.pilot.domain.EsVectorDomainService;
import com.sankuai.dzim.pilot.domain.data.EmbeddingConfig;
import com.sankuai.dzim.pilot.domain.data.EsVectorInsertResult;
import com.sankuai.dzim.pilot.domain.impl.DuplicateCheckService;
import com.sankuai.dzim.pilot.gateway.mq.data.ReviewData;
import com.sankuai.dzim.pilot.utils.LionConfigUtil;
import com.sankuai.meituan.poros.client.PorosApiClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

@Service("review.data.to.es.vector.consumer")
@Slf4j
public class ReviewDataToEsVectorConsumer {

    @Resource
    private DuplicateCheckService duplicateCheckService;

    @Resource
    private EsVectorDomainService esVectorDomainService;

    @Resource
    private LionConfigUtil lionConfigUtil;

    @MdpMafkaMsgReceive
    public ConsumeStatus receive(List<String> reviewDataJsonList) {
        if (CollectionUtils.isEmpty(reviewDataJsonList)) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        List<ReviewData> reviewDataList = reviewDataJsonList.parallelStream().map(json -> JsonCodec.decode(json, ReviewData.class)).collect(Collectors.toList());
        EsVectorInsertResult esVectorInsertResult = esVectorDomainService.bulkInsert(reviewDataList, buildEmbeddingConfig(), data -> duplicateCheckService.checkReviewDataDuplicate(data));
        if (!esVectorInsertResult.isSuccess()) {
            Cat.logEvent("EmbeddingFailCnt", String.valueOf(CollectionUtils.emptyIfNull(esVectorInsertResult.getFailInsertDataList()).size()));
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }

    private EmbeddingConfig buildEmbeddingConfig() {
        EmbeddingConfig embeddingConfig = new EmbeddingConfig();
        embeddingConfig.setAppId(lionConfigUtil.getReviewEmbeddingAppId());
        embeddingConfig.setModel("text-embedding-ada-002");
        return embeddingConfig;
    }


}
