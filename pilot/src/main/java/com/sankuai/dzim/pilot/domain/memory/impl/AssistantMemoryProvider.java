package com.sankuai.dzim.pilot.domain.memory.impl;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.sankuai.dzim.message.dto.MessageDTO;
import com.sankuai.dzim.pilot.api.enums.assistant.MessageInputSourceEnum;
import com.sankuai.dzim.pilot.api.enums.assistant.MessageSendDirectionEnum;
import com.sankuai.dzim.pilot.api.enums.assistant.MessageTypeEnum;
import com.sankuai.dzim.pilot.buffer.enums.StreamEventCardTypeEnum;
import com.sankuai.dzim.pilot.buffer.enums.StreamEventDataTypeEnum;
import com.sankuai.dzim.pilot.buffer.enums.StreamEventTypeEnum;
import com.sankuai.dzim.pilot.buffer.stream.build.ext.CardBuildExt;
import com.sankuai.dzim.pilot.buffer.stream.vo.StreamEventCardDataVO;
import com.sankuai.dzim.pilot.buffer.stream.vo.StreamEventDataVO;
import com.sankuai.dzim.pilot.buffer.stream.vo.StreamEventVO;
import com.sankuai.dzim.pilot.chain.data.AIAnswerData;
import com.sankuai.dzim.pilot.dal.entity.pilot.PilotChatMessageEntity;
import com.sankuai.dzim.pilot.dal.pilotdao.PilotChatGroupMessageDAO;
import com.sankuai.dzim.pilot.domain.annotation.Memory;
import com.sankuai.dzim.pilot.domain.memory.MemoryProvider;
import com.sankuai.dzim.pilot.domain.memory.data.QueryMemoryContext;
import com.sankuai.dzim.pilot.domain.message.AssistantMessage;
import com.sankuai.dzim.pilot.domain.message.Message;
import com.sankuai.dzim.pilot.domain.message.PluginCall;
import com.sankuai.dzim.pilot.domain.message.PluginResultMessage;
import com.sankuai.dzim.pilot.domain.message.SystemMessage;
import com.sankuai.dzim.pilot.domain.message.UserMessage;
import com.sankuai.dzim.pilot.scene.data.AssistantExtraConstant;
import com.sankuai.dzim.pilot.scene.data.AssistantSceneContext;
import com.sankuai.dzim.pilot.utils.TimeUtil;
import com.sankuai.dzim.pilot.utils.context.RequestContext;
import com.sankuai.dzim.pilot.utils.context.RequestContextConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Component
@Slf4j
@Memory(name = "AssistantMemory", desc = "助手记忆查询")
public class AssistantMemoryProvider implements MemoryProvider {

    private static List<Integer> ACCEPT_MESSAGE_TYPE = Lists.newArrayList(MessageTypeEnum.TEXT.value, MessageTypeEnum.CARD.value);

    @Autowired
    private PilotChatGroupMessageDAO chatGroupMessageDAO;

    @Autowired
    private List<CardBuildExt> cardBuildExts;

    @Override
    public List<Message> getMemory(QueryMemoryContext context) {
        if (context.getChatGroupId() <= 0) {
            return Lists.newArrayList();
        }

        List<PilotChatMessageEntity> pilotChatMessageEntities = chatGroupMessageDAO.multiGetLatestMessages(context.getChatGroupId(), context.getLimit());
        //时间间隔过滤
        List<PilotChatMessageEntity> oldChatMessageEntities = Lists.newArrayList();

        Date timeLimit = TimeUtil.getSpecificTime(-context.getInterval());
        for(PilotChatMessageEntity pilotChatMessageEntity : pilotChatMessageEntities){
            if(pilotChatMessageEntity.getAddTime().before(timeLimit)){
                oldChatMessageEntities.add(pilotChatMessageEntity);
            }
        }
        pilotChatMessageEntities.removeAll(oldChatMessageEntities);

        if (CollectionUtils.isEmpty(pilotChatMessageEntities)) {
            return Lists.newArrayList();
        }

        // 如果问题是重新生成的，删除前面聊天记录中重复的问答
        removeRepeatQuestionAndAnswer(pilotChatMessageEntities);

        //todo 先简单处理,后续再建设message拼装通用逻辑
        Long chatGroupStartId = pilotChatMessageEntities.stream().filter(message -> message.getMessageType() == MessageTypeEnum.TIPS.value).map(PilotChatMessageEntity::getId).max(Comparator.comparing(Long::longValue)).orElse(0L);
        pilotChatMessageEntities = pilotChatMessageEntities.stream().filter(message -> message.getId() > chatGroupStartId).collect(Collectors.toList());
        pilotChatMessageEntities.sort(Comparator.comparing(PilotChatMessageEntity::getId));
        List<Message> messages = Lists.newArrayList();
        for (PilotChatMessageEntity message : pilotChatMessageEntities) {
            if (!ACCEPT_MESSAGE_TYPE.contains(message.getMessageType())) {
                continue;
            }
            if (message.getMessageType() == MessageTypeEnum.CARD.value) {
                Message flowMessage = buildFlowMessage(message);
                if (flowMessage != null) {
                    messages.add(flowMessage);
                }
                continue;
            }
            messages.add(getModelMessage(message, message.getMessage()));
        }
        return messages;
    }

    private Message getPluginMessage(String message) {
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            JsonNode rootNode = objectMapper.readTree(message);

            String content = rootNode.get("content").asText();
            String pluginName = rootNode.get("pluginName").asText();
            String pluginCallId = rootNode.get("pluginCallId").asText();

            PluginResultMessage resultMessage = new PluginResultMessage(pluginCallId, pluginName, content);
            if (StringUtils.isEmpty(resultMessage.getContent())) {
                return null;
            }

            AIAnswerData aiAnswerData = JsonCodec.decode(content, AIAnswerData.class);
            if (aiAnswerData == null) {
                return null;
            }
            resultMessage.setContent(aiAnswerData.getAnswer());
            return resultMessage;
        } catch (Exception e) {
            log.error("get PluginMessage error, message: {}.", message, e);
            return null;
        }

    }

    private Message getAssistantMessage(String message) {
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            JsonNode rootNode = objectMapper.readTree(message);

            JsonNode contentNode = rootNode.get("content");
            JsonNode reasonContentNode = rootNode.get("reasonContent");
            JsonNode pluginCallsNode = rootNode.get("pluginCalls");

            List<PluginCall> pluginCalls = new ArrayList<>();
            if (pluginCallsNode.isArray()) {
                Iterator<JsonNode> elements = pluginCallsNode.elements();
                while (elements.hasNext()) {
                    JsonNode pluginCallNode = elements.next();
                    PluginCall pluginCall = new PluginCall();
                    pluginCall.setId(pluginCallNode.get("id").asText());
                    pluginCall.setPluginName(pluginCallNode.get("pluginName").asText());
                    pluginCall.setArguments(pluginCallNode.get("arguments"));
                    pluginCalls.add(pluginCall);
                }
            }

            String content = contentNode.isNull() ? null : contentNode.asText();
            String reasonContent = reasonContentNode.isNull() ? null : reasonContentNode.asText();

            AssistantMessage assistantMessage = new AssistantMessage(content, reasonContent);
            assistantMessage.setPluginCalls(pluginCalls);
            return assistantMessage;
        } catch (Exception e) {
            log.error("get assistantMessage error, message: {}.", message, e);
            return null;
        }
    }

    private void removeRepeatQuestionAndAnswer(List<PilotChatMessageEntity> pilotChatMessageEntities) {
        AssistantSceneContext context = RequestContext.getAttribute(RequestContextConstants.ASSISTANT_CONTEXT);
        if (context == null) {
            return;
        }
        if (MapUtils.isEmpty(context.getExtra()) || context.getExtra().get(AssistantExtraConstant.MESSAGE_INPUT_SOURCE) == null) {
            return;
        }
        int inputSource = NumberUtils.toInt(context.getExtra().get(AssistantExtraConstant.MESSAGE_INPUT_SOURCE).toString());
        if (inputSource != MessageInputSourceEnum.REPEAT_GENERATE.getType()) {
            return;
        }
        MessageDTO currentUserMessage = context.getMessageDTO();
        long mimRepeatMessageId = pilotChatMessageEntities.stream()
                .filter(message -> message.getDirection() == MessageSendDirectionEnum.CLIENT_SEND.value)
                .filter(message -> message.getMessage().equals(currentUserMessage.getMessage()))
                .mapToLong(PilotChatMessageEntity::getMessageId).min().orElse(0L);
        if (mimRepeatMessageId <= 0) {
            return;
        }
        pilotChatMessageEntities.removeIf(message -> message.getMessageId() >= mimRepeatMessageId);
    }

    private Message getModelMessage(PilotChatMessageEntity messageEntity, String message) {
        if (messageEntity.getDirection() == MessageSendDirectionEnum.CLIENT_SEND.value) {
            return UserMessage.build(message);
        }
        if (messageEntity.getDirection() == MessageSendDirectionEnum.ASSISTANT_SEND.value) {
            return AssistantMessage.build(message);
        }
        if (messageEntity.getDirection() == MessageSendDirectionEnum.SYSTEM_SEND.value) {
            return SystemMessage.build(message);
        }
        // 暂时先不要工具调用
//        if (messageEntity.getMessageType() == MessageTypeEnum.TOOL_CALL.value || messageEntity.getDirection() == MessageSendDirectionEnum.TOOL_CALL.value) {
//            return messageEntity.getCreator().equals(AssistantConstant.ASSISTANT_CREATOR) ? getAssistantMessage(message) : getPluginMessage(message);
//        }

        return AssistantMessage.build(message);
    }

    private Message buildFlowMessage(PilotChatMessageEntity message) {
        String content = message.getMessage();
        List<StreamEventVO> streamEventVOS = JsonCodec.converseList(content, StreamEventVO.class);
        if (CollectionUtils.isEmpty(streamEventVOS)) {
            return null;
        }

        streamEventVOS = streamEventVOS.stream().filter(Objects::nonNull)
                .filter(streamEventVO -> streamEventVO.getType().equals(StreamEventTypeEnum.MESSAGE.getType()))
                .filter(streamEventVO -> streamEventVO.getData().getEvent().equals(StreamEventDataTypeEnum.MAIN_TEXT.getType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(streamEventVOS)) {
            return null;
        }

        StringBuilder sb = new StringBuilder();
        for (StreamEventVO streamEventVO : streamEventVOS) {
            StreamEventDataVO data = streamEventVO.getData();
            if (CollectionUtils.isEmpty(data.getCardsData())) {
                sb.append(data.getContent());
                continue;
            }
            // 处理标签
            String validContent = handleSpecialTag(data);

            // 处理跳链
            validContent = handleJumpUrl(validContent);
            sb.append(validContent);
        }

        return getModelMessage(message, sb.toString());
    }

    private static String handleJumpUrl(String validContent) {
        String urlReg = "\\[([^]]+)]\\(([^)]+)\\)";
        Pattern pattern = Pattern.compile(urlReg);
        Matcher matcher = pattern.matcher(validContent);

        while (matcher.find()) {
            // 获取匹配的链接文本和链接地址
            String linkText = matcher.group(1);
            validContent = validContent.replace(matcher.group(0), "**" + linkText + "**");
        }

        return validContent;
    }

    private String handleSpecialTag(StreamEventDataVO data) {
        if (CollectionUtils.isEmpty(data.getCardsData())) {
            return data.getContent();
        }

        String content = data.getContent();
        String tagTemplate = "<%s>%s</%s>";
        for (StreamEventCardDataVO cardDataVO : data.getCardsData()) {
            try {
                if (cardDataVO == null) {
                    continue;
                }
                String tag = String.format(tagTemplate, cardDataVO.getType(), cardDataVO.getKey(), cardDataVO.getType());
                // 卡片类型不在assistant memory展示
                if (StreamEventCardTypeEnum.isNotDisplayInAssistantMemoryCardType(cardDataVO.getType())) {
                    content = content.replace(tag, "");
                }

                // 卡片解析成ai友好的内容
                for (CardBuildExt cardBuildExt : cardBuildExts) {
                    if (cardBuildExt.accept(cardDataVO) && MapUtils.isNotEmpty(cardDataVO.getCardProps())) {
                        String decodeContent = cardBuildExt.cardDecode(cardDataVO);
                        content = content.replace(tag, decodeContent);
                    }
                }
            } catch (Exception e) {
                log.error("Assistant Memory handleSpecialTag error, StreamEventDataVO: {}.", data, e);
            }
        }
        return content.replace(":::{", "").replace("}:::", "").replace(":::}", "").replace("{:::", "");
    }

}