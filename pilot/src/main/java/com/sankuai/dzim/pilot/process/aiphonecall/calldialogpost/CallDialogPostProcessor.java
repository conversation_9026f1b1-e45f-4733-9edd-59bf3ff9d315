package com.sankuai.dzim.pilot.process.aiphonecall.calldialogpost;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.dzim.pilot.dal.entity.aiphonecall.AIPhoneCallDetailEntity;
import com.sankuai.dzim.pilot.dal.entity.aiphonecall.AIPhoneCallRecordEntity;
import com.sankuai.dzim.pilot.dal.entity.aiphonecall.AIPhoneCallTaskEntity;
import com.sankuai.dzim.pilot.dal.pilotdao.aiphonecall.AIPhoneCallDetailDAO;
import com.sankuai.dzim.pilot.dal.pilotdao.aiphonecall.AIPhoneCallRecordDAO;
import com.sankuai.dzim.pilot.dal.pilotdao.aiphonecall.AIPhoneCallTaskDAO;
import com.sankuai.dzim.pilot.enums.AIPhoneCallSceneTypeEnum;
import com.sankuai.dzim.pilot.process.aiphonecall.data.AIPhoneCallDialogPostContext;
import com.sankuai.dzim.pilot.process.aiphonecall.data.CallDialogMessage;
import com.sankuai.dzim.pilot.process.aiphonecall.data.CallDialogRawData;
import com.sankuai.dzim.pilot.utils.LionConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class CallDialogPostProcessor implements InitializingBean, ApplicationContextAware {


    @Autowired
    private ApplicationContext applicationContext;

    private final Map<String, CallDialogPostExtPt> callDialogPostHashMap = new HashMap<>();

    @Autowired
    private LionConfigUtil lionConfigUtil;

    @Resource
    private AIPhoneCallDetailDAO callDetailDAO;

    @Resource
    private AIPhoneCallRecordDAO callRecordDAO;

    @Resource
    private AIPhoneCallTaskDAO callTaskDAO;

    @Override
    public void afterPropertiesSet() throws Exception {
        // 在所有属性设置之后，加载插件
        Map<String, Object> pluginBeans = applicationContext.getBeansWithAnnotation(CallDialogPost.class);
        for (Object bean : pluginBeans.values()) {
            CallDialogPost pluginAnnotation = bean.getClass().getAnnotation(CallDialogPost.class);
            callDialogPostHashMap.put(pluginAnnotation.name(), (CallDialogPostExtPt) bean);
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    public void handleCallDialogPost(long callDetailId) {
        try {
            // 查询通话内容
            AIPhoneCallDialogPostContext context = buildCallDialogPostContext(callDetailId);

            // 根据场景获取通话内容后置处理器
            List<String> callDialogPostExtNames = MapUtils.emptyIfNull(lionConfigUtil.getAiPhoneCallDialogPostConfig()).get(String.valueOf(context.getAiPhoneCallSceneTypeEnum().getType()));
            for (String postExtName : CollectionUtils.emptyIfNull(callDialogPostExtNames)) {
                CallDialogPostExtPt postExtPt = this.callDialogPostHashMap.get(postExtName);
                if (postExtPt == null) {
                    continue;
                }
                postExtPt.handle(context);
            }
        } catch (Exception e) {
            log.error("handleCallDialogPost error, detailId = {}", callDetailId, e);
        }
    }

    private AIPhoneCallDialogPostContext buildCallDialogPostContext(long callDetailId) {
        AIPhoneCallDetailEntity callDetailEntity = callDetailDAO.getById(callDetailId);
        AIPhoneCallRecordEntity callRecordEntity = callRecordDAO.getByContactId(callDetailEntity.getContactId());
        AIPhoneCallTaskEntity callTaskEntity = callTaskDAO.getByTaskId(callDetailEntity.getTaskId());

        AIPhoneCallDialogPostContext context = new AIPhoneCallDialogPostContext();
        context.setDetailId(callDetailEntity.getId());
        context.setTaskId(callTaskEntity.getId());
        context.setShopId(callDetailEntity.getShopId());
        context.setPlatform(callDetailEntity.getPlatform());
        context.setAiPhoneCallSceneTypeEnum(AIPhoneCallSceneTypeEnum.getByType(callTaskEntity.getSceneType()));
        context.setCallDialog(convertNaturalDialog(callRecordEntity.getDialogRawData()));
        return context;
    }

    private String convertNaturalDialog(String dialogRawData) {
        CallDialogRawData callDialogRawData = JsonCodec.decode(dialogRawData, CallDialogRawData.class);
        if (callDialogRawData == null || CollectionUtils.isEmpty(callDialogRawData.getHistory())) {
            return StringUtils.EMPTY;
        }

        StringBuffer callDialog = new StringBuffer(StringUtils.EMPTY);
        for (CallDialogMessage callDialogMessage: callDialogRawData.getHistory()) {
            if (callDialogMessage == null || StringUtils.isEmpty(callDialogMessage.getMessage())) {
                continue;
            }

            if (callDialogMessage.getSource().equals("user")) {
                callDialog.append("商家：" + callDialogMessage.getMessage() + "\n");
            }
            if (callDialogMessage.getSource().equals("machine")) {
                callDialog.append("AI助手：" + callDialogMessage.getMessage() + "\n");
            }
        }
        return callDialog.toString();
    }
}
