package com.sankuai.dzim.pilot.process.data;

import com.sankuai.it.xcontract.easypoi.annotation.Excel;
import lombok.Data;

/**
 * @author: zhouyibing
 * @date: 2024/7/18
 */
@Data
public class GenerativeSearchWordImportData {

    @Excel(name = "bizType")
    private Integer bizType;

    @Excel(name = "question")
    private String question;

    @Excel(name = "templateType")
    private Integer templateType;

    @Excel(name = "noteIds")
    private String noteIds;

    @Excel(name = "noteCount")
    private Integer noteCount;

    @Excel(name = "keyword")
    private String keyword;

    @Excel(name = "score")
    private Integer score;

    @Excel(name = "synonymKeyword")
    private String synonymKeyword;

    @Excel(name = "descriptiveWord")
    private String descriptiveWord;

    @Excel(name = "synonymDescriptiveWord")
    private String synonymDescriptiveWord;

    @Excel(name = "sourceType")
    private Integer sourceType;

    @Excel(name = "sourceBizID")
    private String sourceBizID;

    @Excel(name = "buildCateFromNoteIds")
    private Boolean buildCateFromNoteIds;

}
