package com.sankuai.dzim.pilot.process.router.impl;

import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.dzim.pilot.api.data.search.generative.TabInfoDTO;
import com.sankuai.dzim.pilot.process.data.TemplateBaseInfo;
import com.sankuai.dzim.pilot.process.router.data.RouteResponse;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2024/10/12 10:05
 */
@Component
public class CommonTemplateDataProcess {
    @ConfigValue(
            key = "com.sankuai.mim.pilot.template.base.config",
            defaultValue = "{\"2\":{\"headInfo\":{\"titleInfo\":{\"title\":\"根据症状选项目\",\"icon\":\"默认-icon\",\"link\":\"默认-link\"},\"moreInfo\":{\"title\":\"更多\",\"icon\":\"默认-更多-icon\",\"link\":\"默认-更多-link\"}},\"themeInfo\":{},\"relatedQuestionBaseLink\":\"相关问题跳链\",\"projectTabBaseLink\":\"项目跳链\"}}"
    )
    private Map<Integer, TemplateBaseInfo> bizType2TemplateBaseInfo;

    @ConfigValue(key = "com.sankuai.mim.pilot.card.project.random.config", defaultValue = "{\"6_酒吧\": \"true\"}")
    private Map<String, Boolean> bizTypeQuestion2shouldRandomProjectTab;

    private static final String ALL = "全部";

    /**
     * 1. 去除项目词中标题为”全部“的项目
     * 2. 去除搜索词
     * 3. 填充项目tab的跳链(如果为空)
     * 
     * @param projectTabs
     * @param bizType
     */
    public void fillProjectTabsLinkAndRemoveSearchWord(List<TabInfoDTO.ProjectTabDTO> projectTabs, int bizType) {
        // 去除掉下面的项目词tab的搜索词，并在链接为空的情况下拼接跳链
        Optional.ofNullable(projectTabs).ifPresent(
                tabs -> tabs.stream().filter(projectTab -> !ALL.equals(projectTab.getTitle())).forEach(projectTab -> {
                    if (StringUtils.isBlank(projectTab.getLink())) {
                        projectTab.setLink("");
                        Optional.ofNullable(bizType2TemplateBaseInfo.get(bizType))
                                .map(TemplateBaseInfo::getProjectTabBaseLink).ifPresent(baseLink -> {
                                    if (StringUtils.isNotBlank(baseLink)) {
                                        String keyWord = StringUtils.isNotBlank(projectTab.getSearchWord())
                                                ? projectTab.getSearchWord() : projectTab.getTitle();
                                        projectTab.setLink(baseLink + keyWord);
                                    }
                                });
                    }
                }));
    }

    public void randomProjectTabIfNeed(RouteResponse routeResponse) {
        Optional.ofNullable(routeResponse)
                .filter(response -> Optional.ofNullable(response.getQuestion())
                        .map(question -> bizTypeQuestion2shouldRandomProjectTab
                                .getOrDefault(response.getBizType() + "_" + question, false)).orElse(false))
                .map(RouteResponse::getSuggestionAnswerDTO)
                .flatMap(suggestionAnswerDTO -> Optional.ofNullable(suggestionAnswerDTO.getTemplateDataDTO()))
                .flatMap(templateDataDTO -> Optional.ofNullable(templateDataDTO.getTabInfo()))
                .ifPresent(tabInfo -> tabInfo.forEach(tab -> {
                    Optional.ofNullable(tab.getOuterProjectTabs()).ifPresent(Collections::shuffle);
                    Optional.ofNullable(tab.getInnerProjectTabs()).ifPresent(Collections::shuffle);
                }));
    }
}
