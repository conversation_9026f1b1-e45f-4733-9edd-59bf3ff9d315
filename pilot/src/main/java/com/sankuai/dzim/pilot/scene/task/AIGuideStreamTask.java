package com.sankuai.dzim.pilot.scene.task;

import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.api.data.search.generative.QueryAnswerRequest;
import com.sankuai.dzim.pilot.chain.data.AIAnswerData;
import com.sankuai.dzim.pilot.domain.GenerativeSearchDomainService;
import com.sankuai.dzim.pilot.domain.data.GenerativeSearchE;
import com.sankuai.dzim.pilot.scene.task.data.TaskContext;
import com.sankuai.dzim.pilot.scene.task.data.TaskResult;
import com.sankuai.dzim.pilot.scene.task.data.TaskTypeEnum;
import com.sankuai.dzim.pilot.utils.LionConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2024/09/18 14:15
 */
@Component
@Slf4j
public class AIGuideStreamTask extends CommonTask implements Task {

    @Autowired
    private LionConfigUtil lionConfigUtil;

    @Autowired
    private GenerativeSearchDomainService generativeSearchDomainService;

    @Override
    public boolean accept(TaskContext context) {
        return TaskTypeEnum.AI_GUIDE_STREAM_TASK.getType().equals(context.getTaskType());
    }

    @Override
    public TaskResult execute(TaskContext context) {
        TaskResult sensitiveWordsResult = handleSensitiveWords(context);
        if (sensitiveWordsResult != null) {
            return sensitiveWordsResult;
        }
        // 根据数据库数据查找答案
        TaskResult dbResult = queryAnswerFromDB(context);
        if (dbResult != null) {
            return dbResult;
        }
        // 调用chain层获取AIAnswerData
        AIAnswerData aiAnswerData = getAIAnswerData(context);
        return TaskResult.builder().taskContext(context).aiAnswerData(aiAnswerData).build();
    }

    private TaskResult queryAnswerFromDB(TaskContext context) {
        QueryAnswerRequest request = buildQueryAnswerRequest(context);
        if (request == null) {
            return null;
        }
        try {
            // 查数据库模板数据
            GenerativeSearchE generativeSearchE = generativeSearchDomainService.getGenerativeSearchAnswer(request);
            String answer = Optional.ofNullable(generativeSearchE).map(GenerativeSearchE::getAnswer).filter(StringUtils::isNotBlank)
                    .orElse(null);
            return buildTextResult(context, answer);
        }catch (Exception e){
            LogUtils.logFailLog(log, TagContext.builder().action("queryAnswerFromDB:error").userId(context.getUserId()).build(),
                    new WarnMessage("queryAnswerFromDB", "从数据库查询模板数据失败", null), request, e);
            return null;
        }
    }

    private QueryAnswerRequest buildQueryAnswerRequest(TaskContext context) {
        String message = context.getMessageDTO().getMessage();
        if (StringUtils.isEmpty(message)) {
            return null;
        }
        Integer assistantType = context.getAssistantType();
        Map<Integer, Integer> bizType2AssistantType = lionConfigUtil.getBizType2AssistantType();
        for (Map.Entry<Integer, Integer> entry : bizType2AssistantType.entrySet()) {
            if (entry.getValue().equals(assistantType)) {
                return QueryAnswerRequest.builder().bizType(entry.getKey()).userType(1)
                        .userId(Long.valueOf(context.getUserId().substring(1))).question(message).build();
            }
        }
        return null;
    }

    @Override
    public void afterExecute(TaskResult result) {

    }
}