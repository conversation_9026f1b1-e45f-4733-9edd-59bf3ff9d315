package com.sankuai.dzim.pilot.process.search.data;

import lombok.Data;

import java.util.List;

@Data
public class ProductPinPoolGroupM {

    /**
     * 长订单号
     */
    private String unifiedOrderId;

    /**
     * 拼场组描述
     */
    private String description;

    /**
     * 拼场订单来源。0：普通线上订单；1：线下订单；2：线下转线上订单（处理逻辑同普通线上订单）
     */
    private int orderFrom;

    /**
     * 玩家数量
     */
    private int playerCount;

    /**
     * 男性数量
     */
    private int manCount;

    /**
     * 女性数量
     */
    private int womanCount;

    /**
     * 拼场组内用户
     */
    private List<ProductUserM> users;
}
