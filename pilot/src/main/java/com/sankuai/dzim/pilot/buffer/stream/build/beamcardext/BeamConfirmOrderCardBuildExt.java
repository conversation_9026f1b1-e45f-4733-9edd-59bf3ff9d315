package com.sankuai.dzim.pilot.buffer.stream.build.beamcardext;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.frog.sdk.util.CollectionUtils;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.dzim.pilot.buffer.core.PilotBufferItemDO;
import com.sankuai.dzim.pilot.buffer.enums.StreamEventCardTypeEnum;
import com.sankuai.dzim.pilot.buffer.stream.build.beamcardext.data.BeamCardBuildContext;
import com.sankuai.dzim.pilot.buffer.stream.build.ext.data.BeamDealOrderPreviewData;
import com.sankuai.dzim.pilot.buffer.stream.vo.BeamChoiceVO;
import com.sankuai.dzim.pilot.enums.BeamResourceTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringEscapeUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/12
 */
@Component
@Slf4j
public class BeamConfirmOrderCardBuildExt implements BeamCardBuildExt {

    @Override
    public boolean acceptByStreamCardType(String streamCardType) {
        return StreamEventCardTypeEnum.BEAM_DEAL_ORDER_PREVIEW.getType().equals(streamCardType);
    }

    @Override
    public boolean acceptByBeamResourceType(String resourceType) {
        return BeamResourceTypeEnum.FWLS_DEAL_PREVIEW_ORDER.getKey().equals(resourceType);
    }

    @Override
    public List<BeamChoiceVO.DeltaResource> buildResources(BeamCardBuildContext context) {
        List<BeamChoiceVO.DeltaResource> deltaResources = Lists.newArrayList();

        List<PilotBufferItemDO> tagItems = context.getTagItems();
        if (CollectionUtils.isEmpty(tagItems)) {
            return Collections.emptyList();
        }

        PilotBufferItemDO itemDO = tagItems.get(0);
        BeamDealOrderPreviewData orderCardData = JsonCodec.decode(JsonCodec.encodeWithUTF8(itemDO.getExtra()), BeamDealOrderPreviewData.class);
        BeamChoiceVO.DeltaResource deltaResource = buildDeltaResource(orderCardData);
        deltaResources.add(deltaResource);
        return deltaResources;
    }

    @Override
    public String cardDecode(String cardContent) {
        cardContent = StringEscapeUtils.unescapeJava(cardContent);
        JSONObject jsonObject = JSONObject.parseObject(cardContent);
        String previewId = jsonObject.getString("previewId");
        Long productId = jsonObject.getLong("productId");
        String productName = jsonObject.getString("productName");
        Integer quantity = jsonObject.getInteger("quantity");

        StringBuilder sb = new StringBuilder();
        sb.append("\n【交易预览卡片】\n");
        sb.append("预览Id(previewId)：").append(previewId).append("\n");
        sb.append("商品Id(productId)：").append(productId).append("\n");
        sb.append("商品名称(productName)：").append(productName).append("\n");
        sb.append("商品数量(quantity)：").append(quantity).append("\n");
        sb.append("商品类型(productType)：").append("团购").append("\n");

        return sb.toString();
    }

    private BeamChoiceVO.DeltaResource buildDeltaResource(BeamDealOrderPreviewData orderPreviewData) {
        BeamChoiceVO.DeltaResource deltaResource = new BeamChoiceVO.DeltaResource();
        deltaResource.setResource_index(orderPreviewData.getPreviewId());
        deltaResource.setResource_type(BeamResourceTypeEnum.FWLS_DEAL_PREVIEW_ORDER.getKey());
        BeamChoiceVO.ResourceMetaData metaData = new BeamChoiceVO.ResourceMetaData();
        metaData.setBiz_data(JSON.toJSONString(orderPreviewData));
        deltaResource.setMetadata(metaData);
        return deltaResource;
    }
}
