package com.sankuai.dzim.pilot.acl.impl;

import com.alibaba.fastjson.JSONObject;
import com.dianping.appkit.constants.BizGroup;
import com.dianping.degrade.util.JsonCodec;
import com.dianping.haima.client.HaimaClient;
import com.dianping.haima.client.request.HaimaRequest;
import com.dianping.haima.client.response.HaimaResponse;
import com.dianping.haima.entity.haima.HaimaConfig;
import com.dianping.haima.entity.haima.HaimaContent;
import com.google.common.collect.Maps;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.acl.HaimaAclService;
import com.sankuai.dzim.pilot.acl.data.haima.FuzzySkipWordsConfig;
import com.sankuai.dzim.pilot.acl.data.haima.PilotHaimaConfig;
import com.sankuai.dzim.pilot.process.aireservebook.data.ReserveItemData;
import com.sankuai.dzim.pilot.process.aireservebook.data.StandardReserveItem;
import com.sankuai.dzim.pilot.process.data.AIServiceConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @since 2024/12/18 17:41
 */
@Service
@Slf4j
public class HaimaAclServiceImpl implements HaimaAclService {

    @Autowired
    private HaimaClient haimaClient;

    private static final String FUZZY_SKIP_WORDS_CONFIG_NAME = "模糊匹配黑名单";

    private static final String FUZZY_QUESTION_MAPPING_CONFIG_NAME = "模糊匹配问题映射";

    private static final String BACK_CAT_ID_FIELD = "backCategoryId";

    private static final String POI_STANDARD_RESERVE_ITEMS_SCENE = "standard_reserve_elements";

    private static final String BEAM_POI_STANDARD_RESERVE_ITEMS_SCENE = "beam_standard_reserve_elements";

    private static final String RESERVE_ITEM_KEY = "reserveItemKey";

    private static final String RESERVE_ITEM_NAME = "reserveItemName";

    private static final String RESERVE_IS_MUST_NEED = "isMustNeed";

    @Override
    public HaimaResponse queryConfig(String sceneKey) {
        HaimaRequest haimaRequest = new HaimaRequest();
        haimaRequest.setBizGroup(BizGroup.DIANPING);
        haimaRequest.setSceneKey(sceneKey);
        return haimaClient.query(haimaRequest);
    }

    @Override
    public List<String> queryFuzzySkipWords(int bizType) {
        HaimaResponse haimaResponse = null;
        try {
            haimaResponse = queryConfig("pilot");
            if (!haimaResponse.isSuccess() || CollectionUtils.isEmpty(haimaResponse.getData())) {
                return Collections.emptyList();
            }

            for (HaimaConfig haimaConfig : haimaResponse.getData()) {
                PilotHaimaConfig pilotHaimaConfig = JsonCodec.decode(haimaConfig.getExtJson(), PilotHaimaConfig.class);
                if (FUZZY_SKIP_WORDS_CONFIG_NAME.equals(pilotHaimaConfig.getName())) {
                    return haimaConfig.getContents()
                                      .stream()
                                      .map(content -> JsonCodec.decode(content.getExtJson(), FuzzySkipWordsConfig.class))
                                      .filter(config -> config.getBizType() == bizType)
                                      .findFirst()
                                      .map(FuzzySkipWordsConfig::getValue).orElse(Lists.emptyList());
                }
            }
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("queryFuzzySkipWords").build(),
                                new WarnMessage("HaimaAclServiceImpl", "查询模糊匹配黑名单失败", null), bizType, haimaResponse, e);
        }
        return Collections.emptyList();
    }

    @Override
    public String queryQuestionMapping(Integer bizType, String question) {
        HaimaResponse haimaResponse = null;
        try {
            haimaResponse = queryConfig("pilot");
            if (!haimaResponse.isSuccess() || CollectionUtils.isEmpty(haimaResponse.getData())) {
                return question;
            }

            for (HaimaConfig haimaConfig : haimaResponse.getData()) {
                PilotHaimaConfig pilotHaimaConfig = JsonCodec.decode(haimaConfig.getExtJson(), PilotHaimaConfig.class);
                if (FUZZY_QUESTION_MAPPING_CONFIG_NAME.equals(pilotHaimaConfig.getName())) {
                    Map<String, Set<String>> map = haimaConfig.getContents().stream()
                                                              .map(content -> JsonCodec.decode(content.getExtJson(), FuzzySkipWordsConfig.class))
                                                              .filter(config -> config.getBizType().equals(bizType))
                                                              .findFirst()
                                                              .map(FuzzySkipWordsConfig::getMap).orElse(Maps.newHashMap());
                    for (Map.Entry<String, Set<String>> entry : map.entrySet()) {
                        if (entry.getValue().contains(question)) {
                            return entry.getKey();
                        }
                    }
                }
            }
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("queryQuestionMapping").build(),
                                new WarnMessage("HaimaAclServiceImpl", "查询模糊匹配映射失败", null), bizType + question, haimaResponse, e);
        }
        return question;
    }

    /**
     * 查询AI模型配置
     * @param key AI模型唯一标识
     * @return
     */
    @Override
    public AIServiceConfig queryAIConfig(String key) {
        if (StringUtils.isBlank(key)) {
            return null;
        }
        HaimaResponse haimaResponse = null;
        try {
            haimaResponse = queryConfig("pilot_ai_config");
            if (!haimaResponse.isSuccess() || CollectionUtils.isEmpty(haimaResponse.getData())) {
                return null;
            }

            for (HaimaConfig haimaConfig : haimaResponse.getData()) {
                for (HaimaContent content : haimaConfig.getContents()) {
                    JSONObject configObject = JSONObject.parseObject(content.getExtJson());
                    if (key.equals(configObject.getString("key"))) {
                        AIServiceConfig config = JsonCodec.decode(configObject.getString("config"), AIServiceConfig.class);
                        String prompt = configObject.getString("prompt");
                        if (config != null) {
                            if (StringUtils.isNotBlank(prompt)) {
                                config.setSystemPrompt(prompt);
                            }
                            return config;
                        }
                    }
                }
            }
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("queryAIConfig").build(),
                                new WarnMessage("HaimaAclServiceImpl", "查询AI模型配置失败", null), key, haimaResponse, e);
        }
        return null;
    }

    @Override
    public List<StandardReserveItem> queryStandardReserveItems(Integer backCategory) {
        HaimaResponse haimaResponse = null;
        try {
            haimaResponse = queryConfigWithBackCates(POI_STANDARD_RESERVE_ITEMS_SCENE, Lists.newArrayList(backCategory));
            if (!haimaResponse.isSuccess() || CollectionUtils.isEmpty(haimaResponse.getData())) {
                return null;
            }
            HaimaConfig haimaConfig = haimaResponse.getData().get(0);
            if (CollectionUtils.isEmpty(haimaConfig.getContents())) {
                return null;
            }
            List<StandardReserveItem> result = Lists.newArrayList();
            for (HaimaContent haimaContent : haimaConfig.getContents()) {
                if (haimaContent == null) {
                    continue;
                }
                StandardReserveItem item = new StandardReserveItem();
                item.setKey(haimaContent.getContentString(RESERVE_ITEM_KEY));
                item.setName(haimaContent.getContentString(RESERVE_ITEM_NAME));
                item.setMustNeed(Boolean.valueOf(Optional.ofNullable(haimaContent.getContentString(RESERVE_IS_MUST_NEED)).orElse("false")));
                result.add(item);
            }
            return result;
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("queryStandardReserveItems").build(),
                                new WarnMessage("HaimaAclServiceImpl", "查询行业标准预约要素失败", null), backCategory, haimaResponse, e);
        }
        return null;
    }

    @Override
    public List<StandardReserveItem> queryBeamStandardReserveItems(Integer backCategory) {
        HaimaResponse haimaResponse = null;
        try {
            haimaResponse = queryConfigWithBackCates(BEAM_POI_STANDARD_RESERVE_ITEMS_SCENE, Lists.newArrayList(backCategory));
            if (!haimaResponse.isSuccess() || CollectionUtils.isEmpty(haimaResponse.getData())) {
                return null;
            }
            HaimaConfig haimaConfig = haimaResponse.getData().get(0);
            if (CollectionUtils.isEmpty(haimaConfig.getContents())) {
                return null;
            }
            List<StandardReserveItem> result = Lists.newArrayList();
            for (HaimaContent haimaContent : haimaConfig.getContents()) {
                if (haimaContent == null) {
                    continue;
                }
                StandardReserveItem item = new StandardReserveItem();
                item.setKey(haimaContent.getContentString(RESERVE_ITEM_KEY));
                item.setName(haimaContent.getContentString(RESERVE_ITEM_NAME));
                item.setMustNeed(Boolean.valueOf(Optional.ofNullable(haimaContent.getContentString(RESERVE_IS_MUST_NEED)).orElse("false")));
                result.add(item);
            }
            return result;
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("queryBeamStandardReserveItems").build(),
                    new WarnMessage("HaimaAclServiceImpl", "查询Beam行业标准预约要素失败", null), backCategory, haimaResponse, e);
        }
        return null;
    }

    private HaimaResponse queryConfigWithBackCates(String sceneKey, List<Integer> backCates) {
        HaimaRequest haimaRequest = new HaimaRequest();
        haimaRequest.setBizGroup(BizGroup.DIANPING);
        haimaRequest.setSceneKey(sceneKey);
        if (CollectionUtils.isNotEmpty(backCates)) {
            // 后台类目需要子类目在前，父类目在后。需要对类目倒排
            haimaRequest.addField(BACK_CAT_ID_FIELD, convertList2Str(reverseList(backCates)));
        }
        return haimaClient.query(haimaRequest);
    }

    private List<Integer> reverseList(List<Integer> source) {
        if (CollectionUtils.isEmpty(source)) {
            return source;
        }
        return IntStream.range(0, source.size())
                .mapToObj(i -> source.get(source.size() - i - 1))
                .collect(Collectors.toList());
    }

    private String convertList2Str(List<Integer> list) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(list)) {
            return "";
        }
        return StringUtils.join(list, ",");
    }


}
