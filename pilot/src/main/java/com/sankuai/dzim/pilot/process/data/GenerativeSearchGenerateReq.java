package com.sankuai.dzim.pilot.process.data;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class GenerativeSearchGenerateReq {

    /**
     * 业务类型
     */
    private int bizType;

    /**
     * 问题类型
     * @see QuestionTypeEnum
     */
    private int questionType;

    /**
     * 问题
     */
    private String question;

    /**
     * 答案ID,用于项目sug更新使用
     */
    private long answerId;

    /**
     * 筛选标签列表
     */
    private String tags;

}
