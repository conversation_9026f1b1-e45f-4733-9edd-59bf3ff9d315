package com.sankuai.dzim.pilot.scene.task;

import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.dzim.pilot.api.data.AIAnswerTypeEnum;
import com.sankuai.dzim.pilot.buffer.core.PilotBufferItemDO;
import com.sankuai.dzim.pilot.buffer.utils.BufferUtils;
import com.sankuai.dzim.pilot.chain.AIService;
import com.sankuai.dzim.pilot.chain.data.AIAnswerData;
import com.sankuai.dzim.pilot.chain.data.AIServiceContext;
import com.sankuai.dzim.pilot.process.data.AIServiceConfig;
import com.sankuai.dzim.pilot.scene.task.data.TaskContext;
import com.sankuai.dzim.pilot.scene.task.data.TaskResult;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * task类需要的公共方法
 *
 * @author: zhouyibing
 * @date: 2024/8/1
 */
@Component
public class CommonTask {

    @Autowired
    private List<AIService> aiServices;

    @ConfigValue(key = "com.sankuai.mim.pilot.beauty.ask.reject.keywords", defaultValue = "[]")
    private List<String> rejectKeywords;

    @ConfigValue(key = "com.sankuai.mim.pilot.beauty.ask.reject.answers", defaultValue = "{}")
    private Map<Integer, String> assistantType2RejectAnswer;

    private static final String defaultRejectAnswer = "这个问题我还在学习，可以继续问我其他生活相关问题哦";

    public AIAnswerData getAIAnswerData(TaskContext context) {
        AIServiceConfig aiServiceConfig = context.getTaskConfig().getAiServiceConfig();
        AIService aiService = getAIService(aiServiceConfig.getAiServiceType());
        if (aiService == null) {
            return null;
        }
        AIServiceContext aiServiceContext = buildAIServiceContext(context, aiServiceConfig);

        return aiService.execute(aiServiceContext);
    }

    public AIServiceContext buildAIServiceContext(TaskContext context, AIServiceConfig config) {
        AIServiceContext aIServiceContext = new AIServiceContext();
        aIServiceContext.setSceneCode(buildAssistantSceneCode(context));
        aIServiceContext.setMessageDTO(context.getMessageDTO());
        aIServiceContext.setMemoryComponents(config.getMemoryComponents());
        aIServiceContext.setQueryUnderstandComponents(config.getQueryUnderstandComponents());
        aIServiceContext.setRetrievalComponents(config.getRetrievalComponents());
        aIServiceContext.setPluginNames(config.getPluginNames());
        aIServiceContext.setSystemPrompt(config.getSystemPrompt());
        aIServiceContext.setModel(config.getModel());
        aIServiceContext.setAppId(config.getAppId());
        aIServiceContext.setTemperature(config.getTemperature());
        aIServiceContext.setTopP(config.getTopP());
        aIServiceContext.setIsJsonModel(config.getIsJsonModel());
        aIServiceContext.setStream(config.isStream());
        aIServiceContext.setExtraInfo(context.getExtra());
        aIServiceContext.setRetrievalMap(config.getRetrievalMap());
        aIServiceContext.setMaxTokens(config.getMaxTokens());
        aIServiceContext.setToolChoice(config.getToolChoice());
        return aIServiceContext;
    }

    private int buildAssistantSceneCode(TaskContext context) {
        return context.getAssistantType() > 0 ? NumberUtils.toInt("100" + context.getAssistantType()) : 0;
    }

    /**
     * 检测敏感词，如果有敏感词则返回拒绝回答
     *
     * @param context
     * @return
     */
    public TaskResult handleSensitiveWords(TaskContext context) {
        if (context == null || context.getMessageDTO() == null) {
            return null;
        }
        String rejectAnswer = hitKeyword(context.getMessageDTO().getMessage(), context.getAssistantType());
        return buildTextResult(context, rejectAnswer);
    }

    /**
     * 构建文本任务返回结果
     *
     * @param context
     * @param answer
     * @return
     */
    public TaskResult buildTextResult(TaskContext context, String answer) {
        if (StringUtils.isBlank(answer)) {
            return null;
        }
        AIAnswerData aiAnswerData = new AIAnswerData();
        aiAnswerData.setAiAnswerType(AIAnswerTypeEnum.TEXT.getType());
        aiAnswerData.setAnswer(answer);
        BufferUtils.writeMainTextBuffer(PilotBufferItemDO.builder().data(answer).build());
        return TaskResult.builder().taskContext(context).aiAnswerData(aiAnswerData).build();
    }

    private String hitKeyword(String userQuestion, int assistantType) {
        if (CollectionUtils.isEmpty(rejectKeywords)) {
            return StringUtils.EMPTY;
        }
        if (rejectKeywords.stream().anyMatch(userQuestion::contains)) {
            return assistantType2RejectAnswer.getOrDefault(assistantType, defaultRejectAnswer);
        }
        return StringUtils.EMPTY;
    }

    public AIService getAIService(int aiServiceType) {
        for (AIService aiService : aiServices) {
            if (aiService.accept(aiServiceType)) {
                return aiService;
            }
        }
        return null;
    }
}
