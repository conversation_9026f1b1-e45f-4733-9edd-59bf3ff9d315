package com.sankuai.dzim.pilot.process.localplugin;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.meituan.nibtp.trade.client.combine.bean.WebParamDTO;
import com.meituan.nibtp.trade.client.combine.requset.AgentRefundApplyOrderReqDTO;
import com.meituan.nibtp.trade.client.combine.requset.AgentRefundApplyReqDTO;
import com.meituan.nibtp.trade.client.combine.response.AgentRefundApplyResDTO;
import com.sankuai.dzim.pilot.acl.AgentTradeAclService;
import com.sankuai.dzim.pilot.api.data.AIAnswerTypeEnum;
import com.sankuai.dzim.pilot.buffer.core.PilotBufferItemDO;
import com.sankuai.dzim.pilot.buffer.stream.build.ext.data.FwslDealOrder;
import com.sankuai.dzim.pilot.buffer.utils.BufferUtils;
import com.sankuai.dzim.pilot.chain.data.AIAnswerData;
import com.sankuai.dzim.pilot.domain.annotation.LocalPlugin;
import com.sankuai.dzim.pilot.gateway.api.assistant.data.SendBeamMessageReq;
import com.sankuai.dzim.pilot.process.localplugin.param.BeamRefundOrderParam;
import com.sankuai.dzim.pilot.utils.PluginContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Beam订单退款工具
 *
 * <AUTHOR>
 * @date 2025/4/29
 */
@Component
@Slf4j
public class BeamRefundOrderPlugin {

    @Resource
    private AgentTradeAclService agentTradeAclService;

    @LocalPlugin(name = "RefundOrderTool", description = "退款工具，帮用户完成退款操作", returnDirect = true)
    public AIAnswerData getBeamRefundOrderAnswer(BeamRefundOrderParam param) {
        log.info("RefundOrderTool invoke param: {}", JsonCodec.encodeWithUTF8(param));
        SendBeamMessageReq sendBeamMessageReq = PluginContextUtil.getBeamRequestContext(param);
        fillOrderId(param, sendBeamMessageReq);
        if (param.getOrderId() == null || param.getOrderId() == 0) {
            BufferUtils.writeMainTextBuffer(PilotBufferItemDO.builder().data("订单不存在").build());
            return new AIAnswerData(AIAnswerTypeEnum.TEXT.getType(), "订单不存在");
        }

        AIAnswerData answerData = new AIAnswerData();
        answerData.setAiAnswerType(AIAnswerTypeEnum.TEXT.getType());
        answerData.setAnswer("退款失败，请稍后重试～");

        AgentRefundApplyReqDTO agentRefundApplyReqDTO = buildAgentRefundApplyReqDTO(param, sendBeamMessageReq);
        if (agentRefundApplyReqDTO == null) {
            BufferUtils.writeMainTextBuffer(PilotBufferItemDO.builder().data("退款失败，请稍后重试～").build());
            return answerData;
        }

        AgentRefundApplyResDTO agentRefundApplyResDTO = agentTradeAclService.refundApply(agentRefundApplyReqDTO);
        if (agentRefundApplyResDTO == null || agentRefundApplyResDTO.isFail()) {
            log.error("调用退款工具 agentTradeAclService.refundApply 失败");
            String failTip = agentRefundApplyResDTO == null? "退款失败，请稍候重试～": "退款失败，" + agentRefundApplyResDTO.getMessage();
            BufferUtils.writeMainTextBuffer(PilotBufferItemDO.builder().data(failTip).build());
            return answerData;
        }

        BufferUtils.writeMainTextBuffer(PilotBufferItemDO.builder().data("退款成功").build());
        return answerData;
    }

    private void fillOrderId(BeamRefundOrderParam param, SendBeamMessageReq sendBeamMessageReq) {
        if (param.getOrderId() != null && param.getOrderId() > 0) {
            return;
        }

        // 从beam上下文中提取订单id
        if (sendBeamMessageReq.getContext() == null || sendBeamMessageReq.getContext().getBusiness_data() == null) {
            return;
        }
        FwslDealOrder fwslDealOrder = getFwlsDealOrder(sendBeamMessageReq.getContext().getBusiness_data());
        if (fwslDealOrder == null || fwslDealOrder.getOrderList() == null) {
            return;
        }
        List<Long> orderIdList = fwslDealOrder.getOrderList().stream().map(FwslDealOrder.OrderInfo::getOrder_id)
                .filter(Objects::nonNull)
                .map(Long::parseLong)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderIdList)) {
            return;
        }
        param.setOrderId(orderIdList.get(0));
    }

    private AgentRefundApplyReqDTO buildAgentRefundApplyReqDTO(BeamRefundOrderParam param, SendBeamMessageReq sendBeamMessageReq) {
        if (sendBeamMessageReq == null || sendBeamMessageReq.getUser_info() == null || sendBeamMessageReq.getUser_info().getUserid() == null) {
            return null;
        }
        WebParamDTO webParamDTO = agentTradeAclService.buildWebParam(sendBeamMessageReq);
        if (webParamDTO == null) {
            return null;
        }

        AgentRefundApplyReqDTO agentRefundApplyReqDTO = new AgentRefundApplyReqDTO();
        AgentRefundApplyOrderReqDTO agentRefundApplyOrderReqDTO = new AgentRefundApplyOrderReqDTO();
        agentRefundApplyOrderReqDTO.setOrderId(param.getOrderId());
        agentRefundApplyOrderReqDTO.setCount(param.getCount());
        agentRefundApplyReqDTO.setWebParam(webParamDTO);
        agentRefundApplyReqDTO.setOrderList(Lists.newArrayList(agentRefundApplyOrderReqDTO));
        return agentRefundApplyReqDTO;
    }

    private FwslDealOrder getFwlsDealOrder(JSONObject businessData) {
        if (businessData == null) {
            return null;
        }
        String fwslDealOrder = businessData.getString("fwls_deal_order");
        if (fwslDealOrder == null) {
            return null;
        }
        return JSON.parseObject(fwslDealOrder, FwslDealOrder.class);
    }

}
