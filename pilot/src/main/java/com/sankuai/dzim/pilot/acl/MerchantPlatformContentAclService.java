package com.sankuai.dzim.pilot.acl;

import com.sankuai.mpmctcontent.query.thrift.dto.search.SearchDataDetailDTO;
import com.sankuai.mpmctcontent.query.thrift.dto.search.SearchDetailResponseDTO;

import java.util.concurrent.CompletableFuture;

/**
 * 商家平台内容相关领域，如查询宴会厅、超值特惠等定制化的商家信息
 */
public interface MerchantPlatformContentAclService {

    /**
     * 查询门店拥有的宴会厅或者菜单信息
     * searchScene决定是查询宴会厅还是菜单
     * @param dpShopId
     * @return
     */
    CompletableFuture<SearchDetailResponseDTO> findShopContents(long dpShopId, String searchScene, int page, int pageSize);

    /**
     * 查询单个商家素材内容，如宴会厅详细信息等
     * @param infoId
     * @return
     */
    CompletableFuture<SearchDataDetailDTO> loadShopSingleContentInfo(long infoId, String searchScene);
}
