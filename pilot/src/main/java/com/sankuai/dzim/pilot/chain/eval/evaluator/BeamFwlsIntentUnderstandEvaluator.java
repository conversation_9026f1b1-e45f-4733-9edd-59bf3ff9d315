package com.sankuai.dzim.pilot.chain.eval.evaluator;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.TextNode;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.chain.eval.data.EvalCase;
import com.sankuai.dzim.pilot.chain.eval.data.EvalCaseResult;
import com.sankuai.dzim.pilot.chain.eval.data.EvalContext;
import com.sankuai.dzim.pilot.domain.message.PluginCall;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.Iterator;
import java.util.List;

/**
 * Beam服务零售子Ahent意图理解评测器
 *
 */
@Component
@Slf4j
public class BeamFwlsIntentUnderstandEvaluator implements Evaluator {

    private final ObjectMapper objectMapper = new ObjectMapper();


    @Override
    public boolean accept(String evaluator) {
        return this.getClass().getSimpleName().equals(evaluator);
    }

    @Override
    public EvalCaseResult measure(EvalContext evalContext, EvalCase evalCase) {
        List<PluginCall> targetCalls = evalCase.getPluginCalls();
        List<PluginCall> actualCalls = parsePluginCall(evalCase.getOutput());

        if (CollectionUtils.isEmpty(targetCalls)) {
            throw new RuntimeException("目标工具调用为空");
        }
        if (CollectionUtils.isEmpty(actualCalls)) {
            throw new RuntimeException("实际工具调用为空");
        }

        PluginCall targetCall = targetCalls.get(0);
        PluginCall actualCall = actualCalls.get(0);

        // 工具准确率
        boolean toolAccuracy = evaluateToolAccuracy(targetCall, actualCall);
        // 参数准确率，工具名和工具参数需要完全相等
        boolean paramAccuracy = evaluateParamAccuracy(targetCall, actualCall);
        // 整体准确率
        boolean functionCallingAccuracy = toolAccuracy && paramAccuracy;

        // 组装结果
        EvalCaseResult evalCaseResult = new EvalCaseResult();
        BeanUtils.copyProperties(evalCase, evalCaseResult);
        evalCaseResult.setFunctionCallingAccuracy(functionCallingAccuracy);
        evalCaseResult.setOutput(StringUtils.defaultString(evalCaseResult.getOutput()));
        evalCaseResult.setOutput(evalCaseResult.getOutput().substring(0, Math.min(10000, evalCaseResult.getOutput().length())));
        return evalCaseResult;
    }

    private List<PluginCall> parsePluginCall(String json) {
        try {
            return objectMapper.readValue(json, new TypeReference<List<PluginCall>>() {});
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("parsePluginCall").build(),
                    new WarnMessage("FunctionCallingEvaluator", "解析模型回复异常", null), json, null, e);
            return null;
        }
    }

    private boolean evaluateToolAccuracy(PluginCall target, PluginCall actual) {
        return target.getPluginName().equals(actual.getPluginName());
    }

    private boolean evaluateParamAccuracy(PluginCall target, PluginCall actual) {
        JsonNode targetArguments = target.getArguments();
        JsonNode actualArguments = actual.getArguments();
        if (targetArguments == null) {
            return true;
        }
        if (actualArguments == null) {
            return false;
        }

        try {
            return excuteEvaluate(targetArguments, actualArguments);
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("evaluateParamAccuracy").build(),
                    new WarnMessage("FunctionCallingEvaluator", "参数对比异常", null), actual, null, e);
            return false;
        }
    }

    private boolean excuteEvaluate(JsonNode targetArguments, JsonNode actualArguments) {
        // 检查所有key是否匹配
        Iterator<String> targetFields = targetArguments.fieldNames();
        while (targetFields.hasNext()) {
            String key = targetFields.next();
            JsonNode targetValue = targetArguments.get(key);
            if ("0/null".equals(targetValue.asText()) || "1/null".equals(targetValue.asText())) {
                if (!actualArguments.has(key)) {
                    continue;
                }

                JsonNode actualValue = actualArguments.get(key);
                if(!targetValue.asText().contains(actualValue.asText())) {
                    return false;
                }
            } else {
                // 比较每个key对应的值
                if (!targetArguments.get(key).asText().equals(JSONObject.parseObject(((TextNode) actualArguments).textValue()).getString(key))) {
                    return false;
                }
            }
        }

        return true;
    }
}
