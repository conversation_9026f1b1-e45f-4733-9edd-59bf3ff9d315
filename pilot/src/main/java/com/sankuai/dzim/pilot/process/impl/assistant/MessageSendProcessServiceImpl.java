package com.sankuai.dzim.pilot.process.impl.assistant;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.dianping.zebra.group.router.ZebraForceMasterHelper;
import com.google.common.collect.Maps;
import com.meituan.mafka.client.bean.MafkaProducer;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.message.common.utils.ImAccountTypeUtils;
import com.sankuai.dzim.message.dto.AuditResDTO;
import com.sankuai.dzim.message.enums.AuditStatusEnum;
import com.sankuai.dzim.pilot.acl.AuditAclService;
import com.sankuai.dzim.pilot.api.enums.assistant.AssistantTypeEnum;
import com.sankuai.dzim.pilot.api.enums.assistant.MessageInputSourceEnum;
import com.sankuai.dzim.pilot.api.enums.assistant.MessageSendDirectionEnum;
import com.sankuai.dzim.pilot.api.enums.assistant.MessageTypeEnum;
import com.sankuai.dzim.pilot.buffer.enums.StreamEventDataTypeEnum;
import com.sankuai.dzim.pilot.buffer.enums.StreamEventTypeEnum;
import com.sankuai.dzim.pilot.buffer.stream.vo.BufferMergedVO;
import com.sankuai.dzim.pilot.buffer.stream.vo.StreamEventDataVO;
import com.sankuai.dzim.pilot.buffer.stream.vo.StreamEventVO;
import com.sankuai.dzim.pilot.dal.entity.pilot.BlockListEntity;
import com.sankuai.dzim.pilot.dal.entity.pilot.PilotChatGroupEntity;
import com.sankuai.dzim.pilot.dal.entity.pilot.PilotChatMessageEntity;
import com.sankuai.dzim.pilot.dal.pilotdao.PilotChatGroupDAO;
import com.sankuai.dzim.pilot.dal.pilotdao.PilotChatGroupMessageDAO;
import com.sankuai.dzim.pilot.domain.RiskUserBlockDomainService;
import com.sankuai.dzim.pilot.enums.BlockStatusEnum;
import com.sankuai.dzim.pilot.gateway.api.assistant.data.SendBeamMessageReq;
import com.sankuai.dzim.pilot.gateway.api.assistant.data.SendMessageReq;
import com.sankuai.dzim.pilot.gateway.api.assistant.data.SseAwareException;
import com.sankuai.dzim.pilot.gateway.api.vo.ChatGroupExpVO;
import com.sankuai.dzim.pilot.gateway.mq.data.RiskBlockMessageData;
import com.sankuai.dzim.pilot.process.AssistantMessageCacheProcessService;
import com.sankuai.dzim.pilot.process.MessageAuditProcessService;
import com.sankuai.dzim.pilot.process.MessageSendProcessService;
import com.sankuai.dzim.pilot.process.PilotChatGroupProcessService;
import com.sankuai.dzim.pilot.process.PilotMessageBizRefProcessService;
import com.sankuai.dzim.pilot.process.data.AssistantConstant;
import com.sankuai.dzim.pilot.process.data.AssistantMessageCacheKeyData;
import com.sankuai.dzim.pilot.process.data.AuditResult;
import com.sankuai.dzim.pilot.process.data.BlockWhiteListConfig;
import com.sankuai.dzim.pilot.process.data.bizref.MessageBizRefData;
import com.sankuai.dzim.pilot.process.localplugin.param.ShopDetailParam;
import com.sankuai.dzim.pilot.scene.DefaultAssistantScene;
import com.sankuai.dzim.pilot.scene.data.AssistantSceneRequest;
import com.sankuai.dzim.pilot.utils.*;
import com.sankuai.dzim.pilot.utils.context.RequestContext;
import com.sankuai.dzim.pilot.utils.context.RequestContextConstants;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @since 2024/7/31 17:23
 */
@Slf4j
@Service
public class MessageSendProcessServiceImpl implements MessageSendProcessService {

    @Autowired
    private DefaultAssistantScene defaultAssistantScene;

    @Autowired
    private IDGenerateUtil IDGenerateUtil;

    @Autowired
    private AuditAclService auditAclService;

    @Resource
    private PilotChatGroupDAO pilotChatGroupDAO;

    @Resource
    private PilotChatGroupMessageDAO pilotChatGroupMessageDAO;

    @Resource
    private AssistantMessageCacheProcessService assistantMessageCacheAclService;

    @Autowired
    private LionConfigUtil lionConfigUtil;

    @Autowired
    private MafkaProducer sendMessageProducer;

    @Autowired
    private RiskUserBlockDomainService riskUserBlockDomainService;

    @Autowired
    private PilotMessageBizRefProcessService pilotMessageBizRefProcessService;

    @Resource
    private PilotChatGroupProcessService chatGroupProcessService;

    @ConfigValue(key = "com.sankuai.mim.pilot.message.pre.send.config", defaultValue = "[14]")
    private List<Integer> preSendAssistantTypes;

    @Autowired
    private MessageAuditProcessService messageAuditProcessService;

    final String AUDIT_FAIL_MESSAGE_KEY = "auditFailMessage";

    @Override
    public boolean sendMessage(SendMessageReq sendMessageReq) {
        try {
            // 查拉黑表 查是否在白名单里
            if (!checkIfUserIsBlocked(sendMessageReq)) {
                LogUtils.logFailLog(log, TagContext.builder().action("sendMessage").build(),
                        new WarnMessage("sendMessage", "用户被拉黑",""), sendMessageReq, false);
                return false;
            }

            metricMessageSendCnt(sendMessageReq.getAssistantType());
            SseEmitter sseEmitter = RequestContext.getAttribute(RequestContextConstants.SSE_EMITTER);
            if (sseEmitter == null) {
                return false;
            }
            String imUserId = sendMessageReq.getImUserId();
            long userMessageId = IDGenerateUtil.nextMessageId();
            if (userMessageId <= 0) {
                return false;
            }
            AuditResDTO audit = auditAclService.audit(userMessageId, imUserId, sendMessageReq.getAssistantType(), sendMessageReq.getMessage());
            metricAuditSucc(sendMessageReq.getAssistantType());

            // 用户发送
            PilotChatMessageEntity userMessage = null;
            // 大模型回复
            PilotChatMessageEntity replyMessage = null;
            if (audit != null && audit.isPass()) {
                Pair<PilotChatMessageEntity, PilotChatMessageEntity> userReplyMessages = sendMessageAfterAudit(sseEmitter, imUserId, sendMessageReq, userMessageId);
                userMessage = userReplyMessages.getKey();
                replyMessage = userReplyMessages.getValue();
            }

            if (audit == null || !audit.isPass()) {
                sendSseContent(sseEmitter, StreamEventTypeEnum.ERROR.getType(), "auditFailed", "审核失败");
                LogUtils.logFailLog(log, TagContext.builder().action("sendMessage").build(),
                        new WarnMessage("messageAudit", "发送消息审核失败", ""), sendMessageReq, audit);
            }
            // 风险用户拉黑
            ProducerUtils.sendRiskUserBlockMessage(sendMessageProducer, buildRiskBlackMessageData(sendMessageReq, audit, userMessage, replyMessage));
            return true;
        } catch (SseAwareException e) {
            LogUtils.logFailLog(log, TagContext.builder().action("sendMessage").build(),
                    new WarnMessage("MessageSendProcessService", "发送消息流程异常", ""), sendMessageReq, e);
            throw e;
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("sendMessage").build(),
                    new WarnMessage("MessageSendProcessService", "发送消息流程异常", ""), sendMessageReq, e);
            return false;
        }
    }

    @Override
    public boolean sendBeamMessage(SendBeamMessageReq sendBeamMessageReq) {
        try {
            metricMessageSendCnt(AssistantTypeEnum.BEAM_FWLS_AGENT.getType());
            SseEmitter sseEmitter = RequestContext.getAttribute(RequestContextConstants.SSE_EMITTER);
            if (sseEmitter == null) {
                return false;
            }

            String imUserId = ImAccountTypeUtils.getImMtUserId(NumberUtils.toLong(sendBeamMessageReq.getUser_info().getUserid()));
            long userMessageId = IDGenerateUtil.nextMessageId();
            if (userMessageId <= 0) {
                return false;
            }
            metricMessageSendSucc(AssistantTypeEnum.BEAM_FWLS_AGENT.getType());

            // 发送消息
            sendBeamMessageInternal(sseEmitter, imUserId, sendBeamMessageReq, userMessageId);
            return true;
        } catch (SseAwareException e) {
            LogUtils.logFailLog(log, TagContext.builder().action("sendMessage").build(),
                    new WarnMessage("MessageSendProcessService", "发送消息流程异常", ""), sendBeamMessageReq, e);
            throw e;
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("sendMessage").build(),
                    new WarnMessage("MessageSendProcessService", "发送消息流程异常", ""), sendBeamMessageReq, e);
            return false;
        }
    }

    private Pair<PilotChatMessageEntity, PilotChatMessageEntity> sendBeamMessageInternal(SseEmitter sseEmitter,
                                                                                         String imUserId, SendBeamMessageReq sendBeamMessageReq, long userMessageId) {
        int assistantType = AssistantTypeEnum.BEAM_FWLS_AGENT.getType();
        // 查询会话
        PilotChatGroupEntity chatGroupEntity = chatGroupProcessService.getOrCreateChatGroup(imUserId, assistantType);
        if (chatGroupEntity == null) {
            LogUtils.logFailLog(log, TagContext.builder().action("sendBeamMessage").build(),
                    new WarnMessage("sendBeamMessageInternal", "查询会话异常", imUserId), sendBeamMessageReq, null);
            return new Pair<>(null, null);
        }

        //大模型回复
        BufferMergedVO bufferMergedVO = defaultAssistantScene.process(AssistantSceneRequest.builder()
                .assistantType(assistantType).userId(imUserId)
                .question(sendBeamMessageReq.getOriginal_user_input().getContent()).messageId(userMessageId)
                .chatGroupId(chatGroupEntity.getId()).beamRequestContext(sendBeamMessageReq).build());
        List<StreamEventVO> reply = bufferMergedVO.getStreamEventVOs();
        int messageType = bufferMergedVO.getMessageType();

        long replyMessageId = IDGenerateUtil.nextMessageId();
        if (CollectionUtils.isEmpty(reply) || replyMessageId <= 0) {
            LogUtils.logFailLog(log, TagContext.builder().action("sendMessage").build(),
                    new WarnMessage("replyMessageError", "模型生成错误", String.valueOf(replyMessageId)), sendBeamMessageReq, reply);
            return new Pair<>(null, null);
        }

        //更新会话信息
        updateChatGroup(chatGroupEntity.getId(), userMessageId);
        //插入用户消息
        PilotChatMessageEntity userMessage = buildPilotChatMessageEntity(userMessageId, MessageTypeEnum.TEXT.value,
                chatGroupEntity.getId(), assistantType, imUserId, MessageSendDirectionEnum.CLIENT_SEND.value,
                AuditStatusEnum.PASS.getCode(), sendBeamMessageReq.getOriginal_user_input().getContent(), imUserId, buildExtra(sendBeamMessageReq));
        pilotChatGroupMessageDAO.insertMessage(userMessage);

        //插入模型消息
        PilotChatMessageEntity replyMessageEntity = buildPilotChatMessageEntity(replyMessageId, messageType,
                chatGroupEntity.getId(), assistantType, imUserId,
                MessageSendDirectionEnum.ASSISTANT_SEND.value, AuditStatusEnum.PASS.getCode(),
                JsonCodec.encodeWithUTF8(reply), AssistantConstant.ASSISTANT_CREATOR, buildAIExtraMap(true));
        pilotChatGroupMessageDAO.insertMessage(replyMessageEntity);

        //插入模型消息外部关联实体
        insertPilotMessageBizRef(replyMessageId);

        metricMessageSendSucc(assistantType);
        return new Pair<>(userMessage, replyMessageEntity);
    }

    private String buildExtra(SendBeamMessageReq sendBeamMessageReq) {
        Map<String, String> extra = Maps.newHashMap();
        extra.put("global_id", sendBeamMessageReq.getGlobal_id());
        extra.put("conversation_id", sendBeamMessageReq.getConversation_id());
        return JsonCodec.encode(extra);
    }


    @Override
    public Pair<PilotChatMessageEntity, PilotChatMessageEntity> sendMessageAfterAudit(SseEmitter sseEmitter,
                                                                                      String imUserId, SendMessageReq sendMessageReq, long userMessageId){
        //发送消息ID到SSE
        sendSseContent(sseEmitter, StreamEventTypeEnum.MESSAGE.getType(), StreamEventDataTypeEnum.MESSAGE_ID.getType(), String.valueOf(userMessageId));

        if (hasCache(sendMessageReq)) {
            PilotChatMessageEntity replyMessageEntity = assistantMessageCacheAclService.getCacheMessage(getCacheKeyDataBeforeAskLLM(sendMessageReq));
            if (replyMessageEntity != null) {
                replyMessageEntity.setId(null);
                long replyMessageId = IDGenerateUtil.nextMessageId();
                boolean success = sendSseContent(sseEmitter, StreamEventTypeEnum.MESSAGE.getType(), StreamEventDataTypeEnum.REPLY_MESSAGE_ID.getType(), String.valueOf(replyMessageId));
                //更新会话信息
                updateChatGroup(sendMessageReq.getChatGroupId(), userMessageId);
                //插入用户消息
                PilotChatMessageEntity userMessage = buildPilotChatMessageEntity(userMessageId, sendMessageReq.getMessageType(),
                        sendMessageReq.getChatGroupId(), sendMessageReq.getAssistantType(), imUserId, MessageSendDirectionEnum.CLIENT_SEND.value,
                        AuditStatusEnum.PASS.getCode(), sendMessageReq.getMessage(), imUserId, buildUserExtraMap(sendMessageReq.getExtra(), replyMessageId));
                pilotChatGroupMessageDAO.insertMessage(userMessage);
                //插入模型消息
                replyMessageEntity.setMessageId(replyMessageId);
                replyMessageEntity.setChatGroupId(sendMessageReq.getChatGroupId());
                replyMessageEntity.setAssistantType(sendMessageReq.getAssistantType());
                replyMessageEntity.setUserId(imUserId);
                replyMessageEntity.setExtraData(buildAIExtraMap(success));
                pilotChatGroupMessageDAO.insertMessage(replyMessageEntity);
                metricAuditSucc(sendMessageReq.getAssistantType());
                return new Pair<>(userMessage, replyMessageEntity);
            }
        }
        // 回复messageId前置生成，然后放到ThreadLocal
        long replyMessageId = IDGenerateUtil.nextMessageId();
        boolean success = false;
        if (preSendAssistantTypes.contains(sendMessageReq.getAssistantType())) {
            success = sendSseContent(sseEmitter, StreamEventTypeEnum.MESSAGE.getType(), StreamEventDataTypeEnum.REPLY_MESSAGE_ID.getType(), String.valueOf(replyMessageId));
        }
        // 异步审核用户输入
        CompletableFuture<AuditResult> userAuditResultCF = messageAuditProcessService.auditUserMessage(sendMessageReq, userMessageId, replyMessageId);
        //大模型回复
        BufferMergedVO bufferMergedVO = defaultAssistantScene.process(AssistantSceneRequest.builder()
                .assistantType(sendMessageReq.getAssistantType()).userId(sendMessageReq.getImUserId())
                .question(sendMessageReq.getMessage()).messageId(userMessageId)
                .replyMessageId(replyMessageId)
                .chatGroupId(sendMessageReq.getChatGroupId()).inputSourceType(sendMessageReq.getInputSource())
                .bizParams(sendMessageReq.getBizParams()).envContext(sendMessageReq.getEnvContext()).userIp(sendMessageReq.getUserIp())
                .chatGroupExp(sendMessageReq.getChatGroupExp()).channel(sendMessageReq.getChannel()).build());
        List<StreamEventVO> reply = bufferMergedVO.getStreamEventVOs();
        int messageType =  bufferMergedVO.getMessageType();
        if (CollectionUtils.isEmpty(reply) || replyMessageId <= 0) {
            LogUtils.logFailLog(log, TagContext.builder().action("sendMessage").build(),
                    new WarnMessage("replyMessageError", "模型生成错误", String.valueOf(replyMessageId)), sendMessageReq, reply);
            return new Pair<>(null, null);
        }
        if (!preSendAssistantTypes.contains(sendMessageReq.getAssistantType())) {
            success = sendSseContent(sseEmitter, StreamEventTypeEnum.MESSAGE.getType(), StreamEventDataTypeEnum.REPLY_MESSAGE_ID.getType(), String.valueOf(replyMessageId));
        }
        //更新会话信息
        updateChatGroup(sendMessageReq.getChatGroupId(), userMessageId);
        //插入用户消息
        PilotChatMessageEntity userMessage = buildPilotChatMessageEntity(userMessageId, sendMessageReq.getMessageType(),
                                                                         sendMessageReq.getChatGroupId(), sendMessageReq.getAssistantType(), imUserId, MessageSendDirectionEnum.CLIENT_SEND.value,
                                                                         AuditStatusEnum.PASS.getCode(), sendMessageReq.getMessage(), imUserId, buildUserExtraMap(sendMessageReq.getExtra(), replyMessageId));
        ChatGroupExpVO chatGroupExpVO = MessageUtils.parseChatGroupExp(sendMessageReq.getChatGroupExp());
        //插入模型消息
        PilotChatMessageEntity replyMessageEntity = buildPilotChatMessageEntity(replyMessageId, messageType,
                sendMessageReq.getChatGroupId(), sendMessageReq.getAssistantType(), imUserId,
                MessageSendDirectionEnum.ASSISTANT_SEND.value, AuditStatusEnum.PASS.getCode(),
                JsonCodec.encodeWithUTF8(reply), AssistantConstant.ASSISTANT_CREATOR, buildAIExtraMap(success));
        assistantMessageCacheAclService.setCacheMessage(getCacheKeyDataAfterAskLLM(sendMessageReq), replyMessageEntity);
        // 异步审核模型生成内容
        CompletableFuture<AuditResult> llmAuditResultCF = messageAuditProcessService.auditLLMGenerateMessage(replyMessageEntity, userMessageId, replyMessageId);

        // 根据审核结果更新消息
        auditUpdateMessage(userAuditResultCF, llmAuditResultCF, userMessage, replyMessageEntity);
        //没有chatGroupExp或chatGroupExp解析不需要流式返回时处理时落库
        if (chatGroupExpVO == null || !chatGroupExpVO.getNeedCallStream() || sendMessageReq.getAssistantType() != AssistantTypeEnum.HAIR_DIALOGUE_AGENT.getType()) {
            pilotChatGroupMessageDAO.insertMessage(userMessage);
        }
        pilotChatGroupMessageDAO.insertMessage(replyMessageEntity);
        //插入模型消息外部关联实体
        insertPilotMessageBizRef(replyMessageId);
        metricMessageSendSucc(sendMessageReq.getAssistantType());
        return new Pair<>(userMessage, replyMessageEntity);
    }

    private void auditUpdateMessage(CompletableFuture<AuditResult> userAuditResultCF, CompletableFuture<AuditResult> llmAuditResultCF, PilotChatMessageEntity userMessage, PilotChatMessageEntity replyMessageEntity) {
        try {
            AuditResult userAuditResult = userAuditResultCF.join();
            AuditResult llmAuditResult = llmAuditResultCF.join();
            if ((userAuditResult != null && !userAuditResult.isPass()) ||
                    (llmAuditResult != null && !llmAuditResult.isPass())) {
                userMessage.setAuditStatus(AuditStatusEnum.SENSITIVE_FAIL.getCode());
                replyMessageEntity.setAuditStatus(AuditStatusEnum.SENSITIVE_FAIL.getCode());
            }
        } catch (Exception e) {
            log.error("auditUpdateMessage error", e);
        }
    }

    private void insertPilotMessageBizRef(long replyMessageId) {
        MessageBizRefData messageBizRefData = RequestContext.getAttribute(RequestContextConstants.NEW_CHAT_MESSAGE_BIZ_REF);
        if (messageBizRefData != null) {
            messageBizRefData.setMessageId(replyMessageId);
            pilotMessageBizRefProcessService.saveMessageBizRef(messageBizRefData);
        }
    }

    private boolean checkIfUserIsBlocked(SendMessageReq sendMessageReq) {
        BlockWhiteListConfig blockWhiteListConfig = lionConfigUtil.getBlockWhiteListConfig();
        if (!blockWhiteListConfig.isOpen) {
            return false;
        }

        //  白名单用户
        if (blockWhiteListConfig.getWhiteList().contains(sendMessageReq.getImUserId())) {
            return true;
        }

        BlockListEntity entity = riskUserBlockDomainService.selectUserByIdAndAssistantType(sendMessageReq.getImUserId(), sendMessageReq.getAssistantType());
        if (entity == null) {
            return true;
        }

        if (entity !=null && entity.getBlockedStatus() != BlockStatusEnum.NORMAL.getStatus()) {
            return false;
        }
        return true;
    }

    private RiskBlockMessageData buildRiskBlackMessageData(SendMessageReq req, AuditResDTO audit, PilotChatMessageEntity userMessage, PilotChatMessageEntity replyMessageEntity) {
        RiskBlockMessageData riskBlockMessageData = new RiskBlockMessageData();
        riskBlockMessageData.setAudit(audit);
        riskBlockMessageData.setUserMessage(userMessage);
        riskBlockMessageData.setReplyMessageEntity(replyMessageEntity);
        riskBlockMessageData.setReq(req);
        return riskBlockMessageData;
    }

    private String buildUserExtraMap(String originalExtra, long replyMessageId) {
        Map<String, Object> extraMap = Maps.newHashMap();
        
        // 如果原有extra不为空，先解析原有的extra内容
        if (StringUtils.isNotEmpty(originalExtra)) {
            try {
                Map<String, Object> originalExtraMap = JsonCodec.decode(originalExtra, Map.class);
                if (MapUtils.isNotEmpty(originalExtraMap)) {
                    extraMap.putAll(originalExtraMap);
                }
            } catch (Exception e) {
                log.error("Failed to parse original extra data: {}", originalExtra, e);
            }
        }
        
        // 添加replyMessageId
        extraMap.put("replyMessageId", replyMessageId);
        
        return JsonCodec.encodeWithUTF8(extraMap);
    }

    private String buildAIExtraMap(boolean success) {
        if (success) {
            return StringUtils.EMPTY;
        }

        Map<String, Object> extraMap = Maps.newHashMap();
        extraMap.put("sseClose", true);
        return JsonCodec.encodeWithUTF8(extraMap);
    }

    private boolean sendSseContent(SseEmitter sseEmitter, String type, String event, String content) {
        try {
            sseEmitter.send(SseEmitter.event().data(StreamEventVO.builder().type(type)
                    .data(StreamEventDataVO.builder().event(event).content(content).build()).build()));
            return true;
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("MessageSendProcess:sendSseContent").build(),
                    new WarnMessage("MessageSendProcessService", "MessageSendProcess发送SSE消息异常", ""), new Object[]{type, event, content}, e);
        }
        return false;
    }

    private boolean hasCache(SendMessageReq sendMessageReq) {
        if (MessageInputSourceEnum.REPEAT_GENERATE.getType() == sendMessageReq.getInputSource()) {
            return false;
        }
        return assistantMessageCacheAclService.containsQuestion(getCacheKeyDataBeforeAskLLM(sendMessageReq));
    }

    private AssistantMessageCacheKeyData getCacheKeyDataBeforeAskLLM(SendMessageReq sendMessageReq) {
        String question = sendMessageReq.getMessage();
        AssistantMessageCacheKeyData keyData = AssistantMessageCacheKeyData.builder()
                .question(question)
                .assistantType(sendMessageReq.getAssistantType()).build();
        if (lionConfigUtil.getQuestionUVConfig().getPageSourceQuestionMap().containsValue(question)) {
            long shopId = getShopIdFromChatHistory(sendMessageReq.getChatGroupId(), sendMessageReq.getAssistantType());
            keyData.setShopId(shopId);
        }
        return keyData;
    }

    private long getShopIdFromChatHistory(long chatGroupId, int assistantType) {
        ZebraForceMasterHelper.forceMasterInLocalContext();
        List<PilotChatMessageEntity> chatMessageEntities = pilotChatGroupMessageDAO.multiGetLatestMessages(chatGroupId, 30);
        ZebraForceMasterHelper.clearLocalContext();
        for (PilotChatMessageEntity entity : chatMessageEntities) {
            if (containsWelcomeMessage(entity.getMessage(), assistantType)) {
                return 0;
            }
            long shopId = AssistantMessageUtils.extractShopId(entity.getMessage());
            if (shopId > 0) {
                return shopId;
            }
        }
        return 0;
    }

    private boolean containsWelcomeMessage(String message, int assistantType) {
        return message.contains(lionConfigUtil.getAssistantWelcomeConfig(String.valueOf(assistantType)).getWelcomeMessage());
    }

    private AssistantMessageCacheKeyData getCacheKeyDataAfterAskLLM(SendMessageReq sendMessageReq) {
        String question = sendMessageReq.getMessage();
        ShopDetailParam shopDetailParam = RequestContext.getAttribute(RequestContextConstants.QUESTION_SHOP_INFO);
        if (lionConfigUtil.getQuestionUVConfig().getPageSourceQuestionMap().containsValue(question)) {
            if (shopDetailParam == null) {
                return null;
            }
            return AssistantMessageCacheKeyData.builder()
                    .question(question)
                    .shopId(shopDetailParam.getShopId())
                    .assistantType(sendMessageReq.getAssistantType()).build();
        }
        // 如果有用了shopId插件，但不是亮点问题，则不缓存
        if (shopDetailParam != null && shopDetailParam.getShopId() > 0) {
            return null;
        }
        return AssistantMessageCacheKeyData.builder()
                .question(question)
                .assistantType(sendMessageReq.getAssistantType()).build();
    }

    public void metricMessageSendCnt(int assistantType) {
        Map<String, String> tags = Maps.newHashMap();
        tags.put("assistant", String.valueOf(assistantType));
        CatUtils.logMetricReq("MessageSend", tags);
    }

    public void metricMessageSendSucc(int assistantType) {
        Map<String, String> tags = Maps.newHashMap();
        tags.put("assistant", String.valueOf(assistantType));
        CatUtils.logMetricSucc("MessageSend", tags);
    }

    public void metricAuditSucc(int assistantType) {
        Map<String, String> tags = Maps.newHashMap();
        tags.put("assistant", String.valueOf(assistantType));
        CatUtils.logMetricSucc("Audit", tags);
    }

    private PilotChatMessageEntity buildPilotChatMessageEntity(long messageId, int messageType, long chatGroupId, int assistantType,
                                                               String userId, int direction, int auditStatus, String message,
                                                               String creator, String extra) {
        PilotChatMessageEntity messageEntity = new PilotChatMessageEntity();
        messageEntity.setMessageId(messageId);
        messageEntity.setMessageType(messageType);
        messageEntity.setChatGroupId(chatGroupId);
        messageEntity.setAssistantType(assistantType);
        messageEntity.setUserId(userId);
        messageEntity.setDirection(direction);
        messageEntity.setAuditStatus(auditStatus);
        messageEntity.setExtraData(extra == null ? StringUtils.EMPTY : extra);
        messageEntity.setMessage(message);
        messageEntity.setCreator(creator);
        return messageEntity;
    }

    private boolean updateChatGroup(long chatGroupId, long userMessageId) {
        ZebraForceMasterHelper.forceMasterInLocalContext();
        PilotChatGroupEntity chatGroupEntity = pilotChatGroupDAO.queryChatGroupById(chatGroupId);
        if (chatGroupEntity == null) {
            return false;
        }
        long startMessageId = chatGroupEntity.getStartMessageId() > 0 ? chatGroupEntity.getStartMessageId() : userMessageId;
        int updateRow = pilotChatGroupDAO.updateChatGroupMessage(chatGroupId, startMessageId, userMessageId);
        return updateRow > 0;
    }


}
