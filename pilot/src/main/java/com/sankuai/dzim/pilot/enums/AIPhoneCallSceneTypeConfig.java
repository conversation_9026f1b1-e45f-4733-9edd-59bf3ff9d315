package com.sankuai.dzim.pilot.enums;

import lombok.Getter;

@Getter
public enum AIPhoneCallSceneTypeConfig {

    RESERVATION(AIPhoneCallSceneTypeEnum.RESERVATION.getType(), AIPhoneCallModeEnum.SINGLE.getType()),
    CANCEL_RESERVATION(AIPhoneCallSceneTypeEnum.CANCEL_RESERVATION.getType(), AIPhoneCallModeEnum.SINGLE.getType());

    private int sceneType;

    private int mode;

    AIPhoneCallSceneTypeConfig(int sceneType, int mode) {
        this.sceneType = sceneType;
        this.mode = mode;
    }

    public static AIPhoneCallSceneTypeConfig getBySceneType(int sceneType) {
        for (AIPhoneCallSceneTypeConfig config: AIPhoneCallSceneTypeConfig.values()) {
            if (sceneType == config.sceneType) {
                return config;
            }
        }
        return null;
    }
}
