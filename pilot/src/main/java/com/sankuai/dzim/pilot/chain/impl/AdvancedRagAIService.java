package com.sankuai.dzim.pilot.chain.impl;

import com.dianping.lion.client.Lion;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.mtrace.Tracer;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.message.common.utils.ImAccountTypeUtils;
import com.sankuai.dzim.message.dto.MessageDTO;
import com.sankuai.dzim.message.enums.AuditStatusEnum;
import com.sankuai.dzim.pilot.acl.FridayAclService;
import com.sankuai.dzim.pilot.acl.data.fraiday.ChatCompletionRequest;
import com.sankuai.dzim.pilot.acl.data.fraiday.ChatCompletionResponse;
import com.sankuai.dzim.pilot.acl.data.fraiday.FridayMessage;
import com.sankuai.dzim.pilot.acl.data.fraiday.FridayMessageRoleEnum;
import com.sankuai.dzim.pilot.acl.data.fraiday.Tool;
import com.sankuai.dzim.pilot.acl.data.fraiday.ToolCall;
import com.sankuai.dzim.pilot.api.data.AIAnswerTypeEnum;
import com.sankuai.dzim.pilot.api.enums.assistant.AssistantTypeEnum;
import com.sankuai.dzim.pilot.api.enums.assistant.MessageSendDirectionEnum;
import com.sankuai.dzim.pilot.api.enums.assistant.MessageTypeEnum;
import com.sankuai.dzim.pilot.buffer.core.PilotBufferItemDO;
import com.sankuai.dzim.pilot.buffer.enums.BufferItemTypeEnum;
import com.sankuai.dzim.pilot.buffer.enums.StreamEventCardTypeEnum;
import com.sankuai.dzim.pilot.buffer.enums.StreamEventErrorTypeEnum;
import com.sankuai.dzim.pilot.chain.AIService;
import com.sankuai.dzim.pilot.chain.data.AIAnswerData;
import com.sankuai.dzim.pilot.chain.data.AIServiceContext;
import com.sankuai.dzim.pilot.chain.data.MemoryComponent;
import com.sankuai.dzim.pilot.chain.data.QueryUnderstandComponent;
import com.sankuai.dzim.pilot.chain.data.RetrievalComponent;
import com.sankuai.dzim.pilot.chain.data.ToolCallResult;
import com.sankuai.dzim.pilot.chain.enums.AIServiceExtraKeyEnum;
import com.sankuai.dzim.pilot.chain.enums.AIServiceTypeEnum;
import com.sankuai.dzim.pilot.dal.entity.pilot.PilotChatMessageEntity;
import com.sankuai.dzim.pilot.dal.pilotdao.PilotChatGroupDAO;
import com.sankuai.dzim.pilot.dal.pilotdao.PilotChatGroupMessageDAO;
import com.sankuai.dzim.pilot.domain.annotation.Hallucination;
import com.sankuai.dzim.pilot.domain.annotation.Memory;
import com.sankuai.dzim.pilot.domain.annotation.Query;
import com.sankuai.dzim.pilot.domain.annotation.Retrieval;
import com.sankuai.dzim.pilot.domain.hallucination.HallucinationCheckHandle;
import com.sankuai.dzim.pilot.domain.hallucination.data.HallucinationCheckContext;
import com.sankuai.dzim.pilot.domain.hallucination.data.HallucinationCheckResult;
import com.sankuai.dzim.pilot.domain.memory.MemoryProvider;
import com.sankuai.dzim.pilot.domain.memory.data.QueryMemoryContext;
import com.sankuai.dzim.pilot.domain.message.AssistantMessage;
import com.sankuai.dzim.pilot.domain.message.Message;
import com.sankuai.dzim.pilot.domain.message.PluginCall;
import com.sankuai.dzim.pilot.domain.message.PluginResultMessage;
import com.sankuai.dzim.pilot.domain.message.UserMessage;
import com.sankuai.dzim.pilot.domain.plugin.PluginRegistry;
import com.sankuai.dzim.pilot.domain.plugin.data.LocalPluginConfig;
import com.sankuai.dzim.pilot.domain.plugin.data.PluginSpecification;
import com.sankuai.dzim.pilot.domain.plugin.data.PluginTypeEnum;
import com.sankuai.dzim.pilot.domain.plugin.executor.data.PluginExecutionRequest;
import com.sankuai.dzim.pilot.domain.query.QueryHandle;
import com.sankuai.dzim.pilot.domain.query.data.QueryHandleContext;
import com.sankuai.dzim.pilot.domain.query.data.QueryHandleResult;
import com.sankuai.dzim.pilot.domain.retrieval.RetrievalAugmentor;
import com.sankuai.dzim.pilot.domain.retrieval.data.Knowledge;
import com.sankuai.dzim.pilot.domain.retrieval.data.RetrievalContext;
import com.sankuai.dzim.pilot.domain.retrieval.data.RetrievalResult;
import com.sankuai.dzim.pilot.domain.retrieval.data.SourceData;
import com.sankuai.dzim.pilot.gateway.api.assistant.data.SseAwareException;
import com.sankuai.dzim.pilot.process.data.AssistantConstant;
import com.sankuai.dzim.pilot.process.localplugin.data.UserContext;
import com.sankuai.dzim.pilot.scene.data.AssistantSceneContext;
import com.sankuai.dzim.pilot.utils.AnnotationUtil;
import com.sankuai.dzim.pilot.utils.IDGenerateUtil;
import com.sankuai.dzim.pilot.utils.LionConfigUtil;
import com.sankuai.dzim.pilot.utils.Log2HiveUtils;
import com.sankuai.dzim.pilot.utils.PluginContextUtil;
import com.sankuai.dzim.pilot.utils.context.BusinessKeyConstant;
import com.sankuai.dzim.pilot.utils.context.RequestContext;
import com.sankuai.dzim.pilot.utils.context.RequestContextConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sankuai.dzim.pilot.buffer.enums.StreamEventCardTypeEnum.buildCardContent;

/**
 * 包含function-call等所有组件,可以作为通用的aiservice
 */
@Slf4j
@Component
public class AdvancedRagAIService implements AIService {

    private static final String REACH_MAX_PLUGIN_RUNNING_TIMES_REPLY = "很抱歉，目前获取相关信息时间过长，请您稍后再咨询客服，感谢您的理解。";

    @Autowired
    protected FridayAclService fridayAclService;

    @Autowired
    private AnnotationUtil annotationUtil;

    @Autowired
    private LionConfigUtil lionConfigUtil;

    @Autowired
    private IDGenerateUtil IDGenerateUtil;

    @Resource
    private PilotChatGroupDAO pilotChatGroupDAO;

    @Resource
    private PilotChatGroupMessageDAO pilotChatGroupMessageDAO;

    @Override
    public boolean accept(int aiServiceType) {
        return aiServiceType == AIServiceTypeEnum.ADVANCED_RAG.getType();
    }

    @Override
    public AIAnswerData execute(AIServiceContext aiServiceContext) {
        // 1.查询记忆：聊天上下文等，作为LLM的入参，如果有商品信息，则数据为简单的商品信息-名称、id
        List<Message> memory = getMemory(aiServiceContext);

        //2.查询理解
        QueryHandleResult queryHandleResult = queryUnderstand(aiServiceContext, memory);

        //todo 多条查询理解需要并发调用进行生成再汇总答案

        // 3.执行检索组件：填充上下文，作为LLM的补充知识，提高LLM的处理性能，如业务知识检索，为根据最后一条消息，去向量知识库搜索匹配的知识
        List<RetrievalResult> retrievalResults = getRetrievalKnowledge(aiServiceContext, queryHandleResult);

        // 3.获取工具配置
        List<PluginSpecification> pluginSpecifications = getPlugins(aiServiceContext);

        // 4.获取答案：大模型根据上下文，还有写好的prompt，生成回复，中途会调用插件获取必要的信息
        AIAnswerData answer = getAnswer(aiServiceContext, memory, retrievalResults, pluginSpecifications);
        answer.setToolCallResults(aiServiceContext.getToolCallResults());

        // 卡片写入buffer
        if (MapUtils.isNotEmpty(aiServiceContext.getRetrievalMap())) {
            writeDataSourceBuffer(aiServiceContext, retrievalResults);
        }

        logAIAnswer(aiServiceContext, answer, retrievalResults);


        // 5.幻觉检测
        if (StringUtils.isBlank(answer.getAnswer()) || StringUtils.isBlank(aiServiceContext.getHallucinationCheckHandle()) || aiServiceContext.isStream()) {
            return answer;
        }

        AIAnswerData correctAnswer = checkHallucination(aiServiceContext, retrievalResults, memory, answer);
        return correctAnswer;
    }

    private QueryHandleResult queryUnderstand(AIServiceContext aiServiceContext, List<Message> memory) {
        if (CollectionUtils.isEmpty(aiServiceContext.getQueryUnderstandComponents())) {
            return null;
        }

        List<String> queryUnderstandNames = aiServiceContext.getQueryUnderstandComponents().stream().map(QueryUnderstandComponent::getName).collect(Collectors.toList());
        List<QueryHandle> handles = annotationUtil.getBeanWithAnnotation(Query.class, "name", queryUnderstandNames, QueryHandle.class);
        if (CollectionUtils.isEmpty(handles)) {
            return null;
        }

        QueryHandleResult queryHandleResult = new QueryHandleResult();
        queryHandleResult.setNewQueries(Lists.newArrayList());
        for (QueryHandle handle : handles) {
            Query annotation = handle.getClass().getAnnotation(Query.class);
            QueryHandleContext context = buildQueryUnderstandContext(aiServiceContext, annotation.name(), memory);
            QueryHandleResult understand = handle.understand(context);
            if (understand != null && CollectionUtils.isNotEmpty(understand.getNewQueries())) {
                queryHandleResult.getNewQueries().addAll(understand.getNewQueries());
            }
        }
        return queryHandleResult;
    }

    private QueryHandleContext buildQueryUnderstandContext(AIServiceContext aiServiceContext, String name, List<Message> memory) {
        QueryHandleContext context = new QueryHandleContext();
        context.setQuery(aiServiceContext.getMessageDTO().getMessage());
        context.setHistories(new ArrayList<>(memory));
        context.setExtraInfo(aiServiceContext.getExtraInfo());

        Map<String, QueryUnderstandComponent> name2QueryHandleMap = aiServiceContext.getQueryUnderstandComponents().stream().collect(Collectors.toMap(QueryUnderstandComponent::getName, Function.identity()));
        context.setType(name2QueryHandleMap.get(name).getType());
        return context;
    }

    private AIAnswerData checkHallucination(AIServiceContext aiServiceContext, List<RetrievalResult> retrievalResults, List<Message> memory, AIAnswerData answer) {
        String knowledgeText = getKnowledgeText(retrievalResults);
        HallucinationCheckContext hallucinationContext = HallucinationCheckContext.builder().answer(answer.getAnswer()).messages(memory).knowledge(knowledgeText).build();
        List<HallucinationCheckHandle> checkHandles = annotationUtil.getBeanWithAnnotation(Hallucination.class, "name", Lists.newArrayList(aiServiceContext.getHallucinationCheckHandle()), HallucinationCheckHandle.class);
        if (CollectionUtils.isEmpty(checkHandles)) {
            return answer;
        }

        HallucinationCheckResult checkResult = checkHandles.get(0).check(hallucinationContext);
        if (checkResult == null) {
            return answer;
        }

        return checkResult.getCorrectAnswer(answer);
    }

    private List<Message> getMemory(AIServiceContext aiServiceContext) {
        if (CollectionUtils.isEmpty(aiServiceContext.getMemoryComponents())) {
            return Lists.newArrayList(UserMessage.build(aiServiceContext.getMessageDTO().getMessage()));
        }
        List<String> memoriesNames = aiServiceContext.getMemoryComponents().stream().map(MemoryComponent::getName).collect(Collectors.toList());
        List<MemoryProvider> memoryProviders = annotationUtil.getBeanWithAnnotation(Memory.class, "name", memoriesNames, MemoryProvider.class);
        if (CollectionUtils.isEmpty(memoryProviders)) {
            return Lists.newArrayList(UserMessage.build(aiServiceContext.getMessageDTO().getMessage()));
        }

        List<Message> memoryMessages = Lists.newArrayList();
        for (MemoryProvider memoryProvider : memoryProviders) {
            Memory annotation = memoryProvider.getClass().getAnnotation(Memory.class);
            QueryMemoryContext context = buildQueryMemoryContext(aiServiceContext, annotation.name());
            List<Message> memory = memoryProvider.getMemory(context);
            if (CollectionUtils.isNotEmpty(memory)) {
                memoryMessages.addAll(memory);
            }
        }
        if (CollectionUtils.isEmpty(memoryMessages) || !memoryMessages.get(memoryMessages.size() - 1).equals(UserMessage.build(aiServiceContext.getMessageDTO().getMessage()))) {
            memoryMessages.add(UserMessage.build(aiServiceContext.getMessageDTO().getMessage()));
        }

        paddingMemoryInUserContext(aiServiceContext, memoryMessages);

        return memoryMessages;
    }

    /**
     * 填充用户上下文的memory
     * @param aiServiceContext
     * @param memoryMessages
     */
    private void paddingMemoryInUserContext(AIServiceContext aiServiceContext, List<Message> memoryMessages) {
        Map<String, Object> extraInfo = aiServiceContext.getExtraInfo();
        if (MapUtils.isEmpty(extraInfo)) {
            return;
        }
        UserContext userContext = PluginContextUtil.getUserContext(aiServiceContext);
        userContext.setMemory(memoryMessages);
        extraInfo.put(AIServiceExtraKeyEnum.USER_CONTEXT.getKey(), userContext);
    }

    private QueryMemoryContext buildQueryMemoryContext(AIServiceContext aiServiceContext, String memoryName) {
        Map<String, MemoryComponent> name2MemoryMap = aiServiceContext.getMemoryComponents().stream().collect(Collectors.toMap(MemoryComponent::getName, Function.identity()));
        MessageDTO messageDTO = aiServiceContext.getMessageDTO();
        QueryMemoryContext queryContext = new QueryMemoryContext();
        queryContext.setChatGroupId(messageDTO.getChatGroupId());
        queryContext.setChatGroupId(messageDTO.getChatGroupId());
        queryContext.setImClientId(ImAccountTypeUtils.isClientId(messageDTO.getFromUid()) ? messageDTO.getFromUid() : messageDTO.getToUid());
        queryContext.setImMerchantId(ImAccountTypeUtils.isClientId(messageDTO.getFromUid()) ? messageDTO.getToUid() : messageDTO.getFromUid());
        queryContext.setLastMessageId(messageDTO.getMessageId());
        queryContext.setAiServiceContext(aiServiceContext);
        if (name2MemoryMap.get(memoryName).getLimit() <= 0) {
            queryContext.setLimit(20);
        } else {
            queryContext.setLimit(name2MemoryMap.get(memoryName).getLimit());
        }

        if (name2MemoryMap.get(memoryName).getInterval() > 0) {
            queryContext.setInterval(name2MemoryMap.get(memoryName).getInterval());
        } else {
            queryContext.setInterval(30);
        }
        return queryContext;
    }

    private List<RetrievalResult> getRetrievalKnowledge(AIServiceContext aiServiceContext, QueryHandleResult queryHandleResult) {
        if (CollectionUtils.isEmpty(aiServiceContext.getRetrievalComponents())) {
            return Lists.newArrayList();
        }

        // 只有文本需要检索消息
        if (aiServiceContext.getMessageDTO().getMessageType() != MessageTypeEnum.TEXT.getValue()) {
            return Lists.newArrayList();
        }

        List<String> retrievalNames = aiServiceContext.getRetrievalComponents().stream().map(RetrievalComponent::getName).collect(Collectors.toList());
        List<RetrievalAugmentor> retrievalAugmentors = annotationUtil.getBeanWithAnnotation(Retrieval.class, "name", retrievalNames, RetrievalAugmentor.class);
        if (CollectionUtils.isEmpty(retrievalAugmentors)) {
            return Lists.newArrayList();
        }


        //todo 后面可以并行化
        List<RetrievalResult> retrievalResults = Lists.newArrayList();
        for (RetrievalAugmentor retrievalAugmentor : retrievalAugmentors) {
            List<RetrievalResult> knowledges = doRetrieval(aiServiceContext, retrievalAugmentor, queryHandleResult);
            if (CollectionUtils.isNotEmpty(knowledges)) {
                retrievalResults.addAll(knowledges);
            }
        }

        //todo 重排&混排
        return retrievalResults;
    }

    private List<RetrievalResult> doRetrieval(AIServiceContext aiServiceContext, RetrievalAugmentor retrievalAugmentor, QueryHandleResult queryHandleResult) {
        if (queryHandleResult == null || CollectionUtils.isEmpty(queryHandleResult.getNewQueries())) {
            RetrievalResult retrievalResult = doRetrievalWithQuery(aiServiceContext, retrievalAugmentor, aiServiceContext.getMessageDTO().getMessage());
            if (retrievalResult != null && CollectionUtils.isNotEmpty(retrievalResult.getKnowledges())) {
                return Lists.newArrayList(retrievalResult);
            }
        }
        if (queryHandleResult == null || CollectionUtils.isEmpty(queryHandleResult.getNewQueries())) {
            return Lists.newArrayList();
        }

        //todo 后面可以并行化
        List<RetrievalResult> results = Lists.newArrayList();
        for (String query : queryHandleResult.getNewQueries()) {
            RetrievalResult retrievalResult = doRetrievalWithQuery(aiServiceContext, retrievalAugmentor, query);
            if (retrievalResult != null && CollectionUtils.isNotEmpty(retrievalResult.getKnowledges())) {
                results.add(retrievalResult);
            }
        }

        return results;
    }

    private RetrievalResult doRetrievalWithQuery(AIServiceContext aiServiceContext, RetrievalAugmentor retrievalAugmentor, String query) {

        Retrieval annotation = retrievalAugmentor.getClass().getAnnotation(Retrieval.class);
        RetrievalContext retrievalContext = buildRetrievalContext(aiServiceContext, annotation.name(), query);
        return retrievalAugmentor.retrieveKnowledge(retrievalContext);
    }

    private RetrievalContext buildRetrievalContext(AIServiceContext aiServiceContext, String name, String query) {
        Map<String, RetrievalComponent> name2RetrievalMap = aiServiceContext.getRetrievalComponents().stream().collect(Collectors.toMap(RetrievalComponent::getName, Function.identity()));
        RetrievalContext retrievalContext = new RetrievalContext();
        retrievalContext.setQuery(query);
        retrievalContext.setTopK(name2RetrievalMap.get(name).getTopK());
        retrievalContext.setType2SubjectIdMap(name2RetrievalMap.get(name).getType2SubjectIdMap());
        retrievalContext.setRetrievals(name2RetrievalMap.get(name).getRetrievals());
        retrievalContext.setExtraInfo(name2RetrievalMap.get(name).getExtraInfo());
        retrievalContext.setStartStatus(name2RetrievalMap.get(name).getStartStatus());
        retrievalContext.setEndStatus(name2RetrievalMap.get(name).getEndStatus());
        return retrievalContext;
    }

    protected List<PluginSpecification> getPlugins(AIServiceContext aiServiceContext) {
        if (CollectionUtils.isEmpty(aiServiceContext.getPluginNames())) {
            return Lists.newArrayList();
        }

        List<PluginSpecification> pluginSpecifications = Lists.newArrayList();
        for (String pluginName : aiServiceContext.getPluginNames()) {
            PluginSpecification pluginSpecification = PluginRegistry.getPlugin(pluginName);
            if (pluginSpecification == null) {
                LogUtils.logFailLog(log, TagContext.builder().action("function-call:getPluginFail").bizId(pluginName).build(),
                        new WarnMessage("FunctionCallAIService", "插件在注册中心中不存在", pluginName), pluginName, null);
                continue;
            }
            pluginSpecifications.add(pluginSpecification);
        }
        return pluginSpecifications;
    }

    private AIAnswerData getAnswer(AIServiceContext aiServiceContext, List<Message> memory, List<RetrievalResult> retrievalResults, List<PluginSpecification> pluginSpecifications) {
        // 将上下文，插件的描述传给LLM，看大模型是直接回复，还是需要执行插件然后再回复
        ChatCompletionRequest request = buildChatCompletionRequest(aiServiceContext, memory, retrievalResults, pluginSpecifications);
        ChatCompletionResponse chatCompletionResponse = fridayAclService.chatCompletion(aiServiceContext.getAppId(), request);
        if (chatCompletionResponse == null) {
            throw new SseAwareException(StreamEventErrorTypeEnum.SERVER_ERROR);
        }

        if (CollectionUtils.isEmpty(chatCompletionResponse.getChoices())) {
            throw new SseAwareException(StreamEventErrorTypeEnum.getByErrorCode(chatCompletionResponse.getCode()));
        }

        // 结果解析
        AssistantMessage assistantMessage = AssistantMessage.build(chatCompletionResponse);
        if (assistantMessage == null) {
            log.error("advancedRagAIService assistantMessage is null, chatCompletionResponse={}", chatCompletionResponse);
            throw new SseAwareException(StreamEventErrorTypeEnum.SERVER_ERROR);
        }
        // 大模型直接回复了，则无需执行插件，直接返回大模型的回复
        if (assistantMessage.getContent() != null) {
            return new AIAnswerData(AIAnswerTypeEnum.TEXT.getType(), assistantMessage.getContent(), assistantMessage.getReasonContent());
        }

        // LLM返回的插件结果与用户的问题顺序一致，用户最新的问题对应的工具在返回结果的最后，因此需要对LLM返回的插件结果进行逆序
        // assistantMessage里面的pluginCalls需要和过滤后的保持一致，不然会报错
        List<PluginCall> pluginCalls = getNeedExecutePluginList(assistantMessage.getPluginCalls());
        assistantMessage.setPluginCalls(pluginCalls);
        memory.add(assistantMessage);
        insertMessage(assistantMessage, aiServiceContext);
        setPluginCallRecord(aiServiceContext, pluginCalls);
        // 执行插件：如团购插件，重复LLM调用的填充上下文步骤（配置的上下文填充检索），然后调用大模型回复文本
        for (PluginCall pluginCall : pluginCalls) {
            PluginSpecification plugin = PluginRegistry.getPlugin(pluginCall.getPluginName());
            if (plugin == null) {
                continue;
            }
            PluginResultMessage pluginResultMessage = executePlugin(aiServiceContext, pluginCall);
            memory.add(pluginResultMessage);
            insertMessage(pluginResultMessage, aiServiceContext);

            if (plugin.isReturnDirect() || pluginResultMessage.isReturnDirect()) {
                String message = pluginResultMessage.getContent();
                return JsonCodec.decode(message, AIAnswerData.class);
            }
        }

        // 死循环判断,plugin调用次数,超时等自动终止调用
        aiServiceContext.executeTimesPlus();
        if (aiServiceContext.getExecuteTimes() >= lionConfigUtil.getMaxPluginRunningTimes()) {
            return new AIAnswerData(REACH_MAX_PLUGIN_RUNNING_TIMES_REPLY);
        }
        // 再次执行
        return getAnswer(aiServiceContext, memory, retrievalResults, pluginSpecifications);
    }

    private void setPluginCallRecord(AIServiceContext aiServiceContext, List<PluginCall> pluginCalls) {
        if (CollectionUtils.isEmpty(pluginCalls)) {
            return;
        }
        AssistantSceneContext sceneContext = RequestContext.getAttribute(RequestContextConstants.ASSISTANT_CONTEXT);
        if (sceneContext == null || sceneContext.getAssistantType() != AssistantTypeEnum.BEAM_FWLS_AGENT.getType()) {
            return;
        }
        RequestContext.setAttribute(RequestContextConstants.BEAM_TOOL_CALL_NAME, pluginCalls.get(0).getPluginName());
    }

    private void insertMessage(Message message, AIServiceContext aiServiceContext) {
        if (message == null) {
            return;
        }
        String messageStr = "";
        if (message instanceof AssistantMessage) {
            AssistantMessage assistantMessage = (AssistantMessage) message;
            messageStr = JsonCodec.encodeWithUTF8(assistantMessage);
        } else if (message instanceof PluginResultMessage) {
            PluginResultMessage pluginResultMessage = (PluginResultMessage) message;
            messageStr = JsonCodec.encodeWithUTF8(pluginResultMessage);
        }
        if (StringUtils.isBlank(messageStr)) {
            return;
        }
        try {
            long replyMessageId = IDGenerateUtil.nextMessageId();
            UserContext userContext = PluginContextUtil.getUserContext(aiServiceContext);
            //插入模型消息
            PilotChatMessageEntity replyMessageEntity = buildPilotChatMessageEntity(replyMessageId, MessageTypeEnum.TOOL_CALL.getValue(),
                                                                                    userContext.getChatGroupId(), userContext.getAssistantType(), userContext.getImUserId(),
                                                                                    MessageSendDirectionEnum.TOOL_CALL.value, AuditStatusEnum.PASS.getCode(),
                                                                                    messageStr, AssistantConstant.TOOL_CREATOR, null);
            pilotChatGroupMessageDAO.insertMessage(replyMessageEntity);
        } catch (Exception e) {
            log.error("AdvancedRagAIService.insertMessage error, message:{}", messageStr, e);
        }
    }

    private PilotChatMessageEntity buildPilotChatMessageEntity(long messageId, int messageType, long chatGroupId, int assistantType,
                                                               String userId, int direction, int auditStatus, String message,
                                                               String creator, String extra) {
        PilotChatMessageEntity messageEntity = new PilotChatMessageEntity();
        messageEntity.setMessageId(messageId);
        messageEntity.setMessageType(messageType);
        messageEntity.setChatGroupId(chatGroupId);
        messageEntity.setAssistantType(assistantType);
        messageEntity.setUserId(userId);
        messageEntity.setDirection(direction);
        messageEntity.setAuditStatus(auditStatus);
        messageEntity.setExtraData(extra == null ? StringUtils.EMPTY : extra);
        messageEntity.setMessage(message);
        messageEntity.setCreator(creator);
        return messageEntity;
    }

    private List<PluginCall> getNeedExecutePluginList(List<PluginCall> originPluginList) {
        List<PluginCall> reversePlugins = Lists.reverse(originPluginList);
        // 首个工具直接返回的话，仅使用该工具即可
        PluginCall pluginCall = reversePlugins.get(0);
        if (isPluginCallReturnDirect(pluginCall)) {
            return Lists.newArrayList(pluginCall);
        }
        // 首个工具非直接返回的，将后续非直接返回的工具抽出来做执行
        return reversePlugins.stream().filter(e -> !isPluginCallReturnDirect(e)).collect(Collectors.toList());
    }

    private boolean isPluginCallReturnDirect(PluginCall pluginCall) {
        PluginSpecification plugin = PluginRegistry.getPlugin(pluginCall.getPluginName());
        return plugin.isReturnDirect();
    }

    private PluginResultMessage executePlugin(AIServiceContext aiServiceContext, PluginCall pluginCall) {
        PluginSpecification plugin = PluginRegistry.getPlugin(pluginCall.getPluginName());
        PluginExecutionRequest pluginExecutionRequest = new PluginExecutionRequest();
        pluginExecutionRequest.setName(pluginCall.getPluginName());

        //填充插件参数
        fillPluginParam(aiServiceContext, pluginCall, pluginExecutionRequest);
        String result = plugin.getExecutor().execute(pluginExecutionRequest);
        if (StringUtils.isBlank(result)) {
            return PluginResultMessage.build(pluginCall.getId(), plugin.getName(), "暂无相关内容");
        }
        AIAnswerData answerData = JsonCodec.decode(result, AIAnswerData.class);
        Optional.ofNullable(aiServiceContext.getToolCallResults())
                .orElse(Lists.newArrayList())
                .add(new ToolCallResult(pluginCall.getPluginName(), answerData.getAnswer()));
        PluginResultMessage message = PluginResultMessage.build(pluginCall.getId(), plugin.getName(), answerData.getAnswer());
        if (plugin.isReturnDirect() || answerData.isReturnDirect()) {
            message = PluginResultMessage.build(pluginCall.getId(), plugin.getName(), JsonCodec.encodeWithUTF8(answerData));
        }
        if (answerData.isReturnDirect()) {
            message.setReturnDirect(true);
        }
        return message;
    }

    private void fillPluginParam(AIServiceContext aiServiceContext, PluginCall pluginCall, PluginExecutionRequest pluginExecutionRequest) {
        String paramStr = pluginCall.getArguments().asText();
        if (StringUtils.isBlank(paramStr)) {
            pluginExecutionRequest.setParams("{}");
            return;
        }
        Map<String, Object> paramMap = JsonCodec.converseMap(paramStr, String.class, Object.class);
        if (paramMap == null) {
            paramMap = Maps.newHashMap();
        }

        // 透传上下文
        paramMap.put("aiServiceContext", aiServiceContext);
        pluginExecutionRequest.setParams(JsonCodec.encodeWithUTF8(paramMap));
    }

    protected ChatCompletionRequest buildChatCompletionRequest(AIServiceContext aiServiceContext, List<Message> memory, List<RetrievalResult> retrievalResults, List<PluginSpecification> pluginSpecifications) {
        ChatCompletionRequest request = new ChatCompletionRequest();

        request.setMessages(getMessagesFromMemory(aiServiceContext, memory, retrievalResults));
        request.setModel(aiServiceContext.getModel());
        request.setTemperature(aiServiceContext.getTemperature());
        request.setTop_p(aiServiceContext.getTopP());
        request.setUser(Tracer.id());
        request.setTools(getOpenAITools(pluginSpecifications));
        if (CollectionUtils.isNotEmpty(request.getTools())) {
            request.setTool_choice("auto");
        }
        if (StringUtils.isNotBlank(aiServiceContext.getToolChoice())) {
            request.setTool_choice(aiServiceContext.getToolChoice());
        }
        request.setStream(aiServiceContext.isStream());
        request.setMax_tokens(aiServiceContext.getMaxTokens());
        if (aiServiceContext.getIsJsonModel() != null) {
            request.setResponse_format(aiServiceContext.getIsJsonModel() ? ImmutableMap.of("type", "json_object") : null);
        }

        AssistantSceneContext assistantSceneContext = RequestContext.getAttribute(RequestContextConstants.ASSISTANT_CONTEXT);
        if (assistantSceneContext != null) {
            Integer assistantType = assistantSceneContext.getAssistantType();
            if (assistantType != null && lionConfigUtil.getToolCallAssistants().contains(assistantType)) {
                request.setBusinessKey(BusinessKeyConstant.TOOL_CALL_ASSISTANT);
            }
        }

        Map<String, Object> extraInfo = aiServiceContext.getExtraInfo();
        if (MapUtils.emptyIfNull(extraInfo).containsKey(AIServiceExtraKeyEnum.BUFFER_EXTRA.getKey())) {
            request.setBufferExtra((Map<String, Object>) extraInfo.get(AIServiceExtraKeyEnum.BUFFER_EXTRA.getKey()));
        }
        return request;
    }

    private List<Tool> getOpenAITools(List<PluginSpecification> pluginSpecifications) {
        if (CollectionUtils.isEmpty(pluginSpecifications)) {
            return null;
        }

        List<Tool> tools = Lists.newArrayList();
        for (PluginSpecification pluginSpecification : pluginSpecifications) {
            Tool tool = new Tool();
            tool.setFunction(getOpenAIFunction(pluginSpecification));
            tools.add(tool);
        }
        return tools;
    }

    private com.sankuai.dzim.pilot.acl.data.fraiday.Function getOpenAIFunction(PluginSpecification pluginSpecification) {
        // 本地插件,如果有配置lion配置,则把插件属性更新到最新
        if (pluginSpecification.getType() == PluginTypeEnum.LOCAL.getType() && StringUtils.isNotBlank(pluginSpecification.getLionKey())) {
            updatePluginSpecification(pluginSpecification);
        }

        // 直接获取插件注册时的属性
        com.sankuai.dzim.pilot.acl.data.fraiday.Function function = new com.sankuai.dzim.pilot.acl.data.fraiday.Function();
        function.setName(pluginSpecification.getName());
        function.setDescription(pluginSpecification.getDescription());
        function.setParameters(pluginSpecification.getParams());
        return function;
    }

    private void updatePluginSpecification(PluginSpecification pluginSpecification) {
        LocalPluginConfig localPluginConfig = Lion.getBean("com.sankuai.mim.pilot", pluginSpecification.getLionKey(), LocalPluginConfig.class);
        if (localPluginConfig == null) {
            return;
        }
        String oldPluginName = localPluginConfig.getName();
        if (StringUtils.isNotBlank(localPluginConfig.getName())) {
            pluginSpecification.setName(localPluginConfig.getName());
        }
        if (StringUtils.isNotBlank(localPluginConfig.getDescription())) {
            pluginSpecification.setDescription(localPluginConfig.getDescription());
        }
        if (localPluginConfig.getParams() != null) {
            pluginSpecification.setParams(localPluginConfig.getParams());
        }
        if (localPluginConfig.getReturnDirect() != null) {
            pluginSpecification.setReturnDirect(localPluginConfig.getReturnDirect());
        }

        PluginRegistry.removePlugin(oldPluginName);
        PluginRegistry.addPlugin(pluginSpecification);
    }

    private List<FridayMessage> getMessagesFromMemory(AIServiceContext aiServiceContext, List<Message> memory, List<RetrievalResult> retrievalResults) {
        List<FridayMessage> messages = Lists.newArrayList();

        String knowledge = getKnowledgeText(retrievalResults);

        // 系统消息
        messages.add(new FridayMessage(FridayMessageRoleEnum.SYSTEM.getValue(), aiServiceContext.getSystemPrompt() + "\n\n" + knowledge));

        // 对话消息
        if (CollectionUtils.isNotEmpty(memory)) {
            List<FridayMessage> chatMessages = memory.stream().map(this::transform2OpenAIMessage).collect(Collectors.toList());
            messages.addAll(chatMessages);
        }

        return messages;
    }

    private String getKnowledgeText(List<RetrievalResult> retrievalResults) {
        StringBuilder knowledge = new StringBuilder("");
        for (RetrievalResult retrievalResult : retrievalResults) {
            if (CollectionUtils.isEmpty(retrievalResult.getKnowledges())) {
                continue;
            }
            knowledge.append(retrievalResult.getKnowledgePrompt()).append(retrievalResult.getKnowledges().stream().map(Knowledge::getKnowledge).collect(Collectors.joining("\n"))).append("\n\n");
        }
        return knowledge.toString();
    }

    private FridayMessage transform2OpenAIMessage(Message message) {
        FridayMessage fridayMessage = new FridayMessage(FridayMessageRoleEnum.getFromMessage(message).getValue(), message.getContent());
        // 助手消息可能是插件调用
        fridayMessage.setTool_calls(getFunctionCalls(message));
        fridayMessage.setTool_call_id(getFunctionCallId(message));
        return fridayMessage;
    }

    private String getFunctionCallId(Message message) {
        if (message instanceof PluginResultMessage) {
            return ((PluginResultMessage) message).getPluginCallId();
        }
        return null;
    }

    private List<ToolCall> getFunctionCalls(Message message) {
        if (message instanceof AssistantMessage) {
            AssistantMessage assistantMessage = (AssistantMessage) message;
            if (assistantMessage.getPluginCalls() == null) {
                return null;
            }
            return assistantMessage.getPluginCalls().stream().map(pluginCall -> {
                ToolCall toolCall = new ToolCall();
                toolCall.setId(pluginCall.getId());
                toolCall.setFunction(ToolCall.ToolCallDesc.builder().arguments(pluginCall.getArguments()).name(pluginCall.getPluginName()).build());
                return toolCall;
            }).collect(Collectors.toList());
        }

        return null;
    }

    public void writeDataSourceBuffer(AIServiceContext aiServiceContext, List<RetrievalResult> retrievalResults) {
        if (StringUtils.isBlank((String) aiServiceContext.getRetrievalMap().get("retrieval")) || CollectionUtils.isEmpty(retrievalResults)) {
            return;
        }

        PilotBufferItemDO pilotBufferItemDO = new PilotBufferItemDO();
        pilotBufferItemDO.setType(BufferItemTypeEnum.MAIN_TEXT.getType());
        pilotBufferItemDO.setData(buildCardContent(StreamEventCardTypeEnum.KNOWLEDGE_CARD, (String) aiServiceContext.getRetrievalMap().get("retrieval")));
        Map<String, Object> extra = Maps.newHashMap();

        List<SourceData> sourceDataList = retrievalResults.stream().map(RetrievalResult::getSource).collect(Collectors.toList());
        extra.put("retrievalResults", sourceDataList);
        pilotBufferItemDO.setExtra(extra);

        RequestContext.writeBuffer(Lists.newArrayList(pilotBufferItemDO));
    }


    private void logAIAnswer(AIServiceContext aiServiceContext, AIAnswerData correctAnswer, List<RetrievalResult> retrievalResults) {
        AssistantSceneContext assistantSceneContext = RequestContext.getAttribute(RequestContextConstants.ASSISTANT_CONTEXT);
        if (assistantSceneContext == null) {
            return;
        }
        Integer assistantType = assistantSceneContext.getAssistantType();
        if (!lionConfigUtil.getLogModelOutputAssistants().contains(assistantType)) {
            return;
        }
        Log2HiveUtils.addLLMAnswerLog(aiServiceContext, correctAnswer, getKnowledgeText(retrievalResults));
    }

}
