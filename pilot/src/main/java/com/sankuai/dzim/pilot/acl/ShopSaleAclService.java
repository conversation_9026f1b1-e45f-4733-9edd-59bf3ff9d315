package com.sankuai.dzim.pilot.acl;

import com.dianping.joy.order.dto.soldcount.BatchQuerySoldCountBySpuIdsReqDTO;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

public interface ShopSaleAclService {

    Integer loadSoldCountByVenueShop(long dpShopId);

    /**
     * 查询预订门店销量
     */
    CompletableFuture<Map<Integer, Integer>> multiGetBookingProductSales(BatchQuerySoldCountBySpuIdsReqDTO reqDTO);
}
