package com.sankuai.dzim.pilot.acl.data.fraiday;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class BulkingData {

    /**
     * 输出文本的时候有值,function-call时为null
     */
    private String content;


    /**
     * 输出推理文本
     */
    private String reasoning_content;

    /**
     * function-call时为assistant
     */
    private String role;

    /**
     * function-call时有该参数
     */
    private List<ToolCallStream> tool_calls;
}
