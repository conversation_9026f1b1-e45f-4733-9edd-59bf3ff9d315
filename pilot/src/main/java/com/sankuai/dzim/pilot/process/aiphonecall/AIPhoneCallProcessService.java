package com.sankuai.dzim.pilot.process.aiphonecall;

import com.sankuai.dzim.pilot.dal.entity.aiphonecall.AIPhoneCallTaskEntity;
import com.sankuai.dzim.pilot.enums.AIPhoneCallBlackTypeEnum;
import com.sankuai.dzim.pilot.enums.AIPhoneCallTaskStatusEnum;
import com.sankuai.dzim.pilot.process.aiphonecall.data.AIPhoneCallTaskCancelReq;
import com.sankuai.dzim.pilot.process.aiphonecall.data.AIPhoneCallTaskCreateReq;
import com.sankuai.dzim.pilot.process.aiphonecall.data.AIPhoneCallTaskCreateRes;
import com.sankuai.dzim.pilot.utils.data.Response;

import java.util.Date;

public interface AIPhoneCallProcessService {

    /**
     * 创建外呼任务
     * @param createReq
     * @return
     */
    Response<AIPhoneCallTaskCreateRes> createAIPhoneCallTask(AIPhoneCallTaskCreateReq createReq);

    /**
     * 外呼拉黑门店
     * @param dpShopId
     * @param blackTypeEnum
     * @param expireTime 黑名单到期时间。若为null，则视为永久拉黑
     * @return
     */
    boolean addShopBlackList(long dpShopId, AIPhoneCallBlackTypeEnum blackTypeEnum, Date expireTime);

    /**
     * 取消外呼任务
     */
    Response<Boolean> cancelAIPhoneCallTask(AIPhoneCallTaskCancelReq cancelReq);
}
