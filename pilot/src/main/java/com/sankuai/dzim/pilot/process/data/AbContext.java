package com.sankuai.dzim.pilot.process.data;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/08/22 13:09
 */
@Data
public class AbContext {
    /**
     * 平台
     */
    private Integer platform;
    /**
     * 设备ID
     */
    private String deviceId;
    /**
     * 设备UnionId
     */
    private String unionId;
    /**
     * 页面类型 1-频道页 2-商详页 3-团详页
     */
    private int sourceType;
    /**
     * @see com.sankuai.dzim.pilot.api.enums.search.generative.GenerativeSearchTypeEnum
     */
    private int bizType;

    /**
     * 城市Id
     */
    private String cityId;

    /**
     * app版本
     */
    private String appVersion;

    /**
     * 操作系统
     */
    private String os;

    /**
     * 用户id
     */
    private String userId;
}
