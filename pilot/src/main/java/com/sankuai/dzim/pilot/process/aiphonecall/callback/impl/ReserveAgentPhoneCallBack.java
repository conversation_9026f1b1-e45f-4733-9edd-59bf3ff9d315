package com.sankuai.dzim.pilot.process.aiphonecall.callback.impl;

import com.alibaba.fastjson.JSON;
import com.dianping.dzim.common.enums.ImUserType;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.dp.arts.common.util.CollectionUtils;
import com.meituan.nibtp.trade.client.combine.response.AgentCreateOrderResDTO;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.acl.ProductAclService;
import com.sankuai.dzim.pilot.acl.ShopAclService;
import com.sankuai.dzim.pilot.acl.UserAclService;
import com.sankuai.dzim.pilot.api.enums.aibook.TaskStatusEnum;
import com.sankuai.dzim.pilot.api.enums.assistant.PlatformEnum;
import com.sankuai.dzim.pilot.chain.data.AIAnswerData;
import com.sankuai.dzim.pilot.enums.AIPhoneCallBlackTypeEnum;
import com.sankuai.dzim.pilot.enums.AIPhoneCallDialogInfoKeyEnum;
import com.sankuai.dzim.pilot.enums.AIPhoneCallSceneTypeEnum;
import com.sankuai.dzim.pilot.enums.AIPhoneCallSourceEnum;
import com.sankuai.dzim.pilot.process.PilotMessageBizRefProcessService;
import com.sankuai.dzim.pilot.process.aibook.AIBookCacheProcessService;
import com.sankuai.dzim.pilot.process.aiphonecall.AIPhoneCallProcessService;
import com.sankuai.dzim.pilot.process.aiphonecall.callback.AIPhoneCallback;
import com.sankuai.dzim.pilot.process.aiphonecall.data.AIPhoneCallBackData;
import com.sankuai.dzim.pilot.process.aireservebook.AIReserveBookProcessService;
import com.sankuai.dzim.pilot.process.aireservebook.AIReserveBookQueryService;
import com.sankuai.dzim.pilot.process.aireservebook.data.*;
import com.sankuai.dzim.pilot.process.aireservebook.enums.POIIndustryType;
import com.sankuai.dzim.pilot.process.aireservebook.enums.ReserveItemType;
import com.sankuai.dzim.pilot.process.aireservebook.enums.ShopReserveWayType;
import com.sankuai.dzim.pilot.process.data.bizref.MessageBizRefData;
import com.sankuai.dzim.pilot.scene.task.data.EnvContext;
import com.sankuai.dzim.pilot.utils.AIChatUtil;
import com.sankuai.dzim.pilot.utils.ReserveBookCardUtils;
import com.sankuai.dzim.pilot.utils.ReserveInfoProcessUtils;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.wpt.user.retrieve.thrift.message.UserModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

import static com.sankuai.dzim.pilot.process.impl.assistant.PilotMessageBizRefProcessServiceImpl.BOOK_FAIL_KEY;
import static com.sankuai.dzim.pilot.process.impl.assistant.PilotMessageBizRefProcessServiceImpl.BOOK_FAIL_TEXT_KEY;

/**
 * @author: wuwenqiang
 * @create: 2025-04-26
 * @description:
 */
@Component
@Slf4j
public class ReserveAgentPhoneCallBack implements AIPhoneCallback {

    public static final String RESERVE_AGENT = "reserveAgent";
    public static final String TYPE = "type";
    public static final String TASK_RESERVE_TYPE = "taskReserveType";
    public static final String ENV_CONTEXT = "envContext";
    public static final String USER_ID = "userId";


    @Autowired
    private PilotMessageBizRefProcessService pilotMessageBizRefProcessService;

    @Autowired
    private AIReserveBookProcessService aiReserveBookProcessService;

    @Autowired
    private AIReserveBookQueryService aiReserveBookQueryService;

    @Autowired
    private AIBookCacheProcessService aiBookCacheProcessService;

    @Autowired
    private UserAclService userAclService;

    @Autowired
    private AIChatUtil aiChatUtil;

    @Autowired
    private AIPhoneCallProcessService aiPhoneCallProcessService;

    @Autowired
    private ShopAclService shopAclService;

    @Autowired
    private ProductAclService productAclService;

    @ConfigValue(key = "com.sankuai.mim.pilot.hair.assistant.card.message.config", defaultValue = "{}")
    private Map<String, String> cardMessageConfig;

    @ConfigValue(key = "com.sankuai.mim.pilot.reserve.book.agent.feedback.config", defaultValue = "{}")
    private Map<String, String> feedBackConfig;

    private static ThreadPool threadPool = Rhino.newThreadPool("ReserveAgentPhoneCallBackPool",
            DefaultThreadPoolProperties.Setter().withCoreSize(50).withMaxSize(100).withMaxQueueSize(100));


    @Override
    public boolean accept(AIPhoneCallBackData callBackData) {
        if (callBackData == null || StringUtils.isEmpty(callBackData.getBizData())
                || callBackData.getAiPhoneCallSceneTypeEnum() == null) {
            return false;
        }
        Map<String, Object> bizData = JSON.parseObject(callBackData.getBizData(), Map.class);
        AIPhoneCallSceneTypeEnum sceneTypeEnum = callBackData.getAiPhoneCallSceneTypeEnum();
        AIPhoneCallSourceEnum sourceEnum = callBackData.getAiPhoneCallSourceEnum();
        if (MapUtils.isNotEmpty(bizData) && RESERVE_AGENT.equals(bizData.get("type"))
            && sceneTypeEnum == AIPhoneCallSceneTypeEnum.RESERVATION && sourceEnum == AIPhoneCallSourceEnum.MAIN_FWLS_AGENT) {
            return true;
        }
        return false;
    }

    @Override
    public void onSuccess(AIPhoneCallBackData callBackData) {
        log.info("[ReserveAgentPhoneCallBack] completed, input={}", JSON.toJSONString(callBackData));
        // 电话拨通后的结果
        // 1. 查询任务记录
        MessageBizRefData<UserReserveInfo> messageBizRefData = queryMessageBizRefData(callBackData);
        if (messageBizRefData == null) {
            return;
        }
        // 2. 校验预约是否成功
        if (!validateAndPush(callBackData, messageBizRefData)) {
            handleReserveFail(callBackData, messageBizRefData);
            return;
        }
        // 3. 发起预约
        handleReserveSuccess(callBackData, messageBizRefData);
    }

    @Override
    public void onFail(AIPhoneCallBackData callBackData) {
        log.info("[ReserveAgentPhoneCallBack] failed, input={}", JSON.toJSONString(callBackData));
        // 电话未拨通
        // 1. 查询任务记录
        MessageBizRefData<UserReserveInfo> messageBizRef = queryMessageBizRefData(callBackData);
        if (messageBizRef == null) {
            return;
        }
        // 2. 刷新失败卡片
        refreshOldBookCard(TaskStatusEnum.BOOK_FAILED, cardMessageConfig.get(BOOK_FAIL_KEY), feedBackConfig.get(ReserveAnswerConstants.PHONE_CALL_FAILED), messageBizRef);
    }

    @Override
    public void onDelay(AIPhoneCallBackData callBackData) {
        // 延迟拨打通知
        // 1. 查询任务记录
        MessageBizRefData<UserReserveInfo> messageBizRefData = queryMessageBizRefData(callBackData);
        if (messageBizRefData == null) {
            return;
        }
        // 2. Todo: 刷新通知
//        refreshOldBookCard(TaskStatusEnum.HAS_TASK_ASKING, feedBackConfig.get(ReserveAnswerConstants.SHOP_NOT_OPEN_DELAY_CALL), null, messageBizRefData);
    }

    @Override
    public void onDelayWakeUp(AIPhoneCallBackData callBackData) {
        // Todo：
    }

    @Override
    public void onCancel(AIPhoneCallBackData callBackData) {
    }

    private boolean validateAndPush(AIPhoneCallBackData callBackData, MessageBizRefData<UserReserveInfo> messageBizRefData) {
        Map<String, Object> callDialogExtractInfo = callBackData.getCallDialogExtractInfo();
        if (!validateShopSupportReserve(callDialogExtractInfo, messageBizRefData)) {
            // 门店不支持预约，推送失败消息
            refreshOldBookCard(TaskStatusEnum.BOOK_FAILED, cardMessageConfig.get(BOOK_FAIL_KEY),
                    feedBackConfig.getOrDefault(ReserveAnswerConstants.SHOP_NOT_SUPPORT, cardMessageConfig.get(BOOK_FAIL_TEXT_KEY)),
                    messageBizRefData);
            return false;
        }
        if (!isReserveSuccess(callDialogExtractInfo)) {
            // 推送预约失败消息
            handleReserveFail(callBackData, messageBizRefData);
            return false;
        }
        return true;
    }

    private boolean validateShopSupportReserve(Map<String, Object> callDialogExtractInfo, MessageBizRefData<UserReserveInfo> messageBizRefData) {
        if (MapUtils.isEmpty(callDialogExtractInfo)) {
            return false;
        }
        // 默认支持预约
        Boolean supportReserve = (Boolean) Optional.ofNullable(callDialogExtractInfo.get(AIPhoneCallDialogInfoKeyEnum.IS_SUPPORT_RESERVATION.getKey())).orElse(true);
        // 不支持预约门店加入黑名单
        if (supportReserve) {
            return true;
        }
        pushShopNotSupport(messageBizRefData);
        return supportReserve;
    }

    private void pushShopNotSupport(MessageBizRefData<UserReserveInfo> messageBizRefData) {
        UserReserveInfo userReserveInfo = messageBizRefData.getBizExtra();
        String imUserId = userReserveInfo.getImUserId();
        Long shopId = userReserveInfo.getShopId();
        int platform = getPlatform(imUserId);
        Long dpShopId = shopId;
        if (platform == PlatformEnum.MT.getType()) {
            dpShopId = shopAclService.loadDpShopIdByMtShopId(shopId);
        }
        if (dpShopId == null) {
            return;
        }
        // 将门店加入黑名单，默认设置7天
        aiPhoneCallProcessService.addShopBlackList(dpShopId, AIPhoneCallBlackTypeEnum.NOT_SUPPORT_RESERVATION, getExpirationDate());
    }

    public Date getExpirationDate() {
        LocalDateTime localDateTime = LocalDateTime.now().plusDays(7);
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    private boolean isReserveSuccess(Map<String, Object> callDialogExtractInfo) {
        if (MapUtils.isEmpty(callDialogExtractInfo)) {
            return false;
        }
        return (Boolean) Optional.ofNullable(callDialogExtractInfo.get(AIPhoneCallDialogInfoKeyEnum.RESERVATION_RESULT.getKey())).orElse(false);
    }

    private void handleReserveFail(AIPhoneCallBackData callBackData, MessageBizRefData<UserReserveInfo> messageBizRefData) {
        Map<String, Object> callDialogExtractInfo = callBackData.getCallDialogExtractInfo();
        if (MapUtils.isEmpty(callDialogExtractInfo)) {
            return;
        }
        // 1.获取失败理由 + 最近可约时间
        String reserveFailReason = (String) Optional.ofNullable(callDialogExtractInfo.get(AIPhoneCallDialogInfoKeyEnum.RESERVATION_FAIL_REASON.getKey())).orElse("");
        String recentTime = (String) Optional.ofNullable(callDialogExtractInfo.get(AIPhoneCallDialogInfoKeyEnum.RECENT_RESERVATION_TIME.getKey())).orElse("");
        StringBuilder bookFailText = new StringBuilder(String.format("预约失败理由: %s", reserveFailReason));
        if (StringUtils.isNotBlank(recentTime)) {
            bookFailText.append(String.format("商家最近可约时间: %s", recentTime));
        }
        String failText = summaryFailText(bookFailText.toString(), cardMessageConfig.get(BOOK_FAIL_TEXT_KEY));
        refreshOldBookCard(TaskStatusEnum.BOOK_FAILED, cardMessageConfig.get(BOOK_FAIL_KEY),
                failText, messageBizRefData);
    }

    private void refreshOldBookCard(TaskStatusEnum taskStatusEnum, String title, String text, MessageBizRefData<UserReserveInfo> messageBizRefData) {
        BookCard bookCard = generateBookCard(taskStatusEnum, title, text);
        pilotMessageBizRefProcessService.refreshOldBookMessageCard(bookCard, messageBizRefData.getBizId(), messageBizRefData, null);
    }

    private String summaryFailText(String input, String defaultText) {
        if (StringUtils.isBlank(input)) {
            return defaultText;
        }
        AIAnswerData answerData = aiChatUtil.chat("reserve_failed_summary", input, null);
        if (answerData != null && StringUtils.isNotBlank(answerData.getAnswer())) {
            return answerData.getAnswer();
        }
        return defaultText;
    }

    private void handleReserveSuccess(AIPhoneCallBackData callBackData, MessageBizRefData<UserReserveInfo> messageBizRefData) {
        try {
            // 1. 解析环境参数
            EnvContext envContext = parseEnvContext(callBackData);
            // 2. 从缓存查询最新用户预约信息
            UserReserveInfo userReserveInfo = queryUserReserveInfo(messageBizRefData);
            // 3. 发起预约流程
            AgentCreateOrderResDTO resp = createOrder(callBackData, envContext, userReserveInfo, messageBizRefData);
            // 4. 后续操作
            if (validateCreateOrderSuccess(resp)) {
                // 4.1 预约成功，刷新缓存和数据库
                userReserveInfo.setReserveId(resp.getMainOrderId());
                refreshCacheAndMessageDB(messageBizRefData, userReserveInfo);
            } else {
                // 4.2 预约失败，返回预约失败卡片
                String message = resp == null || StringUtils.isBlank(resp.getMessage()) ? cardMessageConfig.get(BOOK_FAIL_TEXT_KEY) : resp.getMessage();
                LogUtils.logFailLog(log, TagContext.builder().action("ReserveAgentPhoneCallBack").build(),
                        WarnMessage.build("ReserveAgentPhoneCallBack", "AI外呼回调预约失败", ""),
                        String.format("callBackData:%s, envContext:%s, userReserveInfo:%s",
                                JSON.toJSONString(callBackData), JSON.toJSONString(envContext), JSON.toJSONString(userReserveInfo)), JSON.toJSONString(resp));
                // 刷新预约失败卡片
                String bookFailText = summaryFailText(message, cardMessageConfig.get(BOOK_FAIL_TEXT_KEY));
                refreshOldBookCard(TaskStatusEnum.BOOK_FAILED, cardMessageConfig.get(BOOK_FAIL_KEY), bookFailText, messageBizRefData);
            }
        } catch (Exception e) {
            log.error("[ReserveAgentPhoneCallBack] handleReserveSuccess={}", JSON.toJSONString(callBackData), e);
            // 刷新预约失败卡片
            refreshOldBookCard(TaskStatusEnum.BOOK_FAILED, cardMessageConfig.get(BOOK_FAIL_KEY), cardMessageConfig.get(BOOK_FAIL_TEXT_KEY), messageBizRefData);
        }

    }

    private MessageBizRefData<UserReserveInfo> queryMessageBizRefData(AIPhoneCallBackData callBackData) {
        if (StringUtils.isEmpty(callBackData.getBizId())) {
            return null;
        }
        String bizId = callBackData.getBizId();
        return pilotMessageBizRefProcessService.getMessageBizRef(Long.valueOf(bizId), UserReserveInfo.class);
    }

    private EnvContext parseEnvContext(AIPhoneCallBackData callBackData) {
        Map<String, Object> bizData = JSON.parseObject(callBackData.getBizData(), Map.class);
        Object envContext = bizData.get("envContext");
        return envContext == null ? null : JsonCodec.decode(envContext.toString(), EnvContext.class);
    }

    private UserReserveInfo queryUserReserveInfo(MessageBizRefData<UserReserveInfo> messageBizRefData) {
        UserReserveInfo userReserveInfo = messageBizRefData.getBizExtra();
        String imUserId = userReserveInfo.getImUserId();
        Long shopId = userReserveInfo.getShopId();
        int platform = getPlatform(imUserId);
        return aiReserveBookQueryService.queryUserReserveInfo(imUserId, shopId, platform);
    }

    private AgentCreateOrderResDTO createOrder(AIPhoneCallBackData callBackData, EnvContext envContext, UserReserveInfo userReserveInfo, MessageBizRefData<UserReserveInfo> messageBizRefData) {
        if (envContext == null || userReserveInfo == null) {
            return null;
        }
        String imUserId = userReserveInfo.getImUserId();
        Long shopId = userReserveInfo.getShopId();
        int platform = getPlatform(imUserId);
        // 2. 获取前置信息
        ShopReserveWayType shopReserveWayType = getShopReserveWayType(userReserveInfo);
        CompletableFuture<ShopReserveContext> shopReserveContextF = getShopReserveContext(shopId, platform, shopReserveWayType);
        CompletableFuture<UserModel> userModelF = getUserModelFuture(imUserId);
        ShopReserveContext shopReserveContext = shopReserveContextF == null ? null : shopReserveContextF.join();
        UserModel userModel = userModelF == null ? null : userModelF.join();
        String merchantRemark = getMerchantRemark(callBackData);
        updateUserReserveInfo(userReserveInfo, callBackData.getCallDialogExtractInfo(), merchantRemark);
        // 3. 发起预约
        return aiReserveBookProcessService.createReserve(envContext, userModel, shopReserveContext, userReserveInfo, merchantRemark, false, Lists.newArrayList());
    }

    private boolean validateCreateOrderSuccess(AgentCreateOrderResDTO createOrderRes) {
        return createOrderRes != null && createOrderRes.isSuccess() && createOrderRes.getMainOrderId() != null && createOrderRes.getMainOrderId() > 0;
    }

    private void refreshCacheAndMessageDB(MessageBizRefData<UserReserveInfo> messageBizRefData, UserReserveInfo userReserveInfo) {
        // 1. 刷新缓存
        refreshUserReserveInfo(userReserveInfo);
        // 2. 更新数据库
        messageBizRefData.setBizId(String.valueOf(userReserveInfo.getReserveId()));
        messageBizRefData.setBizExtra(userReserveInfo);
        boolean result = pilotMessageBizRefProcessService.updateMessageBizRef(messageBizRefData);
        if (!result) {
            // 刷新预约失败卡片
            refreshOldBookCard(TaskStatusEnum.BOOK_FAILED, cardMessageConfig.get(BOOK_FAIL_KEY), cardMessageConfig.get(BOOK_FAIL_TEXT_KEY), messageBizRefData);
        }
    }

    private String getMerchantRemark(AIPhoneCallBackData callBackData) {
        Map<String, Object> callDialogExtractInfo = callBackData.getCallDialogExtractInfo();
        return (String) Optional.ofNullable(callDialogExtractInfo.get(AIPhoneCallDialogInfoKeyEnum.RESERVATION_SUCCESS_REMARK.getKey())).orElse("");
    }

    private ShopReserveWayType getShopReserveWayType(UserReserveInfo userReserveInfo) {
        if (userReserveInfo == null) {
            return null;
        }
        return ShopReserveWayType.getByType(userReserveInfo.getShopReserveWayType());
    }

    private CompletableFuture<ShopReserveContext> getShopReserveContext(Long shopId, int platform, ShopReserveWayType shopReserveWayType) {
        return CompletableFuture.supplyAsync(() -> {
            if (shopId == null || shopId <= 0 || shopReserveWayType == null) {
                return null;
            }
            DpPoiDTO dpPoiDTO = aiReserveBookQueryService.queryDpPoiDTO(shopId, platform);
            if (dpPoiDTO == null) {
                return null;
            }
            Integer secondBackCategoryId = aiReserveBookProcessService.extractMainSecondBackCategoryId(dpPoiDTO);
            POIIndustryType poiIndustryType = POIIndustryType.getBySecondBackCategoryId(secondBackCategoryId);
            return ShopReserveContext.buildShopReserveContext(dpPoiDTO, poiIndustryType, shopReserveWayType);
        }, threadPool.getExecutor())
                .exceptionally(e -> {
                    log.error("[ReserveAgentPhoneCallBack] getShopReserveContext error, shopId:{}, platform:{}, shopReserveWayType:{}", shopId, platform, shopReserveWayType, e);
                    return null;
                });
    }

    private CompletableFuture<UserModel> getUserModelFuture(String imUserId) {
        return CompletableFuture.supplyAsync(() -> userAclService.queryUserModel(imUserId), threadPool.getExecutor());
    }

    private void updateUserReserveInfo(UserReserveInfo userReserveInfo, Map<String, Object> callDialogExtractInfo, String merchantRemark) {
        if (userReserveInfo == null || MapUtils.isEmpty(callDialogExtractInfo)) {
            return;
        }
        ReserveInfo reserveInfo = userReserveInfo.getCollectedReserveInfo();
        // 修改预约开始时间
        String startTimeStr = (String) Optional.ofNullable(callDialogExtractInfo.get(AIPhoneCallDialogInfoKeyEnum.RESERVATION_BEGIN_TIME.getKey())).orElse(null);
        if (StringUtils.isNotBlank(startTimeStr)) {
            ReserveInfoProcessUtils.updateItem(reserveInfo, ReserveItemType.START_TIME.getKey(), startTimeStr);
        }
        // 修改预约时长
        String durationStr = String.valueOf(Optional.ofNullable(callDialogExtractInfo.get(AIPhoneCallDialogInfoKeyEnum.RESERVATION_DURATION.getKey())).orElse(""));
        if (StringUtils.isNotBlank(durationStr)) {
            ReserveInfoProcessUtils.updateItem(reserveInfo, ReserveItemType.DURATION.getKey(), durationStr);
        }
        // 增加商家备注
        if (StringUtils.isNotBlank(merchantRemark)) {
            userReserveInfo.setMerchantRemark(merchantRemark);
        }
    }

    private BookCard generateBookCard(TaskStatusEnum taskStatusEnum, String title, String text) {
        BookCard bookCard = new BookCard();
        bookCard.setTaskStatus(taskStatusEnum.getCode());
        switch (taskStatusEnum) {
            case HAS_TASK_ASKING:
                fillProcessCard(bookCard, title);
                break;
            case BOOK_FAILED:
                fillFailCard(bookCard, title, text);
                break;
            default:
                break;
        }
        return bookCard;
    }

    private void fillFailCard(BookCard bookCard, String title, String text) {
        BookSimpleData bookSimpleData = new BookSimpleData();
        bookSimpleData.setTitle(title);
        bookSimpleData.setText(text);
        bookCard.setBookFailData(bookSimpleData);
    }

    private void fillProcessCard(BookCard bookCard, String title) {
        BookProcessData bookProcessData = new BookProcessData();
        bookProcessData.setLoadingText(title);
        bookCard.setBookProcessData(bookProcessData);
    }

    private void refreshUserReserveInfo(UserReserveInfo userReserveInfo) {
        String imUserId = userReserveInfo.getImUserId();
        int platform = getPlatform(imUserId);
        Long shopId = userReserveInfo.getShopId();
        aiBookCacheProcessService.setAgentReserveAndBookInfo(imUserId, shopId, platform, userReserveInfo);
    }

    private int getPlatform(String imUserId) {
        return imUserId.startsWith(ImUserType.MT.getPrefix()) ? PlatformEnum.MT.getType() : PlatformEnum.DP.getType();
    }
}
