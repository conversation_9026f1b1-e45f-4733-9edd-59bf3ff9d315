package com.sankuai.dzim.pilot.gateway.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dianping.vc.mq.client.api.annotation.Consumer;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mtrace.Tracer;
import com.sankuai.dzim.pilot.dal.entity.aiphonecall.AIPhoneCallRecordEntity;
import com.sankuai.dzim.pilot.dal.pilotdao.aiphonecall.AIPhoneCallRecordDAO;
import com.sankuai.dzim.pilot.domain.AIPhoneCallDomainService;
import com.sankuai.dzim.pilot.enums.AIPhoneCallSourceEnum;
import com.sankuai.dzim.pilot.gateway.mq.data.AIPhoneCallRecordData;
import com.sankuai.dzim.pilot.process.aiphonecall.data.AIPhoneCallConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

@Slf4j
@Component
public class AIPhoneCallRecordConsumer {

    @Resource
    private AIPhoneCallDomainService aiPhoneCallDomainService;

    @Resource
    private AIPhoneCallRecordDAO callRecordDAO;

    @Consumer(nameSpace = "daozong",
            appKey = "com.sankuai.mim.pilot",
            group = "pilot.ai.phone.call.record.consumer",
            topic = "pilot.ai.phone.call.record"
    )
    public ConsumeStatus recvMessage(String mafkaMessage) {
        log.info("AIPhoneCallRecordConsumer receive msg = {}", mafkaMessage);
        AIPhoneCallRecordData callRecordData = JsonCodec.decode(mafkaMessage, AIPhoneCallRecordData.class);
        if (callRecordData == null) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        try {
            // 判断是否已处理过
            AIPhoneCallRecordEntity callRecordEntity = callRecordDAO.getByContactId(callRecordData.getContactId());
            if (callRecordEntity != null) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            // 处理通话记录
            aiPhoneCallDomainService.processCallRecord(callRecordData);
        } catch (Exception e) {
            log.error("AIPhoneCallRecordConsumer error, msg = {}", mafkaMessage, e);
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }


    public String getServiceId(String jsonString) {
        if (StringUtils.isEmpty(jsonString)) {
            return null;
        }

        try {
            Map<String, String> map = JSON.parseObject(
                    jsonString,
                    new TypeReference<Map<String, String>>() {}
            );
            return map.get("serviceId");
        } catch (Exception e) {
            log.error("Parse json failed, json: {}", jsonString, e);
            return null;
        }
    }

}
