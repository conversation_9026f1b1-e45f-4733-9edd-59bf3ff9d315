package com.sankuai.dzim.pilot.acl;

import com.sankuai.dzim.message.dto.*;
import com.sankuai.dzim.message.dto.sendmessage.SingleSendMessageRequest;
import com.sankuai.dzim.pilot.utils.data.Response;

import java.util.List;
import java.util.Map;

public interface MessageAclService {

    /**
     * 查询最近消息记录
     *
     * @param chatGroupId
     * @param imAccountId
     * @param limit
     * @return
     */
    List<MessageDTO> getRecentChatMessages(long chatGroupId, String imAccountId, int limit);

    /**
     * 查询某条消息之后的消息列表
     *
     * @param chatGroupId    会话Id
     * @param startMessageId 起始消息Id
     * @param limit          查询消息数量，不超过50
     * @return
     */
    List<MessageDTO> multiGetMessagesForMerchant(long chatGroupId, long startMessageId, int limit);

    /**
     * 分页查询IM消息(商家视角),拼装好的消息
     *
     * @param request
     * @return
     */
    List<ChatMessageDTO> paginateQueryMessageForMerchant(PaginateQueryMessageRequest request);

    /**
     * 根据外部bizId查询对应的消息卡片展示内容
     * @param request
     * @return
     */
    Map<String, Map<String, String>> batchGetMessageUnit(BatchMessageUnitQueryRequest request);

    /**
     * 推送信令消息
     * @return
     */
    boolean pushSignalMessage(SignalMessagePushRequest signalMessagePushRequest);

    /**
     * 推送正在输入中等的信令消息，异步推送信令
     * 发送信令消息 0-结束输入 1-正在输入中
     * @return
     */
    void pushSignalMessageAsync(String fromId, String toId, int input);

    /**
     * 用户和门店之间发送消息
     * @param sendMessageRequest
     * @return
     */
    Response<Long> sendMessage(SingleSendMessageRequest sendMessageRequest);

    /**
     * 发送延迟消息
     * @param sendMessageRequest
     * @return
     */
    Response<String> sendDelayMessage(SingleSendMessageRequest sendMessageRequest, int delaySecs);
}
