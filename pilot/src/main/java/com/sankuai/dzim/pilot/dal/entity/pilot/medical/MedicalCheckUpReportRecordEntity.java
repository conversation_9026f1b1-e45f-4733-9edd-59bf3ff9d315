package com.sankuai.dzim.pilot.dal.entity.pilot.medical;

import lombok.Data;

import java.util.Date;

/**
 * 体检报告记录表
 */
@Data
public class MedicalCheckUpReportRecordEntity {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 体检报告文件名，用;分割
     * 保存的是S3的objectName,访问时需要先获取授权url
     */
    private String reportUrl;

    /**
     * 体检报告图片地址
     */
    private String reportImageUrl;

    /**
     * ocr结果
     */
    private String ocrResult;

    /**
     * 分析结果
     */
    private String analysisResult;

    /**
     * 分析状态，1-解读中，2-解读完成，3-解读失败
     */
    private Integer analysisStatus;

    /**
     * 状态，1有效，0删除
     */
    private Integer status;

    /**
     * 扩展参数
     */
    private String extraData;

    /**
     * 创建时间
     */
    private Date addTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
