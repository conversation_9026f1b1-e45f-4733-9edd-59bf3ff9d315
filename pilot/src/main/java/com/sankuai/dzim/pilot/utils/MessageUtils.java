package com.sankuai.dzim.pilot.utils;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.dzim.message.enums.AuditStatusEnum;
import com.sankuai.dzim.pilot.api.enums.assistant.MessageSendDirectionEnum;
import com.sankuai.dzim.pilot.api.enums.assistant.MessageTypeEnum;
import com.sankuai.dzim.pilot.chain.enums.AIServiceExtraKeyEnum;
import com.sankuai.dzim.pilot.dal.entity.pilot.PilotChatMessageEntity;
import com.sankuai.dzim.pilot.dal.pilotdao.PilotChatGroupMessageDAO;
import com.sankuai.dzim.pilot.gateway.api.vo.ChatGroupExpVO;
import com.sankuai.dzim.pilot.process.data.AssistantConstant;
import com.sankuai.dzim.pilot.process.localplugin.data.UserContext;
import com.sankuai.dzim.pilot.process.localplugin.param.Param;
import com.sankuai.dzim.pilot.scene.data.AssistantSceneContext;
import com.sankuai.dzim.pilot.utils.context.RequestContext;
import com.sankuai.dzim.pilot.utils.context.RequestContextConstants;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class MessageUtils {

    @Autowired
    private IDGenerateUtil iDGenerateUtil;

    @Autowired
    private PilotChatGroupMessageDAO pilotChatGroupMessageDAO;


    public void insertIntoSystemPrompt(String message, Param param) {
        long messageId = iDGenerateUtil.nextMessageId();
        UserContext userContext = PluginContextUtil.getUserContext(param);
        long chatGroupId = userContext.getChatGroupId();
        String imUserId = userContext.getImUserId();
        int assistantType = userContext.getAssistantType();
        //插入模型消息
        PilotChatMessageEntity replyMessageEntity = buildPilotChatMessageEntity(messageId, MessageTypeEnum.TEXT.value,
                chatGroupId, assistantType, imUserId,
                MessageSendDirectionEnum.SYSTEM_SEND.value, AuditStatusEnum.PASS.getCode(),
                message, AssistantConstant.SYSTEM, StringUtils.EMPTY);
        pilotChatGroupMessageDAO.insertMessage(replyMessageEntity);
    }

    private PilotChatMessageEntity buildPilotChatMessageEntity(long messageId, int messageType, long chatGroupId, int assistantType,
                                                               String userId, int direction, int auditStatus, String message,
                                                               String creator, String extra) {
        PilotChatMessageEntity messageEntity = new PilotChatMessageEntity();
        messageEntity.setMessageId(messageId);
        messageEntity.setMessageType(messageType);
        messageEntity.setChatGroupId(chatGroupId);
        messageEntity.setAssistantType(assistantType);
        messageEntity.setUserId(userId);
        messageEntity.setDirection(direction);
        messageEntity.setAuditStatus(auditStatus);
        messageEntity.setExtraData(extra);
        messageEntity.setMessage(message);
        messageEntity.setCreator(creator);
        return messageEntity;
    }

    public static ChatGroupExpVO parseChatGroupExp(String chatGroupStr) {
        if (StringUtils.isNotBlank(chatGroupStr)) {
            return JsonCodec.decode(chatGroupStr, ChatGroupExpVO.class);
        }
        return null;
    }

    public ChatGroupExpVO parseChatGroupExpFromAssistantSceneContext() {
        AssistantSceneContext assistantSceneContext = RequestContext.getAttribute(RequestContextConstants.ASSISTANT_CONTEXT);
        String chatGroupStr = Optional.ofNullable(assistantSceneContext)
                                      .map(AssistantSceneContext::getExtra)
                                      .map(extra -> (String) extra.get(AIServiceExtraKeyEnum.CHAT_GROUP_EXP.getKey()))
                                      .orElse(StringUtils.EMPTY);
        return parseChatGroupExp(chatGroupStr);
    }
}
