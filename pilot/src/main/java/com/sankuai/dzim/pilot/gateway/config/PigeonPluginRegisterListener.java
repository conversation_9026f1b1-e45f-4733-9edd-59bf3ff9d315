package com.sankuai.dzim.pilot.gateway.config;

import com.dianping.lion.client.Lion;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfigListener;
import com.meituan.mdp.boot.starter.config.vo.ConfigEvent;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.domain.plugin.PluginRegistry;
import com.sankuai.dzim.pilot.domain.plugin.data.HttpPluginConfig;
import com.sankuai.dzim.pilot.domain.plugin.data.PigeonPluginConfig;
import com.sankuai.dzim.pilot.domain.plugin.data.PluginSpecification;
import com.sankuai.dzim.pilot.domain.plugin.data.PluginTypeEnum;
import com.sankuai.dzim.pilot.domain.plugin.executor.data.PigeonParamConfig;
import com.sankuai.dzim.pilot.domain.plugin.executor.impl.PigeonPluginExecutor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Http插件注册监听器
 */
@Component
@Slf4j
public class PigeonPluginRegisterListener implements ApplicationRunner {

    private static final String APPKEY = "com.sankuai.mim.pilot";

    private static final String PIGEON_PLUGIN_LION_KEY = "com.sankuai.mim.pilot.pigeon.plugin.configs";

    /**
     * 服务run前,初始化插件
     *
     * @param args
     * @throws Exception
     */
    @Override
    public void run(ApplicationArguments args) throws Exception {
        String configStr = Lion.getString(APPKEY, PIGEON_PLUGIN_LION_KEY);
        List<PigeonPluginConfig> pigeonPluginConfigs = getPigeonPluginConfigs(configStr);
        if (CollectionUtils.isEmpty(pigeonPluginConfigs)) {
            return;
        }

        registerPigeonPlugin(pigeonPluginConfigs);
    }

    /**
     * 监听lion配置的变化
     *
     * @param configEvent
     */
    @MdpConfigListener(PIGEON_PLUGIN_LION_KEY)
    private void listenerLionChange(ConfigEvent configEvent) {
        if (configEvent == null) {
            return;
        }

        // 获取新旧配置
        List<PigeonPluginConfig> oldConfigs = getPigeonPluginConfigs(configEvent.getOldValue());
        List<PigeonPluginConfig> newConfigs = getPigeonPluginConfigs(configEvent.getNewValue());

        // 移除旧插件
        removePigeonPlugin(oldConfigs, newConfigs);

        // 注册新插件
        addPigeonPlugin(oldConfigs, newConfigs);
    }

    private void removePigeonPlugin(List<PigeonPluginConfig> oldConfigs, List<PigeonPluginConfig> newConfigs) {
        if (CollectionUtils.isEmpty(oldConfigs)) {
            return;
        }

        for (PigeonPluginConfig oldConfig : oldConfigs) {
            if (newConfigs.contains(oldConfig)) {
                continue;
            }

            PluginSpecification plugin = PluginRegistry.getPlugin(oldConfig.getName());
            if (plugin == null) {
                continue;
            }
            PluginRegistry.removePlugin(oldConfig.getName());
        }
    }

    private void addPigeonPlugin(List<PigeonPluginConfig> oldConfigs, List<PigeonPluginConfig> newConfigs) {
        if (CollectionUtils.isEmpty(newConfigs)) {
            return;
        }

        List<PigeonPluginConfig> newPigeonPluginConfigs = Lists.newArrayList();

        for (PigeonPluginConfig newConfig : newConfigs) {
            if (oldConfigs.contains(newConfig)) {
                continue;
            }

            newPigeonPluginConfigs.add(newConfig);
        }

        if (CollectionUtils.isEmpty(newPigeonPluginConfigs)) {
            return;
        }

        registerPigeonPlugin(newPigeonPluginConfigs);
    }

    private List<PigeonPluginConfig> getPigeonPluginConfigs(String configStr) {
        if (StringUtils.isBlank(configStr)) {
            return Lists.newArrayList();
        }

        return JsonCodec.converseList(configStr, PigeonPluginConfig.class);
    }

    private void registerPigeonPlugin(List<PigeonPluginConfig> pigeonPluginConfigs) {
        if (CollectionUtils.isEmpty(pigeonPluginConfigs)) {
            return;
        }

        for (PigeonPluginConfig pigeonPluginConfig : pigeonPluginConfigs) {
            PluginSpecification plugin = PluginRegistry.getPlugin(pigeonPluginConfig.getName());
            if (plugin != null) {
                LogUtils.logFailLog(log, TagContext.builder().action("pigeonPluginRegister").bizId(pigeonPluginConfig.getName()).build(),
                        new WarnMessage("pigeonPluginRegisterListener", "pigeon插件注册失败", "存在同名插件"), pigeonPluginConfig, null);
                continue;
            }

            PluginSpecification pigeonPlugin = getPigeonPlugin(pigeonPluginConfig);
            if (pigeonPlugin == null) {
                continue;
            }
            PluginRegistry.addPlugin(pigeonPlugin);
        }
    }

    private PluginSpecification getPigeonPlugin(PigeonPluginConfig pigeonPluginConfig) {
        if (StringUtils.isBlank(pigeonPluginConfig.getUrl())) {
            LogUtils.logFailLog(log, TagContext.builder().action("pigeonPluginRegister").bizId(pigeonPluginConfig.getName()).build(),
                    new WarnMessage("pigeonPluginRegisterListener", "pigeon插件注册失败", "缺少url"), pigeonPluginConfig, null);
            return null;
        }

        if (StringUtils.isBlank(pigeonPluginConfig.getDescription())) {
            LogUtils.logFailLog(log, TagContext.builder().action("pigeonPluginRegister").bizId(pigeonPluginConfig.getName()).build(),
                    new WarnMessage("pigeonPluginRegisterListener", "PigeonPluginConfig", "缺少插件描述"), pigeonPluginConfig, null);
            return null;
        }

        if (pigeonPluginConfig.getParams() == null) {
            LogUtils.logFailLog(log, TagContext.builder().action("httpPluginRegister").bizId(pigeonPluginConfig.getName()).build(),
                    new WarnMessage("pigeonPluginRegisterListener", "PigeonPluginConfig", "缺少参数描述"), pigeonPluginConfig, null);
            return null;
        }

        return PluginSpecification.builder()
                .type(PluginTypeEnum.PIGEON.getType())
                .name(pigeonPluginConfig.getName())
                .description(pigeonPluginConfig.getDescription())
                .returnDirect(pigeonPluginConfig.isReturnDirect())
                .params(pigeonPluginConfig.getParams())
                .executor(new PigeonPluginExecutor(pigeonPluginConfig.getUrl(), pigeonPluginConfig.getMethod(), pigeonPluginConfig.getTimeout()
                        , pigeonPluginConfig.getAppkey(), pigeonPluginConfig.getParamConfigs()))
                .build();
    }
}
