package com.sankuai.dzim.pilot.process.aibook.data;

import lombok.Data;

@Data
public class AIBookSubmitRequest {

    private long userId;

    private int platform;

    private int timePeriodId;

    private long shopId;

    private String shopIds;

    private int cityId;

    private int recommendSwitch;

    private String userPhone;

    private int bookType;

    private String naviFilterId;

    private int naviType;

    private String naviName;

    private double lng;

    private double lat;

    /**
     * 点评:dpid
     * 美团:uuid
     */
    private String deviceId;

    private String version;

    private long bookStartTime;

    private long bookEndTime;

    private Integer priceId;

}
