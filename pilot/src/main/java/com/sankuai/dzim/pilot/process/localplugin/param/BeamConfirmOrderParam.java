package com.sankuai.dzim.pilot.process.localplugin.param;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import lombok.Data;

/**
 *  Beam团购订单预览工具
 *
 * <AUTHOR>
 * @date 2025/5/6
 */
@Data
public class BeamConfirmOrderParam extends Param {

    @JsonPropertyDescription("Extracted product id or deal id from context, do not extract the product display serial number as the productId.")
    @JsonProperty(required = true)
    private Long productId;

    @JsonPropertyDescription("Extracted product type from context. If not mentioned, the default value is empty string.")
    @JsonProperty(required = true)
    private String productType;

    @JsonPropertyDescription("The quantity of the product placed by the user is extracted from the user input. If not mentioned, the default value is 1")
    @JsonProperty(required = false)
    private Integer quantity;

    public Integer getQuantity() {
        if (quantity == null) {
            return 1;
        }
        return quantity;
    }
}
