package com.sankuai.dzim.pilot.process.localplugin;

import com.alibaba.fastjson.JSONObject;
import com.dianping.dzim.common.enums.ImUserType;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.message.common.utils.ImAccountTypeUtils;
import com.sankuai.dzim.pilot.acl.UserAclService;
import com.sankuai.dzim.pilot.api.data.AIAnswerTypeEnum;
import com.sankuai.dzim.pilot.buffer.core.PilotBufferItemDO;
import com.sankuai.dzim.pilot.buffer.enums.BufferItemTypeEnum;
import com.sankuai.dzim.pilot.buffer.utils.BufferUtils;
import com.sankuai.dzim.pilot.chain.data.AIAnswerData;
import com.sankuai.dzim.pilot.domain.annotation.LocalPlugin;
import com.sankuai.dzim.pilot.gateway.api.aibook.data.BookingPriceDetailData;
import com.sankuai.dzim.pilot.process.aibook.AIBookCacheProcessService;
import com.sankuai.dzim.pilot.process.aibook.data.DistanceRangeEnum;
import com.sankuai.dzim.pilot.process.aibook.data.UserBookRequest;
import com.sankuai.dzim.pilot.process.localplugin.param.HaircutBookConfirmParam;
import com.sankuai.dzim.pilot.scene.data.AssistantExtraConstant;
import com.sankuai.dzim.pilot.scene.data.AssistantSceneContext;
import com.sankuai.dzim.pilot.scene.task.data.TaskBizParamData;
import com.sankuai.dzim.pilot.utils.LionConfigUtil;
import com.sankuai.dzim.pilot.utils.PoiExtractUtil;
import com.sankuai.dzim.pilot.utils.context.RequestContext;
import com.sankuai.dzim.pilot.utils.context.RequestContextConstants;
import com.sankuai.wpt.user.retrieve.thrift.message.UserModel;
import lombok.extern.slf4j.Slf4j;
import mtmap.geoinfo.geoinfo_base.Poi;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/02/24 17:02
 */
@Component
@Slf4j
public class HaircutBookConfirmPlugin {

    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm:ss");

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    @ConfigValue(key = "com.sankuai.mim.pilot.haircut.book.confirm.feedback.config", defaultValue = "{}")
    private Map<String, String> feedBackConfig;

    private final String FEEDBACK_CONFIG_SWITCH = "feedbackConfigSwitch";

    private static final String MANUAL_HIGHLIGHT_SWITCH = "manualHighlightSwitch";

    // 位置提取不准确或缺失时反馈
    private final String POSITION_FEEDBACK_ANSWER = "positionFeedbackAnswer";

    // 预约时间不准确或缺失时反馈
    private final String DATE_LACK_FEEDBACK_ANSWER = "dateLackFeedbackAnswer";

    // 预约时间不符合预期时反馈(可能是过去时间、超过7天、开始时间大于结束时间等)
    private final String DATE_OUT_LIMIT_FEEDBACK_ANSWER = "dateOutLimitFeedbackAnswer";

    // 预约时间格式不正确(不太可能触发,有做强制处理)
    private static final String DATE_FORMAT_ERROR_FEEDBACK_ANSWER = "dateFormatErrorFeedbackAnswer";

    // 预约时间范围(默认是7,表示未来7天内)
    private final String DAYS_LIMIT = "daysLimit";

    // 信息检验正确但大模型没有给确认信息内容时的兜底话术
    private static final String DATA_CONFIRM_ANSWER = "dataConfirmAnswer";

    // 预订项目类型错误时反馈
    private static final String BOOK_TYPE_ERROR_FEEDBACK_ANSWER = "bookTypeErrorFeedbackAnswer";

    // 插件异常的兜底话术
    private static final String ERROR_FEEDBACK_ANSWER = "errorFeedbackAnswer";

    // 手机号异常的兜底话术
    private static final String PHONE_ERROR_FEEDBACK_ANSWER = "phoneErrorFeedbackAnswer";

    // 确认语，默认是"将为你预约"
    private static final String CONFIRM_WORD = "confirmWord";

    @Autowired
    private UserAclService userAclService;

    @Autowired
    private PoiExtractUtil poiExtractUtil;

    @Autowired
    private LionConfigUtil lionConfigUtil;

    @Autowired
    private AIBookCacheProcessService aiBookCacheProcessService;

    @ConfigValue(key = "com.sankuai.mim.pilot.haircut.operate.question.answer", defaultValue = "{}")
    private Map<String, String> operateQuestionAnswer;

    @LocalPlugin(name = "HaircutBookConfirmTool", description = "预约信息处理工具,当用户提供的有预约相关信息时就必须使用", returnDirect = false)
    public AIAnswerData confirmHaircutBook(HaircutBookConfirmParam haircutBookConfirmParam) {
        AIAnswerData aiAnswerData = new AIAnswerData();
        try {
            updateBookConfirmParamCache(haircutBookConfirmParam);
            if (haircutBookConfirmParam == null) {
                return new AIAnswerData();
            }
            UserBookRequest userBookRequest = new UserBookRequest();
            BeanUtils.copyProperties(haircutBookConfirmParam, userBookRequest);
            AssistantSceneContext assistantSceneContext = RequestContext.getAttribute(RequestContextConstants.ASSISTANT_CONTEXT);
            if (assistantSceneContext == null) {
                return new AIAnswerData();
            }
            String bookKey = assistantSceneContext.getUserId();

            if (aiBookCacheProcessService.containsBookCache(bookKey)) {
                userBookRequest = aiBookCacheProcessService.getBookCache(bookKey);
            }
            setBasicInfo(userBookRequest, haircutBookConfirmParam);
            setPositionInfo(userBookRequest, haircutBookConfirmParam);
            setBookDateTime(userBookRequest, haircutBookConfirmParam);
            setOptionalInfo(userBookRequest, haircutBookConfirmParam);
            // 缓存当前提前的数据
            aiBookCacheProcessService.setBookCache(bookKey, userBookRequest);

            aiAnswerData.setAiAnswerType(AIAnswerTypeEnum.TEXT.getType());
            // 运营配置反馈
            String feedback = checkAndGetFeedback(userBookRequest);
            if (StringUtils.isBlank(feedback)) {
                // 数据齐全
                if (StringUtils.isNotBlank(haircutBookConfirmParam.getConfirmInfo())) {
                    fillConfirmInfo(haircutBookConfirmParam);
                    aiAnswerData.setAnswer(haircutBookConfirmParam.getConfirmInfo() + "\n\n" + getLast4Digits(userBookRequest.getUserPhone()));
                } else {
                    aiAnswerData.setAnswer(feedBackConfig.get(DATA_CONFIRM_ANSWER) + "\n\n" + getLast4Digits(userBookRequest.getUserPhone()));
                }
                sendMessage(aiAnswerData);
                sendCard();
                aiAnswerData.setReturnDirect(true);
                return aiAnswerData;
            } else {
                // 数据缺失
                aiAnswerData.setAnswer(feedback);
                if (StringUtils.isNotBlank(haircutBookConfirmParam.getErrorInfo())) {
                    aiAnswerData.setAnswer(haircutBookConfirmParam.getErrorInfo());
                }
            }
            if (Boolean.parseBoolean(feedBackConfig.getOrDefault(FEEDBACK_CONFIG_SWITCH, "false"))) {
                // 直接返回运营配置的反馈回答
                sendMessage(aiAnswerData);
                aiAnswerData.setReturnDirect(true);
            }
            return aiAnswerData;
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("confirmHaircutBook").build(),
                    new WarnMessage("haircutBookConfirmParam", "美发预约插件异常", ""), haircutBookConfirmParam, e);
            log.error("confirmHaircutBook error, haircutBookConfirmParam:{}", JsonCodec.encode(haircutBookConfirmParam), e);
            aiAnswerData.setAnswer(feedBackConfig.get(ERROR_FEEDBACK_ANSWER));
            sendMessage(aiAnswerData);
            aiAnswerData.setReturnDirect(true);
            return aiAnswerData;
        }
    }

    private void fillConfirmInfo(HaircutBookConfirmParam haircutBookConfirmParam) {
        String confirmInfo = haircutBookConfirmParam.getConfirmInfo();
        if (StringUtils.isBlank(confirmInfo) || !Boolean.parseBoolean(feedBackConfig.getOrDefault(MANUAL_HIGHLIGHT_SWITCH, "true"))) {
            return;
        }
        if (confirmInfo.contains("**")) {
            return;
        }
        String confirmWord = feedBackConfig.getOrDefault(CONFIRM_WORD, "为你预约");
        if (confirmInfo.contains(confirmWord)) {
            confirmInfo = confirmInfo.replace(confirmWord, confirmWord + "**");
            confirmInfo += "**";
        }
        haircutBookConfirmParam.setConfirmInfo(confirmInfo);
    }

    private void sendCard() {
        final String CONFIRM_CARD = "预约卡片";
        String operateAnswer = operateQuestionAnswer.get(CONFIRM_CARD);
        JSONObject answerObj = JSONObject.parseObject(operateAnswer);
        PilotBufferItemDO pilotBufferItemDO = new PilotBufferItemDO();
        pilotBufferItemDO.setType(BufferItemTypeEnum.MAIN_TEXT.getType());
        pilotBufferItemDO.setData(answerObj.getString("content"));
        Map<String, Object> extra = answerObj.getJSONObject("extra");
        pilotBufferItemDO.setExtra(extra);
        BufferUtils.writeMainTextBuffer(pilotBufferItemDO);
    }

    private String getLast4Digits(String userPhone) {
        if (StringUtils.isBlank(userPhone) || userPhone.length() != 11) {
            return StringUtils.EMPTY;
        }

        String suffix = userPhone.substring(userPhone.length() - 4);
        return "将预留尾号**" + suffix + "**的手机号.";
    }

    private void updateBookConfirmParamCache(HaircutBookConfirmParam haircutBookConfirmParam) {
        AssistantSceneContext assistantSceneContext = RequestContext.getAttribute(RequestContextConstants.ASSISTANT_CONTEXT);
        if (assistantSceneContext == null) {
            return;
        }
        HaircutBookConfirmParam cacheBookInfo = new HaircutBookConfirmParam();
        if (aiBookCacheProcessService.containsOriginBookInfo(assistantSceneContext.getUserId())) {
            cacheBookInfo = aiBookCacheProcessService.getOriginBookInfo(assistantSceneContext.getUserId());
        }
        BeanUtils.copyProperties(haircutBookConfirmParam, cacheBookInfo);
        aiBookCacheProcessService.setOriginBookInfo(assistantSceneContext.getUserId(), cacheBookInfo);
    }

    private void setBasicInfo(UserBookRequest userBookRequest, HaircutBookConfirmParam haircutBookConfirmParam) {
        AssistantSceneContext assistantSceneContext = RequestContext.getAttribute(RequestContextConstants.ASSISTANT_CONTEXT);
        if (assistantSceneContext != null) {
            userBookRequest.setUserId(ImAccountTypeUtils.getAccountId(assistantSceneContext.getUserId()));
            userBookRequest.setPlatform(assistantSceneContext.getUserId().startsWith(ImUserType.MT.getPrefix()) ? 2 : 1);
        }
        userBookRequest.setPosition(haircutBookConfirmParam.getPositionInfo());
        if (StringUtils.isEmpty(haircutBookConfirmParam.getPositionInfo())) {
            if (CollectionUtils.isNotEmpty(haircutBookConfirmParam.getShopNames())) {
                userBookRequest.setPosition(haircutBookConfirmParam.getShopNames().stream().collect(Collectors.joining(",")));
            } else if (haircutBookConfirmParam.getDistanceId() != null) {
                int distanceId = haircutBookConfirmParam.getDistanceId().intValue();
                for (DistanceRangeEnum distanceRangeEnum : DistanceRangeEnum.values()) {
                    if (distanceId == distanceRangeEnum.getCode()) {
                        userBookRequest.setPosition(distanceRangeEnum.getDesc());
                        break;
                    }
                }
            }
        }

        TaskBizParamData taskBizParamData = getTaskBizParamData();
        if (taskBizParamData != null) {
            userBookRequest.setCityId(taskBizParamData.getCityId());
            userBookRequest.setLat(taskBizParamData.getLat());
            userBookRequest.setLng(taskBizParamData.getLng());
        }
    }

    private String checkAndGetFeedback(UserBookRequest userBookRequest) {
        if (userBookRequest.getBookType() <= 0) {
            return feedBackConfig.get(BOOK_TYPE_ERROR_FEEDBACK_ANSWER);
        }
        if (StringUtils.isNotBlank(userBookRequest.getUserPhone())) {
            String userPhone = userBookRequest.getUserPhone();
            if (userPhone.length() != 11 || !userPhone.startsWith("1")) {
                return feedBackConfig.get(PHONE_ERROR_FEEDBACK_ANSWER);
            }
        }
        String feedback = checkPositionAndGetFeedback(userBookRequest);
        if (StringUtils.isNotBlank(feedback)) {
            return feedback;
        }
        return checkBookDateTimeAndGetFeedback(userBookRequest);
    }

    private String checkBookDateTimeAndGetFeedback(UserBookRequest userBookRequest) {
        if (userBookRequest.getBookStartTime() == null || userBookRequest.getBookEndTime() == null) {
            return feedBackConfig.get(DATE_LACK_FEEDBACK_ANSWER);
        }

        LocalDateTime bookEndTime = LocalDateTime.ofInstant(userBookRequest.getBookEndTime().toInstant(), ZoneId.systemDefault());
        LocalDateTime bookStartTime = LocalDateTime.ofInstant(userBookRequest.getBookStartTime().toInstant(), ZoneId.systemDefault());
        LocalDateTime now = LocalDateTime.now();

        if (bookStartTime.getHour() < 10 || bookStartTime.getHour() > 21 ||
                bookEndTime.getHour() < 10 || bookEndTime.getHour() > 21 ||
                bookStartTime.isAfter(bookEndTime) ||
                bookEndTime.isBefore(now)) {
            clearBookDateTimeCache(userBookRequest);
            return feedBackConfig.get(DATE_OUT_LIMIT_FEEDBACK_ANSWER);
        }
        return checkBookTimeLimit(bookStartTime, bookEndTime, now, userBookRequest);
    }

    private String checkBookTimeLimit(LocalDateTime bookStartTime, LocalDateTime bookEndTime, LocalDateTime now, UserBookRequest userBookRequest) {
        if (bookEndTime.isAfter(now.plusDays(Integer.parseInt(feedBackConfig.getOrDefault(DAYS_LIMIT, "7"))))) {
            clearBookDateTimeCache(userBookRequest);
            return feedBackConfig.get(DATE_OUT_LIMIT_FEEDBACK_ANSWER);
        }
        if (bookStartTime.getSecond() != 0
                || bookEndTime.getSecond() != 0
                || (bookStartTime.getMinute() != 0 && bookStartTime.getMinute() != 30)
                || (bookEndTime.getMinute() != 0 && bookEndTime.getMinute() != 30)) {
            clearBookDateTimeCache(userBookRequest);
            return feedBackConfig.get(DATE_FORMAT_ERROR_FEEDBACK_ANSWER);
        }
        return StringUtils.EMPTY;
    }

    private void clearBookDateTimeCache(UserBookRequest userBookRequest) {
        userBookRequest.setBookStartTime(null);
        userBookRequest.setBookEndTime(null);
        AssistantSceneContext assistantSceneContext = RequestContext.getAttribute(RequestContextConstants.ASSISTANT_CONTEXT);
        if (assistantSceneContext == null) {
            return;
        }
        String bookKey = assistantSceneContext.getUserId();
        aiBookCacheProcessService.setBookCache(bookKey, userBookRequest);
    }

    private String checkPositionAndGetFeedback(UserBookRequest userBookRequest) {
        if (CollectionUtils.isEmpty(userBookRequest.getRegionId())
                && CollectionUtils.isEmpty(userBookRequest.getShopIds())
                && StringUtils.isBlank(userBookRequest.getDistanceRange())) {
            return feedBackConfig.get(POSITION_FEEDBACK_ANSWER);
        }
        return StringUtils.EMPTY;
    }

    private void setOptionalInfo(UserBookRequest userBookRequest, HaircutBookConfirmParam haircutBookConfirmParam) {
        AssistantSceneContext assistantSceneContext = RequestContext.getAttribute(RequestContextConstants.ASSISTANT_CONTEXT);
        // 手机号
        if (StringUtils.isBlank(userBookRequest.getUserPhone()) && assistantSceneContext != null) {
            String userMobile = queryUserPhoneByUserId(assistantSceneContext.getUserId());
            userBookRequest.setUserPhone(userMobile);
        }
        String userPhone = haircutBookConfirmParam.getUserPhone();
        if (StringUtils.isNotBlank(userPhone)) {
            userBookRequest.setUserPhone(userPhone);
        }

        // 价格区间
        Integer priceId = haircutBookConfirmParam.getPriceId();
        if (priceId != null) {
            Pair<Integer, Integer> minMaxPrice = getMinMaxPrice(haircutBookConfirmParam.getPriceId(), haircutBookConfirmParam.getBookType());
            if (minMaxPrice.getLeft() != null) {
                userBookRequest.setLowerPrice(minMaxPrice.getLeft());
                userBookRequest.setUpperPrice(minMaxPrice.getRight());
            }
        }
    }

    private String queryUserPhoneByUserId(String imUserId) {
        long userId = ImAccountTypeUtils.getAccountId(imUserId);
        if (ImAccountTypeUtils.isDpUserId(imUserId)) {
            userId = userAclService.getMtRealBindUserId(userId);
        }
        UserModel userModel = userAclService.getMtUserModel(userId);
        if (userModel == null) {
            return StringUtils.EMPTY;
        }
        return userModel.getMobile();
    }

    private Pair<Integer, Integer> getMinMaxPrice(Integer priceId, int bookType) {
        if (priceId == null) {
            return Pair.of(null, null);
        }

        Map<String, List<BookingPriceDetailData>> bookingPriceData = lionConfigUtil.getBookingPriceData();
        List<BookingPriceDetailData> bookingPriceDetailDataList = bookingPriceData.get(String.valueOf(bookType));
        if (CollectionUtils.isEmpty(bookingPriceDetailDataList)) {
            return Pair.of(null, null);
        }

        BookingPriceDetailData bookingPriceDetailData = bookingPriceDetailDataList.stream()
                .filter(priceDetail -> priceDetail.getPriceId() == priceId)
                .findFirst().orElse(null);
        if (bookingPriceDetailData == null) {
            return Pair.of(null, null);
        }

        return Pair.of(bookingPriceDetailData.getMinPrice(), bookingPriceDetailData.getMaxPrice());
    }

    private void setBookDateTime(UserBookRequest userBookRequest, HaircutBookConfirmParam haircutBookConfirmParam) {
        Pair<Date, Date> bookDateTime = buildBookDate(haircutBookConfirmParam);
        userBookRequest.setBookStartTime(bookDateTime.getLeft());
        userBookRequest.setBookEndTime(bookDateTime.getRight());
    }

    private void setPositionInfo(UserBookRequest userBookRequest, HaircutBookConfirmParam haircutBookConfirmParam) {
        // 具体商户
        List<Long> shopIds = queryMtShopIdsByNames(haircutBookConfirmParam.getShopNames());
        if (CollectionUtils.isNotEmpty(shopIds)) {
            userBookRequest.setShopIds(shopIds);
            return;
        }

        // 商圈
        TaskBizParamData taskBizParamData = getTaskBizParamData();
        if (taskBizParamData != null && StringUtils.isNotBlank(haircutBookConfirmParam.getPositionInfo())) {
            List<Integer> regionIds = queryRegionIds(haircutBookConfirmParam.getPositionInfo(), taskBizParamData.getLng(), taskBizParamData.getLat(), taskBizParamData.getCityId());
            if (CollectionUtils.isNotEmpty(regionIds)) {
                userBookRequest.setRegionId(regionIds);
                return;
            }
        }

        // 位置距离
        Integer distanceId = haircutBookConfirmParam.getDistanceId();
        if (distanceId != null) {
            userBookRequest.setDistanceRange(DistanceRangeEnum.getByCode(distanceId).getValue());
            return;
        }
    }

    private TaskBizParamData getTaskBizParamData() {
        AssistantSceneContext assistantSceneContext = RequestContext.getAttribute(RequestContextConstants.ASSISTANT_CONTEXT);
        String bizParams = Optional.ofNullable(assistantSceneContext)
                .map(AssistantSceneContext::getExtra)
                .map(extra -> MapUtils.getString(extra, AssistantExtraConstant.MESSAGE_BIZ_PARAMS))
                .orElse("");
        if (StringUtils.isNotBlank(bizParams)) {
            return JsonCodec.decode(bizParams, TaskBizParamData.class);
        }
        return null;
    }

    private List<Long> queryMtShopIdsByNames(List<String> shopNames) {
        TaskBizParamData taskBizParamData = getTaskBizParamData();
        if (taskBizParamData == null || CollectionUtils.isEmpty(shopNames)) {
            return Lists.newArrayList();
        }
        return shopNames.stream()
                .map(shopName -> queryMtShopIdByName(shopName, taskBizParamData.getLng(), taskBizParamData.getLat(), taskBizParamData.getCityId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private Long queryMtShopIdByName(String shopName, Double lng, Double lat, Integer cityId) {
        List<Poi> pois = poiExtractUtil.queryPoiByKeyword(shopName, cityId, lat, lng);
        return pois.stream()
                .filter(poi -> CollectionUtils.isNotEmpty(poi.getMt_ids()))
                .findFirst()
                .map(poi -> Long.parseLong(poi.getMt_ids().get(0)))
                .orElse(null);
    }


    private List<Integer> queryRegionIds(String positionInfo, Double lng, Double lat, Integer cityId) {
        List<Poi> pois = poiExtractUtil.queryPoiByKeyword(positionInfo, cityId, lat, lng);
        return pois.stream().findFirst().map(Poi::getMt_front_area_ids).orElse(Lists.newArrayList());
    }


    private void sendMessage(AIAnswerData aiAnswerData) {
        PilotBufferItemDO pilotBufferItemDO = new PilotBufferItemDO();
        pilotBufferItemDO.setType(BufferItemTypeEnum.MAIN_TEXT.getType());
        pilotBufferItemDO.setData(aiAnswerData.getAnswer());
        BufferUtils.writeMainTextBuffer(pilotBufferItemDO);
    }

    private Pair<Date, Date> buildBookDate(HaircutBookConfirmParam haircutBookConfirmParam) {
        String bookDayStr = haircutBookConfirmParam.getBookDay();
        if (StringUtils.isBlank(bookDayStr)) {
            bookDayStr = "null";
        }
        try {
            // 日期维度
            LocalDate bookDate = LocalDate.now();
            if ("今天".equals(bookDayStr)) {
                bookDate = LocalDate.now();
            } else if ("明天".equals(bookDayStr)) {
                bookDate = bookDate.plusDays(1);
            } else if ("后天".equals(bookDayStr)) {
                bookDate = bookDate.plusDays(2);
            } else if ("大后天".equals(bookDayStr)) {
                bookDate = bookDate.plusDays(3);
            } else if ("现在".equals(bookDayStr)) {
                LocalDateTime now = LocalDateTime.now();
                LocalDateTime startDateTime = now.withSecond(0).withNano(0);
                if (now.getMinute() < 30) {
                    startDateTime = startDateTime.withMinute(30);
                } else {
                    startDateTime = startDateTime.withMinute(0).plusHours(1);
                }
                LocalDateTime endDateTime = startDateTime.plusHours(2);
                Date start = Date.from(startDateTime.atZone(ZoneId.systemDefault()).toInstant());
                Date end = Date.from(endDateTime.atZone(ZoneId.systemDefault()).toInstant());
                return Pair.of(start, end);
            } else {
                if (StringUtils.isBlank(haircutBookConfirmParam.getBookDate())) {
                    return Pair.of(null, null);
                }
                //todo 跨年的时候可能存在问题
                String fullDate = LocalDate.now().getYear() + "-" + haircutBookConfirmParam.getBookDate();
                bookDate = LocalDate.parse(fullDate, DATE_FORMATTER);
            }

            // 时间维度
            String bookStartTime = haircutBookConfirmParam.getBookStartTime();
            bookStartTime = fillTime(bookStartTime);
            LocalTime startTime = LocalTime.parse(bookStartTime, TIME_FORMATTER);
            String bookEndTime = haircutBookConfirmParam.getBookEndTime();
            bookEndTime = fillTime(bookEndTime);
            LocalTime endTime = LocalTime.parse(bookEndTime, TIME_FORMATTER);
            // 拼接
            LocalDateTime startDateTime = LocalDateTime.of(bookDate, startTime);
            LocalDateTime endDateTime = LocalDateTime.of(bookDate, endTime);
            Date start = Date.from(startDateTime.atZone(ZoneId.systemDefault()).toInstant());
            Date end = Date.from(endDateTime.atZone(ZoneId.systemDefault()).toInstant());
            return Pair.of(start, end);
        } catch (Exception e) {
            log.error("buildBookDate error, haircutBookConfirmParam:{}", JsonCodec.encodeWithUTF8(haircutBookConfirmParam), e);
            return Pair.of(null, null);
        }
    }

    private String fillTime(String time) {
        if (StringUtils.isBlank(time)) {
            return StringUtils.EMPTY;
        }
        if (StringUtils.countMatches(time, ":") == 2) {
            return time;
        }
        return time + ":00";
    }
}
