package com.sankuai.dzim.pilot.acl;

import com.dianping.account.dto.UserAccountDTO;
import com.meituan.data.ups.thrift.LabelData;
import com.sankuai.wpt.user.retrieve.thrift.message.UserModel;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/1/9 19:19
 */
public interface UserAclService {


    /**
     * 查询点评用户信息
     *
     * @param dpUserId dpUserId
     * @return
     */
    UserAccountDTO getDpUserAccountDTO(long dpUserId);


    /**
     * 查询美团用户ID
     *
     * @param mtUserId mtUserId
     * @return
     */
    UserModel getMtUserModel(long mtUserId);


    /**
     * 点评用户转美团
     *
     * @param dpUserId dpUserId
     * @return
     */
    Long getMtRealBindUserId(long dpUserId);

    /**
     * 美团用户转点评
     *
     * @param mtUserId dpUserId
     * @return
     */
    Long getDpRealBindUserId(long mtUserId);


    /**
     * 查询Persona画像
     *
     * @param mtUserId 用户Id
     * @param tagIds Persona画像ID
     * @return
     */
    List<LabelData> getUserProfile(long mtUserId, List<Integer> tagIds);

    /**
     * 根据手机号查询美团用户
     * @param userPhone
     * @return
     */
    UserModel getMtUserByMobile(String userPhone);

    /**
     * 查询用户手机号
     * @param userId
     * @param platform
     * @return
     */
    String queryUserPhone(long userId, int platform);

    /**
     * 查询用户信息
     * @param imUserId
     * @return
     */
    UserModel queryUserModel(String imUserId);
}
