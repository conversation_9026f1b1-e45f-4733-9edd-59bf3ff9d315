package com.sankuai.dzim.pilot.process.localplugin.beta;

import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.dzim.message.dto.MessageDTO;
import com.sankuai.dzim.message.enums.AuditStatusEnum;
import com.sankuai.dzim.pilot.acl.MapAclService;
import com.sankuai.dzim.pilot.acl.ProductAclService;
import com.sankuai.dzim.pilot.acl.ShopAclService;
import com.sankuai.dzim.pilot.api.enums.assistant.MessageSendDirectionEnum;
import com.sankuai.dzim.pilot.api.enums.assistant.MessageTypeEnum;
import com.sankuai.dzim.pilot.buffer.core.PilotBufferItemDO;
import com.sankuai.dzim.pilot.buffer.utils.BufferUtils;
import com.sankuai.dzim.pilot.chain.data.AIAnswerData;
import com.sankuai.dzim.pilot.chain.data.AIServiceContext;
import com.sankuai.dzim.pilot.dal.entity.pilot.PilotChatGroupEntity;
import com.sankuai.dzim.pilot.dal.entity.pilot.PilotChatMessageEntity;
import com.sankuai.dzim.pilot.dal.entity.pilot.ReviewSyncEntity;
import com.sankuai.dzim.pilot.dal.entity.pilot.TechnicianSyncEntity;
import com.sankuai.dzim.pilot.dal.pilotdao.PilotChatGroupDAO;
import com.sankuai.dzim.pilot.dal.pilotdao.PilotChatGroupMessageDAO;
import com.sankuai.dzim.pilot.dal.pilotdao.ReviewSyncDAO;
import com.sankuai.dzim.pilot.dal.pilotdao.TechnicianSyncDAO;
import com.sankuai.dzim.pilot.dal.respository.ReviewRepositoryService;
import com.sankuai.dzim.pilot.domain.annotation.LocalPlugin;
import com.sankuai.dzim.pilot.process.data.AssistantConstant;
import com.sankuai.dzim.pilot.process.localplugin.param.RecommendParam;
import com.sankuai.dzim.pilot.utils.IDGenerateUtil;
import com.sankuai.dzim.pilot.utils.IMConstants;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.detail.DealGroupTemplateDetailDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.MustServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.OptionalServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectDTO;
import com.sankuai.sinai.data.api.dto.MtPoiDTO;
import com.sankuai.sinai.data.api.util.MtPoiUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
public class RecommendPlugin {

    private static List<String> RECOMMEND_TYPE = Lists.newArrayList("shop", "product", "technician");

    private static final String RECOMMEND_MASK_KEY = "PilotRecommendMask";

    @Autowired
    private ShopAclService shopAclService;

    @Autowired
    private ReviewRepositoryService reviewRepositoryService;

    @Autowired
    private ProductAclService productAclService;

    @Autowired
    private TechnicianSyncDAO technicianSyncDAO;

    @Autowired
    private ReviewSyncDAO reviewSyncDAO;

    @Autowired
    private MapAclService mapAclService;

    @Resource
    private PilotChatGroupDAO pilotChatGroupDAO;

    @Resource
    private PilotChatGroupMessageDAO pilotChatGroupMessageDAO;

    @Autowired
    private IDGenerateUtil idGenerateUtil;

    @MdpConfig("com.sankuai.mim.pilot.plugin.recommend.type2prompt:{}")
    private HashMap<String, String> type2PromptMap;

    @MdpConfig("com.sankuai.mim.pilot.plugin.shop.detail.map.search.key:\"\"")
    private String mapSearchKey;

    @Resource(name = "redisClient")
    private RedisStoreClient redisStoreClient;

    @LocalPlugin(name = "RecommendTool", description = "当用户表达出寻找门店,商品,手艺人的需求时,你可以使用该插件向用户推荐相关的实体", returnDirect = false)
    public AIAnswerData recommendObject(RecommendParam recommendParam) {
        if (recommendParam == null) {
            return new AIAnswerData("参数错误");
        }

        if (recommendParam.getClaim().equals("")) {
            return new AIAnswerData("用户没有表达推荐诉求请不要使用该工具");
        }
        if (!RECOMMEND_TYPE.contains(recommendParam.getRecommendType())) {
            return new AIAnswerData("recommendType不正确,只能是:" + JsonCodec.encodeWithUTF8(RECOMMEND_TYPE));
        }

        AIAnswerData knowledge = recommend(recommendParam);
        BufferUtils.writeStatusBuffer(PilotBufferItemDO.builder().data("以为您找到相关内容").build());
        return knowledge;
    }

    private void insertSystemMessage(AIServiceContext aiServiceContext, long shopId) {
        try {
            if (shopId <= 0 || aiServiceContext == null || aiServiceContext.getMessageDTO() == null || aiServiceContext.getMessageDTO().getChatGroupId() <= 0) {
                return;
            }

            // 非流式的请求不加这个消息
            if (!aiServiceContext.isStream()) {
                return;
            }

            MessageDTO messageDTO = aiServiceContext.getMessageDTO();

            PilotChatGroupEntity pilotChatGroupEntity = pilotChatGroupDAO.queryChatGroupById(messageDTO.getChatGroupId());
            if (pilotChatGroupEntity == null) {
                return;
            }

            long messageId = idGenerateUtil.nextMessageId();
            if (messageId <= 0) {
                return;
            }

            String messageTemplateType = "用户当前正在咨询 门店id=%s 的门店, 如果后续有以任何形式提示其他门店id,则忽略该条消息";
            //插入模型消息
            PilotChatMessageEntity replyMessageEntity = buildPilotChatMessageEntity(messageId, MessageTypeEnum.TEXT.value,
                    pilotChatGroupEntity.getId(), pilotChatGroupEntity.getAssistantType(), pilotChatGroupEntity.getUserId(),
                    MessageSendDirectionEnum.SYSTEM_SEND.value, AuditStatusEnum.PASS.getCode(),
                    String.format(messageTemplateType, shopId), AssistantConstant.SYSTEM, StringUtils.EMPTY);
            pilotChatGroupMessageDAO.insertMessage(replyMessageEntity);
        } catch (Exception e) {
            log.error("insertSystemMessage error, ShopDetailPlugin. context: {}.", aiServiceContext, e);
        }
    }

    private PilotChatMessageEntity buildPilotChatMessageEntity(long messageId, int messageType, long chatGroupId, int assistantType,
                                                               String userId, int direction, int auditStatus, String message,
                                                               String creator, String extra) {
        PilotChatMessageEntity messageEntity = new PilotChatMessageEntity();
        messageEntity.setMessageId(messageId);
        messageEntity.setMessageType(messageType);
        messageEntity.setChatGroupId(chatGroupId);
        messageEntity.setAssistantType(assistantType);
        messageEntity.setUserId(userId);
        messageEntity.setDirection(direction);
        messageEntity.setAuditStatus(auditStatus);
        messageEntity.setExtraData(extra);
        messageEntity.setMessage(message);
        messageEntity.setCreator(creator);
        return messageEntity;
    }

    private AIAnswerData recommend(RecommendParam recommendParam) {
        // 查会话
        PilotChatGroupEntity chatGroup = pilotChatGroupDAO.queryChatGroupById(recommendParam.getAiServiceContext().getMessageDTO().getChatGroupId());
        if (chatGroup == null) {
            return new AIAnswerData("没有相关内容");
        }
        if (recommendParam.getRecommendType().equals("shop")) {
            return recommendShop(recommendParam, chatGroup);
        }

        if (recommendParam.getRecommendType().equals("product")) {
            return recommendProduct(recommendParam, chatGroup);
        }

        if (recommendParam.getRecommendType().equals("technician")) {
            return recommendTechnician(recommendParam, chatGroup);
        }

        return new AIAnswerData("没有相关内容");
    }

    private AIAnswerData recommendShop(RecommendParam recommendParam, PilotChatGroupEntity chatGroup) {
        // buffer状态
        BufferUtils.writeStatusBuffer(PilotBufferItemDO.builder().data("正在为您推荐(门店): " + recommendParam.getClaim()).build());

        List<ReviewSyncEntity> reviewSyncEntities = getReviewSyncEntities(recommendParam, 300);
        if (CollectionUtils.isEmpty(reviewSyncEntities)) {
            return new AIAnswerData("暂无推荐");
        }

        reviewSyncEntities = getCanRecommendReview(reviewSyncEntities, chatGroup, "shop");

        Map<Long, List<ReviewSyncEntity>> shopId2ReviewsMap = reviewSyncEntities.stream().collect(Collectors.groupingBy(ReviewSyncEntity::getMtShopId));
        shopId2ReviewsMap = getObjectId2ReviewBySimilarity(shopId2ReviewsMap, 100);

        // 查询门店信息
        Map<Long, MtPoiDTO> mtPoiDTOMap = shopAclService.batchGetMtShopInfo(new ArrayList<>(shopId2ReviewsMap.keySet()));
        if (MapUtils.isEmpty(mtPoiDTOMap)) {
            return new AIAnswerData("暂无推荐");
        }

        // 拼接接待推荐内容
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<Long, List<ReviewSyncEntity>> entry : shopId2ReviewsMap.entrySet()) {
            MtPoiDTO mtPoiDTO = mtPoiDTOMap.get(entry.getKey());
            if (mtPoiDTO == null) {
                continue;
            }
            sb.append("\n\n###待推荐门店ID:").append(entry.getKey()).append("\n");
            sb.append("####门店信息:\n");
            appendShopInfo(sb, mtPoiDTO);
            sb.append("####门店评价:\n");
            appendReviews(sb, entry.getValue());
        }

        String prompt = type2PromptMap.get("shop");

        return new AIAnswerData(String.format(prompt, recommendParam.getClaim()) + sb);
    }

    private Map<Long, List<ReviewSyncEntity>> getObjectId2ReviewBySimilarity(Map<Long, List<ReviewSyncEntity>> shopId2ReviewsMap, int limit) {
        return shopId2ReviewsMap.entrySet().stream()
                .sorted((e1, e2) -> {
                    double maxScore1 = e1.getValue().stream().mapToDouble(ReviewSyncEntity::getSimilarity).max().orElse(Integer.MIN_VALUE);
                    double maxScore2 = e2.getValue().stream().mapToDouble(ReviewSyncEntity::getSimilarity).max().orElse(Integer.MIN_VALUE);
                    return Double.compare(maxScore2, maxScore1);
                })
                .limit(limit)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (e1, e2) -> e1, LinkedHashMap::new));
    }

    private List<ReviewSyncEntity> getCanRecommendReview(List<ReviewSyncEntity> reviewSyncEntities, PilotChatGroupEntity chatGroup, String type) {
        List<ReviewSyncEntity> result = new ArrayList<>();
        // 查询已经推荐过的内容
        List<Long> hasRecommendObjectId = getHasRecommendObjectId(chatGroup.getUserId(), type);
        for (ReviewSyncEntity entity : reviewSyncEntities) {
            if (entity.getSimilarity() < 0.6) {
                continue;
            }

            if (hasRecommendObjectId.contains(entity.getMtShopId())) {
                continue;
            }

            result.add(entity);
        }
        return result;
    }

    private void appendReviews(StringBuilder sb, List<ReviewSyncEntity> reviews) {
        if (CollectionUtils.isEmpty(reviews)) {
            return;
        }
        for (ReviewSyncEntity entity : reviews) {
            addStarType(sb, entity);
            sb.append(entity.getContent()).append("\n");
        }
    }

    private AIAnswerData recommendProduct(RecommendParam recommendParam, PilotChatGroupEntity chatGroup) {
        // buffer状态
        BufferUtils.writeStatusBuffer(PilotBufferItemDO.builder().data("正在为您推荐(商品): " + recommendParam.getClaim()).build());

        List<ReviewSyncEntity> reviewSyncEntities = getReviewSyncEntities(recommendParam, 300);
        if (CollectionUtils.isEmpty(reviewSyncEntities)) {
            return new AIAnswerData("暂无推荐");
        }

        reviewSyncEntities = reviewSyncEntities.stream().filter(reviewSyncEntity -> reviewSyncEntity.getSimilarity() > 0.6).collect(Collectors.toList());

        reviewSyncEntities = getCanRecommendReview(reviewSyncEntities, chatGroup, "product");

        Map<Long, List<ReviewSyncEntity>> productId2ReviewsMap = reviewSyncEntities.stream().filter(r -> r.getDealId() > 0).collect(Collectors.groupingBy(ReviewSyncEntity::getDealId));

        productId2ReviewsMap = getObjectId2ReviewBySimilarity(productId2ReviewsMap, 100);

        // 查询门店信息
        Map<Long, DealGroupDTO> dealGroupDTOMap = productAclService.batchGetDealBaseInfo(new ArrayList<>(productId2ReviewsMap.keySet()));
        if (MapUtils.isEmpty(dealGroupDTOMap)) {
            return new AIAnswerData("暂无推荐");
        }

        // 拼接接待推荐内容
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<Long, List<ReviewSyncEntity>> entry : productId2ReviewsMap.entrySet()) {
            DealGroupDTO dealGroupDTO = dealGroupDTOMap.get(entry.getKey());
            if (dealGroupDTO == null) {
                continue;
            }
            sb.append("\n\n###待推荐商品ID:").append(entry.getKey()).append("\n");
            sb.append("####团购信息:\n");
            appendDealInfo(sb, dealGroupDTO);
            sb.append("####团购评价:\n");
            appendReviews(sb, entry.getValue());
        }

        String prompt = type2PromptMap.get("product");

        return new AIAnswerData(String.format(prompt, recommendParam.getClaim()) + sb);
    }

    private AIAnswerData recommendTechnician(RecommendParam recommendParam, PilotChatGroupEntity chatGroup) {
        // buffer状态
        BufferUtils.writeStatusBuffer(PilotBufferItemDO.builder().data("正在为您推荐(手艺人): " + recommendParam.getClaim()).build());

        List<ReviewSyncEntity> reviewSyncEntities = getReviewSyncEntities(recommendParam, 300);
        if (CollectionUtils.isEmpty(reviewSyncEntities)) {
            return new AIAnswerData("暂无推荐");
        }

        reviewSyncEntities = reviewSyncEntities.stream().filter(reviewSyncEntity -> reviewSyncEntity.getSimilarity() > 0.6).collect(Collectors.toList());

        reviewSyncEntities = getCanRecommendReview(reviewSyncEntities, chatGroup, "technician");

        Map<Long, List<ReviewSyncEntity>> technician2ReviewsMap = reviewSyncEntities.stream().filter(r -> r.getTechnicianId() > 0).collect(Collectors.groupingBy(ReviewSyncEntity::getTechnicianId));

        technician2ReviewsMap = getObjectId2ReviewBySimilarity(technician2ReviewsMap, 100);

        // 不足10条评价且有门店id,获取全量的手艺人数据
        if ((reviewSyncEntities.size() <= 10 || technician2ReviewsMap.size() <= 2) && recommendParam.getShopId() > 0) {
            reviewSyncEntities = getReviewSyncEntitiesWithShop(recommendParam);
            technician2ReviewsMap = reviewSyncEntities.stream().filter(r -> r.getTechnicianId() > 0).collect(Collectors.groupingBy(ReviewSyncEntity::getTechnicianId));
        }

        if (MapUtils.isEmpty(technician2ReviewsMap)) {
            return new AIAnswerData("暂无推荐");
        }

        // 查询门店信息
        List<TechnicianSyncEntity> technicianSyncEntities = technicianSyncDAO.batchFindByTechnicianId(new ArrayList<>(technician2ReviewsMap.keySet()));
        if (CollectionUtils.isEmpty(technicianSyncEntities)) {
            return new AIAnswerData("暂无推荐");
        }

        Map<Long, TechnicianSyncEntity> technicianMap = technicianSyncEntities.stream().collect(Collectors.toMap(TechnicianSyncEntity::getTechnicianId, Function.identity()));
        // 拼接接待推荐内容
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<Long, List<ReviewSyncEntity>> entry : technician2ReviewsMap.entrySet()) {
            TechnicianSyncEntity technicianSyncEntity = technicianMap.get(entry.getKey());
            if (technicianSyncEntity == null) {
                continue;
            }
            sb.append("\n\n###待推荐手艺人ID:").append(entry.getKey()).append("\n");
            sb.append("####手艺人信息:\n");
            appendTechnicianInfo(sb, technicianSyncEntity);
            sb.append("####手艺人评价:\n");
            appendReviews(sb, entry.getValue());
        }

        String prompt = type2PromptMap.get("technician");
        return new AIAnswerData(String.format(prompt, recommendParam.getClaim()) + sb);
    }

    private List<ReviewSyncEntity> getReviewSyncEntitiesWithShop(RecommendParam recommendParam) {
        if (recommendParam.getShopId() <= 0) {
            return Lists.newArrayList();
        }

        List<TechnicianSyncEntity> technicianSyncEntities = technicianSyncDAO.findByMtShopId(recommendParam.getShopId());
        if (CollectionUtils.isEmpty(technicianSyncEntities)) {
            return Lists.newArrayList();
        }

        List<Long> technicianIds = technicianSyncEntities.stream().map(TechnicianSyncEntity::getTechnicianId).collect(Collectors.toList());
        return reviewSyncDAO.batchFindByTechnicianIds(technicianIds);
    }

    private List<ReviewSyncEntity> getReviewSyncEntities(RecommendParam recommendParam, int topK) {
        Map<String, String> scalarFilter = Maps.newHashMap();
        if (recommendParam.getShopId() > 0) {
            scalarFilter.put("mtShopId", String.valueOf(recommendParam.getShopId()));
        }
        return reviewRepositoryService.searchReview(recommendParam.getClaim(), scalarFilter, topK);
    }

    private void appendShopInfo(StringBuilder sb, MtPoiDTO mtShopInfo) {
        //名字
        appendName(sb, mtShopInfo);

        //地址
        appendAddress(sb, mtShopInfo);

        //营业时间
        appendBusiness(sb, mtShopInfo);

        //联系方式
        appendPhone(sb, mtShopInfo);

        //人均&星级
        appendStar(sb, mtShopInfo);
    }

    private void appendDealInfo(StringBuilder sb, DealGroupDTO dealGroupDTO) {
        sb.append(convertDealGroup2Text(dealGroupDTO));
    }

    private String convertDealGroup2Text(DealGroupDTO dealGroupDTO) {
        StringBuilder sb = new StringBuilder();
        sb.append("商品名称:").append(getTitle(dealGroupDTO)).append("\n");
        sb.append("商品价格:").append(getPrice(dealGroupDTO)).append("\n");
        sb.append("商品服务项:").append(getServiceProject(dealGroupDTO)).append("\n");
        sb.append("商品介绍:").append(getIntroduction(dealGroupDTO)).append("\n");

        return sb.toString();
    }

    private String getIntroduction(DealGroupDTO dealGroupDTO) {
        if (dealGroupDTO.getDetail() == null || CollectionUtils.isEmpty(dealGroupDTO.getDetail().getTemplateDetailDTOs())) {
            return "暂无商品介绍信息";
        }

        StringBuilder sb = new StringBuilder();
        for (DealGroupTemplateDetailDTO templateDetailDTO : dealGroupDTO.getDetail().getTemplateDetailDTOs()) {
            if (templateDetailDTO.getTitle().equals("产品介绍")) {
                sb.append("\n\t产品介绍:").append(parseHtml(templateDetailDTO.getContent(), "\n\t\t"));
            }
            if (templateDetailDTO.getTitle().equals("团购详情")) {
                sb.append("\n\t补充信息:").append(parseHtml(templateDetailDTO.getContent(), "\n\t\t"));
            }
            if (templateDetailDTO.getTitle().equals("购买须知")) {
                sb.append("\n\t购买须知:").append(parseHtml(templateDetailDTO.getContent(), "\n\t\t"));
            }
        }

        return sb.toString();
    }

    private String parseHtml(String content, String prefix) {
        StringBuilder stringBuilder = new StringBuilder();

        Document doc = Jsoup.parse(content);
        Elements paragraphs = doc.select("p");
        for (Element paragraph : paragraphs) {
            if (StringUtils.isNotBlank(paragraph.text())) {
                stringBuilder.append(prefix).append(paragraph.text());
            }
        }

        return stringBuilder.toString();
    }

    private String getServiceProject(DealGroupDTO dealGroupDTO) {
        if (dealGroupDTO.getServiceProject() == null) {
            return "暂无服务项目信息";
        }
        StringBuilder serviceSb = new StringBuilder();

        // 全部可享
        if (CollectionUtils.isNotEmpty(dealGroupDTO.getServiceProject().getMustGroups())) {
            serviceSb.append("\n\t【全部可享】服务包含:");
            for (MustServiceProjectGroupDTO serviceGroup : dealGroupDTO.getServiceProject().getMustGroups()) {
                if (CollectionUtils.isEmpty(serviceGroup.getGroups())) {
                    continue;
                }
                for (ServiceProjectDTO serviceProjectDTO : serviceGroup.getGroups()) {
                    serviceSb.append("\n\t\t").append(parseServiceProject(serviceProjectDTO));
                }
            }
        }

        // 部分可享
        if (CollectionUtils.isNotEmpty(dealGroupDTO.getServiceProject().getOptionGroups())) {
            serviceSb.append("\n\t【部分可享】服务包含:");
            for (OptionalServiceProjectGroupDTO serviceGroup : dealGroupDTO.getServiceProject().getOptionGroups()) {
                if (CollectionUtils.isEmpty(serviceGroup.getGroups())) {
                    continue;
                }
                String optionTip = String.format("%s选%s", serviceGroup.getGroups().size(), serviceGroup.getOptionalCount());
                serviceSb.append("\n\t\t").append(optionTip).append(":");
                for (ServiceProjectDTO serviceProjectDTO : serviceGroup.getGroups()) {
                    serviceSb.append("\n\t\t\t").append(parseServiceProject(serviceProjectDTO));
                }
            }
        }
        return serviceSb.toString();
    }

    private String parseServiceProject(ServiceProjectDTO serviceProjectDTO) {
        StringBuilder sb = new StringBuilder();
        sb.append("服务名称:").append(serviceProjectDTO.getName());
        if (StringUtils.isNotBlank(serviceProjectDTO.getMarketPrice())) {
            sb.append(" ( ").append("价值:").append(serviceProjectDTO.getMarketPrice()).append("元").append(" )");
        }
        if (CollectionUtils.isEmpty(serviceProjectDTO.getAttrs())) {
            return sb.toString();
        }

        for (int index = 0; index < serviceProjectDTO.getAttrs().size(); ++index) {
            ServiceProjectAttrDTO attrDTO = serviceProjectDTO.getAttrs().get(index);
            sb.append(" ( ").append(attrDTO.getChnName()).append(":").append(attrDTO.getAttrValue());
            if (index != serviceProjectDTO.getAttrs().size() - 1) {
                sb.append(", ");
            } else {
                sb.append(" )");
            }
        }
        return sb.toString();
    }

    private String getPrice(DealGroupDTO dealGroupDTO) {
        if (dealGroupDTO.getPrice() == null) {
            return "暂无价格信息," + IMConstants.ANSWER_PROBLEM_NO_REPLY;
        }

        return dealGroupDTO.getPrice().getSalePrice() + "元";
    }

    private String getTitle(DealGroupDTO dealGroupDTO) {
        String title = dealGroupDTO.getBasic().getTitle();
        String titleDesc = dealGroupDTO.getBasic().getTitleDesc();
        if (StringUtils.isNotBlank(titleDesc)) {
            return title + "( " + titleDesc + " )";
        }
        return title;
    }

    private void addStarType(StringBuilder sb, ReviewSyncEntity entity) {
        String star = entity.getStar();
        Map<String, String> starMap = JsonCodec.converseMap(star, String.class, String.class);
        if (starMap.containsKey("starType")) {
            String starType = starMap.get("starType");
            switch (starType) {
                case "1":
                    sb.append("好评: ");
                    break;
                case "2":
                    sb.append("差评: ");
                    break;
                case "3":
                    sb.append("中评: ");
                    break;
            }
        }
    }

    private void appendStar(StringBuilder sb, MtPoiDTO mtShopInfo) {
        if (mtShopInfo.getMtAvgPrice() != null && mtShopInfo.getMtAvgPrice() > 0) {
            sb.append("人均消费: ").append(mtShopInfo.getMtAvgPrice()).append("元");
        }
        if (mtShopInfo.getMtAvgScore() != null && mtShopInfo.getMtAvgScore() > 0) {
            sb.append(", 星级: ").append(mtShopInfo.getMtAvgScore());
        }
        sb.append("\n");
        if (CollectionUtils.isNotEmpty(mtShopInfo.getSubScores())) {
            sb.append("子星级: ").append(mtShopInfo.getSubScores().stream().map(s -> s.getTitle() + s.getScore() + "星").collect(Collectors.joining(", "))).append("\n");
        }
        if (CollectionUtils.isNotEmpty(mtShopInfo.getSubScoreDTOList())) {
            sb.append("子星级: ").append(mtShopInfo.getSubScores().stream().map(s -> s.getTitle() + s.getScore() + "星").collect(Collectors.joining(", "))).append("\n");
        }
    }

    private void appendName(StringBuilder sb, MtPoiDTO mtShopInfo) {
        sb.append("门店名称: ").append(MtPoiUtil.getMtPoiName(mtShopInfo.getName(), mtShopInfo.getBranchName())).append("\n");
    }

    private void appendPhone(StringBuilder sb, MtPoiDTO mtShopInfo) {
        if (CollectionUtils.isNotEmpty(mtShopInfo.getNormPhones())) {
            sb.append("门店电话: ").append(MtPoiUtil.getMtPoiPhone(mtShopInfo.getNormPhones())).append("\n");
        }
    }

    private void appendBusiness(StringBuilder sb, MtPoiDTO mtShopInfo) {
        if (StringUtils.isNotBlank(mtShopInfo.getBusinessHours())) {
            sb.append("营业时间: ").append(mtShopInfo.getBusinessHours()).append("\n");
        }
    }

    private void appendAddress(StringBuilder sb, MtPoiDTO mtShopInfo) {
        if (StringUtils.isNotBlank(mtShopInfo.getAddress())) {
            sb.append("门店地址: ").append(mtShopInfo.getAddress());
            if (StringUtils.isNotBlank(mtShopInfo.getReferenceAddress())) {
                sb.append("(").append(mtShopInfo.getReferenceAddress()).append(")");
            }
            sb.append("\n");
        }
        if (StringUtils.isNotBlank(mtShopInfo.getMallName())) {
            sb.append("位于商场: ").append(mtShopInfo.getMallName());
            if (StringUtils.isNotBlank(mtShopInfo.getFloor())) {
                sb.append(", 楼层: ").append(mtShopInfo.getFloor());
            }
            sb.append("\n");
        }
        if (StringUtils.isNotBlank(mtShopInfo.getPublicTransit())) {
            sb.append("交通信息: ").append(mtShopInfo.getPublicTransit()).append("\n");
        }
        if (CollectionUtils.isNotEmpty(mtShopInfo.getPoiSubways())) {
            String subwayDistance = mtShopInfo.getPoiSubways().stream().map(s -> s.getName() + s.getDistance() + "米").collect(Collectors.joining(", "));
            sb.append("地铁信息: 距离").append(subwayDistance).append("\n");
        }
        if (CollectionUtils.isNotEmpty(mtShopInfo.getPoiLandmarks())) {
            String subwayDistance = mtShopInfo.getPoiLandmarks().stream().map(s -> s.getName() + s.getDistance() + "米").collect(Collectors.joining(", "));
            sb.append("地标信息: 距离").append(subwayDistance).append("\n");
        }
        if (StringUtils.isNotBlank(mtShopInfo.getParkingInfo())) {
            sb.append("停车位信息: ").append(mtShopInfo.getParkingInfo()).append("\n");
        }
    }

    private void appendTechnicianInfo(StringBuilder sb, TechnicianSyncEntity technicianSyncEntity) {
        sb.append("手艺人信息(手艺人id = ").append(technicianSyncEntity.getTechnicianId()).append(")\n");
        sb.append("手艺人名称: ").append(technicianSyncEntity.getTechnicianName()).append("\n");
        if (StringUtils.isNotBlank(technicianSyncEntity.getSkill())) {
            sb.append("手艺人技能: ").append(technicianSyncEntity.getSkill()).append("\n");
        }
        if (StringUtils.isNotBlank(technicianSyncEntity.getTitle())) {
            sb.append("手艺人头衔: ").append(technicianSyncEntity.getSkill());
            if (StringUtils.isNotBlank(technicianSyncEntity.getSkill())) {
                sb.append(", 技能: ").append(technicianSyncEntity.getSkill());
            }
            sb.append("\n");
        }
        if (StringUtils.isNotBlank(technicianSyncEntity.getSummary())) {
            sb.append("手艺人简介: ").append(technicianSyncEntity.getSummary()).append("\n");
        }
        if (StringUtils.isNotBlank(technicianSyncEntity.getPhoto())) {
            sb.append("头像").append(technicianSyncEntity.getPhoto()).append("\n");
        }
    }

    private List<Long> getHasRecommendObjectId(String userId, String type) {
        StoreKey key = new StoreKey(RECOMMEND_MASK_KEY, userId, type);
        List<Long> ids = redisStoreClient.lrange(key, 0, -1);
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        return ids;
    }

    public void addRecommendObject(String userId, String type, Long objectId) {
        try {
            StoreKey key = new StoreKey(RECOMMEND_MASK_KEY, userId, type);
            redisStoreClient.rpush(key, objectId);
            redisStoreClient.expire(key, 60 * 30);
        } catch (Exception e) {
            log.error("addRecommendObject error, userId:{}, type:{}, objectIds:{}.", userId, type, objectId, e);
        }
    }
}
