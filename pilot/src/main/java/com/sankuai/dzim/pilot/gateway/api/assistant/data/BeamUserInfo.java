package com.sankuai.dzim.pilot.gateway.api.assistant.data;

import lombok.Data;


@Data
public class BeamUserInfo {

    private String userid;

    private String user_token;

    private String ip;

    private String fingerprint;

    private String user_name;

    private String phone;

    private String user_avatar;

    private String uuid;

    private String location;

    private String latitude;

    private String longitude;

    private String city_id;

    private String intent_location;

    private String intent_lat;

    private String intent_lng;

    private String intent_city_id;

    private String client_type;

    private String appversion;

    private String utm_campaign;

    private String utm_content;

    private String utm_medium;

    private String utm_source;

    private String utm_term;

    private String os;

    private String mtgsig;

    private String gps_accuracy;

    private String location_way;

    private String ci;

    private String wm_dversion;
}
