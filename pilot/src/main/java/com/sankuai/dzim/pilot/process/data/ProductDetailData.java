package com.sankuai.dzim.pilot.process.data;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 商品详情信息（套餐、图文详情、售卖规则、拼场规则、房间设置等，用于大模型决策）
 */
@Data
public class ProductDetailData {

    /**
     * 商品信息
     */
    private Map<String, String> basicProductInfo;

    /**
     * 图文详情
     */
    private Map<String, Object> description;

    /**
     * 售卖信息
     */
    private Map<String, Object> saleInfo;

    /**
     * 拼场规则
     */
    private Map<String, Object> poolRules;

    /**
     * 预订商品上关联的房间信息
     */
    private Map<String, Object> roomInfo;

    /**
     * 预订商品上关联的套餐信息
     */
    private Map<String, Object> packageInfo;

}
