package com.sankuai.dzim.pilot.process.aireservebook.data;

import lombok.Data;

/**
 * @author: wu<PERSON><PERSON><PERSON>
 * @create: 2025-04-25
 * @description: 预约确认卡片
 */
@Data
public class BookConfirmCard {
    /**
     * 标题
     */
    private String title;

    /**
     * 门店id
     */
    private Long shopId;

    /**
     * 门店名
     */
    private String shopName;

    /**
     * 预约时间
     */
    private String bookTime;

    /**
     * 预约项目
     */
    private String project;

    /**
     * 参与人数
     */
    private String peopleNum;

    /**
     * 联系方式
     */
    private String phone;

    /**
     * 备注
     */
    private String remark;

    /**
     * 确认文案
     */
    private String confirmText;

    /**
     * 确认按钮展示内容
     */
    private String confirmButtonText;

    /**
     * 发送消息文案
     */
    private String confirmSendText;

    /**
     * 是否置灰
     */
    private Boolean isGray;
}
