package com.sankuai.dzim.pilot.gateway.api.assistant;

import com.dianping.dzim.common.enums.ImUserType;
import com.dianping.gateway.enums.APIModeEnum;
import com.dianping.vc.web.api.API;
import com.dianping.vc.web.api.URL;
import com.dianping.vc.web.api.VCAssert;
import com.dianping.vc.web.biz.client.api.AppContext;
import com.dianping.vc.web.biz.client.api.AppContextAware;
import com.dianping.vc.web.biz.client.api.MTUserIdAware;
import com.dianping.vc.web.biz.client.api.UserIdAware;
import com.google.common.collect.Maps;
import com.sankuai.dzim.message.enums.PlatformTypeEnum;
import com.sankuai.dzim.pilot.api.enums.assistant.PlatformEnum;
import com.sankuai.dzim.pilot.gateway.api.vo.ListPurposeVO;
import com.sankuai.dzim.pilot.process.ListPurposeService;
import com.sankuai.dzim.pilot.process.data.AgentListPurposeData;
import com.sankuai.dzim.pilot.process.data.AgentListPurposeReq;
import com.sankuai.dzim.pilot.process.data.AgentListTypeEnum;
import com.sankuai.dzim.pilot.utils.CatUtils;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

@Setter
@Slf4j
@Service
@URL(url = "/dzim/pilot/assistant/list/purpose/guide", mode = APIModeEnum.CLIENT, methods = "GET")
public class ListPurposeGuideApi implements API, MTUserIdAware, UserIdAware, AppContextAware {
    @Setter
    private AppContext appContext;

    @Setter
    private long mTUserId;

    @Setter
    private long userId;

    @Setter
    private String listType;

    @Setter
    private String currentQuery;

    @Setter
    private String templateKey;

    @Setter
    private Long categoryId;

    @Setter
    private Float lat;

    @Setter
    private Float lng;

    @Resource
    private ListPurposeService listPurposeService;

    @Override
    public Object execute() {
        checkParam();
        metricListTypeCnt();
        AgentListPurposeData agentListPurposeData = listPurposeService.getAigcWelcomeContent(buildAgentListPurposeReq());
        metricListTypeSucc();
        return buildAgentListPurposeVO(agentListPurposeData);
    }

    public void metricListTypeCnt() {
        Map<String, String> tags = Maps.newHashMap();
        tags.put("listType", String.valueOf(listType));
        CatUtils.logMetricReq("assistantListPurposeGuide", tags);
    }

    public void metricListTypeSucc(){
        Map<String, String> tags = Maps.newHashMap();
        tags.put("listType", String.valueOf(listType));
        CatUtils.logMetricSucc("assistantListPurposeGuide", tags);
    }


    private AgentListPurposeReq buildAgentListPurposeReq() {
        int platform = PlatformEnum.getByName(appContext.getPlatform()).getType();
        return AgentListPurposeReq.builder()
                .platform(platform)
                .listType(listType)
                .imUserId(getImUserId(platform))
                .templateKey(templateKey)
                .categoryId(categoryId)
                .currentQuery(currentQuery)
                .lng(lng)
                .lat(lat).build();
    }

    private String getImUserId(int platform) {
        if (platform == PlatformTypeEnum.DP.value) {
            return ImUserType.DP.getPrefix() + userId;
        } else {
            return ImUserType.MT.getPrefix() + mTUserId;
        }
    }

    private void checkParam() {
        VCAssert.isTrue(userId > 0 || mTUserId > 0, API.UNLOGIN_CODE, "未登陆");
        VCAssert.isTrue(AgentListTypeEnum.checkExistByName(listType), API.INVALID_PARAM_CODE, "listType不合法");
    }

    private ListPurposeVO buildAgentListPurposeVO(AgentListPurposeData agentListPurposeData) {
        ListPurposeVO listPurposeVO = new ListPurposeVO();
        String hiWord = "你好呀，我可以帮你找店哦";
        if(agentListPurposeData != null && agentListPurposeData.getListGuide() != null){
            hiWord = agentListPurposeData.getListGuide();
        }
        listPurposeVO.setListGuide(hiWord);
        return listPurposeVO;
    }
}
