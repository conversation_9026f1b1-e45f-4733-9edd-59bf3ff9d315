package com.sankuai.dzim.pilot.process.localplugin;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.lion.client.Lion;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.dianping.vc.sdk.concurrent.threadpool.ExecutorServices;
import com.google.common.collect.Lists;
import com.sankuai.dzim.message.common.utils.ImAccountTypeUtils;
import com.sankuai.dzim.pilot.acl.HaimaAclService;
import com.sankuai.dzim.pilot.acl.ShopAclService;
import com.sankuai.dzim.pilot.api.data.AIAnswerTypeEnum;
import com.sankuai.dzim.pilot.buffer.core.PilotBufferItemDO;
import com.sankuai.dzim.pilot.buffer.enums.BufferItemTypeEnum;
import com.sankuai.dzim.pilot.buffer.utils.BufferUtils;
import com.sankuai.dzim.pilot.chain.data.AIAnswerData;
import com.sankuai.dzim.pilot.chain.data.AIServiceContext;
import com.sankuai.dzim.pilot.chain.enums.AIServiceExtraKeyEnum;
import com.sankuai.dzim.pilot.domain.annotation.LocalPlugin;
import com.sankuai.dzim.pilot.domain.memory.impl.BeamChatMemoryProvider;
import com.sankuai.dzim.pilot.gateway.api.assistant.data.SendBeamMessageReq;
import com.sankuai.dzim.pilot.process.aireservebook.enums.POIIndustryType;
import com.sankuai.dzim.pilot.process.data.ProductDetailData;
import com.sankuai.dzim.pilot.process.data.ProductRetrievalData;
import com.sankuai.dzim.pilot.process.data.ProductStockData;
import com.sankuai.dzim.pilot.process.localplugin.data.UserContext;
import com.sankuai.dzim.pilot.process.localplugin.param.BeamShopProductRetrievalParam;
import com.sankuai.dzim.pilot.process.localplugin.param.ShopProductSearchParam;
import com.sankuai.dzim.pilot.process.search.SearchProcessService;
import com.sankuai.dzim.pilot.process.search.data.TimeSliceM;
import com.sankuai.dzim.pilot.process.search.enums.IndustryBackendCategoryEnum;
import com.sankuai.dzim.pilot.utils.AIChatUtil;
import com.sankuai.dzim.pilot.utils.LionConfigUtil;
import com.sankuai.dzim.pilot.utils.PluginContextUtil;
import com.sankuai.dzim.pilot.utils.SupplyUtils;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

@Component
@Slf4j
public class BeamShopProductRecommendPlugin {

    @Resource
    private SearchProcessService searchProcessService;

    @Resource
    private SupplyUtils supplyUtils;

    @Autowired
    private LionConfigUtil lionConfigUtil;

    @Autowired
    private HaimaAclService haimaAclService;

    @Autowired
    private ShopAclService shopAclService;

    @Autowired
    private AIChatUtil aiChatUtil;

    @Autowired
    private BeamChatMemoryProvider beamChatMemoryProvider;

    private static final List<Integer> NEED_QUERY_RESERVE_CATEGORY_IDS = Lists.newArrayList(
            IndustryBackendCategoryEnum.KTV.getCategoryIds(),
            IndustryBackendCategoryEnum.CHESS.getCategoryIds(),
            IndustryBackendCategoryEnum.BALL.getCategoryIds(),
            IndustryBackendCategoryEnum.VENUE.getCategoryIds(),
            IndustryBackendCategoryEnum.BACKROOM.getCategoryIds(),
            IndustryBackendCategoryEnum.ROLE_PLAY.getCategoryIds()
    ).stream().flatMap(List::stream).collect(Collectors.toList());


    private static final DateTimeFormatter formatter = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm");

//    private ExecutorService executor = ExecutorServices.forThreadPoolExecutor("BeamShopProductRecommendPlugin", 5, 30);

    private static ThreadPool beamShopProductRecommendPool = Rhino.newThreadPool("BeamShopProductRecommendPool",
            DefaultThreadPoolProperties.Setter().withCoreSize(20).withMaxSize(100).withMaxQueueSize(500).withRejectHandler(new ThreadPoolExecutor.CallerRunsPolicy()));


    @LocalPlugin(name = "ShopProductRetrievalTool",
            description = "搜索指定门店下的商品.", returnDirect = true)
    public AIAnswerData recommendShopProduct(BeamShopProductRetrievalParam shopProductRetrievalParam) {
        log.info("【调用商户内推荐商品的工具，ShopProductRecommendPlugin】 param: {}", JsonCodec.encodeWithUTF8(shopProductRetrievalParam));
        ShopProductSearchParam param = buildShopProductSearchParam(shopProductRetrievalParam);
        BufferUtils.writeStatusBuffer(PilotBufferItemDO.builder().data(getShowText(param)).build());

        SendBeamMessageReq sendBeamMessageReq = PluginContextUtil.getBeamRequestContext(param);
        AIServiceContext aiServiceContext = param.getAiServiceContext();

        Map<String, Object> extraInfo = MapUtils.emptyIfNull(aiServiceContext.getExtraInfo());
        String userInput = sendBeamMessageReq.getOriginal_user_input().getContent();
        long shopId = org.apache.commons.lang3.math.NumberUtils.toLong(sendBeamMessageReq.getContext().getPoi_id());
        param.setShopId(shopId);

        // 查询商品
        List<ProductRetrievalData> products = getShopProducts(param);

        // 无商品兜底回复
        if (CollectionUtils.isEmpty(products)) {
            BufferUtils.writeMainTextBuffer(PilotBufferItemDO.builder().type(BufferItemTypeEnum.MAIN_TEXT.getType())
                    .data("抱歉哦，该门店暂时没有商品").build());
            return new AIAnswerData(AIAnswerTypeEnum.TEXT.getType(), "抱歉哦，该门店暂时没有商品");
        }

        fillBufferExtraData(extraInfo, products);
        String dynamicPrompt = buildDynamicPrompt(products, param, sendBeamMessageReq);
        // <BeamProductCard>${index}:${productId}:${recommendReason}</BeamProductCard>
        AIAnswerData answerData = aiChatUtil.chat("beam_product_recommend", userInput, extraInfo, dynamicPrompt);

        if (answerData == null || StringUtils.isEmpty(answerData.getAnswer())) {
            BufferUtils.writeMainTextBuffer(PilotBufferItemDO.builder().type(BufferItemTypeEnum.MAIN_TEXT.getType())
                    .data("抱歉哦，该门店暂时没有商品").build());
            return new AIAnswerData(AIAnswerTypeEnum.TEXT.getType(), "抱歉哦，该门店暂时没有适合您的商品");
        }
        return answerData;
    }


    private ShopProductSearchParam buildShopProductSearchParam(BeamShopProductRetrievalParam beamShopProductRetrievalParam) {
        ShopProductSearchParam param = new ShopProductSearchParam();
        param.setProductSemanticCondition(beamShopProductRetrievalParam.getProductSemanticCondition());
        param.setAiServiceContext(beamShopProductRetrievalParam.getAiServiceContext());
        param.setBookDate(beamShopProductRetrievalParam.getBookDate());
        param.setShowText(beamShopProductRetrievalParam.getShowText());
        return param;
    }

    private String buildDynamicPrompt(List<ProductRetrievalData> products, ShopProductSearchParam param, SendBeamMessageReq sendBeamMessageReq) {
        String productMarkdown = toMarkdown(products, param);
        String dynamicPrompt = lionConfigUtil.getBeamProductRecommendTemplate();
        String userProfile = sendBeamMessageReq.getContext().getUser_profile();
        dynamicPrompt = dynamicPrompt.replace("${user_profile}",
                StringUtils.defaultString(userProfile));
        dynamicPrompt = dynamicPrompt.replace("${user_input}",
                StringUtils.defaultString(sendBeamMessageReq.getOriginal_user_input().getContent()));
        dynamicPrompt = dynamicPrompt.replace("${user_understanding}",
                StringUtils.defaultString(sendBeamMessageReq.getContext().getUser_understanding()));
        dynamicPrompt = dynamicPrompt.replace("${product_options}", productMarkdown);
        dynamicPrompt = dynamicPrompt.replace("${fixed_knowledge}",
                StringUtils.defaultString(sendBeamMessageReq.getContext().getFixed_knowledge()));
//        List<Message> chatHistory = beamChatMemoryProvider.getMemory(QueryMemoryContext.builder().aiServiceContext(param.aiServiceContext).limit(10).build());
//        dynamicPrompt = dynamicPrompt.replace("${chat_history}",
//                JSONObject.toJSONString(chatHistory));
        return dynamicPrompt;
    }

    private void fillBufferExtraData(Map<String, Object> extraInfo, List<ProductRetrievalData> products) {
        Map<String, Object> bufferExtra = null;
        int productType = getProductType(products);
        if (productType == 1) {
            bufferExtra = getRecommendDealInfoForDeal(products);
        } else {
            bufferExtra = getRecommendDealInfoForReserve(products);
        }
        log.info("【店内推品扩展数据】extra:{}",JsonCodec.encodeWithUTF8(bufferExtra));
        extraInfo.put(AIServiceExtraKeyEnum.BUFFER_EXTRA.getKey(), bufferExtra);
    }

    private List<ProductRetrievalData> getShopProducts(ShopProductSearchParam param) {
        // 使用CompletableFuture异步查询团购和预订商品，团购商品兜底一定会查询，非预订优先行业，预订商品不会返回商品结果
        CompletableFuture<List<ProductRetrievalData>> shopProductsFuture = CompletableFuture
                .supplyAsync(() -> searchProcessService.searchShopProducts(param), beamShopProductRecommendPool.getExecutor());
        CompletableFuture<List<ProductRetrievalData>> reserveProductsFuture = needQueryReserve(param)
                ? CompletableFuture.supplyAsync(() -> searchProcessService.searchShopReserveProducts(param), beamShopProductRecommendPool.getExecutor())
                : CompletableFuture.completedFuture(Collections.emptyList());

        try {
            // 选择结果返回
            CompletableFuture<List<ProductRetrievalData>> finalProductsFuture = CompletableFuture
                    .allOf(shopProductsFuture, reserveProductsFuture).thenApply(none -> {
                        List<ProductRetrievalData> shopProducts = shopProductsFuture.join();
                        List<ProductRetrievalData> reserveProducts = reserveProductsFuture.join();
                        if (CollectionUtils.isNotEmpty(reserveProducts)) {
                            return reserveProducts;
                        }
                        return shopProducts;
                    });
            return finalProductsFuture.join();
        } catch (Exception e) {
            log.error("异步查询商品信息失败", e);
            return Collections.emptyList();
        }
    }

    private boolean needQueryReserve(ShopProductSearchParam param) {
        // 查询点评门店后台类目id
        int shopBackCategoryId = getShopBackCategoryId(buildDpShopId(param));
        return NEED_QUERY_RESERVE_CATEGORY_IDS.contains(shopBackCategoryId);
    }

    private long buildDpShopId(ShopProductSearchParam param) {
        UserContext userContext = PluginContextUtil.getUserContext(param);
        boolean isMt = ImAccountTypeUtils.isMtUserId(userContext.getImUserId());

        // 转化点评门店ID为美团门店ID
        return isMt ? getDpShopId(param.getShopId()) : param.getShopId();
    }

    private long getDpShopId(long mtShopId) {
        return shopAclService.loadDpShopIdByMtShopId(mtShopId);
    }

    private int getShopBackCategoryId(long dpShopId) {
        DpPoiDTO dpPoiDTO = shopAclService.getShopInfo(dpShopId);
        if (dpPoiDTO == null || CollectionUtils.isEmpty(dpPoiDTO.getShopBackCategoryList())) {
            return 0;
        }
        return dpPoiDTO.getShopBackCategoryList().get(0).getCategoryId();
    }

    private Map<String, Object> getRecommendDealInfoForDeal(List<ProductRetrievalData> products) {
        Map<String, Object> recommendDealInfo = new HashMap<>();
        for (int i = 0; i < products.size(); i++) {
            ProductRetrievalData product = products.get(i);
            long productId = product.getMtProductId();
            JSONObject productInfo = new JSONObject();
            productInfo.put("dealId", productId);
            productInfo.put("dealName", product.getTitle());
            productInfo.put("finalPrice", product.getSalePrice());
            productInfo.put("subTitle", JsonCodec.encodeWithUTF8(product.getSubTitles()));
            productInfo.put("dealImages", StringUtils.isEmpty(product.getHeadPic()) ? new ArrayList<>()
                    : new ArrayList<>(Arrays.asList(product.getHeadPic())));
            productInfo.put("dealTags", new JSONArray());
            productInfo.put("recommendReason", joinRecommendReason(product));
            productInfo.put("productType", product.getProductType());
            recommendDealInfo.put(String.valueOf(productId), productInfo);
        }
        return recommendDealInfo;
    }

    private Map<String, Object> getRecommendDealInfoForReserve(List<ProductRetrievalData> products) {
        Map<String, Object> recommendDealInfo = new LinkedHashMap<>();
        for (int i = 0; i < products.size(); i++) {
            ProductRetrievalData product = products.get(i);
            long productId = product.getMtProductId();
            JSONObject productInfo = new JSONObject();
            productInfo.put("productId", productId);
            if (CollectionUtils.isNotEmpty(product.getRelatedMtProductIds())) {
                productInfo.put("relatedProductIdList", product.getRelatedMtProductIds());
            }
            productInfo.put("dealName", product.getTitle());
            productInfo.put("subTitle", JsonCodec.encodeWithUTF8(product.getSubTitles()));
            productInfo.put("finalPrice", product.getSalePrice());
            productInfo.put("dealImages", StringUtils.isEmpty(product.getHeadPic()) ? new ArrayList<>()
                    : new ArrayList<>(Arrays.asList(product.getHeadPic())));
            if (product.getSalesNum() != null && product.getSalesNum() > 0) {
                productInfo.put("saleNum", product.getSalesNum());
            }
            productInfo.put("stocks", product.getStocks());
            if (product.getPoolData() != null) {
                productInfo.put("poolData", product.getPoolData());
            }
            productInfo.put("productCategoryId", product.getCategoryId());
            productInfo.put("productType", product.getProductType());
            recommendDealInfo.put(String.valueOf(productId), productInfo);
        }
        return recommendDealInfo;
    }

    private String joinRecommendReason(ProductRetrievalData product) {
        if (product == null) {
            return StringUtils.EMPTY;
        }

        List<String> subTitles = product.getSubTitles();
        String recommendReason = "";
        if (StringUtils.isNotEmpty(product.getSalePrice())) {
            recommendReason = recommendReason + "\n价格：¥" + product.getSalePrice();
        }
        if (CollectionUtils.isNotEmpty(subTitles)) {
            recommendReason = recommendReason + "\n内容：" + String.join("|", subTitles);
        }
        return recommendReason;
    }

    private int getProductType(List<ProductRetrievalData> products) {
        if (CollectionUtils.isEmpty(products)) {
            // 团购agent兜底
            return 1;
        }
        return Optional.ofNullable(products.get(0)).map(ProductRetrievalData::getProductType).orElse(1);
    }

    private String toMarkdown(List<ProductRetrievalData> products, ShopProductSearchParam param) {
        if (CollectionUtils.isEmpty(products)) {
            return "未找到相关商品";
        }
        UserContext userContext = PluginContextUtil.getUserContext(param);
        boolean isMt = ImAccountTypeUtils.isMtUserId(userContext.getImUserId());
        int productType = getProductType(products);

        return buildMarkDown(products, productType, isMt);
    }

    private String buildMarkDown(List<ProductRetrievalData> products, Integer productType, boolean isMt) {
        if (productType == 1) {
            return buildDealMarkDown(products, isMt);
        }
        return buildReserveMarkDown(products, isMt);
    }

    private String buildReserveMarkDown(List<ProductRetrievalData> products, boolean isMt) {
        StringBuilder markdown = new StringBuilder();
        // 使用字段注释构建表头
        markdown.append("| 商品名称 | productId | 门店类目 | 价格 | 销量 | 商品适用人数 | 商品时段库存 | 商品详情 | 商品关联场次拼场状态 |\n");
        markdown.append(
                "|-------- |-------|---------|----------|-----|------|------------|--------|-------------------|\n");

        // 构建表格内容
        for (ProductRetrievalData product : products) {
            markdown.append(String.format("| %s | %d | %s | %s | %d | %s | %s | %s | %s |\n",
                    supplyUtils.escapeMarkdown(product.getTitle()), product.getMtProductId(),
                    supplyUtils.escapeMarkdown(buildShopCategoryName(product.getCategoryId())),
                    supplyUtils.escapeMarkdown(product.getSalePrice()), product.getSalesNum(),
                    supplyUtils.escapeMarkdown(JSON.toJSONString(buildApplyPersonNum(product))),
                    supplyUtils.escapeMarkdown(JSON.toJSONString(buildStockDisplayInfo(product.getStocks()))),
                    supplyUtils.escapeMarkdown(buildProductDetailDisplayInfo(product)),
                    supplyUtils.escapeMarkdown(JSON.toJSONString(product.getPoolData()))));
        }
        return markdown.toString();
    }

    private String buildProductDetailDisplayInfo(ProductRetrievalData product) {
        if (needAggregatePackageDetailInfo(product)) {
            return StringUtils.EMPTY;
        }
        return JSON.toJSONString(product.getProductDetails());
    }

    private boolean needAggregatePackageDetailInfo(ProductRetrievalData product) {
        if (product.getCategoryId() == null) {
            return false;
        }
        return POIIndustryType.KTV.getSecondBackCategoryId() == product.getCategoryId().intValue();
    }

    private Object buildApplyPersonNum(ProductRetrievalData product) {
        if (product == null) {
            return "不限制人数";
        }
        ProductDetailData productDetails = product.getProductDetails();
        if (productDetails == null) {
            return "不限制人数";
        }
        Map<String, Object> saleInfo = productDetails.getSaleInfo();
        if (MapUtils.isEmpty(saleInfo)) {
            return "不限制人数";
        }
        return saleInfo.get("商品适用人数") == null ? "不限制人数" : saleInfo.get("商品适用人数");
    }

    private String buildShopCategoryName(Long categoryId) {
        if (categoryId == null || categoryId <= 0) {
            return StringUtils.EMPTY;
        }
        POIIndustryType industryType = POIIndustryType.getBySecondBackCategoryId(categoryId.intValue());
        if (industryType == null) {
            return StringUtils.EMPTY;
        }
        return industryType.getDesc();
    }

    private Map<String, Object> buildStockDisplayInfo(ProductStockData stockData) {
        Map<String, Object> stockDisplayInfo = new HashMap<>();
        if (stockData == null || CollectionUtils.isEmpty(stockData.getTimeSlices())) {
            return stockDisplayInfo;
        }
        if (stockData.getEarliestBookableTime() != null) {
            stockDisplayInfo.put("最早可订时间", formatter.print(new DateTime(stockData.getEarliestBookableTime())));
        }
        if (CollectionUtils.isNotEmpty(stockData.getTimeSlices())) {
            stockDisplayInfo.put("时间片可订态列表", buildTimeSliceDisplayInfo(stockData.getTimeSlices()));
        }
        return stockDisplayInfo;
    }

    private Map<String, Object> buildTimeSliceDisplayInfo(List<TimeSliceM> timeSlices) {
        Map<String, Object> timeSliceDisplayInfo = new HashMap<>();
        for (TimeSliceM timeSlice : timeSlices) {
            timeSliceDisplayInfo.put("时间片开始时间", formatter.print(new DateTime(timeSlice.getStartTime())));
            if (timeSlice.getEndTime() != null) {
                timeSliceDisplayInfo.put("时间片结束时间", formatter.print(new DateTime(timeSlice.getEndTime())));
            }
            timeSliceDisplayInfo.put("时间片可订态", timeSlice.isAvailable() ? "可订" : "不可订");
        }
        return timeSliceDisplayInfo;
    }

    private String buildDealMarkDown(List<ProductRetrievalData> products, boolean isMt) {
        StringBuilder markdown = new StringBuilder();
        // 使用字段注释构建表头
        markdown.append("| 商品名称 | productId | 价格 | 销量 | 服务类型 | 服务项目 |\n");
        markdown.append("|---------|-----------|-----|-----|---------|--------|\n");

        int dealProductLimit = Lion.getInt("com.sankuai.mim.pilot", "com.sankuai.mim.pilot.beam.deal.product.limit",
                products.size());

        // 构建表格内容
        for (int i = 0; i < Math.min(dealProductLimit, products.size()); i++) {
            ProductRetrievalData product = products.get(i);
            markdown.append(String.format("| %s | %d | %s | %d | %s | %s |\n",
                    supplyUtils.escapeMarkdown(product.getTitle()),
                    isMt ? product.getMtProductId() : product.getDpProductId(),
                    supplyUtils.escapeMarkdown(product.getSalePrice()),
                    product.getSalesNum(), supplyUtils.escapeMarkdown(product.getServiceType()),
                    supplyUtils.escapeMarkdown(supplyUtils.buildServiceProject(product))
            // supplyUtils.escapeMarkdown(supplyUtils.buildIntroduction(product))
            ));
        }
        return markdown.toString();
    }

    private String getShowText(ShopProductSearchParam param) {
        if (param == null || StringUtils.isEmpty(param.getShowText())) {
            return "正在为你检索...";
        }
        return param.getShowText();
    }
}
