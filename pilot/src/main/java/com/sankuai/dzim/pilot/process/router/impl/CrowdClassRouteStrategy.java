package com.sankuai.dzim.pilot.process.router.impl;

import com.google.common.collect.Lists;
import com.meituan.itc.udm.mustang.thrift.JudgeResponse;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.acl.MustangAclService;
import com.sankuai.dzim.pilot.api.data.search.generative.QueryAnswerRequest;
import com.sankuai.dzim.pilot.process.router.TemplateRouteStrategy;
import com.sankuai.dzim.pilot.process.router.data.RouteResponse;
import com.sankuai.dzim.pilot.process.router.data.TemplateConfig;
import com.sankuai.dzim.pilot.utils.LionConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/09/29 16:09
 */
@Component
@Order(3)
@Slf4j
public class CrowdClassRouteStrategy implements TemplateRouteStrategy {
    public static final String ROUTE = "crowdClassRoute";

    private static final String CROWD_MISS = "miss";

    @Override
    public boolean match(String route) {
        return route.contains(ROUTE);
    }

    @Autowired
    private MustangAclService mustangAclService;

    @Autowired
    private LionConfigUtil lionConfigUtil;

    @Override
    public RouteResponse getRouteResult(QueryAnswerRequest request) {
        String controlType = getControlType(request);
        if (StringUtils.isBlank(controlType)) {
            return null;
        }
        RouteResponse routeResponse = new RouteResponse();
        routeResponse.setCrowdIds(Lists.newArrayList(controlType));
        routeResponse.setControlType(controlType);
        routeResponse.setBizType(request.getBizType());
        return routeResponse;
    }

    private String getControlType(QueryAnswerRequest request) {
        JudgeResponse judgeResponse = null;
        try {
            judgeResponse = mustangAclService.judgeCrowdClass(request.getUserId());
        } catch (TException e) {
            LogUtils.logFailLog(log, TagContext.builder().action("mustangService").build(),
                    new WarnMessage("CrowdClassRouteStrategy", "获取人群判定异常", ""), request.getUserId(), null, e);
            return null;
        }

        if(judgeResponse == null || judgeResponse.getStatusCode() != 0){
            LogUtils.logReturnInfo(log, TagContext.builder().action("mustangService").build(),
                    new WarnMessage("CrowdClassRouteStrategy", "获取人群判定失败", ""), request.getUserId(), null);
            return null;
        }

        // 选定人群号
        List<Integer> inCrowds = judgeResponse.getJudgeResults().entrySet().stream()
                .filter(Objects::nonNull)
                .filter(result -> result.getValue() == 1)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());


        return CollectionUtils.isEmpty(inCrowds) ? CROWD_MISS : getCrowdId(request, inCrowds);
    }

    private String getCrowdId(QueryAnswerRequest request, List<Integer> inCrowds){
        TemplateConfig templateConfig = lionConfigUtil.getBizType2TemplateConfig().get(String.valueOf(request.getBizType()));
        Set<String> configCrowds = templateConfig.getConfigCrowds(request.getQuestion());

        for (Integer inCrowd : inCrowds) {
            String inCrowdStr = String.valueOf(inCrowd);
            if (configCrowds.stream().anyMatch(configCrowd -> configCrowd.contains(inCrowdStr))) {
                return inCrowdStr;
            }
        }
        return CROWD_MISS;
    }
}
