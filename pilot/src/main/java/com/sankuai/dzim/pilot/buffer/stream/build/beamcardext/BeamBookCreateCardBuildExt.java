package com.sankuai.dzim.pilot.buffer.stream.build.beamcardext;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.frog.sdk.util.CollectionUtils;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.dzim.pilot.buffer.core.PilotBufferItemDO;
import com.sankuai.dzim.pilot.buffer.enums.StreamEventCardTypeEnum;
import com.sankuai.dzim.pilot.buffer.stream.build.beamcardext.data.BeamCardBuildContext;
import com.sankuai.dzim.pilot.buffer.stream.build.ext.data.BeamBookCreateData;
import com.sankuai.dzim.pilot.buffer.stream.vo.BeamChoiceVO;
import com.sankuai.dzim.pilot.enums.BeamResourceTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * @author: zhouyibing
 * @date: 2025/5/13
 */
@Component
@Slf4j
public class BeamBookCreateCardBuildExt implements BeamCardBuildExt {

    @Override
    public boolean acceptByStreamCardType(String streamCardType) {
        return StreamEventCardTypeEnum.BEAM_BOOK_CREATE_CARD.getType().equals(streamCardType);
    }

    @Override
    public boolean acceptByBeamResourceType(String resourceType) {
        return BeamResourceTypeEnum.FWLS_BOOK_CREATE_ORDER.getKey().equals(resourceType);
    }

    @Override
    public List<BeamChoiceVO.DeltaResource> buildResources(BeamCardBuildContext context) {
        List<BeamChoiceVO.DeltaResource> deltaResources = Lists.newArrayList();

        List<PilotBufferItemDO> tagItems = context.getTagItems();
        if (CollectionUtils.isEmpty(tagItems)) {
            return Collections.emptyList();
        }

        PilotBufferItemDO itemDO = tagItems.get(0);
        BeamBookCreateData bookCreateData = JsonCodec.decode(JsonCodec.encodeWithUTF8(itemDO.getExtra()), BeamBookCreateData.class);
        BeamChoiceVO.DeltaResource deltaResource = buildDeltaResource(bookCreateData);
        deltaResources.add(deltaResource);
        return deltaResources;
    }

    @Override
    public String cardDecode(String cardContent) {
        cardContent = StringEscapeUtils.unescapeJava(cardContent);
        JSONObject jsonObject = JSONObject.parseObject(cardContent);
        StringBuilder sb = new StringBuilder();
        sb.append("\n以下是门店预约信息\n");
        sb.append("shopId：").append(MapUtils.getString(jsonObject, "shopId")).append("\n");
        sb.append("预约单Id：").append(MapUtils.getString(jsonObject, "orderId")).append("\n");
        return sb.toString();
    }

    private BeamChoiceVO.DeltaResource buildDeltaResource(BeamBookCreateData bookCreateData) {
        BeamChoiceVO.DeltaResource deltaResource = new BeamChoiceVO.DeltaResource();
        deltaResource.setResource_index("FWLS-1");
        deltaResource.setResource_type(BeamResourceTypeEnum.FWLS_BOOK_CREATE_ORDER.getKey());
        BeamChoiceVO.ResourceMetaData metaData = new BeamChoiceVO.ResourceMetaData();
        metaData.setBiz_data(JSON.toJSONString(bookCreateData));
        deltaResource.setMetadata(metaData);
        return deltaResource;
    }
}
