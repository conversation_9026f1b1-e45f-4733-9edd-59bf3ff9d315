package com.sankuai.dzim.pilot.gateway.api.aibook;

import com.dianping.vc.web.api.API;
import com.dianping.vc.web.api.URL;
import com.dianping.vc.web.api.VCAssert;
import com.dianping.vc.web.biz.client.api.AppContext;
import com.dianping.vc.web.biz.client.api.AppContextAware;
import com.dianping.vc.web.biz.client.api.MTUserIdAware;
import com.dianping.vc.web.biz.client.api.UserIdAware;
import com.sankuai.dzim.pilot.api.enums.assistant.PlatformEnum;
import com.sankuai.dzim.pilot.gateway.api.aibook.data.AIBookEntryVO;
import com.sankuai.dzim.pilot.process.aibook.AIBookProcessService;
import com.sankuai.dzim.pilot.process.aibook.data.AIBookEntryQueryRequest;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@Setter
@URL(url = "/dzim/pilot/ai/book/poi/entry/query", methods = "GET")
public class AIBookEntryQueryAPI implements API, UserIdAware, MTUserIdAware, AppContextAware {

    private AppContext appContext;

    private long userId;

    private long mTUserId;

    private long shopid;

    private int cityid;

    @Autowired
    private AIBookProcessService aiBookProcessService;

    @Override
    public Object execute() {
        validateParams();

        AIBookEntryQueryRequest request = buildAIBookEntryQueryRequest();
        AIBookEntryVO aiBookEntryVO = aiBookProcessService.queryAIBookEntry(request);
        return aiBookEntryVO;
    }

    private AIBookEntryQueryRequest buildAIBookEntryQueryRequest() {
        int platform = PlatformEnum.getByName(appContext.getPlatform()).getType();
        AIBookEntryQueryRequest request = new AIBookEntryQueryRequest();
        request.setPlatform(platform);
        request.setShopId(shopid);
        request.setCityId(cityid);
        request.setUnionId(appContext.getUnionid());
        return request;
    }

    private void validateParams() {
        VCAssert.isTrue(userId != 0 || mTUserId != 0, API.UNLOGIN_CODE, "用户未登录");
        VCAssert.isTrue(appContext.getPlatform().equals(PlatformEnum.DP.getName()) || appContext.getPlatform().equals(PlatformEnum.MT.getName()), API.INVALID_PARAM_CODE,"平台参数不合法");
        VCAssert.isTrue(shopid > 0, API.INVALID_PARAM_CODE, "无效的门店id");
        VCAssert.isTrue(cityid > 0, API.INVALID_PARAM_CODE, "无效的城市id");
    }


}
