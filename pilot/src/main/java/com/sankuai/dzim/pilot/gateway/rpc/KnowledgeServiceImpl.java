package com.sankuai.dzim.pilot.gateway.rpc;

import com.google.common.collect.Lists;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonServer;
import com.sankuai.dzim.pilot.api.KnowledgeService;
import com.sankuai.dzim.pilot.api.data.*;
import com.sankuai.dzim.pilot.process.KnowledgeProcessService;
import com.sankuai.dzim.pilot.process.QuestionScoreProcessService;
import com.sankuai.dzim.pilot.process.data.AddBizKnowledgeReq;
import com.sankuai.dzim.pilot.process.data.AddMerchantKnowledgeReq;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@MdpPigeonServer
public class KnowledgeServiceImpl implements KnowledgeService {

    @Autowired
    private KnowledgeProcessService knowledgeProcessService;
    @Autowired
    private QuestionScoreProcessService questionScoreProcessService;

    @Override
    public long addBizKnowledge(AddBizKnowledgeRequest request) {
        if (request == null) {
            return 0;
        }

        return knowledgeProcessService.addBizKnowledge(convert2AddBizKnowledgeReq(request));
    }

    private AddBizKnowledgeReq convert2AddBizKnowledgeReq(AddBizKnowledgeRequest request) {
        AddBizKnowledgeReq addBizKnowledgeReq = new AddBizKnowledgeReq();
        addBizKnowledgeReq.setBizType(request.getBizType());
        addBizKnowledgeReq.setQuestion(request.getQuestion());
        addBizKnowledgeReq.setAnswer(request.getAnswer());
        addBizKnowledgeReq.setEditor(request.getEditor());
        return addBizKnowledgeReq;
    }

    @Override
    public int batchAddBizKnowledge(String fileName, List<AddBizKnowledgeRequest> requests) {
        List<AddBizKnowledgeReq> reqs = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(requests)) {
            reqs = requests.stream().map(this::convert2AddBizKnowledgeReq).collect(Collectors.toList());
        }
        return knowledgeProcessService.batchAddBizKnowledge(fileName, reqs);
    }

    @Override
    public int batchAddBeautyBizKnowledge(String fileName, List<AddBizKnowledgeRequest> requests) {
        List<AddBizKnowledgeReq> reqs = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(requests)) {
            reqs = requests.stream().map(this::convert2AddBizKnowledgeReq).collect(Collectors.toList());
        }
        return knowledgeProcessService.batchAddBeautyKnowledge(fileName, reqs);
    }

    @Override
    public int batchAddBeautyKnowledgeAfterGPT4(String fileName, int start, int end) {
        List<AddBizKnowledgeReq> reqs = Lists.newArrayList();
        List<AddBizKnowledgeRequest> requests = Lists.newArrayList();

        if (CollectionUtils.isNotEmpty(requests)) {
            reqs = requests.stream().map(this::convert2AddBizKnowledgeReq).collect(Collectors.toList());
        }
        return knowledgeProcessService.batchAddBeautyKnowledgeAfterGPT4(fileName, start, end, reqs);
    }

    @Override
    public boolean deleteBizKnowledge(long bizKnowledgeId, String editor) {
        return knowledgeProcessService.deleteBizKnowledge(bizKnowledgeId, editor);
    }

    @Override
    public int batchDeleteBizKnowledge(List<Long> bizKnowledgeIds, String editor) {
        return knowledgeProcessService.batchDeleteBizKnowledge(bizKnowledgeIds, editor);
    }

    @Override
    public long addMerchantKnowledge(AddMerchantKnowledgeRequest request) {
        if (request == null) {
            return 0;
        }
        return knowledgeProcessService.addMerchantKnowledge(convert2AddMerchantKnowledgeReq(request));
    }

    private AddMerchantKnowledgeReq convert2AddMerchantKnowledgeReq(AddMerchantKnowledgeRequest request) {
        AddMerchantKnowledgeReq addMerchantKnowledgeReq = new AddMerchantKnowledgeReq();
        addMerchantKnowledgeReq.setType(request.getType());
        addMerchantKnowledgeReq.setSubjectId(request.getSubjectId());
        addMerchantKnowledgeReq.setQuestion(request.getQuestion());
        addMerchantKnowledgeReq.setAnswer(request.getAnswer());
        addMerchantKnowledgeReq.setEditor(request.getEditor());
        return addMerchantKnowledgeReq;
    }

    @Override
    public int batchAddMerchantKnowledge(String fileName, List<AddMerchantKnowledgeRequest> requests) {
        List<AddMerchantKnowledgeReq> reqs = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(reqs)) {
            reqs = requests.stream().map(this::convert2AddMerchantKnowledgeReq).collect(Collectors.toList());
        }
        return knowledgeProcessService.batchAddMerchantKnowledge(fileName, reqs);
    }

    @Override
    public boolean deleteMerchantKnowledge(long merchantKnowledgeId, String editor) {
        return knowledgeProcessService.deleteMerchantKnowledge(merchantKnowledgeId, editor);
    }

    @Override
    public int batchDeleteMerchantKnowledge(List<Long> merchantKnowledgeIds, String editor) {
        return knowledgeProcessService.batchDeleteMerchantKnowledge(merchantKnowledgeIds, editor);
    }

    @Override
    public List<BizKnowledgeDTO> searchBizKnowledge(int bizType, String query, int topK) {
        return knowledgeProcessService.searchBizKnowledge(bizType, query, topK);
    }

    @Override
    public List<MerchantKnowledgeDTO> searchMerchantKnowledge(String query, int type, String subjectId, int topK) {
        return knowledgeProcessService.searchMerchantKnowledge(query, type, subjectId, topK);
    }

    @Override
    public List<RetrievalKnowledgeDTO> retrievalKnowledge(RetrievalKnowledgeRequest request) {
        Assert.isTrue(StringUtils.isNotBlank(request.getRetrievalName()), "检索器名称不能为空");
        Assert.isTrue(request.getTopK() > 0, "返回前几的知识必须大于0");
        Assert.isTrue(MapUtils.isNotEmpty(request.getParams()), "查询参数不能为空");

        return knowledgeProcessService.retrievalKnowledge(request);
    }

    @Override
    public int batchAddNote(String fileName) {
        return knowledgeProcessService.batchAddNoteInfo(fileName);
    }

    @Override
    public int batchAddReview(String fileName) {
        return knowledgeProcessService.batchAddReview(fileName);
    }

    @Override
    public boolean deleteReview(long reviewId) {
        return knowledgeProcessService.deleteReview(reviewId);
    }

    @Override
    public int addBLZQuestionScore(String fileName, int size) {
        return questionScoreProcessService.addBLZQuestionScore(fileName, size);
    }
}
