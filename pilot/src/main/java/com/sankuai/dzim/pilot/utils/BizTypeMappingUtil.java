package com.sankuai.dzim.pilot.utils;

import com.dianping.vc.web.api.API;
import com.dianping.vc.web.api.VCAssert;
import com.meituan.beauty.sakura.behavior.enums.PlatformTypeEnum;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.dzim.pilot.acl.ShopAclService;
import com.sankuai.dzim.pilot.acl.data.ShopCategoryData;
import com.sankuai.dzim.pilot.api.enums.search.generative.GenerativeSearchTypeEnum;
import com.sankuai.dzim.pilot.process.data.PageSourceTypeEnum;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2024/08/20 16:47
 */
@Component
public class BizTypeMappingUtil {
    /**
     * 二级类目和页面类型到BizType的映射
     */
    private static final Map<CategorySourceType, GenerativeSearchTypeEnum> SECOND_CATEGORY_ID2_BIZ_TYPE = new HashMap<>();

    @Autowired
    private ShopAclService shopAclService;

    @ConfigValue(key = "com.sankuai.mim.pilot.channel.biz.code.pilot.biz.type.map", defaultValue = "{}")
    private Map<String, Integer> bizCode2BizTypeMap;

    static {
        SECOND_CATEGORY_ID2_BIZ_TYPE.put(new CategorySourceType(2898, 1), GenerativeSearchTypeEnum.BEAUTY_ASK_TATTOO_CHANNEL);
        SECOND_CATEGORY_ID2_BIZ_TYPE.put(new CategorySourceType(2898, 2), GenerativeSearchTypeEnum.BEAUTY_ASK_TATTOO_POI);
        SECOND_CATEGORY_ID2_BIZ_TYPE.put(new CategorySourceType(2898, 3), GenerativeSearchTypeEnum.BEAUTY_ASK_TATTOO_POI);
        SECOND_CATEGORY_ID2_BIZ_TYPE.put(new CategorySourceType(158, 2), GenerativeSearchTypeEnum.BEAUTY_ASK_COSMETIC);
        SECOND_CATEGORY_ID2_BIZ_TYPE.put(new CategorySourceType(158, 3), GenerativeSearchTypeEnum.BEAUTY_ASK_COSMETIC);
        SECOND_CATEGORY_ID2_BIZ_TYPE.put(new CategorySourceType(182, 1), GenerativeSearchTypeEnum.BEAUTY_ASK_DENTAL);
        SECOND_CATEGORY_ID2_BIZ_TYPE.put(new CategorySourceType(182, 2), GenerativeSearchTypeEnum.BEAUTY_ASK_DENTAL);
        SECOND_CATEGORY_ID2_BIZ_TYPE.put(new CategorySourceType(182, 3), GenerativeSearchTypeEnum.BEAUTY_ASK_DENTAL);
        SECOND_CATEGORY_ID2_BIZ_TYPE.put(new CategorySourceType(183, 2), GenerativeSearchTypeEnum.BEAUTY_ASK_MEDICAL_BEAUTY);
        SECOND_CATEGORY_ID2_BIZ_TYPE.put(new CategorySourceType(183, 3), GenerativeSearchTypeEnum.BEAUTY_ASK_MEDICAL_BEAUTY);
    }

    /**
     * 查询bizType
     *
     * @param shopId     门店ID
     * @param platform   平台类型 1-点评 2-美团
     * @param sourceType 页面类型 1-频道页 2-商详页 3-团详页
     * @param bizCode    如果是频道页，则传入业务code
     * @return
     */
    public int getBizType(long shopId, int platform, int sourceType, String bizCode) {
        ShopCategoryData shopCategoryData = getShopCategoryData(shopId, platform);
        return buildBizType(shopCategoryData, sourceType, bizCode);
    }


    public ShopCategoryData getShopCategoryData(long shopId, int platform) {
        if (shopId <= 0) {
            return null;
        }
        long dpShopId = getDpShopId(shopId, platform);
        // 查询门店类目
        return shopAclService.loadShopFrontCatePath(dpShopId);
    }

    private long getDpShopId(long shopId, int platform) {
        if (platform == PlatformTypeEnum.MT.code) {
            VCAssert.isTrue(shopId > 0, API.INVALID_PARAM_CODE, "shopId应大于0");
            return shopAclService.loadDpShopIdByMtShopId(shopId);
        }
        return shopId;
    }

    private int buildBizType(ShopCategoryData shopCategoryData, int sourceType, String bizCode) {
        if (sourceType == PageSourceTypeEnum.CHANNEL_PAGE.getType()) {
            return bizCode2BizTypeMap.getOrDefault(bizCode, 0);
        }
        if (sourceType == PageSourceTypeEnum.POI_PAGE.getType() || sourceType == PageSourceTypeEnum.DEAL_PAGE.getType()) {
            int secondCategoryId = Optional.ofNullable(shopCategoryData).map(ShopCategoryData::getForeSecondCateId).orElse(0);
            GenerativeSearchTypeEnum bizTypeEnum = SECOND_CATEGORY_ID2_BIZ_TYPE.get(new CategorySourceType(secondCategoryId, sourceType));
            return bizTypeEnum == null ? 0 : bizTypeEnum.getType();
        }
        return 0;
    }

    @EqualsAndHashCode
    @AllArgsConstructor
    static class CategorySourceType {

        /**
         * 点评前台二级类目
         */
        int secondCategoryId;

        /**
         * 来源页面 1-频道页 2-商详页 3-团详页
         */
        int sourceType;
    }
}
