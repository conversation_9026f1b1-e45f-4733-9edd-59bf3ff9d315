package com.sankuai.dzim.pilot.gateway.api.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/8/1 21:29
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@MobileDo
public class MessageOperateVO implements Serializable {

    @MobileDo.MobileField(key = 0xe95f)
    private int isSuccess;
}
