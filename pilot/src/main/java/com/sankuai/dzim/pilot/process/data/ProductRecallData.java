package com.sankuai.dzim.pilot.process.data;

import lombok.Data;

/**
 * 商品（团单/泛商品）召回数据（搜索or推荐召回）
 * <AUTHOR>
 */
@Data
public class ProductRecallData {

    /**
     * 美团商品id
     * 团单id分平台，泛商品id不区分平台
     */
    private long mtProductId;

    /**
     * 点评商品id
     * 团单id分平台，泛商品id不区分平台
     */
    private long dpProductId;

    /**
     * 商品类型
     * 1-团单，2-泛商品
     */
    private int type;

    /**
     * 商品关联的mt商户id
     */
    private long relatedMtShopId;

    /**
     * 商品关联的dp商户id
     */
    private long relatedDpShopId;
}
