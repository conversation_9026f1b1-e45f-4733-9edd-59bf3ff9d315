package com.sankuai.dzim.pilot.process.search.data;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
public class ObjAttrM {

    private String name;

    private Object value;

    private String valueStr;

    public ObjAttrM(String name, Object value) {
        this.name = name;
        this.value = value;
    }

    public ObjAttrM(String name, Object value, String valueStr) {
        this.name = name;
        this.value = value;
        this.valueStr = valueStr;
    }
}
