package com.sankuai.dzim.pilot.process.aireservebook.enums;

import com.google.common.collect.Maps;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * 门店行业类型(后台二级类目)
 * <AUTHOR>
 */
@Getter
public enum POIIndustryType {
    HAIR(38, "美发"),
    FOOT_MASSAGE(48, "按摩/足疗"),
    KTV(51, "KTV"),
    ROLE_PLAY(2634, "剧本杀"),
    BACKROOM(225, "密室"),
    CHESS(2139, "棋牌"),
    BALL(585, "球类运动"),
    GYM(366, "体育场馆"),
    BAR(263, "酒吧"),
    PET_SHOP(114, "宠物店"),
    ;

    private static final Map<Integer, POIIndustryType> CATEGORY_ID_MAP = Maps.newHashMap();

    private static final Map<String, POIIndustryType> CATEGORY_NAME_MAP = Maps.newHashMap();

    static {
        for (POIIndustryType type : POIIndustryType.values()) {
            CATEGORY_ID_MAP.put(type.getSecondBackCategoryId(), type);
            CATEGORY_NAME_MAP.put(type.getDesc(), type);
        }
    }

    POIIndustryType(int secondBackCategoryId, String desc) {
        this.secondBackCategoryId = secondBackCategoryId;
        this.desc = desc;
    }

    public static POIIndustryType getBySecondBackCategoryId(Integer secondBackCategoryId) {
        if (secondBackCategoryId == null) {
            return null;
        }
        return CATEGORY_ID_MAP.get(secondBackCategoryId);
    }

    public static POIIndustryType getBySecondBackCategoryName(String secondBackCategoryName) {
        if (StringUtils.isBlank(secondBackCategoryName)) {
            return null;
        }
        return CATEGORY_NAME_MAP.get(secondBackCategoryName);
    }


    private final int secondBackCategoryId;
    private final String desc;
}
