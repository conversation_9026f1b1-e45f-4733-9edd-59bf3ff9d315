package com.sankuai.dzim.pilot.scene.task;

import com.sankuai.dzim.pilot.chain.data.AIAnswerData;
import com.sankuai.dzim.pilot.domain.message.AssistantMessage;
import com.sankuai.dzim.pilot.domain.message.Message;
import com.sankuai.dzim.pilot.domain.message.UserMessage;
import com.sankuai.dzim.pilot.scene.task.data.TaskContext;
import com.sankuai.dzim.pilot.scene.task.data.TaskResult;
import com.sankuai.dzim.pilot.scene.task.data.TaskTypeEnum;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 通用流式输出任务
 * @author: zhouyibing
 * @date: 2024/7/30
 */
@Component
public class CommonStreamTask extends CommonTask implements Task{

    @Override
    public boolean accept(TaskContext context) {
        return TaskTypeEnum.COMMON_STREAM_TASK.getType().equals(context.getTaskType());
    }

    @Override
    public TaskResult execute(TaskContext context) {
        // 调用chain层获取AIAnswerData
        AIAnswerData aiAnswerData = getAIAnswerData(context);
        // 构造返回值
        return TaskResult.builder().taskContext(context).aiAnswerData(aiAnswerData).build();
    }

    @Override
    public void afterExecute(TaskResult result) {

    }


}
