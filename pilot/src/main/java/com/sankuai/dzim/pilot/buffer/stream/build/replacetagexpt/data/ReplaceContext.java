package com.sankuai.dzim.pilot.buffer.stream.build.replacetagexpt.data;

import com.sankuai.dzim.pilot.buffer.core.PilotBufferItemDO;
import com.sankuai.dzim.pilot.scene.data.AssistantSceneContext;
import lombok.Data;
import org.apache.commons.math3.util.Pair;

import java.util.List;

@Data
public class ReplaceContext {
    private Pair<String, String> tag;

    private String tagContent;

    private String data;

    private AssistantSceneContext context;

    private List<PilotBufferItemDO> itemDOs;
}
