package com.sankuai.dzim.pilot.process.search.impl;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.deal.sales.common.datatype.SalesDisplayInfoDTO;
import com.dianping.technician.common.api.domain.Environment;
import com.dianping.technician.common.api.enums.SourceEnum;
import com.dianping.technician.dto.shopsearch.TechModuleRequest;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.inf.xmdlog.ConfigUtil;
import com.meituan.service.mobile.group.geo.bean.CityInfo;
import com.meituan.service.mobile.group.geo.service.CityService;
import com.meituan.service.mobile.search.poi.thrift.MergeSearchRequest;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.dzim.message.common.utils.ImAccountTypeUtils;
import com.sankuai.dzim.pilot.acl.*;
import com.sankuai.dzim.pilot.process.EsSearchProcessService;
import com.sankuai.dzim.pilot.process.aireservebook.enums.POIIndustryType;
import com.sankuai.dzim.pilot.process.data.*;
import com.sankuai.dzim.pilot.process.localplugin.data.UserContext;
import com.sankuai.dzim.pilot.process.localplugin.param.Param;
import com.sankuai.dzim.pilot.process.localplugin.param.ProductMergeSearchParam;
import com.sankuai.dzim.pilot.process.localplugin.param.ShopMergeSearchParam;
import com.sankuai.dzim.pilot.process.localplugin.param.ShopProductSearchParam;
import com.sankuai.dzim.pilot.process.search.SearchProcessService;
import com.sankuai.dzim.pilot.process.search.data.FilterOption;
import com.sankuai.dzim.pilot.process.search.data.ReserveQueryContext;
import com.sankuai.dzim.pilot.process.search.data.UserEnvParam;
import com.sankuai.dzim.pilot.process.search.enums.ProductFilterOptionEnum;
import com.sankuai.dzim.pilot.process.search.strategy.ReserveProductQueryFactory;
import com.sankuai.dzim.pilot.process.search.strategy.ReserveProductQueryHandler;
import com.sankuai.dzim.pilot.scene.task.data.EnvContext;
import com.sankuai.dzim.pilot.utils.CompletableFutureExpandUtils;
import com.sankuai.dzim.pilot.utils.PluginContextUtil;
import com.sankuai.dzim.pilot.utils.PoiExtractUtil;
import com.sankuai.dzim.pilot.utils.SearchTraceUtil;
import com.sankuai.dzshoplist.aggregate.dzrender.enums.*;
import com.sankuai.dzshoplist.aggregate.dzrender.request.DzRenderRequest;
import com.sankuai.dzshoplist.aggregate.dzrender.request.param.ClientEnv;
import com.sankuai.dzshoplist.aggregate.dzrender.request.param.DzRenderIdDTO;
import com.sankuai.dzshoplist.aggregate.dzrender.request.param.RenderIdDTO;
import com.sankuai.dzshoplist.aggregate.dzrender.response.ShopRenderItem;
import com.sankuai.dzshoplist.aggregate.dzrender.response.product.ProductRenderDTO;
import com.sankuai.dzshoplist.aggregate.dzrender.response.product.ProductTagDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.PriceDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.MtPoiDTO;
import com.sankuai.sinai.data.api.dto.PoiSubwayView;
import com.sankuai.sinai.data.api.dto.TypeHierarchyView;
import com.sankuai.sinai.data.api.util.MtPoiUtil;
import com.sankuai.spt.statequery.api.dto.ShopReserveModeDTO;
import com.sankuai.spt.statequery.api.enums.ReserveModeEnum;
import lombok.extern.slf4j.Slf4j;
import mtmap.geoinfo.geoinfo_base.Poi;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.stream.Collectors;

/**
 * 供给搜索处理服务
 * <AUTHOR>
 */
@Slf4j
@Component
public class SearchProcessServiceImpl implements SearchProcessService {

    @ConfigValue(key = "com.sankuai.mim.pilot.mergesearch.config", defaultValue = "{}")
    private MergeSearchConfig mergeSearchConfig;

    @Resource
    private CityService cityService;

    /**
     * 搜索服务
     */
    @Resource
    private MergeSearchAclService mergeSearchAclService;

    /**
     * 门店服务
     */
    @Resource
    private ShopAclService shopAclService;

    /**
     * 商品服务
     */
    @Resource
    private ProductAclService productAclService;

    /**
     * 渲染服务
     */
    @Resource
    private DzRenderAclService dzRenderAclService;

    /**
     * 门店团单召回服务
     */
    @Resource
    private DealShopQueryAclService dealShopQueryAclService;

    /**
     * 技师服务
     */
    @Resource
    private TechnicianAclService technicianAclService;

    /**
     * es
     */
    @Resource
    private EsSearchProcessService esSearchProcessService;

    @Resource
    private ReserveProductQueryFactory reserveProductQueryFactory;

    @Autowired
    private PoiExtractUtil poiExtractUtil;

    /**
     * 搜索门店
     * @param shopMergeSearchParam 搜索参数
     * @return 门店列表
     */
    @Override
    public List<ShopRetrievalData> searchShops(ShopMergeSearchParam shopMergeSearchParam) {
        try {
            // 搜索召回 shop + 下挂
            CompletableFuture<List<ShopRecallData>> shopRecallCf = searchRecallShops(shopMergeSearchParam, mergeSearchConfig);
            // padding
            CompletableFuture<List<ShopRetrievalData>> paddingShopsCf = shopRecallCf
                    .thenCompose(shopRecallDataList -> paddingShopInfo(shopRecallDataList, shopMergeSearchParam))
                    .exceptionally(e -> {
                        Cat.logError(e);
                        log.error("searchShops error", e);
                        return Lists.newArrayList();
                    });
            return paddingShopsCf.join();
        } catch (Exception e) {
            Cat.logError(e);
            log.error("searchShops error", e);
            return Lists.newArrayList();
        }
    }

    @Override
    public List<ProductRetrievalData> searchProducts(ProductMergeSearchParam productMergeSearchParam) {
        try{
            // 搜索商品 + 关联商户
            CompletableFuture<List<ProductRecallData>> productRecallCf =
                    searchRecallProducts(productMergeSearchParam, mergeSearchConfig);
            // padding
            CompletableFuture<List<ProductRetrievalData>> paddingProductsCf = productRecallCf
                    .thenCompose(productRecallDataList ->
                            paddingDealGroupInfo(productRecallDataList, productMergeSearchParam))
                    .exceptionally(e -> {
                        Cat.logError(e);
                        log.error("searchProducts error", e);
                        return Lists.newArrayList();
                    });
            return paddingProductsCf.join();
        } catch (Exception e) {
            Cat.logError(e);
            log.error("searchProducts error", e);
            return Lists.newArrayList();
        }
    }

    /**
     * 搜索召回门店，美团侧id
     * @param shopMergeSearchParam shopMergeSearchParam
     * @param config config
     * @return ShopRecallData List
     */
    private CompletableFuture<List<ShopRecallData>> searchRecallShops(ShopMergeSearchParam shopMergeSearchParam,
                                                                      MergeSearchConfig config) {
        MergeSearchRequest mergeSearchRequest = buildShopMergeSearchRequest(shopMergeSearchParam, config);
        // 搜索召回 shop + 下挂
        return CompletableFuture.completedFuture(mergeSearchAclService.queryShopMergeSearchResponse(mergeSearchRequest));
//        return mergeSearchAclService.queryShopMergeSearchResponseAsync(mergeSearchRequest);
    }

    /**
     * 填充门店信息
     * @param shopRecallDataList shopRecallDataList
     * @return key:mtShopId value:shopInfo
     */
    private CompletableFuture<List<ShopRetrievalData>> paddingShopInfo(List<ShopRecallData> shopRecallDataList,
                                                                       ShopMergeSearchParam param) {
        if (CollectionUtils.isEmpty(shopRecallDataList)){
            return CompletableFuture.completedFuture(Lists.newArrayList());
        }

        // 商户id
        List<Long> mtShopIds = shopRecallDataList.stream().distinct().map(ShopRecallData::getMtShopId).collect(Collectors.toList());
        // 门店id -> 团单id
        Map<Long, List<Long>> mtShopId2MtDealGroupIds = collectShop2DealGroupIds(shopRecallDataList);
        // 团单id
        List<Long> mtDealGroupIds = collectAllDealGroupIds(shopRecallDataList);

        String semantic = param.getShopSemanticCondition();
        CompletableFuture<Map<Long, ShopRetrievalData>> paddingShopCf = doPaddingShops(mtShopIds, param, semantic, true);
        CompletableFuture<Map<Long, ProductRetrievalData>> paddingDealsCf = doPaddingDeals(mtDealGroupIds, mtShopId2MtDealGroupIds, param, true);

        return CompletableFuture.allOf(paddingShopCf, paddingDealsCf).thenApply(none -> {
            Map<Long, ShopRetrievalData> shopRetrievalDataMap = paddingShopCf.join();
            Map<Long, ProductRetrievalData> productRetrievalDataMap = paddingDealsCf.join();

            shopRetrievalDataMap.forEach((mtShopId, shopRetrievalData) -> {
                List<Long> relatedDealGroupIds = mtShopId2MtDealGroupIds.get(mtShopId);
                if (CollectionUtils.isEmpty(relatedDealGroupIds)) {
                    return;
                }
                List<ProductRetrievalData> relatedProducts = relatedDealGroupIds.stream()
                        .map(productRetrievalDataMap::get)
                        .filter(Objects::nonNull).collect(Collectors.toList());
                shopRetrievalData.setRelatedProducts(relatedProducts);
            });
            // 按照搜索结果排序
            return mtShopIds.stream().map(shopRetrievalDataMap::get)
                    .filter(Objects::nonNull).collect(Collectors.toList());
        });
    }


    private CompletableFuture<Map<Long, ShopRetrievalData>> doPaddingShops(List<Long> mtShopIds, Param param, String semantic, boolean isMt) {
        // 商户基本信息
        CompletableFuture<Map<Long, MtPoiDTO>> shopBaseInfoCf = shopAclService.batchGetMtShopInfoAsync(mtShopIds);

        // 商户渲染信息（本次不需要）
        CompletableFuture<Map<Long, ShopRenderItem>> shopRenderCf = dzRenderAclService.queryShopRenderInfo(buildShopRenderRequest(mtShopIds, param));

        // 手艺人信息
        CompletableFuture<Map<Long, List<TechnicianData>>> shopTechnicianCf = shopBaseInfoCf
                .thenCompose(mtPoiMap -> doBatchSearchShopTechnicians(mtPoiMap, mtShopIds, param, isMt));

        // 评价信息
        Map<Long, List<String>> shopReviewsMap = doBatchSearchShopReviews(mtShopIds, semantic);

        // 在线预约系统状态
        Map<Long, ShopReserveModeDTO> shopReserveMap = doBatchQueryShopReserveMode(mtShopIds);

        return CompletableFuture.allOf(shopBaseInfoCf, shopTechnicianCf, shopRenderCf).thenApply(none -> {
            Map<Long, MtPoiDTO> shopBaseInfoMap = shopBaseInfoCf.join();
            Map<Long, ShopRenderItem> shopRenderMap = shopRenderCf.join();
            Map<Long, List<TechnicianData>> shopTechnicianMap = shopTechnicianCf.join();
            return buildShopRetrievalDataMap(shopBaseInfoMap, shopRenderMap, shopTechnicianMap, shopReviewsMap, shopReserveMap);
        }).exceptionally(e -> {
            Cat.logError(e);
            log.error("doPaddingShops error", e);
            return Maps.newHashMap();
        });
    }

    /**
     * 批量查询商户对应的手艺人信息
     * @param mtPoiMap mtPoiMap
     * @param mtShopIds mtShopIds
     * @param param param
     * @param isMt isMt
     * @return mtShopId -> TechnicianDataList
     */
    private CompletableFuture<Map<Long, List<TechnicianData>>> doBatchSearchShopTechnicians(Map<Long, MtPoiDTO> mtPoiMap,
                                                                                            List<Long> mtShopIds,
                                                                                            Param param, boolean isMt) {
        if (MapUtils.isEmpty(mtPoiMap)) {
            return CompletableFuture.completedFuture(Maps.newHashMap());
        }
        if (isMt) {
            // 手艺人只能用dpShopId查
            Map<Long, Long> dpShopId2MtShopId = Maps.newHashMap();
            mtPoiMap.forEach((mtShopId, mtPoiDto) -> dpShopId2MtShopId.put(mtPoiDto.getDpPoiId(), mtShopId));
            CompletableFuture<Map<Long, List<TechnicianData>>> dpShopId2TechCf =
                    batchSearchShopTechnicians(Lists.newArrayList(dpShopId2MtShopId.keySet()), param);
            return dpShopId2TechCf.thenApply(dpShopId2TechMap -> {
                Map<Long, List<TechnicianData>> mtTechMap = Maps.newHashMap();
                dpShopId2TechMap.forEach((dpShopId, techList) -> {
                    Long mtShopId = dpShopId2MtShopId.get(dpShopId);
                    mtTechMap.put(mtShopId, techList);
                });
                return mtTechMap;
            });
        } else {
            return batchSearchShopTechnicians(mtShopIds, param);
        }
    }

    private Map<Long, List<String>> doBatchSearchShopReviews(List<Long> mtShopIds, String semantic) {
        if (CollectionUtils.isEmpty(mtShopIds) || StringUtils.isBlank(semantic)) {
            return Maps.newHashMap();
        }

        return mtShopIds.stream()
                .collect(Collectors.toMap(mtShopId -> mtShopId,
                        mtShopId -> esSearchProcessService.searchShopReview(mtShopId, semantic, 5),
                        (v1,v2) -> v1));
    }

    private Map<Long, ShopReserveModeDTO> doBatchQueryShopReserveMode(List<Long> mtShopIds) {
        try {
            return productAclService.batchQueryShopReserveMode(mtShopIds);
        } catch (Exception e) {
            Cat.logError(e);
            log.error("doBatchSearchShopReviews error", e);
            return Maps.newHashMap();
        }
    }

    private Map<Long, ShopRetrievalData> buildShopRetrievalDataMap(Map<Long, MtPoiDTO> shopBaseInfoMap,
                                                                   Map<Long, ShopRenderItem> shopRenderMap,
                                                                   Map<Long, List<TechnicianData>> shopTechnicianMap,
                                                                   Map<Long, List<String>> shopReviewsMap,
                                                                   Map<Long, ShopReserveModeDTO> shopReserveMap) {
        if (shopBaseInfoMap == null || shopBaseInfoMap.isEmpty()) {
            return Maps.newHashMap();
        }

        Map<Long, ShopRetrievalData> result = Maps.newHashMap();
        shopBaseInfoMap.forEach((mtShopId, mtPoiDTO) -> {
            if (mtPoiDTO == null) {
                return;
            }
            ShopRetrievalData shopRetrievalData = new ShopRetrievalData();

            // 设置基础信息
            buildShopRetrievalDataBasic(shopRetrievalData, mtPoiDTO);

            // 推荐理由
            ShopRenderItem shopRenderItem = shopRenderMap.get(mtShopId);
            if(shopRenderItem != null && shopRenderItem.getShopRenderInfo() != null
                    && CollectionUtils.isNotEmpty(shopRenderItem.getShopRenderInfo().getRecommends())) {
                shopRetrievalData.setRecommends(shopRenderItem.getShopRenderInfo().getRecommends());
            }

            // 手艺人
            List<TechnicianData> technicians = shopTechnicianMap.get(mtShopId);
            if(CollectionUtils.isNotEmpty(technicians)){
                shopRetrievalData.setTechnicians(technicians);
            }

            // 评价
            List<String> shopReviews = shopReviewsMap.get(mtShopId);
            if(CollectionUtils.isNotEmpty(shopReviews)){
                shopRetrievalData.setReviews(shopReviews);
            }

            // 足疗行业排除有在线预订的商户
            if(needExcludeMassageOnlineBookShop(shopRetrievalData, shopReserveMap.get(mtShopId))) {
                log.info("needExcludeMassageOnlineBookShop: {}", mtShopId);
                return;
            }
            result.put(mtShopId, shopRetrievalData);
        });
        return result;
    }

    private void buildShopRetrievalDataBasic(ShopRetrievalData shopRetrievalData, MtPoiDTO mtPoiDTO) {
        if (shopRetrievalData == null || mtPoiDTO == null) {
            return;
        }

        shopRetrievalData.setMtShopId(mtPoiDTO.getMtPoiId());
        shopRetrievalData.setDpShopId(mtPoiDTO.getDpPoiId());
        shopRetrievalData.setShopName(MtPoiUtil.getMtPoiName(mtPoiDTO.getName(), mtPoiDTO.getBranchName()));
        shopRetrievalData.setMtAvgPrice(mtPoiDTO.getMtAvgPrice());
        shopRetrievalData.setMtAvgScore(mtPoiDTO.getMtAvgScore());
        List<TypeHierarchyView> typeHierarchy = mtPoiDTO.getTypeHierarchy();
        shopRetrievalData.setMtCateName0(typeHierarchy.get(typeHierarchy.size() - 1).getName());
        shopRetrievalData.setMtCateName1(typeHierarchy.size() > 1 ? typeHierarchy.get(typeHierarchy.size() - 2).getName() : StringUtils.EMPTY);
        shopRetrievalData.setMtCateName2(typeHierarchy.size() > 2 ? typeHierarchy.get(typeHierarchy.size() - 3).getName() : StringUtils.EMPTY);
        shopRetrievalData.setMtCityName(mtPoiDTO.getMtCityLocationName());
        shopRetrievalData.setMtDistrictName(mtPoiDTO.getMtLocationName());
        shopRetrievalData.setMtRegionName(mtPoiDTO.getMtBareaName());
        shopRetrievalData.setShopAddress(MtPoiUtil.getMtPoiAddress(mtPoiDTO.getAddress(), mtPoiDTO.getReferenceAddress()));
        List<PoiSubwayView> poiSubways = mtPoiDTO.getPoiSubways();
        shopRetrievalData.setSubwayStation(CollectionUtils.isEmpty(poiSubways) ? StringUtils.EMPTY : poiSubways.get(0).getName());
        shopRetrievalData.setBrandName(StringUtils.isEmpty(mtPoiDTO.getBrandName()) ? StringUtils.EMPTY : mtPoiDTO.getBrandName());
    }

    private CompletableFuture<Map<Long, ProductRetrievalData>> doPaddingDeals(List<Long> dealGroupIds,
                                                                              Map<Long, List<Long>> shopId2DealGroupIdsMap,
                                                                              Param param, boolean isMt) {
        // 团购基本信息
        Map<Long, DealGroupDTO> dealGroupBaseInfoMap = productAclService.batchGetDealBaseInfo(dealGroupIds, isMt);

        // 团购销量信息
        CompletableFuture<Map<Long, SalesDisplayInfoDTO>> dealGroupSalesCf = productAclService.batchQueryDealSales(dealGroupIds, isMt);

        // 团购渲染信息（下挂方式渲染）
        CompletableFuture<Map<Long, ShopRenderItem>> dealGroupRenderCf = doPaddingProductRenderInfo(dealGroupIds, shopId2DealGroupIdsMap, dealGroupBaseInfoMap, param, isMt);

        return CompletableFuture.allOf(dealGroupSalesCf, dealGroupRenderCf).thenApply(none -> {
            Map<Long, SalesDisplayInfoDTO> dealSalesMap = dealGroupSalesCf.join();
            Map<Long, ShopRenderItem> shopDealGroupRenderMap = dealGroupRenderCf.join();
            Map<Long, ProductRenderDTO> dealRenderMap = Maps.newHashMap();
            if (MapUtils.isNotEmpty(shopDealGroupRenderMap)) {
                shopDealGroupRenderMap.forEach((shopId, shopItem) -> {
                    if (shopItem == null || CollectionUtils.isEmpty(shopItem.getRelatedProducts())) {
                        return;
                    }
                    for (ProductRenderDTO productRenderItem : shopItem.getRelatedProducts()) {
                        if (dealRenderMap.containsKey(productRenderItem.getProductId())){
                            continue;
                        }
                        dealRenderMap.put(productRenderItem.getProductId(), productRenderItem);
                    }
                });
            }
            return buildProductRetrievalDataMap(dealGroupBaseInfoMap, dealSalesMap, dealRenderMap);
        }).exceptionally(e -> {
            Cat.logError(e);
            log.error("doPaddingDeals error", e);
            return Maps.newHashMap();
        });
    }

    private CompletableFuture<Map<Long, ShopRenderItem>> doPaddingProductRenderInfo(List<Long> dealGroupIds,
                                                                                    Map<Long, List<Long>> shopId2DealGroupIdsMap,
                                                                                    Map<Long, DealGroupDTO> dealGroupBaseInfoMap,
                                                                                    Param param, boolean isMt) {

            if (isMt) {
                // 美团侧直接调用渲染服务
                return dzRenderAclService.queryShopRenderInfo(buildShopProductRenderRequest(dealGroupIds, shopId2DealGroupIdsMap, param));
            } else {
                // 点评侧id全部转成mt侧id请求
                Map<Long, Long> mtDealGroupId2DpDealGroupId = dealGroupBaseInfoMap.values().stream().filter(Objects::nonNull)
                        .collect(Collectors.toMap(DealGroupDTO::getMtDealGroupId, DealGroupDTO::getDpDealGroupId,
                                (v1, v2) -> v1));
                Map<Long, Long> dpDealGroupId2MtDealGroupId = dealGroupBaseInfoMap.values().stream().filter(Objects::nonNull)
                        .collect(Collectors.toMap(DealGroupDTO::getDpDealGroupId, DealGroupDTO::getMtDealGroupId,
                                (v1, v2) -> v1));
                List<Long> mtDealGroupIds = Lists.newArrayList(mtDealGroupId2DpDealGroupId.keySet());

                Map<Long, Long> dp2mtShopIdMap = shopAclService.queryMtIdLByDpIdL(Lists.newArrayList(shopId2DealGroupIdsMap.keySet())).join();
                Map<Long, Long> mt2dpShopIdMap = dp2mtShopIdMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey, (v1, v2) -> v1));

                Map<Long, List<Long>> mtShopId2MtDealGroupIdsMap = Maps.newHashMap();
                shopId2DealGroupIdsMap.forEach((dpShopId, dpDealGroupIds) -> {
                    Long mtShopId = dp2mtShopIdMap.get(dpShopId);
                    if (mtShopId == null || mtShopId <= 0) {
                        return;
                    }
                    List<Long> mtDealGroupIdList = dpDealGroupIds.stream().map(dpDealGroupId2MtDealGroupId::get).filter(Objects::nonNull).collect(Collectors.toList());
                    mtShopId2MtDealGroupIdsMap.put(mtShopId, mtDealGroupIdList);
                });
                // 用美团侧id去请求
                return dzRenderAclService.queryShopRenderInfo(buildShopProductRenderRequest(mtDealGroupIds, mtShopId2MtDealGroupIdsMap, param))
                        .thenApply(mtDealGroupIdRenderMap -> {
                            Map<Long, ShopRenderItem> dpRenderMap = Maps.newHashMap();
                            mtDealGroupIdRenderMap.forEach((mtShopId, renderItem) -> {
                                Long dpShopId = mt2dpShopIdMap.get(mtShopId);
                                if (dpShopId == null || dpShopId <= 0 || renderItem == null) {
                                    return;
                                }
                                // 转回dp侧id
                                dpRenderMap.put(dpShopId, renderItem);
                            });
                            return dpRenderMap;
                        });
            }

    }

    private Map<Long, ProductRetrievalData> buildProductRetrievalDataMap(Map<Long, DealGroupDTO> dealBaseInfoMap,
                                                                         Map<Long, SalesDisplayInfoDTO> dealSalesMap,
                                                                         Map<Long, ProductRenderDTO> dealRenderMap) {
        if(MapUtils.isEmpty(dealBaseInfoMap)) {
            return Maps.newHashMap();
        }

        return dealBaseInfoMap.entrySet().stream()
                .filter(entry -> filterProduct(entry.getValue()))
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> buildProductRetrievalData(entry.getValue(),
                                dealSalesMap.get(entry.getKey()),
                                dealRenderMap.get(entry.getKey())),
                        (v1, v2) -> v1));
    }

    private boolean filterProduct(DealGroupDTO dealGroupDTO) {
        if(dealGroupDTO == null) {
            return false;
        }

        // 需要过滤多sku的团单，套餐状态1的套餐数量 > 1 表示有多个sku
        if(needExcludeDealGroupMultiSku()) {
            List<DealGroupDealDTO> deals = dealGroupDTO.getDeals();
            if (CollectionUtils.isNotEmpty(deals)) {
                List<Long> onlineDeals = deals.stream()
                        .filter(deal -> deal != null && deal.getBasic() != null && deal.getBasic().getStatus() == 1)
                        .map(DealGroupDealDTO::getDealId)
                        .collect(Collectors.toList());
                if(onlineDeals.size() > 1) {
                    log.info("needExcludeDealGroupMultiSku: {}, onlineDeals: {} ", dealGroupDTO.getMtDealGroupId(), onlineDeals);
                    return false;
                }
            }
        }
        return true;
    }

    private ProductRetrievalData buildProductRetrievalData(DealGroupDTO dealGroupDTO,
                                                           SalesDisplayInfoDTO salesInfo,
                                                           ProductRenderDTO renderItem) {
        if(dealGroupDTO == null) {
            return null;
        }
        ProductRetrievalData data = new ProductRetrievalData();

        // 设置基础信息
        data.setMtProductId(dealGroupDTO.getMtDealGroupId());
        data.setDpProductId(dealGroupDTO.getDpDealGroupId());
        data.setProductType(1);

        data.setDealGroupDTO(dealGroupDTO);
        data.setDeals(dealGroupDTO.getDeals());

        // 填充基本信息
        fillBasicInfo(data, dealGroupDTO.getBasic());

        // 填充分类信息
        fillCategoryInfo(data, dealGroupDTO.getCategory());

        // 填充价格信息
        fillPriceInfo(data, dealGroupDTO.getPrice());

        // 填充销量信息
        fillSalesInfo(data, salesInfo);

        // 填充渲染信息
        fillRenderInfo(data, renderItem);

        return data;
    }

    private void fillBasicInfo(ProductRetrievalData data, DealGroupBasicDTO basic) {
        Optional.ofNullable(basic).ifPresent(b -> {
            data.setCategoryId(b.getCategoryId());
            data.setTitle(b.getTitle());
            data.setTitleDesc(b.getTitleDesc());
            data.setBrandName(b.getBrandName());
        });
    }

    private void fillCategoryInfo(ProductRetrievalData data, DealGroupCategoryDTO category) {
        Optional.ofNullable(category).ifPresent(c -> {
            data.setServiceType(c.getServiceType());
            data.setServiceTypeId(c.getServiceTypeId());
        });
    }

    private void fillPriceInfo(ProductRetrievalData data, PriceDTO price) {
        Optional.ofNullable(price).ifPresent(p -> {
            data.setSalePrice(p.getSalePrice());
            data.setMarketPrice(p.getMarketPrice());
        });
    }

    private void fillSalesInfo(ProductRetrievalData data, SalesDisplayInfoDTO salesInfo) {
        Optional.ofNullable(salesInfo).ifPresent(s -> {
            data.setSalesTag(s.getSalesTag());
            data.setSalesNum(s.getSalesNum());
        });
        if (data.getSalesNum() == null) {
            data.setSalesNum(0L);
        }
    }

    private void fillRenderInfo(ProductRetrievalData data, ProductRenderDTO renderItem) {
        Optional.ofNullable(renderItem)
                .ifPresent(renderInfo -> {
                    data.setTitle(StringUtils.defaultIfEmpty(renderInfo.getTitle(), StringUtils.defaultIfEmpty(data.getTitle(), "")));
                    data.setSubTitles(ListUtils.defaultIfNull(renderInfo.getSubTitles(),ListUtils.defaultIfNull(data.getSubTitles(),Lists.newArrayList())));
                    Optional.ofNullable(renderInfo.getPrice()).ifPresent(price -> {
                        data.setSalePrice(String.valueOf(price.getSalePrice()));
                        data.setMarketPrice(String.valueOf(price.getMarketPrice()));
                    });
                    data.setHeadPic(StringUtils.defaultIfEmpty(renderInfo.getHeadPic(),StringUtils.defaultIfEmpty(data.getHeadPic(),"")));
                    data.setJumpUrl(StringUtils.defaultIfEmpty(renderInfo.getJumpUrl(),StringUtils.defaultIfEmpty(data.getJumpUrl(),"")));
                    if (CollectionUtils.isNotEmpty(renderInfo.getProductTags())) {
                        data.setDiscountTags(renderInfo.getProductTags().stream()
                                .filter(productTagDTO -> productTagDTO.getType() == ProductTagTypeEnum.DISCOUNT_PROMO_TAG.getCode())
                                .map(ProductTagDTO::getTagText)
                                .findFirst().orElse(Lists.newArrayList()));
                    }
                });
    }


    /**
     * 商户渲染信息
     * @param mtShopIds mtShopIds
     * @param param param
     * @return DzRenderRequest
     */
    private DzRenderRequest buildShopRenderRequest(List<Long> mtShopIds, Param param) {
        if (CollectionUtils.isEmpty(mtShopIds)) {
            return null;
        }
        DzRenderRequest request = buildBaseRenderRequest(param);
        List<DzRenderIdDTO> dzRenderIdDTOList = mtShopIds.stream().distinct().map(mtShopId -> {
            DzRenderIdDTO dzRenderIdDTO = new DzRenderIdDTO();
            RenderIdDTO renderIdDTO = new RenderIdDTO();
            renderIdDTO.setItemId(mtShopId);
            renderIdDTO.setItemType(RenderItemTypeEnum.POI.getCode());
            dzRenderIdDTO.setRenderId(renderIdDTO);
            return dzRenderIdDTO;
        }).collect(Collectors.toList());
        request.setRenderIds(dzRenderIdDTOList);
        request.setRenderType(RenderTypeEnum.POI_PRODUCT.getType());
        return request;
    }

    /**
     * 团购渲染信息
     * @param mtDealGroupIds mtDealGroupIds
     * @param param param
     * @return DzRenderRequest
     */
    private DzRenderRequest buildShopProductRenderRequest(List<Long> mtDealGroupIds, Map<Long, List<Long>> mtShopId2DealsMap, Param param) {
        if (CollectionUtils.isEmpty(mtDealGroupIds)) {
            return null;
        }
        DzRenderRequest request = buildBaseRenderRequest(param);
        List<DzRenderIdDTO> dzRenderIdDTOList = Lists.newArrayList();
        mtShopId2DealsMap.forEach((shopId, dealGroupIds) -> {
            if (CollectionUtils.isEmpty(dealGroupIds)) {
                return;
            }

            DzRenderIdDTO dzRenderIdDTO = new DzRenderIdDTO();
            RenderIdDTO renderIdDTO = new RenderIdDTO();
            renderIdDTO.setItemId(shopId);
            renderIdDTO.setItemType(RenderItemTypeEnum.POI.getCode());
            dzRenderIdDTO.setRenderId(renderIdDTO);
            dzRenderIdDTO.setRelatedIds(dealGroupIds.stream().map(deal -> {
                RenderIdDTO relatedRenderIdDTO = new RenderIdDTO();
                relatedRenderIdDTO.setItemId(deal);
                relatedRenderIdDTO.setItemType(RenderItemTypeEnum.PRODUCT.getCode());
                Map<String, String> extraParams = Maps.newHashMap();
                extraParams.put("product_type", "1");
                relatedRenderIdDTO.setExtraParams(extraParams);
                return relatedRenderIdDTO;
            }).collect(Collectors.toList()));
            dzRenderIdDTOList.add(dzRenderIdDTO);
        });
        request.setRenderIds(dzRenderIdDTOList);
        request.setRenderType(RenderTypeEnum.POI_PRODUCT.getType());
        return request;
    }

    private DzRenderRequest buildBaseRenderRequest(Param param) {
        if (param == null) {
            return null;
        }
        EnvContext envContext = PluginContextUtil.getEnvContext(param);
        UserContext userContext = PluginContextUtil.getUserContext(param);
        boolean isMt = ImAccountTypeUtils.isMtUserId(userContext.getImUserId());

        DzRenderRequest request = new DzRenderRequest();
        request.setSceneCode(SceneCodeEnum.SHOP_LIST_HAIRCUT_FOOT.getCode());
        request.setSpaceKey(SpaceKeyEnum.PLATFORM_AGENT_RENDER.getCode());
        ClientEnv clientEnv = new ClientEnv();
        clientEnv.setGpsCityId(envContext.getCityId());
        clientEnv.setCityId(envContext.getCityId());
        // 默认都mt
        clientEnv.setPlatform(PlatformEnum.MT.getCode());
        clientEnv.setClientType(ClientTypeEnum.MT_APP_ANDROID.getCode());
        clientEnv.setLng(envContext.getLng());
        clientEnv.setLat(envContext.getLat());
        clientEnv.setPageLng(envContext.getLng());
        clientEnv.setPageLat(envContext.getLat());
        clientEnv.setUuId(isMt ? envContext.getDeviceId() : "");
        clientEnv.setDpId(isMt ? "" : envContext.getDeviceId());
        clientEnv.setUserId(userContext.getUserId());
        clientEnv.setVersion(envContext.getAppVersion());
        request.setClientEnv(clientEnv);
        return request;
    }

    private Map<Long, List<Long>> collectShop2DealGroupIdsByProduct(List<ProductRecallData> productRecallDataList) {
        Map<Long, List<Long>> shop2DealGroupIds = Maps.newHashMap();
        for (ProductRecallData productRecallData : productRecallDataList) {
            if (shop2DealGroupIds.containsKey(productRecallData.getRelatedMtShopId())) {
                shop2DealGroupIds.get(productRecallData.getRelatedMtShopId()).add(productRecallData.getMtProductId());
            } else {
                List<Long> dealGroupIds = Lists.newArrayList(productRecallData.getMtProductId());
                shop2DealGroupIds.put(productRecallData.getRelatedMtShopId(), dealGroupIds);
            }
        }
        return shop2DealGroupIds;
    }


    private Map<Long, List<Long>> collectShop2DealGroupIds(List<ShopRecallData> shopRecallDataList) {
        Map<Long, List<Long>> shop2DealGroupIds = Maps.newHashMap();
        for (ShopRecallData shopRecallData : shopRecallDataList) {
            shop2DealGroupIds.put(shopRecallData.getMtShopId(), shopRecallData.getRelatedDeals());
        }
        return shop2DealGroupIds;
    }

    private List<Long> collectAllDealGroupIds(List<ShopRecallData> shopRecallDataList) {
        return shopRecallDataList.stream()
                .flatMap(shopRecallData -> shopRecallData.getRelatedDeals().stream())
                .distinct()
                .collect(Collectors.toList());
    }

    private MergeSearchRequest buildShopMergeSearchRequest(ShopMergeSearchParam param,
                                                           MergeSearchConfig config) {
        MergeSearchRequest request = new MergeSearchRequest();
        EnvContext envContext = PluginContextUtil.getEnvContext(param);
        UserContext userContext = PluginContextUtil.getUserContext(param);

        request.setQuery(param.getKeyword());
        request.setCity(getCityName(envContext.getCityId()));
        request.setCityId(envContext.getCityId());
        request.setOffset(buildOffset(param.getPageNo(), config.getShopSearchPageSize()));
        request.setLimit(config.getShopSearchPageSize());
        request.setLocation(String.format("%s,%s", envContext.getLat(), envContext.getLng()));
        request.setUuid(envContext.getDeviceId());
        request.setCateId(getShopSearchCateId(config));
//        request.setUtmSource(filterListRequestDTO.getUtmSource());
//        request.setUtmMedium(filterListRequestDTO.getUtmMedium());
        request.setVersion(envContext.getAppVersion());
        request.setUserId(userContext.getUserId());
        request.setOrderby(buildOrderBy(param.getSort()));
//        request.setDistance(getDistanceFromFilterListRequestDTO(filterListRequestDTO, filterIdList, mergeSearchRecallParam));
        request.setAreaId(buildAreaId(param, envContext));

        request.setTraceId(ConfigUtil.getTraceID());
        request.setQueryId(SearchTraceUtil.buildQueryId(userContext.getUserIp()));
        request.setExtensions(buildShopRequestExtensions(envContext, userContext));
        return request;
    }

    private String buildAreaId(ShopMergeSearchParam param, EnvContext envContext) {
        if (StringUtils.isBlank(param.getArea())) {
            return null;
        }

        String area = param.getArea();
        List<Poi> pois = poiExtractUtil.queryPoiByKeyword(area, envContext.getCityId(), envContext.getLat(), envContext.getLng());
        if (CollectionUtils.isEmpty(pois)) {
            return null;
        }

        if (CollectionUtils.isEmpty(pois.get(0).getMt_front_area_ids())) {
            return null;
        }

        return String.valueOf(pois.get(0).getMt_front_area_ids().get(0));
    }

    private Map<String, String> buildShopRequestExtensions(EnvContext envContext, UserContext userContext) {
        Map<String, String> result = Maps.newHashMap();
        result.put("search_result_type", "poi");
        result.put("entrance", "3");
        // 垂搜这么传的
        String ste = "_b00000";
        if (StringUtils.isNotEmpty(ste)) {
            result.put("ste", ste);
        }
        result.put("api_version", "/v4/poi/search");
        if (StringUtils.isNotEmpty(envContext.getAppVersion())) {
            result.put("device_version", envContext.getAppVersion());
        }
        result.put("dz_channel_search", "true");
        result.put("trace_id", SearchTraceUtil.buildGlobalId(userContext.getUserIp()));
        result.put("locationCityId", String.valueOf(envContext.getCityId()));
//        result.put("not_qc", "true");

        if (envContext.getCityId() != null) {
            result.put("city_id", String.valueOf(envContext.getCityId()));
        }

        // 过滤广告
        result.put("adsSkip", "true");
//        if (StringUtils.isNotEmpty(filterListRequestDTO.getRankQueryId())) {
//            result.put("rankQueryId", filterListRequestDTO.getRankQueryId());
//        }
        result.put("user_id", String.valueOf(userContext.getUserId()));
//        if (StringUtils.isNotEmpty(filterListRequestDTO.getLocationFingerprint())) {
//            result.put("locationFingerprint", filterListRequestDTO.getLocationFingerprint());
//        }
        result.put("source", "dz_agent");
        return result;
    }

    private int buildOffset(int pageNo, int pageSize) {
        if(pageNo <= 0){
            return 0;
        }
        return (pageNo - 1) * pageSize;
    }

    private String getShopSearchCateId(MergeSearchConfig config) {
        return config.getShopSearchCateId();
    }

    private String getProductSearchCateId(MergeSearchConfig config) {
        return config.getProductSearchCateId();
    }

    private String buildOrderBy(Integer sort) {

        if(sort == null || sort <= 0 || sort > 5){
            return StringUtils.EMPTY;
        }
        // 距离优先
        if (sort == 2) {
            return "@geodist asc";
        }
        //好评优先
        if (sort == 3) {
            return "rating_score desc";
        }
        //人均最低
        if (sort == 4) {
            return "percapita asc";
        }
        //人均最高
        if (sort == 5) {
            return "percapita desc";
        }

        return StringUtils.EMPTY;
    }

    private String getCityName(Integer cityId) {
        if (cityId == null || cityId == 0) {
            return "";
        }
        CityInfo city = cityService.getCityById(cityId);
        if (city == null) {
            return "";
        }
        return city.getName();
    }

    private CompletionStage<List<ProductRetrievalData>> paddingDealGroupInfo(List<ProductRecallData> productRecallDataList,
                                                                             ProductMergeSearchParam param) {
        if (CollectionUtils.isEmpty(productRecallDataList)){
            return CompletableFuture.completedFuture(Lists.newArrayList());
        }

        // 团单id
        List<Long> mtDealGroupIds = productRecallDataList.stream().distinct().map(ProductRecallData::getMtProductId).collect(Collectors.toList());
        // 团单id -> 门店id
        Map<Long, Long> mtDealGroupId2ShopId = collectDealGroupId2ShopId(productRecallDataList);
        // 门店id -> 团单id
        Map<Long, List<Long>> shop2DealGroupIds = collectShop2DealGroupIdsByProduct(productRecallDataList);
        // 门店id
        List<Long> mtShopIds = collectAllShopIds(productRecallDataList);

        CompletableFuture<Map<Long, ProductRetrievalData>> paddingDealsCf = doPaddingDeals(mtDealGroupIds, shop2DealGroupIds, param, true);
        CompletableFuture<Map<Long, ShopRetrievalData>> paddingShopCf = doPaddingShops(mtShopIds, param, "", true);

        return CompletableFuture.allOf(paddingShopCf, paddingDealsCf).thenApply(none -> {
            Map<Long, ProductRetrievalData> productRetrievalDataMap = paddingDealsCf.join();
            Map<Long, ShopRetrievalData> shopRetrievalDataMap = paddingShopCf.join();

            productRetrievalDataMap.forEach((mtDealGroupId, productRetrievalData) -> {
                if (productRetrievalData == null || mtDealGroupId2ShopId.get(mtDealGroupId) == null || mtDealGroupId2ShopId.get(mtDealGroupId) <= 0) {
                    return;
                }
                Long relatedMtShopId = mtDealGroupId2ShopId.get(mtDealGroupId);
                productRetrievalData.setRelatedMtShopId(relatedMtShopId);

                ShopRetrievalData shopRetrievalData = shopRetrievalDataMap.get(relatedMtShopId);
                if (shopRetrievalData == null) {
                    return;
                }
                productRetrievalData.setRelatedShop(shopRetrievalData);
            });
            // 按照搜索结果排序
            return mtDealGroupIds.stream().map(productRetrievalDataMap::get)
                    .filter(Objects::nonNull).collect(Collectors.toList());
        });
    }

    private Map<Long, Long> collectDealGroupId2ShopId(List<ProductRecallData> productRecallDataList) {
        return productRecallDataList.stream()
                .collect(Collectors.toMap(ProductRecallData::getMtProductId,
                        ProductRecallData::getRelatedMtShopId, (v1, v2) -> v1));
    }

    private List<Long> collectAllShopIds(List<ProductRecallData> productRecallDataList) {
        return productRecallDataList.stream()
                .map(ProductRecallData::getRelatedMtShopId)
                .distinct()
                .collect(Collectors.toList());
    }

    private CompletableFuture<List<ProductRecallData>> searchRecallProducts(ProductMergeSearchParam productMergeSearchParam,
                                                                            MergeSearchConfig config) {
        MergeSearchRequest mergeSearchRequest = buildProductMergeSearchRequest(productMergeSearchParam, config);
        // 搜索召回商户+关联门店
        return CompletableFuture.completedFuture(mergeSearchAclService.queryProductMergeSearchResponse(mergeSearchRequest));
//        return mergeSearchAclService.queryProductMergeSearchResponseAsync(mergeSearchRequest);
    }

    private MergeSearchRequest buildProductMergeSearchRequest(ProductMergeSearchParam param,
                                                              MergeSearchConfig config) {
        MergeSearchRequest request = new MergeSearchRequest();
        EnvContext envContext = PluginContextUtil.getEnvContext(param);
        UserContext userContext = PluginContextUtil.getUserContext(param);

        request.setQuery(param.getKeyword());
        request.setCity(getCityName(envContext.getCityId()));
        request.setCateId(getProductSearchCateId(config));
        request.setCityId(envContext.getCityId());
        request.setOffset(buildOffset(param.getPageNo(), config.getProductSearchPageSize()));
        request.setLimit(config.getProductSearchPageSize());
        request.setUuid(envContext.getDeviceId());
        request.setLocation(String.format("%s,%s", envContext.getLat(), envContext.getLng()));
        request.setVersion(envContext.getAppVersion());
        request.setUserId(userContext.getUserId());
        request.setOrderby(buildOrderBy(param.getSort()));
//        request.setDistance(getDistance(mergeSearchRecallParam));
//        request.setAreaId(getAreaId(mergeSearchRecallParam));
        request.setTraceId(ConfigUtil.getTraceID());
        request.setQueryId(SearchTraceUtil.buildQueryId(userContext.getUserIp()));
        request.setExtensions(buildProductRequestExtensions(envContext, userContext));
        return request;
    }

    private Map<String, String> buildProductRequestExtensions(EnvContext envContext, UserContext userContext) {
        Map<String, String> result = Maps.newHashMap();
        // 垂搜这么传的
        String ste = "_b00000";
        if (StringUtils.isNotEmpty(ste)) {
            result.put("ste", ste);
        }
        if (StringUtils.isNotEmpty(envContext.getAppVersion())) {
            result.put("device_version", envContext.getAppVersion());
        }

        result.put("trace_id", SearchTraceUtil.buildGlobalId(userContext.getUserIp()));
        result.put("locationCityId", String.valueOf(envContext.getCityId()));
        if (envContext.getCityId() != null) {
            result.put("city_id", String.valueOf(envContext.getCityId()));
        }
        result.put("search_result_type", "deal");
        result.put("entrance", "3");
        result.put("api_version", "/v4/poi/search");
        result.put("dz_channel_search", "true");
        result.put("no_ads", "true");

//        putParamIfExist(extensions, "switchCity", getSwitchCity(filterListRequestDTO));
//        putParamIfExist(extensions, "subStationPos", getSubStationPos(mergeSearchRecallParam));
//        putParamIfExist(extensions, "trace_id", getTraceId(filterListRequestDTO));
//        putParamIfExist(extensions, "locationCityId", getLocationCityId(filterListRequestDTO));
//        putParamIfExist(extensions, "not_qc", getTipsTypeFromRequest(filterListRequestDTO));
//        putParamIfExist(extensions, "city_id", String.valueOf(filterListRequestDTO.getCityId()));
//        putParamIfExist(extensions, "rankQueryId", filterListRequestDTO.getRankQueryId());
        return result;
    }


    @Override
    public List<ProductRetrievalData> searchShopProducts(ShopProductSearchParam param) {

        try {
            if(param.getShopId() == null || param.getShopId() <= 0){
                return Lists.newArrayList();
            }
            long shopId = param.getShopId();

            UserContext userContext = PluginContextUtil.getUserContext(param);
            boolean isMt = ImAccountTypeUtils.isMtUserId(userContext.getImUserId());

            long dpShopId = shopId;
            CompletableFuture<List<Long>> dpDealGroupIdsCf;
            if (isMt) {
                dpShopId = shopAclService.loadDpShopIdByMtShopId(shopId);
                dpDealGroupIdsCf = dealShopQueryAclService.getShopDealGroupIds(dpShopId, 100, 0, 100);
            } else {
                dpDealGroupIdsCf = dealShopQueryAclService.getShopDealGroupIds(dpShopId, 100, 0, 100);
            }
            final long finalDpShopId = dpShopId;
            CompletableFuture<List<ProductRetrievalData>> saleSortDealGroupCf = dpDealGroupIdsCf
                    .thenCompose(dpDealGroupId -> {
                        // 平台一致
                        Map<Long, List<Long>> shop2DpDealGroupIds = Maps.newHashMap();
                        shop2DpDealGroupIds.put(finalDpShopId, dpDealGroupId);
                        return doPaddingDeals(dpDealGroupId, shop2DpDealGroupIds, param, false);
                    })
                    .thenApply(map -> {
                        // 按照销量降序
                        return map.values().stream().sorted(Comparator.comparing(ProductRetrievalData::getSalesNum)
                                .reversed()).collect(Collectors.toList());
                    });

            return saleSortDealGroupCf.join();
        } catch (Exception e) {
            Cat.logError(e);
            log.error("searchShopProducts error", e);
            return Lists.newArrayList();
        }

    }

    @Override
    public List<ProductRetrievalData> searchShopReserveProducts(ShopProductSearchParam param) {

        try {
            // 构建预订门店商品请求上下文
            if(param.getShopId() == null || param.getShopId() <= 0){
                return Lists.newArrayList();
            }
            ReserveQueryContext reserveQueryContext = buildReserveQueryRequest(param);
            
            log.info("start searchShopReserveProducts ! reserveQueryContext={}", JSON.toJSONString(reserveQueryContext));
            // 使用策略模式，按照门店前台类目处理不同行业的预订商品召回逻辑
            List<ReserveProductQueryHandler> handlers = reserveProductQueryFactory.getHandler(reserveQueryContext);

            if (CollectionUtils.isEmpty(handlers)) {
                return Lists.newArrayList();
            }
            List<ProductRetrievalData> result = handlers.stream().map(handler -> {
                ReserveQueryContext copy = SerializationUtils.clone(reserveQueryContext);
                return handler.execute(copy);
            }).filter(Objects::nonNull).flatMap(List::stream).collect(Collectors.toList());
            log.info("end searchShopReserveProducts ! result={}", JSON.toJSONString(result));
            return result;
        } catch (Exception e) {
            Cat.logError(e);
            log.error("searchShopReserveProducts error", e);
            return Lists.newArrayList();
        }

    }

    /**
     * 构建预订门店商品请求上下文
     */
    private ReserveQueryContext buildReserveQueryRequest(ShopProductSearchParam param) {

        UserContext userContext = PluginContextUtil.getUserContext(param);
        boolean isMt = ImAccountTypeUtils.isMtUserId(userContext.getImUserId());

        // 转化点评门店ID为美团门店ID
        long shopId = param.getShopId();
        long mtShopId = isMt ? shopId : getMtShopId(shopId);
        long dpShopId = isMt ? getDpShopId(shopId) : shopId;

        // 查询点评门店后台类目id
        int shopBackCategoryId = getShopBackCategoryId(dpShopId);

        // 构建用户预订前置筛选元素
        List<FilterOption> filterOptions = buildFilterOptions(param);

        // 构建用户环境参数
        UserEnvParam userEnvParam = buildUserEnvParam(param);

        // 构建 ReserveQueryRequest 对象
        return buildReserveQueryContext(dpShopId, isMt, mtShopId, shopBackCategoryId, userEnvParam, filterOptions);
    }

    private UserEnvParam buildUserEnvParam(Param param) {
        EnvContext envContext = PluginContextUtil.getEnvContext(param);
        UserContext userContext = PluginContextUtil.getUserContext(param);

        boolean isMt = ImAccountTypeUtils.isMtUserId(userContext.getImUserId());

        UserEnvParam userEnvParam = new UserEnvParam();
        userEnvParam.setCityId(envContext.getCityId());
        userEnvParam.setClientType(ClientTypeEnum.MT_APP_ANDROID.getCode());
        userEnvParam.setLat(envContext.getLat());
        userEnvParam.setLng(envContext.getLng());
        userEnvParam.setUuId(isMt ? envContext.getDeviceId() : "");
        userEnvParam.setDpId(isMt ? "" : envContext.getDeviceId());
        userEnvParam.setUserId(userContext.getUserId());
        userEnvParam.setVersion(envContext.getAppVersion());
        return userEnvParam;
    }

    private List<FilterOption> buildFilterOptions(ShopProductSearchParam param) {
        // 默认推荐当天日期的商品
        FilterOption filterOption = new FilterOption();
        filterOption.setFilterType(ProductFilterOptionEnum.DATE.getCode());
        filterOption.setValues(buildFilterOptionValues(param));
        return Lists.newArrayList(filterOption);
    }


    private static final DateTimeFormatter formatter = DateTimeFormat.forPattern("yyyy-MM-dd");

    private Map<String, Object> buildFilterOptionValues(ShopProductSearchParam param) {
        if (StringUtils.isBlank(param.getBookDate())) {
            return buildFilterTodayOptionValues();
        }
        Map<String, Object> result = Maps.newHashMap();
        try {
            long bookDate = DateTime.parse(param.getBookDate(), formatter).getMillis();
            boolean checkResult = validatorParam(bookDate);
            if (!checkResult) {
                return null;
            }
            result.put("filterSpecificDate", Boolean.TRUE);
            result.put("bookDate", bookDate);
        } catch (Exception e) {
            log.error("buildFilterOptionValues error, {}", param.getBookDate(), e);
        }
        return result;
    }

    private Map<String, Object> buildFilterTodayOptionValues() {
        Map<String, Object> result = Maps.newHashMap();
        result.put("filterToday", Boolean.TRUE);
        return result;
    }

    private boolean validatorParam(long bookDate) {
        if (bookDate <= 0) {
            return false;
        }
        // 预订时间小于今天
        LocalDate today = LocalDate.now();
        LocalDate beginDate = Instant.ofEpochMilli(bookDate).atZone(ZoneId.systemDefault()).toLocalDate();
        if (beginDate.isBefore(today)) {
            return false;
        }

        // 预订时间超过14天
        LocalDate endDate = Instant.ofEpochMilli(bookDate).atZone(ZoneId.systemDefault()).toLocalDate();
        return !endDate.isAfter(today.plusDays(14));
    }


    private long getMtShopId(long dpShopId) {
        return shopAclService.loadMTShopIdByDPShopId(dpShopId);
    }

    private long getDpShopId(long mtShopId) {
        return shopAclService.loadDpShopIdByMtShopId(mtShopId);
    }

    private int getShopBackCategoryId(long dpShopId) {
        DpPoiDTO dpPoiDTO = shopAclService.getShopInfo(dpShopId);
        if (dpPoiDTO == null || CollectionUtils.isEmpty(dpPoiDTO.getShopBackCategoryList())) {
            return 0;
        }
        return dpPoiDTO.getShopBackCategoryList().get(0).getCategoryId();
    }

    private ReserveQueryContext buildReserveQueryContext(long dpShopId, boolean isMt, long mtShopId, int shopBackCategoryId, UserEnvParam userEnvParam, List<FilterOption> filterOptions) {
        ReserveQueryContext request = new ReserveQueryContext();
        request.setDpShopId(dpShopId);
        request.setMtShopId(mtShopId);
        request.setShopBackCategoryId(shopBackCategoryId);
        request.setUserEnvParam(userEnvParam);
        request.setPlatform(isMt ? PlatformEnum.MT.getCode(): PlatformEnum.DP.getCode());
        request.setFilterOptions(filterOptions);

        return request;
    }

    public CompletableFuture<Map<Long, List<TechnicianData>>> batchSearchShopTechnicians(List<Long> dpShopIds, Param param) {
        if (CollectionUtils.isEmpty(dpShopIds)) {
            return CompletableFuture.completedFuture(Maps.newHashMap());
        }

        Map<Long, CompletableFuture<List<TechnicianData>>> batchCf = dpShopIds.stream()
                .collect(Collectors.toMap(dpShopId -> dpShopId, dpShopId -> {
                    TechModuleRequest techModuleRequest = buildTechModuleRequest(dpShopId, param);
                    return technicianAclService.searchShopTechnicians(techModuleRequest);
        }, (k1, k2) -> k1));

        return CompletableFutureExpandUtils.eachMap(batchCf);
    }

    private TechModuleRequest buildTechModuleRequest(Long dpShopId, Param param) {
        EnvContext envContext = PluginContextUtil.getEnvContext(param);
        UserContext userContext = PluginContextUtil.getUserContext(param);
        TechModuleRequest request = new TechModuleRequest();
        request.setDpShopIdLong(dpShopId);
        request.setPageIndex(1);
        // 查10个
        request.setPageSize(10);
        Environment environment = new Environment();
        // 用美团环境
        environment.setPlatform(com.dianping.technician.common.api.enums.PlatformEnum.MEI_TUAN);
        environment.setUuid(envContext.getDeviceId());
        environment.setSourceEnum(SourceEnum.MT_APP);
        environment.setIp(userContext.getUserIp());
        environment.setAppVersion(envContext.getAppVersion());
        request.setEnvironment(environment);
        return request;
    }

    private boolean needExcludeMassageOnlineBookShop(ShopRetrievalData shopRetrievalData, ShopReserveModeDTO reserveModeDTO) {
        Boolean excludeMassageOnlineBooking = mergeSearchConfig.getExcludeMassageOnlineBooking();
        if(excludeMassageOnlineBooking == null || !excludeMassageOnlineBooking) {
            return false;
        }

        if(!StringUtils.equals(shopRetrievalData.getMtCateName1(), POIIndustryType.FOOT_MASSAGE.getDesc())) {
            return false;
        }

        if(reserveModeDTO == null) {
            return false;
        }

        ReserveModeEnum reserveMode = reserveModeDTO.getReserveMode();
        if (reserveMode == null) {
            log.info("needExcludeMassageOnlineBookShop reserveMode == null shopId: {}", shopRetrievalData.getMtShopId());
            return false;
        }

        return reserveMode.equals(ReserveModeEnum.ONLINE_RESERVE);
    }

    private boolean needExcludeDealGroupMultiSku() {
        Boolean excludeDealGroupMultiSku = mergeSearchConfig.getExcludeDealGroupMultiSku();
        return excludeDealGroupMultiSku != null && excludeDealGroupMultiSku;
    }

}
