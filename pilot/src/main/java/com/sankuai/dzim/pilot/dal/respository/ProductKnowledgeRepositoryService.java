package com.sankuai.dzim.pilot.dal.respository;

import com.sankuai.dzim.pilot.api.enums.ProductTypeEnum;
import com.sankuai.dzim.pilot.dal.entity.ProductKnowledgeEntity;

import java.util.List;

public interface ProductKnowledgeRepositoryService {

    /**
     * 添加商品知识
     *
     * @param productKnowledge
     * @return
     */
    long addProductKnowledge(ProductKnowledgeEntity productKnowledge);

    /**
     * 批量添加门店商品
     *
     * @param shopId
     * @param platform
     * @return
     */
    int batchAddProductKnowledgeByShopId(long shopId, int platform);

    /**
     * 批量删除商品知识
     *
     * @param knowledgeIds
     * @return
     */
    int batchDeleteProductKnowledge(List<Long> knowledgeIds);

    /**
     * 批量更新商品知识
     *
     * @param shopId
     * @param platform
     * @return
     */
    boolean updateProductKnowledge(long shopId, int platform);

    /**
     * 搜索商品知识
     *
     * @param query     搜索关键词
     * @param type      商品类型(用于标量过滤)
     * @param shopId    门店Id(用于标量过滤)
     * @param platform  平台(用于更新商品)
     * @param topK      返回结果数量
     * @return
     */
    List<ProductKnowledgeEntity> searchProductKnowledge(String query, int type, long shopId, int platform, int topK);
}
