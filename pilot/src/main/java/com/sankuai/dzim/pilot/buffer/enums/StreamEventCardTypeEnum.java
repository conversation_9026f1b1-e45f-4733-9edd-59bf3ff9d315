package com.sankuai.dzim.pilot.buffer.enums;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/7/29 19:28
 */
@Getter
@AllArgsConstructor
public enum StreamEventCardTypeEnum {

    @Deprecated
    SHOP("shop", "商家卡片", false, true),

    @Deprecated
    PRODUCT("product", "商品卡片", false,true),

    RELATED_QUESTION_CARD("GuessAskQuestion", "相关问题卡片", true,true),

    FEEDBACK_TAIL_CARD("FeedbackTail", "反馈尾部卡片", false,true),

    TECHNICIAN_CARD("technician", "手艺人卡片", false,true),

    KNOWLEDGE_CARD("KnowledgeSource", "数据来源卡片", false,true),

    PICTURE_CARD("Picture", "图片卡片", false,true),

    OPERATE_ANSWER("operateAnswer", "运营问题答案卡片", false,true),

    MEDICAL_CHECKUP_INDICATOR_CARD("MedicalCheckupIndicator", "体检报告指标解读卡片", false,true),

    HAIR_CUT_BOOK_CARD("HairCutBook", "美发预约卡片", true,true),

    HAIR_CUT_SUBMIT_CARD("HairCutSubmit", "美发提交卡片", false,true),

    RELATED_QUESTIONS_CARD("GuessAskQuestions", "相关问题卡片，多问题合并一个卡片", true,true),

    RECOMMEND_SHOP_URL("RecommendShopUrl", "模型返回门店跳链标签，缓冲池跳链替换", false,false),

    SHOP_CARD("ShopCard", "商家卡片", false, true),

    PRODUCT_CARD("ProductCard", "商品卡片", false,true),

    BOOK_CONFIRM("BookConfirm", "预约确认卡片", false,true),

    BOOK_CARD("BookCard", "预约卡片", false, true),

    ORDER_CARD("OrderCard", "商品订单卡片", true,true),

    SHOP_PRODUCT_CARD("ShopProductCard", "纯商品卡片", false,true),

    FEEDBACK_TAIL_TEXT_CARD("FeedbackTextTail", "反馈尾部文本卡片", false,true),

    THINK_PROCESS_CARD("ThinkProcess", "思考过程卡片", false,true),

    WELCOME_GUESS_ASK_QUESTIONS("WelcomeGuessAskQuestions", "欢迎语猜你想问卡片", true,true),
    ;


    private final String type;

    private final String desc;

    /**
     * 是否为气泡外卡片，类似猜你想问卡片
     */
    private boolean isOutBubble;

    /**
     * 是否需要卡片
     */
    private boolean isNeedCard;

    private static final List<StreamEventCardTypeEnum> NOT_DISPLAY_IN_MEMORY_CARD_TYPES = Lists.newArrayList(RELATED_QUESTION_CARD, FEEDBACK_TAIL_CARD, FEEDBACK_TAIL_TEXT_CARD);

    public static StreamEventCardTypeEnum getByType(String type) {
        for (StreamEventCardTypeEnum typeEnum : values()) {
            if (typeEnum.getType().equals(type)) {
                return typeEnum;
            }
        }
        return null;
    }

    public String getPrefixTag() {
        return "<" + type + ">";
    }

    public String getSuffixTag() {
        return "</" + type + ">";
    }

    public static String buildCardContent(StreamEventCardTypeEnum typeEnum, String key) {
        return typeEnum.getPrefixTag() + key + typeEnum.getSuffixTag();
    }

    public static String buildCardContent(String type, String key) {
        return String.format("<%s>%s</%s>", type, key, type);
    }

    /**
     * pike推送直接推前端的场景，没有走buffer逻辑，需要显式加上:::}{:::的气泡标签
     */
    public static String buildCardContentWithBubbleTag(StreamEventCardTypeEnum typeEnum, String key) {
        String cardContent = buildCardContent(typeEnum, key);
        //气泡内卡片
        if (typeEnum.isOutBubble()) {
            return ":::{" + cardContent + "}:::";
        }
        //气泡外卡片
        return ":::}" + cardContent + "{:::";
    }

    /**
     * 在给大模型消息历史中不展示的卡片类型，例如相关问题，尾部点赞点踩等卡片
     */
    public static boolean isNotDisplayInAssistantMemoryCardType(String type) {
        return NOT_DISPLAY_IN_MEMORY_CARD_TYPES.contains(getByType(type));
    }
}
