package com.sankuai.dzim.pilot.enums;

import lombok.Getter;

/**
 * @author: zhouyibing
 * @date: 2025/4/26
 */
@Getter
public enum GroupBuyOrderBizStatusEnum {

    /**
     * 团购订单只保存支付状态卡片
     * 待支付→已支付，待消费
     * 待支付→取消支付
     * 待支付→支付超时
     */
    PAYMENT_CARD(1, "支付卡片"),

    ;

    private int type;

    private String desc;

    GroupBuyOrderBizStatusEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
