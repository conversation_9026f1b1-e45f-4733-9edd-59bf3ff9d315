package com.sankuai.dzim.pilot.process.aibook.impl;

import com.dianping.cat.Cat;
import com.dianping.generic.entrance.poiphone.dto.PhoneDTO;
import com.dianping.martgeneral.recommend.api.entity.RecommendParameters;
import com.dianping.poi.bizhour.dto.BizForecastDTO;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.dianping.vc.enums.VCPlatformEnum;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.dianping.zebra.group.router.ZebraForceMasterHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.data.ups.thrift.LabelData;
import com.meituan.mafka.client.bean.MafkaProducer;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.acl.*;
import com.sankuai.dzim.pilot.acl.data.req.CreateBookReq;
import com.sankuai.dzim.pilot.acl.AIBookCallAclService;
import com.sankuai.dzim.pilot.acl.ShopAclService;
import com.sankuai.dzim.pilot.acl.TokenAccessAclService;
import com.sankuai.dzim.pilot.acl.UserAclService;
import com.sankuai.dzim.pilot.api.data.beauty.ai.BookCallbackReq;
import com.sankuai.dzim.pilot.api.enums.aibook.TaskStatusEnum;
import com.sankuai.dzim.pilot.api.enums.assistant.PlatformEnum;
import com.sankuai.dzim.pilot.api.enums.beauty.ai.BookResultEnum;
import com.sankuai.dzim.pilot.dal.entity.aibook.AIBookDetailEntity;
import com.sankuai.dzim.pilot.dal.entity.aibook.AIBookTaskEntity;
import com.sankuai.dzim.pilot.dal.pilotdao.aibook.AIBookDetailDAO;
import com.sankuai.dzim.pilot.dal.pilotdao.aibook.AIBookTaskDAO;
import com.sankuai.dzim.pilot.dal.respository.aibook.AIBookTaskRepositoryService;
import com.sankuai.dzim.pilot.dal.respository.aibook.data.AIBookTaskE;
import com.sankuai.dzim.pilot.gateway.api.aibook.AIBookSubmitMAPI;
import com.sankuai.dzim.pilot.process.aibook.AIBookAssistantProcessService;
import com.sankuai.dzim.pilot.process.aibook.AIBookDispatchProcessService;
import com.sankuai.dzim.pilot.process.aibook.data.*;
import com.sankuai.dzim.pilot.scene.task.data.TaskTypeEnum;
import com.sankuai.dzim.pilot.utils.*;
import com.sankuai.meituan.wmdrecsys.aigc.api.domain.outboundcall.BookingTimeExtraInfo;
import com.sankuai.meituan.wmdrecsys.aigc.api.domain.outboundcall.BusinessSource;
import com.sankuai.meituan.wmdrecsys.aigc.api.domain.outboundcall.Gender;
import com.sankuai.meituan.wmdrecsys.aigc.api.domain.outboundcall.OutboundCallRequest;
import com.sankuai.sinai.data.api.dto.MtPoiDTO;
import com.sankuai.sinai.data.api.dto.TypeHierarchyView;
import com.sankuai.wpt.user.retrieve.thrift.message.UserModel;
import com.sankuai.zdc.tag.apply.dto.DisplayTagDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.joda.time.DateTimeZone;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sankuai.dzim.pilot.process.aibook.data.AIBookConstants.AI_BOOK_CALL_BACK_BIZ_TYPE_BOOK;
import static com.sankuai.dzim.pilot.process.aibook.data.AIBookConstants.AI_BOOK_CALL_BACK_BIZ_TYPE_CANCEL;

/**
 * <AUTHOR>
 * @since 2025/1/9 11:12
 */
@Slf4j
@Service
public class AIBookDispatchProcessServiceImpl implements AIBookDispatchProcessService {

    @Resource(name = "redisClient")
    private RedisStoreClient redisStoreClient;

    @Resource
    private TokenAccessAclService tokenAccessAclService;

    @Resource
    private AIBookTaskRepositoryService aiBookTaskRepositoryService;

    @Resource
    private MafkaProducer aiBookDispatchDelayProducer;

    @Resource
    private MafkaProducer aiBookCallbackDelayProducer;


    @Resource
    private MafkaProducer aiBookCallAgainDelayProducer;

    @Resource
    private AIBookTaskDAO aiBookTaskDAO;

    @Resource
    private AIBookDetailDAO aiBookDetailDAO;


    @Resource
    private AIBookCallAclService aiBookCallAclService;

    @Resource
    private ShopAclService shopAclService;

    @Resource
    private UserAclService userAclService;

    @Resource
    private LeadsAclService leadsAclService;

    @Autowired
    private RecommendAclService recommendAclService;

    @Autowired
    private LionConfigUtil lionConfigUtil;

    @Autowired
    private AIBookAssistantProcessService aiBookAssistantProcessService;

    @Override
    public Pair<Integer, Long> dispatchUserBookTask(UserBookRequest request) {
        if (request == null) {
            return Pair.of(AIBookSubmitMAPI.BOOK_SUBMIT_FAIL_SYSTEM_EXCEPTION_CODE, 0L);
        }

        // 召回门店
        long startTime = System.currentTimeMillis();
        List<Long> shopIds = recallRecommendShopIds(request);
        Cat.newCompletedTransactionWithDuration("AIBookRecallShop", "recallRecommendShopIds", System.currentTimeMillis() - startTime);

        // 任务写入
        int taskStatus = getCurrentTimeNeedDelay() ? TaskStatusEnum.HAS_TASK_DELAY.getCode() : TaskStatusEnum.HAS_TASK_ASKING.getCode();
        if (CollectionUtils.isEmpty(shopIds)) {
            Cat.logEvent("DispatchUserBookTask", "NoShopIds");
            return Pair.of(AIBookSubmitMAPI.BOOK_SUBMIT_SHOP_EMPTY, 0L);
        }
        AIBookTaskEntity task = buildAIBookTaskEntity(request, taskStatus);
        List<AIBookDetailEntity> details = buildTaskDetails(request, shopIds);
        long taskId = aiBookTaskRepositoryService.insertAIBookTask(task, details);
        if (taskId <= 0) {
            Cat.logEvent("DispatchUserBookTask", "AddTaskFail");
            return Pair.of(AIBookSubmitMAPI.BOOK_SUBMIT_FAIL_SYSTEM_EXCEPTION_CODE, 0L);
        }

        // 调度内部逻辑，发起延迟或发起外呼
        dispatchInternal(taskStatus == TaskStatusEnum.HAS_TASK_DELAY.getCode(), taskId);
        return Pair.of(AIBookSubmitMAPI.BOOK_SUBMIT_SUCC_CODE, taskId);
    }


    @Override
    public List<Long> recallRecommendShopIds(UserBookRequest request) {

        // 商详页进入
        if (request.getShopId() > 0) {
            if (request.getRecommendSwitch() == 0) {
                return Lists.newArrayList(request.getShopId());
            }
        }

        // mock召回数据
        if (lionConfigUtil.getAiBookMockConfigData().hitMock(request.getPlatform(), request.getUserId())) {
            List<Long> mockShopIds = lionConfigUtil.getAiBookMockConfigData().getMockShopIds(request.getPlatform(), request.getUserId());
            return mockShopIds;
        }

        List<Long> result = Lists.newArrayList();
        // 查询用户性别并填充到request中
        long mtUserId = getMtUserId(request.getPlatform(), request.getUserId());
        int gender = queryUserGender(mtUserId);
        request.setUserGender(gender);

        // 召回请求入参门店
        List<Long> inParamShopIds = recallInParamShopIds(request);
        result.addAll(inParamShopIds);

        // 召回招商门店
        List<Long> advertiseShopIds = recallAdvertiseRecommendShopIds(request);
        result.addAll(advertiseShopIds);

        // 召回默认推荐门店
        List<Long> recallShopIds = recallDefaultRecommendShopIds(request, 1, result);

        // 返回全部的门店
        return recallShopIds;
    }

    private List<Long> recallInParamShopIds(UserBookRequest request) {
        List<Long> inParamShopIds = Lists.newArrayList();
        if (request.getShopId() > 0) {
            inParamShopIds.add(request.getShopId());
        }
        if (CollectionUtils.isNotEmpty(request.getShopIds())) {
            inParamShopIds.addAll(request.getShopIds());
        }
        return inParamShopIds;
    }


    private List<Long> recallAdvertiseRecommendShopIds(UserBookRequest request) {

        List<Long> shopAdvertiseTagIds = lionConfigUtil.getRecommendShopConfigData().getShopAdvertiseTagIds();
        if (CollectionUtils.isEmpty(shopAdvertiseTagIds)) {
            return Lists.newArrayList();
        }
        // 招商门店召回，设置招商的商户标签ID
        int shopAdvertiseSizeLimit = lionConfigUtil.getRecommendShopConfigData().getShopAdvertiseSizeLimit();
        RecommendParameters recommendParameters = buildRecommendParameters(request, 1, shopAdvertiseSizeLimit);
        Map<String, Object> bizParams = recommendParameters.getBizParams();
        bizParams.put("zdcTagIds", StringUtils.join(shopAdvertiseTagIds, ","));
        List<Long> shopIds = recommendAclService.getRecommendShopIds(recommendParameters);
        if (CollectionUtils.isEmpty(shopIds)) {
            return Lists.newArrayList();
        }
        // 门店过滤
        return filterShops(request, shopIds);
    }

    private List<Long> recallDefaultRecommendShopIds(UserBookRequest request, int pageNo, List<Long> recallShopIds) {

        RecommendShopConfigData recommendShopConfigData = lionConfigUtil.getRecommendShopConfigData();
        // 超出数量限制
        if (recallShopIds.size() >= recommendShopConfigData.getRecommendShopSizeLimit()) {
            return recallShopIds.stream().distinct().limit(recommendShopConfigData.getRecommendShopSizeLimit()).collect(Collectors.toList());
        }

        // 超出召回次数限制
        if (pageNo > recommendShopConfigData.getTurnLimit()) {
            return recallShopIds.stream().distinct().collect(Collectors.toList());
        }

        // 门店召回
        RecommendParameters recommendParameters = buildRecommendParameters(request, pageNo, recommendShopConfigData.getPageSize());
        List<Long> shopIds = recommendAclService.getRecommendShopIds(recommendParameters);
        if (CollectionUtils.isEmpty(shopIds)) {
            return recallShopIds.stream().distinct().collect(Collectors.toList());
        }

        // 门店过滤
        List<Long> filterShopIds = filterShops(request, shopIds);
        recallShopIds.addAll(filterShopIds);

        return recallDefaultRecommendShopIds(request, pageNo + 1, recallShopIds);
    }


    @Override
    public boolean dispatchDelayTask(AIBookDispatchDelayData data) {
        long taskId = data.getTaskId();
        AIBookTaskEntity taskEntity = aiBookTaskDAO.getById(taskId);
        if (taskEntity == null) {
            Cat.logEvent("DispatchDelayTask", "TaskNotExist");
            return false;
        }
        if (taskEntity.getTaskStatus() != TaskStatusEnum.HAS_TASK_DELAY.getCode()) {
            Cat.logEvent("DispatchDelayTask", "TaskStatusNotMatch");
            return false;
        }
        List<AIBookDetailEntity> detailEntities = aiBookDetailDAO.getByTaskId(taskId);
        if (CollectionUtils.isEmpty(detailEntities)) {
            Cat.logEvent("DispatchDelayTask", "DetailsEmpty");
            return false;
        }
        // 更新任务表状态
        aiBookTaskDAO.updateTaskStatus(taskId, TaskStatusEnum.HAS_TASK_ASKING.getCode());

        // 执行门店外呼
        dispatchInternal(getCurrentTimeNeedDelay(), taskId);
        return true;
    }

    @Override
    public boolean processCallbackTimeout(AIBookCallbackDelayData data) {

        long taskId = data.getTaskId();
        AIBookTaskEntity taskEntity = aiBookTaskDAO.getById(taskId);
        if (taskEntity == null) {
            Cat.logEvent("ProcessCallbackTimeout", "TaskNotExist");
            return false;
        }

        if (taskEntity.getTaskStatus() != TaskStatusEnum.HAS_TASK_ASKING.getCode()) {
            Cat.logEvent("ProcessCallbackTimeout", "TaskStatusNotMatch");
            return false;
        }

        AIBookDetailEntity detail = aiBookDetailDAO.getById(data.getDetailId());
        if (detail == null) {
            Cat.logEvent("ProcessCallbackTimeout", "TaskDetailNotExist");
            return false;
        }

        if (detail.getCallResult() != TaskCallResultEnum.WAIT_CALL_BACK.getCode()) {
            Cat.logEvent("ProcessCallbackTimeout", "TaskDetailCallResultNotMatch");
            return false;
        }


        StoreKey callAgainTimes = new StoreKey(AIBookConstants.AI_BOOK_SHOP_CALL_AGAIN_TIME, detail.getId());
        Object timesVal = redisStoreClient.get(callAgainTimes);
        int redisTimes = timesVal == null ? 0 : NumberUtils.toInt(timesVal.toString());
        long callAgainTime = data.getCallAgainTime();
        if (redisTimes > 0 && callAgainTime < lionConfigUtil.getDispatchConfigData().getShopCallAgainTimeLimit()) {
            Cat.logEvent("ProcessCallbackTimeout", "DetailHasCallAgain");
            return false;
        }

        BookCallbackReq bookCallbackReq = new BookCallbackReq();
        bookCallbackReq.setBizType(AI_BOOK_CALL_BACK_BIZ_TYPE_BOOK);
        bookCallbackReq.setCallResult(BookResultEnum.CALL_BACK_TIMEOUT.getCode());
        bookCallbackReq.setBookingTimestamp(0L);
        bookCallbackReq.setOrderId(detail.getId());
        bookCallbackReq.setReceiptMessage(StringUtils.EMPTY);
        bookCallbackReq.setReleaseReason(StringUtils.EMPTY);
        boolean isSuccess = callbackAIBook(bookCallbackReq);
        LogUtils.logReturnInfo(log, TagContext.builder().action("processCallbackTimeout").build(),
                new WarnMessage("processCallbackTimeout", "预约单超时回调处理记录", ""), data, isSuccess);
        return isSuccess;
    }

    @Override
    public boolean processCallAgain(AIBookCallAgainDelayData data) {
        long taskId = data.getTaskId();
        AIBookTaskEntity taskEntity = aiBookTaskDAO.getById(taskId);
        if (taskEntity == null) {
            Cat.logEvent("processCallAgain", "TaskNotExist");
            return false;
        }

        if (taskEntity.getTaskStatus() != TaskStatusEnum.HAS_TASK_ASKING.getCode()) {
            Cat.logEvent("processCallAgain", "TaskStatusNotMatch");
            return false;
        }

        AIBookDetailEntity detail = aiBookDetailDAO.getById(data.getDetailId());
        if (detail == null) {
            Cat.logEvent("processCallAgain", "TaskDetailNotExist");
            return false;
        }

        if (detail.getCallResult() != TaskCallResultEnum.WAIT_CALL_BACK.getCode()) {
            Cat.logEvent("processCallAgain", "TaskDetailCallResultNotMatch");
            return false;
        }

        // 记录重拨次数
        StoreKey callAgainTimes = new StoreKey(AIBookConstants.AI_BOOK_SHOP_CALL_AGAIN_TIME, detail.getId());
        Long callAgainTime = redisStoreClient.incrBy(callAgainTimes, 1, 7 * 24 * 60 * 60);

        // 延迟再次拨打
        executeSingleShop(taskEntity, detail, callAgainTime);

        // 记录日志
        LogUtils.logReturnInfo(log, TagContext.builder().action("processCallAgain").build(),
                new WarnMessage("processCallAgain", "单门店重呼处理", ""), data, true);
        return true;
    }


    @Override
    public boolean executeTaskCall(long taskId) {

        // 查询主库任务数据，判断是否需要调度
        AIBookTaskEntity taskEntity = aiBookTaskDAO.getById(taskId);
        List<AIBookDetailEntity> detailEntities = aiBookDetailDAO.getByTaskId(taskId);

        // 还有带外呼的门店
        List<AIBookDetailEntity> waitCallDetails = detailEntities.stream().filter(detail ->
                detail.getCallResult() == TaskCallResultEnum.INITIAL.getCode()).collect(Collectors.toList());
        // 当前有正在等待回调的门店
        List<AIBookDetailEntity> waitCallbackDetails = detailEntities.stream().filter(detail ->
                detail.getCallResult() == TaskCallResultEnum.WAIT_CALL_BACK.getCode()).collect(Collectors.toList());

        if (taskEntity.getTaskStatus() != TaskStatusEnum.HAS_TASK_ASKING.getCode() ||
                CollectionUtils.isEmpty(waitCallDetails) ||
                CollectionUtils.isNotEmpty(waitCallbackDetails)) {
            // 任务状态不是外呼中 或者没有带外呼的门店 或者有等待回调的门店，暂定调度下一个门店
            return false;
        }

        // 逐个判断是否能外呼
        for (AIBookDetailEntity detailEntity : waitCallDetails) {
            if (!isShopCanBookCall(detailEntity.getShopId(), detailEntity.getPlatform(), taskEntity.getBookStartTime(), taskEntity.getBookEndTime())) {
                aiBookDetailDAO.updateCallResult(detailEntity.getId(), TaskCallResultEnum.BOOK_SKIP.getCode());
            } else {
                String contactId = executeSingleShop(taskEntity, detailEntity, 0L);
                if (StringUtils.isEmpty(contactId)) {
                    aiBookDetailDAO.updateCallResult(detailEntity.getId(), TaskCallResultEnum.BOOK_FAILED.getCode());
                } else {
                    aiBookDetailDAO.updateCallResult(detailEntity.getId(), TaskCallResultEnum.WAIT_CALL_BACK.getCode());
                    break;
                }
            }
        }

        // 总结任务状态，门店可能已经调度完，则汇总数据到任务信息上，包括任务状态、预约时间等
        summaryTaskFailedStatus(taskEntity.getId());
        return true;
    }

    @Override
    public boolean callbackAIBook(BookCallbackReq req) {

        // 参数校验
        if (req == null || req.getOrderId() <= 0) {
            Cat.logEvent("CallbackAIBook", "InvalidRequest");
            return false;
        }
        AIBookDetailEntity detailEntity = aiBookDetailDAO.findById(req.getOrderId());
        if (detailEntity == null) {
            Cat.logEvent("CallbackAIBook", "DetailNotExist");
            return false;
        }

        AIBookTaskEntity taskEntity = aiBookTaskDAO.getById(detailEntity.getTaskId());
        if (taskEntity == null) {
            Cat.logEvent("CallbackAIBook", "TaskNotExist");
            return false;
        }

        CompletableFuture.runAsync(() -> {
            // 处理这次回调的数据
            processCallbackData(req, taskEntity, detailEntity);

            // 失败更新任务主状态
            summaryTaskFailedStatus(taskEntity.getId());

            // 继续调度任务
            dispatchInternal(getCurrentTimeNeedDelay(), taskEntity.getId());

        }, ThreadPoolUtil.AI_BOOK_CALLBACK_POOL.getExecutor());

        return true;
    }

    @Override
    public boolean dispatchCancelTask(Long leadsId) {
        LogUtils.logRequestLog(log, TagContext.builder().bizId(String.valueOf(leadsId)).build(), "dispatchCancelTask", leadsId);

        try {
            // 查询任务
            AIBookTaskE aiBookTaskE = aiBookTaskRepositoryService.findTaskByLeadsId(leadsId);
            if (aiBookTaskE == null || CollectionUtils.isEmpty(aiBookTaskE.getDetails())) {
                LogUtils.logFailLog(log, TagContext.builder().bizId(String.valueOf(leadsId)).build(),
                        new WarnMessage("AIBookDispatchProcessService:dispatchCancelTask", "预约单不存在", ""), leadsId, null);
                return false;
            }

            // 取消
            return executeCancel(leadsId, aiBookTaskE);
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().bizId(String.valueOf(leadsId)).build(),
                    new WarnMessage("AIBookDispatchProcessService:dispatchCancelTask", "取消预约失败", "系统异常"), leadsId, null, e);
        }
        return false;
    }


    private void summaryTaskFailedStatus(long taskId) {
        if (taskId <= 0) {
            return;
        }
        ZebraForceMasterHelper.forceMasterInLocalContext();
        AIBookTaskEntity taskEntity = aiBookTaskDAO.getById(taskId);
        List<AIBookDetailEntity> detailEntities = aiBookDetailDAO.getByTaskId(taskId);
        ZebraForceMasterHelper.clearLocalContext();

        if (taskEntity == null || CollectionUtils.isEmpty(detailEntities)) {
            return;
        }

        if (TaskStatusEnum.isBookCallEnd(taskEntity.getTaskStatus())) {
            return;
        }

        boolean isAllFailed = detailEntities.stream().allMatch(detail -> detail.getCallResult() == TaskCallResultEnum.BOOK_FAILED.getCode() ||
                detail.getCallResult() == TaskCallResultEnum.BOOK_SKIP.getCode() ||
                detail.getCallResult() == TaskCallResultEnum.TIMEOUT_CALL_BACK.getCode());
        if (!isAllFailed) {
            return;
        }

        // 外呼结束 并且 没哟预约成功的，更新任务状态为失败
        aiBookTaskDAO.updateTaskStatus(taskId, TaskStatusEnum.BOOK_FAILED.getCode());

        //发送小助手失败消息
        aiBookAssistantProcessService.sendBookResultAssistantReply(taskEntity.getPlatform(), taskEntity.getUserId(), false);


        // 发送预约失败触达：接预约接口
        long mtUserId = getMtUserId(taskEntity.getPlatform(), taskEntity.getUserId());
        UserModel mtUserModel = userAclService.getMtUserModel(mtUserId);
        String userMobile = Optional.ofNullable(mtUserModel).map(UserModel::getMobile).orElse(StringUtils.EMPTY);
        leadsAclService.bookFailedPush(taskEntity.getPlatform(), taskEntity.getUserId(), userMobile);

        // 记录日志
        LogUtils.logReturnInfo(log, TagContext.builder().action("summaryTaskFailedStatus").build(),
                new WarnMessage("summaryTaskFailedStatus", "任务失败状态回写", ""), taskId, "success");
    }


    private String executeSingleShop(AIBookTaskEntity taskEntity, AIBookDetailEntity detailEntity, long callAgainTime) {

        // 查询门店、用户数据
        long mtShopId = getMtShopId(detailEntity.getShopId(), detailEntity.getPlatform());
        long dpShopId = getDpShopId(detailEntity.getShopId(), detailEntity.getPlatform());

        MtPoiDTO mtShopInfo = shopAclService.getMtShopInfo(mtShopId);
        String shopName = buildShopName(mtShopInfo);
        String userPlainMobile = tokenAccessAclService.getPlainMobile(taskEntity.getUserPhone());
        long mtUserId = getMtUserId(taskEntity.getPlatform(), taskEntity.getUserId());
        int gender = queryUserGender(mtUserId);
        List<String> shopPhoneList = shopAclService.getShopPhone(dpShopId);
        String shopMobile = callAgainTime > 0 ? tokenAccessAclService.getPlainMobile(detailEntity.getCallPhone()) :
                buildShopMobile(shopPhoneList);

        // 外呼拨打
        Pair<Integer, String> pair = aiBookCallAclService.sendHairstylingOutboundCall(buildOutboundCallRequest(shopName, shopMobile,
                gender, userPlainMobile, taskEntity, detailEntity));
        // 根据拨打结果将门店手机列入黑名单
        int code = pair.getKey();
        if (CollectionUtils.isNotEmpty(lionConfigUtil.getDispatchConfigData().getShopPhoneBlackListCode()) &&
                lionConfigUtil.getDispatchConfigData().getShopPhoneBlackListCode().contains(code)) {
            addShopPhoneBlackList(VCPlatformEnum.MT.getType(), mtShopId, shopMobile);
        }

        // 拨打成功，更新门店数据
        String contactId = pair.getRight();
        if (StringUtils.isNotEmpty(contactId)) {
            // 当日外呼次数+1
            StoreKey shopCntKey = new StoreKey(AIBookConstants.AI_BOOK_SHOP_CNT_CATEGORY, mtShopId, DateUtils.covertDateNumStr(new Date()));
            redisStoreClient.incrBy(shopCntKey, 1, 24 * 60 * 60);

            // 门店外呼上锁
            StoreKey shopLockKey = new StoreKey(AIBookConstants.AI_BOOK_SHOP_LOCK_CATEGORY, mtShopId);
            redisStoreClient.set(shopLockKey, "1", 5 * 60);
        }

        // 更新记录外呼时间
        AIBookDetailExtraData extraData = AIBookDetailExtraData.builder().contactId(contactId).build();
        aiBookDetailDAO.updateCallTime(detailEntity.getId(), new Date(), JsonCodec.encodeWithUTF8(extraData),
                tokenAccessAclService.getTokenMobile(shopMobile));

        // 发送外呼回调延时，用于兜底处理回调异常场景，避免流程中断
        int delaySeconds = lionConfigUtil.getDispatchConfigData().getCallbackDelaySeconds() <= 0 ? 600 :
                lionConfigUtil.getDispatchConfigData().getCallbackDelaySeconds();
        ProducerUtils.sendDelayMessage(aiBookCallbackDelayProducer, AIBookCallbackDelayData.builder().taskId(taskEntity.getId())
                .detailId(detailEntity.getId()).callAgainTime(callAgainTime).build(), delaySeconds * 1000L, "aiBookCallbackDelayProducer");

        // 记录日志
        LogUtils.logReturnInfo(log, TagContext.builder().action("executeSingleShop").build(),
                WarnMessage.build("executeSingleShop", "单个门店发起外呼", ""),
                taskEntity.getId(), Lists.newArrayList(detailEntity.getId(), callAgainTime));
        return contactId;
    }


    private String buildShopMobile(List<String> shopPhoneList) {
        if (CollectionUtils.isEmpty(shopPhoneList)) {
            return StringUtils.EMPTY;
        }
        List<String> shopMobile = shopPhoneList.stream().filter(this::checkIsPhone).collect(Collectors.toList());
        return CollectionUtils.isNotEmpty(shopMobile) ? shopMobile.get(new Random().nextInt(shopMobile.size())) :
                shopPhoneList.get(new Random().nextInt(shopPhoneList.size()));
    }


    private boolean checkIsPhone(String phone) {
        String regex = "^1\\d{10}$";
        return phone.matches(regex);
    }


    private long getMtUserId(int platform, long userId) {
        if (platform == VCPlatformEnum.DP.getType()) {
            return userAclService.getMtRealBindUserId(userId);
        }
        return userId;
    }

    private OutboundCallRequest buildOutboundCallRequest(String shopName, String shopMobile, int gender, String userPlainPhone,
                                                         AIBookTaskEntity taskEntity, AIBookDetailEntity detailEntity) {
        OutboundCallRequest request = new OutboundCallRequest();
        request.setMerchantName(shopName);
        request.setPhoneNumber(shopMobile);
        //美团用户中心 男-1 女-2
        request.setBookingUserGender(gender == Gender.OTHER.getValue() ? Gender.MALE : Gender.findByValue(gender));
        request.setBookingPhoneNumber(userPlainPhone);
        BookingTimeExtraInfo bookingTimeExtraInfo = new BookingTimeExtraInfo();
        bookingTimeExtraInfo.setStartTimestamp(buildBookStartTime(taskEntity).getTime() / 1000);
        bookingTimeExtraInfo.setEndTimestamp(taskEntity.getBookEndTime().getTime() / 1000);
        request.setBookingTimeExtraInfo(bookingTimeExtraInfo);
        request.setBookingType(Optional.ofNullable(IntentProjectCodeEnum.getByCode(taskEntity.getBookType()))
                .map(IntentProjectCodeEnum::getDesc).orElse(IntentProjectCodeEnum.HAIRDRESSING.getDesc()));
        request.setBusinessSource(BusinessSource.HAIRSTYLING_BOOKING);
        request.setOrderId(detailEntity.getId());
        return request;
    }

    private Date buildBookStartTime(AIBookTaskEntity taskEntity) {
        Date nowAddOffsetTime = TimeUtil.getCurrentTimePlusOffsetMinute(
                lionConfigUtil.getDispatchConfigData().getDispatchMinuteOffset());
        Date taskBookStartTime = taskEntity.getBookStartTime();
        Date taskBookEndTime = taskEntity.getBookEndTime();
        // 当前时间取整后加半小时 比 任务记录的预约开始时间大，并且比任务记录的预约结束时间早半小时以上，
        // 则用计算后的作为外呼开始时间，解决由于外呼耗时过长，原定开始时间不够预留的问题
        if (nowAddOffsetTime.after(taskBookStartTime) && (taskBookEndTime.getTime() - nowAddOffsetTime.getTime()) > 30 * 60 * 1000) {
            return nowAddOffsetTime;
        }
        return taskBookStartTime;
    }


    private String buildShopName(MtPoiDTO mtPoiDTO) {
        if (StringUtils.isEmpty(mtPoiDTO.getBranchName())) {
            return mtPoiDTO.getName();
        }
        return mtPoiDTO.getName() + "(" + mtPoiDTO.getBranchName() + ")";
    }

    private long getMtShopId(long shopId, int platform) {
        if (platform == VCPlatformEnum.MT.getType()) {
            return shopId;
        }
        return shopAclService.loadMTShopIdByDPShopId(shopId);
    }

    private long getDpShopId(long shopId, int platform) {
        if (platform == VCPlatformEnum.MT.getType()) {
            return shopAclService.loadDpShopIdByMtShopId(shopId);
        }
        return shopId;
    }

    private List<Long> filterShops(UserBookRequest request, List<Long> shopIds) {
        long startTime = System.currentTimeMillis();
        Map<Long, Long> shopIdMap = getShopIdMap(shopIds, request.getPlatform());

        // 营业状态和类目过滤
        shopIds = filterShopByBusinessStatus(request, shopIds, shopIdMap);

        // 营业时间过滤
        if (CollectionUtils.isNotEmpty(shopIds)) {
            shopIds = filterShopByBizTime(request, shopIds, shopIdMap);
        }

        // 库存过滤
        if (CollectionUtils.isNotEmpty(shopIds)) {
            shopIds = filterShopByStock(request, shopIds, shopIdMap);
        }

        // 手机号过滤
        if (CollectionUtils.isNotEmpty(shopIds)) {
            shopIds = filterShopByPhone(request, shopIds, shopIdMap);
        }

        // 商户黑名单那过滤
        if (CollectionUtils.isNotEmpty(shopIds)) {
            shopIds = filterShopByBlackList(request, shopIds, shopIdMap);
        }

        // 商户手机号黑名单过滤
        if (CollectionUtils.isNotEmpty(shopIds)) {
            shopIds = filterShopByPhoneBlackList(request, shopIds, shopIdMap);
        }

        // 单日黑名单商户过滤
        if (CollectionUtils.isNotEmpty(shopIds)) {
            shopIds = filterShopDailyBlackList(request, shopIds, shopIdMap);
        }

        // 用户取消门店过滤
        if (CollectionUtils.isNotEmpty(shopIds)) {
            shopIds = filterShopByUserCancel(request, shopIds, shopIdMap);
        }

        Cat.newCompletedTransactionWithDuration("AIBookRecallShop", "filterShops", System.currentTimeMillis() - startTime);
        return shopIds;
    }


    private List<Long> filterShopDailyBlackList(UserBookRequest request, List<Long> shopIds, Map<Long, Long> shopIdMap) {
        return shopIds.stream().filter(shopId -> {
            long mtShopId = shopId;
            if (request.getPlatform() == PlatformEnum.DP.getType()) {
                mtShopId = shopIdMap.getOrDefault(shopId, 0L);
            }
            StoreKey shopPhoneBlackListKey = new StoreKey(AIBookConstants.AI_BOOK_SHOP_DAILY_BLACK_LIST, mtShopId, DateUtils.covertDateNumStr(new Date()));
            return !redisStoreClient.exists(shopPhoneBlackListKey);
        }).collect(Collectors.toList());
    }

    private List<Long> filterShopByUserCancel(UserBookRequest request, List<Long> shopIds, Map<Long, Long> shopIdMap) {
        List<AIBookTaskEntity> canceledTasks = aiBookTaskDAO.queryUserTasksByStatus(request.getUserId(),
                request.getPlatform(), Lists.newArrayList(TaskStatusEnum.CANCEL.getCode()));
        if (CollectionUtils.isEmpty(canceledTasks)) {
            return shopIds;
        }
        // 时间范围内，同项目的已取消任务
        List<AIBookTaskEntity> matchedCanceledTasks = canceledTasks.stream()
                .filter(task -> task.getBookTime() != null)
                .filter(task -> task.getBookTime().after(request.getBookStartTime()) && task.getBookTime().before(request.getBookEndTime()))
                .filter(task -> task.getBookType() == request.getBookType())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(matchedCanceledTasks)) {
            return shopIds;
        }
        List<Long> taskIds = matchedCanceledTasks.stream().map(AIBookTaskEntity::getId).collect(Collectors.toList());
        List<AIBookDetailEntity> canceledDetails = aiBookDetailDAO.queryByTaskIdsAndCallResults(taskIds,
                Lists.newArrayList(TaskCallResultEnum.CANCEL_SUCCESS.getCode(), TaskCallResultEnum.CANCEL_FAILED.getCode(),
                        TaskCallResultEnum.CANCEL_SKIP.getCode()));
        if (CollectionUtils.isEmpty(canceledDetails)) {
            return shopIds;
        }
        List<Long> needFilteredShopIds = canceledDetails.stream().map(AIBookDetailEntity::getShopId).collect(Collectors.toList());
        return shopIds.stream().filter(shopId -> !needFilteredShopIds.contains(shopId)).collect(Collectors.toList());
    }

    private List<Long> filterShopByBlackList(UserBookRequest request, List<Long> shopIds, Map<Long, Long> shopIdMap) {
        if (CollectionUtils.isEmpty(lionConfigUtil.getDispatchConfigData().getDpShopIdBlackList())) {
            return shopIds;
        }
        List<Long> dpShopIdBlackList = lionConfigUtil.getDispatchConfigData().getDpShopIdBlackList();
        return shopIds.stream().filter(shopId -> {
            long dpShopId = shopId;
            if (request.getPlatform() == PlatformEnum.MT.getType()) {
                dpShopId = shopIdMap.getOrDefault(shopId, 0L);
            }
            return !dpShopIdBlackList.contains(dpShopId);
        }).collect(Collectors.toList());
    }

    private List<Long> filterShopByPhoneBlackList(UserBookRequest request, List<Long> shopIds, Map<Long, Long> shopIdMap) {
        return shopIds.stream().filter(shopId -> {
            long mtShopId = shopId;
            if (request.getPlatform() == PlatformEnum.DP.getType()) {
                mtShopId = shopIdMap.getOrDefault(shopId, 0L);
            }
            StoreKey shopPhoneBlackListKey = new StoreKey(AIBookConstants.AI_BOOK_SHOP_PHONE_BLACK_LIST, mtShopId);
            return !redisStoreClient.exists(shopPhoneBlackListKey);
        }).collect(Collectors.toList());
    }

    private List<Long> filterShopByStock(UserBookRequest request, List<Long> shopIds, Map<Long, Long> shopIdMap) {
        return shopIds.stream().filter(shopId -> {
            long mtShopId = shopId;
            if (request.getPlatform() == PlatformEnum.DP.getType()) {
                mtShopId = shopIdMap.getOrDefault(shopId, 0L);
            }
            return isShopHasStock(mtShopId, request.getBookStartTime(), request.getBookEndTime());
        }).collect(Collectors.toList());
    }

    private Map<Long, Long> getShopIdMap(List<Long> shopIds, int platform) {
        if (platform == PlatformEnum.MT.getType()) {
            return shopAclService.queryDpIdLByMtIdL(shopIds).join();
        }
        return shopAclService.queryMtIdLByDpIdL(shopIds).join();
    }

    private List<Long> filterShopByPhone(UserBookRequest request, List<Long> shopIds, Map<Long, Long> shopIdMap) {
        List<Long> dpShopIds = shopIds;

        if (request.getPlatform() == PlatformEnum.MT.getType()) {
            dpShopIds = shopIds.stream().map(mtShopId -> shopIdMap.getOrDefault(mtShopId, 0L)).filter(dpShopId -> dpShopId > 0).collect(Collectors.toList());
        }
        Map<Long, List<PhoneDTO>> dpShopId2PhoneMap = shopAclService.batchGetShopPhone(dpShopIds);

        // 返回的request中平台对应的shopId类型
        return shopIds.stream().filter(shopId -> {
            long dpShopId = shopId;
            if (request.getPlatform() == PlatformEnum.MT.getType()) {
                dpShopId = shopIdMap.getOrDefault(shopId, 0L);
            }
            List<PhoneDTO> phoneDTOS = dpShopId2PhoneMap.get(dpShopId);
            return CollectionUtils.isNotEmpty(phoneDTOS) && !isAllPhone400(phoneDTOS);
        }).collect(Collectors.toList());
    }

    private boolean isAllPhone400(List<PhoneDTO> phoneDTOs) {
        return phoneDTOs.stream().allMatch(phoneDTO -> phoneDTO.getClickPhone().startsWith("400"));
    }

    private List<Long> filterShopByBizTime(UserBookRequest request, List<Long> shopIds, Map<Long, Long> shopIdMap) {
        // 计算出预约时间段内所有日期和半小时粒度索引
        Map<String, List<String>> dateKey2HalfHourIndex = getBetweenTimeHalfHourIndex(request.getBookStartTime(), request.getBookEndTime());
        if (MapUtils.isEmpty(dateKey2HalfHourIndex)) {
            return shopIds;
        }
        // 判断门店在预约时间内是否开门
        return shopIds.stream().filter(shopId -> {
            long dpShopId = shopId;
            if (request.getPlatform() == PlatformEnum.MT.getType()) {
                dpShopId = shopIdMap.getOrDefault(shopId, 0L);
            }
            return isShopBizTimeOpen(dpShopId, dateKey2HalfHourIndex);
        }).collect(Collectors.toList());
    }

    private boolean isShopBizTimeOpen(long dpShopId, Map<String, List<String>> dateKey2HalfHourIndex) {
        for (Map.Entry<String, List<String>> entry : dateKey2HalfHourIndex.entrySet()) {
            String dateKey = entry.getKey();
            List<String> halfHourIndex = entry.getValue();
            String dateStr = DateUtils.parseDateKey2DateStr(dateKey);
            if (StringUtils.isEmpty(dateStr)) {
                continue;
            }
            BizForecastDTO bizForecast = shopAclService.getBizForecast(dpShopId, dateStr);
            if (bizForecast == null || StringUtils.isEmpty(bizForecast.getToday())) {
                continue;
            }
            if (isShopCurrentDayOpen(bizForecast, halfHourIndex)) {
                return true;
            }
        }
        return false;
    }


    private boolean isShopCurrentDayOpen(BizForecastDTO bizForecast, List<String> halfHourIndex) {

        String today = bizForecast.getToday();
        for (String halfHour : halfHourIndex) {
            int halfHourNum = NumberUtils.toInt(halfHour);
            if (halfHourNum < 0) {
                continue;
            }
            if (today.charAt(halfHourNum) == '1') {
                return true;
            }
        }
        return false;
    }


    private List<Long> filterShopByBusinessStatus(UserBookRequest request, List<Long> shopIds, Map<Long, Long> shopIdMap) {
        List<Long> mtShopIds = shopIds;
        if (request.getPlatform() == PlatformEnum.DP.getType()) {
            mtShopIds = shopIds.stream().map(dpShopId -> shopIdMap.getOrDefault(dpShopId, 0L)).filter(mtShopId -> mtShopId > 0).collect(Collectors.toList());
        }

        Map<Long, MtPoiDTO> mtShopInfos = shopAclService.batchGetMtShopInfo(mtShopIds);

        return shopIds.stream().filter(shopId -> {
            long mtShopId = shopId;
            if (request.getPlatform() == PlatformEnum.DP.getType()) {
                mtShopId = shopIdMap.getOrDefault(shopId, 0L);
            }
            MtPoiDTO mtPoiDTO = mtShopInfos.get(mtShopId);
            return isShopCateBizMatch(mtPoiDTO);
        }).collect(Collectors.toList());
    }


    private boolean isShopCateBizMatch(MtPoiDTO mtPoiDTO) {
        if (mtPoiDTO == null) {
            return false;
        }
        if (mtPoiDTO.getCloseStatus() != 0) {
            return false;
        }
        List<TypeHierarchyView> typeHierarchy = mtPoiDTO.getTypeHierarchy();
        TypeHierarchyView typeHierarchyView = CollectionUtils.isEmpty(typeHierarchy) ? null :
                typeHierarchy.get(typeHierarchy.size() - 2);
        if (typeHierarchyView == null) {
            return false;
        }
        RecommendShopConfigData recommendShopConfigData = lionConfigUtil.getRecommendShopConfigData();
        if (CollectionUtils.isNotEmpty(recommendShopConfigData.getRecallShopBackSecCateIds()) &&
                !recommendShopConfigData.getRecallShopBackSecCateIds().contains(typeHierarchyView.getId())) {
            return false;
        }
        return true;
    }

    private RecommendParameters buildRecommendParameters(UserBookRequest request, int pageNo, int pageSize) {
        RecommendParameters recommendParameters = new RecommendParameters();
        recommendParameters.setBizId(449); //商户推荐
        recommendParameters.setCityId(request.getCityId()); //城市id
        recommendParameters.setOriginUserId(String.valueOf(request.getUserId())); // 用户Id字符串，缺失传入null
        recommendParameters.setPlatformEnum(request.getPlatform() == PlatformEnum.DP.getType() ?
                com.dianping.martgeneral.recommend.api.enums.PlatformEnum.DP : com.dianping.martgeneral.recommend.api.enums.PlatformEnum.MT);
        recommendParameters.setLat(request.getLat());
        recommendParameters.setLng(request.getLng());
        recommendParameters.setPageSize(pageSize);
        recommendParameters.setPageNumber(pageNo);
        if (request.getPlatform() == PlatformEnum.MT.getType()) {
            recommendParameters.setUuid(request.getDeviceId()); //美团侧需要uuid
        } else {
            recommendParameters.setDpid(request.getDeviceId());
        }

        // 接口协议：https://km.sankuai.com/page/**********
        Map<String, Object> bizParams = new HashMap<>();
        bizParams.put("flowFlag", "001");
        bizParams.put("productTagIds", Optional.ofNullable(IntentProjectCodeEnum.getByCode(request.getBookType()).getTags()).orElse(IntentProjectCodeEnum.HAIRDRESSING.getTags()));

        // 填充召回位置参数
        paddingBizParamsPosition(request, bizParams);

        // 填充召回价格参数
        paddingBizParamsPrice(request, bizParams);

        recommendParameters.setBizParams(bizParams);
        return recommendParameters;
    }

    private void paddingBizParamsPosition(UserBookRequest request, Map<String, Object> bizParams) {
        if (CollectionUtils.isNotEmpty(request.getDistrictId())) {
            bizParams.put("districtId", request.getDistrictId().stream().map(String::valueOf).collect(Collectors.joining(",")));
        }
        if (CollectionUtils.isNotEmpty(request.getRegionId())) {
            bizParams.put("regionId", request.getRegionId().stream().map(String::valueOf).collect(Collectors.joining(",")));
        }
        if (CollectionUtils.isNotEmpty(request.getStationId())) {
            bizParams.put("stationId", request.getStationId().stream().map(String::valueOf).collect(Collectors.joining(",")));
        }
        if (CollectionUtils.isNotEmpty(request.getStationLine())) {
            bizParams.put("stationLine", request.getStationLine().stream().map(String::valueOf).collect(Collectors.joining(",")));
        }
        if (StringUtils.isNotEmpty(request.getDistanceRange())) {
            bizParams.put("distanceRange", request.getDistanceRange());
        }
    }

    private void paddingBizParamsPrice(UserBookRequest request, Map<String, Object> bizParams) {
        List<Long> shopMaleHairTagIds = lionConfigUtil.getRecommendShopConfigData().getShopMaleHairTagIds();
        if (request.getUserGender() == Gender.FEMALE.getValue() && CollectionUtils.isNotEmpty(shopMaleHairTagIds)) {
            //女性用户不召回男士理发店
            bizParams.put("blackZdcTagIds", StringUtils.join(shopMaleHairTagIds, ","));
        }

        if (request.getLowerPrice() != null) {
            // 按项目传入供给价格标签
            bizParams.put("commTags", buildHairCommTag(request));
        }
    }

    private String buildHairCommTag(UserBookRequest request) {
        if (request.getLowerPrice() == null) {
            return StringUtils.EMPTY;
        }
        String productTagIds = Optional.ofNullable(IntentProjectCodeEnum.getByCode(request.getBookType()).getTags())
                .orElse(IntentProjectCodeEnum.HAIRDRESSING.getTags());
        String[] split = StringUtils.split(productTagIds, ',');
        List<String> priceTags = Lists.newArrayList();
        for (String tagId : split) {
            priceTags.add("innerpg_" + tagId + "_" + request.getLowerPrice());
        }
        return StringUtils.join(priceTags, ",");
    }

    private void dispatchInternal(boolean needDelay, long taskId) {
        ZebraForceMasterHelper.forceMasterInLocalContext();
        AIBookTaskEntity taskEntity = aiBookTaskDAO.getById(taskId);
        if (taskEntity == null) {
            return;
        }
        if (taskEntity.getTaskStatus() != TaskStatusEnum.HAS_TASK_ASKING.getCode() &&
                taskEntity.getTaskStatus() != TaskStatusEnum.HAS_TASK_DELAY.getCode()) {
            //任务非进行中状态，不需要调度
            return;
        }
        if (new Date().after(taskEntity.getBookEndTime())) {
            // 预约时间是否已经过期，过期直接预约失败
            aiBookTaskDAO.updateTaskStatus(taskId, TaskStatusEnum.BOOK_FAILED.getCode());
            return;
        }

        // 延时发起
        if (needDelay) {
            // 发送延时消息
            aiBookTaskDAO.updateTaskStatus(taskId, TaskStatusEnum.HAS_TASK_DELAY.getCode());
            long delaySeconds = TimeUtil.getDifSecond(getTargetDispatchTime()) + new Random().nextInt(lionConfigUtil.getDispatchConfigData().getDispatchInterval());
            ProducerUtils.sendDelayMessage(aiBookDispatchDelayProducer, AIBookDispatchDelayData.builder().taskId(taskId).build(),
                    delaySeconds * 1000, "aiBookDispatchDelayProducer");
            return;
        }
        // 发起第一个门店外呼
        executeTaskCall(taskId);
        ZebraForceMasterHelper.clearLocalContext();
    }


    private boolean isShopCanBookCall(long shopId, int platform, Date bookStartTime, Date bookEndTime) {

        long dpShopId = getDpShopId(shopId, platform);
        long mtShopId = getMtShopId(shopId, platform);
        DispatchConfigData dispatchConfigData = lionConfigUtil.getDispatchConfigData();

        if (!checkBlackList(shopId, mtShopId, dpShopId)) {
            return false;
        }

        // 门店有电话
        List<String> shopPhone = shopAclService.getShopPhone(dpShopId);
        if (CollectionUtils.isEmpty(shopPhone) || shopPhone.stream().allMatch(phone -> phone.startsWith("400"))) {
            Cat.logEvent("isShopCanBookCall", "ShopNoPhone_" + shopId);
            return false;
        }
        // 门店外呼次数
        StoreKey shopCntKey = new StoreKey(AIBookConstants.AI_BOOK_SHOP_CNT_CATEGORY, mtShopId, DateUtils.covertDateNumStr(new Date()));
        Object storeVal = redisStoreClient.get(shopCntKey);
        long shopCnt = storeVal == null ? 0L : NumberUtils.toLong(storeVal.toString());
        if (shopCnt >= dispatchConfigData.getShopBookCntLimit()) {
            Cat.logEvent("isShopCanBookCall", "ShopCntLimit_" + shopId);
            return false;
        }

        // 门店锁
        StoreKey shopLockKey = new StoreKey(AIBookConstants.AI_BOOK_SHOP_LOCK_CATEGORY, mtShopId);
        if (redisStoreClient.exists(shopLockKey)) {
            Cat.logEvent("isShopCanBookCall", "ShopLock_" + shopId);
            return false;
        }

        // 门店库存
        if (!isShopHasStock(mtShopId, bookStartTime, bookEndTime)) {
            Cat.logEvent("isShopCanBookCall", "ShopNoStock_" + shopId);
            return false;
        }

        return true;
    }

    private boolean checkBlackList(long shopId, long mtShopId, long dpShopId) {
        // 门店黑名单
        DispatchConfigData dispatchConfigData = lionConfigUtil.getDispatchConfigData();
        if (CollectionUtils.isNotEmpty(dispatchConfigData.getDpShopIdBlackList()) &&
                dispatchConfigData.getDpShopIdBlackList().contains(dpShopId)) {
            Cat.logEvent("isShopCanBookCall", "ShopInBlackList" + shopId);
            return false;
        }

        // 门店手机号是否在黑名单中
        StoreKey shopPhoneBlackList = new StoreKey(AIBookConstants.AI_BOOK_SHOP_PHONE_BLACK_LIST, mtShopId);
        if (redisStoreClient.exists(shopPhoneBlackList)) {
            Cat.logEvent("isShopCanBookCall", "ShopPhoneInBlackList" + shopId);
            return false;
        }

        // 门店是否在天维度黑名单中
        StoreKey shopDailyBlackList = new StoreKey(AIBookConstants.AI_BOOK_SHOP_DAILY_BLACK_LIST, mtShopId, DateUtils.covertDateNumStr(new Date()));
        if (redisStoreClient.exists(shopDailyBlackList)) {
            Cat.logEvent("isShopCanBookCall", "ShopInDailyBlackList" + shopId);
            return false;
        }
        return true;
    }

    private boolean isShopHasStock(long mtShopId, Date bookStartTime, Date bookEndTime) {
        Map<String, List<String>> betweenTimeHalfHourIndex = getBetweenTimeHalfHourIndex(bookStartTime, bookEndTime);
        boolean isShopHasStock = false;
        for (Map.Entry<String, List<String>> entry : betweenTimeHalfHourIndex.entrySet()) {
            String dateKey = entry.getKey();
            List<String> halfHourIndex = entry.getValue();
            StoreKey stockKey = new StoreKey(AIBookConstants.AI_BOOK_SHOP_STOCK_CATEGORY, mtShopId, dateKey);
            Map<String, Integer> dateStock = redisStoreClient.hgetAll(stockKey);
            boolean hasStock = halfHourIndex.stream().anyMatch(index -> dateStock.getOrDefault(index, 1) > 0);
            if (hasStock) {
                isShopHasStock = true;
                break;
            }
        }
        return isShopHasStock;
    }


    private Date getTargetDispatchTime() {
        int hourOfDay = LocalDateTime.now().getHourOfDay();
        DispatchConfigData dispatchConfigData = lionConfigUtil.getDispatchConfigData();
        if (hourOfDay >= dispatchConfigData.getDispatchEndHour()) {
            return LocalDateTime.now().plusDays(1).withHourOfDay(dispatchConfigData.getDispatchBeginHour()).withMinuteOfHour(0).toDate();
        }
        return LocalDateTime.now().withHourOfDay(dispatchConfigData.getDispatchBeginHour()).withMinuteOfHour(0).toDate();
    }

    private boolean getCurrentTimeNeedDelay() {
        int hourOfDay = LocalDateTime.now().getHourOfDay();
        DispatchConfigData dispatchConfigData = lionConfigUtil.getDispatchConfigData();
        return hourOfDay >= dispatchConfigData.getDispatchEndHour() || hourOfDay < dispatchConfigData.getDispatchBeginHour();
    }

    private AIBookTaskEntity buildAIBookTaskEntity(UserBookRequest request, int taskStatus) {
        AIBookTaskEntity entity = new AIBookTaskEntity();
        entity.setUserId(request.getUserId());
        entity.setShopId(request.getShopId());
        entity.setPlatform(request.getPlatform());
        entity.setBookType(request.getBookType());
        entity.setBookStartTime(request.getBookStartTime());
        entity.setBookEndTime(request.getBookEndTime());
        entity.setTaskStatus(taskStatus);
        entity.setUserPhone(tokenAccessAclService.getTokenMobile(request.getUserPhone()));
        entity.setPosition(request.getPosition());
        entity.setRecommendSwitch(request.getRecommendSwitch());
        entity.setBookPeriodDesc(request.getTimePeriodDesc());
        entity.setStatus(1);
        entity.setLng(String.valueOf(request.getLng()));
        entity.setLat(String.valueOf(request.getLat()));
        return entity;
    }


    private List<AIBookDetailEntity> buildTaskDetails(UserBookRequest request, List<Long> shopIds) {
        List<AIBookDetailEntity> details = Lists.newArrayList();
        for (Long shopId : shopIds) {
            AIBookDetailEntity detail = new AIBookDetailEntity();
            detail.setPlatform(request.getPlatform());
            detail.setShopId(shopId);
            detail.setCallPhone(StringUtils.EMPTY);
            detail.setCallResult(TaskCallResultEnum.INITIAL.getCode());
            detail.setBookLeadsId(0L);
            detail.setStatus(1);
            detail.setExtraData(StringUtils.EMPTY);
            details.add(detail);
        }
        return details;
    }


    private void processCallbackData(BookCallbackReq req, AIBookTaskEntity taskEntity, AIBookDetailEntity detailEntity) {
        try {
            // 释放门店锁
            long mtShopId = getMtShopId(detailEntity.getShopId(), detailEntity.getPlatform());
            long mtUserId = getMtUserId(taskEntity.getPlatform(), taskEntity.getUserId());
            StoreKey shopLockKey = new StoreKey(AIBookConstants.AI_BOOK_SHOP_LOCK_CATEGORY, mtShopId);
            redisStoreClient.delete(shopLockKey);

            // 构建外呼结果
            int detailCallResult = buildCallResult(req);
            if (detailCallResult == TaskCallResultEnum.BOOK_SUCCESS.getCode()) {
                // 预约成功处理
                processBookSuccess(req, taskEntity, detailEntity, detailCallResult, mtUserId);
                return;
            }

            if (detailCallResult == TaskCallResultEnum.BOOK_FAILED.getCode()) {
                // 预约失败处理
                processBookFailed(req, taskEntity, detailEntity, detailCallResult);
                return;
            }
            // 取消成功 或者 取消失败，只做结果记录
            aiBookDetailDAO.updateCallResult(detailEntity.getId(), detailCallResult);
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("processCallbackData").build(),
                    new WarnMessage("processCallbackData", "处理回调数据异常", "系统异常"), req, e);
        }
    }


    private void processBookSuccess(BookCallbackReq req, AIBookTaskEntity taskEntity, AIBookDetailEntity
            detailEntity, int detailCallResult, long mtUserId) {

        if (taskEntity.getTaskStatus() == TaskStatusEnum.CANCEL_INTERUPT.getCode()) {
            //任务已手动取消
            AIBookTaskE aiBookTaskE = aiBookTaskRepositoryService.buildAIBookTaskE(taskEntity, detailEntity);
            aiBookTaskE.setBookTime(new Date(req.getBookingTimestamp() * 1000));
            aiBookTaskE.getDetails().get(0).setBookTime(new Date(req.getBookingTimestamp() * 1000));
            // 执行取消外呼，并更新任务状态
            executeCancel(0L, aiBookTaskE);
            return;
        }

        // 预约成功则创建预约
        long bookLeadsId = Optional.ofNullable(leadsAclService.createBook(buildCreateBookReq(req, taskEntity, detailEntity))).orElse(0L);
        Date bookTime = req.getBookingTimestamp() != null ? new Date(req.getBookingTimestamp() * 1000) : null;

        // 记录用户预约次数
        StoreKey userBookCnt = new StoreKey(AIBookConstants.AI_BOOK_USER_DISPATCH_CNT_CATEGORY, mtUserId, DateUtils.covertDateNumStr(new Date()));
        redisStoreClient.incrBy(userBookCnt, 1, 24 * 60 * 60);

        // 更新 detail 记录
        AIBookDetailExtraData extraData = Optional.ofNullable(JsonCodec.decode(detailEntity.getExtraData(), AIBookDetailExtraData.class)).orElse(new AIBookDetailExtraData());
        extraData.setShopNote(StringUtils.isEmpty(req.getReceiptMessage()) ? StringUtils.EMPTY : req.getReceiptMessage());
        extraData.setReleaseReason(StringUtils.isEmpty(req.getReleaseReason()) ? StringUtils.EMPTY : req.getReleaseReason());
        extraData.setHistoryMessages(CollectionUtils.isEmpty(req.getHistoryMessageList()) ? Lists.newArrayList() : req.getHistoryMessageList());
        extraData.setCallBackBookResult(req.getCallResult());
        boolean updateResult = aiBookTaskRepositoryService.updateBookSuccess(taskEntity, detailEntity, detailCallResult, extraData, bookLeadsId, bookTime);

        // 发送预约成功小助手消息
        aiBookAssistantProcessService.sendBookResultAssistantReply(taskEntity.getPlatform(), taskEntity.getUserId(), true);

        LogUtils.logReturnInfo(log, TagContext.builder().action("processBookSuccess").build(), WarnMessage.build("processBookSuccess",
                "预约成功更新回调数据", ""), Lists.newArrayList(detailEntity.getId(), detailCallResult, JsonCodec.encodeWithUTF8(extraData),
                bookLeadsId, bookTime), updateResult);
    }


    private void processBookFailed(BookCallbackReq req, AIBookTaskEntity taskEntity, AIBookDetailEntity detailEntity, int detailCallResult) {

        if (req.getCallResult() == BookResultEnum.NOT_CONNECTED.getCode() &&
                taskEntity.getRecommendSwitch() == 0 && !TaskStatusEnum.isBookCallEnd(taskEntity.getTaskStatus())) {
            // 未接通-可重拨，发起延时消息，待后续重拨
            StoreKey timesKey = new StoreKey(AIBookConstants.AI_BOOK_SHOP_CALL_AGAIN_TIME, detailEntity.getId());
            Object timesVal = redisStoreClient.get(timesKey);
            int times = timesVal == null ? 0 : NumberUtils.toInt(timesVal.toString());
            int shopCallAgainTimeLimit = lionConfigUtil.getDispatchConfigData().getShopCallAgainTimeLimit();
            if (times < shopCallAgainTimeLimit) {
                ProducerUtils.sendDelayMessage(aiBookCallAgainDelayProducer, AIBookCallAgainDelayData.builder()
                                .taskId(taskEntity.getId()).detailId(detailEntity.getId()).build(),
                        lionConfigUtil.getDispatchConfigData().getCallAgainDelaySeconds() * 1000L,
                        "aiBookCallAgainDelayProducer");
                return;
            }
        }

        // 根据回调结果处理黑名单
        processShopBlackList(req, taskEntity, detailEntity);

        // 失败信息记录
        AIBookDetailExtraData extraData = Optional.ofNullable(JsonCodec.decode(detailEntity.getExtraData(),
                AIBookDetailExtraData.class)).orElse(new AIBookDetailExtraData());
        extraData.setReleaseReason(StringUtils.isEmpty(req.getReleaseReason()) ? StringUtils.EMPTY : req.getReleaseReason());
        extraData.setHistoryMessages(CollectionUtils.isEmpty(req.getHistoryMessageList()) ?
                extraData.getHistoryMessages() : req.getHistoryMessageList());
        extraData.setCallBackBookResult(req.getCallResult());
        int updateResult = aiBookDetailDAO.updateCallback(detailEntity.getId(), detailCallResult, JsonCodec.encodeWithUTF8(extraData), new Date(),
                0L, null);
        LogUtils.logReturnInfo(log, TagContext.builder().action("processBookFailed").build(), WarnMessage.build("processBookFailed",
                "预约失败更新回调数据", ""), Lists.newArrayList(req, detailEntity.getId(), detailCallResult), updateResult);

        // 门店库存信息记录
        if (req.getCallResult() != BookResultEnum.BOOK_FAIL_NO_SUPPLY.getCode()) {
            return;
        }
        processShopStock(req, taskEntity, detailEntity);
    }

    private void processShopStock(BookCallbackReq req, AIBookTaskEntity taskEntity, AIBookDetailEntity detailEntity) {
        Date bookStartTime = taskEntity.getBookStartTime();
        Date bookEndTime = taskEntity.getBookEndTime();
        long mtShopId = getMtShopId(detailEntity.getShopId(), detailEntity.getPlatform());
        Map<String, Map<String, Integer>> dateKey2HalfHourStock = buildBookFailedShopStockTimeKey(bookStartTime, bookEndTime);
        for (Map.Entry<String, Map<String, Integer>> entry : dateKey2HalfHourStock.entrySet()) {
            String dateKey = entry.getKey();
            Map<String, Integer> halfHourStock = entry.getValue();
            StoreKey shopStockKey = new StoreKey(AIBookConstants.AI_BOOK_SHOP_STOCK_CATEGORY, mtShopId, dateKey);
            redisStoreClient.hmset(shopStockKey, halfHourStock);
            redisStoreClient.expire(shopStockKey, 72 * 60 * 60);
        }
    }

    private void processShopBlackList(BookCallbackReq req, AIBookTaskEntity taskEntity, AIBookDetailEntity detailEntity) {
        // 7天黑名单添加
        if (req.getCallResult() == BookResultEnum.NOT_CONNECTED_BALCKLIST.getCode() ||
                req.getCallResult() == BookResultEnum.CONNECTED_BLACKLIST_EMO.getCode()) {
            // 未接通-拉黑，将门店手机号拉黑，后续不再拨打
            addShopPhoneBlackList(detailEntity.getPlatform(), detailEntity.getShopId(), detailEntity.getCallPhone());
        }

        // 单日黑名单添加
        if (req.getCallResult() == BookResultEnum.CONNECTED_BLACKLIST_REJECT.getCode() ||
                callbackDurationTooShort(detailEntity)) {
            addShopDailyBlackList(detailEntity.getPlatform(), detailEntity.getShopId());
        }
    }

    private void addShopPhoneBlackList(int platform, long shopId, String shopPhoneToken) {
        try {
            long mtShopId = getMtShopId(shopId, platform);
            String shopPhone = checkIsPhone(shopPhoneToken) ? shopPhoneToken : tokenAccessAclService.getPlainMobile(shopPhoneToken);
            StoreKey shopPhoneBlackList = new StoreKey(AIBookConstants.AI_BOOK_SHOP_PHONE_BLACK_LIST, mtShopId);
            redisStoreClient.sadd(shopPhoneBlackList, shopPhone, 7 * 24 * 60 * 60);
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("addShopPhoneBlackList").build(),
                    new WarnMessage("addShopPhoneBlackList", "添加门店手机号黑名单异常", ""),
                    Lists.newArrayList(platform, shopId, shopPhoneToken), e);
        }
    }


    private void addShopDailyBlackList(int platform, long shopId) {
        try {
            long mtShopId = getMtShopId(shopId, platform);
            StoreKey shopDailyBlackList = new StoreKey(AIBookConstants.AI_BOOK_SHOP_DAILY_BLACK_LIST, mtShopId, DateUtils.covertDateNumStr(new Date()));
            redisStoreClient.set(shopDailyBlackList, "1", 24 * 60 * 60);
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("addShopDailyBlackList").build(),
                    new WarnMessage("addShopDailyBlackList", "添加门店天维度黑名单异常", ""),
                    Lists.newArrayList(platform, shopId), e);
        }
    }


    private boolean callbackDurationTooShort(AIBookDetailEntity detailEntity) {
        if (detailEntity == null || detailEntity.getCallStartTime() == null) {
            return false;
        }
        long currentTime = System.currentTimeMillis();
        long callStartTime = detailEntity.getCallStartTime().getTime();
        return currentTime - callStartTime < 1000 * 20;
    }

    private Map<String, Map<String, Integer>> buildBookFailedShopStockTimeKey(Date bookStartTime, Date bookEndTime) {
        Map<String, Map<String, Integer>> result = Maps.newHashMap();
        Map<String, List<String>> betweenTimeHalfHourIndex = getBetweenTimeHalfHourIndex(bookStartTime, bookEndTime);
        for (Map.Entry<String, List<String>> entry : betweenTimeHalfHourIndex.entrySet()) {
            String dateKey = entry.getKey();
            List<String> halfHourIndex = entry.getValue();
            Map<String, Integer> halfHourStock = Maps.newHashMap();
            for (String index : halfHourIndex) {
                // 预约失败无供给记录为0-不可约，没有则默认为可约
                halfHourStock.put(index, 0);
            }
            result.put(dateKey, halfHourStock);
        }
        return result;
    }

    private Map<String, List<String>> getBetweenTimeHalfHourIndex(Date bookStartTime, Date bookEndTime) {
        Map<String, List<String>> result = Maps.newHashMap();
        // 半小时对应的毫秒数
        long oneHalfHourInMillis = 30 * 60 * 1000L;
        for (long currentTime = bookStartTime.getTime(); currentTime < bookEndTime.getTime(); currentTime += oneHalfHourInMillis) {
            Date currentDate = new Date(currentTime);
            String dateKey = DateUtils.covertDateNumStr(currentDate);
            List<String> currentDayIndex = result.getOrDefault(dateKey, Lists.newArrayList());
            currentDayIndex.add(String.valueOf(getHalfHourIndex(currentDate)));
            result.put(dateKey, currentDayIndex);
        }
        return result;
    }

    private int getHalfHourIndex(Date date) {
        LocalDateTime currentLocalDateTime = new LocalDateTime(date, DateTimeZone.getDefault());
        int hour = currentLocalDateTime.getHourOfDay();
        int minute = currentLocalDateTime.getMinuteOfHour();
        return hour * 2 + (minute >= 30 ? 1 : 0);
    }


    private CreateBookReq buildCreateBookReq(BookCallbackReq request, AIBookTaskEntity
            taskEntity, AIBookDetailEntity detailEntity) {
        DispatchConfigData dispatchConfigData = lionConfigUtil.getDispatchConfigData();
        CreateBookReq req = new CreateBookReq();
        req.setUserId(taskEntity.getUserId());
        req.setPlatform(taskEntity.getPlatform());
        req.setShopId(detailEntity.getShopId());
        req.setBookProject(Optional.ofNullable(IntentProjectCodeEnum.getByCode(taskEntity.getBookType()))
                .orElse(IntentProjectCodeEnum.HAIRDRESSING));
        req.setUserPhone(tokenAccessAclService.getPlainMobile(taskEntity.getUserPhone()));
        req.setMerchantRemark(StringUtils.isEmpty(request.getReceiptMessage()) ? StringUtils.EMPTY : request.getReceiptMessage());
        req.setBookTime(request.getBookingTimestamp() == null ? 0L : request.getBookingTimestamp() * 1000);
        req.setBizSourceId(dispatchConfigData.getBookLeadsBizSourceId());
        req.setMerchantPhone(tokenAccessAclService.getPlainMobile(detailEntity.getCallPhone()));
        return req;
    }


    private int buildCallResult(BookCallbackReq req) {

        if (isCallResultSuccess(req)) {
            return TaskCallResultEnum.BOOK_SUCCESS.getCode();
        }
        if (req.getBizType() == AI_BOOK_CALL_BACK_BIZ_TYPE_BOOK && req.getCallResult() == BookResultEnum.CALL_BACK_TIMEOUT.getCode()) {
            return TaskCallResultEnum.TIMEOUT_CALL_BACK.getCode();
        }
        if (req.getBizType() == AI_BOOK_CALL_BACK_BIZ_TYPE_CANCEL) {
            return req.getCallResult() == BookResultEnum.CANCEL_SUCCESS.getCode() ?
                    TaskCallResultEnum.CANCEL_SUCCESS.getCode() : TaskCallResultEnum.CANCEL_FAILED.getCode();
        }
        return TaskCallResultEnum.BOOK_FAILED.getCode();
    }

    private boolean isCallResultSuccess(BookCallbackReq req) {
        return req.getBizType() == AI_BOOK_CALL_BACK_BIZ_TYPE_BOOK && req.getCallResult() == BookResultEnum.BOOK_SUCCESS.getCode() &&
                req.getBookingTimestamp() != null && req.getBookingTimestamp() > 0;
    }

    private boolean executeCancel(Long leadsId, AIBookTaskE aiBookTaskE) {
        long mtShopId = getMtShopId(aiBookTaskE.getDetails().get(0).getShopId(), aiBookTaskE.getDetails().get(0).getPlatform());

        // 是否能取消
        if (!canCancel(aiBookTaskE, mtShopId)) {
            LogUtils.logFailLog(log, TagContext.builder().action("executeCancel").bizId(String.valueOf(leadsId)).build(),
                    new WarnMessage("AIBookDispatchProcessService:dispatchCancelTask", "门店正在外呼中", "直接取消"), leadsId, null);
            aiBookTaskE.getDetails().get(0).setCallResult(TaskCallResultEnum.CANCEL_SKIP.getCode());
            aiBookTaskE.setTaskStatus(aiBookTaskE.getTaskStatus() == TaskStatusEnum.CANCEL_INTERUPT.getCode() ?
                    TaskStatusEnum.CANCEL_INTERUPT.getCode() : TaskStatusEnum.CANCEL.getCode());
            return aiBookTaskRepositoryService.cancelAIBookTask(aiBookTaskE);
        }

        // 取消外呼拨打
        String contactId = callCancelBook(aiBookTaskE, mtShopId);

        // 记录状态
        boolean success = updateCancelStatus(aiBookTaskE, contactId);
        LogUtils.logReturnInfo(log, TagContext.builder().action("executeCancel").bizId(String.valueOf(leadsId)).build(),
                new WarnMessage("AIBookDispatchProcessService:dispatchCancelTask", "取消预约结果", ""), leadsId, success);
        return success;
    }

    private boolean canCancel(AIBookTaskE aiBookTaskE, long mtShopId) {
        if (new Date().after(aiBookTaskE.getBookTime())) {
            return false;
        }
        StoreKey shopLockKey = new StoreKey(AIBookConstants.AI_BOOK_SHOP_LOCK_CATEGORY, mtShopId);
        if (redisStoreClient.exists(shopLockKey)) {
            return false;
        }

        if (getCurrentTimeNeedDelay()) {
            // 夜间不打取消外呼
            return false;
        }
        return true;
    }

    private boolean updateCancelStatus(AIBookTaskE aiBookTaskE, String contactId) {
        aiBookTaskE.setTaskStatus(aiBookTaskE.getTaskStatus() == TaskStatusEnum.CANCEL_INTERUPT.getCode() ?
                TaskStatusEnum.CANCEL_INTERUPT.getCode() : TaskStatusEnum.CANCEL.getCode());
        aiBookTaskE.getDetails().get(0).setCallResult(TaskCallResultEnum.CANCEL_SUCCESS.getCode());
        String extraDataStr = aiBookTaskE.getDetails().get(0).getExtraData();
        AIBookDetailExtraData aiBookDetailExtraData = null;
        if (StringUtils.isNotEmpty(extraDataStr)) {
            aiBookDetailExtraData = JsonCodec.decode(extraDataStr, AIBookDetailExtraData.class);
            if (aiBookDetailExtraData != null) {
                aiBookDetailExtraData.setCancelContactId(contactId);
            }
        }
        if (aiBookDetailExtraData == null) {
            aiBookDetailExtraData = AIBookDetailExtraData.builder().cancelContactId(contactId).build();
        }
        aiBookTaskE.getDetails().get(0).setExtraData(JsonCodec.encodeWithUTF8(aiBookDetailExtraData));
        return aiBookTaskRepositoryService.cancelAIBookTask(aiBookTaskE);
    }

    private String callCancelBook(AIBookTaskE aiBookTaskE, long mtShopId) {
        // 查询门店、用户数据
        MtPoiDTO mtShopInfo = shopAclService.getMtShopInfo(mtShopId);
        String shopName = buildShopName(mtShopInfo);
        String shopMobile = tokenAccessAclService.getPlainMobile(aiBookTaskE.getDetails().get(0).getCallPhone());
        String userMobile = tokenAccessAclService.getPlainMobile(aiBookTaskE.getUserPhone());
        long mtUserId = getMtUserId(aiBookTaskE.getPlatform(), aiBookTaskE.getUserId());
        int gender = queryUserGender(mtUserId);

        // 外呼拨打
        return aiBookCallAclService.sendHairstylingOutboundCall(buildCancelCallRequest(shopName, shopMobile,
                gender, userMobile, aiBookTaskE)).getRight();
    }

    private OutboundCallRequest buildCancelCallRequest(String shopName, String shopMobile, int gender, String userPlainPhone, AIBookTaskE task) {
        OutboundCallRequest request = new OutboundCallRequest();
        request.setMerchantName(shopName);
        request.setPhoneNumber(shopMobile);
        //美团用户中心 男-1 女-2
        request.setBookingUserGender(gender == Gender.OTHER.getValue() ? Gender.MALE : Gender.findByValue(gender));
        request.setBookingPhoneNumber(userPlainPhone);
        request.setBookingType(Optional.ofNullable(IntentProjectCodeEnum.getByCode(task.getBookType()))
                .map(IntentProjectCodeEnum::getDesc).orElse(IntentProjectCodeEnum.HAIRDRESSING.getDesc()));
        request.setBusinessSource(BusinessSource.HAIRSTYLING_CANCEL);
        request.setOrderId(task.getDetails().get(0).getId());
        request.setBookingTime(task.getDetails().get(0).getBookTime().getTime() / 1000);
        return request;
    }


    private int queryUserGender(long mtUserId) {
        int tagId = lionConfigUtil.getDispatchConfigData().getGenderPersonaTagId();
        List<LabelData> labelDatas = userAclService.getUserProfile(mtUserId, Lists.newArrayList(tagId));
        Map<Integer, LabelData> labelDataMap = labelDatas.stream().collect(Collectors.toMap(LabelData::getId, Function.identity(), (o1, o2) -> o1));
        if (!labelDataMap.containsKey(tagId)) {
            return Gender.OTHER.getValue();
        }
        LabelData labelData = labelDataMap.get(tagId);
        if (labelData == null || labelData.getValue() == null) {
            return Gender.OTHER.getValue();
        }
        // 0-女 1-男
        return "0".equals(labelData.getValue()) ? Gender.FEMALE.getValue() : Gender.MALE.getValue();
    }
}
