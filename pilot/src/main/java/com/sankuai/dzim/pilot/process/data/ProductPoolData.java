package com.sankuai.dzim.pilot.process.data;

import lombok.Data;

@Data
public class ProductPoolData {

    /**
     * 拼场场次描述
     */
    private String description;

    /**
     * 拼场最低人数
     */
    private int minNum;

    /**
     * 拼场最高人数
     */
    private int maxNum;

    /**
     * 当前场次人数
     */
    private int currentNum;

    /**
     * 当前拼场状态 1:初始状态, 2:拼场中, 3:拼场成功, 4:人数不足拼场失败, 5:其他人包场拼场失败, 6:商品变动拼场失败
     */
    private int poolStatus;

    /**
     * 拼场类型，仅包场，仅拼场，可拼可包锁场，可拼可包不锁场等，具体见dztrade-common的com.dianping.dztrade.enums.PoolTypeEnum类
     */
    private int poolType;

    /**
     * 当前拼场是否还可以加入
     */
    private boolean canJoin;

    /**
     * 场次开始时间
     */
    private long bookStartTime;

}
