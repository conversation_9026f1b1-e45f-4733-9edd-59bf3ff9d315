package com.sankuai.dzim.pilot.domain;

import com.sankuai.dzim.pilot.dal.entity.aiphonecall.AIPhoneCallDetailEntity;
import com.sankuai.dzim.pilot.dal.entity.aiphonecall.AIPhoneCallTaskEntity;
import com.sankuai.dzim.pilot.enums.AIPhoneCallDetailStatusEnum;
import com.sankuai.dzim.pilot.enums.AIPhoneCallTaskStatusEnum;
import com.sankuai.dzim.pilot.gateway.mq.data.AIPhoneCallRecordData;

import java.util.List;

public interface AIPhoneCallDomainService {

    /**
     * 插入外呼任务和明细
     * @param callTaskEntity
     * @param callDetailEntityList
     * @return
     */
    boolean insertTaskAndDetail(AIPhoneCallTaskEntity callTaskEntity, List<AIPhoneCallDetailEntity> callDetailEntityList);

    /**
     * 执行任务
     * @param taskId
     * @param oldState
     */
    void executeTask(long taskId, AIPhoneCallTaskStatusEnum oldState);

    /**
     * 检查任务下的外呼目标是否均已外呼完成
     * @param taskId
     * @return
     */
    boolean checkAllDetailDone(long taskId);

    /**
     * 判断能否外呼下一个明细
     * @param taskId
     * @return
     */
    boolean canScheduleNext(long taskId);

    /**
     * 发起外呼电话
     *
     * @param oldState
     * @param detailId
     * @return 是否发起成功
     */
    boolean initiateCall(AIPhoneCallDetailStatusEnum oldState, long detailId);

    /**
     * 延迟外呼
     * @param detailId
     * @return
     */
    boolean delayCall(long detailId);

    /**
     * 拨动成功后处理
     * @param detailId
     * @param taskId
     * @return
     */
    boolean callSuccessPostProcess(long detailId, long taskId);

    /**
     * 是否存在外呼成功的明细
     * @param taskId
     * @return
     */
    boolean hasSuccessCallDetail(long taskId);

    /**
     * 判断任务是否可流转到完成态
     */
    boolean checkTaskIsComplete(long taskId);

    /**
     * 重新外呼
     * @param detailId
     * @return
     */
    boolean retryCall(long detailId,int sceneType);

    /**
     * 处理通话记录
     * @param callRecordData
     */
    void processCallRecord(AIPhoneCallRecordData callRecordData);

    /**
     * 判断能否重新外呼
     * @param detailId
     * @return true：可重新外呼；false：不可重新外呼
     */
    boolean preCheckBeforeRetry(long detailId,int sceneType);

    /**
     * 查询门店负面反馈次数
     * @param dpShopId
     * @return
     */
    int getShopNegativeCallCount(long dpShopId);

    /**
     * 发起外呼前校验
     * @param callDetailId
     * @return true：校验通过；false：校验不通过
     */
    boolean preCheckBeforeIvrCall(long callDetailId);

    /**
     * 取消外呼任务
     */
    void cancelTaskDetail(long taskId);

}
