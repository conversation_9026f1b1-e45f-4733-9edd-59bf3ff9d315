package com.sankuai.dzim.pilot.process.impl.suglist;

import arts.utils.UrlUtils;
import com.google.common.collect.Lists;
import com.sankuai.dzim.pilot.api.data.search.generative.QuerySuggestionRequest;
import com.sankuai.dzim.pilot.api.data.search.generative.SuggestionDTO;
import com.sankuai.dzim.pilot.api.enums.search.generative.GenerativeSearchAnswerTemplateTypeEnum;
import com.sankuai.dzim.pilot.api.enums.search.generative.GenerativeSearchTypeEnum;
import com.sankuai.dzim.pilot.enums.SugListStrategyEnum;
import com.sankuai.dzim.pilot.process.data.TherapySugListConfigData;
import com.sankuai.dzim.pilot.utils.LionConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Random;

@Component
@Slf4j
public class TherapySearchWordQuestionStrategy implements QuerySugListStrategy {
    @Autowired
    private LionConfigUtil lionConfigUtil;

    public TherapySearchWordQuestionStrategy(LionConfigUtil lionConfigUtil) {
        this.lionConfigUtil = lionConfigUtil;
    }

    private final String jumpurl = "imeituan://www.meituan.com/gc/mrn?mrn_biz=gcbu&mrn_entry=searchpage&mrn_component=mrn-gc-searchresult&categoryid=2&keyword=%s&switchcityid=%s&subbizid=2&hintword=%s&showkeyword=%s&source=joy_v3home&searchSource=sug";

    @Override
    public boolean accept(String strategyName) {
        if (StringUtils.isEmpty(strategyName) || !SugListStrategyEnum.TherapySearchWordQuestionStrategy.getName().equals(strategyName)) {
            return false;
        }
        return true;
    }

    @Override
    public List<SuggestionDTO> execute(QuerySuggestionRequest request, List<SuggestionDTO> suggestionDTOList) {
        if (request.getBizType() != GenerativeSearchTypeEnum.ENTERTAINMENT.getType()) {
            return suggestionDTOList;
        }
        TherapySugListConfigData therapySugListConfigData = lionConfigUtil.getTherapySugListConfigData();

        // 确定主体词
        String sugWord = getSugWord(therapySugListConfigData, request);
        if (StringUtils.isEmpty(sugWord)) {
            return suggestionDTOList;
        }

        String sceneWord = therapySugListConfigData.getCurrentScene();
        //确定主体词词性
        String wordType = getWordType(sugWord, therapySugListConfigData.getWordtype());
        if (StringUtils.isEmpty(wordType)) {
            return suggestionDTOList;
        }

        //查找sug
        List<String> sugFromLion = getSugFromLion(sugWord, sceneWord, wordType, therapySugListConfigData.getWord2query());
        return randomSelectAndConvert(request, sugFromLion, sugWord, therapySugListConfigData);
    }

    private List<SuggestionDTO> randomSelectAndConvert(QuerySuggestionRequest request, List<String> sugFromLion, String sugWord, TherapySugListConfigData therapySugListConfigData) {
        if (CollectionUtils.isEmpty(sugFromLion)) {
            return Lists.newArrayList();
        }
        String selectedSug = sugFromLion.get(new Random().nextInt(sugFromLion.size()));

        SuggestionDTO suggestionDTO = new SuggestionDTO();
        suggestionDTO.setSuggestion(selectedSug);
        suggestionDTO.setTemplateType(GenerativeSearchAnswerTemplateTypeEnum.UNIFIED_TOP_CONVERGENCE.getType());
        suggestionDTO.setKeywordList(Lists.newArrayList(sugWord));
        suggestionDTO.setLink(buildTherapyLink(request, sugWord, selectedSug, therapySugListConfigData));
        return Lists.newArrayList(suggestionDTO);
    }


    // 产品希望跳到频道页直接进入第一个卡片选项给定的搜索词
    public String buildTherapyLink(QuerySuggestionRequest request, String sugWord, String selectedSug, TherapySugListConfigData therapySugListConfigData) {
        List<String> projectWord = therapySugListConfigData.getWordtype().get("project");
        if (!projectWord.contains(sugWord)) {
            sugWord = "艾灸";
        }
        return String.format(jumpurl, UrlUtils.urlEncode(sugWord), request.getCityId(), UrlUtils.urlEncode(selectedSug), UrlUtils.urlEncode(selectedSug));
    }

    private String getSugWord(TherapySugListConfigData therapySugListConfigData, QuerySuggestionRequest request) {
        String sugWord = "";
        for (Map.Entry<String, String> entry : therapySugListConfigData.getQuestion2Word().entrySet()) {
            String question = entry.getKey();
            if (request.getQuery().contains(question)) {
                sugWord = entry.getValue();
                return sugWord;
            }
        }
        return sugWord;
    }

    private String getWordType(String sceneWord, Map<String, List<String>> wordType) {
        String type = "";
        for (Map.Entry<String, List<String>> entry : wordType.entrySet()) {
            if (entry.getValue().contains(sceneWord)) {
                type = entry.getKey();
                return type;
            }
        }
        return type;
    }

    private List<String> getSugFromLion(String sugWord, String sceneWord, String wordType, Map<String, List<String>> word2query) {
        if ("project".equals(wordType)) {
            return buildProjectSug(sugWord, sceneWord, word2query);
        }
        String key = buildKey(sugWord, sceneWord);
        return word2query.get(key);
    }

    public List<String> buildProjectSug(String sugWord, String sceneWord, Map<String, List<String>> word2query) {
        List<String> copySugs = Lists.newArrayList();
        String key = buildKey("project", sceneWord);
        List<String> sugs = word2query.get(key);
        for (int i = 0; i < sugs.size(); i++) {
            copySugs.add(String.format(sugs.get(i), sugWord));
        }
        return copySugs;
    }

    public String buildKey(String sugWord, String sceneWord) {
        return sugWord + "-" + sceneWord;
    }
}
