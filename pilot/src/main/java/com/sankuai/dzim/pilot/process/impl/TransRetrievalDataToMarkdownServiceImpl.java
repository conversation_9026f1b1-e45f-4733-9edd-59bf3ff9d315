package com.sankuai.dzim.pilot.process.impl;

import com.meituan.data.ups.thrift.LabelData;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.acl.UserAclService;
import com.sankuai.dzim.pilot.domain.retrieval.data.PersonaLabelConfig;
import com.sankuai.dzim.pilot.process.TransRetrievalDataToMarkdownService;
import com.sankuai.dzim.pilot.process.data.PersonaLabelEnum;
import com.sankuai.dzim.pilot.process.data.ShopRetrievalData;
import com.sankuai.dzim.pilot.utils.IMConstants;
import com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.detail.DealGroupTemplateDetailDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.MustServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.OptionalServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TransRetrievalDataToMarkdownServiceImpl implements TransRetrievalDataToMarkdownService {

    @Autowired
    private UserAclService userAclService;

    //用户行为操作店铺信息表头
    private static final List<String> TABLE_HEAD_SHOP_INFO = new ArrayList<>(Arrays.asList(
            "店铺名称", "美团星级", "美团人均消费价格", "后台城市名称", "后台行政区名称", "后台商圈名称", "商户地址", "附近地铁站", "品牌名称",
            "距离用户距离(-1表示信息缺失)", "用户操作", "用户操作时间", "操作时页面的搜索词"
    ));

    //用户行为操作商品信息表头
    private static final List<String> TABLE_HEAD_DEAL_INFO = new ArrayList<>(Arrays.asList(
            "商品名称", "商品价格", "商品服务项", "商品介绍",
            "店铺名称", "美团星级", "美团人均消费价格", "后台城市名称", "后台行政区名称", "后台商圈名称", "商户地址", "附近地铁站", "品牌名称",
            "距离用户距离(-1表示信息缺失)", "用户操作", "用户操作时间", "操作时页面的搜索词"
    ));


    @Override
    public String transShopOpData2Markdown(List<ShopRetrievalData> shopOpDatas) {
        StringBuilder markdown = new StringBuilder();
        getTableHeader(markdown, TABLE_HEAD_SHOP_INFO);
        for (ShopRetrievalData shopOpData : shopOpDatas) {
            try {
                StringBuilder tempMarkdown = new StringBuilder();
                tempMarkdown.append("|");
                tempMarkdown.append(escapeMarkdown(shopOpData.getShopName())).append("|");
                tempMarkdown.append(escapeMarkdown(formatMtScore(shopOpData.getMtAvgScore()))).append("|");
                tempMarkdown.append(escapeMarkdown(shopOpData.getMtAvgPrice())).append("|");
                tempMarkdown.append(escapeMarkdown(shopOpData.getMtCityName())).append("|");
                tempMarkdown.append(escapeMarkdown(shopOpData.getMtDistrictName())).append("|");
                tempMarkdown.append(escapeMarkdown(shopOpData.getMtRegionName())).append("|");
                tempMarkdown.append(escapeMarkdown(shopOpData.getShopAddress())).append("|");
                tempMarkdown.append(escapeMarkdown(shopOpData.getSubwayStation())).append("|");
                tempMarkdown.append(escapeMarkdown(shopOpData.getBrandName())).append("|");
                tempMarkdown.append(escapeMarkdown(shopOpData.getUserDistance())).append("|");
                tempMarkdown.append(escapeMarkdown(shopOpData.getUserOperation())).append("|");
                tempMarkdown.append(escapeMarkdown(shopOpData.getUserOperationTime())).append("|");
                tempMarkdown.append(escapeMarkdown(shopOpData.getKeyword())).append("|");
                tempMarkdown.append("\n");

                markdown.append(tempMarkdown);
            } catch (Exception e) {
                LogUtils.logFailLog(log, TagContext.builder().action("transShopOpData2Markdown").build(),
                        new WarnMessage("transShopOpData2Markdown", "将门店数据转化为markdown失败", null), shopOpDatas, null, e);
            }
        }
        return markdown.toString();
    }


    @Override
    public String transDealOpData2Markdown(List<ShopRetrievalData> dealOpDatas) {
        StringBuilder markdown = new StringBuilder();
        getTableHeader(markdown, TABLE_HEAD_DEAL_INFO);

        for (ShopRetrievalData dealOpData : dealOpDatas) {
            try {
                StringBuilder tempMarkdown = new StringBuilder();
                DealGroupDTO dealGroupDTO = dealOpData.getDealGroupDTO();

                if (dealGroupDTO == null) {
                    continue;
                }
                tempMarkdown.append("|");
                tempMarkdown.append(escapeMarkdown(getTitle(dealGroupDTO))).append("|");
                tempMarkdown.append(escapeMarkdown(getPrice(dealGroupDTO))).append("|");
                tempMarkdown.append(escapeMarkdown(getServiceProject(dealGroupDTO))).append("|");
                tempMarkdown.append(escapeMarkdown(getIntroduction(dealGroupDTO))).append("|");
                tempMarkdown.append(escapeMarkdown(dealOpData.getShopName())).append("|");
                tempMarkdown.append(escapeMarkdown(formatMtScore(dealOpData.getMtAvgScore()))).append("|");
                tempMarkdown.append(escapeMarkdown(dealOpData.getMtAvgPrice())).append("|");
                tempMarkdown.append(escapeMarkdown(dealOpData.getMtCityName())).append("|");
                tempMarkdown.append(escapeMarkdown(dealOpData.getMtDistrictName())).append("|");
                tempMarkdown.append(escapeMarkdown(dealOpData.getMtRegionName())).append("|");
                tempMarkdown.append(escapeMarkdown(dealOpData.getShopAddress())).append("|");
                tempMarkdown.append(escapeMarkdown(dealOpData.getSubwayStation())).append("|");
                tempMarkdown.append(escapeMarkdown(dealOpData.getBrandName())).append("|");
                tempMarkdown.append(escapeMarkdown(dealOpData.getUserDistance())).append("|");
                tempMarkdown.append(escapeMarkdown(dealOpData.getUserOperation())).append("|");
                tempMarkdown.append(escapeMarkdown(dealOpData.getUserOperationTime())).append("|");
                tempMarkdown.append(escapeMarkdown(dealOpData.getKeyword())).append("|");
                tempMarkdown.append("\n");

                markdown.append(tempMarkdown);
            } catch (Exception e) {
                LogUtils.logFailLog(log, TagContext.builder().action("transDealOpData2Markdown").build(),
                        new WarnMessage("transDealOpData2Markdown", "将团单数据转化为markdown失败", null), dealOpDatas, null, e);
            }
        }

        return markdown.toString();
    }

    @Override
    public String getAndTransPersonal2Markdown(Long mtUserId, List<PersonaLabelConfig> personaLabelConfigs) {
        //portrait <1, 男>  <3, 未婚>
        Map<Integer, String> portrait = getMtUserPortrait(mtUserId, personaLabelConfigs);
        StringBuilder markdown = new StringBuilder();
        getTableHeader(markdown, new ArrayList<>(Arrays.asList("画像名称", "画像值")));
        for (Map.Entry<Integer, String> entry : portrait.entrySet()) {
            String item = PersonaLabelEnum.get(entry.getKey());
            if (!StringUtils.isBlank(item)) {
                markdown.append("|");
                markdown.append(escapeMarkdown(item)).append("|");
                markdown.append(escapeMarkdown(entry.getValue())).append("|");
                markdown.append("\n");
            }
        }
        log.info("getAndTransPersonal2Markdown:{}", markdown);
        return markdown.toString();
    }

    private Map<Integer, String> getMtUserPortrait(Long mtUserId, List<PersonaLabelConfig> personaLabelConfigs) {
        List<Integer> labelsIds = personaLabelConfigs.stream().map(PersonaLabelConfig::getLabelId).collect(Collectors.toList());
        List<LabelData> labelDatas = userAclService.getUserProfile(mtUserId, labelsIds);
        Map<Integer, LabelData> labelDataMap = labelDatas.stream().collect(Collectors.toMap(LabelData::getId, Function.identity(), (o1, o2) -> o1));

        Map<Integer, String> portrait = new HashMap<>();
        for (PersonaLabelConfig config : personaLabelConfigs) {
            int labelId = config.getLabelId();
            if (!labelDataMap.containsKey(labelId)) {
                continue;
            }

            //标签实际存储数据
            String actualTagVal = labelDataMap.get(labelId).getValue();

            //有些标签存储实际数据不是 0,1这种映射，直接用，如济南这种，所以lion配置不配 map，默认值使用实际存储值
            portrait.put(config.getPersonaLabelType(), config.getValue(actualTagVal, String.class, "未知"));
        }
        return portrait;
    }

    // 转义 Markdown 中的特殊字符（如 |）
    private static String escapeMarkdown(Object value) {
        if (value == null) return "";
        return value.toString().replace("|", "\\|");
    }


    /**
     * 打下表头和表头下方的分隔行
     *
     * @param markdown
     * @param headers
     * @return
     */
    private static void getTableHeader(StringBuilder markdown, List<String> headers) {
        markdown.append("|");
        for (String header : headers) {
            markdown.append(header);
            markdown.append("|");
        }
        markdown.append("\n");

        //打下分割行
        markdown.append("|");
        for (int i = 0; i < headers.size(); i++) {
            markdown.append("---|");
        }
        markdown.append("\n");
    }

    /**
     * 格式化美团星级 46 -> 4.6
     *
     * @param mtScore int
     * @return float
     */
    public double formatMtScore(int mtScore) {
        BigDecimal bigDecimalNumber = BigDecimal.valueOf(mtScore);
        BigDecimal result = bigDecimalNumber.divide(BigDecimal.valueOf(10), 1, RoundingMode.HALF_UP);
        return result.doubleValue();
    }

    private String getTitle(DealGroupDTO dealGroupDTO) {
        String title = Optional.ofNullable(dealGroupDTO).map(DealGroupDTO::getBasic).map(DealGroupBasicDTO::getTitle).orElse("");
        String titleDesc = Optional.ofNullable(dealGroupDTO).map(DealGroupDTO::getBasic).map(DealGroupBasicDTO::getTitleDesc).orElse("");
        if (StringUtils.isNotBlank(titleDesc)) {
            return title + "( " + titleDesc + " )";
        }
        return title;
    }

    //copy from RecommendPlugin.java
    private String getPrice(DealGroupDTO dealGroupDTO) {
        // 检查团购商品的价格信息是否为空
        if (dealGroupDTO.getPrice() == null) {
            // 如果价格信息为空，返回默认提示信息
            return "暂无价格信息," + IMConstants.ANSWER_PROBLEM_NO_REPLY;
        }
        return dealGroupDTO.getPrice().getSalePrice() + "元";
    }

    //copy from RecommendPlugin.java
    private String getServiceProject(DealGroupDTO dealGroupDTO) {
        if (dealGroupDTO.getServiceProject() == null) {
            return "暂无服务项目信息";
        }

        StringBuilder serviceSb = new StringBuilder();

        // 处理全部可享服务
        appendMustServiceProjects(dealGroupDTO, serviceSb);

        // 处理部分可享服务
        appendOptionalServiceProjects(dealGroupDTO, serviceSb);

        return serviceSb.toString();
    }

    /**
     * 添加全部可享服务项目
     */
    private void appendMustServiceProjects(DealGroupDTO dealGroupDTO, StringBuilder serviceSb) {
        if (CollectionUtils.isEmpty(dealGroupDTO.getServiceProject().getMustGroups())) {
            return;
        }

        serviceSb.append("\n\t【全部可享】服务包含:");
        for (MustServiceProjectGroupDTO serviceGroup : dealGroupDTO.getServiceProject().getMustGroups()) {
            if (serviceGroup == null || CollectionUtils.isEmpty(serviceGroup.getGroups())) {
                continue;
            }
            for (ServiceProjectDTO serviceProjectDTO : serviceGroup.getGroups()) {
                serviceSb.append("\n\t\t").append(parseServiceProject(serviceProjectDTO));
            }
        }
    }

    /**
     * 添加部分可享服务项目
     */
    private void appendOptionalServiceProjects(DealGroupDTO dealGroupDTO, StringBuilder serviceSb) {
        if (CollectionUtils.isEmpty(dealGroupDTO.getServiceProject().getOptionGroups())) {
            return;
        }

        serviceSb.append("\n\t【部分可享】服务包含:");
        for (OptionalServiceProjectGroupDTO serviceGroup : dealGroupDTO.getServiceProject().getOptionGroups()) {
            if (serviceGroup == null || CollectionUtils.isEmpty(serviceGroup.getGroups())) {
                continue;
            }
            String optionTip = String.format("%s选%s", serviceGroup.getGroups().size(), serviceGroup.getOptionalCount());
            serviceSb.append("\n\t\t").append(optionTip).append(":");
            for (ServiceProjectDTO serviceProjectDTO : serviceGroup.getGroups()) {
                if (serviceProjectDTO == null) {
                    continue;
                }
                serviceSb.append("\n\t\t\t").append(parseServiceProject(serviceProjectDTO));
            }
        }
    }

    //copy from RecommendPlugin.java
    private String parseServiceProject(ServiceProjectDTO serviceProjectDTO) {
        StringBuilder sb = new StringBuilder();
        sb.append("服务名称:").append(serviceProjectDTO.getName());
        if (StringUtils.isNotBlank(serviceProjectDTO.getMarketPrice())) {
            sb.append(" ( ").append("价值:").append(serviceProjectDTO.getMarketPrice()).append("元").append(" )");
        }
        if (CollectionUtils.isEmpty(serviceProjectDTO.getAttrs())) {
            return sb.toString();
        }

        for (int index = 0; index < serviceProjectDTO.getAttrs().size(); ++index) {
            ServiceProjectAttrDTO attrDTO = serviceProjectDTO.getAttrs().get(index);
            sb.append(" ( ").append(attrDTO.getChnName()).append(":").append(attrDTO.getAttrValue());
            if (index != serviceProjectDTO.getAttrs().size() - 1) {
                sb.append(", ");
            } else {
                sb.append(" )");
            }
        }
        return sb.toString();
    }

    //copy from RecommendPlugin.java
    private String getIntroduction(DealGroupDTO dealGroupDTO) {
        if (dealGroupDTO.getDetail() == null || CollectionUtils.isEmpty(dealGroupDTO.getDetail().getTemplateDetailDTOs())) {
            return "暂无商品介绍信息";
        }

        StringBuilder sb = new StringBuilder();
        for (DealGroupTemplateDetailDTO templateDetailDTO : dealGroupDTO.getDetail().getTemplateDetailDTOs()) {
            if (templateDetailDTO.getTitle().equals("产品介绍")) {
                sb.append("\n\t产品介绍:").append(parseHtml(templateDetailDTO.getContent(), "\n\t\t"));
            }
            if (templateDetailDTO.getTitle().equals("团购详情")) {
                sb.append("\n\t补充信息:").append(parseHtml(templateDetailDTO.getContent(), "\n\t\t"));
            }
            if (templateDetailDTO.getTitle().equals("购买须知")) {
                sb.append("\n\t购买须知:").append(parseHtml(templateDetailDTO.getContent(), "\n\t\t"));
            }
        }

        return sb.toString();
    }

    //copy from RecommendPlugin.java
    private String parseHtml(String content, String prefix) {
        StringBuilder stringBuilder = new StringBuilder();

        Document doc = Jsoup.parse(content);
        Elements paragraphs = doc.select("p");
        for (Element paragraph : paragraphs) {
            if (StringUtils.isNotBlank(paragraph.text())) {
                stringBuilder.append(prefix).append(paragraph.text());
            }
        }

        return stringBuilder.toString();
    }
}
