package com.sankuai.dzim.pilot.chain.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.acl.data.fraiday.ChatCompletionRequest;
import com.sankuai.dzim.pilot.acl.data.fraiday.ChatCompletionResponse;
import com.sankuai.dzim.pilot.api.data.AIAnswerTypeEnum;
import com.sankuai.dzim.pilot.buffer.enums.StreamEventErrorTypeEnum;
import com.sankuai.dzim.pilot.chain.AIService;
import com.sankuai.dzim.pilot.chain.data.AIAnswerData;
import com.sankuai.dzim.pilot.chain.data.AIServiceContext;
import com.sankuai.dzim.pilot.chain.enums.AIServiceExtraKeyEnum;
import com.sankuai.dzim.pilot.chain.enums.AIServiceTypeEnum;
import com.sankuai.dzim.pilot.chain.eval.data.EvalCase;
import com.sankuai.dzim.pilot.domain.message.AssistantMessage;
import com.sankuai.dzim.pilot.domain.message.Message;
import com.sankuai.dzim.pilot.domain.message.UserMessage;
import com.sankuai.dzim.pilot.domain.plugin.data.PluginSpecification;
import com.sankuai.dzim.pilot.domain.retrieval.data.RetrievalResult;
import com.sankuai.dzim.pilot.gateway.api.assistant.data.SseAwareException;
import com.taobao.tair3.client.json.JSONArray;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * 评测AIService
 * @author: zhouyibing
 * @date: 2025/4/16
 */
@Slf4j
@Component
public class EvalAIService extends AdvancedRagAIService implements AIService {

    private final ObjectMapper mapper = new ObjectMapper();

    @Override
    public boolean accept(int aiServiceType) {
        return aiServiceType == AIServiceTypeEnum.EVALUATION.getType();
    }

    @Override
    public AIAnswerData execute(AIServiceContext aiServiceContext) {
        EvalCase evalCase = getEvalCase(aiServiceContext);
        if (evalCase == null) {
            throw new RuntimeException("评测用例为空");
        }
        // 1.记忆
        List<Message> memory = getMemory(evalCase);

        // 2.检索组件
        List<RetrievalResult> retrievalResults = getRetrievalKnowledge(evalCase);

        // 3.工具
        List<PluginSpecification> pluginSpecifications = getPlugins(aiServiceContext);

        // 4.模版参数
        String systemPrompt = buildEvalSystemPrompt(aiServiceContext, evalCase);
        aiServiceContext.setSystemPrompt(systemPrompt);

        // 5.获取答案
        AIAnswerData answer = getAnswer(aiServiceContext, memory, retrievalResults, pluginSpecifications);

        return answer;
    }

    private String buildEvalSystemPrompt(AIServiceContext aiServiceContext, EvalCase evalCase) {
        //将context中的参数拼到模版中
        String systemPrompt = aiServiceContext.getSystemPrompt();
        try {
            JSONObject contextJson = JsonCodec.decode(evalCase.getContext(), JSONObject.class);
            if (contextJson == null) {
                return systemPrompt;
            }

            for (String fieldName : contextJson.keySet()) {
                Object value = contextJson.get(fieldName);
                if (value != null) {
                    systemPrompt = systemPrompt.replace("${" + fieldName + "}", value.toString());
                }
            }
            return systemPrompt;
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("buildEvalSystemPrompt").build(),
                    new WarnMessage("EvalAIService", "buildEvalSystemPrompt异常", null), evalCase, null, e);
            return systemPrompt;
        }
    }

    private EvalCase getEvalCase(AIServiceContext aiServiceContext) {
        return Optional.ofNullable(aiServiceContext.getExtraInfoValue(AIServiceExtraKeyEnum.EVAL_CASE.getKey()))
                .map(value -> (EvalCase) value)
                .orElse(null);
    }

    private List<Message> getMemory(EvalCase evalCase) {
        List<Message> memoryList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(evalCase.getChatHistory())) {
            memoryList.addAll(evalCase.getChatHistory());
        }
        UserMessage userMessage = UserMessage.build(evalCase.getInput());
        memoryList.add(userMessage);
        return memoryList;
    }

    private List<RetrievalResult> getRetrievalKnowledge(EvalCase evalCase) {
        String knowledge = evalCase.getKnowledge();
        if (StringUtils.isBlank(knowledge)) {
            return Lists.newArrayList();
        }
        try {
            return JSON.parseArray(knowledge, RetrievalResult.class);
        }
        catch (Exception e) {
            throw new RuntimeException("知识格式不合法");
        }
    }

    private AIAnswerData getAnswer(AIServiceContext aiServiceContext, List<Message> memory, List<RetrievalResult> retrievalResults, List<PluginSpecification> pluginSpecifications) {
        // 将上下文，插件的描述传给LLM，看大模型是直接回复，还是需要执行插件然后再回复
        ChatCompletionRequest request = buildChatCompletionRequest(aiServiceContext, memory, retrievalResults, pluginSpecifications);
        ChatCompletionResponse chatCompletionResponse = fridayAclService.chatCompletion(aiServiceContext.getAppId(), request);
        if (chatCompletionResponse == null) {
            throw new SseAwareException(StreamEventErrorTypeEnum.SERVER_ERROR);
        }

        if (CollectionUtils.isEmpty(chatCompletionResponse.getChoices())) {
            throw new SseAwareException(StreamEventErrorTypeEnum.getByErrorCode(chatCompletionResponse.getCode()));
        }

        // 结果解析
        AssistantMessage assistantMessage = AssistantMessage.build(chatCompletionResponse);
        if (assistantMessage == null) {
            throw new SseAwareException(StreamEventErrorTypeEnum.SERVER_ERROR);
        }
        // 大模型调用插件
        if (CollectionUtils.isNotEmpty(assistantMessage.getPluginCalls())) {
            return new AIAnswerData(AIAnswerTypeEnum.TEXT.getType(), writeValueAsString(assistantMessage.getPluginCalls()), assistantMessage.getReasonContent());
        }
        // 大模型直接回复
        if (assistantMessage.getContent() != null) {
            return new AIAnswerData(AIAnswerTypeEnum.TEXT.getType(), assistantMessage.getContent(), assistantMessage.getReasonContent());
        }
        return new AIAnswerData();
    }

    private String writeValueAsString(Object o) {
        try {
            return mapper.writeValueAsString(o);
        } catch (JsonProcessingException e) {
            LogUtils.logFailLog(log, TagContext.builder().action("writeValueAsString").build(),
                    new WarnMessage("EvalAIService", "writeValueAsString异常", null), o, null, e);
            return null;
        }
    }
}
