package com.sankuai.dzim.pilot.dal.pilotdao.aibook;

import com.sankuai.dzim.pilot.dal.entity.aibook.AIBookTaskEntity;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/1/9 11:29
 */
public interface AIBookTaskDAO {

    int insert(@Param("entity") AIBookTaskEntity taskEntity);

    AIBookTaskEntity getById(@Param("id") long id);

    int updateTaskStatus(@Param("id") long id, @Param("taskStatus") int taskStatus);

    int updateTaskBookSuccess(@Param("id") long id, @Param("taskStatus") int taskStatus, @Param("bookTime") Date bookTime);


    AIBookTaskEntity queryLatestBookTask(@Param("userId") long userId, @Param("platform") int platform);

    List<AIBookTaskEntity> queryBookTaskList(@Param("userId") long userId, @Param("platform") int platform);


    List<AIBookTaskEntity> queryUserTasksByStatus(@Param("userId") long userId, @Param("platform") int platform,
                                                     @Param("taskStatusList") List<Integer> taskStatusList);



}
