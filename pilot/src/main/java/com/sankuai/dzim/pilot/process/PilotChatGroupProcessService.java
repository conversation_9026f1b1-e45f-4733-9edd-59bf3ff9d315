package com.sankuai.dzim.pilot.process;

import com.sankuai.dzim.pilot.api.data.assistant.PilotChatGroupDTO;
import com.sankuai.dzim.pilot.dal.entity.pilot.PilotChatGroupEntity;
import com.sankuai.dzim.pilot.gateway.api.vo.ChatGroupListAndPageVO;
import com.sankuai.dzim.pilot.gateway.api.vo.InnerAssistantChatGroupVO;
import com.sankuai.dzim.pilot.process.aireservebook.enums.POIFrontType;
import com.sankuai.dzim.pilot.process.data.ChatGroupLoadReq;

public interface PilotChatGroupProcessService {
    /**
     * 根据imUserId和助手类型获取会话，会话包括(最后一条消息)或者(TIPS和欢迎语)
     */
    PilotChatGroupDTO getChatGroupInfo(ChatGroupLoadReq chatGroupLoadReq);

    /**
     * 根据chatGroupId获取会话，但没有消息内容
     *
     * @param chatGroupId
     * @return
     */
    PilotChatGroupDTO getChatGroupById(long chatGroupId);

    InnerAssistantChatGroupVO getChatGroupData(Long chatGroupId, Integer assistantType, String ssoUserId);

    ChatGroupListAndPageVO getChatGroupList(Integer pageNum, String userId, Integer pageSize);

    /**
     * 获取会话，若不存在则新建会话
     * @param imUserId
     * @param assistantType
     * @return
     */
    PilotChatGroupEntity getOrCreateChatGroup(String imUserId, int assistantType);

    /**
     * 提取类目
     * @param listType
     * @param bizCode
     * @param categoryId
     * @param currentQuery
     * @return
     */
    POIFrontType checkCategoryIntent(String listType, String bizCode, Long categoryId, String currentQuery);
}
