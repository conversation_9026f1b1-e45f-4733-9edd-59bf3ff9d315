package com.sankuai.dzim.pilot.gateway.config;

import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.module.jsonSchema.JsonSchema;
import com.fasterxml.jackson.module.jsonSchema.customProperties.TitleSchemaFactoryWrapper;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.domain.plugin.PluginRegistry;
import com.sankuai.dzim.pilot.domain.plugin.data.PluginSpecification;
import com.sankuai.dzim.pilot.domain.plugin.data.PluginTypeEnum;
import com.sankuai.dzim.pilot.domain.annotation.LocalPlugin;
import com.sankuai.dzim.pilot.domain.plugin.executor.impl.LocalPluginExecutor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Iterator;

@Component
@Slf4j
public class LocalPluginPostProcessor implements BeanPostProcessor {

    private final ObjectMapper mapper = new ObjectMapper();

    @Override
    public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
        Class<?> clazz = bean.getClass();
        Method[] declaredMethods = clazz.getDeclaredMethods();
        if (declaredMethods.length == 0) {
            return bean;
        }

        for (Method method : declaredMethods) {
            LocalPlugin annotation = method.getAnnotation(LocalPlugin.class);
            if (annotation == null) {
                continue;
            }

            // 本地插件有且只有一个参数
            if (method.getParameterCount() != 1) {
                throw new IllegalArgumentException("本地插件方法参数数量错误,有且只有一个参数");
            }

            try {
                PluginSpecification plugin = PluginSpecification.builder()
                        .type(PluginTypeEnum.LOCAL.getType())
                        .name(annotation.name())
                        .description(annotation.description())
                        .returnDirect(annotation.returnDirect())
                        .lionKey(annotation.lionKey())
                        .params(getParamJsonSchema(method.getParameterTypes()[0]))
                        .executor(new LocalPluginExecutor(bean, method))
                        .build();
                PluginRegistry.addPlugin(plugin);
            } catch (JsonMappingException e) {
                LogUtils.logFailLog(log, TagContext.builder().action("plugin:initerror").build()
                , new WarnMessage("LocalPluginPostProcessor", "插件注册失败", null), annotation.name(), beanName, e);
            }
        }
        return bean;
    }

    private JsonNode getParamJsonSchema(Class<?> parameterType) throws JsonMappingException {
        TitleSchemaFactoryWrapper wrapper = new TitleSchemaFactoryWrapper();
        mapper.acceptJsonFormatVisitor(parameterType, wrapper);
        JsonSchema jsonSchema = wrapper.finalSchema();
        JsonNode jsonNode = mapper.valueToTree(jsonSchema);
        addOtherProperty(jsonNode);
        return jsonNode;
    }

    private JsonNode addOtherProperty(JsonNode jsonNode) {
        JsonNode properties = jsonNode.get("properties");
        if (properties == null) {
            return jsonNode;
        }

        ArrayNode requiredPropertyList = mapper.createArrayNode();
        Iterator<String> iterator = properties.fieldNames();
        while (iterator.hasNext()) {
            String fieldName = iterator.next();
            JsonNode element = properties.get(fieldName);

            if (element == null) {
                continue;
            }

            JsonNode elementRequired = element.get("required");
            if (elementRequired != null && elementRequired.asBoolean()) {
                requiredPropertyList.add(fieldName);
                ((ObjectNode) element).remove("required");
            }

            // 判断当前属性是否是一个对象,是对象的话需要递归修改该对象内部的属性
            JsonNode elementType = element.get("type");
            if (elementType != null && elementType.asText().equals("object")) {
                ((ObjectNode) properties).set(fieldName, addOtherProperty(element));
            }
        }

        //添加required属性
        ((ObjectNode) jsonNode).set("required", requiredPropertyList);

        // 移除id属性
        JsonNode elementId = jsonNode.get("id");
        if (elementId != null) {
            ((ObjectNode) jsonNode).remove("id");
        }

        // 移除title属性
        JsonNode elementTitle = jsonNode.get("title");
        if (elementId != null) {
            ((ObjectNode) jsonNode).remove("title");
        }

        return jsonNode;
    }
}
