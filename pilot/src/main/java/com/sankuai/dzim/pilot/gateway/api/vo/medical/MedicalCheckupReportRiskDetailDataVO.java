package com.sankuai.dzim.pilot.gateway.api.vo.medical;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @author: zhouyibing
 * @date: 2024/9/12
 */
@Data
@MobileDo
public class MedicalCheckupReportRiskDetailDataVO implements Serializable {

    private String name;

    private String analysis;

    private Integer bodyPart;

    private String bodyPartDesc;

    private Integer system;

    private String systemDesc;

    private MedicalCheckupReportCardVO indicatorCard;

    private List<MedicalCheckupReportCardVO> departmentCard;

    private MedicalCheckupReportCardVO questionCard;

}
