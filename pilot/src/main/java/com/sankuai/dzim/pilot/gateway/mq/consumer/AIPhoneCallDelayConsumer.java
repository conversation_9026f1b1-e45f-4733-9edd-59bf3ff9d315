package com.sankuai.dzim.pilot.gateway.mq.consumer;

import com.dianping.vc.mq.client.api.annotation.Consumer;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.sankuai.dzim.pilot.dal.entity.aiphonecall.AIPhoneCallDetailEntity;
import com.sankuai.dzim.pilot.dal.entity.aiphonecall.AIPhoneCallTaskEntity;
import com.sankuai.dzim.pilot.dal.pilotdao.aiphonecall.AIPhoneCallDetailDAO;
import com.sankuai.dzim.pilot.dal.pilotdao.aiphonecall.AIPhoneCallTaskDAO;
import com.sankuai.dzim.pilot.enums.AIPhoneCallDetailStatusEnum;
import com.sankuai.dzim.pilot.enums.AIPhoneCallSceneTypeEnum;
import com.sankuai.dzim.pilot.enums.AIPhoneCallTaskStatusEnum;
import com.sankuai.dzim.pilot.gateway.mq.data.AIPhoneCallDelayData;
import com.sankuai.dzim.pilot.process.aiphonecall.callback.AIPhoneCallbackProcessor;
import com.sankuai.dzim.pilot.process.aiphonecall.data.AIPhoneCallBackData;
import com.sankuai.dzim.pilot.process.aiphonecall.impl.AIPhoneCallStateManager;
import com.sankuai.xm.openplatform.api.entity.CallBackData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 执行延迟了的外呼
 */
@Component
@Slf4j
public class AIPhoneCallDelayConsumer {

    @Resource
    private AIPhoneCallStateManager stateManager;

    @Resource
    private AIPhoneCallDetailDAO callDetailDAO;

    @Resource
    private AIPhoneCallTaskDAO callTaskDAO;

    @Resource
    private AIPhoneCallbackProcessor callbackProcessor;

    @Consumer(nameSpace = "daozong", appKey = "com.sankuai.mim.pilot", group = "pilot.ai.phone.call.delay.consumer", topic = "pilot.ai.phone.call.delay.topic")
    public ConsumeStatus recvMessage(String mafkaMessage) {
        log.info("AIPhoneCallDelayConsumer receive msg = {}", mafkaMessage);
        AIPhoneCallDelayData callDelayData = JsonCodec.decode(mafkaMessage, AIPhoneCallDelayData.class);
        if (callDelayData == null) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        AIPhoneCallDetailEntity callDetailEntity = callDetailDAO.getById(callDelayData.getCallDetailId());
        if (callDetailEntity == null) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        // 提前终止
        AIPhoneCallTaskEntity callTaskEntity = callTaskDAO.getByTaskId(callDetailEntity.getTaskId());
        if (callTaskEntity.getStatus() == AIPhoneCallTaskStatusEnum.TERMINATED.getStatus()) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        // 执行延迟外呼明细
        stateManager.transitAICallDetailState(callDelayData.getCallDetailId(), AIPhoneCallDetailStatusEnum.PENDING, "执行延迟外呼");
        if (callDetailEntity.getStatus() == AIPhoneCallDetailStatusEnum.DELAY.getStatus()) {
            callbackProcessor.onDelayWakeUp(callTaskEntity.getId());
        }
        stateManager.transitAICallTaskState(callDetailEntity.getTaskId(), AIPhoneCallTaskStatusEnum.PROCESSING, "延迟外呼明细开始执行");
        return ConsumeStatus.CONSUME_SUCCESS;
    }

}
