package com.sankuai.dzim.pilot.process.search.data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

public class PromoItemM {

    /**
     * 优惠的ID
     */
    private long promoId;

    /**
     * 优惠的类型Id
     */
    private int promoTypeCode;

    /**
     * 优惠类型，如：商家立减
     */
    private String promoType;

    /**
     * 优惠描述
     */
    private String desc;

    /**
     * 优惠金额标签
     */
    private String promoTag;

    /**
     * 优惠价格
     */
    private BigDecimal promoPrice;

    /**
     * 是否新客
     */
    private boolean isNewUser;

    /**
     * 优惠类型（新老客）https://km.sankuai.com/collabpage/1651440412#id-PromoDTO
     */
    private String promoNewOldIdentity;

    /**
     * 是否可领优惠
     */
    private boolean canAssign;

    /**
     * 使用的优惠类型列表，一条优惠条目可能是多个优惠融合而成, com.sankuai.dztheme.generalproduct.enums.PromoItemTypeEnum
     */
    private List<Integer> usedPromoTypes;

    /**
     * 券金额门槛,优惠弹窗使用
     */
    private String priceLimitDesc;

    /**
     * 券使用时间描述，优惠弹窗使用
     */
    private String useTimeDesc;

    /**
     * 优惠来源类型
     */
    private int sourceType;

    /**
     * 优惠折扣
     */
    private BigDecimal promoDiscount;

    /**
     * 优惠图标
     */
    private String icon;

    /**
     * 优惠标识，当数据源为价格服务时透传价格服务的标识，其他业务可自定义
     */
    private String promoIdentity;

    /**
     * 券批次id
     */
    private String couponGroupId;

    /**
     * 神券领用状态  NO_STATUS(1, "无领用状态"),ASSIGNED(2, "已领"),UN_ASSIGNED(3, "未领");
     */
    private Integer couponAssignStatus;

    /**
     * COUPON_PURCHASE(1, "领券购");
     */
    private Integer promoUseType;

    /**
     * 优惠展示类型
     */
    private String promoShowType;

    /**
     * 优惠名称，用于弹层
     */
    private String promoName;

    /**
     * 优惠解释性标签, 从营销查询优惠策略（commonTag）映射而来，
     * 枚举类参考：com.sankuai.dealuser.price.display.api.enums.PromotionExplanatoryTagEnum
     */
    private List<Integer> promotionExplanatoryTags;

    /**
     * 透传的营销扩展信息
     * key枚举类：com.sankuai.nibmkt.promotion.api.common.enums.PromotionPropertyEnum
     */
    private Map<String,String> promotionOtherInfoMap;

    /**
     * 券id
     */
    private String couponId;

    /**
     * 优惠文案展示相关信息
     */
    private PromoItemTextM promoItemTextM;

    /**
     * 基础券金额（元）
     */
    private BigDecimal amount;

    /**
     * 券门槛金额（元）
     */
    private BigDecimal minConsumptionAmount;

    /**
     * 透传代理的营销展示文案扩展信息（角标文案）
     * key枚举参考：
     * com.sankuai.nibmkt.promotion.api.common.enums.PromotionTextEnum
     */
    private Map<String, String> promotionDisplayTextMap;

    /**
     * 优惠开始时间
     */
    private Date startTime;

    /**
     * 优惠结束时间
     */
    private Date endTime;

    /**
     * 优惠标题
     */
    private String couponTitle;

    public BigDecimal getPromoDiscount() {
        return promoDiscount;
    }

    public void setPromoDiscount(BigDecimal promoDiscount) {
        this.promoDiscount = promoDiscount;
    }

    public List<Integer> getUsedPromoTypes() {
        return usedPromoTypes;
    }

    public void setUsedPromoTypes(List<Integer> usedPromoTypes) {
        this.usedPromoTypes = usedPromoTypes;
    }

    public int getSourceType() {
        return sourceType;
    }

    public void setSourceType(int sourceType) {
        this.sourceType = sourceType;
    }

    public boolean isNewUser() {
        return isNewUser;
    }

    public void setNewUser(boolean newUser) {
        isNewUser = newUser;
    }

    public boolean isCanAssign() {
        return canAssign;
    }

    public void setCanAssign(boolean canAssign) {
        this.canAssign = canAssign;
    }

    public String getPromoType() {
        return promoType;
    }

    public void setPromoType(String promoType) {
        this.promoType = promoType;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getPromoTag() {
        return promoTag;
    }

    public void setPromoTag(String promoTag) {
        this.promoTag = promoTag;
    }

    public BigDecimal getPromoPrice() {
        return promoPrice;
    }

    public void setPromoPrice(BigDecimal promoPrice) {
        this.promoPrice = promoPrice;
    }

    public long getPromoId() {
        return promoId;
    }

    public void setPromoId(long promoId) {
        this.promoId = promoId;
    }

    public int getPromoTypeCode() {
        return promoTypeCode;
    }

    public void setPromoTypeCode(int promoTypeCode) {
        this.promoTypeCode = promoTypeCode;
    }

    public String getPriceLimitDesc() {
        return priceLimitDesc;
    }

    public void setPriceLimitDesc(String priceLimitDesc) {
        this.priceLimitDesc = priceLimitDesc;
    }

    public String getUseTimeDesc() {
        return useTimeDesc;
    }

    public void setUseTimeDesc(String useTimeDesc) {
        this.useTimeDesc = useTimeDesc;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getIcon() {
        return icon;
    }
    
    public void setPromoIdentity(String promoIdentity) {
        this.promoIdentity = promoIdentity;
    }

    public String getPromoIdentity() {
        return promoIdentity;
    }

    public void setPromoNewOldIdentity(String promoNewOldIdentity) {
        this.promoNewOldIdentity = promoNewOldIdentity;
    }

    public String getPromoNewOldIdentity() {
        return promoNewOldIdentity;
    }

    public String getCouponGroupId() {
        return couponGroupId;
    }

    public void setCouponGroupId(String couponGroupId) {
        this.couponGroupId = couponGroupId;
    }

    public Integer getCouponAssignStatus() {
        return couponAssignStatus;
    }

    public void setCouponAssignStatus(Integer couponAssignStatus) {
        this.couponAssignStatus = couponAssignStatus;
    }

    public Integer getPromoUseType() {
        return promoUseType;
    }

    public void setPromoUseType(Integer promoUseType) {
        this.promoUseType = promoUseType;
    }

    public String getPromoShowType() {
        return promoShowType;
    }

    public void setPromoShowType(String promoShowType) {
        this.promoShowType = promoShowType;
    }

    public String getPromoName() {
        return promoName;
    }

    public void setPromoName(String promoName) {
        this.promoName = promoName;
    }

    public List<Integer> getPromotionExplanatoryTags() {
        return promotionExplanatoryTags;
    }

    public void setPromotionExplanatoryTags(List<Integer> promotionExplanatoryTags) {
        this.promotionExplanatoryTags = promotionExplanatoryTags;
    }

    public Map<String, String> getPromotionOtherInfoMap() {
        return promotionOtherInfoMap;
    }

    public void setPromotionOtherInfoMap(Map<String, String> promotionOtherInfoMap) {
        this.promotionOtherInfoMap = promotionOtherInfoMap;
    }

    public String getCouponId() {
        return couponId;
    }

    public void setCouponId(String couponId) {
        this.couponId = couponId;
    }

    public PromoItemTextM getPromoItemTextM() {
        return promoItemTextM;
    }

    public void setPromoItemTextM(PromoItemTextM promoItemTextM) {
        this.promoItemTextM = promoItemTextM;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getMinConsumptionAmount() {
        return minConsumptionAmount;
    }

    public void setMinConsumptionAmount(BigDecimal minConsumptionAmount) {
        this.minConsumptionAmount = minConsumptionAmount;
    }

    public Map<String, String> getPromotionDisplayTextMap() {
        return promotionDisplayTextMap;
    }

    public void setPromotionDisplayTextMap(Map<String, String> promotionDisplayTextMap) {
        this.promotionDisplayTextMap = promotionDisplayTextMap;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getCouponTitle() {
        return couponTitle;
    }

    public void setCouponTitle(String couponTitle) {
        this.couponTitle = couponTitle;
    }
}
