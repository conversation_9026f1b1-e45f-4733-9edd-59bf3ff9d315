package com.sankuai.dzim.pilot.gateway.api.medical;

import com.dianping.gateway.enums.APIModeEnum;
import com.dianping.vc.web.api.API;
import com.dianping.vc.web.api.URL;
import com.dianping.vc.web.api.VCAssert;
import com.dianping.vc.web.biz.client.api.MTUserIdAware;
import com.dianping.vc.web.biz.client.api.UserIdAware;
import com.sankuai.dzim.pilot.process.MedicalCheckupProcessService;
import com.sankuai.dzim.pilot.utils.UserIdUtils;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 体检报告上传
 * @author: zhouyibing
 * @date: 2024/9/6
 */
@Setter
@Slf4j
@Service
@URL(url = "/dzim/pilot/medical/checkup/report/upload", mode = APIModeEnum.CLIENT, methods = "POST")
public class MedicalCheckupReportUploadAPI implements API, UserIdAware, MTUserIdAware {

    private long mTUserId;

    private long userId;

    private int platform;

    private String reporturl;

    @Autowired
    private MedicalCheckupProcessService medicalCheckupProcessService;

    @Override
    public Object execute() {
        VCAssert.isTrue(StringUtils.isNotBlank(reporturl), API.INVALID_PARAM_CODE, "报告链接不能为空");

        String imUserId = UserIdUtils.getImUserId(userId, mTUserId, platform);
        VCAssert.isTrue(StringUtils.isNotBlank(imUserId), API.UNLOGIN_CODE, "未登陆");

        return medicalCheckupProcessService.uploadCheckupReport(imUserId, reporturl);
    }
}
