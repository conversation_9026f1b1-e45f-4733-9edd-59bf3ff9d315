package com.sankuai.dzim.pilot.process.aibook;

import com.sankuai.dzim.pilot.process.aibook.data.UserBookRequest;
import com.sankuai.dzim.pilot.process.localplugin.param.HaircutBookConfirmParam;
import com.sankuai.dzim.pilot.process.aireservebook.data.UserReserveInfo;

/**
 * <AUTHOR>
 * @since 2025/02/26 10:14
 */
public interface AIBookCacheProcessService {

    /**
     * 设置缓存
     * @param key
     * @param userBookRequest
     */
    void setBookCache(String platformUserid, UserBookRequest userBookRequest);

    /**
     * 获取缓存
     * @param key
     * @return
     */
    UserBookRequest getBookCache(String platformUserid);

    /**
     * 判断缓存是否存在
     * @param key
     * @return
     */
    Boolean containsBookCache(String platformUserid);

    /**
     * 判断是否包含原始预约信息
     * @param imUserid
     * @return
     */
    boolean containsOriginBookInfo(String imUserid);

    /**
     * 获取原始预约信息
     * @param imUserId
     * @return
     */
    HaircutBookConfirmParam getOriginBookInfo(String imUserId);

    /**
     * 设置原始预约信息
     * @param userId
     * @param cacheBookInfo
     */
    void setOriginBookInfo(String userId, HaircutBookConfirmParam cacheBookInfo);

    /**
     * 判断是否包含Agent预约预订消息
     * @param imUserId
     * @param shopId
     * @param platform
     * @return
     */
    boolean containsAgentReserveAndBookInfo(String imUserId, Long shopId, int platform);

    /**
     * 获取Agent预约预订消息
     * @param imUserId
     * @param shopId
     * @param platform
     * @return
     */
    UserReserveInfo getAgentReserveAndBookInfo(String imUserId, Long shopId, int platform);

    /**
     * 设置Agent预约预订消息
     * @param imUserId
     * @param shopId
     * @param platform
     * @param userReserveInfo
     */
    void setAgentReserveAndBookInfo(String imUserId, Long shopId, int platform, UserReserveInfo userReserveInfo);

    /**
     * 删除Agent预约预订消息
     * @param imUserId
     * @param shopId
     * @param platform
     */
    void deleteAgentReserveAndBookInfo(String imUserId, Long shopId, int platform);

    /**
     * 判断是否包含Beam子Agent预约预订消息
     * @param imUserId
     * @param shopId
     * @param platform
     * @return
     */
    boolean containsBeamAgentReserveAndBookInfo(String imUserId, Long shopId, int platform);

    /**
     * 获取Beam子Agent预约预订消息
     * @param imUserId
     * @param shopId
     * @param platform
     * @return
     */
    UserReserveInfo getBeamAgentReserveAndBookInfo(String imUserId, Long shopId, int platform);

    /**
     * 设置Beam子Agent预约预订消息
     * @param imUserId
     * @param shopId
     * @param platform
     * @param userReserveInfo
     */
    void setBeamAgentReserveAndBookInfo(String imUserId, Long shopId, int platform, UserReserveInfo userReserveInfo);

    /**
     * 删除Beam子Agent预约预订消息
     * @param imUserId
     * @param shopId
     * @param platform
     */
    void deleteBeamAgentReserveAndBookInfo(String imUserId, Long shopId, int platform);
}
