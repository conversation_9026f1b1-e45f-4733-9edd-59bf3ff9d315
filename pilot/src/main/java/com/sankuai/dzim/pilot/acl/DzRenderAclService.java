package com.sankuai.dzim.pilot.acl;

import com.sankuai.dzshoplist.aggregate.dzrender.request.DzRenderRequest;
import com.sankuai.dzshoplist.aggregate.dzrender.response.ProductRenderItem;
import com.sankuai.dzshoplist.aggregate.dzrender.response.ShopRenderItem;
import com.sankuai.dzshoplist.aggregate.dzrender.response.TechnicianRenderItem;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 到综搜推渲染服务
 * <AUTHOR>
 */
public interface DzRenderAclService {

    /**
     * 查询门店渲染信息
     * @param request request
     * @return shopRenderItemMap
     */
    CompletableFuture<Map<Long, ShopRenderItem>> queryShopRenderInfo(DzRenderRequest request);

    /**
     * 查询商品渲染信息
     * @param request request
     * @return productRenderItemMap
     */
    CompletableFuture<Map<Long, ProductRenderItem>> queryProductRenderInfo(DzRenderRequest request);


    /**
     * 查询手艺人渲染信息
     * @param request request
     * @return productRenderItemMap
     */
    CompletableFuture<Map<Long, TechnicianRenderItem>> queryTechnicianRenderInfo(DzRenderRequest request);
}
