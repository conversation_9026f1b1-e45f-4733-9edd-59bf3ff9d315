package com.sankuai.dzim.pilot.gateway.api.aibook;


import com.dianping.vc.web.api.API;
import com.dianping.vc.web.api.URL;
import com.dianping.vc.web.api.VCAssert;
import com.dianping.vc.web.biz.client.api.AppContext;
import com.dianping.vc.web.biz.client.api.AppContextAware;
import com.dianping.vc.web.biz.client.api.MTUserIdAware;
import com.dianping.vc.web.biz.client.api.UserIdAware;
import com.google.common.collect.Maps;
import com.sankuai.dzim.pilot.api.enums.assistant.PlatformEnum;
import com.sankuai.dzim.pilot.gateway.api.aibook.data.AIBookSubmitResultVO;
import com.sankuai.dzim.pilot.process.aibook.AIBookProcessService;
import com.sankuai.dzim.pilot.process.aibook.data.AIBookSubmitRequest;
import com.sankuai.dzim.pilot.process.aibook.data.AIBookTypeEnum;
import com.sankuai.dzim.pilot.process.aibook.data.BookPageFixedContentConfigData;
import com.sankuai.dzim.pilot.utils.CatUtils;
import com.sankuai.dzim.pilot.utils.LionConfigUtil;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

@Slf4j
@Service
@URL(url = "/dzim/pilot/ai/book/submit", methods = "POST")
public class AIBookSubmitMAPI implements API, UserIdAware, MTUserIdAware, AppContextAware {

    @Setter
    private AppContext appContext;

    @Setter
    private long userId;

    @Setter
    private long mTUserId;

    @Setter
    private int cityid;

    @Setter
    private long shopid;

    @Setter
    private String shopids;

    @Setter
    private int timeperiodid;

    @Setter
    private int recommendswitch;

    @Setter
    private String userphone;

    @Setter
    private int booktype;

    @Setter
    private String navifilterid;

    @Setter
    private String naviname;

    @Setter
    private int navitype;

    @Setter
    private double lng;

    @Setter
    private double lat;

    @Setter
    private String version;

    @Setter
    private long bookstarttime;

    @Setter
    private long bookendtime;

    @Setter
    private Integer priceid;

    @Resource
    private AIBookProcessService aiBookProcessService;

    @Resource
    private LionConfigUtil lionConfigUtil;

    /**
     * 预约提交成功的结果码
     */
    public static final int BOOK_SUBMIT_SUCC_CODE = 1;

    /**
     * 预约提交失败的结果码
     */
    public static final int BOOK_SUBMIT_FAIL_CODE = 2;

    /**
     * 重复提交的内部错误码
     */
    public static final int BOOK_SUBMIT_FAIL_DUPLICATE_SUBMIT_CODE = 2;

    /**
     * 预约次数达上限的内部错误码
     */
    public static final int BOOK_SUBMIT_FAIL_REACH_MAX_USAGE_CODE = 3;

    /**
     * 系统异常的内部错误码
     */
    public static final int BOOK_SUBMIT_FAIL_SYSTEM_EXCEPTION_CODE = 4;


    /**
     * 预约时间范围错误码
     */
    public static final int BOOK_TIME_RANGE_EXCEPTION_CODE = 5;

    /**
     * 预约时间段不存在错误码
     */
    public static final int BOOK_TIME_NOT_EXIST_CODE = 6;

    /**
     * 召回门店为空
     */
    public static final int BOOK_SUBMIT_SHOP_EMPTY = 7;

    /**
     * 用户停留时间过长
     */
    public static final int BOOK_SUBMIT_STAY_TOO_LONG = 8;

    @Override
    public Object execute() {
        // 校验参数
        validateParams();

        // 提交预约
        AIBookSubmitRequest aiBookSubmitRequest = buildAIBookSubmitRequest();
        int submitResultCode = aiBookProcessService.submitAIBook(aiBookSubmitRequest);

        // 水位打点
        logMetricSubmitResult(submitResultCode);
        return buildAIBookSubmitResultVO(submitResultCode);
    }

    private void logMetricSubmitResult(int innerSubmitResultCode) {
        Map<String, String> tags = Maps.newHashMap();
        tags.put("code", String.valueOf(innerSubmitResultCode));
        CatUtils.logMetricResult("SubmitAIBook", tags);
    }

    private Object buildAIBookSubmitResultVO(int submitResultCode) {
        if (submitResultCode == BOOK_SUBMIT_SUCC_CODE) {
            return AIBookSubmitResultVO.builder().submitResultType(BOOK_SUBMIT_SUCC_CODE).build();
        }

        String errorMsg = "";
        BookPageFixedContentConfigData fixedContentConfigData = lionConfigUtil.getBookPageFixedContentConfig();
        if (submitResultCode == BOOK_SUBMIT_FAIL_DUPLICATE_SUBMIT_CODE) {
            errorMsg = fixedContentConfigData.getDuplicateSubmitTip();
        } else if (submitResultCode == BOOK_SUBMIT_FAIL_REACH_MAX_USAGE_CODE) {
            errorMsg = String.format(fixedContentConfigData.getUserReachMaxUsageTip(), lionConfigUtil.getUserMaxDailyUsageCount());
        } else if (submitResultCode == BOOK_TIME_RANGE_EXCEPTION_CODE) {
            errorMsg = fixedContentConfigData.getBookTimeRangeExceptionTip();
        } else if (submitResultCode == BOOK_TIME_NOT_EXIST_CODE) {
            errorMsg = fixedContentConfigData.getBookTimeNotExistExceptionTip();
        } else if (submitResultCode == BOOK_SUBMIT_SHOP_EMPTY) {
            errorMsg = fixedContentConfigData.getSubmitBookShopEmptyTip();
        }else if(submitResultCode == BOOK_SUBMIT_STAY_TOO_LONG){
            errorMsg = fixedContentConfigData.getSubmitUserStayTooLongTip();
        }else {
            errorMsg = fixedContentConfigData.getSubmitBookFailTip();
        }
        return AIBookSubmitResultVO.builder().submitResultType(submitResultCode).errorMsg(errorMsg).build();
    }

    private AIBookSubmitRequest buildAIBookSubmitRequest() {
        AIBookSubmitRequest request = new AIBookSubmitRequest();
        request.setUserId(appContext.getPlatform().equals(PlatformEnum.DP.getName()) ? userId : mTUserId);
        request.setPlatform(PlatformEnum.getByName(appContext.getPlatform()).getType());
        request.setCityId(cityid);
        request.setShopId(shopid);
        request.setShopIds(shopids);
        request.setTimePeriodId(timeperiodid);
        request.setRecommendSwitch(shopid != 0 ? recommendswitch : 1);
        request.setBookType(booktype);
        request.setNaviFilterId(navifilterid);
        request.setNaviType(navitype);
        request.setDeviceId(appContext.getDpid());
        request.setUserPhone(userphone);
        request.setNaviName(naviname);
        request.setLng(lng);
        request.setLat(lat);
        request.setVersion(StringUtils.defaultString(version, "0"));
        request.setBookStartTime(bookstarttime);
        request.setBookEndTime(bookendtime);
        request.setPriceId(priceid);
        return request;
    }

    private void validateParams() {
        VCAssert.isTrue(userId != 0 || mTUserId != 0, API.UNLOGIN_CODE, "用户未登录");
        VCAssert.isTrue(timeperiodid != 0, API.INVALID_PARAM_CODE, "预约时间段不能为空");
        VCAssert.isTrue(AIBookTypeEnum.getEnum(booktype) != null, API.INVALID_PARAM_CODE, "无效预约项目");
        VCAssert.isTrue(appContext.getPlatform().equals(PlatformEnum.DP.getName()) || appContext.getPlatform().equals(PlatformEnum.MT.getName()), API.INVALID_PARAM_CODE, "平台参数不合法");
    }

}
