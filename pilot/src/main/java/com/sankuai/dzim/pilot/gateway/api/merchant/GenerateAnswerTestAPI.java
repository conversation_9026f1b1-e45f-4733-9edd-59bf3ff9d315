package com.sankuai.dzim.pilot.gateway.api.merchant;

import com.dianping.gateway.enums.APIModeEnum;
import com.dianping.vc.web.api.API;
import com.dianping.vc.web.api.URL;
import com.dianping.vc.web.biz.merchant.api.ShopAccountIdAware;
import com.sankuai.dzim.pilot.process.GenerativeSearchProcessService;
import com.sankuai.dzim.pilot.process.data.GenerativeSearchGenerateReq;
import com.sankuai.dzim.pilot.process.data.QuestionTypeEnum;
import com.sankuai.dzim.pilot.process.impl.GenerativeSearchProcessServiceImpl;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Setter
@Slf4j
@Service
@URL(url = "/dzim/pilot/merchant/generate/answer", mode = APIModeEnum.MERCHANT)
public class GenerateAnswerTestAPI implements API, ShopAccountIdAware {

    private int shopAccountId;

    /**
     * im商家账号id，此处为im门店id，如s1234
     */
    private int bizType;

    /**
     * im用户id，此处为im用户id，如im点评用户id，u1234，美团用户id，m1234
     */
    private String question;

    private int dataType;

    @Autowired
    private GenerativeSearchProcessServiceImpl generativeSearchProcessService;

    @Override
    public Object execute() {
        GenerativeSearchGenerateReq generativeSearchGenerateReq = new GenerativeSearchGenerateReq();
        generativeSearchGenerateReq.setQuestion(question);
        generativeSearchGenerateReq.setBizType(bizType);
        generativeSearchGenerateReq.setQuestionType(QuestionTypeEnum.QUERY.getCode());
        return generativeSearchProcessService.getAnswer(generativeSearchGenerateReq);
    }
}