package com.sankuai.dzim.pilot.process.localplugin;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.dzim.message.common.utils.ImAccountTypeUtils;
import com.sankuai.dzim.pilot.api.data.AIAnswerTypeEnum;
import com.sankuai.dzim.pilot.buffer.core.PilotBufferItemDO;
import com.sankuai.dzim.pilot.buffer.enums.StreamEventCardTypeEnum;
import com.sankuai.dzim.pilot.buffer.stream.build.ext.data.ThinkProcessData;
import com.sankuai.dzim.pilot.buffer.utils.BufferUtils;
import com.sankuai.dzim.pilot.chain.data.AIAnswerData;
import com.sankuai.dzim.pilot.domain.annotation.LocalPlugin;
import com.sankuai.dzim.pilot.process.data.ProductRetrievalData;
import com.sankuai.dzim.pilot.process.data.ShopRetrievalData;
import com.sankuai.dzim.pilot.process.data.TechnicianData;
import com.sankuai.dzim.pilot.process.localplugin.data.UserContext;
import com.sankuai.dzim.pilot.process.localplugin.param.ShopMergeSearchParam;
import com.sankuai.dzim.pilot.process.search.SearchProcessService;
import com.sankuai.dzim.pilot.utils.LionConfigUtil;
import com.sankuai.dzim.pilot.utils.PluginContextUtil;
import com.sankuai.dzim.pilot.utils.SupplyUtils;
import com.sankuai.dzim.pilot.utils.UuidUtil;
import com.sankuai.dzshoplist.aggregate.dzrender.response.common.RecommendDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 商户搜索工具
 * 内部调用到综垂搜ms（融合查询）服务
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class ShopMergeSearchPlugin {

    @Resource
    private SearchProcessService searchProcessService;

    @Resource
    private SupplyUtils supplyUtils;

    @Autowired
    private LionConfigUtil lionConfigUtil;

    @ConfigValue(key = "com.sankuai.mim.pilot.search.result.mock", defaultValue = "{}")
    private Map<String, List<Long>> mockShopIdMap;

    @LocalPlugin(name = "ShopMergeSearchTool",
            description = "Search shops based on the given query conditions", returnDirect = false)
    public AIAnswerData searchShops(ShopMergeSearchParam param) {
        log.info("【调用商户shop搜索工具ShopMergeSearchPlugin】，入参：param: {}", JsonCodec.encodeWithUTF8(param));
        if (lionConfigUtil.getThinkProcessData("shopSearchResult") == null) {
            BufferUtils.writeStatusBuffer(PilotBufferItemDO.builder().data(getShowText(param)).build());
        }

        AIAnswerData answerData = new AIAnswerData();
        answerData.setAiAnswerType(AIAnswerTypeEnum.TEXT.getType());
        answerData.setAnswer("未找到相关商户, 请尝试重新调整符合用户需求的工具参数");
        if (!checkAndHandleParam(param)) {
            log.info("【调用商户shop搜索工具 ShopMergeSearchPlugin】 入参校验失败");
            return answerData;
        }

        List<ShopRetrievalData> shopRetrievalData = searchProcessService.searchShops(param);
        sendFindShopThinkProcess(param, shopRetrievalData);

        if (MapUtils.isNotEmpty(mockShopIdMap)) {
            String catName = "丽人";
            if (CollectionUtils.isNotEmpty(shopRetrievalData)) {
                catName = shopRetrievalData.get(0).getMtCateName0();
            }
            List<Long> mockShopIds = mockShopIdMap.get(catName);
            if (CollectionUtils.isNotEmpty(mockShopIds)) {
                shopRetrievalData = mockShopRetrievalData(shopRetrievalData, mockShopIds);
            }
        }

        String searchAnswer = JsonCodec.encodeWithUTF8(shopRetrievalData);
        String markdown = toMarkdown(shopRetrievalData, param);
        log.info("【调用商户shop搜索工具ShopMergeSearchPlugin】，结果：{} markdown: {}", searchAnswer, markdown);
        answerData.setAiAnswerType(AIAnswerTypeEnum.TEXT.getType());
        answerData.setAnswer(markdown);
        return answerData;
    }

    private List<ShopRetrievalData> mockShopRetrievalData(List<ShopRetrievalData> shopRetrievalData, List<Long> mockShopIds) {
        if (CollectionUtils.isEmpty(mockShopIds)) {
            return Lists.newArrayList();
        }

        for (int i = 0; i < Math.min(shopRetrievalData.size(), mockShopIds.size()); ++i) {
            shopRetrievalData.get(i).setMtShopId(mockShopIds.get(i));
        }
        return shopRetrievalData.subList(0, Math.min(shopRetrievalData.size(), 3));
    }

    private boolean checkAndHandleParam(ShopMergeSearchParam shopMergeSearchParam) {
        if (shopMergeSearchParam == null) {
            return false;
        }

        if (StringUtils.isBlank(shopMergeSearchParam.getKeyword())) {
            return false;
        }

        if (StringUtils.isEmpty(shopMergeSearchParam.getShopSemanticCondition())) {
            shopMergeSearchParam.setShopSemanticCondition("");
        }

        if (shopMergeSearchParam.getSort() == null || shopMergeSearchParam.getSort() < 0 || shopMergeSearchParam.getSort() > 5) {
            shopMergeSearchParam.setSort(1);
        }

        if (shopMergeSearchParam.getPageNo() == null || shopMergeSearchParam.getPageNo() <= 0) {
            shopMergeSearchParam.setPageNo(1);
        }

        return true;
    }


    private String toMarkdown(List<ShopRetrievalData> shopRetrievalData, ShopMergeSearchParam param) {
        if (CollectionUtils.isEmpty(shopRetrievalData)) {
            return "未找到相关商户";
        }
        UserContext userContext = PluginContextUtil.getUserContext(param);
        boolean isMt = ImAccountTypeUtils.isMtUserId(userContext.getImUserId());

        StringBuilder markdown = new StringBuilder();
        // 使用字段注释构建表头
        markdown.append("| 商户名称 | shopId | 星级 | 人均消费价格 | 品牌名称 | 一级类目 | 二级类目 | 三级类目 | 推荐理由 | 商户地址 | 商圈 | 地铁站 | 店内商品 | 用户评价 | 店内手艺人 |\n");
        markdown.append("|--------|--------|------|-----------|---------|---------|--------|----------|---------|--------|-----|-------|---------|--------|----------|\n");

        // 构建表格内容
        for (ShopRetrievalData shop : shopRetrievalData) {
            markdown.append(String.format("| %s | %d | %d | %d | %s | %s | %s | %s | %s | %s | %s | %s | %s | %s | %s |\n",
                    supplyUtils.escapeMarkdown(shop.getShopName()),
                    isMt ? shop.getMtShopId() : shop.getDpShopId(),
                    shop.getMtAvgScore(),
                    shop.getMtAvgPrice(),
                    supplyUtils.escapeMarkdown(shop.getBrandName()),
                    supplyUtils.escapeMarkdown(shop.getMtCateName0()),
                    supplyUtils.escapeMarkdown(shop.getMtCateName1()),
                    supplyUtils.escapeMarkdown(shop.getMtCateName2()),
                    supplyUtils.escapeMarkdown(buildRecommends(shop.getRecommends())),
                    supplyUtils.escapeMarkdown(shop.getShopAddress()),
                    supplyUtils.escapeMarkdown(shop.getMtRegionName()),
                    supplyUtils.escapeMarkdown(shop.getSubwayStation()),
                    supplyUtils.escapeMarkdown(buildProductsMarkdown(shop.getRelatedProducts(), isMt)),
                    supplyUtils.escapeMarkdown(buildReviewsMarkdown(shop.getReviews())),
                    supplyUtils.escapeMarkdown(buildTechniciansMarkdown(shop.getTechnicians()))
            ));
        }

        return markdown.toString();
    }

    private String buildRecommends(List<RecommendDTO> recommends) {
        if (CollectionUtils.isEmpty(recommends)) {
            return "无";
        }

        StringBuilder markdown = new StringBuilder();
        int idx = 1;
        for (RecommendDTO recommend : recommends) {
            if (recommend == null) {
                continue;
            }
            String recommendStr = String.format("%s", recommend.getText());

            markdown.append("（").append(idx).append("）").append(recommendStr).append("； ");
            idx++;
        }
        return markdown.toString();
    }

    private String buildProductsMarkdown(List<ProductRetrievalData> relatedProducts, boolean isMt) {
        if (CollectionUtils.isEmpty(relatedProducts)) {
            return "无";
        }

        StringBuilder markdown = new StringBuilder();
        int idx = 1;
        for (ProductRetrievalData product : relatedProducts) {
            if (product == null) {
                continue;
            }
            String productStr = String.format("productId: %s，名称：%s，价格： %s, 销量： %d，服务项目： %s",
                    isMt ? product.getMtProductId() : product.getDpProductId(),
                    supplyUtils.escapeMarkdown(product.getTitle()),
                    product.getSalePrice(),
                    product.getSalesNum(),
                    supplyUtils.escapeMarkdown(supplyUtils.buildServiceProject(product))
//                    supplyUtils.escapeMarkdown(supplyUtils.buildIntroduction(product))
            );
            markdown.append("（").append(idx).append("）").append(productStr).append("； ");
            idx++;
        }

        return markdown.toString();
    }

    private String buildReviewsMarkdown(List<String> reviews) {
        if (CollectionUtils.isEmpty(reviews)) {
            return "无";
        }
        StringBuilder markdown = new StringBuilder();
        int idx = 1;
        for (String review : reviews) {
            if (StringUtils.isEmpty(review)) {
                continue;
            }
            markdown.append("（").append(idx).append("）").append(review).append("； ");
            idx++;
        }
        return markdown.toString();
    }

    private String buildTechniciansMarkdown(List<TechnicianData> technicians) {
        if (CollectionUtils.isEmpty(technicians)) {
            return "无";
        }
        StringBuilder markdown = new StringBuilder();
        int idx = 1;
        for (TechnicianData technician : technicians) {
            if (technician == null) {
                continue;
            }
            String techStr = String.format("手艺人： %s， 岗位：%s， 好评数： %s， 服务人次： %s， 标签： %s",
                    technician.getName(),
                    StringUtils.isNotEmpty(technician.getTitle()) ? technician.getTitle() : "",
                    StringUtils.isNotEmpty(technician.getLikesTitle()) ? technician.getLikesTitle() : "",
                    StringUtils.isNotEmpty(technician.getSellNum()) ? technician.getSellNum() : "",
                    StringUtils.isNotEmpty(technician.getFeatureTag()) ? technician.getFeatureTag() : ""
            );
            markdown.append("（").append(idx).append("）").append(techStr).append("； ");
            idx++;
        }
        return markdown.toString();
    }

    private void sendFindShopThinkProcess(ShopMergeSearchParam param, List<ShopRetrievalData> shopRetrievalData) {
        ThinkProcessData shopSearchResult = lionConfigUtil.getThinkProcessData("shopSearchResult");
        if (shopSearchResult != null && CollectionUtils.isNotEmpty(shopRetrievalData)) {
            int reviewSum = shopRetrievalData.stream().map(data -> {
                if (CollectionUtils.isNotEmpty(data.getReviews())) {
                    return data.getReviews().size();
                }
                return 0;
            }).mapToInt(Integer::intValue).sum();
            String showText = StringUtils.isNotBlank(param.getShowText()) ? param.getShowText() : "相关门店";
            String finishText = String.format(shopSearchResult.getFinishText(), shopRetrievalData.size(), showText, reviewSum);

            BufferUtils.writeThinkProcessBuffer(PilotBufferItemDO.builder()
                    .data(StreamEventCardTypeEnum.buildCardContent(StreamEventCardTypeEnum.THINK_PROCESS_CARD, UuidUtil.getRandomCardKey()))
                    .extra(shopSearchResult.toMap(finishText))
                    .build());
        } else {
            if (CollectionUtils.isNotEmpty(shopRetrievalData) && shopRetrievalData.size() > 3) {
                String shopNames = shopRetrievalData.stream().limit(3).map(ShopRetrievalData::getShopName).collect(Collectors.joining("、"));
                BufferUtils.writeStatusBuffer(PilotBufferItemDO.builder().data(String.format("一共找到了%s等%s家门店, 正在为您进一步筛选...", shopNames, shopRetrievalData.size())).build());
            }
        }
    }

    private String getShowText(ShopMergeSearchParam param) {
        if(param == null || StringUtils.isEmpty(param.getShowText())){
            return "正在为你检索...";
        }
        return param.getShowText();
    }

}
