package com.sankuai.dzim.pilot.domain.plugin.executor.impl;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.domain.plugin.executor.PluginExecutor;
import com.sankuai.dzim.pilot.domain.plugin.executor.data.PluginExecutionRequest;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
public class HttpPluginExecutor implements PluginExecutor {

    /**
     * http客户端,每个插件的超时时间可能不一样,所以不能共用一个client
     */
    private final OkHttpClient httpClient;

    /**
     * http地址
     */
    private final String url;

    /**
     * 是否是post请求
     */
    private final Boolean isPost;

    public HttpPluginExecutor(String url, Boolean isPost, long timeout) {
        this.url = url;
        this.isPost = isPost;
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(timeout, TimeUnit.MILLISECONDS)
                .readTimeout(timeout, TimeUnit.MILLISECONDS)
                .writeTimeout(timeout, TimeUnit.MILLISECONDS)
                .build();
    }

    @Override
    public String execute(PluginExecutionRequest request) {
        LogUtils.logRequestLog(log, TagContext.builder().action("HttpPluginExecutor").build(), request.getName(), request);

        // get请求
        if (!isPost) {
            return executeGet(request);
        }

        // post请求
        return executePost(request);
    }

    private String executeGet(PluginExecutionRequest request) {
        // 构造get Request对象
        Request httpRequest = buildGetRequest(request.getParams());

        return doExecute(request, httpRequest);
    }

    private String executePost(PluginExecutionRequest request) {
        // 构造post Request对象
        Request httpRequest = buildPostRequest(request.getParams());

        return doExecute(request, httpRequest);
    }

    private String doExecute(PluginExecutionRequest request, Request httpRequest) {
        try {
            // 执行请求并获取响应
            Response response = httpClient.newCall(httpRequest).execute();

            // 检查响应是否成功
            if (!response.isSuccessful() || response.body() == null) {
                LogUtils.logFailLog(log, TagContext.builder().action("HttpPluginExecutor:fail").bizId(request.getName()).build()
                        , new WarnMessage("HttpPluginExecutor", "http插件执行失败", request.getName())
                        , request, new Object[]{JsonCodec.encodeWithUTF8(response), response.code(), response.message()});
                return "调用失败";
            }

            String responseText = response.body().string();
            LogUtils.logReturnInfo(log, TagContext.builder().action("HttpPluginExecutor:success").bizId(request.getName()).build()
                    , new WarnMessage("HttpPluginExecutor", "http插件执行成功", request.getName())
                    , request, responseText);
            // 获取响应
            return responseText;
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("HttpPluginExecutor:error").bizId(request.getName()).build(),
                    new WarnMessage("HttpPluginExecutor", "http插件执行异常",
                            request.getName()), request, null, e);
        }
        return "调用失败";
    }

    private Request buildGetRequest(String params) {
        HttpUrl.Builder urlBuilder = HttpUrl.parse(url).newBuilder();
        Map<String, Object> paramsMap = parseParams(params);
        if (MapUtils.isEmpty(paramsMap)) {
            return new Request.Builder()
                    .url(urlBuilder.build().toString())
                    .build();
        }

        for (Map.Entry<String, Object> param : paramsMap.entrySet()) {
            urlBuilder.addQueryParameter(param.getKey(), JsonCodec.encodeWithUTF8(param.getValue()));
        }

        return new Request.Builder()
                .url(urlBuilder.build().toString())
                .build();
    }

    private Map<String, Object> parseParams(String params) {
        if (StringUtils.isBlank(params)) {
            return new HashMap<>();
        }

        return JsonCodec.converseMap(params, String.class, Object.class);
    }

    private Request buildPostRequest(String params) {
        Map<String, Object> paramsMap = parseParams(params);
        if (MapUtils.isEmpty(paramsMap)) {
            return new Request.Builder()
                    .url(url)
                    .build();
        }

        // 构造FormBody
        FormBody.Builder formBuilder = new FormBody.Builder();
        for (Map.Entry<String, Object> param : paramsMap.entrySet()) {
            formBuilder.add(param.getKey(), JsonCodec.encodeWithUTF8(param.getValue()));
        }

        // 构造post Request对象
        return new Request.Builder()
                .url(url)
                .post(formBuilder.build())
                .build();
    }
}
