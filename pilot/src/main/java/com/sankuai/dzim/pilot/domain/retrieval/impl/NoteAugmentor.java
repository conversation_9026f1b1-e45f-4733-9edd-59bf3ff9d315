package com.sankuai.dzim.pilot.domain.retrieval.impl;

import com.sankuai.dzim.pilot.acl.FridayAclService;
import com.sankuai.dzim.pilot.dal.entity.NoteInfoEntity;
import com.sankuai.dzim.pilot.dal.respository.NoteRepositoryService;
import com.sankuai.dzim.pilot.domain.annotation.Retrieval;
import com.sankuai.dzim.pilot.domain.retrieval.RetrievalAugmentor;
import com.sankuai.dzim.pilot.domain.retrieval.data.RetrievalContext;
import com.sankuai.dzim.pilot.domain.retrieval.data.RetrievalResult;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.Comparator;
import java.util.List;

@Component
@Retrieval(name = "NoteRetrieval", desc = "笔记检索器")
public class NoteAugmentor implements RetrievalAugmentor {

    @Autowired
    private NoteRepositoryService noteRepositoryService;

    @Autowired
    private FridayAclService fridayAclService;

    @Override
    public String retrieval(RetrievalContext context) {
        Assert.isTrue(StringUtils.isNotBlank(context.getQuery()), "搜索关键词不能为空");
        Assert.isTrue(MapUtils.isNotEmpty(context.getExtraInfo()), "笔记检索参数不能为空");
        return retrievalBizKnowledge(context);
    }

    private String retrievalBizKnowledge(RetrievalContext context) {
        List<NoteInfoEntity> notes = noteRepositoryService.searchNoteInfo(context.getQuery(), context.getExtraInfo(), context.getTopK());
        if (CollectionUtils.isEmpty(notes)) {
            return StringUtils.EMPTY;
        }

        notes.sort(Comparator.comparing(NoteInfoEntity::getSimilarity).reversed());
        StringBuilder sb = new StringBuilder();
        sb.append("以下是你可以参考的笔记:\n");
        int index = 1;
        for (NoteInfoEntity note : notes) {
            sb.append("###第").append(index).append("篇笔记：\n")
                    .append("笔记标题：\n").append(note.getTitle()).append("\n\n")
                    .append("笔记内容：\n").append(note.getContent()).append("\n\n");
            index++;
        }
        return sb.toString();
    }

    @Override
    public RetrievalResult retrieveKnowledge(RetrievalContext context) {
        return null;
    }
}
