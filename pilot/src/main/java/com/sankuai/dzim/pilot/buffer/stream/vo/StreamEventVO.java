package com.sankuai.dzim.pilot.buffer.stream.vo;

import com.sankuai.dzim.pilot.api.enums.assistant.MessageTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/7/29 19:01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StreamEventVO {

    private String type;

    private Integer index;

    private StreamEventDataVO data;

}
