package com.sankuai.dzim.pilot.utils;

import com.dianping.gateway.dto.Cookie;
import com.dianping.vc.sdk.web.CookieUtils;
import com.dianping.vc.web.api.EnvContext;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.dzim.pilot.gateway.api.vo.UserVO;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * Created by FanJiangQi on 2021/7/12.
 * 解析sso账号
 */
@Component
public class SsoParseUtils {

    private static final String SSO_COOKIE_NAME_POSTFIX = "_ssoid";

    @ConfigValue(key = "com.sankuai.mim.pilot.sso.client", defaultValue = "")
    private String ssoClient;

    public int getSsoUserId(EnvContext envContext) {
        List<Cookie> cookies = envContext.getCookies();
        Cookie ssoCookie = cookies.stream()
                .filter(cookie -> cookie.getName().equals(ssoClient + SSO_COOKIE_NAME_POSTFIX))
                .findFirst().orElse(null);
        if (ssoCookie == null) {
            return 0;
        }
        return parseSsoUserId(ssoCookie.getValue());
    }

    public int parseSsoUserId(String ssoCookieValue) {
        User ssoUser = UserUtils.getUser(ssoCookieValue);
        if (ssoUser == null) {
            return 0;
        }
        return ssoUser.getId();
    }

    public User parseSsoUser(EnvContext envContext) {
        List<Cookie> cookies = envContext.getCookies();
        Cookie ssoCookie = cookies.stream()
                .filter(cookie -> cookie.getName().equals(ssoClient + SSO_COOKIE_NAME_POSTFIX))
                .findFirst().orElse(null);
        if (ssoCookie == null) {
            return null;
        }
        return UserUtils.getUser(ssoCookie.getValue());
    }

    public int getSsoUserId(HttpServletRequest request) {
        String ssoCookieValue = CookieUtils.getCookieValue(request, ssoClient + SSO_COOKIE_NAME_POSTFIX);
        return parseSsoUserId(ssoCookieValue);
    }

    public User getSsoUser(EnvContext envContext) {
        List<Cookie> cookies = envContext.getCookies();
        Cookie ssoCookie = cookies.stream()
                .filter(cookie -> cookie.getName().equals(ssoClient + SSO_COOKIE_NAME_POSTFIX))
                .findFirst().orElse(null);
        if (ssoCookie == null) {
            return null;
        }

        return UserUtils.getUser(ssoCookie.getValue());
    }

    public User getSsoUser(HttpServletRequest request) {
        String ssoCookieValue = CookieUtils.getCookieValue(request, ssoClient + SSO_COOKIE_NAME_POSTFIX);
        User ssoUser = UserUtils.getUser(ssoCookieValue);
        if (ssoUser == null) {
            return null;
        }

        return ssoUser;
    }
}
