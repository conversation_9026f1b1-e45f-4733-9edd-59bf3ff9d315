package com.sankuai.dzim.pilot.process.search.strategy.recall;

import com.sankuai.dzim.pilot.process.search.data.ReserveProductM;
import com.sankuai.dzim.pilot.process.search.data.ReserveQueryContext;

import java.util.List;


public interface ReserveProductRecallHandler {

    /**
     * 召回门店在线商品
     */
    List<ReserveProductM> recall(ReserveQueryContext queryContext);

    /**
     * 是否匹配当前策略
     */
    boolean match(ReserveQueryContext queryContext);
}
