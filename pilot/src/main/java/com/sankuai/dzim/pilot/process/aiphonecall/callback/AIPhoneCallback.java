package com.sankuai.dzim.pilot.process.aiphonecall.callback;

import com.sankuai.dzim.pilot.process.aiphonecall.data.AIPhoneCallBackData;

public interface AIPhoneCallback {

    boolean accept(AIPhoneCallBackData callBackData);

    /**
     * 外呼通话成功的回调。非业务含义上的成功
     * @param callBackData
     */
    void onSuccess(AIPhoneCallBackData callBackData);

    /**
     * 外呼通话失败的回调。
     * @param callBackData
     */
    void onFail(AIPhoneCallBackData callBackData);

    /**
     * 外呼拨打延迟的回调
     */
    void onDelay(AIPhoneCallBackData callBackData);

    /**
     * 外呼拨打延迟的回调唤醒
     */
    void onDelayWakeUp(AIPhoneCallBackData callBackData);

    /**
     * 外呼取消
     */
    void onCancel(AIPhoneCallBackData callBackData);
}
