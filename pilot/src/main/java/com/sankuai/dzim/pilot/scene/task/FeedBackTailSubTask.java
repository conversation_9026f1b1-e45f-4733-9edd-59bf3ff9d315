package com.sankuai.dzim.pilot.scene.task;

import com.google.common.collect.Lists;
import com.sankuai.dzim.pilot.buffer.core.PilotBufferItemDO;
import com.sankuai.dzim.pilot.buffer.enums.BufferItemTypeEnum;
import com.sankuai.dzim.pilot.buffer.enums.StreamEventCardTypeEnum;
import com.sankuai.dzim.pilot.scene.task.data.TaskContext;
import com.sankuai.dzim.pilot.scene.task.data.TaskResult;
import com.sankuai.dzim.pilot.scene.task.data.TaskTypeEnum;
import com.sankuai.dzim.pilot.utils.UuidUtil;
import com.sankuai.dzim.pilot.utils.context.RequestContext;
import org.springframework.stereotype.Component;

/**
 * 尾部反馈卡片，包括复制、点赞点踩功能
 * @author: zhouyibing
 * @date: 2024/8/5
 */
@Component
public class FeedBackTailSubTask implements Task {

    @Override
    public boolean accept(TaskContext context) {
        return TaskTypeEnum.FEEDBACK_TAIL_SUB_TASK.getType().equals(context.getTaskType());
    }

    @Override
    public TaskResult execute(TaskContext context) {
        return TaskResult.builder().taskContext(context).build();
    }

    @Override
    public void afterExecute(TaskResult result) {
        String cardKey = UuidUtil.getRandomCardKey();
        String data = StreamEventCardTypeEnum.buildCardContent(StreamEventCardTypeEnum.FEEDBACK_TAIL_CARD, cardKey);

        PilotBufferItemDO pilotBufferItemDO = new PilotBufferItemDO();
        pilotBufferItemDO.setType(BufferItemTypeEnum.MAIN_TEXT.getType());
        pilotBufferItemDO.setData(data);
        pilotBufferItemDO.setExtra(null);

        RequestContext.writeBuffer(Lists.newArrayList(pilotBufferItemDO));
    }
}
