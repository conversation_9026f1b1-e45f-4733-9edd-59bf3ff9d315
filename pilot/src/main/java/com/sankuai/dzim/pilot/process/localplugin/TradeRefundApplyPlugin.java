package com.sankuai.dzim.pilot.process.localplugin;


import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.meituan.nibtp.trade.client.combine.bean.WebParamDTO;
import com.meituan.nibtp.trade.client.combine.requset.AgentRefundApplyOrderReqDTO;
import com.meituan.nibtp.trade.client.combine.requset.AgentRefundApplyReqDTO;
import com.meituan.nibtp.trade.client.combine.response.AgentOrderDTO;
import com.meituan.nibtp.trade.client.combine.response.AgentRefundApplyResDTO;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.acl.AgentTradeAclService;
import com.sankuai.dzim.pilot.acl.UserAclService;
import com.sankuai.dzim.pilot.api.data.AIAnswerTypeEnum;
import com.sankuai.dzim.pilot.buffer.core.PilotBufferItemDO;
import com.sankuai.dzim.pilot.buffer.enums.BufferItemTypeEnum;
import com.sankuai.dzim.pilot.buffer.enums.OrderCardStatusEnum;
import com.sankuai.dzim.pilot.buffer.enums.StreamEventCardTypeEnum;
import com.sankuai.dzim.pilot.buffer.stream.build.ext.data.OrderCardData;
import com.sankuai.dzim.pilot.buffer.stream.build.ext.data.OrderCardDetailData;
import com.sankuai.dzim.pilot.buffer.utils.BufferUtils;
import com.sankuai.dzim.pilot.chain.data.AIAnswerData;
import com.sankuai.dzim.pilot.domain.annotation.LocalPlugin;
import com.sankuai.dzim.pilot.enums.GroupBuyOrderBizStatusEnum;
import com.sankuai.dzim.pilot.enums.MessageBizRefTypeEnum;
import com.sankuai.dzim.pilot.process.PilotMessageBizRefProcessService;
import com.sankuai.dzim.pilot.process.data.AgentTradeOrderConfig;
import com.sankuai.dzim.pilot.process.data.bizref.GroupBuyOrderExtra;
import com.sankuai.dzim.pilot.process.data.bizref.MessageBizRefData;
import com.sankuai.dzim.pilot.process.localplugin.param.TradeRefundApplyPram;
import com.sankuai.dzim.pilot.scene.data.AssistantSceneContext;
import com.sankuai.dzim.pilot.scene.task.data.EnvContext;
import com.sankuai.dzim.pilot.utils.LionConfigUtil;
import com.sankuai.dzim.pilot.utils.PluginContextUtil;
import com.sankuai.dzim.pilot.utils.UuidUtil;
import com.sankuai.dzim.pilot.utils.context.RequestContext;
import com.sankuai.dzim.pilot.utils.context.RequestContextConstants;
import com.sankuai.wpt.user.retrieve.thrift.message.UserModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 交易退款工具
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class TradeRefundApplyPlugin {
    @Autowired
    private AgentTradeAclService agentTradeAclService;
    @Autowired
    private UserAclService userAclService;
    @Autowired
    private PilotMessageBizRefProcessService pilotMessageBizRefProcessService;
    @Autowired
    private LionConfigUtil lionConfigUtil;

    @LocalPlugin(name = "OrderRefundTool", description = "商品订单退款工具，用户确认退款需要调用", returnDirect = true)
    public AIAnswerData TradeCreateOrder(TradeRefundApplyPram tradeRefundApplyPram) {
        //0，声明变量
        AgentTradeOrderConfig cardConfig = lionConfigUtil.getAgentTradeOrderConfig();
        AIAnswerData answerData = new AIAnswerData();
        answerData.setReturnDirect(true);
        answerData.setAiAnswerType(AIAnswerTypeEnum.TEXT.getType());
        answerData.setAnswer("退款失败，请稍后重试～");//失败文案
        WebParamDTO webParamDTO;
        AgentRefundApplyResDTO agentRefundApplyResDTO;
        try {
            //1、先推loading态
            BufferUtils.writeStatusBuffer(PilotBufferItemDO.builder().data(cardConfig.getDisplayTextConfig(OrderCardStatusEnum.REFUND_SUCCESS.getType()).loadingStatusText).build());
            log.info("调用退款工具TradeRefundApply，入参：TradeRefundApplyPram: {}", JsonCodec.encodeWithUTF8(tradeRefundApplyPram));
            if (!checkAndHandleParam(tradeRefundApplyPram)) {
                log.error("调用退款工具 TradeRefundApply 入参校验失败");
                BufferUtils.writeMainTextBuffer(PilotBufferItemDO.builder().data("退款失败，请稍后重试～").build());
                return answerData;
            }

            webParamDTO = buildWebParam();
            if (webParamDTO == null) {
                BufferUtils.writeMainTextBuffer(PilotBufferItemDO.builder().data("退款失败，请稍后重试～").build());
                log.error("调用退款工具失败 TradeRefundApply buildWebParam");
                return answerData;
            }

            //2、调退款接口
            agentRefundApplyResDTO = agentTradeAclService.refundApply(buildAgentRefundApplyResDTO(webParamDTO, tradeRefundApplyPram));
            if (agentRefundApplyResDTO == null || agentRefundApplyResDTO.isFail() || CollectionUtils.isEmpty(agentRefundApplyResDTO.getOrderList())) {
                BufferUtils.writeMainTextBuffer(PilotBufferItemDO.builder().data("退款失败，请稍后重试～").build());
                log.error("调用退款工具退款接口失败 refundApply: {}", agentRefundApplyResDTO);
                return answerData;
            }
        } catch (Exception e) {
            log.error("调用退款工具退款流程失败：", e);
            BufferUtils.writeMainTextBuffer(PilotBufferItemDO.builder().data("退款失败，请稍后重试～").build());
            return answerData;
        }

        // 退款成功后续
        try {
            //3、查下订单
            AgentOrderDTO agentOrderDTO = agentTradeAclService.queryOrderSingle(webParamDTO.getUserId(), agentRefundApplyResDTO.getOrderList().get(0).getOrderId());
            if (agentOrderDTO == null) {
                log.error("调用退款工具查询订单失败 queryOrderSingle");
                BufferUtils.writeMainTextBuffer(PilotBufferItemDO.builder().data(cardConfig.getDisplayTextConfig(OrderCardStatusEnum.REFUND_SUCCESS.getType()).getTitle()).build());
                return answerData;
            }

            //3、写退款成功卡片
            OrderCardData orderCardData = buildOrderCardData(agentOrderDTO, cardConfig);

            PilotBufferItemDO pilotBufferItemDO = new PilotBufferItemDO();
            pilotBufferItemDO.setType(BufferItemTypeEnum.MAIN_TEXT.getType());
            String data = StreamEventCardTypeEnum.buildCardContent(StreamEventCardTypeEnum.ORDER_CARD, UuidUtil.getRandomCardKey());
            pilotBufferItemDO.setData(data);
            pilotBufferItemDO.setExtra(JsonCodec.converseMap(JsonCodec.encodeWithUTF8(orderCardData), String.class, Object.class));
            BufferUtils.writeMainTextBuffer(pilotBufferItemDO);

            //4、标记已推送退款卡片
            markRefundCardPushed(agentOrderDTO.getOrderId());

            answerData.setAnswer("退款成功");
            return answerData;
        } catch (Exception e) {
            log.error("调用退款工具退款后续流程失败 queryOrderSingle", e);
            BufferUtils.writeMainTextBuffer(PilotBufferItemDO.builder().data(cardConfig.getDisplayTextConfig(OrderCardStatusEnum.REFUND_SUCCESS.getType()).getTitle()).build());
            return answerData;
        }
    }

    private OrderCardData buildOrderCardData(AgentOrderDTO agentOrderDTO, AgentTradeOrderConfig cardConfigs) {
        try {
            AgentTradeOrderConfig.DisplayTextConfig cardConfig = cardConfigs.getDisplayTextConfig(OrderCardStatusEnum.REFUND_SUCCESS.getType());
            OrderCardData orderCardData = new OrderCardData();
            OrderCardDetailData orderCardDetailData = new OrderCardDetailData();

            orderCardDetailData.setPrice(String.valueOf(agentOrderDTO.getPayAmount()));
            orderCardDetailData.setTitle(agentOrderDTO.getOrderItemList().get(0).getProductName());
            orderCardDetailData.setSubTitle("¥" + String.format("%.2f", agentOrderDTO.getPayAmount() / 100.0));
            orderCardDetailData.setHeadPic(agentOrderDTO.getHeadImageUrl());
            orderCardDetailData.setJumpUrl(cardConfigs.buildOrderDetailUrl(agentOrderDTO.getOrderId(), getPlatformFromThread()));

            orderCardData.setTitle(cardConfig.getTitle());
            orderCardData.setOrderInfo(orderCardDetailData);
            orderCardData.setStatus(OrderCardStatusEnum.REFUND_SUCCESS.getType());
            orderCardData.setOrderId(agentOrderDTO.getOrderId());

            return orderCardData;
        } catch (Exception e) {
            log.error("buildOrderCardData error", e);
            return null;
        }
    }

    private WebParamDTO buildWebParam() {
        try {
            //1、获取上下文
            AssistantSceneContext assistantSceneContext = RequestContext.getAttribute(RequestContextConstants.ASSISTANT_CONTEXT);
            //1.1 获取用户信息
            UserModel userModel = userAclService.queryUserModel(assistantSceneContext.getUserId());
            if (userModel == null) {
                log.info("下单工具 获取userModel失败");
                return null;
            }
            //1.2 获取bizParam
            EnvContext envContext = PluginContextUtil.getEnvContextFromRequestContext();
            if (envContext == null) {
                return null;
            }

            return agentTradeAclService.buildWebParam(userModel, envContext);
        } catch (Exception e) {
            log.error("下单工具 buildWebParam 异常", e);
            return null;
        }
    }


    private AgentRefundApplyReqDTO buildAgentRefundApplyResDTO(WebParamDTO webParamDTO, TradeRefundApplyPram tradeRefundApplyPram) {
        AgentRefundApplyReqDTO agentRefundApplyReqDTO = new AgentRefundApplyReqDTO();
        AgentRefundApplyOrderReqDTO agentRefundApplyOrderReqDTO = new AgentRefundApplyOrderReqDTO();
        agentRefundApplyOrderReqDTO.setOrderId(tradeRefundApplyPram.getOrderId());

        agentRefundApplyReqDTO.setWebParam(webParamDTO);
        agentRefundApplyReqDTO.setOrderList(Lists.newArrayList(agentRefundApplyOrderReqDTO));
        return agentRefundApplyReqDTO;
    }

    private boolean checkAndHandleParam(TradeRefundApplyPram tradeRefundApplyPram) {
        if (tradeRefundApplyPram == null) {
            return false;
        }

        if (tradeRefundApplyPram.getOrderId() == 0) {
            return false;
        }

        return true;
    }

    private int getPlatformFromThread() {
        EnvContext envContext = PluginContextUtil.getEnvContextFromRequestContext();
        return envContext.getPlatform();
    }

    private void markRefundCardPushed(Long orderId) {
        try {
            MessageBizRefData<GroupBuyOrderExtra> messageBizRef = pilotMessageBizRefProcessService.getMessageBizRef(
                    MessageBizRefTypeEnum.GROUP_BUY_ORDER.getType(), String.valueOf(orderId),
                    GroupBuyOrderBizStatusEnum.PAYMENT_CARD.name(), GroupBuyOrderExtra.class);
            if (messageBizRef != null && messageBizRef.getBizExtra() != null) {
                messageBizRef.getBizExtra().setHasRefundSuccessCardPushed(true);
                pilotMessageBizRefProcessService.saveMessageBizRef(messageBizRef);
            }
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("TradeRefundApplyPlugin").build(),
                    WarnMessage.build("markRefundCardPushed", "标记已推送退款成功卡片失败", ""),
                    orderId, false, e);
        }
    }
}
