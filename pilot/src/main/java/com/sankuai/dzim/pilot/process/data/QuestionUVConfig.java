package com.sankuai.dzim.pilot.process.data;

import lombok.Data;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2024/08/21 17:53
 */
@Data
public class QuestionUVConfig {
    /**
     * 问题类别分计算总分时所占比例
     */
    private Float categoryRatio;
    /**
     * 问题高优分计算总分时所占比例
     */
    private Float excellentRatio;
    /**
     * 问题点击分计算总分时所占比例
     */
    private Float clickRatio;
    /**
     * 问题统一跳转链接
     */
    private String baseJumpUrl;

    /**
     * 点评问题统一跳转链接
     */
    private String dpBaseJumpUrl;
    /**
     * 查询问题数量
     */
    private Integer querySize;
    /**
     * 页面类型与亮点问题内容映射，如2(商详页)->帮我总结门店亮点
     */
    private Map<Integer, String> pageSourceQuestionMap;
    /**
     * 高优问题集
     */
    private Set<String> excellentQuestions;

    /**
     * 展示门店亮点的评价最低数量
     */
    private int reviewLimit;
}

