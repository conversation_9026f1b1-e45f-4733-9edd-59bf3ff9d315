package com.sankuai.dzim.pilot;

import com.dianping.cat.servlet.CatFilter;
import com.dianping.gateway.web.AccountFilter;
import com.dianping.gateway.web.GatewayFilter;
import com.dianping.lion.client.Lion;
import com.sankuai.it.sso.sdk.spring.FilterFactoryBean;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.filter.DelegatingFilterProxy;

import java.util.ArrayList;
import java.util.List;

import static javax.servlet.DispatcherType.REQUEST;

@Configuration
public class FilterConfiguration {

    private String ssoSecret = Lion.getString("com.sankuai.mim.pilot", "com.sankuai.mim.pilot.sso.secret");
    private String clientId = Lion.getString("com.sankuai.mim.pilot", "com.sankuai.mim.pilot.sso.client");


    @Bean
    public FilterRegistrationBean catFilter() {
        CatFilter filter = new CatFilter();
        FilterRegistrationBean filterRegistrationBean = new FilterRegistrationBean();
        filterRegistrationBean.setFilter(filter);
        List<String> urlPatterns = new ArrayList<String>();
        urlPatterns.add("/*");
        filterRegistrationBean.setUrlPatterns(urlPatterns);
        filterRegistrationBean.setOrder(1);
        return filterRegistrationBean;
    }

    /**
     * 商机、用户的账号解析过滤器
     * @return
     */
    @Bean
    public FilterRegistrationBean getAccountFilter() {
        FilterRegistrationBean filterRegistrationBean = new FilterRegistrationBean();
        filterRegistrationBean.setFilter(new AccountFilter());
        filterRegistrationBean.addUrlPatterns("/*");
        filterRegistrationBean.setOrder(4);
        return filterRegistrationBean;
    }

    @Bean
    public FilterRegistrationBean getCORSFilter() {
        CORSFilter corsFilter = new CORSFilter();
        FilterRegistrationBean filterRegistrationBean = new FilterRegistrationBean();
        filterRegistrationBean.setFilter(corsFilter);
        List<String> urlPatterns = new ArrayList<String>();
        urlPatterns.add("/*");
        filterRegistrationBean.setUrlPatterns(urlPatterns);
        filterRegistrationBean.setOrder(2);
        return filterRegistrationBean;
    }


    /**
     * 新垂直网关，上面的账号解析一定要在这个之前执行，不然拿不到账号信息
     * @return
     */
    @Bean
    public FilterRegistrationBean getGatewayFilter() {
        GatewayFilter gatewayFilter = new GatewayFilter();
        FilterRegistrationBean filterRegistrationBean = new FilterRegistrationBean();
        filterRegistrationBean.setFilter(gatewayFilter);
        List<String> urlPatterns = new ArrayList<String>();
        urlPatterns.add("/*");
        filterRegistrationBean.setUrlPatterns(urlPatterns);
        filterRegistrationBean.setOrder(5);
        return filterRegistrationBean;
    }


    @Bean
    public FilterRegistrationBean mtFilter() {
        DelegatingFilterProxy filter = new DelegatingFilterProxy();
        FilterRegistrationBean registration = new FilterRegistrationBean();
        filter.setTargetBeanName("mtFilterBean");
        filter.setTargetFilterLifecycle(true);

        registration.setFilter(filter);
        registration.addUrlPatterns("/*");
        registration.setDispatcherTypes(REQUEST);
        registration.setName("mtFilter");
        registration.setOrder(3);
        //如果有出现容器中 mtFilter 被注册两遍，需要配置 registration.setEnabled(false);
        //registration.setEnabled(false);
        return registration;
    }

    @Bean
    public FilterFactoryBean mtFilterBean() {
        FilterFactoryBean filterFactoryBean = new FilterFactoryBean();
        filterFactoryBean.setClientId(clientId);
        filterFactoryBean.setSecret(ssoSecret);
        filterFactoryBean.setIncludedUriList("/dzim/pilot/sso/assistant/inner/**");
        filterFactoryBean.setSsoListener("com.sankuai.dzim.pilot.MySSOListener");
        return filterFactoryBean;
    }
}
