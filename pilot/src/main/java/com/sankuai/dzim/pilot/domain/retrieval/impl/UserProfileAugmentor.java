package com.sankuai.dzim.pilot.domain.retrieval.impl;

import com.google.common.collect.Lists;
import com.sankuai.dzim.pilot.domain.annotation.Retrieval;
import com.sankuai.dzim.pilot.domain.retrieval.RetrievalAugmentor;
import com.sankuai.dzim.pilot.domain.retrieval.data.Knowledge;
import com.sankuai.dzim.pilot.domain.retrieval.data.PersonaLabelConfig;
import com.sankuai.dzim.pilot.domain.retrieval.data.RetrievalContext;
import com.sankuai.dzim.pilot.domain.retrieval.data.RetrievalResult;
import com.sankuai.dzim.pilot.process.TransRetrievalDataToMarkdownService;
import com.sankuai.dzim.pilot.process.data.AgentUserProfileEnum;
import com.sankuai.dzim.pilot.utils.AccountUtils;
import com.sankuai.dzim.pilot.utils.IMConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import com.sankuai.dzim.pilot.utils.LionConfigUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
/**
 * <AUTHOR>
 * @since 2025/04/16 15:37
 */
@Component
@Slf4j
@Retrieval(name = "UserProfileRetrieval", desc = "用户画像检索")
public class UserProfileAugmentor  implements RetrievalAugmentor {
    @Autowired
    private TransRetrievalDataToMarkdownService transRetrievalDataToMarkdownService;
    @Autowired
    private AccountUtils accountUtils;
    @Autowired
    private LionConfigUtil lionConfigUtil;

    /**
     * 当前的RetrievalAugmentor使用用户画像类目组key
     * @see AgentUserProfileEnum
     */
    public static final String UserProfileConfigKey = "UserProfileConfigKey";

    @Override
    public String retrieval(RetrievalContext context) {
        return "";
    }

    @Override
    public RetrievalResult retrieveKnowledge(RetrievalContext context) {
        RetrievalResult retrievalResult = new RetrievalResult();
        retrievalResult.setKnowledgePrompt("以下是以下是用户画像相关数据:");
        Knowledge knowledge = new Knowledge();
        knowledge.setKnowledge(StringUtils.EMPTY);
        retrievalResult.setKnowledges(Lists.newArrayList(knowledge));
        try {
            String imUserId = context.getExtraInfoValue(IMConstants.IM_USER_ID_KEY);
            Long mtUserId = accountUtils.getMtUserId(imUserId);
            StringBuilder res = new StringBuilder();
            // 查用户画像
            List<PersonaLabelConfig> personaLabelConfigs = getPersonaLabelConfig(context);
            String personalInfo = transRetrievalDataToMarkdownService.getAndTransPersonal2Markdown(mtUserId, personaLabelConfigs);
            res.append(personalInfo);
            knowledge.setKnowledge(res.toString());
        } catch (Exception e) {
            log.error("UserProfileAugmentor.retrieveKnowledge error, context:{}", context, e);
        }
        return retrievalResult;
    }

    private List<PersonaLabelConfig> getPersonaLabelConfig(RetrievalContext context) {
        String userProfileConfigKey;
        try {
            userProfileConfigKey =  context.getExtraInfoValue(UserProfileConfigKey);
        } catch (Exception e) {
            log.error("UserProfileAugmentor.getPersonaLabelConfig error, context:{}", context, e);
            userProfileConfigKey =  AgentUserProfileEnum.DEFAULT.getKey();
        }
        if (StringUtils.isBlank(userProfileConfigKey)) {
            userProfileConfigKey =  AgentUserProfileEnum.DEFAULT.getKey();
        }
        return lionConfigUtil.getListPurposeUserLabel().getOrDefault(userProfileConfigKey, Lists.newArrayList());
    }
}
