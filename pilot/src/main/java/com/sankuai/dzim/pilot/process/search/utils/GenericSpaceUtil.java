package com.sankuai.dzim.pilot.process.search.utils;

import com.dianping.cat.util.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Date;

public class GenericSpaceUtil {

    private static final DateTimeFormatter timePattern = DateTimeFormat.forPattern("HH:mm:ss");

    /**
     * 解析字符串时间
     * @param productDate
     * @param periodString
     * @return
     */
    public static DateTime loadPeriodBeginWithDate(DateTime productDate, String periodString) {
        // 1. 判断字符串是否为Null 或者为 空
        if (StringUtils.isEmpty(periodString)) {
            // 2. 如果是的话，就把原来传入的时间返回
            return productDate;
        }
        return productDate.plusMinutes(timePattern.parseDateTime(periodString).getMinuteOfDay());
    }

    public static Date combineDateAndTime(long dateMillis, String timeStr) {
        // 将long时间转换为LocalDateTime
        LocalDateTime dateTime = LocalDateTime.ofInstant(new Date(dateMillis).toInstant(), ZoneId.systemDefault());

        // 解析时分字符串为LocalTime
        LocalTime time = LocalTime.parse(timeStr);

        // 拼接日期和时间
        LocalDateTime combinedDateTime = dateTime.withHour(time.getHour()).withMinute(time.getMinute());

        // 转换为Date类型返回
        return Date.from(combinedDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }
}
