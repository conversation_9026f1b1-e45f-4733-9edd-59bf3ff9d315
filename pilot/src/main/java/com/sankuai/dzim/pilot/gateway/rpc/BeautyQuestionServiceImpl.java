package com.sankuai.dzim.pilot.gateway.rpc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonServer;
import com.sankuai.athena.inf.rpc.CallType;
import com.sankuai.athena.inf.rpc.RpcClient;
import com.sankuai.dzim.message.common.enums.ClientTypeEnum;
import com.sankuai.dzim.pilot.acl.data.AbTestData;
import com.sankuai.dzim.pilot.api.BeautyQuestionService;
import com.sankuai.dzim.pilot.api.data.AbConfigDTO;
import com.sankuai.dzim.pilot.api.data.assistant.PilotEntranceReqDTO;
import com.sankuai.dzim.pilot.api.data.assistant.PilotEntranceRespDTO;
import com.sankuai.dzim.pilot.api.data.assistant.PilotQuestionReqDTO;
import com.sankuai.dzim.pilot.api.data.assistant.PilotQuestionRespDTO;
import com.sankuai.dzim.pilot.api.enums.AbConfigTypeEnum;
import com.sankuai.dzim.pilot.api.enums.search.generative.GenerativeSearchTypeEnum;
import com.sankuai.dzim.pilot.domain.GenerativeSearchDomainService;
import com.sankuai.dzim.pilot.process.QuestionAnswerCacheFlushProcessService;
import com.sankuai.dzim.pilot.process.QuestionScoreProcessService;
import com.sankuai.dzim.pilot.process.data.AbContext;
import com.sankuai.dzim.pilot.process.data.PageSourceTypeEnum;
import com.sankuai.dzim.pilot.process.data.PilotHotQuestionReq;
import com.sankuai.dzim.pilot.utils.BizTypeMappingUtil;
import com.sankuai.mpmctlive.query.thrift.gateway.ShopLiveInfoQueryGatewayService;
import com.sankuai.mpmctlive.query.thrift.live.dto.request.QueryFeedsReqDTO;
import com.sankuai.mpmctlive.query.thrift.live.dto.response.QueryFeedsRespDTO;
import com.sankuai.mpmctlive.query.thrift.searchresult.dto.request.EnvironmentDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/08/20 16:01
 */
@MdpPigeonServer
@Slf4j
public class BeautyQuestionServiceImpl implements BeautyQuestionService {

    @Autowired
    private GenerativeSearchDomainService generativeSearchDomainService;

    @Autowired
    private QuestionScoreProcessService questionScoreProcessService;

    @Autowired
    private BizTypeMappingUtil bizTypeMappingUtil;

    @Autowired
    private QuestionAnswerCacheFlushProcessService questionAnswerCacheFlushProcessService;

    @RpcClient(remoteAppkey = "com.sankuai.mpmctlive.query", callType = CallType.SYNC)
    private ShopLiveInfoQueryGatewayService shopLiveInfoQueryGatewayService;

    @Override
    public PilotEntranceRespDTO queryEntrance(PilotEntranceReqDTO reqDTO) {
        PilotEntranceRespDTO pilotEntranceRespDTO = new PilotEntranceRespDTO();
        pilotEntranceRespDTO.setHasEntrance(Boolean.FALSE);
        try {
            if (reqDTO == null) {
                return pilotEntranceRespDTO;
            }
            // 查灰度AB，获取Ab实验结果
            AbContext context = buildBeautyQuestionContext(reqDTO);
            AbTestData abTestData = questionScoreProcessService.queryAbTestResult(context);
            if (abTestData != null) {
                fillABTestData(pilotEntranceRespDTO, abTestData);
            }
            if (abTestData == null || !abTestData.isHit()) {
                return pilotEntranceRespDTO;
            }
            // 判断是否有直播浮层
            if (hasLiveLayer(reqDTO)) {
                return pilotEntranceRespDTO;
            }
            //查询问题数量，判断是否有入口
            int bizType = bizTypeMappingUtil.getBizType(reqDTO.getShopId(), reqDTO.getPlatform(), reqDTO.getSourceType(), reqDTO.getBizCode());
            if (bizType == 0) {
                return pilotEntranceRespDTO;
            }
            long count = generativeSearchDomainService.getGenerativeSearchWordCountByBizType(bizType);
            pilotEntranceRespDTO.setHasEntrance(count > 0);
        } catch (Exception e) {
            log.error("查询入口异常, reqDTO:{}", reqDTO, e);
            pilotEntranceRespDTO.setHasEntrance(Boolean.FALSE);
        }
        return pilotEntranceRespDTO;
    }

    private boolean hasLiveLayer(PilotEntranceReqDTO reqDTO) {
        if (reqDTO == null || reqDTO.getShopId() <= 0) {
            return false;
        }
        QueryFeedsReqDTO queryFeedsReqDTO = new QueryFeedsReqDTO();
        queryFeedsReqDTO.setPlatform(reqDTO.getPlatform());
        queryFeedsReqDTO.setBizCode("nib.general.shop_live_trailer");
        queryFeedsReqDTO.setSubjectType(1);
        queryFeedsReqDTO.setSubjectId(String.valueOf(reqDTO.getShopId()));
        EnvironmentDTO environmentDTO = new EnvironmentDTO();
        environmentDTO.setDpUserId(reqDTO.getUserId());
        environmentDTO.setMtUserId(reqDTO.getUserId());
        environmentDTO.setAppId(reqDTO.getAppId());
        environmentDTO.setIos(ClientTypeEnum.isIos(reqDTO.getClientType()));
        queryFeedsReqDTO.setEnvironmentDTO(environmentDTO);
        QueryFeedsRespDTO queryFeedsRespDTO = shopLiveInfoQueryGatewayService.fetchLiveData(queryFeedsReqDTO);
        if (queryFeedsRespDTO == null || queryFeedsRespDTO.getTotalCnt() == null) {
            return false;
        }
        return queryFeedsRespDTO.getTotalCnt() > 0;
    }

    private void fillABTestData(PilotEntranceRespDTO pilotEntranceRespDTO, AbTestData abTestData) {
        AbConfigDTO abConfigDTO = new AbConfigDTO();
        abConfigDTO.setExpId(abTestData.getExpId());
        abConfigDTO.setExpType(AbConfigTypeEnum.BEAUTY_ASSISTANT_EXP.getType());
        abConfigDTO.setExpResult(abTestData.getStrategyKey());
        abConfigDTO.setExpBiInfo(abTestData.getAbConfig());
        abConfigDTO.setIsHit(abTestData.isHit());
        pilotEntranceRespDTO.setAbConfigDTO(abConfigDTO);
    }

    @Override
    public PilotQuestionRespDTO queryQuestions(PilotQuestionReqDTO reqDTO) {
        return questionScoreProcessService.queryQuestionList(buildPilotHotQuestionReq(reqDTO));
    }

    @Override
    public void flushCache(String shopIdStr, boolean override) {
        List<Long> shopIds = new ArrayList<>();
        if (StringUtils.isNotEmpty(shopIdStr)) {
            shopIds = JSON.parseObject(shopIdStr, new TypeReference<List<Long>>() {});
        }
        questionAnswerCacheFlushProcessService.flushReviewSummaryCache(shopIds, false, override);
    }

    @Override
    public PilotQuestionRespDTO queryBlzQuestions(PilotQuestionReqDTO reqDTO) {
        PilotQuestionRespDTO respDTO = new PilotQuestionRespDTO();
        //入参校验
        if (reqDTO == null || StringUtils.isBlank(reqDTO.getQuery())) {
            return respDTO;
        }

        //直接复用小美问问-医美的问题列表
        PilotHotQuestionReq pilotHotQuestionReq = new PilotHotQuestionReq();
        pilotHotQuestionReq.setQuery(reqDTO.getQuery());
        pilotHotQuestionReq.setPlatform(reqDTO.getPlatform());
        pilotHotQuestionReq.setSourceType(PageSourceTypeEnum.CHANNEL_PAGE.getType());
        pilotHotQuestionReq.setBizType(GenerativeSearchTypeEnum.BEAUTY_ASK_MEDICAL_BEAUTY.getType());

        return questionScoreProcessService.queryBlzQuestionList(pilotHotQuestionReq);
    }

    private AbContext buildBeautyQuestionContext(PilotEntranceReqDTO reqDTO) {
        AbContext context = new AbContext();
        context.setPlatform(reqDTO.getPlatform());
        context.setDeviceId(reqDTO.getDeviceId());
        context.setUnionId(reqDTO.getUnionId());
        context.setSourceType(reqDTO.getSourceType());
        context.setBizType(bizTypeMappingUtil.getBizType(reqDTO.getShopId(), reqDTO.getPlatform(), reqDTO.getSourceType(), reqDTO.getBizCode()));
        return context;
    }

    private PilotHotQuestionReq buildPilotHotQuestionReq(PilotQuestionReqDTO reqDTO) {
        int bizType = bizTypeMappingUtil.getBizType(reqDTO.getShopId(), reqDTO.getPlatform(), reqDTO.getSourceType(), reqDTO.getBizCode());
        Assert.isTrue(bizType > 0, "shopId、platform、sourceType或bizCode不合法");
        PilotHotQuestionReq pilotHotQuestionReq = new PilotHotQuestionReq();
        pilotHotQuestionReq.setPlatform(reqDTO.getPlatform());
        pilotHotQuestionReq.setBizType(bizType);
        pilotHotQuestionReq.setSourceType(reqDTO.getSourceType());
        pilotHotQuestionReq.setShopId(reqDTO.getShopId());
        pilotHotQuestionReq.setUserId(reqDTO.getUserId());
        pilotHotQuestionReq.setDeviceId(reqDTO.getDeviceId());
        pilotHotQuestionReq.setUnionId(reqDTO.getUnionId());
        pilotHotQuestionReq.setQuery(reqDTO.getQuery());
        pilotHotQuestionReq.setTabWord(reqDTO.getTabWord());
        return pilotHotQuestionReq;
    }
}
