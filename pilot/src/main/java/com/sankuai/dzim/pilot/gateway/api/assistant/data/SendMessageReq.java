package com.sankuai.dzim.pilot.gateway.api.assistant.data;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/7/31 17:50
 */
@Data
public class SendMessageReq {

    private int platform;

    private String imUserId;

    private String message;

    private long chatGroupId;

    private int messageType;

    private int assistantType;

    private int inputSource;

    private String extra;

    private String channel;

    private String bizParams;

    /**
     * 环境参数
     */
    private String envContext;

    private List<String> imgUrls;

    /**
     * 后端获取，前端无需传
     */
    private String userIp;

    /**
     * 某些场景需要从chatGroup接口透传的参数
     */
    private String chatGroupExp;
}
