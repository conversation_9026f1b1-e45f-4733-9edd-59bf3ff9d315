package com.sankuai.dzim.pilot.process.search.utils;

import com.dianping.beauty.clove.enums.PlatformEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dzim.pilot.process.search.data.*;
import com.sankuai.dztheme.generalproduct.GeneralProductItemDTO;
import com.sankuai.dztheme.generalproduct.req.GeneralProductRequest;
import com.sankuai.dztheme.generalproduct.res.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 泛商品主题填充工具
 */
@Slf4j
public class GeneralProductPaddingHandlerUtils {

    public static GeneralProductRequest buildGeneralProductRequest(List<Integer> productIds, ReserveQueryContext ctx) {
        GeneralProductRequest generalProductRequest = new GeneralProductRequest();
        generalProductRequest.setPlanId(ctx.getPlanId());
        generalProductRequest.setProductIds(productIds);
        generalProductRequest.setExtParams(buildParams(ctx, productIds));
        return generalProductRequest;
    }

    private static Map<String, Object> buildParams(ReserveQueryContext ctx, List<Integer> productIds) {
        UserEnvParam userEnvParam = ctx.getUserEnvParam();
        Map<String, Object> customPaddingParam = ctx.getCustomPaddingParam();

        Map<String, Object> extParams = Maps.newHashMap();
        if (MapUtils.isNotEmpty(customPaddingParam)) {
            extParams.putAll(ctx.getCustomPaddingParam());
        }
        extParams.put("shopIdL", buildAutoShopId(ctx));
        extParams.put("dpShopIdL", ctx.getDpShopId());
        extParams.put("mtShopIdL", ctx.getMtShopId());
        extParams.put("platform", ctx.getPlatform());
        extParams.put("shopFrontCategoryId", ctx.getShopBackCategoryId());
        extParams.put("bizProductId2PlatformProductId", ctx.getBizProductId2PlatformProductId());
        extParams.put("productId2ShopIdMap", buildProductId2ShopIdMap(productIds, ctx));
        extParams.put("scene", 400200);

        // 填充用户环境参数
        if (userEnvParam != null) {
            extParams.put("cityId", userEnvParam.getCityId());
            extParams.put("appVersion",userEnvParam.getVersion());
            extParams.put("lat",userEnvParam.getLat());
            extParams.put("lng",userEnvParam.getLng());
            extParams.put("userId",userEnvParam.getUserId());
            extParams.put("clientType",userEnvParam.getClientType());
            extParams.put("uuid",userEnvParam.getUuId());
            extParams.put("dpid",userEnvParam.getDpId());
        }


        return extParams;
    }

    private static Map<Integer, Long> buildProductId2ShopIdMap(List<Integer> productIds, ReserveQueryContext ctx) {
        Map<Integer, Long> productId2ShopIdMap = Maps.newHashMap();
        for (Integer productId : productIds) {
            productId2ShopIdMap.put(productId, buildAutoShopId(ctx));
        }
        return productId2ShopIdMap;
    }

    private static long buildAutoShopId(ReserveQueryContext ctx) {
        if (PlatformEnum.isDp(ctx.getPlatform())) {
            return ctx.getDpShopId();
        }
        return ctx.getMtShopId();
    }

    public static List<ReserveProductM> buildResult(ReserveQueryContext ctx, List<ReserveProductM> products,
            List<CompletableFuture<GeneralProductResult>> resultList) {
        List<GeneralProductDTO> generalProductList = extractAllProductResult(resultList);
        if (CollectionUtils.isEmpty(generalProductList)) {
            return Lists.newArrayList();
        }
        return buildProductMs(ctx, products, generalProductList);
    }

    private static List<ReserveProductM> buildProductMs(ReserveQueryContext ctx, List<ReserveProductM> products,
            List<GeneralProductDTO> generalProductList) {
        Map<Integer, GeneralProductDTO> productId2GeneralProduct = generalProductList.stream()
                .collect(Collectors.toMap(GeneralProductDTO::getProductId, e -> e, (a, b) -> a));
        for (ReserveProductM productM : products) {
            GeneralProductDTO generalProductDTO = productId2GeneralProduct.get(productM.getBizProductId());
            if (generalProductDTO == null) {
                continue;
            }
            paddingProductM(productM, generalProductDTO);
        }
        return products;
    }

    public static void paddingProductM(ReserveProductM productM, GeneralProductDTO generalProduct) {
        productM.setTitle(generalProduct.getName());
        productM.setPicUrl(generalProduct.getHeadPic());
        productM.setSalePriceTag(generalProduct.getSalePrice());
        productM.setProductTags(generalProduct.getProductTags());
        productM.setSale(buildProductSaleM(generalProduct));
        productM.setPinPools(buildPinPools(generalProduct.getPinPools()));
        productM.setExtAttrs(buildAttrs(productM.getExtAttrs(), generalProduct));
        productM.setProductItemMList(buildProductItemList(generalProduct.getProductItems()));
        productM.setPromoPrices(buildPromoPrice(generalProduct.getPromos()));
        productM.setBaseState(buildProductBaseStateM(generalProduct.getProductBaseState()));
        productM.setShopMs(buildShopMs(generalProduct.getShops()));
    }

    private static List<ShopM> buildShopMs(List<GeneralProductShopDTO> shops) {
        if (CollectionUtils.isEmpty(shops)) {
            return Lists.newArrayList();
        }
        return shops.stream().filter(Objects::nonNull).map(GeneralProductPaddingHandlerUtils::buildShopM)
                .collect(Collectors.toList());
    }

    private static ShopM buildShopM(GeneralProductShopDTO generalProductShopDTO) {
        ShopM shopM = new ShopM();
        shopM.setShopId(generalProductShopDTO.getShopIdAsLong());
        shopM.setShopName(generalProductShopDTO.getShopName());
        shopM.setPic(generalProductShopDTO.getShopPic());
        return shopM;
    }

    private static ProductBaseStateM buildProductBaseStateM(ProductBaseStateDTO productBaseStateDTO) {
        if (productBaseStateDTO == null) {
            return null;
        }
        ProductBaseStateM productBaseStateM = new ProductBaseStateM();
        productBaseStateM.setTimeSlices(buildTimeSlices(productBaseStateDTO.getTimeSlice()));
        productBaseStateM.setEarliestBookableTime(productBaseStateDTO.getEarliestBookableTime());
        return productBaseStateM;
    }

    private static List<TimeSliceM> buildTimeSlices(List<TimeSliceDTO> timeSliceDTOS) {
        if (CollectionUtils.isEmpty(timeSliceDTOS)) {
            return null;
        }
        return timeSliceDTOS.stream().filter(Objects::nonNull).map(GeneralProductPaddingHandlerUtils::buildTimeSliceM)
                .collect(Collectors.toList());
    }

    private static TimeSliceM buildTimeSliceM(TimeSliceDTO timeSliceDTO) {
        TimeSliceM timeSliceM = new TimeSliceM();
        timeSliceM.setStartTime(timeSliceDTO.getStartTime());
        timeSliceM.setAvailable(timeSliceDTO.isAvailable());
        timeSliceM.setEndTime(timeSliceDTO.getEndTime());
        timeSliceM.setTimeSliceType(timeSliceDTO.getTimeSliceType());
        return timeSliceM;
    }

    private static ProductPromoPriceM buildProductPromoPriceM(GeneralProductPromoDTO generalProductPromoDTO) {
        ProductPromoPriceM productPromoPriceM = new ProductPromoPriceM();
        productPromoPriceM.setPromoPrice(generalProductPromoDTO.getPromoPrice());
        productPromoPriceM.setPromoPriceTag(generalProductPromoDTO.getPromoPriceTag());
        productPromoPriceM.setMarketPrice(Optional.ofNullable(generalProductPromoDTO.getMarketPrice())
                .map(p -> p.stripTrailingZeros().toPlainString()).orElse(null));
        productPromoPriceM.setSkuId(generalProductPromoDTO.getSkuId());
        productPromoPriceM.setResourceId(generalProductPromoDTO.getResourceId());
        productPromoPriceM.setPromoTag(generalProductPromoDTO.getPromoTag());
        productPromoPriceM.setShortPromoTag(generalProductPromoDTO.getShortPromoTag());
        productPromoPriceM.setPromoTagType(generalProductPromoDTO.getPromoTagType());
        productPromoPriceM.setPromoTagPrefix(generalProductPromoDTO.getPromoTagPrefix());
        productPromoPriceM.setPromoType(generalProductPromoDTO.getPromoType());
        productPromoPriceM.setDiscount(generalProductPromoDTO.getDiscount());
        productPromoPriceM.setPromoAmount(generalProductPromoDTO.getPromoAmount());
        productPromoPriceM.setCoupons(buildCoupons(generalProductPromoDTO.getCoupons()));
        productPromoPriceM.setStartTime(generalProductPromoDTO.getStartTime());
        productPromoPriceM.setEndTime(generalProductPromoDTO.getEndTime());
        productPromoPriceM.setPromoQuantityLimit(generalProductPromoDTO.getPromoQuantityLimit());
        productPromoPriceM.setIcon(generalProductPromoDTO.getIcon());
        productPromoPriceM.setOriginalPromoItems(buildPromoItemList(generalProductPromoDTO.getOriginalPromoItems()));
        productPromoPriceM.setExtendDisplayInfo(generalProductPromoDTO.getExtendDisplayInfo());
        productPromoPriceM.setFeeItemList(buildFeeItemList(generalProductPromoDTO.getFeeList()));
        productPromoPriceM.setPricePromoInfoMap(buildPricePromoInfoMap(generalProductPromoDTO.getPricePromoInfoMap()));
        if (generalProductPromoDTO.getPromoDetail() == null) {
            return productPromoPriceM;
        }
        productPromoPriceM.setTotalPromoPriceTag(generalProductPromoDTO.getPromoDetail().getTotalPromoAmount());
        productPromoPriceM.setTotalPromoPrice(generalProductPromoDTO.getPromoDetail().getTotalPromoAmountValue());
        productPromoPriceM
                .setPromoItemList(buildPromoItemList(generalProductPromoDTO.getPromoDetail().getPromoItems()));
        return productPromoPriceM;
    }

    private static Map<Integer, List<PromoItemM>>
            buildPricePromoInfoMap(Map<Integer, List<PromoItemDTO>> pricePromoInfoMap) {
        if (MapUtils.isEmpty(pricePromoInfoMap)) {
            return null;
        }
        Map<Integer, List<PromoItemM>> result = Maps.newHashMap();
        for (Map.Entry<Integer, List<PromoItemDTO>> entry : pricePromoInfoMap.entrySet()) {
            Integer key = entry.getKey();
            List<PromoItemDTO> promoItemDTOList = entry.getValue();
            List<PromoItemM> promoItemList = buildPromoItemList(promoItemDTOList);
            if (CollectionUtils.isNotEmpty(promoItemList)) {
                result.put(key, promoItemList);
            }
        }
        return result;
    }

    private static List<FeeItemM> buildFeeItemList(List<FeeDTO> feeList) {
        if (CollectionUtils.isEmpty(feeList)) {
            return Lists.newArrayList();
        }
        return feeList.stream().map(feeDTO -> {
            FeeItemM feeItemM = new FeeItemM();
            feeItemM.setFeeAmount(feeDTO.getFeeAmount());
            feeItemM.setFeeType(feeDTO.getFeeType());
            return feeItemM;
        }).collect(Collectors.toList());
    }

    private static List<PromoItemM> buildPromoItemList(List<PromoItemDTO> promoItemList) {
        if (CollectionUtils.isEmpty(promoItemList)) {
            return Lists.newArrayList();
        }
        return promoItemList.stream().map(promoItem -> {
            PromoItemM promoItemM = new PromoItemM();
            promoItemM.setPromoId(promoItem.getPromoId());
            promoItemM.setPromoTypeCode(promoItem.getPromoTypeCode());
            promoItemM.setDesc(promoItem.getPromoDesc());
            promoItemM.setPromoTag(promoItem.getPromoAmount());
            promoItemM.setPromoPrice(promoItem.getPromoPrice());
            promoItemM.setSourceType(promoItem.getSourceType());
            promoItemM.setPromoType(promoItem.getPromoName());
            promoItemM.setCanAssign(promoItem.isCanAssign());
            promoItemM.setNewUser(promoItem.isNewUser());
            promoItemM.setPromoNewOldIdentity(promoItem.getPromoIdentity());
            promoItemM.setPromoDiscount(promoItem.getPromoDiscount());
            promoItemM.setUsedPromoTypes(promoItem.getUsedPromoTypes());
            promoItemM.setPriceLimitDesc(promoItem.getPriceLimitDesc());
            promoItemM.setUseTimeDesc(promoItem.getUseTimeDesc());
            promoItemM.setIcon(promoItem.getPromoIcon());
            promoItemM.setCouponAssignStatus(promoItem.getCouponAssignStatus());
            promoItemM.setPromoUseType(promoItem.getPromoUseType());
            promoItemM.setCouponGroupId(promoItem.getCouponGroupId());
            promoItemM.setCouponId(promoItem.getCouponId());
            promoItemM.setPromotionExplanatoryTags(promoItem.getPromotionExplanatoryTags());
            promoItemM.setPromotionOtherInfoMap(promoItem.getPromotionOtherInfoMap());
            promoItemM.setPromoItemTextM(buildPromoItemTextM(promoItem.getPromoItemTextDTO()));
            promoItemM.setAmount(promoItem.getAmount());
            promoItemM.setMinConsumptionAmount(promoItem.getMinConsumptionAmount());
            promoItemM.setCouponTitle(promoItem.getCouponTitle());
            promoItemM.setStartTime(promoItem.getStartTime());
            promoItemM.setEndTime(promoItem.getEndTime());
            promoItemM.setPromotionDisplayTextMap(promoItem.getPromotionDisplayTextMap());
            promoItemM.setPromoShowType(promoItem.getPromoShowType());
            return promoItemM;
        }).collect(Collectors.toList());
    }

    private static PromoItemTextM buildPromoItemTextM(PromoItemTextDTO promoItemTextDTO) {
        if (promoItemTextDTO == null) {
            return null;
        }
        PromoItemTextM promoItemTextM = new PromoItemTextM();
        promoItemTextM.setTitle(promoItemTextDTO.getTitle());
        promoItemTextM.setPromoName(promoItemTextDTO.getPromoName());
        promoItemTextM.setSubTitle(promoItemTextDTO.getSubTitle());
        promoItemTextM.setIcon(promoItemTextDTO.getIcon());
        promoItemTextM.setAtmosphereBarIcon(promoItemTextDTO.getAtmosphereBarIcon());
        promoItemTextM.setAtmosphereBarText(promoItemTextDTO.getAtmosphereBarText());
        promoItemTextM.setAtmosphereBarButtonText(promoItemTextDTO.getAtmosphereBarButtonText());
        promoItemTextM.setAtmosphereBarButtonUrl(promoItemTextDTO.getAtmosphereBarButtonUrl());
        promoItemTextM.setPromoStatusText(promoItemTextDTO.getPromoStatusText());
        promoItemTextM.setPromoDivideType(promoItemTextDTO.getPromoDivideType());
        promoItemTextM.setPromoDivideTypeDesc(promoItemTextDTO.getPromoDivideTypeDesc());
        return promoItemTextM;
    }

    private static List<ProductCouponM> buildCoupons(List<GeneralProductCouponDTO> generalProductCouponDTOS) {
        if (CollectionUtils.isEmpty(generalProductCouponDTOS)) {
            return Lists.newArrayList();
        }
        return generalProductCouponDTOS.stream().filter(couponDTO -> couponDTO != null)
                .map(couponDTO -> buildCoupon(couponDTO)).collect(Collectors.toList());
    }

    private static ProductCouponM buildCoupon(GeneralProductCouponDTO couponDTO) {
        ProductCouponM productCouponM = new ProductCouponM();
        productCouponM.setCouponTag(couponDTO.getCouponTag());
        productCouponM.setActivityId(couponDTO.getActivityId());
        productCouponM.setCouponId(couponDTO.getCouponId());
        productCouponM.setFlowId(couponDTO.getFlowId());
        productCouponM.setMaterialId(couponDTO.getMaterialId());
        productCouponM.setResourceActivityId(couponDTO.getResourceActivityId());
        productCouponM.setRowKey(couponDTO.getRowKey());
        productCouponM.setRemainStock(couponDTO.getRemainStock());
        productCouponM.setTotalStock(couponDTO.getTotalStock());
        return productCouponM;
    }

    public static List<ProductPromoPriceM> buildPromoPrice(List<GeneralProductPromoDTO> generalProductPromoDTOS) {
        if (CollectionUtils.isEmpty(generalProductPromoDTOS)) {
            return Lists.newArrayList();
        }
        return generalProductPromoDTOS.stream().filter(generalPromo -> generalPromo != null)
                .map(generalPromo -> buildProductPromoPriceM(generalPromo)).collect(Collectors.toList());
    }

    private static List<ProductItemM> buildProductItemList(List<GeneralProductItemDTO> productItems) {
        if (CollectionUtils.isEmpty(productItems)) {
            return Lists.newArrayList();
        }
        return productItems.stream().map(GeneralProductPaddingHandlerUtils::buildProductItem)
                .collect(Collectors.toList());
    }

    private static ProductItemM buildProductItem(GeneralProductItemDTO item) {
        ProductItemM productItemM = new ProductItemM();
        // originalSalePrice 主题层统一用string
        BeanUtils.copyProperties(item, productItemM, "attrs", "bookPeriodStocks", "originalSalePrice");
        productItemM.setOriginalSalePrice(buildOriginSalePrice(item.getOriginalSalePrice()));
        productItemM.setPromoPrices(buildPromoPrice(item.getPromos()));
        productItemM.setExtAttrs(buildItemAttrList(item.getAttrs()));
        productItemM.setAllPricePowerTag(buildPricePowerTag(item.getAllPricePowerTags()));
        productItemM.setBookPeriodStocks(buildBookPeriodStocks(item.getBookPeriodStocks()));
        productItemM.setPriceProtectionInfo(buildPriceProtectionInfo(item.getPriceProtectionInfo()));
        return productItemM;
    }

    private static BigDecimal buildOriginSalePrice(String originalSalePrice) {
        if (StringUtils.isEmpty(originalSalePrice)) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(originalSalePrice);
    }

    private static PriceProtectionInfoM buildPriceProtectionInfo(PriceProtectionInfoDTO priceProtectionInfoDTO) {
        if (null == priceProtectionInfoDTO) {
            return null;
        }
        PriceProtectionInfoM priceProtectionInfoM = new PriceProtectionInfoM();
        priceProtectionInfoM.setValid(priceProtectionInfoDTO.isValid());
        priceProtectionInfoM.setValidityDays(priceProtectionInfoDTO.getValidityDays());
        priceProtectionInfoM.setSkuId(priceProtectionInfoDTO.getSkuId());
        priceProtectionInfoM.setPlatformSkuId(priceProtectionInfoDTO.getPlatformSkuId());
        priceProtectionInfoM.setHasValidGuaranteeActivity(priceProtectionInfoDTO.isHasValidGuaranteeActivity());
        return priceProtectionInfoM;
    }

    private static List<ProductPricePowerM> buildPricePowerTag(List<GeneralProductPricePowerTagDTO> allPricePowerTags) {
        if (CollectionUtils.isEmpty(allPricePowerTags)) {
            return null;
        }

        List<ProductPricePowerM> result = allPricePowerTags.stream()
                .map(pricePowerTag -> convert2ProductPricePowerM(pricePowerTag))
                .filter(pricePowerM -> pricePowerM != null).collect(Collectors.toList());
        return result;
    }

    private static ProductPricePowerM convert2ProductPricePowerM(GeneralProductPricePowerTagDTO pricePowerTag) {
        if (pricePowerTag == null) {
            return null;
        }
        ProductPricePowerM productPricePowerM = new ProductPricePowerM();
        productPricePowerM.setTagType(pricePowerTag.getTagType());
        productPricePowerM.setTagName(pricePowerTag.getTagName());
        productPricePowerM.setTagMode(pricePowerTag.getTagMode());
        return productPricePowerM;
    }

    private static List<AttrM> buildItemAttrList(List<GeneralProductItemAttrDTO> attrs) {
        if (CollectionUtils.isEmpty(attrs)) {
            return null;
        }
        return attrs.stream().map(attr -> new AttrM(attr.getName(), attr.getValue())).collect(Collectors.toList());
    }

    private static List<PeriodStockM> buildBookPeriodStocks(List<PeriodStockDTO> sourceList) {
        // mock数据，线下可打开使用
        // if (CollectionUtils.isEmpty(sourceList) && StringUtils.equals(Tracer.getSwimlane(), "mengmuzi-ufsnl")) {
        // sourceList = mockData();
        // }
        if (CollectionUtils.isEmpty(sourceList)) {
            return new ArrayList<>();
        }
        return sourceList.stream().map(source -> {
            PeriodStockM stockM = new PeriodStockM();
            stockM.setStock(source.getStock());
            stockM.setBeginTime(source.getBeginTime());
            return stockM;
        }).collect(Collectors.toList());
    }

    private static List<AttrM> buildAttrs(List<AttrM> extAttrs, GeneralProductDTO generalProductDTO) {
        if (extAttrs == null) {
            extAttrs = Lists.newArrayList();
        }
        if (generalProductDTO == null || CollectionUtils.isEmpty(generalProductDTO.getAttrs())) {
            return extAttrs;
        }
        extAttrs.addAll(generalProductDTO.getAttrs().stream().filter(Objects::nonNull)
                .filter(generalProductAttrDTO -> StringUtils.isNotEmpty(generalProductAttrDTO.getName()))
                .map(attr -> new AttrM(attr.getName(), attr.getValue()))
                .collect(Collectors.toList()));
        return extAttrs;
    }

    private static List<ProductPinPoolM> buildPinPools(List<GeneralProductPinPoolDTO> pinPoolDTOs) {
        if (CollectionUtils.isEmpty(pinPoolDTOs)) {
            return Lists.newArrayList();
        }
        return pinPoolDTOs.stream().map(GeneralProductPaddingHandlerUtils::buildPinPool).collect(Collectors.toList());
    }

    private static ProductPinPoolM buildPinPool(GeneralProductPinPoolDTO pinPoolDTO) {
        ProductPinPoolM productPinPoolM = new ProductPinPoolM();
        productPinPoolM.setProductName(pinPoolDTO.getProductName());
        productPinPoolM.setPoolId(pinPoolDTO.getPoolId());
        productPinPoolM.setMaxNum(pinPoolDTO.getMaxNum());
        productPinPoolM.setMinNum(pinPoolDTO.getMinNum());
        productPinPoolM.setCurrentNum(pinPoolDTO.getCurrentNum());
        productPinPoolM.setDescription(pinPoolDTO.getDescription());
        productPinPoolM.setInPool(pinPoolDTO.isInPool());
        productPinPoolM.setPrice(pinPoolDTO.getPrice());
        productPinPoolM.setJumpUrl(pinPoolDTO.getJumpUrl());
        productPinPoolM.setPeriod(pinPoolDTO.getPeriod());
        productPinPoolM.setPoolStatus(pinPoolDTO.getPoolStatus());
        productPinPoolM.setPoolType(pinPoolDTO.getPoolType());
        productPinPoolM.setProductItemId(pinPoolDTO.getProductItemId());
        productPinPoolM.setCanJoin(pinPoolDTO.isCanJoin());
        productPinPoolM.setBookStartTime(pinPoolDTO.getBookStartTime());
        productPinPoolM.setSuccessProbability(pinPoolDTO.getSuccessProbability());
        productPinPoolM.setUserTags(pinPoolDTO.getUserTags());
        if (CollectionUtils.isNotEmpty(pinPoolDTO.getPoolGroups())) {
            productPinPoolM.setPoolGroups(pinPoolDTO.getPoolGroups().stream()
                    .map(GeneralProductPaddingHandlerUtils::buildPinPoolGroup).collect(Collectors.toList()));
        }
        return productPinPoolM;
    }

    private static ProductPinPoolGroupM buildPinPoolGroup(GeneralProductPinPoolGroupDTO pinPoolGroupDTO) {
        ProductPinPoolGroupM productPinPoolGroup = new ProductPinPoolGroupM();
        productPinPoolGroup.setDescription(pinPoolGroupDTO.getDescription());
        productPinPoolGroup.setOrderFrom(pinPoolGroupDTO.getOrderFrom());
        if (CollectionUtils.isNotEmpty(pinPoolGroupDTO.getUsers())) {
            productPinPoolGroup.setUsers(pinPoolGroupDTO.getUsers().stream()
                    .map(GeneralProductPaddingHandlerUtils::buildProductUser).collect(Collectors.toList()));
        }
        return productPinPoolGroup;
    }

    private static ProductUserM buildProductUser(GeneralProductUserDTO userDTO) {
        ProductUserM productUser = new ProductUserM();
        productUser.setAvatar(userDTO.getAvatar());
        productUser.setName(userDTO.getName());
        productUser.setSex(userDTO.getSex());
        return productUser;
    }

    private static ProductSaleM buildProductSaleM(GeneralProductDTO generalProduct) {
        if (Objects.isNull(generalProduct.getSale())) {
            return null;
        }
        ProductSaleM saleM = new ProductSaleM();
        saleM.setSale(generalProduct.getSale().getSale());
        saleM.setSaleTag(generalProduct.getSale().getSaleTag());
        return saleM;
    }

    private static List<GeneralProductDTO>
            extractAllProductResult(List<CompletableFuture<GeneralProductResult>> generalProductResultCfList) {
        List<GeneralProductDTO> generalProductList = Lists.newArrayList();
        for (CompletableFuture<GeneralProductResult> resultCf : generalProductResultCfList) {
            GeneralProductResult result = resultCf.join();
            if (result == null || CollectionUtils.isEmpty(result.getProducts())) {
                continue;
            }
            generalProductList.addAll(result.getProducts());
        }
        return generalProductList;
    }

    public static <U> CompletableFuture<List<U>> each(List<CompletableFuture<U>> futures) {
        CompletableFuture<List<U>> overallResult = new CompletableFuture<>();

        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).whenComplete((noUsed, exception) -> {
            if (exception != null) {
                overallResult.completeExceptionally(exception);
                return;
            }
            List<U> results = new ArrayList<>();
            for (CompletableFuture<U> future : futures) {
                results.add(future.join());
            }
            overallResult.complete(results);
        });
        return overallResult;
    }

}
