package com.sankuai.dzim.pilot.process.search.strategy;

import com.dianping.joy.common.resource.enums.fitness.SportTypeEnum;
import com.dianping.vc.sdk.lang.NumberUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.dzim.pilot.process.aireservebook.enums.POIIndustryType;
import com.sankuai.dzim.pilot.process.data.ProductRetrievalData;
import com.sankuai.dzim.pilot.process.data.ProductStockData;
import com.sankuai.dzim.pilot.process.search.data.ProductBaseStateM;
import com.sankuai.dzim.pilot.process.search.data.ReserveProductM;
import com.sankuai.dzim.pilot.process.search.data.ReserveQueryContext;
import com.sankuai.dzim.pilot.process.search.data.TimeSliceM;
import com.sankuai.dzim.pilot.process.search.enums.IndustryBackendCategoryEnum;
import com.sankuai.dzim.pilot.process.search.enums.PlatformCategoryEnum;
import com.sankuai.dzim.pilot.process.search.strategy.padding.ReserveProductPaddingFactory;
import com.sankuai.dzim.pilot.process.search.strategy.padding.ReserveProductPaddingHandler;
import com.sankuai.dzim.pilot.process.search.strategy.recall.ReserveProductRecallFactory;
import com.sankuai.dzim.pilot.process.search.strategy.recall.ReserveProductRecallHandler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class VenueReserveProductQueryHandler implements ReserveProductQueryHandler {

    private static final List<Integer> VENUE_CATEGORY_IDS = IndustryBackendCategoryEnum.VENUE.getCategoryIds();
    private static final List<Integer> BALL_CATEGORY_IDS = IndustryBackendCategoryEnum.BALL.getCategoryIds();

    private final String BALL_PLAN_ID = "10101120";

    @Resource
    private ReserveProductRecallFactory reserveProductRecallFactory;

    @Resource
    private ReserveProductPaddingFactory reserveProductPaddingFactory;

    @Override
    public boolean match(ReserveQueryContext queryContext) {
        if (queryContext == null || queryContext.getShopBackCategoryId() <= 0) {
            return false;
        }
        return VENUE_CATEGORY_IDS.contains(queryContext.getShopBackCategoryId())
                || BALL_CATEGORY_IDS.contains(queryContext.getShopBackCategoryId());
    }

    @Override
    public List<ProductRetrievalData> execute(ReserveQueryContext queryContext) {

        // 加入行业个性化上下文参数
        ReserveQueryContext ctx = fillSpecialContext(queryContext);

        // 根据poiId召回商品id
        List<ReserveProductM> recallProducts = queryShopProductIds(ctx);

        // 根据商品id列表填充商品信息
        List<ReserveProductM> paddingProducts = paddingProductInfo(recallProducts, ctx);

        // 商品分组聚合成展示商品
        List<ReserveProductM> groupProducts = reorganization(paddingProducts);

        // 构建门店预订商品返回结果
        return buildProductRetrievalDataList(queryContext, groupProducts);
    }

    private List<ReserveProductM> paddingProductInfo(List<ReserveProductM> recallProducts, ReserveQueryContext ctx) {
        ReserveProductPaddingHandler handler = reserveProductPaddingFactory.getHandler(ctx);
        if (handler == null) {
            return Lists.newArrayList();
        }
        return handler.padding(recallProducts, ctx);
    }

    private List<ReserveProductM> reorganization(List<ReserveProductM> paddingProducts) {
        return paddingProducts;
    }

    private List<ProductRetrievalData> buildProductRetrievalDataList(ReserveQueryContext queryContext,
            List<ReserveProductM> paddingProducts) {
        if (CollectionUtils.isEmpty(paddingProducts)) {
            return Lists.newArrayList();
        }
        return paddingProducts.stream().map(reserveProductM -> buildProductRetrievalData(queryContext, reserveProductM))
                .collect(Collectors.toList());
    }

    private ProductRetrievalData buildProductRetrievalData(ReserveQueryContext queryContext,
            ReserveProductM reserveProductM) {
        ProductRetrievalData productRetrievalData = new ProductRetrievalData();
        productRetrievalData.setProductType(2);
        productRetrievalData.setCategoryId((long)POIIndustryType.BALL.getSecondBackCategoryId());
        productRetrievalData.setMtProductId(reserveProductM.getPlatformProductId());
        productRetrievalData.setTitle(buildTitle(reserveProductM));
        productRetrievalData.setSubTitles(buildSubTitles(reserveProductM));
        productRetrievalData.setSalePrice(buildSalePrice(reserveProductM.getSalePriceTag()));
        productRetrievalData.setStocks(buildStocks(reserveProductM));
        return productRetrievalData;
    }

    private String buildSalePrice(String salePriceTag) {
        return salePriceTag;
    }

    /**
     * 主商品下任意一个套餐时间片段可订，那主商品在这个套餐就是可订的
     */
    public ProductStockData buildStocks(ReserveProductM reserveProductM) {
        if (reserveProductM == null || reserveProductM.getBaseState() == null) {
            return null;
        }
        ProductBaseStateM baseState = reserveProductM.getBaseState();
        ProductStockData productStockData = new ProductStockData();
        productStockData.setEarliestBookableTime(baseState.getEarliestBookableTime());
        productStockData.setTimeSlices(mergeTimeSlice(reserveProductM));
        return productStockData;
    }

    private List<TimeSliceM> mergeTimeSlice(ReserveProductM reserveProductM) {
        ProductBaseStateM baseState = reserveProductM.getBaseState();
        List<TimeSliceM> timeSlices = baseState.getTimeSlices();
        if (CollectionUtils.isEmpty(timeSlices)) {
            return Lists.newArrayList();
        }
        if (timeSlices.size() == 1) {
            return timeSlices;
        }

        return processMerge(timeSlices);
    }

    private List<TimeSliceM> processMerge(List<TimeSliceM> timeSlices) {
        // 排序
        timeSlices.sort(Comparator.comparing(TimeSliceM::getStartTime));
        // 遍历并合并相邻的库存状态相同的时段
        List<TimeSliceM> mergedTimeSlices = new ArrayList<>();
        TimeSliceM currentSlice = timeSlices.get(0);
        mergedTimeSlices.add(currentSlice);
        for (int i = 1; i < timeSlices.size(); i++) {
            TimeSliceM nextSlice = timeSlices.get(i);
            if (nextSlice.isAvailable() == currentSlice.isAvailable()) {
                // 合并相邻的库存状态相同的时段
                currentSlice.setEndTime(nextSlice.getEndTime());
            } else {
                currentSlice = nextSlice;
                mergedTimeSlices.add(currentSlice);
            }
        }
        return mergedTimeSlices;
    }

    private List<String> buildSubTitles(ReserveProductM reserveProductM) {
        if (reserveProductM == null || reserveProductM.getBaseState() == null) {
            return Lists.newArrayList();
        }
        ProductBaseStateM baseState = reserveProductM.getBaseState();
        Long earliestBookableTime = baseState.getEarliestBookableTime();
        if (earliestBookableTime == null) {
            return Lists.newArrayList();
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(earliestBookableTime),
                ZoneId.systemDefault());
        return Lists.newArrayList(String.format("最早%s可订", localDateTime.format(formatter)));
    }

    private String buildTitle(ReserveProductM reserveProductM) {
        String sportTypeValue = reserveProductM.getAttr("attr_resource_site_type");
        String sportTypeName = SportTypeEnum.getDesc(NumberUtils.toInt(sportTypeValue));
        if (StringUtils.isBlank(sportTypeName)) {
            return "";
        }
        String title = reserveProductM.getTitle();
        return sportTypeName + title;
    }

    private ReserveQueryContext fillSpecialContext(ReserveQueryContext queryContext) {
        if (queryContext == null) {
            return null;
        }

        // 泛商品主题方案id
        queryContext.setPlanId(BALL_PLAN_ID);
        queryContext.setPlatformCategoryId(PlatformCategoryEnum.BALL.getCode());
        queryContext.setExtra(buildExtra(queryContext));
        return queryContext;
    }

    private Map<String, Object> buildExtra(ReserveQueryContext queryContext) {
        Map<String, Object> extra = Maps.newHashMap();
        extra.put("dealAttrs", Sets.newHashSet("week"));
        return extra;
    }

    private List<ReserveProductM> queryShopProductIds(ReserveQueryContext ctx) {
        ReserveProductRecallHandler handler = reserveProductRecallFactory.getHandler(ctx);
        if (handler == null) {
            return Lists.newArrayList();
        }
        return handler.recall(ctx);
    }
}
