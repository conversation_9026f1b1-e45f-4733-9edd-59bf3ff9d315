package com.sankuai.dzim.pilot.process.search.strategy;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.dzim.pilot.process.aireservebook.enums.POIIndustryType;
import com.sankuai.dzim.pilot.process.data.ProductDetailData;
import com.sankuai.dzim.pilot.process.data.ProductRetrievalData;
import com.sankuai.dzim.pilot.process.search.data.ReserveProductM;
import com.sankuai.dzim.pilot.process.search.data.ReserveQueryContext;
import com.sankuai.dzim.pilot.process.search.enums.IndustryBackendCategoryEnum;
import com.sankuai.dzim.pilot.process.search.enums.PlatformCategoryEnum;
import com.sankuai.dzim.pilot.process.search.strategy.padding.ReserveProductPaddingFactory;
import com.sankuai.dzim.pilot.process.search.strategy.padding.ReserveProductPaddingHandler;
import com.sankuai.dzim.pilot.process.search.strategy.recall.ReserveProductRecallFactory;
import com.sankuai.dzim.pilot.process.search.strategy.recall.ReserveProductRecallHandler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class PetReserveProductQueryHandler implements ReserveProductQueryHandler {

    private static final List<Integer> PET_CATEGORY_IDS = IndustryBackendCategoryEnum.PET.getCategoryIds();
    private final String PET_PLAN_ID = "10101122";

    @Resource
    private ReserveProductRecallFactory reserveProductRecallFactory;

    @Resource
    private ReserveProductPaddingFactory reserveProductPaddingFactory;

    @Override
    public boolean match(ReserveQueryContext queryContext) {
        if (queryContext == null || queryContext.getShopBackCategoryId() <= 0) {
            return false;
        }
        return PET_CATEGORY_IDS.contains(queryContext.getShopBackCategoryId());
    }

    @Override
    public List<ProductRetrievalData> execute(ReserveQueryContext queryContext) {

        // 加入行业个性化上下文参数
        ReserveQueryContext ctx = fillSpecialContext(queryContext);

        // 根据poiId召回商品id
        List<ReserveProductM> recallProducts = queryShopProductIds(ctx);

        // 根据商品id列表填充商品信息
        List<ReserveProductM> paddingProducts = paddingProductInfo(recallProducts, ctx);

        // 商品分组聚合成展示商品
        List<ReserveProductM> groupProducts = reorganization(paddingProducts);

        // 构建门店预订商品返回结果
        return buildProductRetrievalDataList(queryContext, groupProducts);
    }

    private List<ReserveProductM> paddingProductInfo(List<ReserveProductM> recallProducts, ReserveQueryContext ctx) {
        ReserveProductPaddingHandler handler = reserveProductPaddingFactory.getHandler(ctx);
        if (handler == null) {
            return Lists.newArrayList();
        }
        return handler.padding(recallProducts, ctx);
    }

    private List<ReserveProductM> reorganization(List<ReserveProductM> paddingProducts) {
        return paddingProducts;
    }

    private List<ProductRetrievalData> buildProductRetrievalDataList(ReserveQueryContext queryContext,
            List<ReserveProductM> paddingProducts) {
        if (CollectionUtils.isEmpty(paddingProducts)) {
            return Lists.newArrayList();
        }
        return paddingProducts.stream().map(reserveProductM -> buildProductRetrievalData(queryContext, reserveProductM))
                .collect(Collectors.toList());
    }

    private ProductRetrievalData buildProductRetrievalData(ReserveQueryContext queryContext,
            ReserveProductM reserveProductM) {
        ProductRetrievalData productRetrievalData = new ProductRetrievalData();
        productRetrievalData.setProductType(2);
        productRetrievalData.setCategoryId((long)POIIndustryType.PET_SHOP.getSecondBackCategoryId());
        productRetrievalData.setMtProductId(reserveProductM.getPlatformProductId());
        productRetrievalData.setTitle(buildTitle(reserveProductM));
        productRetrievalData.setProductDetails(buildProductDetails(reserveProductM));
        return productRetrievalData;
    }

    private ProductDetailData buildProductDetails(ReserveProductM reserveProductM) {
        if (reserveProductM == null) {
            return null;
        }
        ProductDetailData productDetailData = new ProductDetailData();
        productDetailData.setBasicProductInfo(buildBasicProductInfo(reserveProductM));
        return productDetailData;
    }

    private Map<String, String> buildBasicProductInfo(ReserveProductM reserveProductM) {
        Map<String, String> basicProductInfo = Maps.newHashMap();
        basicProductInfo.put("适用宠物种类", reserveProductM.getAttr("applyPet"));
        basicProductInfo.put("基础洗浴服务项目", reserveProductM.getAttr("baseBath"));
        basicProductInfo.put("服务类型", reserveProductM.getAttr("category"));
        basicProductInfo.put("适用毛长", reserveProductM.getAttr("hairLengthClassification"));
        basicProductInfo.put("适用体重", reserveProductM.getAttr("isLimited"));
        basicProductInfo.put("服务用时", reserveProductM.getAttr("serviceTime"));
        basicProductInfo.put("宠物疫苗情况", reserveProductM.getAttr("petVaccinationSituation"));
        basicProductInfo.put("SPA", reserveProductM.getAttr("spa"));
        basicProductInfo.put("局部护理", reserveProductM.getAttr("topicalCare"));
        return basicProductInfo;
    }

    private String buildTitle(ReserveProductM reserveProductM) {
        if (StringUtils.isBlank(reserveProductM.getTitle())) {
            return "";
        }
        return reserveProductM.getTitle();
    }

    private ReserveQueryContext fillSpecialContext(ReserveQueryContext queryContext) {
        if (queryContext == null) {
            return null;
        }

        // 泛商品主题方案id
        queryContext.setPlanId(PET_PLAN_ID);
        queryContext.setPlatformCategoryId(PlatformCategoryEnum.PET.getCode());
        queryContext.setExtra(buildExtra(queryContext));
        return queryContext;
    }

    private Map<String, Object> buildExtra(ReserveQueryContext queryContext) {
        Map<String, Object> extra = Maps.newHashMap();
        extra.put("dealAttrs", Sets.newHashSet("week"));
        return extra;
    }

    private List<ReserveProductM> queryShopProductIds(ReserveQueryContext ctx) {
        ReserveProductRecallHandler handler = reserveProductRecallFactory.getHandler(ctx);
        if (handler == null) {
            return Lists.newArrayList();
        }
        return handler.recall(ctx);
    }
}
