package com.sankuai.dzim.pilot.acl.impl;

import com.dianping.cat.Cat;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.inf.threadpool.ThreadPool;
import com.sankuai.conch.certify.tokenaccess.thrift.*;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.acl.TokenAccessAclService;
import com.sankuai.inf.kms.pangolin.api.service.IEncryptService;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/3/15 11:09 上午
 */

@Slf4j
@Service
public class TokenAccessAclServiceImpl implements TokenAccessAclService {

    private static final int TOKEN_SERVICE_CLIENT_ID = 10145;

    @ThreadPool("tokenAccessPool")
    private ExecutorService tokenAccessPool;

    @Resource(name = "phoneNoEncryptService")
    private IEncryptService phoneNoEncryptService;


    @Resource
    private TokenAccessThriftService.Iface tokenAccessThriftService;

    private static final String SUCCESS = "success";

    @Override
    public String getTokenMobile(String plainMobile) {
        if (StringUtils.isBlank(plainMobile)) {
            return "";
        }
        try {
            GetMobileTokenReqTo reqTo = new GetMobileTokenReqTo();
            reqTo.setMobileNo(plainMobile);
            reqTo.setClientId(TOKEN_SERVICE_CLIENT_ID);
            GetMobileTokenResTo resTo = tokenAccessThriftService.getMobileToken(reqTo);
            if (SUCCESS.equalsIgnoreCase(resTo.getStatus()) && resTo.getData() != null) {
                return resTo.getData().getMobileNoToken();
            } else {
                LogUtils.logFailLog(log, TagContext.builder().action("getTokenByPhone").build(),
                        WarnMessage.build("getTokenByPhone", "获取手机号Token失败", ""), plainMobile, resTo);
            }
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("getTokenByPhone").build(),
                    WarnMessage.build("getTokenByPhone", "获取手机号Token失败", ""), plainMobile, e);
        }
        return "";
    }

    @Override
    public String getPlainMobile(String token) {
        String mobileNoEncrypt = getEncryptPhoneByToken(token);
        if (StringUtils.isBlank(mobileNoEncrypt)) {
            return "";
        }
        try {
            return phoneNoEncryptService.decryptUTF8String(mobileNoEncrypt);
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("getPhoneByToken").build(),
                    WarnMessage.build("getPhoneByToken", "根据token解析手机号密文失败", ""), token, e);
            return "";
        }
    }

    @Override
    public Map<String, String> batchGetPlainMobile(List<String> mobilesToken) {
        Map<String, String> encryptMobile2Token = batchGetMobileKmsEncryptPartition(mobilesToken);
        if (MapUtils.isEmpty(encryptMobile2Token)) {
            return Maps.newHashMap();
        }
        return CollectionUtils.emptyIfNull(encryptMobile2Token.entrySet()).stream()
                .filter(Objects::nonNull)
                .filter(item -> item.getKey() != null)
                .filter(item -> item.getValue() != null)
                .collect(Collectors.toMap(Map.Entry::getKey, item -> {
                    try {
                        return phoneNoEncryptService.decryptUTF8String(item.getValue());
                    } catch (Exception e) {
                        LogUtils.logFailLog(log, TagContext.builder().action("decryptUTF8String").build(),
                                WarnMessage.build("decryptUTF8String", "decryptUTF8String 失败", ""), mobilesToken, e);
                        return Strings.EMPTY;
                    }
                }));
    }

    @Override
    public List<String> batchGetPhoneToken(List<String> plainMobiles) {
        if (CollectionUtils.isEmpty(plainMobiles)) {
            return Lists.newArrayList();
        }
        try{
            return batchGetTokenMobilePartition(plainMobiles);
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("batchGetTokenMobile").build(),
                    WarnMessage.build("batchGetTokenMobile", "批量获取手机号密文失败", ""), plainMobiles, e);
            return Lists.newArrayList();
        }
    }

    private List<String> batchGetTokenMobilePartition(List<String> plainMobiles) {
        List<List<String>> mobilesTokenPartition = Lists.partition(plainMobiles, 20);
        List<CompletableFuture<List<String>>> resListCf = Lists.newArrayList();
        List<String> resList = Lists.newArrayList();
        for (List<String> list : mobilesTokenPartition) {
            resListCf.add(CompletableFuture.supplyAsync(() -> batchGetTokenMobileToken(list), tokenAccessPool));
        }
        List<List<String>> ansList = assemble(resListCf).join();
        for (List<String> list : ansList) {
            resList.addAll(list);
        }
        return resList;
    }

    private List<String> batchGetTokenMobileToken(List<String> list) {
        try {
            BatchGetMobileTokenReqTo req = new BatchGetMobileTokenReqTo();
            req.setClientId(TOKEN_SERVICE_CLIENT_ID);
            req.setMobileNoList(list);
            req.setMobileNoPlainList(list);
            BatchGetMobileTokenResTo res = tokenAccessThriftService.batchGetMobileToken(req);
            if (res == null || MapUtils.isEmpty(res.getSuccessMap()) || MapUtils.isNotEmpty(res.getFailMap())) {
                log.error("批量转化明文手机号失败. req:{}, res:{}", JsonCodec.encode(list), JsonCodec.encode(res));
                Cat.logMetricForCount("BatchGetMobileTokenAndEncryptException");
            }
            return res == null ? Lists.newArrayList() : Lists.newArrayList(res.getSuccessMap().values());
        } catch (Exception e) {
            Cat.logMetricForCount("BatchGetMobileTokenAndEncryptException");
            log.error("批量转化明文手机号接口异常. req:{}", JsonCodec.encode(list), e);
            return Lists.newArrayList();
        }
    }


    private Map<String, String> batchGetMobileKmsEncryptPartition(List<String> mobilesToken) {
        List<List<String>> mobilesTokenPartition = Lists.partition(mobilesToken, 20);
        List<CompletableFuture<Map<String, String>>> resListCf = Lists.newArrayList();
        Map<String, String> resMap = Maps.newHashMap();
        for (List<String> list : mobilesTokenPartition) {
            resListCf.add(CompletableFuture.supplyAsync(() -> batchGetMobileKmsEncrypt(list), tokenAccessPool));
        }
        List<Map<String, String>> ansMap = assemble(resListCf).join();
        for (Map<String, String> map : ansMap) {
            resMap.putAll(map);
        }
        return resMap;
    }

    private Map<String, String> batchGetIdentifyIdKmsEncryptPartition(List<String> tokenIdentifyIds) {
        List<List<String>> mobilesTokenPartition = Lists.partition(tokenIdentifyIds, 20);
        List<CompletableFuture<Map<String, String>>> resListCf = Lists.newArrayList();
        Map<String, String> resMap = Maps.newHashMap();
        for (List<String> list : mobilesTokenPartition) {
            resListCf.add(CompletableFuture.supplyAsync(() -> batchGetIdentifyIdKmsEncrypt(list), tokenAccessPool));
        }
        List<Map<String, String>> ansMap = assemble(resListCf).join();
        for (Map<String, String> map : ansMap) {
            resMap.putAll(map);
        }
        return resMap;
    }


    private <T> CompletableFuture<List<T>> assemble(List<CompletableFuture<T>> futures) {
        CompletableFuture<Void> allDoneFuture = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        return allDoneFuture.thenApply(v -> futures.stream().map(CompletableFuture::join).filter(Objects::nonNull).collect(Collectors.toList()));
    }


    private Map<String, String> batchGetBankCardKmsEncrypt(List<String> bankCardsToken) {
        try {
            BatchGetBankcardEncryptReqTo batchGetBankcardEncryptReqTo = buildBatchGetBankCardEncryptReqTo(bankCardsToken);
            BatchGetBankcardEncryptResTo res = tokenAccessThriftService.batchGetBankcardEncrypt(batchGetBankcardEncryptReqTo);
            if (res == null || MapUtils.isEmpty(res.getSuccessMap()) || MapUtils.isNotEmpty(res.getFailMap())) {
                log.error("批量转化明文银行卡号失败. req:{}, res:{}", JsonCodec.encode(bankCardsToken), JsonCodec.encode(res));
                Cat.logMetricForCount("BatchGetBankCardEncryptException");
            }
            return res == null ? Maps.newHashMap() : res.getSuccessMap().entrySet().stream().collect(
                    Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().getBankcardNumEncrypt(), (o1, o2) -> o2));
        } catch (Exception e) {
            Cat.logMetricForCount("BatchGetBankCardEncryptException");
            log.error("批量转化明文银行卡接口异常. req:{}", JsonCodec.encode(bankCardsToken), e);
            return Maps.newHashMap();
        }
    }

    private BatchGetBankcardEncryptReqTo buildBatchGetBankCardEncryptReqTo(List<String> bankCardsToken) {
        BatchGetBankcardEncryptReqTo req = new BatchGetBankcardEncryptReqTo();
        req.setClientId(TOKEN_SERVICE_CLIENT_ID);
        req.setSign("");
        List<BatchGetBankcardEncryptReqDataTo> list = Lists.newArrayList();
        for (int i = 0; i < bankCardsToken.size(); i++) {
            BatchGetBankcardEncryptReqDataTo reqData = new BatchGetBankcardEncryptReqDataTo();
            reqData.setBankcardNumToken(bankCardsToken.get(i));
            list.add(reqData);
        }
        req.setBankcardTokenList(list);
        return req;
    }


    private Map<String, String> batchGetIdentifyIdKmsEncrypt(List<String> identifyIdsToken) {
        try {
            BatchGetIdentifyEncryptReqTo batchGetIdentifyEncryptReqTo = buildBatchGetIdentifyEncryptReqTo(identifyIdsToken);
            BatchGetIdentifyEncryptResTo res = tokenAccessThriftService.batchGetIdentifyEncrypt(batchGetIdentifyEncryptReqTo);
            if (res == null || MapUtils.isEmpty(res.getSuccessMap()) || MapUtils.isNotEmpty(res.getFailMap())) {
                log.error("批量转化明文身份证号失败. req:{}, res:{}", JsonCodec.encode(identifyIdsToken), JsonCodec.encode(res));
                Cat.logMetricForCount("BatchGetIdentifyIdEncryptException");
            }
            return res == null ? Maps.newHashMap() : res.getSuccessMap().entrySet().stream().collect(
                    Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().getIdentifyIdEncrypt(), (o1, o2) -> o2));
        } catch (Exception e) {
            Cat.logMetricForCount("BatchGetMobileEncryptException");
            log.error("批量转化明文身份证接口异常. req:{}", JsonCodec.encode(identifyIdsToken), e);
            return Maps.newHashMap();
        }
    }

    private BatchGetIdentifyEncryptReqTo buildBatchGetIdentifyEncryptReqTo(List<String> identifyIdsToken) {
        BatchGetIdentifyEncryptReqTo req = new BatchGetIdentifyEncryptReqTo();
        req.setClientId(TOKEN_SERVICE_CLIENT_ID);
        req.setSign("");
        req.setIdentifyIdTokenList(identifyIdsToken);
        return req;
    }

    private Map<String, String> batchGetMobileKmsEncrypt(List<String> mobilesToken) {
        try {
            BatchGetMobileEncryptReqTo batchGetMobileEncryptReqTo = buildBatchGetMobileEncryptReq(mobilesToken);
            BatchGetMobileEncryptResTo res = tokenAccessThriftService.batchGetMobileEncrypt(batchGetMobileEncryptReqTo);
            if (res == null || MapUtils.isEmpty(res.getSuccessMap()) || MapUtils.isNotEmpty(res.getFailMap())) {
                log.error("批量转化明文手机号失败. req:{}, res:{}", JsonCodec.encode(mobilesToken), JsonCodec.encode(res));
                Cat.logMetricForCount("BatchGetMobileEncryptException");
            }
            return res == null ? Maps.newHashMap() : res.getSuccessMap();
        } catch (Exception e) {
            Cat.logMetricForCount("BatchGetMobileEncryptException");
            log.error("批量转化明文手机号接口异常. req:{}", JsonCodec.encode(mobilesToken), e);
            return Maps.newHashMap();
        }
    }

    private BatchGetMobileEncryptReqTo buildBatchGetMobileEncryptReq(List<String> mobilesToken) {
        BatchGetMobileEncryptReqTo req = new BatchGetMobileEncryptReqTo();
        req.setClientId(TOKEN_SERVICE_CLIENT_ID);
        req.setSign("");
        req.setMobileNoTokenList(mobilesToken);
        return req;
    }

    @Override
    public String getEncryptPhoneByToken(String token) {
        if (StringUtils.isBlank(token)) {
            return "";
        }
        try {
            GetMobileEncryptReqTo reqTo = new GetMobileEncryptReqTo();
            reqTo.setMobileNoToken(token);
            reqTo.setClientId(TOKEN_SERVICE_CLIENT_ID);
            reqTo.setSign("");
            GetMobileEncryptResTo resTo = tokenAccessThriftService.getMobileEncrypt(reqTo);
            if (SUCCESS.equalsIgnoreCase(resTo.getStatus()) && resTo.getData() != null) {
                return resTo.getData().getMobileNoEncrypt();
            } else {
                LogUtils.logFailLog(log, TagContext.builder().action("getEncryptPhoneByToken").build(),
                        WarnMessage.build("getEncryptPhoneByToken", "根据token获取手机号密文失败", ""), token, resTo);
            }
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("getEncryptPhoneByToken").build(),
                    WarnMessage.build("getEncryptPhoneByToken", "根据token获取手机号密文失败", ""), token, e);
        }
        return "";
    }


}
