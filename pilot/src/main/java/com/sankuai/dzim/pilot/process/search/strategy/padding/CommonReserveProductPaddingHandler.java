package com.sankuai.dzim.pilot.process.search.strategy.padding;

import com.google.common.collect.Lists;
import com.sankuai.dzim.pilot.acl.ProductAclService;
import com.sankuai.dzim.pilot.process.search.data.ReserveProductM;
import com.sankuai.dzim.pilot.process.search.data.ReserveQueryContext;
import com.sankuai.dzim.pilot.process.search.enums.IndustryBackendCategoryEnum;
import com.sankuai.dzim.pilot.process.search.utils.GeneralProductPaddingHandlerUtils;
import com.sankuai.dztheme.generalproduct.req.GeneralProductRequest;
import com.sankuai.dztheme.generalproduct.res.GeneralProductResult;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Component
public class CommonReserveProductPaddingHandler implements ReserveProductPaddingHandler {

    private static final List<Integer> COMMON_CATEGORY_IDS = Lists
            .newArrayList(IndustryBackendCategoryEnum.KTV.getCategoryIds(),
                    IndustryBackendCategoryEnum.CHESS.getCategoryIds(),
                    IndustryBackendCategoryEnum.BALL.getCategoryIds(),
                    IndustryBackendCategoryEnum.VENUE.getCategoryIds(),
                    IndustryBackendCategoryEnum.BACKROOM.getCategoryIds(),
                    IndustryBackendCategoryEnum.ROLE_PLAY.getCategoryIds(),
                    IndustryBackendCategoryEnum.BAR.getCategoryIds(), IndustryBackendCategoryEnum.PET.getCategoryIds())
            .stream().flatMap(List::stream).collect(Collectors.toList());

    @Resource
    private ProductAclService productAclService;

    @Override
    public List<ReserveProductM> padding(List<ReserveProductM> products, ReserveQueryContext queryContext) {
        return paddingProductInfo(products, queryContext);
    }

    private List<ReserveProductM> paddingProductInfo(List<ReserveProductM> products, ReserveQueryContext ctx) {
        if (CollectionUtils.isEmpty(products)) {
            return Lists.newArrayList();
        }
        List<CompletableFuture<GeneralProductResult>> generalProductResultCfList = buildProductResultCfList(products,
                ctx);
        return GeneralProductPaddingHandlerUtils.buildResult(ctx, products, generalProductResultCfList);
    }

    private List<CompletableFuture<GeneralProductResult>> buildProductResultCfList(List<ReserveProductM> products,
            ReserveQueryContext ctx) {
        List<List<Integer>> productIdsList = buildAllProductIds(products);
        List<CompletableFuture<GeneralProductResult>> cfList = Lists.newArrayList();
        for (List<Integer> productIds : productIdsList) {
            GeneralProductRequest request = GeneralProductPaddingHandlerUtils.buildGeneralProductRequest(productIds,
                    ctx);
            CompletableFuture<GeneralProductResult> resultCf = productAclService.queryGeneralProductTheme(request);
            cfList.add(resultCf);
        }

        return cfList;
    }

    private List<List<Integer>> buildAllProductIds(List<ReserveProductM> products) {
        List<Integer> productIds = products.stream().map(ReserveProductM::getBizProductId).filter(Objects::nonNull)
                .collect(Collectors.toList());
        return Lists.partition(productIds, 8);
    }

    @Override
    public boolean match(ReserveQueryContext queryContext) {
        if (queryContext == null || queryContext.getShopBackCategoryId() <= 0) {
            return false;
        }
        return COMMON_CATEGORY_IDS.contains(queryContext.getShopBackCategoryId());
    }

}
