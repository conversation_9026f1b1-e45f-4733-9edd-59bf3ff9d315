package com.sankuai.dzim.pilot.process.search.strategy;

import com.sankuai.dzim.pilot.process.data.ProductRetrievalData;
import com.sankuai.dzim.pilot.process.search.data.ReserveQueryContext;

import java.util.List;


public interface ReserveProductQueryHandler {

    /**
     * 搜索门店商品
     */
    List<ProductRetrievalData> execute(ReserveQueryContext queryContext);

    /**
     * 是否匹配当前策略
     */
    boolean match(ReserveQueryContext queryContext);
}
