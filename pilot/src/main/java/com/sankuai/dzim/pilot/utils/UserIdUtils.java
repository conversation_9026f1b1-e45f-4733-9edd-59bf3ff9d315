package com.sankuai.dzim.pilot.utils;

import com.sankuai.dzim.message.common.utils.ImAccountTypeUtils;
import com.sankuai.dzim.pilot.api.enums.assistant.PlatformEnum;

/**
 * @author: zhouyibing
 * @date: 2024/9/9
 */
public class UserIdUtils {

    public static String getImUserId(long userId, long mTUserId, int platform) {
        if (platform == PlatformEnum.DP.getType()) {
            return ImAccountTypeUtils.getImDpUserId(userId);
        }
        if (platform == PlatformEnum.MT.getType()) {
            return ImAccountTypeUtils.getImMtUserId(mTUserId);
        }
        return "";
    }
}
