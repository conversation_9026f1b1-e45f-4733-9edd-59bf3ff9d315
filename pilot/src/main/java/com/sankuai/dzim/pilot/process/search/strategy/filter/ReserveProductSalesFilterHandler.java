package com.sankuai.dzim.pilot.process.search.strategy.filter;

import com.dianping.deal.sales.common.datatype.SalesDisplayInfoDTO;
import com.google.common.collect.Maps;
import com.sankuai.dzim.pilot.acl.ProductAclService;
import com.sankuai.dzim.pilot.api.enums.assistant.PlatformEnum;
import com.sankuai.dzim.pilot.process.search.data.FilterOption;
import com.sankuai.dzim.pilot.process.search.data.ReserveQueryContext;
import com.sankuai.dzim.pilot.process.search.enums.ProductFilterOptionEnum;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Component
public class ReserveProductSalesFilterHandler implements ReserveProductFilterHandler {

    @Resource
    private ProductAclService productAclService;

    @Override
    public boolean match(ReserveQueryContext context) {
        if (context == null || CollectionUtils.isEmpty(context.getFilterOptions())) {
            return false;
        }
        List<String> filterTypeList = context.getFilterOptions().stream().map(FilterOption::getFilterType)
                .collect(Collectors.toList());
        return filterTypeList.contains(ProductFilterOptionEnum.PRODUCT_SALES.getCode());
    }

    @Override
    public List<DealGroupDTO> filter(ReserveQueryContext context, List<DealGroupDTO> dealGroupDTOList) {
        // 获取销量具体筛选条件
        FilterOption filterOption = context.getFilterOptions().stream()
                .filter(option -> ProductFilterOptionEnum.PRODUCT_SALES.getCode().equals(option.getFilterType()))
                .findFirst().orElse(null);
        int rankLimit = getSalesRankLimit(filterOption);

        // 查询商品销量
        Map<Long, SalesDisplayInfoDTO> bizProductId2Sales = getProductSales(dealGroupDTOList, context);

        // 取销量前rankLimit的商品
        return filterSalesProduct(dealGroupDTOList, bizProductId2Sales,
                rankLimit);
    }

    private Map<Long, SalesDisplayInfoDTO> getProductSales(List<DealGroupDTO> dealGroupDTOList,
            ReserveQueryContext context) {
        if (CollectionUtils.isEmpty(dealGroupDTOList)) {
            return Maps.newHashMap();
        }

        List<Long> bizProductIds = dealGroupDTOList.stream().map(DealGroupDTO::getBizProductId)
                .collect(Collectors.toList());
        boolean isMt = context.getPlatform() == PlatformEnum.MT.getType();

        CompletableFuture<Map<Long, SalesDisplayInfoDTO>> mapCompletableFuture = productAclService
                .batchQueryReserveSales(bizProductIds, isMt);
        if (mapCompletableFuture != null) {
            return mapCompletableFuture.join();
        }

        return Maps.newHashMap();
    }

    private int getSalesRankLimit(FilterOption filterOption) {
        if (filterOption == null) {
            return 10;
        }
        Map<String, Object> values = filterOption.getValues();
        if (MapUtils.isEmpty(values)) {
            return 10;
        }
        return (int)values.get("salesRankLimit");
    }

    private List<DealGroupDTO> filterSalesProduct(List<DealGroupDTO> dealGroupDTOList,
            Map<Long, SalesDisplayInfoDTO> bizProductId2Sales, int rankLimit) {
        // 先取出前rankLimit的业务商品id
        if (MapUtils.isEmpty(bizProductId2Sales)) {
            return dealGroupDTOList;
        }
        List<Map.Entry<Long, SalesDisplayInfoDTO>> topProducts = bizProductId2Sales.entrySet().stream()
                .filter(e -> e.getValue() != null && e.getValue().getSalesNum() != null)
                .sorted((o1, o2) -> o2.getValue().getSalesNum().compareTo(o1.getValue().getSalesNum())).limit(rankLimit)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(topProducts)) {
            return dealGroupDTOList;
        }
        List<Long> topBizProductIds = topProducts.stream().map(Map.Entry::getKey).filter(Objects::nonNull)
                .collect(Collectors.toList());
        return dealGroupDTOList.stream().filter(d -> topBizProductIds.contains(d.getBizProductId()))
                .collect(Collectors.toList());
    }

}
