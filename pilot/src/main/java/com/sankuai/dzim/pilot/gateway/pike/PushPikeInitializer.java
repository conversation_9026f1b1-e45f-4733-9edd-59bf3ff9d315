package com.sankuai.dzim.pilot.gateway.pike;

import com.sankuai.pike.message.sdk.listener.ListenerHolder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Component
public class PushPikeInitializer {

    public static final String PIKE_BIZ_ID = "pilot";

    @Autowired
    private PikeCallback pikeCallback;

    @PostConstruct
    public void init() {
        ListenerHolder.registerLister(PIKE_BIZ_ID, pikeCallback.buildConnectListener());
    }

}