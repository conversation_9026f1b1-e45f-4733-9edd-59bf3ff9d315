package com.sankuai.dzim.pilot.gateway.mq.consumer;

import com.dianping.vc.mq.client.api.annotation.Consumer;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.process.aibook.AIBookDispatchProcessService;
import com.sankuai.dzim.pilot.process.aibook.data.AIBookCallbackDelayData;
import com.sankuai.dzim.pilot.process.aibook.data.AIBookDispatchDelayData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2025/1/10 14:57
 */

@Slf4j
@Component
public class AIBookCallbackDelayConsumer {

    @Resource
    private AIBookDispatchProcessService aiBookDispatchProcessService;

    @Consumer(nameSpace = "daozong",
            appKey = "com.sankuai.mim.pilot",
            group = "pilot.ai.book.callback.consumer",
            topic = "pilot.ai.book.callback.delay.topic"
    )
    public ConsumeStatus recvMessage(String mafkaMessage) {
        AIBookCallbackDelayData data = JsonCodec.decode(mafkaMessage, AIBookCallbackDelayData.class);
        if (data == null) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        boolean isSuccess = aiBookDispatchProcessService.processCallbackTimeout(data);
        if (!isSuccess) {
            LogUtils.logFailLog(log, TagContext.builder().action("AIBookCallbackDelayConsumer").build(),
                    WarnMessage.build("AIBookCallbackDelayConsumer", "回调超时处理失败", ""),
                    data, false);
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
