package com.sankuai.dzim.pilot;

import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.meituan.mdp.boot.starter.fastjson.DisableFastJsonAutoType;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.ImportResource;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@DisableFastJsonAutoType
@SpringBootApplication(exclude={DataSourceAutoConfiguration.class})
@EnableAspectJAutoProxy(exposeProxy = true)
@EnableTransactionManagement
@Configuration
@ImportResource({"classpath*:config/spring/appcontext-*.xml",
        "classpath*:spring/local/appcontext-*.xml",
        "classpath*:config/spring/common/appcontext-*.xml",
        "classpath*:config/appcontext-*.xml"
})
public class ApplicationLoader {

    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(ApplicationLoader.class);
        application.setAdditionalProfiles(MdpContextUtils.getHostEnvStr());
        application.run(args);
    }
}


