package com.sankuai.dzim.pilot.dal.pilotdao;

import com.sankuai.dzim.pilot.dal.entity.pilot.BlockListEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BlockListDAO {
    List<BlockListEntity> selectByIds(@Param("userIds") List<String> userIds);

    BlockListEntity selectUserByIdAndAssistantType(@Param("userId") String userId, @Param("assistantType") int assistantType);

    int insert(@Param("entity") BlockListEntity blockListEntity);

    int batchInsert(@Param("entities") List<BlockListEntity> blockListEntities);

    int update(@Param("entity") BlockListEntity blockListEntity);

    int batchUpdate(@Param("entities") List<BlockListEntity> blockListEntities);
}
