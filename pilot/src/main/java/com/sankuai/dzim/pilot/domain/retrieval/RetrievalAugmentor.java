package com.sankuai.dzim.pilot.domain.retrieval;

import com.sankuai.dzim.pilot.domain.retrieval.data.RetrievalContext;
import com.sankuai.dzim.pilot.domain.retrieval.data.RetrievalResult;

public interface RetrievalAugmentor {

    /**
     * 召回知识,直接返回拼装好的结果
     *
     * @param context
     * @return
     */
    String retrieval(RetrievalContext context);

    /**
     * 返回知识列表
     * @param context
     * @return
     */
    RetrievalResult retrieveKnowledge(RetrievalContext context);
}
