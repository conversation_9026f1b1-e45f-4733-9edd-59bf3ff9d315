package com.sankuai.dzim.pilot.buffer.stream.build.ext;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dzim.message.common.utils.ImAccountTypeUtils;
import com.sankuai.dzim.pilot.acl.DzRenderAclService;
import com.sankuai.dzim.pilot.buffer.enums.StreamEventCardTypeEnum;
import com.sankuai.dzim.pilot.buffer.stream.build.ext.data.ProductCardData;
import com.sankuai.dzim.pilot.buffer.stream.build.ext.data.ShopCardData;
import com.sankuai.dzim.pilot.buffer.stream.vo.StreamEventCardDataVO;
import com.sankuai.dzim.pilot.chain.enums.AIServiceExtraKeyEnum;
import com.sankuai.dzim.pilot.scene.data.AssistantSceneContext;
import com.sankuai.dzim.pilot.scene.task.data.EnvContext;
import com.sankuai.dzim.pilot.utils.LionConfigUtil;
import com.sankuai.dzshoplist.aggregate.dzrender.enums.*;
import com.sankuai.dzshoplist.aggregate.dzrender.request.DzRenderRequest;
import com.sankuai.dzshoplist.aggregate.dzrender.request.param.ClientEnv;
import com.sankuai.dzshoplist.aggregate.dzrender.request.param.DzRenderIdDTO;
import com.sankuai.dzshoplist.aggregate.dzrender.request.param.RenderIdDTO;
import com.sankuai.dzshoplist.aggregate.dzrender.response.ShopRenderItem;
import com.sankuai.dzshoplist.aggregate.dzrender.response.common.PicDTO;
import com.sankuai.dzshoplist.aggregate.dzrender.response.product.ProductRenderDTO;
import com.sankuai.dzshoplist.aggregate.dzrender.response.product.ProductTagDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ShopProductCardBuildExt implements CardBuildExt {

    @Autowired
    private DzRenderAclService dzRenderAclService;

    @Autowired
    private LionConfigUtil lionConfigUtil;

    private static final String SPLIT = ":";

    @Override
    public boolean accept(StreamEventCardDataVO streamEventCardDataVO) {
        if (streamEventCardDataVO == null) {
            return false;
        }
        return streamEventCardDataVO.getType().equals(StreamEventCardTypeEnum.SHOP_PRODUCT_CARD.getType());
    }

    @Override
    public void padding(StreamEventCardDataVO streamEventCardDataVO, AssistantSceneContext context) {
        String key = streamEventCardDataVO.getKey();
        // 校验key的格式
        if (!checkKey(key)) {
            return;
        }

        // 解析key
        long shopId = Long.parseLong(key.split(SPLIT)[0]);

        List<Long> productIds = JsonCodec.converseList(key.split(SPLIT)[1], Long.class);

        // 查询门店数据
        Map<Long, ShopRenderItem> shopRenderItemMap = dzRenderAclService.queryShopRenderInfo(buildRenderRequest(shopId, productIds, context)).join();
        if (MapUtils.isEmpty(shopRenderItemMap) || shopRenderItemMap.get(shopId) == null) {
            return;
        }

        // 拼装数据
        buildCardProps(productIds, shopRenderItemMap.get(shopId), streamEventCardDataVO);
    }

    private void buildCardProps(List<Long> productIds, ShopRenderItem shopRenderItem, StreamEventCardDataVO streamEventCardDataVO) {
        Map<String, Object> cardProps = streamEventCardDataVO.getCardProps();
        if (CollectionUtils.isEmpty(productIds)) {
            return;
        }

        List<ProductRenderDTO> relatedProducts = shopRenderItem.getRelatedProducts();
        if (CollectionUtils.isEmpty(relatedProducts)) {
            return;
        }
        List<ProductCardData> productCardDataList = Lists.newArrayList();
        Map<Long, ProductRenderDTO> productRenderDTOMap = relatedProducts.stream().collect(Collectors.toMap(ProductRenderDTO::getProductId, Function.identity()));
        int index = 1;
        for (Long productId : productIds) {
            if (productRenderDTOMap.get(productId) == null) {
                continue;
            }
            ProductRenderDTO productRenderDTO = productRenderDTOMap.get(productId);
            ProductCardData productCardData = buildProductCard(index, productRenderDTO);
            productCardDataList.add(productCardData);
            index++;
        }
        cardProps.put("productCards", productCardDataList);
        cardProps.put("shopCard", buildShopCard(shopRenderItem));
    }

    private ShopCardData buildShopCard(ShopRenderItem shopRenderItem) {
        ShopCardData shopCardData = new ShopCardData();
        shopCardData.setShopId(shopRenderItem.getShopRenderId().getRenderId().getItemId());
        shopCardData.setShopName(shopRenderItem.getShopRenderInfo().getShopBasicInfo().getName());
        shopCardData.setArea(shopRenderItem.getShopRenderInfo().getShopBasicInfo().getAreaName());
        shopCardData.setJumpUrl(shopRenderItem.getShopRenderInfo().getShopBasicInfo().getJumpUrl());
        shopCardData.setScore(shopRenderItem.getShopRenderInfo().getShopBasicInfo().getStarScore() + "分");
        shopCardData.setAvgPrice(shopRenderItem.getShopRenderInfo().getPrice().getAvgPriceDesc());
        shopCardData.setDistance(getDistance(shopRenderItem.getShopRenderInfo().getShopBasicInfo().getDistance()));
        List<PicDTO> shopPic = shopRenderItem.getShopRenderInfo().getShopMultimedia().getShopPic();
        if (CollectionUtils.isNotEmpty(shopPic)) {
            shopCardData.setShopPics(shopPic.stream().map(PicDTO::getPicUrls).collect(Collectors.toList()));
        }
        return shopCardData;
    }

    private ProductCardData buildProductCard(int index, ProductRenderDTO productRenderDTO) {
        ProductCardData productCardData = new ProductCardData();
        productCardData.setProductId(productRenderDTO.getProductId());
        productCardData.setTitle(productRenderDTO.getTitle());
        productCardData.setHeadPic(productRenderDTO.getHeadPic());
        productCardData.setJumpUrl(productRenderDTO.getJumpUrl());
        productCardData.setSalePrice(transferPrice(productRenderDTO.getPrice().getSalePrice()));
        productCardData.setMarketPrice(transferPrice(productRenderDTO.getPrice().getMarketPrice()));
        productCardData.setDiscountTag(getDiscountTag(productRenderDTO.getProductTags()));
        if (lionConfigUtil.getNeedPayButton()) {
            productCardData.setButtonText(String.format("帮我买第%s个", index));
        }
        return productCardData;
    }

    private String getDiscountTag(List<ProductTagDTO> productTags) {
        if (CollectionUtils.isEmpty(productTags)) {
            return StringUtils.EMPTY;
        }

        List<String> discountTags = productTags.stream()
                .filter(productTagDTO -> productTagDTO.getType() == ProductTagTypeEnum.DISCOUNT_PROMO_TAG.getCode())
                .map(ProductTagDTO::getTagText)
                .findFirst().orElse(Lists.newArrayList());

        return CollectionUtils.isEmpty(discountTags) ? StringUtils.EMPTY : discountTags.get(0);
    }

    private String transferPrice(BigDecimal salePrice) {
        // 取小数点后一位
        return salePrice.setScale(1, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
    }


    private String getDistance(double distance) {
        if (distance < 1000) {
            return (int) distance + "m";
        }
        return String.format("%.1f", distance / 1000) + "km";
    }

    private DzRenderRequest buildRenderRequest(long shopId, List<Long> productIds, AssistantSceneContext context) {
        DzRenderRequest dzRenderRequest = new DzRenderRequest();
        dzRenderRequest.setRenderIds(buildRenderId(shopId, productIds));
        dzRenderRequest.setRenderType(RenderTypeEnum.POI_PRODUCT.getType());
        dzRenderRequest.setSceneCode(SceneCodeEnum.SHOP_LIST_HAIRCUT_FOOT.getCode());
        dzRenderRequest.setSpaceKey(SpaceKeyEnum.PLATFORM_AGENT_RENDER.getCode());
        dzRenderRequest.setClientEnv(buildClientEnv(context));
        return dzRenderRequest;
    }

    private ClientEnv buildClientEnv(AssistantSceneContext context) {
        String envContextStr = Optional.ofNullable(context)
                .map(AssistantSceneContext::getExtra)
                .map(extraInfo -> MapUtils.getString(extraInfo, AIServiceExtraKeyEnum.ENV_CONTEXT.getKey(), StringUtils.EMPTY))
                .orElse(StringUtils.EMPTY);
        EnvContext envContext = JsonCodec.decode(envContextStr, EnvContext.class);
        if (envContext == null) {
            return null;
        }

        boolean isDp = ImAccountTypeUtils.isDpUserId(context.getUserId());
        ClientEnv clientEnv = new ClientEnv();
        clientEnv.setGpsCityId(envContext.getCityId());
        clientEnv.setCityId(envContext.getCityId());
        clientEnv.setLng(envContext.getLng());
        clientEnv.setLat(envContext.getLat());
        clientEnv.setPageLng(envContext.getLng());
        clientEnv.setPageLat(envContext.getLat());
        clientEnv.setUuId(isDp ? "" : envContext.getDeviceId());
        clientEnv.setDpId(isDp ? envContext.getDeviceId() : "");
        clientEnv.setPlatform(isDp ? PlatformEnum.DP.getCode() : PlatformEnum.MT.getCode());
        clientEnv.setUserId(ImAccountTypeUtils.getAccountId(context.getUserId()));
        clientEnv.setVersion(envContext.getAppVersion());
        clientEnv.setClientType(isDp ? ClientTypeEnum.DP_APP_ANDROID.getCode() : ClientTypeEnum.MT_APP_ANDROID.getCode());
        return clientEnv;
    }

    private List<DzRenderIdDTO> buildRenderId(long shopId, List<Long> productIds) {
        List<DzRenderIdDTO> renderIds = Lists.newArrayList();
        DzRenderIdDTO dzRenderIdDTO = new DzRenderIdDTO();
        RenderIdDTO renderIdDTO = new RenderIdDTO();
        renderIdDTO.setItemId(shopId);
        renderIdDTO.setItemType(RenderItemTypeEnum.POI.getCode());
        dzRenderIdDTO.setRenderId(renderIdDTO);

        List<RenderIdDTO> relatedProductIds = Lists.newArrayList();
        for (Long productId : productIds) {
            RenderIdDTO relatedProductId = buildRenderIdDTO(productId);
            relatedProductIds.add(relatedProductId);
        }
        dzRenderIdDTO.setRelatedIds(relatedProductIds);
        renderIds.add(dzRenderIdDTO);
        return renderIds;
    }

    private RenderIdDTO buildRenderIdDTO(Long productId) {
        RenderIdDTO relatedProductId = new RenderIdDTO();
        relatedProductId.setItemId(productId);
        relatedProductId.setItemType(RenderItemTypeEnum.PRODUCT.getCode());
        Map<String, String> extraParams = Maps.newHashMap();
        extraParams.put("product_type", "1");
        relatedProductId.setExtraParams(extraParams);
        return relatedProductId;
    }

    private boolean checkKey(String key) {
        if (StringUtils.isBlank(key)) {
            return false;
        }

        String[] splitKey = key.split(SPLIT);
        if (splitKey.length < 2) {
            return false;
        }
        // 第一个数字
        if (!NumberUtils.isDigits(splitKey[0])) {
            return false;
        }

        // 第二个列表
        List<Long> productIds = JsonCodec.converseList(splitKey[1], Long.class);
        if (CollectionUtils.isEmpty(productIds)) {
            return false;
        }
        return true;
    }

    @Override
    public String cardDecode(StreamEventCardDataVO streamEventCardDataVO) {
        Map<String, Object> cardProps = streamEventCardDataVO.getCardProps();
        // 提取 shopCardData
        Object productCardsObj = cardProps.get("productCards");
        if (productCardsObj == null) {
            return StringUtils.EMPTY;
        }
        List<ProductCardData> productCardDataList = JsonCodec.converseList(JsonCodec.encodeWithUTF8(productCardsObj), ProductCardData.class);
        if (CollectionUtils.isEmpty(productCardDataList)) {
            return StringUtils.EMPTY;
        }

        ShopCardData shopCardData = JsonCodec.decode(JsonCodec.encodeWithUTF8(cardProps.get("shopCard")), ShopCardData.class);
        if (shopCardData == null) {
            return StringUtils.EMPTY;
        }

        int index = 1;

        // 构建描述性字符串
        StringBuilder description = new StringBuilder();
        description.append("为您推荐**ShopId为:").append(shopCardData.getShopId()).append("**, 门店名字为:").append(shopCardData.getShopName()).append("的商品如下:\n").append("\n");
        for (ProductCardData productCardData : productCardDataList) {
            description.append("第").append(index).append("个商品: ").append(productCardData.decodeDesc()).append("\n");
            index++;
        }

        return description.toString();
    }
}
