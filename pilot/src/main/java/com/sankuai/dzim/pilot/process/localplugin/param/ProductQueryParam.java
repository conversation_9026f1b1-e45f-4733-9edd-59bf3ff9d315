package com.sankuai.dzim.pilot.process.localplugin.param;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import lombok.Data;

@Data
public class ProductQueryParam {

    @JsonPropertyDescription("需要查询的门店Id,门店Id请从系统消息中获取")
    @JsonProperty(required = true)
    private long shopId;

    @JsonPropertyDescription("需要查询的门店平台,平台请从系统消息中获取")
    @JsonProperty(required = true)
    private long platform;

    @JsonPropertyDescription("与商品相关的具体问题,越具体越好,最好是一个问句,例如“有洗牙的团购吗?”等")
    @JsonProperty(required = true)
    private String question;
}
