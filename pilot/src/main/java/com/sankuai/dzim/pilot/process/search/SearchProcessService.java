package com.sankuai.dzim.pilot.process.search;

import com.sankuai.dzim.pilot.process.data.ProductRetrievalData;
import com.sankuai.dzim.pilot.process.data.ShopRetrievalData;
import com.sankuai.dzim.pilot.process.localplugin.param.ProductMergeSearchParam;
import com.sankuai.dzim.pilot.process.localplugin.param.ShopMergeSearchParam;
import com.sankuai.dzim.pilot.process.localplugin.param.ShopProductSearchParam;

import java.util.List;

/**
 * 供给搜索处理服务
 * 供给召回+供给详情
 * 店内高销团购召回
 * <AUTHOR>
 */
public interface SearchProcessService {

    /**
     * 搜索门店
     * @param shopMergeSearchParam 搜索参数
     * @return 门店列表
     */
    List<ShopRetrievalData> searchShops(ShopMergeSearchParam shopMergeSearchParam);

    /**
     * 搜索商品
     * @param productMergeSearchParam 搜索参数
     * @return 商品列表
     */
    List<ProductRetrievalData> searchProducts(ProductMergeSearchParam productMergeSearchParam);

    /**
     * 搜索店内商品
     * @param shopProductSearchParam 搜索参数
     * @return 商品列表
     */
    List<ProductRetrievalData> searchShopProducts(ShopProductSearchParam shopProductSearchParam);

    /**
     * 搜索店内预订商品
     */
    List<ProductRetrievalData> searchShopReserveProducts(ShopProductSearchParam shopProductSearchParam);

}
