package com.sankuai.dzim.pilot.process.aibook.data;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/1/10 11:50
 */
@Data
public class DispatchConfigData {

    /**
     * 预约外呼开始时间：小时
     */
    private int dispatchBeginHour;

    /**
     * 预约外呼结束时间：小时
     */
    private int dispatchEndHour;


    /**
     * 预约延迟外呼的随机间隔
     */
    private int dispatchInterval;


    /**
     * 预约接口bizSourceId
     */
    private int bookLeadsBizSourceId;


    /**
     * 门店外呼次数限制
     */
    private int shopBookCntLimit;


    /**
     * 门店召回黑名单
     */
    private List<Long> dpShopIdBlackList;


    /**
     * 外呼回调后延时时间多久自动置为失败
     */
    private int callbackDelaySeconds;

    /**
     * 预约外呼时间的分钟偏移（即用户到店耗时）
     */
    private int dispatchMinuteOffset;


    /**
     * 外呼接口返回的code列表，控制需要加入门店电话黑名单
     */
    private List<Integer> shopPhoneBlackListCode;


    /**
     * 门店重拨次数限制
     */
    private int shopCallAgainTimeLimit;


    /**
     * 重拨延时时长
     */
    private int callAgainDelaySeconds;


    /**
     * 性别persona标签id
     */
    private int genderPersonaTagId;
}
