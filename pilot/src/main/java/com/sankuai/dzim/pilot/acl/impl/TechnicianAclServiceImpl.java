package com.sankuai.dzim.pilot.acl.impl;

import com.dianping.technician.common.api.domain.Environment;
import com.dianping.technician.dto.shopsearch.TechCard;
import com.dianping.technician.dto.shopsearch.TechCategory;
import com.dianping.technician.dto.shopsearch.TechModuleRequest;
import com.dianping.technician.dto.shopsearch.TechModuleResult;
import com.dianping.technician.service.shopsearch.TechShopSearchService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.inf.AthenaInf;
import com.sankuai.athena.inf.rpc.RpcClient;
import com.sankuai.dzim.pilot.acl.TechnicianAclService;
import com.sankuai.dzim.pilot.process.data.TechnicianData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 手艺人服务
 * <AUTHOR>
 */
@Slf4j
@Component
public class TechnicianAclServiceImpl implements TechnicianAclService {

    @RpcClient(url = "com.dianping.technician.service.shopsearch.TechShopSearchService", timeout = 500)
    private TechShopSearchService techShopSearchService;

    @Override
    public CompletableFuture<List<TechnicianData>> searchShopTechnicians(TechModuleRequest request) {
        if (request == null) {
            return CompletableFuture.completedFuture(Lists.newArrayList());
        }

        return AthenaInf
                .getRpcCompletableFuture(techShopSearchService.searchShopTechList(request))
                .thenApply(this::convertTechnicianCardData)
                .exceptionally(e -> {
                    log.error("searchShopTechnicians error, request:{}", request, e);
                    return Lists.newArrayList();
                });
    }

    private List<TechnicianData> convertTechnicianCardData(TechModuleResult result) {
        if (result == null || CollectionUtils.isEmpty(result.getCategoryList())) {
            return Lists.newArrayList();
        }
        List<TechnicianData> res = Lists.newArrayList();
        result.getCategoryList().stream().filter(Objects::nonNull).forEach(techCategory -> {
            if (CollectionUtils.isEmpty(techCategory.getTechnicians())) {
                return;
            }
            List<TechCard> technicians = techCategory.getTechnicians();
            List<TechnicianData> techs = technicians.stream().filter(Objects::nonNull)
                    .map(techCard -> convertTechnicianCardData(techCard, techCategory))
                    .collect(Collectors.toList());
            res.addAll(techs);
        });
        return res;
    }

    private TechnicianData convertTechnicianCardData(TechCard techCard, TechCategory techCategory) {
        if (techCard == null) {
            return null;
        }
        TechnicianData data = new TechnicianData();
        data.setTechnicianId(techCard.getTechnicianId());
        data.setTechnicianId(techCard.getTechnicianId());
        data.setCategoryName(techCategory.getCategoryName());
        data.setLikesTitle(techCard.getLikesTitle());
        data.setVote(techCard.getVote());
        data.setSellNum(techCard.getSellNum());
        data.setFeatureTag(techCard.getFeatureTag());
        data.setSkillList(techCard.getSkillList());
        data.setSkill(techCard.getSkill());
        data.setOrder(techCard.getOrder());
        data.setSeniority(techCard.getSeniority());
        data.setTitle(techCard.getTitle());
        data.setName(techCard.getName());
        data.setCertifiedUrl(techCard.getCertifiedUrl());
        data.setCertifiedNo(techCard.getCertifiedNo());
        data.setAvatarUrl(techCard.getAvatarUrl());
        data.setJobNumber(techCard.getJobNumber());
        data.setTechGoodReviewCount(techCard.getTechGoodReviewCount());
        data.setWorksCount(techCard.getWorksCount());
        data.setReviewCount(techCard.getReviewCount());
        data.setTechReviewCountPer(techCard.getTechReviewCountPer());
        data.setSubTitle(techCard.getSubTitle());
        data.setStarScore(techCard.getStarScore());
        data.setSummary(techCard.getSummary());
        data.setEducationBackground(techCard.getEducationBackground());
        data.setAge(techCard.getAge());
        data.setNativePlace(techCard.getNativePlace());
        data.setWorkExperience(techCard.getWorkExperience());
        data.setCertificateNames(techCard.getCertificateNames());
        data.setServiceTimeType(techCard.getServiceTimeType());
        data.setPeopleServedNum(techCard.getPeopleServedNum());
        data.setComment(techCard.getComment());
        return data;
    }
}
