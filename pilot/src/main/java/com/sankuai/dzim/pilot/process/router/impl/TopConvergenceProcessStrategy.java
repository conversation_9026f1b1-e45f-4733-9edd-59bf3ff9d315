package com.sankuai.dzim.pilot.process.router.impl;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Maps;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.api.data.search.generative.HeadInfoDTO;
import com.sankuai.dzim.pilot.api.data.search.generative.SuggestionAnswerDTO;
import com.sankuai.dzim.pilot.api.data.search.generative.TabInfoDTO;
import com.sankuai.dzim.pilot.api.data.search.generative.TemplateDataDTO;
import com.sankuai.dzim.pilot.api.enums.search.generative.GenerativeSearchAnswerTemplateTypeEnum;
import com.sankuai.dzim.pilot.process.data.TemplateBaseInfo;
import com.sankuai.dzim.pilot.process.router.TemplateDataProcessStrategy;
import com.sankuai.dzim.pilot.process.router.data.RouteResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import static com.caucho.hessian.io.HessianInputFactory.log;

/**
 * 上面的需求tab移除的searchWord
 * 下面的项目tab移除subTitle和link，同时在项目tab列表中添加一个”全部“tab（如果没有配置）
 * 
 * <AUTHOR>
 * @since 2024/09/29 17:21
 */
@Slf4j
@Component
public class TopConvergenceProcessStrategy implements TemplateDataProcessStrategy {
    private static final String ALL = "全部";

    private static final String MORE_QUESTION = "更多问题";

    @ConfigValue(
            key = "com.sankuai.mim.pilot.template.base.config",
            defaultValue = "{\"2\":{\"headInfo\":{\"titleInfo\":{\"title\":\"根据症状选项目\",\"icon\":\"默认-icon\",\"link\":\"默认-link\"},\"moreInfo\":{\"title\":\"更多\",\"icon\":\"默认-更多-icon\",\"link\":\"默认-更多-link\"}},\"themeInfo\":{},\"relatedQuestionBaseLink\":\"相关问题跳链\",\"projectTabBaseLink\":\"项目跳链\"}}"
    )
    private Map<Integer, TemplateBaseInfo> bizType2TemplateBaseInfo;

    @ConfigValue(key = "com.sankuai.mim.pilot.avoid.add.more.question.biz.types", defaultValue = "[14]")
    private Set<Integer> avoidAddMoreQuestionBizTypes;

    @Override
    public boolean match(RouteResponse routeResponse) {
        return GenerativeSearchAnswerTemplateTypeEnum.UNIFIED_TOP_CONVERGENCE.getType() == routeResponse
                .getTemplateType();
    }

    @Override
    public void process(RouteResponse routeResponse) {
        Optional.ofNullable(routeResponse).map(RouteResponse::getSuggestionAnswerDTO)
                .map(SuggestionAnswerDTO::getTemplateDataDTO)
                .ifPresent(templateDataDTO -> processTemplateDataDTO(templateDataDTO, routeResponse));
    }

    private void processTemplateDataDTO(TemplateDataDTO templateDataDTO, RouteResponse routeResponse) {
        // 移除头部的更多信息中的link
        Optional.ofNullable(templateDataDTO).map(TemplateDataDTO::getHeadInfo).map(HeadInfoDTO::getMoreInfo)
                .ifPresent(moreInfo -> {
                    if (StringUtils.isNotBlank(moreInfo.getIcon())) {
                        moreInfo.setLink(null);
                    }
                });
        Optional.ofNullable(templateDataDTO).map(TemplateDataDTO::getTabInfo).ifPresent(
                tabInfoList -> tabInfoList.forEach(tabInfoDTO -> processTabInfoDTO(tabInfoDTO, routeResponse)));

    }

    private void processTabInfoDTO(TabInfoDTO tabInfoDTO, RouteResponse routeResponse) {
        Optional.ofNullable(tabInfoDTO).ifPresent(tabInfo -> {
            Optional.ofNullable(tabInfo.getOuterProjectTabs()).ifPresent(projectTabList -> {
                projectTabList.forEach(this::processProjectTabDTO);
                addAllTabIfNeeded(tabInfo, routeResponse.getQuestion());
            });
        });
        Optional.ofNullable(tabInfoDTO).flatMap(tabInfo -> Optional.ofNullable(tabInfo.getRelatedQuestions()))
                .ifPresent(relatedQuestions -> {
                    boolean hasMoreQuestion = relatedQuestions.stream()
                            .anyMatch(relatedQuestion -> MORE_QUESTION.equals(relatedQuestion.getQuestion()));
                    if (hasMoreQuestion || avoidAddMoreQuestionBizTypes.contains(routeResponse.getBizType())) {
                        return;
                    }
                    Map<String, String> bizParamsMap = Maps.newHashMap();
                    bizParamsMap.put("query", routeResponse.getQuestion());
                    String bizParams = null;
                    try {
                        bizParams = URLEncoder.encode(JsonCodec.encodeWithUTF8(bizParamsMap), "UTF-8");
                    } catch (Exception e) {
                        LogUtils.logFailLog(log, TagContext.builder().action("buildWelcomeCard").build(),
                                new WarnMessage("TopConvergenceProcessStrategy", "更多问题卡牌encode参数异常", ""), bizParamsMap, null, e);
                    }
                    relatedQuestions.add(new TabInfoDTO.RelatedQuestion(null, MORE_QUESTION,
                            bizType2TemplateBaseInfo.get(routeResponse.getBizType()).getRelatedQuestionBaseLink() + "&bizParams=" + bizParams));
                });
    }

    private void processProjectTabDTO(TabInfoDTO.ProjectTabDTO projectTabDTO) {
        if (projectTabDTO != null) {
            projectTabDTO.setSubTitle(null);
            projectTabDTO.setLink(null);
        }
    }

    private void addAllTabIfNeeded(TabInfoDTO tabInfoDTO, String question) {
        tabInfoDTO.setInnerProjectTabs(null);
        if (tabInfoDTO.getOuterProjectTabs() == null) {
            return;
        }
        boolean hasAllTab = tabInfoDTO.getOuterProjectTabs().stream()
                .anyMatch(projectTabDTO -> projectTabDTO != null && ALL.equals(projectTabDTO.getTitle()));
        if (!hasAllTab) {
            TabInfoDTO.ProjectTabDTO allTab = new TabInfoDTO.ProjectTabDTO();
            allTab.setTitle(ALL);
            allTab.setSearchWord(question);
            tabInfoDTO.getOuterProjectTabs().add(0, allTab);
        }
    }
}
