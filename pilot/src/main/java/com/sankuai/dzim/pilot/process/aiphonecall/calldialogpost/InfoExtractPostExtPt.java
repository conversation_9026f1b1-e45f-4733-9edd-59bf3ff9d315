package com.sankuai.dzim.pilot.process.aiphonecall.calldialogpost;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dzim.pilot.acl.FridayAclService;
import com.sankuai.dzim.pilot.acl.ShopAclService;
import com.sankuai.dzim.pilot.acl.data.ChatCompletionConfig;
import com.sankuai.dzim.pilot.acl.data.fraiday.ChatCompletionRequest;
import com.sankuai.dzim.pilot.acl.data.fraiday.ChatCompletionResponse;
import com.sankuai.dzim.pilot.acl.data.fraiday.FridayMessage;
import com.sankuai.dzim.pilot.acl.data.fraiday.FridayMessageRoleEnum;
import com.sankuai.dzim.pilot.dal.entity.aiphonecall.AIPhoneCallDetailEntity;
import com.sankuai.dzim.pilot.dal.entity.aiphonecall.AIPhoneCallRecordEntity;
import com.sankuai.dzim.pilot.dal.entity.aiphonecall.AIPhoneCallTaskEntity;
import com.sankuai.dzim.pilot.dal.pilotdao.aiphonecall.AIPhoneCallDetailDAO;
import com.sankuai.dzim.pilot.dal.pilotdao.aiphonecall.AIPhoneCallRecordDAO;
import com.sankuai.dzim.pilot.dal.pilotdao.aiphonecall.AIPhoneCallTaskDAO;
import com.sankuai.dzim.pilot.domain.ShopDomainService;
import com.sankuai.dzim.pilot.enums.AIPhoneCallExtraKeyEnum;
import com.sankuai.dzim.pilot.enums.AIPhoneCallSceneTypeEnum;
import com.sankuai.dzim.pilot.process.aiphonecall.callback.AIPhoneCallbackProcessor;
import com.sankuai.dzim.pilot.process.aiphonecall.data.AIPhoneCallBackData;
import com.sankuai.dzim.pilot.process.aiphonecall.data.AIPhoneCallDialogPostContext;
import com.sankuai.dzim.pilot.utils.DateUtils;
import com.sankuai.dzim.pilot.utils.LionConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
@CallDialogPost(name = "InfoExtractPostExtPt")
public class InfoExtractPostExtPt implements CallDialogPostExtPt {

    @Autowired
    private LionConfigUtil lionConfigUtil;

    @Autowired
    private FridayAclService fridayAclService;

    @Autowired
    private AIPhoneCallbackProcessor callbackProcessor;

    @Autowired
    private ShopDomainService shopDomainService;

    @Autowired
    private ShopAclService shopAclService;

    @Autowired
    private AIPhoneCallTaskDAO callTaskDAO;

    @Autowired
    private AIPhoneCallDetailDAO callDetailDAO;

    @Autowired
    private AIPhoneCallRecordDAO callRecordDAO;

    @Override
    public void handle(AIPhoneCallDialogPostContext context) {
        try {
            // 查询明细和任务
            AIPhoneCallTaskEntity callTaskEntity = callTaskDAO.getByTaskId(context.getTaskId());
            AIPhoneCallDetailEntity callDetailEntity = callDetailDAO.getById(context.getDetailId());
            AIPhoneCallRecordEntity callRecordEntity = callRecordDAO.getByContactId(callDetailEntity.getContactId());

            // 大模型提取信息
            ChatCompletionConfig chatCompletionConfig = getChatCompletionConfig(context.getAiPhoneCallSceneTypeEnum(), context.getShopId(), context.getPlatform());
            ChatCompletionRequest request = buildChatCompletionRequest(context.getCallDialog(), chatCompletionConfig, callRecordEntity.getEndTime());
            ChatCompletionResponse chatCompletionResponse = fridayAclService.chatCompletion(chatCompletionConfig.getAppId(), request);
            if (chatCompletionResponse == null || CollectionUtils.isEmpty(chatCompletionResponse.getChoices())) {
                return;
            }

            // 更新外呼task的扩展数据
            String content = chatCompletionResponse.getChoices().get(0).getMessage().getContent();
            callDetailEntity.putExtraDataByKey(AIPhoneCallExtraKeyEnum.CALL_DIALOG_EXTRACT_INFO, StringUtils.defaultString(content));
            callDetailDAO.updateExtraData(callDetailEntity);
        } catch (Exception e) {
            log.error("InfoExtractPostExtPt error, callDetailId = {}.", context.getDetailId(), e);
        }
    }

    private ChatCompletionConfig getChatCompletionConfig(AIPhoneCallSceneTypeEnum aiPhoneCallSceneTypeEnum, long shopId, int platform) {
        Map<String, ChatCompletionConfig> infoExtractConfigMap = MapUtils.emptyIfNull(lionConfigUtil.getDialogInfoExtractConfigMap());

        long dpShopId = shopDomainService.getDpShopId(shopId, platform);
        List<Integer> catIds = shopDomainService.getBackCategoryIdList(dpShopId);
        catIds = CollectionUtils.emptyIfNull(catIds).stream().sorted(Comparator.reverseOrder()).collect(Collectors.toList());

        for (int catId: CollectionUtils.emptyIfNull(catIds)) {
            String configKey = buildInfoExtractConfigKey(aiPhoneCallSceneTypeEnum.getType(), catId);
            if (infoExtractConfigMap.containsKey(configKey)) {
                return infoExtractConfigMap.get(configKey);
            }
        }
        return infoExtractConfigMap.get(String.valueOf(aiPhoneCallSceneTypeEnum.getType()));
    }

    private String buildInfoExtractConfigKey(int sceneType, int categoryId) {
        return sceneType + "_" + categoryId;
    }

    private ChatCompletionRequest buildChatCompletionRequest(String naturalCallDialog, ChatCompletionConfig chatCompletionConfig, Date callEndTime) {
        ChatCompletionRequest request = new ChatCompletionRequest();
        request.setMax_tokens(chatCompletionConfig.getMaxTokens());
        request.setModel(chatCompletionConfig.getModel());
        request.setTop_p(chatCompletionConfig.getTopP());
        request.setMessages(buildMessages(naturalCallDialog, chatCompletionConfig.getSystemPrompt(), callEndTime));
        request.setTemperature(chatCompletionConfig.getTemperature());
        if (chatCompletionConfig.isJsonFormat()) {
            Map<String, String> responseFormatMap = Maps.newHashMap();
            responseFormatMap.put("type", "json_object");
            request.setResponse_format(responseFormatMap);
        }
        return request;
    }

    private List<FridayMessage> buildMessages(String naturalCallDialog, String systemPrompt, Date callEndTime) {
        List<FridayMessage> fridayMessages = Lists.newArrayList();
        FridayMessage systemMessage = new FridayMessage();
        systemMessage.setContent(systemPrompt);
        systemMessage.setRole(FridayMessageRoleEnum.SYSTEM.getValue());
        fridayMessages.add(systemMessage);

        FridayMessage userMessage = new FridayMessage();
        String content = String.format("当前时间：%s\n%s", DateUtils.covertDateStr(callEndTime), naturalCallDialog);
        userMessage.setContent(content);
        userMessage.setRole(FridayMessageRoleEnum.USER.getValue());
        fridayMessages.add(userMessage);
        return fridayMessages;
    }
}
