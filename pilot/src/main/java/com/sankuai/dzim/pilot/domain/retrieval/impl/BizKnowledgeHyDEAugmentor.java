package com.sankuai.dzim.pilot.domain.retrieval.impl;

import com.google.common.collect.Lists;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.dzim.pilot.acl.FridayAclService;
import com.sankuai.dzim.pilot.acl.data.fraiday.*;
import com.sankuai.dzim.pilot.dal.entity.BizKnowledgeEntity;
import com.sankuai.dzim.pilot.dal.respository.BizKnowledgeRepositoryService;
import com.sankuai.dzim.pilot.domain.annotation.Retrieval;
import com.sankuai.dzim.pilot.domain.retrieval.RetrievalAugmentor;
import com.sankuai.dzim.pilot.domain.retrieval.data.HyDEConfig;
import com.sankuai.dzim.pilot.domain.retrieval.data.RetrievalContext;
import com.sankuai.dzim.pilot.domain.retrieval.data.RetrievalResult;
import com.sankuai.dzim.pilot.utils.LionConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@Retrieval(name = "BizKnowledgeHyDERetrieval", desc = "业务知识检索器(HyDE)")
@Slf4j
public class BizKnowledgeHyDEAugmentor implements RetrievalAugmentor {

    @Autowired
    private BizKnowledgeRepositoryService bizKnowledgeRepositoryService;

    @Autowired
    private FridayAclService fridayAclService;

    @Autowired
    private LionConfigUtil lionConfigUtil;

    @MdpConfig("com.sankuai.mim.pilot.augmentor.hyde.config")
    private HyDEConfig hyDEConfig;


    @Override
    public String retrieval(RetrievalContext context) {
        Assert.isTrue(StringUtils.isNotBlank(context.getQuery()), "搜索关键词不能为空");

        // hyde检索
        String hQuery = getHyDEQuery(context);
        if (StringUtils.isNotBlank(hQuery)) {
            context.setQuery(hQuery);
        }

        if (context.getType2SubjectIdMap().size() == 1) {
            return retrievalBizKnowledge(context);
        }

        return batchRetrievalBizKnowledge(context);
    }

    private String getHyDEQuery(RetrievalContext context) {
        try {
            ChatCompletionRequest request = new ChatCompletionRequest();
            request.setModel(hyDEConfig.getModel());
            request.setTemperature(hyDEConfig.getTemperature());
            List<FridayMessage> fridayMessages = Lists.newArrayList();
            fridayMessages.add(new FridayMessage(FridayMessageRoleEnum.SYSTEM.getValue(), hyDEConfig.getPrompt()));
            fridayMessages.add(new FridayMessage(FridayMessageRoleEnum.USER.getValue(), context.getQuery()));
            request.setMessages(fridayMessages);

            ChatCompletionResponse chatCompletionResponse = fridayAclService.chatCompletion(hyDEConfig.getAppId(), request);
            if (chatCompletionResponse == null || CollectionUtils.isEmpty(chatCompletionResponse.getChoices())) {
                return StringUtils.EMPTY;
            }
            return chatCompletionResponse.getChoices().get(0).getMessage().getContent();
        } catch (Exception e) {
            log.error("getHyDEQuery error, context: {}", context, e);
        }
        return StringUtils.EMPTY;
    }

    private String retrievalBizKnowledge(RetrievalContext context) {
        List<Integer> types = new ArrayList<>(context.getType2SubjectIdMap().keySet());
        List<BizKnowledgeEntity> knowledges = bizKnowledgeRepositoryService.searchBizKnowledge(context.getQuery(), types.get(0), context.getTopK());

        if (CollectionUtils.isEmpty(knowledges)) {
            return StringUtils.EMPTY;
        }

        knowledges.sort(Comparator.comparing(BizKnowledgeEntity::getSimilarity).reversed());
        return "以下是你可以参考的知识:\n" + knowledges.stream().map(entity -> entity.getQuestion() + "\n" + entity.getAnswer()).collect(Collectors.joining("\n\n"));
    }

    @NotNull
    private String batchRetrievalBizKnowledge(RetrievalContext context) {
        // 0.query向量化
        List<Double> vector = fridayAclService.embedding(lionConfigUtil.getKnowledgeEmbeddingAppId(), FridayConstant.TEXT_EMBEDDING_ADA_002, FridayConstant.TOKEN_LIMIT_1000, context.getQuery());

        List<BizKnowledgeEntity> knowledges = Lists.newArrayList();
        List<String> mask = Lists.newArrayList();
        Set<Integer> types = context.getType2SubjectIdMap().keySet();
        for (Integer type : types) {
            List<BizKnowledgeEntity> knowledge = bizKnowledgeRepositoryService.searchBizKnowledgeByVector(context.getQuery(), vector, type, context.getTopK());
            knowledge = knowledge.stream().filter(entity -> entity.getSimilarity() > 0.8).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(knowledge)) {
                continue;
            }
            List<BizKnowledgeEntity> unique = Lists.newArrayList();
            for (BizKnowledgeEntity entity : knowledge) {
                if (mask.contains(entity.getQuestion() + entity.getAnswer())) {
                    continue;
                }
                mask.add(entity.getQuestion() + entity.getAnswer());
                unique.add(entity);
            }
            knowledges.addAll(unique);
        }

        if (CollectionUtils.isEmpty(knowledges)) {
            return StringUtils.EMPTY;
        }

        knowledges.sort(Comparator.comparing(BizKnowledgeEntity::getSimilarity).reversed());
        if (knowledges.size() > context.getTopK()) {
            knowledges = knowledges.subList(0, context.getTopK());
        }
        return "以下是你可以参考的知识:\n" + knowledges.stream().map(entity -> entity.getQuestion() + "\n" + entity.getAnswer()).collect(Collectors.joining("\n\n"));
    }

    @Override
    public RetrievalResult retrieveKnowledge(RetrievalContext context) {
        return null;
    }
}
