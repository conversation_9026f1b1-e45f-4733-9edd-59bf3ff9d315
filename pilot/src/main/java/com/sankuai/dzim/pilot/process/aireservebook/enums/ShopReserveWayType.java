package com.sankuai.dzim.pilot.process.aireservebook.enums;

import com.google.common.collect.Maps;

import java.util.Map;

/**
 * @author: w<PERSON><PERSON><PERSON><PERSON>
 * @create: 2025-04-20
 * @description: 商户预约方式类型
 */
public enum ShopReserveWayType {
    ONLINE(1, "在线系统"),
    PHONE(2, "电话外呼");

    private static final Map<Integer, ShopReserveWayType> TYPE_MAP = Maps.newHashMap();

    static {
        for (ShopReserveWayType way : values()) {
            TYPE_MAP.put(way.getType(), way);
        }
    }

    ShopReserveWayType(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public int getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static ShopReserveWayType getByType(Integer type) {
        if (type == null) {
            return null;
        }
        return TYPE_MAP.get(type);
    }

    private int type;
    private String desc;
}
