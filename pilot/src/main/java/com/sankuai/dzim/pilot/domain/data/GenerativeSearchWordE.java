package com.sankuai.dzim.pilot.domain.data;

import lombok.Data;

/**
 * @author: zhouyibing
 * @date: 2024/6/17
 */
@Data
public class GenerativeSearchWordE {

    /**
     * 自增主键ID
     */
    private Long id;

    /**
     * 业务类型
     */
    private Integer bizType;

    /**
     * 搜索问题
     */
    private String question;

    /**
     * 答案模板类型
     */
    private Integer templateType;

    /**
     * 排序分，分值越大优先级越高
     */
    private Integer score;

    /**
     * 关键词，召回大搜列表使用
     */
    private String keyword;

    /**
     * sug来源类型
     */
    private Integer sourceType;

    /**
     * sug来源业务id
     */
    private String sourceBizId;

    /**
     * 是否有效，1-有效，0-无效
     */
    private Integer status;

    /**
     * 扩展字段
     */
    private String extraData;
}
