package com.sankuai.dzim.pilot.acl.impl;

import com.dianping.account.UserAccountService;
import com.dianping.account.dto.UserAccountDTO;
import com.dianping.act.report.read.service.BrowseDataReadService;
import com.dianping.piccentercloud.display.api.PictureUrlGenerator;
import com.dianping.piccentercloud.display.api.PictureVisitParams;
import com.dianping.piccentercloud.display.api.enums.PictureVisitPattern;
import com.dianping.piccentercloud.display.api.enums.WaterMark;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.dianping.ugc.note.remote.dto.NoteContentDTO;
import com.dianping.ugc.note.remote.dto.NoteDTO;
import com.dianping.ugc.note.remote.dto.NotePicRelationDTO;
import com.dianping.ugc.note.remote.dto.request.QueryNoteByIdsRequestDTO;
import com.dianping.ugc.note.remote.dto.response.DataResponse;
import com.dianping.ugc.note.remote.note.NoteQueryService;
import com.dianping.userremote.base.dto.SimpleUserDTO;
import com.dianping.userremote.base.service.UserService;
import com.dianping.vc.sdk.concurrent.threadpool.ExecutorServices;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.acl.NoteAclService;
import com.sankuai.dzim.pilot.api.data.search.generative.TherapyNoteDTO;
import com.sankuai.dzim.pilot.enums.AITherapeuticJumpUrlEnum;
import com.sankuai.dzim.pilot.utils.LionConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
public class NoteAclServiceImpl implements NoteAclService {
    @Autowired
    private LionConfigUtil lionConfigUtil;

    @Autowired
    private NoteQueryService noteQueryService;

    @Autowired
    private BrowseDataReadService browseDataReadService;

    @Autowired
    private UserAccountService userAccountService;

    @Autowired
    private UserService userService;

    public static ThreadPool noteAclExecutor = Rhino.newThreadPool("NOTE_ACL_POOL",
            DefaultThreadPoolProperties.Setter().withCoreSize(5).withMaxSize(30).withMaxQueueSize(300));

    public static ThreadPool userAclExecutor = Rhino.newThreadPool("USER_ACL_POOL",
            DefaultThreadPoolProperties.Setter().withCoreSize(5).withMaxSize(30).withMaxQueueSize(300));


    String noteUrl = "dianping://feeddetail?mainid=%s&type=%s&styletype=%s&bussiid=%s&moduleid=%s&source=%s";

    @Override
    public List<TherapyNoteDTO> getTherapyNoteDatas(String keyword) {
        Map<String, List<Long>> therapyNoteIds = lionConfigUtil.getTherapyNoteIds();
        List<Long> noteIds = therapyNoteIds.get(keyword);
        if (CollectionUtils.isEmpty(noteIds)) {
            return Lists.newArrayList();
        }

        // 分partition异步获得笔记详情
        List<Future<DataResponse<List<NoteDTO>>>> noteDatasAsync = getNoteDatasAsync(noteIds);
        // 异步获取浏览量
        List<Future<Map<Long, Integer>>> noteBrowseDataAsync = getNoteBrowseDataAsync(noteIds);
        // join并转换格式
        List<NoteDTO> noteDTOs = noteDTOConvert(noteDatasAsync);
        if (CollectionUtils.isEmpty(noteDTOs)) {
            return Lists.newArrayList();
        }
        Map<Long, Integer> browseDTOs = browseDataConvert(noteBrowseDataAsync, noteIds);
        // 浏览量转换
        Map<Long, String> convertedBrowseData = convertBrowseData(browseDTOs);
        // 请求用户名 用户头像
        Map<Long, SimpleUserDTO> simpleUsers = getSimpleUsers(noteDTOs);
        // 拼接 title是contents0的
        Map<Long, String> noteTitles = getNoteTitles(noteDTOs);
        // 笔记跳链
        Map<Long, String> noteJumpUrl = getNoteJumpUrl(noteDTOs);

        return buildTherapyNoteDatas(noteDTOs, simpleUsers, noteTitles, convertedBrowseData, noteJumpUrl);
    }


    public List<TherapyNoteDTO> buildTherapyNoteDatas(List<NoteDTO> noteDTOs, Map<Long, SimpleUserDTO> simpleUsers, Map<Long, String> noteTitles, Map<Long, String> convertedBrowseData, Map<Long, String> noteJumpUrl) {
        List<TherapyNoteDTO> therapyNoteDTOs = Lists.newArrayList();
        for (NoteDTO noteDTO : noteDTOs) {
            therapyNoteDTOs.add(buildTherapyNoteData(noteDTO, simpleUsers, noteTitles, convertedBrowseData, noteJumpUrl));
        }
        return therapyNoteDTOs;
    }

    public TherapyNoteDTO buildTherapyNoteData(NoteDTO noteDTO,  Map<Long, SimpleUserDTO> simpleUsers, Map<Long, String> noteTitles, Map<Long, String> convertedBrowseData, Map<Long, String> noteJumpUrl) {
        TherapyNoteDTO therapyNoteDTO = new TherapyNoteDTO();
        // 图片 格式转换
        therapyNoteDTO.setPic(convertPicUrlFromPicKey(noteDTO));
        // 标题
        therapyNoteDTO.setNoteTitle(noteTitles.get(noteDTO.getNoteId()));
        // 用户名
        therapyNoteDTO.setUserName(simpleUsers.get(noteDTO.getUserId()) == null ? "" : simpleUsers.get(noteDTO.getUserId()).getUserNickName());
        // 用户头像
        therapyNoteDTO.setUserPic(simpleUsers.get(noteDTO.getUserId()) == null ? "" : simpleUsers.get(noteDTO.getUserId()).getUserFace());
        // 浏览量
        therapyNoteDTO.setBrowseData(convertedBrowseData.get(noteDTO.getNoteId()));
        //跳链
        therapyNoteDTO.setJumpUrl(noteJumpUrl.get(noteDTO.getNoteId()));

        therapyNoteDTO.setNoteId(noteDTO.getNoteId());
        return therapyNoteDTO;
    }


    public Map<Long, SimpleUserDTO> getSimpleUsers(List<NoteDTO> noteDTOs){
        List<Long> userIds = noteDTOs.stream().filter(Objects::nonNull).map(NoteDTO::getUserId).filter(Objects::nonNull).collect(Collectors.toList());
        List<List<Long>> partition = Lists.partition(userIds, 50);
        List<Future<List<SimpleUserDTO>>> futures = Lists.newArrayList();
        for(List<Long> userIdList : partition){
            futures.add(userAclExecutor.submit(() -> userService.findSimpleUsers(userIdList)));
//            futures.add(CompletableFuture.supplyAsync(() -> userService.findSimpleUsers(userIdList), userAclExecutor));
        }
        Map<Long, SimpleUserDTO> userDetailMap = futures.stream().map(
                future -> {
                    try {
                        return future.get();
                    } catch (Exception e) {
                        LogUtils.logFailLog(log, TagContext.builder().action("getSimpleUsers").build(),
                                new WarnMessage("NoteAclService", "获取用户失败", null), userIds, null, e);                        return null;
                    }
                }
                ).filter(Objects::nonNull)
                .flatMap(List::stream)
                .collect(Collectors.toMap(SimpleUserDTO::getUserId, Function.identity()));
        if(MapUtils.isEmpty(userDetailMap)){
            return Maps.newHashMap();
        }
        return userDetailMap;
    }

    public List<String> convertPicUrlFromPicKey(NoteDTO noteDTO) {
        List<String> picUrlList = Lists.newArrayList();
        List<String> picKeys = noteDTO.getPicRelations().stream()
                .filter(pic -> pic.getStatus() == 1)
                .map(NotePicRelationDTO::getPicKey)
                .collect(Collectors.toList());
        for(String key : picKeys){
            PictureVisitParams params = new PictureVisitParams("pic", key, 0, 0, 0, 0, WaterMark.DIANPING);
            PictureUrlGenerator pictureUrlGenerator = new PictureUrlGenerator(params, PictureVisitPattern.COMPATIBLE);
            String finalPictureUrl = pictureUrlGenerator.getFullPictureURL();
            picUrlList.add(finalPictureUrl);
        }
        return picUrlList;
    }

    public Map<Long, String> getNoteJumpUrl(List<NoteDTO> noteDTOs) {
        Map<Long, String> noteIdJumpUrlMap = Maps.newHashMap();
        for (NoteDTO noteDTO : noteDTOs) {
            if (noteDTO == null) {
                continue;
            }
            String jumpUrl = String.format(noteUrl, noteDTO.getNoteId(), AITherapeuticJumpUrlEnum.THERAPEUTIC_JUMP_URL.getType(),
                    AITherapeuticJumpUrlEnum.THERAPEUTIC_JUMP_URL.getStyleType(), AITherapeuticJumpUrlEnum.THERAPEUTIC_JUMP_URL.getBussiId(),
                    AITherapeuticJumpUrlEnum.THERAPEUTIC_JUMP_URL.getModuleid(), AITherapeuticJumpUrlEnum.THERAPEUTIC_JUMP_URL.getSource());
            noteIdJumpUrlMap.put(noteDTO.getNoteId(), jumpUrl);
        }
        return noteIdJumpUrlMap;
    }


    public Map<Long, String> getNoteTitles(List<NoteDTO> noteDTOs) {
        Map<Long, String> noteTitleMap = noteDTOs.stream()
                .filter(Objects::nonNull)
                .map(NoteDTO::getContents)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .filter(content -> content.getContentType() == 0)
                .collect(Collectors.toMap(NoteContentDTO::getNoteId, NoteContentDTO::getContent));
        if (MapUtils.isEmpty(noteTitleMap)) {
            return Maps.newHashMap();
        }
        return noteTitleMap;
    }

    public Map<Long, String> convertBrowseData(Map<Long, Integer> browseDTOs) {
        if (MapUtils.isEmpty(browseDTOs)) {
            return Maps.newHashMap();
        }
        Map<Long, String> convertedBrowseData = Maps.newHashMap();
        for (Map.Entry<Long, Integer> entry : browseDTOs.entrySet()) {
            calcConvertedBrowseData(convertedBrowseData, entry);
        }
        return convertedBrowseData;
    }

    public void calcConvertedBrowseData(Map<Long, String> convertedBrowseData, Map.Entry<Long, Integer> entry) {
        Integer cnt = entry.getValue() == null ? 0 : entry.getValue();
        if (cnt < 50) {
            convertedBrowseData.put(entry.getKey(), "");
        } else if (cnt >= 50 && cnt < 999) {
            convertedBrowseData.put(entry.getKey(), cnt + "人看过");
        } else if (cnt >= 1000 && cnt < 9999) {
            StringBuilder show = new StringBuilder();
            int bai = (cnt / 100) % 10;
            show.append(cnt / 1000);
            if (bai != 0) {
                show.append(".");
                show.append(bai);
            }
            convertedBrowseData.put(entry.getKey(), show + "千人看过");
        } else {
            StringBuilder show = new StringBuilder();
            show.append(cnt / 10000);
            int qian = (cnt / 1000) % 10;
            if (qian != 0) {
                show.append(".");
                show.append(qian);
            }
            convertedBrowseData.put(entry.getKey(), show.toString() + "万人看过");
        }
    }

    public Map<Long, UserAccountDTO> getUserAccountDTO(List<NoteDTO> noteDTOs) { // 未分partition
        List<Long> userIds = noteDTOs.stream().filter(Objects::nonNull).map(NoteDTO::getUserId).filter(Objects::nonNull).collect(Collectors.toList());
        List<UserAccountDTO> userAccountDTOS = userAccountService.loadByIds(userIds);
        if (CollectionUtils.isEmpty(userAccountDTOS)) {
            return Maps.newHashMap();
        }
        return userAccountDTOS.stream().collect(Collectors.toMap(UserAccountDTO::getUserId, Function.identity()));
    }


    public Map<Long, Integer> browseDataConvert(List<Future<Map<Long, Integer>>> noteBrowseDataAsync, List<Long> noteIds) {
        List<Map<Long, Integer>> browseDatas = noteBrowseDataAsync.stream()
                .map(future -> {
                    try {
                        return future.get();
                    } catch (Exception e) {
                        LogUtils.logFailLog(log, TagContext.builder().action("browseDataConvert").build(),
                                new WarnMessage("NoteAclService", "获取浏览数据", null), noteIds, null, e);                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        Map<Long, Integer> browseDataConverted = browseDatas.stream().flatMap(data -> data.entrySet().stream())
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, Integer::sum));
        return browseDataConverted;
    }

    public List<NoteDTO> noteDTOConvert(List<Future<DataResponse<List<NoteDTO>>>> noteDatasAsync) {
        List<DataResponse<List<NoteDTO>>> noteDatas = noteDatasAsync.stream()
                .map(future -> {
                    try {
                        return future.get();
                    } catch (InterruptedException | ExecutionException e) {
                        log.error("Error getting noteDTO result", e);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        List<NoteDTO> noteDataConverted = noteDatas.stream()
                .filter(DataResponse::isSuccess)
                .flatMap(dataResponse -> dataResponse.getResponseData().stream())
                .collect(Collectors.toList());
        return noteDataConverted;
    }

    public List<Future<Map<Long, Integer>>> getNoteBrowseDataAsync(List<Long> noteIds) {
        List<List<Long>> noteIdsPartition = Lists.partition(noteIds, 50);
        List<Future<Map<Long, Integer>>> futures = Lists.newArrayList();
        for (List<Long> partition : noteIdsPartition) {
            futures.add(noteAclExecutor.submit(() -> queryBrowseDataAsync(partition)));
        }
        return futures;
    }

    public Map<Long, Integer> queryBrowseDataAsync(List<Long> noteIds) {
        return browseDataReadService.batchGetAllBrowseByOriginalId(2, noteIds);
    }

    public List<Future<DataResponse<List<NoteDTO>>>> getNoteDatasAsync(List<Long> noteIds) {
        List<List<Long>> noteIdsPartition = Lists.partition(noteIds, 50);
        List<Future<DataResponse<List<NoteDTO>>>> futures = Lists.newArrayList();

        for (List<Long> partition : noteIdsPartition) {
            futures.add(noteAclExecutor.submit(() -> queryNoteByIdsRequestDTO(partition)));
        }
        return futures;
    }

    public DataResponse<List<NoteDTO>> queryNoteByIdsRequestDTO(List<Long> ids) {
        QueryNoteByIdsRequestDTO queryNoteByIdsRequestDTO = new QueryNoteByIdsRequestDTO();
        queryNoteByIdsRequestDTO.setNoteIds(ids);
        return noteQueryService.queryNoteByIds(queryNoteByIdsRequestDTO);
    }

}
