package com.sankuai.dzim.pilot.scene.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dzim.message.dto.MessageDTO;
import com.sankuai.dzim.pilot.api.enums.search.generative.GenerativeSearchAnswerTemplateTypeEnum;
import com.sankuai.dzim.pilot.buffer.core.PilotBufferItemDO;
import com.sankuai.dzim.pilot.buffer.enums.BufferItemTypeEnum;
import com.sankuai.dzim.pilot.buffer.enums.StreamEventCardTypeEnum;
import com.sankuai.dzim.pilot.chain.data.AIAnswerData;
import com.sankuai.dzim.pilot.domain.GenerativeSearchDomainService;
import com.sankuai.dzim.pilot.domain.data.GenerativeSearchWordE;
import com.sankuai.dzim.pilot.domain.memory.data.QueryMemoryContext;
import com.sankuai.dzim.pilot.domain.memory.impl.AssistantMemoryProvider;
import com.sankuai.dzim.pilot.domain.message.AssistantMessage;
import com.sankuai.dzim.pilot.domain.message.Message;
import com.sankuai.dzim.pilot.domain.message.UserMessage;
import com.sankuai.dzim.pilot.scene.task.data.TaskConfig;
import com.sankuai.dzim.pilot.scene.task.data.TaskContext;
import com.sankuai.dzim.pilot.scene.task.data.TaskResult;
import com.sankuai.dzim.pilot.scene.task.data.TaskTypeEnum;
import com.sankuai.dzim.pilot.utils.LionConfigUtil;
import com.sankuai.dzim.pilot.utils.UuidUtil;
import com.sankuai.dzim.pilot.utils.context.RequestContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 下挂关联问题Task
 *
 * @author: zhouyibing
 * @date: 2024/7/30
 */
@Component
@Slf4j
public class RelatedQuestionSubTask extends CommonTask implements Task {

    @Autowired
    private GenerativeSearchDomainService generativeSearchDomainService;

    @Autowired
    private AssistantMemoryProvider assistantMemoryProvider;

    @Autowired
    private LionConfigUtil lionConfigUtil;


    @Override
    public boolean accept(TaskContext context) {
        return TaskTypeEnum.RELATED_QUESTION_SUB_TASK.getType().equals(context.getTaskType());
    }

    @Override
    public TaskResult execute(TaskContext context) {
        try {
            // 提前拼装好message,否则functioncall场景下可能会当成问题进行回复,不生成关联问题
            List<Message> memory = assistantMemoryProvider.getMemory(buildMemoryContext(context.getMessageDTO()));
            // 返回关联问题
            return relatedTaskResult(context, memory);
        } catch (Exception e) {
            log.error("RelatedQuestionSubTask execute error", e);
            return TaskResult.builder().taskContext(context).build();
        }
    }

    @Override
    public void afterExecute(TaskResult result) {
        try {
            if (result == null || result.getAiAnswerData() == null) {
                return;
            }
            // 主task未输出，则关联问题也不输出
            if (isMainTaskStop(result.getTaskContext().getTaskConfig(), result.getMainTaskResult())) {
                // 随机返回三个关联问题
                randomGetRelatedQuestion(result.getTaskContext());
                return;
            }

            AIAnswerData aiAnswerData = result.getAiAnswerData();
            if (StringUtils.isBlank(aiAnswerData.getAnswer())) {
                // 随机返回三个关联问题
                randomGetRelatedQuestion(result.getTaskContext());
                return;
            }
            // 解析关联问题
            if (aiAnswerData.getAnswer().contains("问题无法回答") || aiAnswerData.getAnswer().contains("不知道") || aiAnswerData.getAnswer().contains("还在学习")) {
                // 随机返回三个关联问题
                randomGetRelatedQuestion(result.getTaskContext());
                return;
            }
            JSONObject jsonObject = JSON.parseObject(aiAnswerData.getAnswer());
            List<String> questionList = jsonObject.getJSONArray("questions").toJavaList(String.class);
            if (CollectionUtils.isEmpty(questionList)) {
                // 随机返回三个关联问题
                randomGetRelatedQuestion(result.getTaskContext());
                return;
            }
            // 最多2个问题
            questionList = questionList.subList(0, Math.min(questionList.size(), 2));
            // 写入buffer
            writeBuffer(questionList);
        } catch (Exception e) {
            log.error("RelatedQuestionSubTask afterExecute error, result: {}", result, e);
            // 随机返回三个关联问题
            randomGetRelatedQuestion(result.getTaskContext());
        }
    }

    public void randomGetRelatedQuestion(TaskContext taskContext) {
        if (taskContext.getAssistantType() <= 0) {
            return;
        }
        List<Integer> templateTypes = Lists.newArrayList(GenerativeSearchAnswerTemplateTypeEnum.TEXT_WITH_CONTENT_FILTER_OPTIONAL.getType(), GenerativeSearchAnswerTemplateTypeEnum.TEXT_WITH_CUSTOMIZATION_FILTER.getType(), 0, -1, 1001);

        List<GenerativeSearchWordE> generativeSearchWordES = generativeSearchDomainService.batchFindByAssistantAndTemplate(taskContext.getAssistantType(), templateTypes);
        if (CollectionUtils.isEmpty(generativeSearchWordES)) {
            return;
        }

        Collections.shuffle(generativeSearchWordES);
        generativeSearchWordES = generativeSearchWordES.stream().limit(2).collect(Collectors.toList());

        List<String> questions = generativeSearchWordES.stream().map(GenerativeSearchWordE::getQuestion).collect(Collectors.toList());
        writeBuffer(questions);
    }

    private boolean isMainTaskStop(TaskConfig taskConfig, TaskResult mainTaskResult) {
        // 未配置主任务终止词
        List<String> mainTaskStopWords = taskConfig.getMainTaskStopWords();
        if (CollectionUtils.isEmpty(mainTaskStopWords)) {
            return false;
        }
        if (mainTaskResult == null || mainTaskResult.getAiAnswerData() == null || StringUtils.isBlank(mainTaskResult.getAiAnswerData().getAnswer())) {
            return true;
        }
        String answer = mainTaskResult.getAiAnswerData().getAnswer();
        for (String mainTaskStopWord : mainTaskStopWords) {
            if (answer.contains(mainTaskStopWord)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public TaskResult processException(TaskContext context) {
        AIAnswerData aiAnswerData = new AIAnswerData();
        aiAnswerData.setAnswer("问题无法回答");
        return TaskResult.builder().taskContext(context).aiAnswerData(aiAnswerData).build();
    }

    public TaskResult relatedTaskResult(TaskContext context, List<Message> memory) {
        StringBuilder history = new StringBuilder();
        for (Message message : memory) {
            if (message instanceof UserMessage) {
                history.append("用户: ").append(message.getContent()).append("\n");
            }
            if (message instanceof AssistantMessage) {
                history.append("助手: ").append(message.getContent()).append("\n");
            }
        }

        TaskContext newTaskContext = buildNewTaskContext(context, history.toString());
        // 调用chain层获取AIAnswerData
        AIAnswerData aiAnswerData = getAIAnswerData(newTaskContext);
        // 构造返回值
        return TaskResult.builder().taskContext(context).aiAnswerData(aiAnswerData).build();
    }

    public TaskContext buildNewTaskContext(TaskContext context, String history) {
        TaskContext taskContext = new TaskContext();
        taskContext.setTaskType(context.getTaskType());
        taskContext.setTaskConfig(context.getTaskConfig());
        taskContext.setAssistantType(context.getAssistantType());
        taskContext.setUserId(context.getUserId());
        taskContext.setMessageDTO(buildNewMessageDTO(context.getMessageDTO(), history));
        return taskContext;
    }

    public MessageDTO buildNewMessageDTO(MessageDTO oldMessageDTO, String history) {
        MessageDTO messageDTO = new MessageDTO();
        messageDTO.setMessageId(oldMessageDTO.getMessageId());
        messageDTO.setChatGroupId(oldMessageDTO.getChatGroupId());
        messageDTO.setMessageType(oldMessageDTO.getMessageType());

        String userMessageTemplate = "聊天记录: \n%s\n\n用户当前问题: %s\n\n请基于用户当前问题猜测用户还可能继续问的问题,可以使用function-call获取更多的有效信息";
        messageDTO.setMessage(String.format(userMessageTemplate, history, oldMessageDTO.getMessage()));
        messageDTO.setFromUid(oldMessageDTO.getFromUid());
        messageDTO.setToUid(oldMessageDTO.getToUid());
        messageDTO.setCreatorId(oldMessageDTO.getCreatorId());
        messageDTO.setCreateTime(oldMessageDTO.getCreateTime());
        messageDTO.setClientType(oldMessageDTO.getClientType());
        return messageDTO;
    }

    public QueryMemoryContext buildMemoryContext(MessageDTO messageDTO) {
        QueryMemoryContext context = new QueryMemoryContext();
        context.setLimit(6);
        context.setChatGroupId(messageDTO.getChatGroupId());
        return context;
    }

    public void writeBuffer(List<String> questionList) {

        if (!lionConfigUtil.isSubQuestionMergeSwitch()) {
            for (String question : questionList) {
                String cardKey = UuidUtil.getRandomCardKey();
                String data = StreamEventCardTypeEnum.buildCardContent(StreamEventCardTypeEnum.RELATED_QUESTION_CARD, cardKey);
                Map<String, Object> extra = Maps.newHashMap();
                extra.put("question", question);
                PilotBufferItemDO pilotBufferItemDO = new PilotBufferItemDO();
                pilotBufferItemDO.setType(BufferItemTypeEnum.RECOMMEND_QUESTION.getType());
                pilotBufferItemDO.setData(data);
                pilotBufferItemDO.setExtra(extra);
                RequestContext.writeBuffer(Lists.newArrayList(pilotBufferItemDO));
            }
        }
        if (lionConfigUtil.isSubQuestionMergeSwitch()) {
            String cardKey = UuidUtil.getRandomCardKey();
            String data = StreamEventCardTypeEnum.buildCardContent(StreamEventCardTypeEnum.RELATED_QUESTIONS_CARD, cardKey);
            Map<String, Object> extra = Maps.newHashMap();
            extra.put("questions", questionList);
            PilotBufferItemDO pilotBufferItemDO = new PilotBufferItemDO();
            pilotBufferItemDO.setType(BufferItemTypeEnum.RECOMMEND_QUESTION.getType());
            pilotBufferItemDO.setData(data);
            pilotBufferItemDO.setExtra(extra);
            RequestContext.writeBuffer(Lists.newArrayList(pilotBufferItemDO));
        }
    }
}
