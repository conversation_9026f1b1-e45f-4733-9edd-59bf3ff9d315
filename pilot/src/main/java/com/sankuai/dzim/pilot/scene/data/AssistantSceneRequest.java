package com.sankuai.dzim.pilot.scene.data;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: zhouyibing
 * @date: 2024/7/30
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AssistantSceneRequest {

    private Integer assistantType;

    private String userId;

    private String question;

    private Long messageId;

    private Long replyMessageId;

    private Long chatGroupId;

    /**
     * 消息输入来源
     * @see com.sankuai.dzim.pilot.api.enums.assistant.MessageInputSourceEnum
     */
    private int inputSourceType;

    private String envContext;
    private String bizParams;

    private List<String> imgUrls;

    private String userIp;

    /**
     * 某些场景需要从chatGroup接口透传的参数
     */
    private String chatGroupExp;

    private String channel;
}
