package com.sankuai.dzim.pilot.domain;

import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import com.sankuai.dzim.pilot.domain.data.Embeddable;
import com.sankuai.dzim.pilot.domain.data.EmbeddingConfig;
import com.sankuai.dzim.pilot.domain.data.EsVectorInsertResult;
import com.sankuai.dzim.pilot.domain.impl.EsVectorDomainServiceImpl;

import java.util.List;

public interface EsVectorDomainService {

    <T extends Embeddable> EsVectorInsertResult bulkInsert(List<T> dataList, EmbeddingConfig embeddingConfig, VectorDuplicateChecker<T> duplicateChecker);

    <T> SearchResponse<T> search(SearchRequest searchRequest, Class<T> clazz);

}
