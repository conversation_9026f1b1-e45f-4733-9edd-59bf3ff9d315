package com.sankuai.dzim.pilot.gateway.api.assistant;

import com.dianping.gateway.enums.APIModeEnum;
import com.dianping.vc.web.api.API;
import com.dianping.vc.web.api.URL;
import com.dianping.vc.web.api.VCAssert;
import com.dianping.vc.web.biz.client.api.AppContext;
import com.dianping.vc.web.biz.client.api.AppContextAware;
import com.dianping.vc.web.biz.client.api.MTUserIdAware;
import com.dianping.vc.web.biz.client.api.UserIdAware;
import com.sankuai.dzim.pilot.api.data.assistant.PilotQuestionRespDTO;
import com.sankuai.dzim.pilot.gateway.api.vo.PilotQuestionVO;
import com.sankuai.dzim.pilot.gateway.api.vo.QuestionUVVO;
import com.sankuai.dzim.pilot.process.QuestionScoreProcessService;
import com.sankuai.dzim.pilot.process.data.PilotHotQuestionReq;
import com.sankuai.dzim.pilot.utils.AccountUtils;
import com.sankuai.dzim.pilot.utils.BizTypeMappingUtil;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/08/21 11:18
 * 热门问题列表查询
 * 移动之家：https://mobile.sankuai.com/studio/api/22003/index?tab=management
 */
@URL(url = "/dzim/pilot/assistant/question/list", mode = APIModeEnum.CLIENT, methods = "GET")
@Slf4j
@Service
public class QuestionListQueryAPI implements API, UserIdAware, MTUserIdAware, AppContextAware {

    @Setter
    private String tabWord;

    @Setter
    private String query;

    @Setter
    private long mTUserId;

    @Setter
    private long userId;

    /**
     * 门店ID，团详页、商详页需要传入
     */
    @Setter
    private long shopId;

    @Setter
    private long dealId;

    /**
     * 频道页标识，纹绣频道页-nib.general.eyebrow_tattoos
     */
    @Setter
    private String bizCode;

    /**
     * 页面来源，1-频道页 2-商户详情页 3-团购详情页
     */
    @Setter
    private int sourceType;

    /**
     * 平台 1-点评 2-美团
     */
    @Setter
    private int platform;

    @Setter
    private AppContext appContext;

    @Autowired
    private QuestionScoreProcessService questionScoreProcessService;

    @Autowired
    private BizTypeMappingUtil bizTypeMappingUtil;

    @Override
    public Object execute() {
        // 参数校验
        checkParam();
        // 门店类目信息查询
        int bizType = bizTypeMappingUtil.getBizType(shopId, platform, sourceType, bizCode);
        VCAssert.isTrue(bizType != 0, API.INVALID_PARAM_CODE, "shopId、platform、sourceType或bizCode不合法");
        // 问题列表查询
        PilotQuestionRespDTO questionRespDTO = questionScoreProcessService.queryQuestionList(buildHotQuestionReq(bizType));
        QuestionUVVO questionUVVO = new QuestionUVVO();
        BeanUtils.copyProperties(questionRespDTO, questionUVVO);
        questionUVVO.setQuestions(questionRespDTO.getQuestions().stream().filter(Objects::nonNull).map(questionDTO -> {
            PilotQuestionVO questionVO = new PilotQuestionVO();
            questionVO.setQuestionId(questionDTO.getQuestionId());
            questionVO.setQuestion(questionDTO.getQuestion());
            questionVO.setJumpUrl(questionDTO.getJumpUrl());
            questionVO.setQuestionType(questionDTO.getQuestionType());
            return questionVO;
        }).collect(Collectors.toList()));
        return questionUVVO;
    }

    private PilotHotQuestionReq buildHotQuestionReq(Integer bizType) {
        PilotHotQuestionReq pilotHotQuestionReq = new PilotHotQuestionReq();
        pilotHotQuestionReq.setSourceType(sourceType);
        pilotHotQuestionReq.setBizType(bizType);
        pilotHotQuestionReq.setQuery(query);
        pilotHotQuestionReq.setTabWord(tabWord);
        pilotHotQuestionReq.setUserId(AccountUtils.getUserId(userId, mTUserId, platform));
        pilotHotQuestionReq.setPlatform(platform);
        pilotHotQuestionReq.setShopId(shopId);
        pilotHotQuestionReq.setDealId(dealId);
        pilotHotQuestionReq.setDeviceId(appContext.getDpid());
        pilotHotQuestionReq.setUnionId(appContext.getUnionid());
        return pilotHotQuestionReq;
    }

    private void checkParam() {
        VCAssert.isTrue(userId > 0 || mTUserId > 0, API.UNLOGIN_CODE, "未登陆");
        VCAssert.isTrue(platform > 0 && platform < 3, API.INVALID_PARAM_CODE, "platform不合法");
        VCAssert.isTrue(sourceType > 0 && sourceType < 4, API.INVALID_PARAM_CODE, "sourceType不合法");

    }
}
