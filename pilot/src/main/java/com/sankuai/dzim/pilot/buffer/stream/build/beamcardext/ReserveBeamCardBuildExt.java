package com.sankuai.dzim.pilot.buffer.stream.build.beamcardext;

import com.dianping.frog.sdk.util.CollectionUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dzim.pilot.buffer.core.PilotBufferItemDO;
import com.sankuai.dzim.pilot.buffer.enums.StreamEventCardTypeEnum;
import com.sankuai.dzim.pilot.buffer.stream.build.beamcardext.data.BeamCardBuildContext;
import com.sankuai.dzim.pilot.buffer.stream.vo.BeamChoiceVO;
import com.sankuai.dzim.pilot.enums.BeamResourceTypeEnum;
import com.sankuai.it.iam.common.base.gson.bridge.JSON;
import com.sankuai.it.iam.common.base.gson.bridge.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class ReserveBeamCardBuildExt implements BeamCardBuildExt {
    @Override
    public boolean acceptByStreamCardType(String streamCardType) {
        return StreamEventCardTypeEnum.BEAM_RESERVE_CARD.getType().equals(streamCardType);
    }

    @Override
    public boolean acceptByBeamResourceType(String resourceType) {
        return BeamResourceTypeEnum.FWLS_PRE_PURCHASE_RESERVE.getKey().equals(resourceType);
    }

    @Override
    public List<BeamChoiceVO.DeltaResource> buildResources(BeamCardBuildContext context) {
        List<BeamChoiceVO.DeltaResource> deltaResources = Lists.newArrayList();

        List<PilotBufferItemDO> tagItems = context.getTagItems();
        if (CollectionUtils.isEmpty(tagItems)) {
            return Collections.emptyList();
        }

        Map<String, Object> extra = Maps.newHashMap();
        for (PilotBufferItemDO itemDO : tagItems) {
            extra.putAll(MapUtils.isEmpty(itemDO.getExtra()) ? Maps.newHashMap() : itemDO.getExtra());
        }
        if (MapUtils.isEmpty(extra)) {
            return Collections.emptyList();
        }
        for (Map.Entry<String, Object> entry : extra.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            BeamChoiceVO.DeltaResource deltaResource = buildDeltaResource(key, value);
            deltaResources.add(deltaResource);
        }
        return deltaResources;
    }

    @Override
    public String cardDecode(String cardContent) {
        JSONObject jsonObject = JSON.parseObject(cardContent);
        String PRODUCT_TEMPLATE = "\n该商品Id(productId)为%s，商品类型（productType）为%s。";
        return String.format(PRODUCT_TEMPLATE, jsonObject.getInteger("index"), jsonObject.getInteger("productType") == 1 ? "团购" : "预订", jsonObject.getString("productName"));
    }

    private BeamChoiceVO.DeltaResource buildDeltaResource(String key, Object value) {
        BeamChoiceVO.DeltaResource deltaResource = new BeamChoiceVO.DeltaResource();
        deltaResource.setResource_index(key);
        deltaResource.setResource_type("FWLS_PRE_PURCHASE_RESERVE");
        BeamChoiceVO.ResourceMetaData metaData = new BeamChoiceVO.ResourceMetaData();
        metaData.setBiz_data(JSON.toJSONString(value));
        deltaResource.setMetadata(metaData);
        return deltaResource;
    }
}
