package com.sankuai.dzim.pilot.process.aireservebook.impl;

import com.dianping.cat.util.StringUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.nibtp.trade.client.combine.bean.WebParamDTO;
import com.meituan.nibtp.trade.client.combine.enums.AgentOrderType;
import com.meituan.nibtp.trade.client.combine.enums.AgentPlatformOrderStatus;
import com.meituan.nibtp.trade.client.combine.requset.AgentQueryGeneralBookingOrderItemReqDTO;
import com.meituan.nibtp.trade.client.combine.response.AgentGeneralBookingOrderDTO;
import com.sankuai.dzim.pilot.acl.*;
import com.sankuai.dzim.pilot.acl.data.ProductTimeStateQueryItem;
import com.sankuai.dzim.pilot.acl.data.ShopTimeStateQueryItem;
import com.sankuai.dzim.pilot.api.enums.assistant.PlatformEnum;
import com.sankuai.dzim.pilot.gateway.api.assistant.data.SendBeamMessageReq;
import com.sankuai.dzim.pilot.process.aibook.AIBookCacheProcessService;
import com.sankuai.dzim.pilot.process.aireservebook.AIReserveBookQueryService;
import com.sankuai.dzim.pilot.process.aireservebook.data.*;
import com.sankuai.dzim.pilot.process.aireservebook.enums.POIIndustryType;
import com.sankuai.dzim.pilot.process.aireservebook.enums.ReserveItemType;
import com.sankuai.dzim.pilot.process.aireservebook.enums.ShopReserveWayType;
import com.sankuai.dzim.pilot.process.aireservebook.enums.TaskReserveType;
import com.sankuai.dzim.pilot.process.localplugin.param.Param;
import com.sankuai.dzim.pilot.scene.task.data.EnvContext;
import com.sankuai.dzim.pilot.utils.PluginContextUtil;
import com.sankuai.dzim.pilot.utils.ReserveInfoProcessUtils;
import com.sankuai.dzim.pilot.utils.TimeUtil;
import com.sankuai.dzim.pilot.utils.data.Response;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.spt.statequery.api.dto.BaseStateDTO;
import com.sankuai.spt.statequery.api.dto.ShopReserveModeDTO;
import com.sankuai.spt.statequery.api.dto.TimeSliceDTO;
import com.sankuai.spt.statequery.api.enums.BaseStateQueryConditionKeyEnum;
import com.sankuai.spt.statequery.api.enums.ReserveModeEnum;
import com.sankuai.spt.statequery.api.enums.TimeSliceExtendKeyEnum;
import com.sankuai.spt.statequery.api.enums.TimeSliceRecallStrategyEnum;
import com.sankuai.wpt.user.retrieve.thrift.message.UserModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: wuwenqiang
 * @create: 2025-04-21
 * @description:
 */
@Slf4j
@Component
public class AIReserveBookQueryServiceImpl implements AIReserveBookQueryService {

    @Autowired
    private HaimaAclService haimaAclService;

    @Autowired
    private AIBookCacheProcessService aiBookCacheProcessService;

    @Autowired
    private ShopAclService shopAclService;

    @Autowired
    private ProductAclService productAclService;

    @Autowired
    private AgentTradeAclService agentTradeAclService;



    @Override
    public DpPoiDTO queryDpPoiDTO(Long shopId, int platform) {
        long dpShopId = shopId;
        if (platform == PlatformEnum.MT.getType()) {
            dpShopId = shopAclService.loadDpShopIdByMtShopId(shopId);
        }
        if (dpShopId <= 0) {
            return null;
        }
        return shopAclService.getShopInfo(dpShopId);
    }

    @Override
    public ShopReserveWayType queryShopReserveWayType(Long mtShopId) {
        if (mtShopId == null || mtShopId <= 0) {
            return null;
        }
        Map<Long, ShopReserveWayType> shopReserveWayTypeMap = Maps.newHashMap();
        try {
            shopReserveWayTypeMap = queryShopReserveWayTypes(Lists.newArrayList(mtShopId));
        }catch (Exception e) {
            log.error("queryShopReserveWayType error, mtShopId:{}", mtShopId, e);
            return null;
        }
        if (MapUtils.isEmpty(shopReserveWayTypeMap)) {
            return null;
        }
        return shopReserveWayTypeMap.get(mtShopId);
    }

    @Override
    public Map<Long, ShopReserveWayType> queryShopReserveWayTypes(List<Long> mtShopIds) {
        if (CollectionUtils.isEmpty(mtShopIds)) {
            return Maps.newHashMap();
        }
        Map<Long, ShopReserveModeDTO> reserveModeMap = productAclService.batchQueryShopReserveMode(mtShopIds);
        if (MapUtils.isEmpty(reserveModeMap)) {
            return Maps.newHashMap();
        }

        return reserveModeMap.entrySet().stream()
                .filter(entry -> entry != null && entry.getValue() != null)
                .collect(Collectors.toMap(Map.Entry::getKey,
                        item -> convertShopReserveWayType(item.getValue()),
                        (a, b) -> a));
    }

    private ShopReserveWayType convertShopReserveWayType(ShopReserveModeDTO reserveModeDTO) {
        if (reserveModeDTO == null || reserveModeDTO.getReserveMode() == null) {
            return null;
        }
        return ReserveModeEnum.ONLINE_RESERVE.equals(reserveModeDTO.getReserveMode()) ? ShopReserveWayType.ONLINE : ShopReserveWayType.PHONE;
    }

    @Override
    public List<TimeSliceStockInfo> queryAvailableTimeStock(ShopReserveContext shopReserveContext, ReserveInfo collectedReserveInfo, boolean isAfterOrder, DealGroupDTO dealGroupDTO) {
        POIIndustryType poiIndustryType = shopReserveContext.getPoiIndustryType();
        if (poiIndustryType == null) {
            return null;
        }
        // 1. 足疗（所有场景）& 美发（先约后买）
        // 查询门店库存
        if (POIIndustryType.FOOT_MASSAGE.equals(poiIndustryType) || (POIIndustryType.HAIR.equals(poiIndustryType) && !isAfterOrder)) {
            ShopStockQueryParam param = buildShopStockQueryParam(shopReserveContext, collectedReserveInfo, poiIndustryType);
            return queryShopAvailableTimeStock(param);
        }  else if (POIIndustryType.HAIR.equals(poiIndustryType) && isAfterOrder) {
            // 2. 美发（先买后约）查询商品库存
            ProductStockQueryParam param = buildProductStockQueryParam(shopReserveContext, collectedReserveInfo, dealGroupDTO);
            return queryProductAvailableTimeStock(param);
        }
        return null;
    }

    @Override
    public List<TimeSliceStockInfo> beamQueryAvailableTimeStock(ShopReserveContext shopReserveContext, ReserveInfo collectedReserveInfo, boolean isAfterOrder, DealGroupDTO dealGroupDTO) {
        POIIndustryType poiIndustryType = shopReserveContext.getPoiIndustryType();
        if (poiIndustryType == null) {
            return null;
        }
        // 1. 美发（先买后约）、酒吧查询商品库存
        if ((POIIndustryType.HAIR.equals(poiIndustryType) && isAfterOrder)
                || POIIndustryType.BAR.equals(poiIndustryType)) {
            ProductStockQueryParam param = buildProductStockQueryParam(shopReserveContext, collectedReserveInfo, dealGroupDTO);
            return queryProductAvailableTimeStock(param);
        }

        //其他的查询店铺库存
        ShopStockQueryParam param = buildShopStockQueryParam(shopReserveContext, collectedReserveInfo, poiIndustryType);
        return queryShopAvailableTimeStock(param);
    }

    private ShopStockQueryParam buildShopStockQueryParam(ShopReserveContext shopReserveContext, ReserveInfo collectedReserveInfo, POIIndustryType poiIndustryType) {
        ShopStockQueryParam param = new ShopStockQueryParam();
        param.setMtShopId(shopReserveContext.getMtShopId());
        if (collectedReserveInfo == null) {
            return param;
        }
        param.setStartTime(ReserveInfoProcessUtils.getStringItem(collectedReserveInfo, ReserveItemType.START_TIME.getKey()));
        param.setDuration(ReserveInfoProcessUtils.getLongItem(collectedReserveInfo, ReserveItemType.DURATION.getKey()));
        // 美发行业需要传入project code
        if (POIIndustryType.HAIR.equals(poiIndustryType)) {
            param.setProjectCode(ReserveInfoProcessUtils.getStringItem(collectedReserveInfo, ReserveItemType.PROJECT_CODE.getKey()));
        }
        return param;
    }

    private ProductStockQueryParam buildProductStockQueryParam(ShopReserveContext shopReserveContext, ReserveInfo collectedReserveInfo, DealGroupDTO dealGroupDTO) {
        ProductStockQueryParam param = new ProductStockQueryParam();
        param.setMtShopId(shopReserveContext.getMtShopId());
        if (collectedReserveInfo != null) {
            param.setStartTime(ReserveInfoProcessUtils.getStringItem(collectedReserveInfo, ReserveItemType.START_TIME.getKey()));
        }
        //酒吧用提取到的ProductId
        if (shopReserveContext.getPoiIndustryType() == POIIndustryType.BAR) {
            param.setMtProductId(getBarProductId(collectedReserveInfo));
        }
        else if (dealGroupDTO != null) {
            param.setMtProductId(dealGroupDTO.getMtDealGroupId());
        }
        return param;
    }

    private Long getBarProductId(ReserveInfo collectedReserveInfo) {
        try {
            String productId = ReserveInfoProcessUtils.getStringItem(collectedReserveInfo, ReserveItemType.PROJECT_CODE.getKey());
            if (StringUtils.isNotEmpty(productId)) {
                return Long.parseLong(productId);
            }
        } catch (Exception e) {
         log.error("getBarProductId error, reserveInfo:{}", collectedReserveInfo, e);
        }
        return null;
    }

    @Override
    public List<TimeSliceStockInfo> queryShopAvailableTimeStock(ShopStockQueryParam param) {
        if (param == null) {
            return null;
        }
        ShopTimeStateQueryItem queryItem = buildShopTimeStateQueryItem(param);
        Map<BaseStateQueryConditionKeyEnum, String> condition = Maps.newHashMap();
        if (param.getDuration() != null && param.getDuration() > 0) {
            condition.put(BaseStateQueryConditionKeyEnum.SERVICE_DURATION, String.valueOf(param.getDuration()));
        }
        if (StringUtils.isNotEmpty(param.getProjectCode())) {
            condition.put(BaseStateQueryConditionKeyEnum.SERVICE_PROJECT, param.getProjectCode());
        }
        Map<ShopTimeStateQueryItem, BaseStateDTO> shopTimeStateMap = productAclService.batchQueryShopCanBookingState(Lists.newArrayList(queryItem), condition);
        if (MapUtils.isEmpty(shopTimeStateMap)) {
            return null;
        }
        return convertBaseState2TimeSliceStockInfo(shopTimeStateMap.get(queryItem));
    }

    private ShopTimeStateQueryItem buildShopTimeStateQueryItem(ShopStockQueryParam param) {
        ShopTimeStateQueryItem queryItem = new ShopTimeStateQueryItem();
        queryItem.setMtShopId(param.getMtShopId());
        String day = convertDay(param.getStartTime());
        queryItem.setDay(day);
        return queryItem;
    }

    private TimeSliceStockInfo convertTimeSliceStockInfo(TimeSliceDTO timeSliceDTO, String day) {
        if (StringUtils.isEmpty(day) || timeSliceDTO == null || !timeSliceDTO.isAvailable() ||
                timeSliceDTO.getResidue() == null || timeSliceDTO.getResidue() <= 0) {
            return null;
        }
        TimeSliceStockInfo timeSliceStockInfo = new TimeSliceStockInfo();
        timeSliceStockInfo.setStartTime(contactDayAndTime(day, timeSliceDTO.getStartTime()));
        String endDay = computeEndDay(day, timeSliceDTO.getEndTimeNextDay());
        timeSliceStockInfo.setEndTime(contactDayAndTime(endDay, timeSliceDTO.getEndTime()));
        timeSliceStockInfo.setStock(timeSliceDTO.getResidue());
        timeSliceStockInfo.setSkuId(buildSkuId(timeSliceDTO));
        timeSliceStockInfo.setTimeSliceType(timeSliceDTO.getType());
        timeSliceStockInfo.setPurchaseStockDate(computePurchaseStockDate(timeSliceDTO, day));
        return timeSliceStockInfo;
    }

    /**
     * 酒吧行业需要加SkuId
     * @param timeSliceDTO
     * @return
     */
    private String buildSkuId(TimeSliceDTO timeSliceDTO) {
        try {
            Map<TimeSliceExtendKeyEnum, List<String>> extInfo = timeSliceDTO.getExtInfo();
            if (MapUtils.isEmpty(extInfo) || !extInfo.containsKey(TimeSliceExtendKeyEnum.SKU_ID)) {
                return "";
            }

            List<String> skuIds = extInfo.get(TimeSliceExtendKeyEnum.SKU_ID);
            if (CollectionUtils.isEmpty(skuIds)) {
                return "";
            }
            return skuIds.get(0);
        } catch (Exception e) {
            log.error("buildSkuId error timeSliceDTO:{}", e, timeSliceDTO);
            return "";
        }
    }

    /**
     * 处理跨天库存 purchaseStockDate
     * 时间片召回策略，如果是 YESTERDAY，说明是昨天跨天的场次，如果是TODAY，说明是今日的场次。只需处理YESTERDAY
     * @param timeSliceDTO timeSliceDTO
     * @param day day
     * @return purchaseStockDate
     */
    private String computePurchaseStockDate(TimeSliceDTO timeSliceDTO, String day) {
        Set<TimeSliceRecallStrategyEnum> recallStrategies = timeSliceDTO.getRecallStrategies();
        // 非YESTERDAY，直接用选的日期
        if (CollectionUtils.isEmpty(recallStrategies) || !recallStrategies.contains(TimeSliceRecallStrategyEnum.YESTERDAY)) {
            return day;
        }
        // 跨天库存，用选的日期-1天
        LocalDateTime minus1day = TimeUtil.convertStr2LocalDateTime(day, TimeUtil.FORMAT_DAY).minusDays(1);
        return TimeUtil.convertLocalDateTime2Str(minus1day, TimeUtil.FORMAT_DAY);
    }

    private String computeEndDay(String day, Boolean endTimeNextDay) {
        // 非跨天直接返回
        if (endTimeNextDay == null || !endTimeNextDay || StringUtils.isEmpty(day)) {
            return day;
        }
        // 跨天需要加1天,day的格式为 yyyy-MM-dd
        LocalDateTime endDay = TimeUtil.convertStr2LocalDateTime(day, TimeUtil.FORMAT_DAY).plusDays(1);
        return TimeUtil.convertLocalDateTime2Str(endDay, TimeUtil.FORMAT_DAY);
    }

    private String contactDayAndTime(String day, String time) {
        if (StringUtils.isEmpty(day) || StringUtils.isEmpty(time)) {
            return null;
        }
        return day + " " + time;
    }

    @Override
    public List<TimeSliceStockInfo> queryProductAvailableTimeStock(ProductStockQueryParam param) {
        if (param == null) {
            return null;
        }
        ProductTimeStateQueryItem queryItem = buildProductTimeStateQueryItem(param);
        Map<ProductTimeStateQueryItem, BaseStateDTO> productTimeStateMap = productAclService.batchQueryProductCanBookingState(Lists.newArrayList(queryItem));
        if (MapUtils.isEmpty(productTimeStateMap)) {
            return null;
        }
        return convertBaseState2TimeSliceStockInfo(productTimeStateMap.get(queryItem));
    }

    private ProductTimeStateQueryItem buildProductTimeStateQueryItem(ProductStockQueryParam param) {
        ProductTimeStateQueryItem queryItem = new ProductTimeStateQueryItem();
        queryItem.setMtShopId(param.getMtShopId());
        queryItem.setMtProductId(param.getMtProductId());
        String day = convertDay(param.getStartTime());
        queryItem.setDay(day);
        return queryItem;
    }

    private String convertDay(String startTimeStr) {
        LocalDateTime startTime = TimeUtil.convertStr2LocalDateTime(startTimeStr);
        if (startTime == null) {
            LocalDateTime now = LocalDateTime.now();
            return TimeUtil.convertLocalDateTime2Str(now, TimeUtil.FORMAT_DAY);
        }
        return TimeUtil.convertLocalDateTime2Str(startTime, TimeUtil.FORMAT_DAY);
    }

    private List<TimeSliceStockInfo> convertBaseState2TimeSliceStockInfo(BaseStateDTO baseStateDTO) {
        if (baseStateDTO == null) {
            return null;
        }
        List<TimeSliceDTO> timeSlices = baseStateDTO.getTimeSlices();
        if (CollectionUtils.isEmpty(timeSlices)) {
            return null;
        }
        String day = baseStateDTO.getDay();
        return timeSlices.stream().filter(Objects::nonNull)
                .map(item -> convertTimeSliceStockInfo(item, day)).filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    public AgentGeneralBookingOrderDTO queryReserveOrder(EnvContext envContext, UserModel userModel, Long orderId, Integer taskReserveType) {
        if (userModel == null || orderId == null || taskReserveType == null) {
            return null;
        }
        // 发起预约/预订
        WebParamDTO webParam = agentTradeAclService.buildWebParam(userModel, envContext);
        return queryReserveOrder(webParam, orderId, taskReserveType);
    }

    private AgentGeneralBookingOrderDTO queryReserveOrder(WebParamDTO webParam, Long orderId, Integer taskReserveType) {
        AgentQueryGeneralBookingOrderItemReqDTO req = new AgentQueryGeneralBookingOrderItemReqDTO();
        req.setOrderId(orderId);
        AgentOrderType orderType = TaskReserveType.convertTaskReserveType2OrderType(taskReserveType);
        if (orderType == null) {
            return null;
        }
        req.setOrderType(orderType.getCode());
        Response<List<AgentGeneralBookingOrderDTO>> resp = agentTradeAclService.queryGeneralBookingOrder(webParam, Lists.newArrayList(req));
        if (resp == null || !resp.isSuccess() || CollectionUtils.isEmpty(resp.getData())) {
            return null;
        }
        List<AgentGeneralBookingOrderDTO> orderList = resp.getData();
        return orderList.get(0);
    }

    @Override
    public UserReserveInfo queryUserReserveInfo(String userId, Long shopId, int platform) {
        if (aiBookCacheProcessService.containsAgentReserveAndBookInfo(userId, shopId, platform)) {
            return aiBookCacheProcessService.getAgentReserveAndBookInfo(userId, shopId, platform);
        }
        return null;
    }

    @Override
    public AgentGeneralBookingOrderDTO queryBeamReserveOrder(SendBeamMessageReq sendBeamMessageReq, Long orderId, Integer taskReserveType) {
        if (sendBeamMessageReq == null || orderId == null || taskReserveType == null) {
            return null;
        }
        // 发起预约/预订
        WebParamDTO webParam = agentTradeAclService.buildWebParam(sendBeamMessageReq);
        return queryReserveOrder(webParam, orderId, taskReserveType);
    }

    @Override
    public UserReserveInfo queryBeamUserReserveInfoWithOrderCheck(Param param, String userId, Long shopId, int platform) {
        if (!aiBookCacheProcessService.containsBeamAgentReserveAndBookInfo(userId, shopId, platform)) {
            return null;
        }
        UserReserveInfo userReserveInfo = aiBookCacheProcessService.getBeamAgentReserveAndBookInfo(userId, shopId, platform);
        // 背景：beam的预约单没有监听订单取消/完成等消息，无法删除缓存，需要进行订单id有效性检查
        // 如果订单id存在，且订单结束（取消/完成/退款），删除缓存
        if (userReserveInfo != null && userReserveInfo.getReserveId() != null && userReserveInfo.getReserveId() > 0) {
            if (bookingOrderDone(param, userReserveInfo)) {
                aiBookCacheProcessService.deleteBeamAgentReserveAndBookInfo(userId, shopId, platform);
                return null;
            }
        }

        return userReserveInfo;
    }

    private boolean bookingOrderDone(Param param, UserReserveInfo userReserveInfo) {
        SendBeamMessageReq sendBeamMessageReq = PluginContextUtil.getBeamRequestContext(param);
        AgentGeneralBookingOrderDTO bookingOrderDTO = queryBeamReserveOrder(sendBeamMessageReq, userReserveInfo.getReserveId(), userReserveInfo.getTaskReserveType());
        if (bookingOrderDTO == null) {
            //下游为空，大概率有异常，先返回false
            return false;
        }

        Integer platformOrderStatus = bookingOrderDTO.getPlatformOrderStatus();
        // 已预约
        if (platformOrderStatus.intValue() == AgentPlatformOrderStatus.WAIT_CONSUME.getCode()) {
            return false;
        }
        // 预约中
        if (platformOrderStatus.intValue() == AgentPlatformOrderStatus.WAIT_PAY.getCode() || platformOrderStatus.intValue() == AgentPlatformOrderStatus.DELIVERING.getCode()) {
            return false;
        }
        // 其他状态认为是预约单已结束
        return true;
    }

}
