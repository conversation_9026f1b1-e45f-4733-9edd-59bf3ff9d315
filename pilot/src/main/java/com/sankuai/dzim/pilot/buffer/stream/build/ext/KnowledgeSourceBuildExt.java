package com.sankuai.dzim.pilot.buffer.stream.build.ext;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dzim.pilot.buffer.enums.EventCardShowTypeEnum;
import com.sankuai.dzim.pilot.buffer.enums.StreamEventCardTypeEnum;
import com.sankuai.dzim.pilot.buffer.stream.vo.StreamEventCardDataVO;
import com.sankuai.dzim.pilot.domain.retrieval.data.SourceData;
import com.sankuai.dzim.pilot.scene.data.AssistantSceneContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

import static com.sankuai.dzim.pilot.buffer.enums.EventCardShowTypeEnum.getByKnowledgeSourceType;

@Slf4j
@Component
public class KnowledgeSourceBuildExt implements CardBuildExt{
    @Override
    public boolean accept(StreamEventCardDataVO streamEventCardDataVO) {
        if (streamEventCardDataVO.getType() != null && StreamEventCardTypeEnum.getByType(streamEventCardDataVO.getType()) == StreamEventCardTypeEnum.KNOWLEDGE_CARD) {
            return true;
        }
        return false;
    }

    @Override
    public void padding(StreamEventCardDataVO streamEventCardDataVO, AssistantSceneContext context) {

        String key = streamEventCardDataVO.getKey();
        EventCardShowTypeEnum showTypeEnum = getByKnowledgeSourceType(key);
        if(showTypeEnum == null){
            return;
        }

        Map<String, Object> cardProps = Maps.newHashMap();
        cardProps.put("title", showTypeEnum.getTitle());
        cardProps.put("icon",showTypeEnum.getIcon());
        cardProps.put("showType", showTypeEnum.getShowType());
        cardProps.put("iconHeight", showTypeEnum.getIconHeight());
        cardProps.put("iconWidth", showTypeEnum.getIconWidth());

        if(MapUtils.isEmpty(streamEventCardDataVO.getCardProps())){
            return;
        }

        List<SourceData> sourceData = (List<SourceData>) streamEventCardDataVO.getCardProps().get("retrievalResults");
        if(CollectionUtils.isEmpty(sourceData)){
            return;
        }
        List<SourceData> urlList = Lists.newArrayList();
        cardProps.put("urls", urlList);
        for(SourceData data : sourceData){
            urlList.add(data);
        }
        streamEventCardDataVO.setCardProps(cardProps);
    }

    @Override
    public String cardDecode(StreamEventCardDataVO streamEventCardDataVO) {
        return StreamEventCardTypeEnum.buildCardContent(streamEventCardDataVO.getType(), streamEventCardDataVO.getKey());
    }
}
