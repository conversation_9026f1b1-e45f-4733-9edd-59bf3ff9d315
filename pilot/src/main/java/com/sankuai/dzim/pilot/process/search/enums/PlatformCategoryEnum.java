package com.sankuai.dzim.pilot.process.search.enums;

public enum PlatformCategoryEnum {
    KTV(812000014, "KTV商品查询类目"),
    BACKROOM(81522, "密室商品查询类目"),
    ROLE_PLAY(812000012, "桌面剧本杀商品查询类目"),
    CHESS(812000251, "棋牌商品查询类目"),
    BALL(811650, "球类场馆商品查询类目"),
    MASSAGE(81521, "足疗商品查询类目"),
    BAR(81550, "酒吧商品查询类目"),
    PET(812000273, "宠物商品查询类目"),

    ;

    private long code;
    private String desc;

    PlatformCategoryEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public long getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}