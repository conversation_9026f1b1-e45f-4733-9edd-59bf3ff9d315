package com.sankuai.dzim.pilot.acl.data.fraiday;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.Builder;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ToolCall {

    /**
     * openai生成的工具调用id,并行调用的时候使用
     */
    private String id;

    private String type = "function";

    private ToolCallDesc function;

    @Data
    @Builder
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ToolCallDesc {
        private String name;

        private JsonNode arguments;
    }
}
