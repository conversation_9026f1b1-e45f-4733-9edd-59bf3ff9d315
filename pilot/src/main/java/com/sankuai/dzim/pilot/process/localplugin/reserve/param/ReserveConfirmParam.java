package com.sankuai.dzim.pilot.process.localplugin.reserve.param;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import com.sankuai.dzim.pilot.process.localplugin.param.Param;
import lombok.Data;

/**
 * @author: w<PERSON><PERSON><PERSON><PERSON>
 * @create: 2025-04-16
 * @description:
 */
@Data
public class ReserveConfirmParam extends Param {
    @JsonPropertyDescription("shopId请从历史消息中获取,取最近的一个门店id，不要主动向用户询问id, 不要捏造, 获取要有依据")
    @JsonProperty(required = false)
    private Long shopId;

    @JsonPropertyDescription("商品ID请从消息中获取,没有可以不需要")
    @JsonProperty(required = false)
    private Long productId;

    @JsonPropertyDescription("商品类型请从消息中获取,团购商品为1，预付商品为2，没有可以不需要")
    @JsonProperty(required = false)
    private int productType;


    @JsonPropertyDescription("订单ID请从消息中获取，没有可以不需要")
    @JsonProperty(required = false)
    private Long orderId;
}
