package com.sankuai.dzim.pilot.scene.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dzim.message.dto.MessageDTO;
import com.sankuai.dzim.pilot.buffer.core.PilotBufferItemDO;
import com.sankuai.dzim.pilot.buffer.enums.BufferItemTypeEnum;
import com.sankuai.dzim.pilot.buffer.enums.StreamEventCardTypeEnum;
import com.sankuai.dzim.pilot.chain.data.AIAnswerData;
import com.sankuai.dzim.pilot.domain.memory.data.QueryMemoryContext;
import com.sankuai.dzim.pilot.domain.memory.impl.AssistantMemoryProvider;
import com.sankuai.dzim.pilot.domain.message.Message;
import com.sankuai.dzim.pilot.scene.task.data.TaskContext;
import com.sankuai.dzim.pilot.scene.task.data.TaskResult;
import com.sankuai.dzim.pilot.scene.task.data.TaskTypeEnum;
import com.sankuai.dzim.pilot.utils.JsonUtils;
import com.sankuai.dzim.pilot.utils.UuidUtil;
import com.sankuai.dzim.pilot.utils.context.RequestContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class InnerRelatedQuestionSubTask extends RelatedQuestionSubTask implements Task {

    @Autowired
    private AssistantMemoryProvider assistantMemoryProvider;

    @Override
    public boolean accept(TaskContext context) {
        return TaskTypeEnum.INNER_RELATED_QUESTION_SUB_TASK.getType().equals(context.getTaskType());
    }

    @Override
    public TaskResult execute(TaskContext context) {
        // 提前拼装好message,否则functioncall场景下可能会当成问题进行回复,不生成关联问题
        List<Message> memory = assistantMemoryProvider.getMemory(buildMemoryContext(context.getMessageDTO()));
        // 返回关联问题
        return relatedTaskResult(context, memory);
    }

    @Override
    public void afterExecute(TaskResult result) {
        try {
            if (result == null || result.getAiAnswerData() == null) {
                return;
            }
            AIAnswerData aiAnswerData = result.getAiAnswerData();
            if (StringUtils.isBlank(aiAnswerData.getAnswer())) {
                return;
            }
            // 解析关联问题
            if (aiAnswerData.getAnswer().contains("问题无法回答")) {
                return;
            }
            String answer = JsonUtils.getJson(aiAnswerData.getAnswer());
            JSONObject jsonObject = JSON.parseObject(answer);
            List<String> questionList = jsonObject.getJSONArray("questions").toJavaList(String.class);
            if (CollectionUtils.isEmpty(questionList)) {
                return;
            }
            // 最多8个问题
            questionList = questionList.subList(0, Math.min(questionList.size(), 8));
            // 写入buffer
            this.writeBuffer(questionList);
        } catch (Exception e) {
            log.error("InnerRelatedQuestionSubTask error, result: {}", result, e);
        }
    }

    public void writeBuffer(List<String> questionList) {

        for (String question : questionList) {
            String cardKey = UuidUtil.getRandomCardKey();
            String data = StreamEventCardTypeEnum.buildCardContent(StreamEventCardTypeEnum.RELATED_QUESTION_CARD, cardKey);

            Map<String, Object> extra = Maps.newHashMap();
            extra.put("question", question);

            PilotBufferItemDO pilotBufferItemDO = new PilotBufferItemDO();
            pilotBufferItemDO.setType(BufferItemTypeEnum.RECOMMEND_QUESTION.getType());
            pilotBufferItemDO.setData(data);
            pilotBufferItemDO.setExtra(extra);
            RequestContext.writeBuffer(Lists.newArrayList(pilotBufferItemDO));
        }
    }
}
