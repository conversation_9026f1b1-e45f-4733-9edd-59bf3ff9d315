package com.sankuai.dzim.pilot.process.localplugin.param;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import lombok.Data;

@Data
public class BanquetMenuQueryParam {

    @JsonPropertyDescription("需要查询的门店Id,门店Id请从系统消息中获取")
    @JsonProperty(required = true)
    private long shopId;

    @JsonPropertyDescription("与宴会菜单相关的具体问题,越具体越好,最好是一个问句,例如“套餐里都有什么菜品呢?我想看看你们的菜单情况？”等")
    @JsonProperty(required = true)
    private String question;
}
