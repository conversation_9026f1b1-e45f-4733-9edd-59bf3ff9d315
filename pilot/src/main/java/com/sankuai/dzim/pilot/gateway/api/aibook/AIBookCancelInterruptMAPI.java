package com.sankuai.dzim.pilot.gateway.api.aibook;

import com.dianping.gateway.enums.APIModeEnum;
import com.dianping.vc.web.api.API;
import com.dianping.vc.web.api.URL;
import com.dianping.vc.web.api.VCAssert;
import com.dianping.vc.web.biz.client.api.AppContext;
import com.dianping.vc.web.biz.client.api.AppContextAware;
import com.dianping.vc.web.biz.client.api.MTUserIdAware;
import com.dianping.vc.web.biz.client.api.UserIdAware;
import com.google.common.collect.Maps;
import com.sankuai.dzim.pilot.api.enums.assistant.PlatformEnum;
import com.sankuai.dzim.pilot.gateway.api.aibook.data.AIBookCancelResultVO;
import com.sankuai.dzim.pilot.process.aibook.AIBookProcessService;
import com.sankuai.dzim.pilot.process.aibook.data.AIBookCancelRequest;
import com.sankuai.dzim.pilot.process.aibook.data.AIBookTypeEnum;
import com.sankuai.dzim.pilot.utils.CatUtils;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

@Setter
@Slf4j
@Service
@URL(url = "/dzim/pilot/ai/book/cancel", mode = APIModeEnum.CLIENT, methods = "POST")
public class AIBookCancelInterruptMAPI implements API, UserIdAware, MTUserIdAware, AppContextAware {
    @Setter
    private AppContext appContext;

    @Setter
    private long userId;

    @Setter
    private long mTUserId;


    @Resource
    private AIBookProcessService aiBookProcessService;

    // 近期任务取消成功
    public static final int BOOK_CANCEL_RECENT_TASK_SUCCESS = 1;
    // 未查询到近期任务
    public static final int BOOK_CANCEL_RECENT_TASK_NOT_FOUND = 2;
    // 近期任务取消异常
    public static final int BOOK_CANCEL_RECENT_TASK_EXCEPTION = 3;
    // 近期任务非轮训中，无法取消
    public static final int BOOK_CANCEL_RECENT_TASK_NOT_ASKING = 4;

    private static final String TASK_NOT_FUND = "未查询到近期任务";

    private static final String CANCEL_EXCEPTION = "任务取消异常";

    private static final String TASK_NOT_IN_ASKING = "任务非轮训中，无法取消";

    private static int CANCEL_SUCCESS = 1;
    private static int CANCEL_FAILED = 2;

    private static final String CANCEL_FAIL_MSG = "取消失败";

    @Override
    public Object execute() {
        validateParams();
        AIBookCancelRequest aiBookCancelRequest = buildAiBookCancelRequest();
        int cancelStatus = aiBookProcessService.cancelAIBook(aiBookCancelRequest);

        // 打点
        logMetricSubmitResult(cancelStatus);

        return buildAIBookCancelResultVO(cancelStatus);
    }

    private Object buildAIBookCancelResultVO(int cancelStatus) {
        if (cancelStatus == BOOK_CANCEL_RECENT_TASK_SUCCESS) {
            return AIBookCancelResultVO.builder().cancelResultType(CANCEL_SUCCESS).build();
        } else {
            return AIBookCancelResultVO.builder().cancelResultType(CANCEL_FAILED).errorMsg(CANCEL_FAIL_MSG).build();
        }
    }

    private void logMetricSubmitResult(int innerSubmitResultCode) {
        Map<String, String> tags = Maps.newHashMap();
        tags.put("code", String.valueOf(innerSubmitResultCode));
        CatUtils.logMetricResult("CancelAIBook", tags);
    }

    private AIBookCancelRequest buildAiBookCancelRequest() {
        int platform = PlatformEnum.getByName(appContext.getPlatform()).getType();
        long finalUserId = platform == PlatformEnum.DP.getType() ? userId : mTUserId;

        AIBookCancelRequest request = new AIBookCancelRequest();
        request.setPlatform(platform);
        request.setUserId(finalUserId);
        return request;
    }

    private void validateParams() {
        VCAssert.isTrue(userId != 0 || mTUserId != 0, API.UNLOGIN_CODE, "用户未登录");
        VCAssert.isTrue(appContext.getPlatform().equals(PlatformEnum.DP.getName()) || appContext.getPlatform().equals(PlatformEnum.MT.getName()), API.INVALID_PARAM_CODE, "平台参数不合法");
    }
}
