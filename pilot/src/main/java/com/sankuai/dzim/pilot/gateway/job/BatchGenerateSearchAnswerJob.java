package com.sankuai.dzim.pilot.gateway.job;

import com.cip.crane.client.spring.annotation.Crane;
import com.sankuai.dzim.pilot.process.GenerativeSearchProcessService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 批量生成生成式搜索答案
 * 入参: 文件绝对路径名
 */

@Component
@Slf4j
public class BatchGenerateSearchAnswerJob {

    @Autowired
    private GenerativeSearchProcessService generativeSearchProcessService;

    @Crane("com.sankuai.mim.pilot.batchGenerateSearchAnswer")
    public void handle(String fileName) {
        // param按冒号分隔,第一个为bizType,第二个为fileName
        if (StringUtils.isEmpty(fileName)) {
            log.error("参数错误, param:{}", fileName);
            return;
        }

        int successCnt = generativeSearchProcessService.batchGenerateAnswer(fileName);
        log.info("批量生成生成式搜索答案成功, successCnt:{}", successCnt);
    }
}
