package com.sankuai.dzim.pilot.chain.eval.data;

import com.sankuai.dzim.pilot.domain.message.Message;
import com.sankuai.dzim.pilot.domain.message.PluginCall;
import com.sankuai.it.xcontract.easypoi.annotation.Excel;
import lombok.Data;

import java.util.List;

/**
 * 评测集
 * @author: zhouyibing
 * @date: 2025/4/17
 */
@Data
public class EvalCase {

    @Excel(name = "sessionId")
    private Long sessionId;

    @Excel(name = "input")
    private String input;

    @Excel(name = "output")
    private String output;

    @Excel(name = "targetOutput")
    private String targetOutput;

    private List<PluginCall> pluginCalls;

    @Excel(name = "knowledge")
    private String knowledge;

    @Excel(name = "context")
    private String context;

    /**
     * 历史消息,通过聚合相同的sessionId构造
     */
    private List<Message> chatHistory;

    /**
     * 历史消息字符串,通过聚合相同的sessionId构造
     */
    private String chatHistoryStr;


}
