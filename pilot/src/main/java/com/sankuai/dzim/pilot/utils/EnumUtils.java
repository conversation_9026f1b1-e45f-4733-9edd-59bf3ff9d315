package com.sankuai.dzim.pilot.utils;

import java.lang.reflect.Method;

/**
 * <AUTHOR>
 * @since 2024/11/11 21:05
 */
public class EnumUtils {

    /**
     * 判断枚举类中是否有对应的类型
     * 枚举类中必须存在getType方法
     * @param enumClass
     * @param type
     * @return true/false
     * @param <E>
     */
    public static <E extends Enum<E>> boolean isTypeExist(Class<E> enumClass, int type) {
        try {
            E[] enumConstants = enumClass.getEnumConstants();
            // 获取枚举类中名为 "getType" 的方法
            Method getTypeMethod = enumClass.getMethod("getType");
            for (E e : enumConstants) {
                int enumType = (int) getTypeMethod.invoke(e);
                if (enumType == type) {
                    return true;
                }
            }
        } catch (Exception e) {
            throw new RuntimeException("判断枚举类中是否有对应的类型异常", e);
        }
        return false;
    }
}
