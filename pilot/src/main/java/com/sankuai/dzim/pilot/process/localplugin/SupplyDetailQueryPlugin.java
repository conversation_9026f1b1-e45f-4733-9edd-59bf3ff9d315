package com.sankuai.dzim.pilot.process.localplugin;

import com.google.common.collect.Lists;
import com.sankuai.dzim.pilot.acl.ShopAclService;
import com.sankuai.dzim.pilot.buffer.core.PilotBufferItemDO;
import com.sankuai.dzim.pilot.buffer.utils.BufferUtils;
import com.sankuai.dzim.pilot.chain.data.AIAnswerData;
import com.sankuai.dzim.pilot.domain.annotation.LocalPlugin;
import com.sankuai.dzim.pilot.process.EsSearchProcessService;
import com.sankuai.dzim.pilot.process.localplugin.param.ShopProductSearchParam;
import com.sankuai.dzim.pilot.process.localplugin.param.SupplyDetailQueryParam;
import com.sankuai.dzim.pilot.utils.LionConfigUtil;
import com.sankuai.dzim.pilot.utils.SupplyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/04/17 11:19
 * 供给详情查询
 * 1. 根据各id查询对应供给详情，并附有5条评价信息
 * 2. 没有门店id，有门店名，如果门店名查到唯一结果，则使用对应门店id查询，否则交给找店找品
 */
@Component
@Slf4j
public class SupplyDetailQueryPlugin {

    @Autowired
    private SupplyUtils supplyUtils;
    @Autowired
    private EsSearchProcessService esSearchProcessService;
    @Autowired
    private ShopAclService shopAclService;
    @Autowired
    private ShopProductSearchPlugin shopProductSearchPlugin;

    @Autowired
    private LionConfigUtil lionConfigUtil;

    @LocalPlugin(name = "SupplyDetailQueryTool", description = "你可以使用这个工具查询具体单个供给(门店、商品、手艺人)详细信息,请使用TaskDispatchTool")
    public AIAnswerData querySupplyDetail(SupplyDetailQueryParam param) {
        log.info("调用插件-SupplyDetailQueryTool，入参={}", param);
        AIAnswerData answerData = new AIAnswerData();
        answerData.setAnswer("没有找到目标供给,请向用户确认具体咨询的供给是哪一个或从上下文中重新提取");

        try {
            // 查门店
            String shopDetail = queryShopDetail(param);

            // 查商品
            String productDetail = queryDealDetail(param);

            // 查手艺人
            String technicianDetail = queryTechnicianDetail(param);

            StringBuilder res = new StringBuilder();
            if (StringUtils.isNotBlank(shopDetail)) {
                res.append("相关门店信息为: \n").append(shopDetail).append("\n");
            }
            if (StringUtils.isNotBlank(productDetail)) {
                res.append("相关商品信息为: \n").append(productDetail).append("\n");
            }
            if (StringUtils.isNotBlank(technicianDetail)) {
                res.append("相关手艺人信息为: \n").append(technicianDetail).append("\n");
            }
            if (StringUtils.isBlank(res.toString())) {
                return answerData;
            }
            res.append("请基于以上信息回答用户的问题,你的答案和格式必须来自于上面的信息,请不要随意捏造,如果无法回答用户的问题,请礼貌的告知用户暂时回答不了,可以在门店详情页内直接向商家咨询.");
            answerData.setAnswer(res.toString());

        } catch (Exception e) {
            log.error("调用插件-SupplyDetailQueryTool异常, param:{}", param, e);
            answerData.setAnswer("抱歉，我暂时无法提供该供给的详细信息。");
        }
        log.info("调用插件-SupplyDetailQueryTool，结果={}", answerData.getAnswer().replaceAll("\n", "↵"));
        return answerData;
    }

    private String queryShopDetail(SupplyDetailQueryParam param) {
        if (param.getShopId() <= 0 && StringUtils.isBlank(param.getShopName())) {
            return null;
        }

        if (param.getShopId() > 0) {
            String info = queryShopDetail(param.getShopId(), param.getSemanticCondition(), param);
            if (StringUtils.isNotBlank(info)) {
                return info;
            }
        }

        if (StringUtils.isNotBlank(param.getShopName())) {
            List<Long> mtShopIds = supplyUtils.queryShopIdByName(param.getShopName(), param)
                    .stream()
                    .limit(5)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(mtShopIds)) {
                return null;
            }
            if (mtShopIds.size() == 1) {
                String info = queryShopDetail(mtShopIds.get(0), param.getSemanticCondition(), param);
                if (StringUtils.isNotBlank(info)) {
                    return info;
                }
            }
            return "找到多个相似门店,请将任务下发给任务执行工具中的找店找品能力,向用户确认最合适的门店";
        }
        return null;
    }

    private String queryShopDetail(long mtShopId, String condition, SupplyDetailQueryParam param) {
        BufferUtils.writeStatusBuffer(PilotBufferItemDO.builder().data(lionConfigUtil.getLoadingText("findShop")).build());

        Map<Long, String> mtShopId2ShopNL = supplyUtils.batchTransferShop2NL(Lists.newArrayList(mtShopId));
        if (MapUtils.isEmpty(mtShopId2ShopNL)) {
            return StringUtils.EMPTY;
        }
        StringBuilder res = new StringBuilder();
        String shopInfo = mtShopId2ShopNL.get(mtShopId);
        if (StringUtils.isBlank(shopInfo)) {
            return StringUtils.EMPTY;
        }
        res.append(shopInfo);
        res.append("\n");
        if (StringUtils.isNotBlank(condition)) {
            List<String> reviews = esSearchProcessService.searchShopReview(mtShopId, condition, 5);
            if (CollectionUtils.isNotEmpty(reviews)) {
                res.append("相关的门店评价信息为:\n");
                res.append(String.join("\n", reviews));
                res.append("\n\n");
            }
        }

        // 查询高销团购
        AIAnswerData aiAnswerData = shopProductSearchPlugin.searchShopProducts(buildShopProductSearchParam(mtShopId, param));
        if (aiAnswerData != null && StringUtils.isNotBlank(aiAnswerData.getAnswer())) {
            res.append("门店相关的商品信息为:\n").append(aiAnswerData.getAnswer()).append("\n");
            // todo 总结指令线上化
            res.append("如果用户是想要寻找内部的团购,请按照如下格式输出:\n").append(String.format("# 总结要求  \n1. **开头**：先给出一句总推荐语（≤40 字），突出本次推荐相关商品的核心亮点，如何满足用户的需求。  \n2. **主体**：判断本次内容类型——  \n   - **推荐商品**：按输入顺序输出  \n     <ShopProductCard>${shopId}:${productIds}</ShopProductCard>  \n   规则：  \n   - `${shop}` 当前为%s。\n   - `**${productIds}`、，productIds为一个数字，**数组中的每个元素是一个要推荐的ProductId**，最多3~5个**\n   - 严格按上述格式输出。  \n   - **禁止**插入其他字符、空行或标点。\n3. **结尾**：一句结束语，可邀请用户预约/购买，或基于决策要素追问需求（≤40 字）。\n\n# 示例  \n好滴，我帮您找到了 3 个非常适合腰肌劳损团购，首推 **推拿艾灸这个**～  \n<ShopProductCard>s1:[p1,p2,p3]</ShopProductCard>  \n喜欢的话我可以帮你预约；如果对环境或餐食有特殊偏好，也告诉我，我再帮你找找",
                    mtShopId)).append("\n");
        }
        return res.toString();
    }

    private ShopProductSearchParam buildShopProductSearchParam(long mtShopId, SupplyDetailQueryParam param) {
        ShopProductSearchParam shopProductSearchParam = new ShopProductSearchParam();
        shopProductSearchParam.setShopId(mtShopId);
        shopProductSearchParam.setProductSemanticCondition(param.getSemanticCondition());
//        shopProductSearchParam.setShowText("正在为您查找商品信息...");
        shopProductSearchParam.setAiServiceContext(param.getAiServiceContext());
        return shopProductSearchParam;
    }

    private String queryDealDetail(SupplyDetailQueryParam param) {
        if (param.getProductId() <= 0) {
            return null;
        }
        BufferUtils.writeStatusBuffer(PilotBufferItemDO.builder().data(lionConfigUtil.getLoadingText("findProduct")).build());

        StringBuilder res = new StringBuilder();
        Map<Long, String> dealId2DealNL = supplyUtils.batchTransferDeal2NL(Lists.newArrayList(param.getProductId()));
        if (MapUtils.isEmpty(dealId2DealNL)) {
            return StringUtils.EMPTY;
        }
        String shopInfo = dealId2DealNL.get(param.getProductId());
        if (StringUtils.isBlank(shopInfo)) {
            return StringUtils.EMPTY;
        }
        res.append(shopInfo);
        res.append("\n");
        if (StringUtils.isNotBlank(param.getSemanticCondition())) {
            List<String> reviews = esSearchProcessService.searchProductReview(param.getProductId(), param.getSemanticCondition(), 5);
            if (CollectionUtils.isNotEmpty(reviews)) {
                res.append("相关的评价信息为:\n");
                res.append(String.join("\n", reviews));
                res.append("\n\n");
            }
        }
        return res.toString();
    }

    private String queryTechnicianDetail(SupplyDetailQueryParam param) {
        if (param.getTechnicianId() <= 0) {
            return null;
        }
        BufferUtils.writeStatusBuffer(PilotBufferItemDO.builder().data("正在为您查询技师信息...").build());
        Map<Long, String> technicianId2TechnicianNL = supplyUtils.batchTransferTechnician2NL(Lists.newArrayList(param.getTechnicianId()));
        if (MapUtils.isEmpty(technicianId2TechnicianNL)) {
            return StringUtils.EMPTY;
        }
        StringBuilder res = new StringBuilder(technicianId2TechnicianNL.get(param.getTechnicianId()));
        List<String> reviews = esSearchProcessService.searchTechnicianReview(param.getTechnicianId(), param.getSemanticCondition(), 5);
        if (CollectionUtils.isNotEmpty(reviews)) {
            res.append("相关的评价信息为:\n");
            res.append(String.join("\n", reviews));
            res.append("\n\n");
        }
        return res.toString();
    }
}
