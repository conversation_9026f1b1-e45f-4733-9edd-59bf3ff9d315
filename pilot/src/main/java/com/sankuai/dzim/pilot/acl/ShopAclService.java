package com.sankuai.dzim.pilot.acl;

import com.dianping.generic.entrance.poiphone.dto.PhoneDTO;
import com.dianping.poi.bizhour.dto.BizForecastDTO;
import com.dianping.poi.bizhour.enums.SourceEnum;
import com.meituan.nibmp.mem.vaf.query.thrift.dto.BatchQueryBizCooperateInfoReqDTO;
import com.meituan.nibmp.mem.vaf.query.thrift.dto.BizCooperateInfoDTO;
import com.sankuai.dzim.pilot.acl.data.ShopBackCategoryData;
import com.sankuai.dzim.pilot.acl.data.ShopCardData;
import com.sankuai.dzim.pilot.acl.data.ShopCategoryData;
import com.sankuai.dzim.pilot.acl.data.req.ShopCardQueryReq;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.MtPoiDTO;
import com.sankuai.zdc.tag.apply.dto.DisplayTagDto;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

public interface ShopAclService {

    /**
     * @param dpShopId
     * @return
     */
    DpPoiDTO getShopInfo(long dpShopId);

    /**
     * 异步获取门店信息
     *
     * @param dpShopId
     * @return
     */
    CompletableFuture<DpPoiDTO> getShopInfoAsync(long dpShopId);

    /**
     * 查询poi标签
     *
     * @param dpShopId
     * @param bizCode
     * @return
     */
    CompletableFuture<List<DisplayTagDto>> getPoiTagsByShopIdAndBizCode(long dpShopId, String bizCode);

    /**
     * 查询门店的点评前台类目路径
     *
     * @param dpShopId
     * @return
     */
    ShopCategoryData loadShopFrontCatePath(long dpShopId);

    /**
     * 批量查询点评->美团门店映射关系
     *
     * @param shopIds, 100个限制
     * @return
     */
    CompletableFuture<Map<Long, Long>> queryPoiMappingByDpIdL(List<Long> shopIds);

    /**
     * 批量查询点评->美团门店映射关系
     *
     * @param shopIds, 无限制,内部并行
     * @return
     */
    CompletableFuture<Map<Long, Long>> queryMtIdLByDpIdL(List<Long> shopIds);

    /**
     * 批量查询美团->点评门店映射关系
     *
     * @param shopIds, 无限制,内部并行
     * @return
     */
    CompletableFuture<Map<Long, Long>> queryDpIdLByMtIdL(List<Long> shopIds);

    /**
     * 通过美团门店id查询点评门店id
     *
     * @param dpShopId
     * @return
     */
    long loadMTShopIdByDPShopId(long dpShopId);

    /**
     * @param mtShopId
     * @return
     */
    MtPoiDTO getMtShopInfo(long mtShopId);

    /**
     * 美团门店转点评门店ID
     *
     * @param mtShopId
     * @return
     */
    long loadDpShopIdByMtShopId(long mtShopId);


    /**
     * 批量查询美团门店信息
     *
     * @param mtShopIds
     * @return
     */
    Map<Long, MtPoiDTO> batchGetMtShopInfo(List<Long> mtShopIds);

    /**
     * 异步批量查询美团门店信息
     *
     * @param mtShopIds
     * @return
     */
    CompletableFuture<Map<Long, MtPoiDTO>> batchGetMtShopInfoAsync(List<Long> mtShopIds);

    ShopCardData getShopCard(ShopCardQueryReq req);


    /**
     * 查询门店电话
     *
     * @param dpShopId dpShopId
     * @return
     */
    List<String> getShopPhone(long dpShopId);

    /**
     * 批量查询门店电话
     *
     * @param dpShopIds dpShopIds
     * @return
     */
    Map<Long, List<PhoneDTO>> batchGetShopPhone(List<Long> dpShopIds);


    /**
     * 指定时间营业时间预告查询
     *
     * @param poiId poiId
     * @param source source
     * @param dateString dateString
     * @return
     */
    BizForecastDTO getBizForecast(long dpShopId, String dateString);

    /**
     * 查询门店的合作状态
     * @param reqDTO
     * @return
     */
    Map<Long, BizCooperateInfoDTO> queryShopCooperateInfo(BatchQueryBizCooperateInfoReqDTO reqDTO);

    /**
     * 获取商户后台类目
     * @param dpShopId
     * @return
     */
    List<ShopBackCategoryData> getShopBackCategory(long dpShopId);

    /**
     * 从商户信息中提取后台类目数据
     * @param dpPoiDTO
     * @return
     */
    List<ShopBackCategoryData> extractShopBackCategoryData(DpPoiDTO dpPoiDTO);

    /**
     * 从商户信息中提取主营后台类目路径
     * https://km.sankuai.com/collabpage/1397934780
     * @param dpPoiDTO
     * @return
     */
    List<ShopBackCategoryData> extractShopBackMainCategoryPath(DpPoiDTO dpPoiDTO);
}
