package com.sankuai.dzim.pilot;

import com.dianping.lion.client.Lion;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

@Component
public class CORSFilter implements Filter {

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        HttpServletResponse res = (HttpServletResponse) response;
        HttpServletRequest req = (HttpServletRequest) request;
        String referer = req.getHeader("Referer");
        String allowHeaders = Lion.getString("com.sankuai.mim.pilot", "com.sankuai.mim.pilot.cors.header.config", "Content-Type, Authorization, credentials, X-Requested-With");
        if (StringUtils.isNotEmpty(referer)) {
            List<String> acceptDomains = Lion.getList("com.sankuai.mim.pilot", "com.sankuai.mim.pilot.cors.domain.config", String.class);
            if (CollectionUtils.isNotEmpty(acceptDomains)) {
                for (String domain : acceptDomains) {
                    if (referer.contains(domain) || domain.contains(referer)) {
                        res.setHeader("Access-Control-Allow-Origin", req.getHeader("Origin"));
                        res.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
                        res.setHeader("Access-Control-Allow-Credentials", "true");
                        res.setHeader("Access-Control-Allow-Headers", allowHeaders);
                        break;
                    }
                }

            }
        }

        // 只处理 内部 请求
        if (req.getRequestURI().contains("inner") || req.getRequestURI().contains("assistant/chatgroup/history")) {
            res.setHeader("Access-Control-Allow-Origin", req.getHeader("Origin"));
            res.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
            res.setHeader("Access-Control-Allow-Credentials", "true");
            res.setHeader("Access-Control-Allow-Headers", allowHeaders);
        }

        chain.doFilter(request, response);
    }

    @Override
    public void destroy() {
    }
}
