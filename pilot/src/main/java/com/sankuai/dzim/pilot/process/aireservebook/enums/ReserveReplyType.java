package com.sankuai.dzim.pilot.process.aireservebook.enums;

/**
 * @author: w<PERSON><PERSON><PERSON><PERSON>
 * @create: 2025-04-24
 * @description:
 */
public enum ReserveReplyType {

    DIRECT(1, "直接回复"),
    GENERATE(2, "生成回复"),
    CARD(3, "卡片回复");

    ReserveReplyType(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public int getType() {
        return type;
    }

    private int type;
    private String desc;
}
