package com.sankuai.dzim.pilot.domain.retrieval.impl;

import com.sankuai.dzim.pilot.acl.ProductAclService;
import com.sankuai.dzim.pilot.domain.annotation.Retrieval;
import com.sankuai.dzim.pilot.domain.retrieval.RetrievalAugmentor;
import com.sankuai.dzim.pilot.domain.retrieval.data.RetrievalContext;
import com.sankuai.dzim.pilot.utils.IMConstants;
import com.sankuai.dzim.pilot.domain.retrieval.data.RetrievalResult;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.detail.DealGroupTemplateDetailDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@Retrieval(name = "DealDetailRetrieval", desc = "团购详情检索器,信息来自商品查询中心")
public class DealDetailAugmentor implements RetrievalAugmentor {

    @Autowired
    private ProductAclService productAclService;

    @Override
    public String retrieval(RetrievalContext context) {
        // 获取商品Id
        long dealId = getDealId(context);
        if (dealId == 0) {
            return IMConstants.DEFAULT_ANSWER;
        }

        // 查询商品信息
        DealGroupDTO dealGroupDTO = productAclService.getDealBaseInfo(dealId);
        if (dealGroupDTO == null) {
            return IMConstants.DEFAULT_ANSWER;
        }

        return convertDealGroup2Text(dealGroupDTO);
    }

    private String convertDealGroup2Text(DealGroupDTO dealGroupDTO) {
        StringBuilder sb = new StringBuilder();
        sb.append("你可以参考的团购商品信息如下:").append("\n");
        sb.append("商品名称:").append(getTitle(dealGroupDTO)).append("\n");
        sb.append("商品价格:").append(getPrice(dealGroupDTO)).append("\n");
        sb.append("商品服务项:").append(getServiceProject(dealGroupDTO)).append("\n");
        sb.append("商品介绍:").append(getIntroduction(dealGroupDTO)).append("\n");

        return sb.toString();
    }

    private String getIntroduction(DealGroupDTO dealGroupDTO) {
        if (dealGroupDTO.getDetail() == null || CollectionUtils.isEmpty(dealGroupDTO.getDetail().getTemplateDetailDTOs())) {
            return "暂无商品介绍信息";
        }

        StringBuilder sb = new StringBuilder();
        for (DealGroupTemplateDetailDTO templateDetailDTO : dealGroupDTO.getDetail().getTemplateDetailDTOs()) {
            if (templateDetailDTO.getTitle().equals("产品介绍")) {
                sb.append("\n\t产品介绍:").append(parseHtml(templateDetailDTO.getContent(), "\n\t\t"));
            }
            if (templateDetailDTO.getTitle().equals("团购详情")) {
                sb.append("\n\t补充信息:").append(parseHtml(templateDetailDTO.getContent(), "\n\t\t"));
            }
            if (templateDetailDTO.getTitle().equals("购买须知")) {
                sb.append("\n\t购买须知:").append(parseHtml(templateDetailDTO.getContent(), "\n\t\t"));
            }
        }

        return sb.toString();
    }

    private String parseHtml(String content, String prefix) {
        StringBuilder stringBuilder = new StringBuilder();

        Document doc = Jsoup.parse(content);
        Elements paragraphs = doc.select("p");
        for (Element paragraph : paragraphs) {
            if (StringUtils.isNotBlank(paragraph.text())) {
                stringBuilder.append(prefix).append(paragraph.text());
            }
        }

        return stringBuilder.toString();
    }

    private String getServiceProject(DealGroupDTO dealGroupDTO) {
        if (dealGroupDTO.getServiceProject() == null) {
            return "暂无服务项目信息";
        }
        DealGroupServiceProjectDTO serviceProject = dealGroupDTO.getServiceProject();
        StringBuilder serviceSb = new StringBuilder();

        // 全部可享
        if (CollectionUtils.isNotEmpty(dealGroupDTO.getServiceProject().getMustGroups())) {
            serviceSb.append("\n\t【全部可享】服务包含:");
            for (MustServiceProjectGroupDTO serviceGroup : dealGroupDTO.getServiceProject().getMustGroups()) {
                if (CollectionUtils.isEmpty(serviceGroup.getGroups())) {
                    continue;
                }
                for (ServiceProjectDTO serviceProjectDTO : serviceGroup.getGroups()) {
                    serviceSb.append("\n\t\t").append(parseServiceProject(serviceProjectDTO));
                }
            }
        }

        // 部分可享
        if (CollectionUtils.isNotEmpty(dealGroupDTO.getServiceProject().getOptionGroups())) {
            serviceSb.append("\n\t【部分可享】服务包含:");
            for (OptionalServiceProjectGroupDTO serviceGroup : dealGroupDTO.getServiceProject().getOptionGroups()) {
                if (CollectionUtils.isEmpty(serviceGroup.getGroups())) {
                    continue;
                }
                String optionTip = String.format("%s选%s", serviceGroup.getGroups().size(), serviceGroup.getOptionalCount());
                serviceSb.append("\n\t\t").append(optionTip).append(":");
                for (ServiceProjectDTO serviceProjectDTO : serviceGroup.getGroups()) {
                    serviceSb.append("\n\t\t\t").append(parseServiceProject(serviceProjectDTO));
                }
            }
        }
        return serviceSb.toString();
    }

    private String parseServiceProject(ServiceProjectDTO serviceProjectDTO) {
        StringBuilder sb = new StringBuilder();
        sb.append("服务名称:").append(serviceProjectDTO.getName());
        if (StringUtils.isNotBlank(serviceProjectDTO.getMarketPrice())) {
            sb.append(" ( ").append("价值:").append(serviceProjectDTO.getMarketPrice()).append("元").append(" )");
        }
        if (CollectionUtils.isEmpty(serviceProjectDTO.getAttrs())) {
            return sb.toString();
        }

        for (int index = 0; index < serviceProjectDTO.getAttrs().size(); ++index) {
            ServiceProjectAttrDTO attrDTO = serviceProjectDTO.getAttrs().get(index);
            sb.append(" ( ").append(attrDTO.getChnName()).append(":").append(attrDTO.getAttrValue());
            if (index != serviceProjectDTO.getAttrs().size() - 1) {
                sb.append(", ");
            } else {
                sb.append(" )");
            }
        }
        return sb.toString();
    }

    private String getPrice(DealGroupDTO dealGroupDTO) {
        if (dealGroupDTO.getPrice() == null) {
            return "暂无价格信息," + IMConstants.ANSWER_PROBLEM_NO_REPLY;
        }

        return dealGroupDTO.getPrice().getSalePrice() + "元";
    }

    private String getTitle(DealGroupDTO dealGroupDTO) {
        String title = dealGroupDTO.getBasic().getTitle();
        String titleDesc = dealGroupDTO.getBasic().getTitleDesc();
        if (StringUtils.isNotBlank(titleDesc)) {
           return title + "( " + titleDesc + " )";
        }
        return title;
    }

    private long getDealId(RetrievalContext context) {
        Map<Integer, String> type2SubjectIdMap = context.getType2SubjectIdMap();
        if (MapUtils.isEmpty(type2SubjectIdMap)) {
            return 0;
        }

        return type2SubjectIdMap.values().stream().map(Long::parseLong).findFirst().orElse(0L);
    }

    @Override
    public RetrievalResult retrieveKnowledge(RetrievalContext context) {
        return null;
    }
}
