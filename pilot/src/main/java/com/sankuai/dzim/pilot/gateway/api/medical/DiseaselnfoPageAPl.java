package com.sankuai.dzim.pilot.gateway.api.medical;

import com.dianping.gateway.enums.APIModeEnum;
import com.dianping.vc.web.api.API;
import com.dianping.vc.web.api.URL;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.pilot.api.enums.assistant.PlatformEnum;
import com.sankuai.dzim.pilot.dal.entity.pilot.medical.DiseaseScienceInfoEntity;
import com.sankuai.dzim.pilot.dal.respository.medical.DiseaseScienceInfoRepositoryService;
import com.sankuai.dzim.pilot.domain.retrieval.impl.DiseaseSciencePageAugmentor;
import com.sankuai.dzim.pilot.gateway.api.medical.data.DiseaseScienceInfoVO;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Setter
@Slf4j
@Service
@URL(url = "/dzim/pilot/medical/disease/load", mode = APIModeEnum.CLIENT, methods = "GET")
public class DiseaselnfoPageAPl implements API {

    @Autowired
    private DiseaseScienceInfoRepositoryService diseaseScienceInfoRepositoryService;

    @Autowired
    private DiseaseSciencePageAugmentor diseaseSciencePageAugmentor;

    private String diseaseId;

    private int platform;

    @Override
    public Object execute() {
        DiseaseScienceInfoEntity entity = diseaseScienceInfoRepositoryService.selectById(Long.parseLong(diseaseId));
        if (entity == null) {
            return null;
        }

        int selectedPlatform = platform == PlatformEnum.DP.getType() ? platform : PlatformEnum.MT.getType();
        DiseaseScienceInfoVO diseaseScienceInfoVO = diseaseSciencePageAugmentor.processData(entity, selectedPlatform);

        if (diseaseScienceInfoVO == null) {
            LogUtils.logRequestLog(log, TagContext.builder().action("SearchDisease").build(), "SearchDisease", "疾病id：" + diseaseId);
            return null;
        }
        return diseaseScienceInfoVO;
    }
}
