package com.sankuai.dzim.pilot.domain.plugin.data;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class LocalPluginConfig {


    /**
     * 插件名称
     */
    private String name;

    /**
     * 插件描述
     */
    private String description;

    /**
     * 插件参数
     */
    private JsonNode params;

    /**
     * 是否直接返回
     */
    private Boolean returnDirect;
}
