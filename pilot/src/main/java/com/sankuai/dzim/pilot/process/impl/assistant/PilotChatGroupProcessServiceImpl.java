package com.sankuai.dzim.pilot.process.impl.assistant;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.beauty.sakura.behavior.enums.PlatformTypeEnum;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.message.enums.AuditStatusEnum;
import com.sankuai.dzim.pilot.acl.FridayAppAclService;
import com.sankuai.dzim.pilot.acl.ShopAclService;
import com.sankuai.dzim.pilot.acl.data.fraiday.app.FridayAppSendMessageReq;
import com.sankuai.dzim.pilot.api.data.assistant.PilotChatGroupDTO;
import com.sankuai.dzim.pilot.api.data.assistant.PilotMessageDTO;
import com.sankuai.dzim.pilot.api.enums.assistant.AssistantTypeEnum;
import com.sankuai.dzim.pilot.api.enums.assistant.MessageSendDirectionEnum;
import com.sankuai.dzim.pilot.api.enums.assistant.MessageTypeEnum;
import com.sankuai.dzim.pilot.api.enums.search.generative.GenerativeSearchAnswerTemplateTypeEnum;
import com.sankuai.dzim.pilot.buffer.enums.StreamEventCardTypeEnum;
import com.sankuai.dzim.pilot.buffer.enums.StreamEventDataTypeEnum;
import com.sankuai.dzim.pilot.buffer.enums.StreamEventTypeEnum;
import com.sankuai.dzim.pilot.buffer.stream.vo.StreamEventCardDataVO;
import com.sankuai.dzim.pilot.buffer.stream.vo.StreamEventDataVO;
import com.sankuai.dzim.pilot.buffer.stream.vo.StreamEventVO;
import com.sankuai.dzim.pilot.dal.entity.pilot.PilotChatGroupEntity;
import com.sankuai.dzim.pilot.dal.entity.pilot.PilotChatMessageEntity;
import com.sankuai.dzim.pilot.dal.pilotdao.PilotChatGroupDAO;
import com.sankuai.dzim.pilot.dal.pilotdao.PilotChatGroupMessageDAO;
import com.sankuai.dzim.pilot.domain.GenerativeSearchDomainService;
import com.sankuai.dzim.pilot.domain.data.GenerativeSearchWordE;
import com.sankuai.dzim.pilot.domain.data.RedisKeys;
import com.sankuai.dzim.pilot.gateway.api.vo.ChatGroupListAndPageVO;
import com.sankuai.dzim.pilot.gateway.api.vo.ChatGroupVO;
import com.sankuai.dzim.pilot.gateway.api.vo.InnerAssistantChatGroupVO;
import com.sankuai.dzim.pilot.gateway.api.vo.PageVO;
import com.sankuai.dzim.pilot.process.ListPurposeService;
import com.sankuai.dzim.pilot.process.PilotChatGroupProcessService;
import com.sankuai.dzim.pilot.process.QuestionScoreProcessService;
import com.sankuai.dzim.pilot.process.aireservebook.enums.POIFrontType;
import com.sankuai.dzim.pilot.process.data.*;
import com.sankuai.dzim.pilot.process.impl.assistant.strategy.QuestionRecallStrategy;
import com.sankuai.dzim.pilot.scene.task.data.EnvContext;
import com.sankuai.dzim.pilot.utils.*;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/07/31 15:06
 */
@Slf4j
@Service
public class PilotChatGroupProcessServiceImpl implements PilotChatGroupProcessService {

    @ConfigValue(key = "com.sankuai.mim.pilot.generativeSearchSugConfig", defaultValue = "{}")
    private HashMap<String, SuggestionConfig> generativeSearchSugConfig;

    @ConfigValue(key = "com.sankuai.mim.pilot.welcome.history.message.asssitant.type", defaultValue = "{}")
    private List<Integer> needFillHistoryMessageAssistant;

    /**
     * 足疗/美发小助手欢迎语追问配置
     */
    @ConfigValue(key = "com.sankuai.mim.pilot.assistant.welcome.question.config", defaultValue = "{}")
    private AssistantWelcomeQuestionConfig assistantWelcomeQuestionConfig;

    @Resource
    private PilotChatGroupDAO pilotChatGroupDAO;

    @Resource
    private PilotChatGroupMessageDAO pilotChatGroupMessageDAO;

    @Resource
    private LionConfigUtil lionConfigUtil;
    @Resource
    private IDGenerateUtil IDGenerateUtil;
    @Resource
    private AssistantLoadUtils assistantLoadUtils;

    @Autowired
    private ShopAclService shopAclService;

    @Autowired
    private GenerativeSearchDomainService generativeSearchDomainService;

    @Autowired
    private QuestionScoreProcessService questionScoreProcessService;

    @Autowired
    private ListPurposeService listPurposeService;

    @Autowired
    private List<QuestionRecallStrategy> questionRecallStrategies;

    @Autowired
    private FridayAppAclService fridayAppAclService;

    @Resource(name = "tollPhoneRedisClient")
    private RedisStoreClient redisStoreClient;

    @Override
    public PilotChatGroupDTO getChatGroupInfo(ChatGroupLoadReq chatGroupLoadReq) {
        PilotChatGroupDTO pilotChatGroupDTO = new PilotChatGroupDTO();
        try {
            // 1. 查询或创建会话
            PilotChatGroupEntity pilotChatGroupEntity = getOrCreateChatGroup(chatGroupLoadReq.getImUserId(), chatGroupLoadReq.getAssistantType());
            // 2. 获取最后一条消息或欢迎语
            handleWelcomeMessage(pilotChatGroupEntity, pilotChatGroupDTO, chatGroupLoadReq);
            // 3. 设置会话基本信息
            setChatGroupInfo(pilotChatGroupEntity, pilotChatGroupDTO);
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("PilotChatGroupProcessService:getChatGroupInfo").userId(chatGroupLoadReq.getImUserId()).build(),
                    new WarnMessage("PilotChatGroupProcessService", "查询会话失败", null), chatGroupLoadReq.getAssistantType(), null, e);
        }
        return pilotChatGroupDTO;
    }

    @Override
    public PilotChatGroupDTO getChatGroupById(long chatGroupId) {
        Assert.isTrue(chatGroupId > 0, "会话id参数错误");
        PilotChatGroupEntity entity = pilotChatGroupDAO.queryChatGroupById(chatGroupId);
        if (entity == null) {
            return null;
        }
        PilotChatGroupDTO chatGroupDTO = new PilotChatGroupDTO();
        BeanUtils.copyProperties(entity, chatGroupDTO);
        setChatGroupInfo(entity, chatGroupDTO);
        return chatGroupDTO;
    }

    @Override
    public InnerAssistantChatGroupVO getChatGroupData(Long chatGroupId, Integer assistantType, String ssoUserId) {
        InnerAssistantChatGroupVO innerAssistantChatGoupData = new InnerAssistantChatGroupVO();

        try {
            //没有chatGroupId就新建会话，有就返回会话
            if (chatGroupId != null && chatGroupId != 0) {
                PilotChatGroupEntity pilotChatGroupEntity = pilotChatGroupDAO.queryChatGroupById(chatGroupId);
                return getChatGroupData(innerAssistantChatGoupData, pilotChatGroupEntity, ssoUserId);
            }

            //新建会话
            PilotChatGroupEntity pilotChatGroupEntity = new PilotChatGroupEntity();
            pilotChatGroupEntity.setUserId(ssoUserId);
            pilotChatGroupEntity.setAssistantType(assistantType);
            pilotChatGroupEntity.setStartMessageId(0L);
            pilotChatGroupEntity.setLastMessageId(0L);
            Integer insertNum = pilotChatGroupDAO.insertChatGroup(pilotChatGroupEntity);
            Assert.isTrue(insertNum > 0, "创建会话失败");

            innerAssistantChatGoupData.setUserId(ssoUserId);
            innerAssistantChatGoupData.setChatGroupId(pilotChatGroupEntity.getId());
            innerAssistantChatGoupData.setAssistantType(assistantType);
            return innerAssistantChatGoupData;
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("getChatGroupInfo").build(),
                    new WarnMessage("PilotChatGroupProcessService", "会话查询异常", ""), ssoUserId, e);
            return null;
        }
    }

    @Override
    public ChatGroupListAndPageVO getChatGroupList(Integer pageNum, String userId, Integer pageSize) {
        long offset = (pageNum - 1) * pageSize;
        List<PilotChatGroupEntity> pilotChatGroupEntities = pilotChatGroupDAO.queryStartMessageIdByUserId(userId, offset, pageSize)
                .stream().filter(Objects::nonNull).collect(Collectors.toList());

        List<Long> startMessageIds = pilotChatGroupEntities.stream().map(PilotChatGroupEntity::getStartMessageId).collect(Collectors.toList());
        List<PilotChatMessageEntity> pilotChatMessageEntities = pilotChatGroupMessageDAO.queryMessageByMessageIds(startMessageIds);


        ChatGroupListAndPageVO chatGroupListAndPageVO = new ChatGroupListAndPageVO();
        PageVO pageVO = buildPageVo(userId, pageNum, pageSize);
        chatGroupListAndPageVO.setPageVO(pageVO);

        List<ChatGroupVO> chatGroupVOS = buildChatGroupVO(userId, pilotChatGroupEntities, pilotChatMessageEntities);
        chatGroupListAndPageVO.setChatGroupVOList(chatGroupVOS);
        return chatGroupListAndPageVO;
    }

    private void setChatGroupInfo(PilotChatGroupEntity pilotChatGroupEntity, PilotChatGroupDTO pilotChatGroupDTO) {
        BeanUtils.copyProperties(pilotChatGroupEntity, pilotChatGroupDTO);
        pilotChatGroupDTO.setChatGroupId(pilotChatGroupEntity.getId());
        if (lionConfigUtil.getAssistantWelcomeConfig(String.valueOf(pilotChatGroupEntity.getAssistantType())) != null) {
            pilotChatGroupDTO.setTitle(lionConfigUtil.getAssistantWelcomeConfig(String.valueOf(pilotChatGroupEntity.getAssistantType())).getTitle());
            pilotChatGroupDTO.setAvatar(lionConfigUtil.getAssistantWelcomeConfig(String.valueOf(pilotChatGroupEntity.getAssistantType())).getAvatar());
            pilotChatGroupDTO.setAvatarWidth(lionConfigUtil.getAssistantWelcomeConfig(String.valueOf(pilotChatGroupEntity.getAssistantType())).getAvatarWidth());
            pilotChatGroupDTO.setAvatarHeight(lionConfigUtil.getAssistantWelcomeConfig(String.valueOf(pilotChatGroupEntity.getAssistantType())).getAvatarHeight());
            pilotChatGroupDTO.setSubTitle(lionConfigUtil.getAssistantWelcomeConfig(String.valueOf(pilotChatGroupEntity.getAssistantType())).getSubTitle());
            pilotChatGroupDTO.setInputText(lionConfigUtil.getAssistantWelcomeConfig(String.valueOf(pilotChatGroupEntity.getAssistantType())).getInputText());
            pilotChatGroupDTO.setTheme(String.valueOf(lionConfigUtil.getAssistantWelcomeConfig(String.valueOf(pilotChatGroupEntity.getAssistantType())).getTheme()));
            pilotChatGroupDTO.setExtra(lionConfigUtil.getAssistantWelcomeConfig(String.valueOf(pilotChatGroupEntity.getAssistantType())).getExtra());
        }
    }

    private void handleWelcomeMessage(PilotChatGroupEntity pilotChatGroupEntity, PilotChatGroupDTO pilotChatGroupDTO, ChatGroupLoadReq chatGroupLoadReq) {
        PilotChatMessageEntity messageEntity = pilotChatGroupMessageDAO.queryMessageByMessageId(pilotChatGroupEntity.getLastMessageId());
        List<PilotMessageDTO> welcomePilotMessageDTOS = new ArrayList<>();

        //取BizParam中的query
        processBizParams(pilotChatGroupEntity, chatGroupLoadReq);
        
        Map<String, Object> welcomeContext = new HashMap<>();
        if (shouldSendWelcomeMessage(welcomeContext, messageEntity, chatGroupLoadReq, pilotChatGroupEntity.getQuery())) {
            if (messageEntity != null) {
                welcomePilotMessageDTOS.add(buildInitialMessage(pilotChatGroupEntity, MessageTypeEnum.TIPS.getValue()));
            }
            
            processWelcomeMessage(welcomeContext, pilotChatGroupDTO, welcomePilotMessageDTOS, chatGroupLoadReq, pilotChatGroupEntity);
            pilotChatGroupDTO.setMessages(welcomePilotMessageDTOS);
        } else {
            // 没有欢迎语,拉取历史记录填充,防止空屏(加速前端加载速度)
            List<PilotMessageDTO> historyMessages = fillHistoryMessage(pilotChatGroupEntity);
            if (CollectionUtils.isNotEmpty(historyMessages)) {
                pilotChatGroupDTO.setMessages(historyMessages);
            }
        }

        processSystemMessageAndQuestionStats(chatGroupLoadReq, pilotChatGroupEntity);
    }

    private List<PilotMessageDTO> fillHistoryMessage(PilotChatGroupEntity pilotChatGroupEntity) {
        if (CollectionUtils.isEmpty(needFillHistoryMessageAssistant) || !needFillHistoryMessageAssistant.contains(pilotChatGroupEntity.getAssistantType())) {
            return Collections.emptyList();
        }
        List<PilotChatMessageEntity> pilotChatMessageEntities = pilotChatGroupMessageDAO.multiGetLatestMessagesFilterByDirection(pilotChatGroupEntity.getId(), 6, Lists.newArrayList(MessageSendDirectionEnum.SYSTEM_SEND.value, MessageSendDirectionEnum.TOOL_CALL.value));
        if (CollectionUtils.isEmpty(pilotChatMessageEntities)) {
            return Collections.emptyList();
        }

        // 反转
        Collections.reverse(pilotChatMessageEntities);
        return pilotChatMessageEntities.stream().map(this::buildPilotMessageDTO).collect(Collectors.toList());
    }

    private PilotMessageDTO buildPilotMessageDTO(PilotChatMessageEntity messageEntity) {
        PilotMessageDTO pilotMessageDTO = new PilotMessageDTO();
        pilotMessageDTO.setMessageId(messageEntity.getMessageId());
        pilotMessageDTO.setMessageType(messageEntity.getMessageType());
        pilotMessageDTO.setMessageContent(messageEntity.getMessage());
        pilotMessageDTO.setDirection(messageEntity.getDirection());
        return pilotMessageDTO;
    }

    private void processBizParams(PilotChatGroupEntity pilotChatGroupEntity, ChatGroupLoadReq chatGroupLoadReq) {
        if (StringUtils.isNotBlank(chatGroupLoadReq.getBizParams())) {
            try {
                String decodedBizParams = URLDecoder.decode(chatGroupLoadReq.getBizParams(), "UTF-8");
                pilotChatGroupEntity.setQuery(JSONObject.parseObject(decodedBizParams).getString("query"));
            } catch (UnsupportedEncodingException e) {
                LogUtils.logFailLog(log, TagContext.builder().action("decode(chatGroupLoadReq.getBizParams(), \"UTF-8\")").build(),
                        new WarnMessage("PilotChatGroupProcessService", "欢迎卡片异常", ""), chatGroupLoadReq.getBizParams(), null);
            }
        }
    }
    
    private void processWelcomeMessage(Map<String, Object> welcomeContext, PilotChatGroupDTO pilotChatGroupDTO,
                                       List<PilotMessageDTO> welcomePilotMessageDTOS, ChatGroupLoadReq chatGroupLoadReq,
            PilotChatGroupEntity pilotChatGroupEntity) {
        if (chatGroupLoadReq.getAssistantType() == AssistantTypeEnum.HAIR_DIALOGUE_AGENT.getType()) {
            //14号助手(足疗/美发)欢迎生成逻辑
            makeHairDialogueAgentWelcome(welcomeContext, pilotChatGroupEntity, pilotChatGroupDTO, welcomePilotMessageDTOS, chatGroupLoadReq);
        }
        else {
            //默认欢迎语生成逻辑
            processDefaultWelcomeMessage(pilotChatGroupEntity, welcomePilotMessageDTOS);
        }
    }
    
    private void processDefaultWelcomeMessage(PilotChatGroupEntity pilotChatGroupEntity, List<PilotMessageDTO> welcomePilotMessageDTOS) {
        AssistantWelcomeConfigData assistantWelcomeConfig = lionConfigUtil.getAssistantWelcomeConfig(String.valueOf(pilotChatGroupEntity.getAssistantType()));
        if (StringUtils.isBlank(pilotChatGroupEntity.getQuery())) {
            welcomePilotMessageDTOS.add(buildInitialMessage(pilotChatGroupEntity, 
                    assistantWelcomeConfig != null && assistantWelcomeConfig.getWelcomeMessageType() > 0 ? 
                    assistantWelcomeConfig.getWelcomeMessageType() : MessageTypeEnum.TEXT.getValue()));
        } else {
            welcomePilotMessageDTOS.add(buildInitialMessage(pilotChatGroupEntity, MessageTypeEnum.CARD.getValue()));
        }
    }
    
    private void processSystemMessageAndQuestionStats(ChatGroupLoadReq chatGroupLoadReq, PilotChatGroupEntity pilotChatGroupEntity) {
        insertSystemMessage(chatGroupLoadReq.getPlatform(), chatGroupLoadReq.getShopId(), pilotChatGroupEntity, chatGroupLoadReq.getAssistantType());
        if (chatGroupLoadReq.getQuestionId() > 0) {
            questionScoreProcessService.addQuestionCount(chatGroupLoadReq.getQuestionId(), 0);
        }
    }

    /**
     * 生成美发/足疗多轮对话助手的欢迎语，
     * @param context
     * @param pilotChatGroupDTO
     * @param welcomePilotMessageDTOS
     * @param chatGroupLoadReq
     */
    private void makeHairDialogueAgentWelcome(Map<String, Object> context, PilotChatGroupEntity pilotChatGroupEntity,
                                              PilotChatGroupDTO pilotChatGroupDTO, List<PilotMessageDTO> welcomePilotMessageDTOS,
                                              ChatGroupLoadReq chatGroupLoadReq) {
        //通用欢迎语卡片
        welcomePilotMessageDTOS.add(buildInitialMessage(pilotChatGroupEntity, MessageTypeEnum.HAIR_WELCOME.getValue()));
        // 足疗/美发前置在入口生成过欢迎语走缓存查，先屏蔽
        if (context.containsKey("agentListPurposeData")) {
            AgentListPurposeData agentListPurposeData = (AgentListPurposeData) context.get("agentListPurposeData");
            if (!agentListPurposeData.getHasShowUser()
                    && StringUtils.isNotBlank(agentListPurposeData.getChatGuide())
                    && StringUtils.isNotBlank(agentListPurposeData.getChatHalo())) {
                String msgContent = "**" + agentListPurposeData.getChatHalo() + "** \n\n" +agentListPurposeData.getChatGuide();

                //标记为已读
                AgentListPurposeReq agentListPurposeReq = genAgentListPurposeReqFromChatGroupLoadReq(chatGroupLoadReq);
                if (agentListPurposeReq == null) {
                    return;
                }
                agentListPurposeData.setHasShowUser(true);
                listPurposeService.setAigcWelcomeContentCache(agentListPurposeReq, agentListPurposeData);

                PilotMessageDTO welcomePilotMessageDTO = new PilotMessageDTO();
                welcomePilotMessageDTO.setMessageId(IDGenerateUtil.nextMessageId());
                welcomePilotMessageDTO.setMessageType(MessageTypeEnum.TEXT.value);
                welcomePilotMessageDTO.setMessageContent(msgContent);
                welcomePilotMessageDTO.setDirection(MessageSendDirectionEnum.ASSISTANT_SEND.value);

                welcomePilotMessageDTOS.add(welcomePilotMessageDTO);
                return;
            }
        }

        if(useFixWelcome()) {
            // 缓存没有，走固定欢迎语
            makeFixHairDialogueAgentWelcome(welcomePilotMessageDTOS, chatGroupLoadReq);
        } else {
            // 缓存没有，走流式生成
            pilotChatGroupDTO.setNeedCallStream(true);
        }
    }

    /**
     * 固定欢迎语+问题
     * @param welcomePilotMessageDTOS welcomePilotMessageDTOS
     * @param chatGroupLoadReq chatGroupLoadReq
     */
    private void makeFixHairDialogueAgentWelcome(List<PilotMessageDTO> welcomePilotMessageDTOS, ChatGroupLoadReq chatGroupLoadReq) {
        log.info("makeFixedHairDialogueAgentWelcome request: {}", chatGroupLoadReq);
        EnvContext envContext = parseHairDialogueAgentBizParams(chatGroupLoadReq);
        if(envContext == null) {
            return;
        }

        POIFrontType category = checkCategoryIntent(envContext.getListType(), envContext.getBizCode(), envContext.getCategoryId(), envContext.getCurrentQuery());
        if(category == null) {
            return;
        }
        WelcomeMessageConfig welcomeMessageConfig = getWelcomeMessageConfig(category);
        if(welcomeMessageConfig == null) {
            return;
        }

        PilotMessageDTO welcomePilotMessageDTO = new PilotMessageDTO();
        welcomePilotMessageDTO.setMessageId(IDGenerateUtil.nextMessageId());
        welcomePilotMessageDTO.setMessageType(MessageTypeEnum.CARD.value);
        welcomePilotMessageDTO.setMessageContent(buildWelcomeGuessAskQuestions(welcomeMessageConfig));
        welcomePilotMessageDTO.setDirection(MessageSendDirectionEnum.ASSISTANT_SEND.value);

        welcomePilotMessageDTOS.add(welcomePilotMessageDTO);
    }

    private WelcomeMessageConfig getWelcomeMessageConfig(POIFrontType category){
        if(category == null) {
            return null;
        }
        if(MapUtils.isEmpty(assistantWelcomeQuestionConfig.getWelcomeMessage())) {
            return null;
        }

        return assistantWelcomeQuestionConfig.getWelcomeMessage().get(String.valueOf(category.getSecondFrontCategoryId()));
    }



    private String buildWelcomeGuessAskQuestions(WelcomeMessageConfig welcomeMessageConfig) {
        List<StreamEventVO> streamEventVOS = Lists.newArrayList();

        StreamEventVO streamEventVO = new StreamEventVO();
        streamEventVO.setType(StreamEventTypeEnum.MESSAGE.getType());

        String key = UuidUtil.getRandomCardKey();
        StreamEventDataVO streamEventDataVO = new StreamEventDataVO();
        streamEventDataVO.setEvent(StreamEventDataTypeEnum.MAIN_TEXT.getType());
        streamEventDataVO.setContent(":::{<WelcomeGuessAskQuestions>" + key +"</WelcomeGuessAskQuestions>}:::");

        List<StreamEventCardDataVO> cardDatas = Lists.newArrayList();
        StreamEventCardDataVO streamEventCardDataVO = new StreamEventCardDataVO();
        streamEventCardDataVO.setType(StreamEventCardTypeEnum.WELCOME_GUESS_ASK_QUESTIONS.getType());
        streamEventCardDataVO.setKey(key);
        Map<String, Object> cardProps = Maps.newHashMap();
        cardProps.put("title", welcomeMessageConfig.getWelcomeMessage());
        cardProps.put("questions", welcomeMessageConfig.getWelcomeQuestions());
        streamEventCardDataVO.setCardProps(cardProps);
        cardDatas.add(streamEventCardDataVO);

        streamEventDataVO.setCardsData(cardDatas);
        streamEventVO.setData(streamEventDataVO);
        streamEventVOS.add(streamEventVO);
        return JsonCodec.encodeWithUTF8(streamEventVOS);
    }

    /**
     * 判断欢迎语的商户类目意图（到后台二级类目）
     * @param listType listType
     * @param categoryId categoryId
     * @param currentQuery currentQuery
     * @param bizCode bizCode
     * @return PoiFrontType
     */
    @Override
    public POIFrontType checkCategoryIntent(String listType, String bizCode, Long categoryId, String currentQuery) {
        try {
            //频道页、listing页，根据类目判断
            if (Objects.equals(listType, AgentListTypeEnum.CHANNEL.getName())) {
                return checkChannelListCategory(bizCode, categoryId);
            }
            else if (Objects.equals(listType, AgentListTypeEnum.SEARCH.getName())) {
                //垂搜页，根据搜索词类目判断
                return checkSearchCategory(currentQuery);
            }
            return null;
        } catch (Exception e) {
            Cat.logError(e);
            log.error("checkCategoryIntent error, listType: {}, categoryId: {}, bizCode: {}, currentQuery:{}, error : {} ",
                    listType, categoryId, bizCode, currentQuery, e);
            return null;
        }
    }

    private POIFrontType checkChannelListCategory(String bizCode, Long categoryId) {
        if (StringUtils.isBlank(bizCode) && categoryId <= 0) {
            return null;
        }
        POIFrontType cate = null;

        // 频道
        if(StringUtils.isNotEmpty(bizCode) && MapUtils.isNotEmpty(assistantWelcomeQuestionConfig.getBizCode())) {
            cate = POIFrontType.getBySecondFrontCategoryId(assistantWelcomeQuestionConfig.getBizCode().get(bizCode));
        }
        if(cate != null) {
            return cate;
        }

        // 列表
        if(categoryId > 0 && MapUtils.isNotEmpty(assistantWelcomeQuestionConfig.getCategory())) {
            cate = POIFrontType.getBySecondFrontCategoryId(assistantWelcomeQuestionConfig.getCategory().get(categoryId + ""));
        }
        return cate;
    }

    private POIFrontType checkSearchCategory(String currentQuery) {
        if (StringUtils.isBlank(currentQuery)) {
            return null;
        }
        try {
            // 缓存查询
            String queryCateJson = redisStoreClient.get(RedisKeys.getAgentSearchEntranceKeywordKey(currentQuery));
            SearchQueryCateBean queryCate = JsonCodec.decode(queryCateJson, SearchQueryCateBean.class);
            if(queryCate == null) {
                return null;
            }
            return POIFrontType.getBySecondFrontCategoryName(queryCate.getCat1());
        } catch (Exception e) {
          Cat.logError(e);
            return null;
        }
    }
    
    
    private EnvContext parseHairDialogueAgentBizParams(ChatGroupLoadReq chatGroupLoadReq) {
        try {
            String bizParams = chatGroupLoadReq.getBizParams();
            return JsonCodec.decode(bizParams, EnvContext.class);
        } catch (Exception e) {
            Cat.logError(e);
            log.error("parseHairDialogueAgentBizParams error, chatGroupLoadReq: {}", chatGroupLoadReq);
            return null;
        }
    }


    private void insertSystemMessage(int platform, long shopId, PilotChatGroupEntity pilotChatGroupEntity, int assistantType) {
        if (!lionConfigUtil.getBeautyAskAssistantIds().contains(assistantType) || shopId <= 0) {
            return;
        }
        long mtShopId = getMtShopId(shopId, platform);
        long messageId = IDGenerateUtil.nextMessageId();
        String messageTemplateType = "用户当前正在咨询 门店id=%s 的门店, 如果后续有以任何形式提示其他门店id,则忽略该条消息";
        //插入模型消息
        PilotChatMessageEntity replyMessageEntity = buildPilotChatMessageEntity(messageId, MessageTypeEnum.TEXT.value,
                pilotChatGroupEntity.getId(), pilotChatGroupEntity.getAssistantType(), pilotChatGroupEntity.getUserId(),
                MessageSendDirectionEnum.SYSTEM_SEND.value, AuditStatusEnum.PASS.getCode(),
                String.format(messageTemplateType, mtShopId), AssistantConstant.SYSTEM, StringUtils.EMPTY);
        pilotChatGroupMessageDAO.insertMessage(replyMessageEntity);
    }


    private long getMtShopId(long shopId, int platform) {
        if (shopId <= 0) {
            return 0;
        }
        if (platform == PlatformTypeEnum.DP.code) {
            return shopAclService.loadMTShopIdByDPShopId(shopId);
        }
        return shopId;
    }

    private PilotChatMessageEntity buildPilotChatMessageEntity(long messageId, int messageType, long chatGroupId, int assistantType,
                                                               String userId, int direction, int auditStatus, String message,
                                                               String creator, String extra) {
        PilotChatMessageEntity messageEntity = new PilotChatMessageEntity();
        messageEntity.setMessageId(messageId);
        messageEntity.setMessageType(messageType);
        messageEntity.setChatGroupId(chatGroupId);
        messageEntity.setAssistantType(assistantType);
        messageEntity.setUserId(userId);
        messageEntity.setDirection(direction);
        messageEntity.setAuditStatus(auditStatus);
        messageEntity.setExtraData(extra);
        messageEntity.setMessage(message);
        messageEntity.setCreator(creator);
        return messageEntity;
    }


    private void updateLastMessageId(PilotChatGroupEntity pilotChatGroupEntity, List<PilotMessageDTO> welcomePilotMessageDTOS) {
        pilotChatGroupEntity.setLastMessageId(welcomePilotMessageDTOS.get(welcomePilotMessageDTOS.size() - 1).getMessageId());
        int affectRows = pilotChatGroupDAO.updateChatGroup(pilotChatGroupEntity);
        Assert.isTrue(affectRows > 0, "更新lastMessageId失败");
    }

    private boolean shouldSendWelcomeMessage(Map<String, Object> welcomeContext, PilotChatMessageEntity messageEntity, ChatGroupLoadReq chatGroupLoadReq, String query) {
        int assistantType = chatGroupLoadReq.getAssistantType();
        String preAsk = chatGroupLoadReq.getPreAsk();
        if (lionConfigUtil.getBeautyAskAssistantIds().contains(assistantType) && StringUtils.isNotEmpty(preAsk)) {
            return false;
        }
        // 若要请求更多问题则需要发送欢迎语
        if (StringUtils.isNotEmpty(query)) {
            return true;
        }

        //足疗美发预约agent
        if (assistantType == AssistantTypeEnum.HAIR_DIALOGUE_AGENT.getType() && checkHairDialogueAgentNeedWelcome(welcomeContext, chatGroupLoadReq)) {
            return true;
        }

        Integer interval = lionConfigUtil.getAssistantType2WelcomeInterval().getOrDefault(assistantType, lionConfigUtil.getSendWelcomeIntervalMinute());
        return messageEntity == null || !TimeUtil.isWithinLimitMinutesFromNow(messageEntity.getAddTime(), interval);
    }

    /**
     * 足疗美发预约agent
     * 如果入口浮球大模型生成引导语内容不为空，且没有给用户发过，需要发送欢迎语
     * 否则发送固定欢迎语
     * @param welcomeContext
     * @param chatGroupLoadReq
     * @return
     */
    private boolean checkHairDialogueAgentNeedWelcome(Map<String, Object> welcomeContext, ChatGroupLoadReq chatGroupLoadReq) {
        AgentListPurposeData agentListPurposeData = getWelcomeCacheSend(chatGroupLoadReq);
        if (isValidAgentListPurposeData(agentListPurposeData)) {
            welcomeContext.put("agentListPurposeData", agentListPurposeData);
            boolean shouldSendWelcomeMessage = shouldSendPreGenWelcomeMessage(agentListPurposeData);
            if(shouldSendWelcomeMessage) {
                return true;
            }
        }

        // 如果是固定欢迎语每次进入都发
        if(useFixWelcome()) {
            return true;
        }
        return false;
    }

    /**
     * 检查AgentListPurposeData是否有效
     * @param agentListPurposeData
     * @return
     */
    private boolean isValidAgentListPurposeData(AgentListPurposeData agentListPurposeData) {
        return agentListPurposeData != null
                && agentListPurposeData.getCategoryId() != null
                && agentListPurposeData.getHasShowUser() != null
                && agentListPurposeData.getChatGuide() != null
                && agentListPurposeData.getChatHalo() != null;
    }
    
    /**
     * 判断是否应该发送欢迎消息（大模型预生成在缓存中的欢迎语）
     * @param agentListPurposeData
     * @return
     */
    private boolean shouldSendPreGenWelcomeMessage(AgentListPurposeData agentListPurposeData) {
        return !agentListPurposeData.getHasShowUser()
                && StringUtils.isNotBlank(agentListPurposeData.getChatGuide())
                && StringUtils.isNotBlank(agentListPurposeData.getChatHalo());
    }

    /**
     * 获取欢迎语缓存
     * @param chatGroupLoadReq
     * @return
     */
    private AgentListPurposeData getWelcomeCacheSend(ChatGroupLoadReq chatGroupLoadReq) {
        AgentListPurposeReq agentListPurposeReq = genAgentListPurposeReqFromChatGroupLoadReq(chatGroupLoadReq);
        if (agentListPurposeReq != null) {
            return listPurposeService.getAigcWelcomeContentCache(agentListPurposeReq);
        }

        return new AgentListPurposeData();
    }

    /**
     * 生成欢迎语缓存请求，填充下必要的Redis Key
     * @param chatGroupLoadReq
     * @return
     */
    private AgentListPurposeReq genAgentListPurposeReqFromChatGroupLoadReq(ChatGroupLoadReq chatGroupLoadReq) {
        if (StringUtils.isNotBlank(chatGroupLoadReq.getBizParams())) {
            try {
                String decodedBizParams = URLDecoder.decode(chatGroupLoadReq.getBizParams(), "UTF-8");
                String listType = JSONObject.parseObject(decodedBizParams).getString("listType");
                Long categoryId = JSONObject.parseObject(decodedBizParams).getLong("categoryId");
                String bizCode = JSONObject.parseObject(decodedBizParams).getString("bizCode");
                String currentQuery = JSONObject.parseObject(decodedBizParams).getString("currentQuery");

                POIFrontType poiFrontType = checkCategoryIntent(listType, bizCode, categoryId, currentQuery);
                if (poiFrontType != null && poiFrontType.getSecondFrontCategoryId() > 0) {
                    categoryId = (long) poiFrontType.getSecondFrontCategoryId();
                }

                return AgentListPurposeReq.builder()
                        .platform(chatGroupLoadReq.getPlatform())
                        .imUserId(chatGroupLoadReq.getImUserId())
                        .categoryId(categoryId)
                        .listType(listType).build();
            } catch (Exception e) {
                LogUtils.logFailLog(log, TagContext.builder().action("genAgentListPurposeReqFromChatGroupLoadReq, \"UTF-8\")").build(),
                        new WarnMessage("genAgentListPurposeReqFromChatGroupLoadReq", "欢迎卡片异常", ""), chatGroupLoadReq.getBizParams(), null);
                return null;
            }
        }
        return null;
    }

    @Override
    public PilotChatGroupEntity getOrCreateChatGroup(String imUserId, int assistantType) {
        PilotChatGroupEntity pilotChatGroupEntity = pilotChatGroupDAO.queryChatGroupByUserIdAndType(imUserId, assistantType);
        if (pilotChatGroupEntity == null) {
            // 没有会话就创建会话
            pilotChatGroupEntity = new PilotChatGroupEntity();
            pilotChatGroupEntity.setUserId(imUserId);
            pilotChatGroupEntity.setAssistantType(assistantType);
            pilotChatGroupEntity.setStartMessageId(0L);
            pilotChatGroupEntity.setLastMessageId(0L);
            int insertNum = pilotChatGroupDAO.insertChatGroup(pilotChatGroupEntity);
            if (insertNum <= 0) {
                return null;
            }
        }
        return pilotChatGroupEntity;
    }

    public List<ChatGroupVO> buildChatGroupVO(String userId, List<PilotChatGroupEntity> pilotChatGroupEntities, List<PilotChatMessageEntity> pilotChatMessageEntities) {
        List<ChatGroupVO> chatGroupVOList = new ArrayList<>();

        Map<Long, PilotChatMessageEntity> messageIdChatMessageEntityMap = pilotChatMessageEntities.stream().collect(Collectors.toMap(PilotChatMessageEntity::getMessageId, Function.identity()));

        //获得lion上的assistants
        List<AssistantData> assistants = assistantLoadUtils.getAssistantListFromLion();
        Map<Integer, AssistantData> assistantTypeMap = assistants.stream().collect(Collectors.toMap(AssistantData::getAssistantType, Function.identity()));

        for (PilotChatGroupEntity pilotChatGroupEntity : pilotChatGroupEntities) {
            AssistantTypeEnum assistantTypeEnum = AssistantTypeEnum.getByType(pilotChatGroupEntity.getAssistantType());
            if (assistantTypeEnum != null && assistantTypeMap.keySet().contains(assistantTypeEnum.getType())) {
                ChatGroupVO chatGroupVO = new ChatGroupVO();
                chatGroupVO.setIcon(assistantTypeMap.get(assistantTypeEnum.getType()).getIcon());
                chatGroupVO.setUserId(userId);
                chatGroupVO.setUpdateTime(DateUtils.covertDateStr(pilotChatGroupEntity.getUpdateTime()));
                chatGroupVO.setChatGroupId(pilotChatGroupEntity.getId());
                if (messageIdChatMessageEntityMap.get(pilotChatGroupEntity.getStartMessageId()) != null) {
                    chatGroupVO.setMessageBrief(CollectionUtils.isEmpty(pilotChatMessageEntities) ? "" :
                            (messageIdChatMessageEntityMap.get(pilotChatGroupEntity.getStartMessageId()) == null ? "" : messageIdChatMessageEntityMap.get(pilotChatGroupEntity.getStartMessageId()).getMessage()));
                }
                chatGroupVO.setAssistantName(assistantTypeMap.get(assistantTypeEnum.getType()).getName());
                chatGroupVO.setAssistantType(assistantTypeEnum.getType());
                chatGroupVOList.add(chatGroupVO);
            }
        }
        return chatGroupVOList;
    }

    public PageVO buildPageVo(String userId, int pageNum, int pageSize) {
        PageVO pageVO = new PageVO();

        int total = pilotChatGroupDAO.queryChatGroupCntByUserId(userId);

        pageVO.setPageNo(pageNum);
        pageVO.setPageSize(pageSize);
        pageVO.setTotal(total);
        return pageVO;
    }

    public InnerAssistantChatGroupVO getChatGroupData(InnerAssistantChatGroupVO innerAssistantChatGoupData, PilotChatGroupEntity pilotChatGroupEntity, String ssoUserId) {
        //验证userid是不是能对上
        if (pilotChatGroupEntity == null || !pilotChatGroupEntity.getUserId().equals(ssoUserId)) {
            return null;
        }
        innerAssistantChatGoupData.setChatGroupId(pilotChatGroupEntity.getId());
        innerAssistantChatGoupData.setAssistantType(pilotChatGroupEntity.getAssistantType());
        innerAssistantChatGoupData.setUserId(pilotChatGroupEntity.getUserId());
        return innerAssistantChatGoupData;
    }

    private PilotMessageDTO buildInitialMessage(PilotChatGroupEntity chatGroupEntity, Integer messageType) {
        PilotChatMessageEntity messageEntity = new PilotChatMessageEntity();
        messageEntity.setMessageId(IDGenerateUtil.nextMessageId());
        messageEntity.setUserId(chatGroupEntity.getUserId());
        messageEntity.setChatGroupId(chatGroupEntity.getId());
        messageEntity.setAssistantType(chatGroupEntity.getAssistantType());
        if (messageType == MessageTypeEnum.TEXT.getValue() || messageType == MessageTypeEnum.HAIR_WELCOME.getValue()) {
            messageEntity.setMessage(buildTextMessage(chatGroupEntity));
        } else if (messageType == MessageTypeEnum.TIPS.getValue()) {
            messageEntity.setMessage(buildTipsMessage(chatGroupEntity));
        } else if (messageType == MessageTypeEnum.CARD.getValue()) {
            messageEntity.setMessage(buildWelcomeCard(chatGroupEntity));
        }
        messageEntity.setMessageType(messageType);
        messageEntity.setDirection(MessageSendDirectionEnum.ASSISTANT_SEND.getValue());
        messageEntity.setCreator(AssistantConstant.ASSISTANT_CREATOR);
        messageEntity.setAuditStatus(AuditStatusEnum.PASS.getCode());
        messageEntity.setExtraData("");

        PilotMessageDTO welcomePilotMessageDTO = new PilotMessageDTO();
        welcomePilotMessageDTO.setMessageId(messageEntity.getMessageId());
        welcomePilotMessageDTO.setMessageType(messageEntity.getMessageType());
        welcomePilotMessageDTO.setMessageContent(messageEntity.getMessage());
        welcomePilotMessageDTO.setDirection(MessageSendDirectionEnum.ASSISTANT_SEND.value);
        return welcomePilotMessageDTO;
    }

    private String buildTextMessage(PilotChatGroupEntity chatGroupEntity) {
        return lionConfigUtil.getAssistantWelcomeConfig(String.valueOf(chatGroupEntity.getAssistantType())) == null ? "" : lionConfigUtil.getAssistantWelcomeConfig(String.valueOf(chatGroupEntity.getAssistantType())).getWelcomeMessage();
    }

    private String buildTipsMessage(PilotChatGroupEntity chatGroupEntity) {
        return lionConfigUtil.getAssistantWelcomeConfig(String.valueOf(chatGroupEntity.getAssistantType())) == null ? "" : lionConfigUtil.getAssistantWelcomeConfig(String.valueOf(chatGroupEntity.getAssistantType())).getTips();
    }

    private String buildWelcomeCard(PilotChatGroupEntity chatGroupEntity) {
        StringBuilder welcomeMessage = new StringBuilder(lionConfigUtil.getAssistantWelcomeConfig(String.valueOf(chatGroupEntity.getAssistantType())).getWelcomeMessage());
        List<StreamEventVO> streamEventVOS = new ArrayList<>();

        StreamEventVO streamEventVO = new StreamEventVO();
        streamEventVO.setType(StreamEventTypeEnum.MESSAGE.getType());

        StreamEventDataVO streamEventDataVO = new StreamEventDataVO();
        streamEventDataVO.setEvent(StreamEventDataTypeEnum.MAIN_TEXT.getType());
        streamEventDataVO.setContent(welcomeMessage.toString());
        streamEventVO.setData(streamEventDataVO);
        streamEventVOS.add(streamEventVO);

        try {
            boolean hasQuestion = false;
            List<String> cardQuestions = new ArrayList<>();
            // 根据query随机加3个下挂问题
            if (chatGroupEntity.getQuery() != null) {
                Map<Integer, Integer> assistantType2BizType = lionConfigUtil.getAssistantType2BizType();
                SuggestionConfig suggestionConfig = getSuggestionConfig(assistantType2BizType.get(chatGroupEntity.getAssistantType()));
                if (suggestionConfig != null) {
                    List<String> tempCardQuestions = setCardQuestions(chatGroupEntity, suggestionConfig);
                    if (!CollectionUtils.isEmpty(tempCardQuestions)) {
                        hasQuestion = true;
                        if (tempCardQuestions.size() > 3) {
                            Collections.shuffle(tempCardQuestions);
                            tempCardQuestions = tempCardQuestions.stream().limit(3).collect(Collectors.toList());
                        }
                        cardQuestions = convertCardQuestions(tempCardQuestions);
                    }
                }
            }
            if (!hasQuestion) {
                // 随机加3个下挂问题
                List<Integer> templateTypes = Lists.newArrayList(GenerativeSearchAnswerTemplateTypeEnum.TEXT_WITH_CONTENT_FILTER_OPTIONAL.getType(), GenerativeSearchAnswerTemplateTypeEnum.TEXT_WITH_CUSTOMIZATION_FILTER.getType(), 0, -1);
                List<GenerativeSearchWordE> generativeSearchWordEntities = generativeSearchDomainService.batchFindByAssistantAndTemplate(chatGroupEntity.getAssistantType(), templateTypes);
                Collections.shuffle(generativeSearchWordEntities);
                generativeSearchWordEntities = generativeSearchWordEntities.stream().limit(3).collect(Collectors.toList());
                for (GenerativeSearchWordE entity : generativeSearchWordEntities) {
                    String question = entity.getQuestion();
                    cardQuestions.add(question);
                }
                if (CollectionUtils.isEmpty(cardQuestions)) {
                    // 搜不到问题就拿用户的搜索内容构造问题
                    cardQuestions.add(chatGroupEntity.getQuery());
                }
            }

            int index = 1;
            List<StreamEventCardDataVO> cards = Lists.newArrayList();
            for (String question : cardQuestions) {
                welcomeMessage.append(":::{<GuessAskQuestion>").append(index).append("</GuessAskQuestion>}:::");

                StreamEventCardDataVO streamEventCardDataVO = new StreamEventCardDataVO();
                streamEventCardDataVO.setType(StreamEventCardTypeEnum.RELATED_QUESTION_CARD.getType());
                streamEventCardDataVO.setKey(String.valueOf(index));

                Map<String, Object> cardProps = Maps.newHashMap();
                cardProps.put("question", question);
                streamEventCardDataVO.setCardProps(cardProps);
                cards.add(streamEventCardDataVO);
                index++;
            }
            streamEventDataVO.setContent(welcomeMessage.toString());
            streamEventDataVO.setCardsData(cards);
        } catch (Exception e) {
            log.error("buildWelcomeCard error, chatGroupEntity: {}", chatGroupEntity, e);
        }

        return JsonCodec.encodeWithUTF8(streamEventVOS);
    }


    private List<String> setCardQuestions(PilotChatGroupEntity pilotChatGroupEntity, SuggestionConfig suggestionConfig) {
        FridayAppSendMessageReq fridayAppReq = buildFridayAppSendMessageReq(pilotChatGroupEntity, suggestionConfig);
        return fridayAppAclService.getSuggestion(fridayAppReq);
    }

    private SuggestionConfig getSuggestionConfig(Integer bizType) {
        if (generativeSearchSugConfig != null) {
            return generativeSearchSugConfig.get(String.valueOf(bizType));
        }
        return null;
    }

    private List<String> convertCardQuestions(List<String> tempCardQuestions) {
        // 支持"祛痘有哪些项目？##祛痘 "和"祛痘有哪些项目？"两种格式的问题提取
        for (int i = 0; i < tempCardQuestions.size(); i++) {
            String str = tempCardQuestions.get(i);
            int index = str.indexOf("#");
            if (index != -1) {
                tempCardQuestions.set(i, str.substring(0, index));
            }
        }
        return tempCardQuestions;
    }

    private FridayAppSendMessageReq buildFridayAppSendMessageReq(PilotChatGroupEntity pilotChatGroupEntity, SuggestionConfig sugConfig) {
        FridayAppSendMessageReq fridayAppSendMessageReq = new FridayAppSendMessageReq();
        fridayAppSendMessageReq.setAppId(sugConfig.getAppId());
        fridayAppSendMessageReq.setClientId(sugConfig.getClientId());
        fridayAppSendMessageReq.setClientSecret(sugConfig.getClientSecret());
        fridayAppSendMessageReq.setUserType(sugConfig.getClientType());
        fridayAppSendMessageReq.setUserId(String.valueOf(pilotChatGroupEntity.getUserId()));
        fridayAppSendMessageReq.setQuery(pilotChatGroupEntity.getQuery());
        fridayAppSendMessageReq.setDatasetIds(sugConfig.getDatasetIds());
        fridayAppSendMessageReq.setCount(sugConfig.getSugCount());
        fridayAppSendMessageReq.setUsePhasePrefix(sugConfig.isUsePhasePrefix());
        return fridayAppSendMessageReq;
    }

    private boolean isWithinLimitMinutesFromNow(Date dateToCompare, int limitMinutes) {
        Date now = new Date();
        long difference = now.getTime() - dateToCompare.getTime();
        if (difference < 0) {
            return false;
        }
        long differenceMinutes = difference / (60 * 1000);
        return differenceMinutes <= limitMinutes;
    }

    private boolean useFixWelcome() {
        if(assistantWelcomeQuestionConfig.getUseFixWelcome() == null) {
            return false;
        }
        return assistantWelcomeQuestionConfig.getUseFixWelcome();
    }

    @Data
    private static class AssistantWelcomeQuestionConfig {
        /**
         * 固定欢迎语开关
         */
        private Boolean useFixWelcome;
        /**
         * 列表页类目->二级类目
         */
        private Map<String, Integer> category;
        /**
         * 频道页bizCode->二级类目
         */
        private Map<String, Integer> bizCode;

        /**
         * 二级类目->欢迎语+追问
         */
        private Map<String, WelcomeMessageConfig> welcomeMessage;
    }

    @Data
    private static class WelcomeMessageConfig {
        /**
         * 欢迎语
         */
        private String welcomeMessage;
        /**
         * 欢迎追问
         */
        private List<String> welcomeQuestions;
    }

    @Data
    private static class SearchQueryCateBean {
        /**
         * 二级类目，文字
         * 如： 按摩/足疗
         */
        private String cat1;

        /**
         * 类目曝光占比
         */
        private Double ratio;
    }
}
