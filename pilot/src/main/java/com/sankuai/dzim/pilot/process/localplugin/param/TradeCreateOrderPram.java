package com.sankuai.dzim.pilot.process.localplugin.param;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import lombok.Data;

/**
 * 用户团购下单工具
 * <AUTHOR>
 */
@Data
public class TradeCreateOrderPram extends Param {
    @JsonPropertyDescription("Extracted mt shop Id id from context.")
    @JsonProperty(required = true)
    private Long shopId;

    @JsonPropertyDescription("Extracted mt product id from context")
    @JsonProperty(required = true)
    private Long productId;

    @JsonPropertyDescription("The quantity of the deal placed by the user is extracted from the user input. If the user does not mention it, the default value is 1")
    @JsonProperty(required = false)
    private Integer quantity;

    @JsonPropertyDescription("Return the store arrival time User mentioned in the context, or an empty string if not mentioned")
    @JsonProperty(required = false)
    private String arrivePoiTime;
}
