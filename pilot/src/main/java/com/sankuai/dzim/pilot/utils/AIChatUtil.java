package com.sankuai.dzim.pilot.utils;

import com.google.common.collect.Maps;
import com.sankuai.dzim.pilot.acl.HaimaAclService;
import com.sankuai.dzim.pilot.acl.data.fraiday.FridayConstant;
import com.sankuai.dzim.pilot.buffer.core.PilotBufferItemDO;
import com.sankuai.dzim.pilot.buffer.utils.BufferUtils;
import com.sankuai.dzim.pilot.chain.data.AIAnswerData;
import com.sankuai.dzim.pilot.chain.data.AIServiceContext;
import com.sankuai.dzim.pilot.chain.enums.AIServiceExtraKeyEnum;
import com.sankuai.dzim.pilot.chain.enums.AIServiceTypeEnum;
import com.sankuai.dzim.pilot.chain.impl.AdvancedRagAIService;
import com.sankuai.dzim.pilot.domain.memory.data.QueryMemoryContext;
import com.sankuai.dzim.pilot.domain.memory.impl.BeamChatMemoryProvider;
import com.sankuai.dzim.pilot.domain.message.AssistantMessage;
import com.sankuai.dzim.pilot.domain.message.Message;
import com.sankuai.dzim.pilot.domain.message.UserMessage;
import com.sankuai.dzim.pilot.gateway.api.assistant.data.SendBeamMessageReq;
import com.sankuai.dzim.pilot.process.data.AIServiceConfig;
import com.sankuai.dzim.pilot.process.localplugin.data.UserContext;
import com.sankuai.dzim.pilot.utils.data.SummaryRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/04/16 11:29
 */
@Component
@Slf4j
public class AIChatUtil {
    @Autowired
    private HaimaAclService haimaAclService;

    @Autowired
    private AdvancedRagAIService advancedRagAIService;

    @Autowired
    private LionConfigUtil lionConfigUtil;

    @Autowired
    private BeamChatMemoryProvider beamChatMemoryProvider;

    /**
     * 调用大模型
     * @param key 大模型唯一标识
     * @param input 用户输入
     * @param extraInfo 上下文信息
     * @return
     */
    public AIAnswerData chat(String key, String input, Map<String, Object> extraInfo) {
        return chat(key, input, extraInfo, null);
    }

    /**
     * Beam总结
     * @param summaryRequest
     * @param originalContext
     */
    public void summaryForBeam(SummaryRequest summaryRequest, AIServiceContext originalContext) {
        AIServiceConfig config = haimaAclService.queryAIConfig("BeamTaskSummary");
        if (config == null) {
            config = buildDefaultAIServiceConfig();
        }

        // 构建模板动态参数值映射
        SendBeamMessageReq sendBeamMessageReq = (SendBeamMessageReq) originalContext.getExtraInfo().get(AIServiceExtraKeyEnum.BEAM_REQUEST_CONTEXT.getKey());
        String summaryRequirement = getSummaryRequirement(summaryRequest.getTaskKey());
        List<Message> messageList = beamChatMemoryProvider.getMemory(QueryMemoryContext.builder().aiServiceContext(originalContext).build());
        String chatHistory = convertChatHistory(messageList);
        Map<String, String> summaryDynamicParams = buildDynamicParams(summaryRequirement, sendBeamMessageReq, summaryRequest, chatHistory);

        // 模板参数替换
        String systemPrompt = StringInnerUtils.replaceTemplate(config.getSystemPrompt(), summaryDynamicParams);
        config.setSystemPrompt(systemPrompt);

        String summaryMessageTemplate = lionConfigUtil.getBeamSummaryMessageTemplate();
        String summaryMessage = StringInnerUtils.replaceTemplate(summaryMessageTemplate, summaryDynamicParams);
        AIServiceContext context = LLMApplyUtil.buildAIServiceContext(config, summaryMessage);

        // 执行
        AIAnswerData aiAnswerData = advancedRagAIService.execute(context);

        // 写buffer
        BufferUtils.writeMainTextBuffer(PilotBufferItemDO.builder().data(aiAnswerData.getAnswer()).build());
    }

    private String convertChatHistory(List<Message> messageList) {
        if (CollectionUtils.isEmpty(messageList)) {
            return StringUtils.EMPTY;
        }

        StringBuffer sb = new StringBuffer();
        for (Message message: messageList) {
            if (message instanceof UserMessage) {
                sb.append("user:").append(message.getContent()).append("\n");
            } else if (message instanceof AssistantMessage) {
                sb.append("assistant:").append(message.getContent()).append("\n");
            }
        }
        return sb.toString();
    }

    private Map<String, String> buildDynamicParams(String summaryRequirements, SendBeamMessageReq sendBeamMessageReq, SummaryRequest summaryRequest, String chatHistory) {
        Map<String, String> dynamicParams = Maps.newHashMap();
        dynamicParams.put("summaryRequirements", StringUtils.defaultString(summaryRequirements));
        dynamicParams.put("taskName", StringUtils.defaultString(summaryRequest.getTaskName()));
        dynamicParams.put("taskDesc", StringUtils.defaultString(summaryRequest.getTaskDesc()));
        dynamicParams.put("taskResult", StringUtils.defaultString(summaryRequest.getTaskResult()));
        if (sendBeamMessageReq != null) {
            dynamicParams.put("userInput", StringUtils.defaultString(sendBeamMessageReq.getOriginal_user_input().getContent()));
            dynamicParams.put("userBehavior", StringUtils.defaultString(sendBeamMessageReq.getContext().getUser_last_n_behavior()));
            dynamicParams.put("userProfile", StringUtils.defaultString(sendBeamMessageReq.getContext().getUser_profile()));
            dynamicParams.put("fixedKnowledge", StringUtils.defaultString(sendBeamMessageReq.getContext().getFixed_knowledge()));
            dynamicParams.put("queryUnderstanding", StringUtils.defaultString(sendBeamMessageReq.getContext().getQuery_understanding()));
            dynamicParams.put("userUnderstanding", StringUtils.defaultString(sendBeamMessageReq.getContext().getUser_understanding()));
            dynamicParams.put("chatHistory", StringUtils.defaultString(chatHistory));
        }
        return dynamicParams;
    }

    private String getSummaryRequirement(String taskKey) {
        Map<String, String> beamSummaryRequirementsConfig = lionConfigUtil.getBeamSummaryRequirementsConfig();
        if (MapUtils.isEmpty(beamSummaryRequirementsConfig)) {
            return StringUtils.EMPTY;
        }

        if (beamSummaryRequirementsConfig.containsKey(taskKey)) {
            return beamSummaryRequirementsConfig.get(taskKey);
        }
        return beamSummaryRequirementsConfig.getOrDefault("default", "");
    }

    public AIAnswerData chat(String key, String input, Map<String, Object> extraInfo, String dynamicPrompt) {
        AIServiceConfig config = haimaAclService.queryAIConfig(key);
        if (config == null) {
            config = buildDefaultAIServiceConfig();
        }
        AIServiceContext context = LLMApplyUtil.buildAIServiceContext(config, input);
        if (extraInfo != null) {
            UserContext userContext = PluginContextUtil.parseUserContext(extraInfo.get(AIServiceExtraKeyEnum.USER_CONTEXT.getKey()));
            if (userContext != null && userContext.getChatGroupId() > 0) {
                context.getMessageDTO().setChatGroupId((int) userContext.getChatGroupId());
            }
            if (StringUtils.isNotBlank(dynamicPrompt)) {
                String systemPrompt = context.getSystemPrompt();
                context.setSystemPrompt(systemPrompt + dynamicPrompt);
            }
            context.setExtraInfo(extraInfo);
        }
        AIAnswerData answerData = advancedRagAIService.execute(context);
        log.info("{}执行结果:{}, context={}", key, answerData, context);
        return answerData;
    }

    /**
     * 默认AI模型配置
     * @return
     */
    public AIServiceConfig buildDefaultAIServiceConfig() {
        AIServiceConfig config = new AIServiceConfig();
        config.setAiServiceType(AIServiceTypeEnum.ADVANCED_RAG.getType());
        config.setModel("gpt-4o-mini");
        config.setAppId(FridayConstant.DEFAULT_APP_ID);
        config.setTemperature(0);
        config.setTopP(1);
        config.setIsJsonModel(false);
        config.setStream(false);
        config.setStartStatus("回答生成中，请稍等");
        config.setEndStatus("基于平台内高质量数据为您回答");
        config.setMaxTokens(4906);
        return config;
    }

    public static void writeToBuffer(AIAnswerData answerData) {
        BufferUtils.writeMainTextBuffer(PilotBufferItemDO.builder().data(answerData.getAnswer()).build());
    }
}
