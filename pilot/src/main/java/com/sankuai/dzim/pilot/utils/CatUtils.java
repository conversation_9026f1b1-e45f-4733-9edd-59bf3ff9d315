package com.sankuai.dzim.pilot.utils;

import com.dianping.cat.Cat;
import com.google.common.collect.Maps;

import java.util.Map;

public class CatUtils {

    public static final String GENERATIVE_SEARCH = "GenerativeSearch";

    public static final String NO_ANSWER = "NoAnswer";

    public static final String NO_ANSWER_WITH_EXTEND = "NoAnswerWithExtend";

    public static final String MISS_CONTENT = "MissContent";

    public static final String PLUGIN_EXECUTE_CNT_PER_REQ = "PluginExecuteCntPerReq";

    public static final String TRANSACTION_METHOD = "Method";

    public static void catMethodTransaction(String methodName, long startTime) {
        long delta = System.currentTimeMillis() - startTime;
        Cat.newCompletedTransactionWithDuration(TRANSACTION_METHOD, methodName, delta);
    }

    public static void logLLMAnswerMetricReq(String name, int sceneType) {
        Map<String, String> tags = Maps.newHashMap();
        tags.put("sceneType", String.valueOf(sceneType));
        Cat.logMetricForCount(name + "Req", tags);
    }

    public static void logLLMAnswerMetricHasData(String name, int sceneType) {
        Map<String, String> tags = Maps.newHashMap();
        tags.put("sceneType", String.valueOf(sceneType));
        Cat.logMetricForCount(name + "HasData", tags);
    }

    public static void logLLMAnswerMetricSucc(String name, int sceneType) {
        Map<String, String> tags = Maps.newHashMap();
        tags.put("sceneType", String.valueOf(sceneType));
        Cat.logMetricForCount(name + "Succ", tags);
    }

    public static void logMetricReq(String name, Map<String, String> tags) {
        Cat.logMetricForCount(name + "Req", tags);
    }

    public static void logMetricSucc(String name, Map<String, String> tags) {
        Cat.logMetricForCount(name + "Succ", tags);
    }

    public static void logMetricResult(String name, Map<String, String> tags) {
        Cat.logMetricForCount(name + "Result", tags);
    }

    public static void logMetricFail(String name, Map<String, String> tags) {
        Cat.logMetricForCount(name + "Fail", tags);
    }

    public static void logMetricHasData(String name, Map<String, String> tags) {
        Cat.logMetricForCount(name + "HasData", tags);
    }


}
