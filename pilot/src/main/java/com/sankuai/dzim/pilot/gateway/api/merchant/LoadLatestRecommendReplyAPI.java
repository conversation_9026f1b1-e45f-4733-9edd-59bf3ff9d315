package com.sankuai.dzim.pilot.gateway.api.merchant;

import com.dianping.gateway.enums.APIModeEnum;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.dianping.vc.web.api.API;
import com.dianping.vc.web.api.URL;
import com.dianping.vc.web.biz.merchant.api.ShopAccountIdAware;
import com.sankuai.dzim.pilot.api.data.AIAnswerTypeEnum;
import com.sankuai.dzim.pilot.domain.data.RecommendReplyRecordE;
import com.sankuai.dzim.pilot.gateway.api.vo.RecommendReplyVO;
import com.sankuai.dzim.pilot.process.IntelligentCustomerProcessService;
import com.sankuai.dzim.pilot.process.data.LoadMessageLatestRecReplyReq;
import com.sankuai.dzim.pilot.process.data.RecommendRecordData;
import com.sankuai.dzim.pilot.process.localplugin.data.ProductRecommendResp;
import com.sankuai.dzim.pilot.utils.data.Response;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Setter
@Slf4j
@Service
@URL(url = "/dzim/pilot/merchant/recommend/reply/load", mode = APIModeEnum.MERCHANT)
public class LoadLatestRecommendReplyAPI implements API, ShopAccountIdAware {

    private int shopAccountId;

    /**
     * im商家账号id，此处为im门店id，如s1234
     */
    private String immerchantid;

    /**
     * im用户id，此处为im用户id，如im点评用户id，u1234，美团用户id，m1234
     */
    private String imuserid;

    /**
     * 客户端类型
     */
    private int clienttype;

    /**
     * 用户最新一条消息的id
     */
    private long userlastmessageid;

    @Autowired
    private IntelligentCustomerProcessService intelligentCustomerProcessService;

    @Override
    public Object execute() {
        LoadMessageLatestRecReplyReq request = buildLoadMessageLatestRecReplyReq();
        Response<RecommendRecordData> response = intelligentCustomerProcessService.loadMessageLatestRecommendReply(request);
        if (!response.isSuccess() || response.getData() == null) {
            return new RecommendReplyVO();
        }
        return buildRecommendReplyVO(response.getData());
    }

    private RecommendReplyVO buildRecommendReplyVO(RecommendRecordData recommendRecordData) {
        RecommendReplyRecordE recommendReplyRecordE = recommendRecordData.getRecommendReplyRecordE();
        Map<String, Map<String, String>> productId2MessageContentMap = recommendRecordData.getProductId2MessageContentMap();

        RecommendReplyVO recommendReplyVO = new RecommendReplyVO();
        if (recommendReplyRecordE.getRecommendType() == AIAnswerTypeEnum.TEXT.getType()) {
            recommendReplyVO.setText(recommendReplyRecordE.getContent());
            return recommendReplyVO;
        }
        if (recommendReplyRecordE.getRecommendType() == AIAnswerTypeEnum.PRODUCT_RECOMMEND.getType()) {
            ProductRecommendResp productRecommendResp = JsonCodec.decode(recommendReplyRecordE.getContent(), ProductRecommendResp.class);
            if (productRecommendResp == null) {
                return recommendReplyVO;
            }
            String productId = productRecommendResp.getProductId();
            recommendReplyVO.setText(productRecommendResp.getRecommendDesc());
            recommendReplyVO.setProduct(productId2MessageContentMap.get(productId));
            return recommendReplyVO;
        }
        return recommendReplyVO;
    }

    private LoadMessageLatestRecReplyReq buildLoadMessageLatestRecReplyReq() {
        LoadMessageLatestRecReplyReq req = new LoadMessageLatestRecReplyReq();
        req.setImMerchantId(immerchantid);
        req.setImUserId(imuserid);
        req.setShopAccountId(shopAccountId);
        req.setUserLastMessageId(userlastmessageid);
        req.setClientType(clienttype);
        return req;
    }

    @Override
    public void setShopAccountId(int shopAccountId) {
        this.shopAccountId = shopAccountId;
    }
}