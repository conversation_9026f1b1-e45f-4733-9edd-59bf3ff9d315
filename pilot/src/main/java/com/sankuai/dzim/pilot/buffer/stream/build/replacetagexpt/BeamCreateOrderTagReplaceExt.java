package com.sankuai.dzim.pilot.buffer.stream.build.replacetagexpt;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.dzim.pilot.buffer.enums.StreamEventCardTypeEnum;
import com.sankuai.dzim.pilot.buffer.stream.build.replacetagexpt.data.ReplaceContext;
import com.sankuai.dzim.pilot.enums.BeamResourceTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/5/12
 */
@Component
@Slf4j
public class BeamCreateOrderTagReplaceExt implements ReplaceTagExt {

    @Override
    public boolean accept(String tag) {
        return StreamEventCardTypeEnum.BEAM_DEAL_ORDER_CREATE.getType().equals(tag);
    }

    @Override
    public String replace(ReplaceContext replaceContext) {
        if (StringUtils.isBlank(replaceContext.getTag().getValue())) {
            return null;
        }
        String type = BeamResourceTypeEnum.FWLS_DEAL_CREATE_ORDER.getKey();

        JSONObject jsonObject = JSONObject.parseObject(replaceContext.getTag().getValue());
        Long index = jsonObject.getLong("orderId");
        String firstTag = "<card " + "type=" + type+" index="+index+" hidden=1>";
        String endTag = "</card>";
        return firstTag + replaceContext.getTag().getValue() + endTag;
    }
}
