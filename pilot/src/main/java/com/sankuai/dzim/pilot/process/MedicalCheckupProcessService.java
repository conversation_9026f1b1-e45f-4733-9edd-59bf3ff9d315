package com.sankuai.dzim.pilot.process;

import com.sankuai.dzim.pilot.gateway.api.vo.medical.MedicalCheckupReportAnalysisVO;
import com.sankuai.dzim.pilot.gateway.api.vo.medical.MedicalCheckupReportFileVO;
import com.sankuai.dzim.pilot.gateway.api.vo.medical.MedicalCheckupReportStatusVO;
import com.sankuai.dzim.pilot.gateway.api.vo.medical.MedicalCheckupReportHistoryListVO;
import com.sankuai.dzim.pilot.process.data.medical.MedicalCheckupIndicatorData;

/**
 * @author: zhouyibing
 * @date: 2024/9/6
 */
public interface MedicalCheckupProcessService {

    MedicalCheckupReportStatusVO queryCheckupReportStatus(String imUserId, long recordId);

    boolean operateCheckupReport(String imUserId, long recordId, int operateType);

    MedicalCheckupReportHistoryListVO queryCheckupReportHistory(String imUserId, int pageNo, int pageSize, int platform);

    MedicalCheckupReportStatusVO uploadCheckupReport(String imUserId, String reportUrl);

    MedicalCheckupReportFileVO queryCheckupReportFile(String imUserId, long recordId);

    MedicalCheckupReportAnalysisVO queryCheckupReportAnalysis(String imUserId, long recordId, int platform);

    MedicalCheckupIndicatorData queryCheckupIndicatorData(long recordId, String disease);
}
