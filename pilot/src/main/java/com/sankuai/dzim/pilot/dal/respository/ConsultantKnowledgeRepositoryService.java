package com.sankuai.dzim.pilot.dal.respository;

import com.sankuai.dzim.pilot.api.enums.consultant.KnowledgeBaseTypeEnum;
import com.sankuai.dzim.pilot.dal.entity.pilot.ConsultantKnowledgeEntity;

import java.util.List;

public interface ConsultantKnowledgeRepositoryService {

    /**
     * 添加知识
     *
     * @param entity 顾问知识entity
     * @return 是否添加成功
     */
    boolean addKnowledge(ConsultantKnowledgeEntity entity);

    /**
     * 获取指定顾问id下相似的知识（Vex）
     *
     * @param imMerchantId 当前im顾问id，如n12345
     * @param query 查找关键词
     * @param maxNum 最多返回条数
     * @return
     */
    List<ConsultantKnowledgeEntity> getSimilarKnowledgeList(String imMerchantId, String query, int maxNum);

    /**
     * 获取指定顾问id下相似的知识（Vex）
     *
     * @param knowledgeBaseType 知识库类型 {@link KnowledgeBaseTypeEnum}
     * @param query 查找关键词
     * @param maxNum 最多返回条数
     * @return
     */
    List<ConsultantKnowledgeEntity> getSimilarKnowledgeList(int knowledgeBaseType, String query, int maxNum);

    /**
     * 更新知识
     * @param entity
     * @return
     */
    int updateBizKnowledgeId(ConsultantKnowledgeEntity entity);
}
