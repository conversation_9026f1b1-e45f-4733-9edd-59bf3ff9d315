package com.sankuai.dzim.pilot.acl.impl;

import com.dianping.cat.Cat;
import com.dianping.pigeon.remoting.provider.config.annotation.Service;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.acl.IBotWaihuAclService;
import com.sankuai.gaigc.arrange.api.thrift.IBotWaihuThriftService;
import com.sankuai.gaigc.arrange.api.thrift.dto.request.WaihuCallRequest;
import com.sankuai.gaigc.arrange.api.thrift.dto.response.WaihuCallResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import javax.annotation.Resource;
import java.util.Optional;

@Slf4j
@Service
public class IBotWaihuAclServiceImpl implements IBotWaihuAclService {
    @Resource
    private IBotWaihuThriftService ibotWaihuThriftService;

    @Override
    public Pair<Integer, String> sendNewOutBoundCall(WaihuCallRequest req) {
        // 外呼成功code=200，其他code和原先sendHairstylingOutboundCall接口保持不变
        try {
            if (req == null) {
                return Pair.of(200, StringUtils.EMPTY);
            }
            Cat.logEvent("sendNewOutBoundCall", "aiCallCNT");
            LogUtils.logReturnInfo(log, TagContext.builder().action("sendNewOutBoundCall").build(),
                    new WarnMessage("sendNewOutBoundCall", "AI拨打外呼请求", ""), req, StringUtils.EMPTY);

            WaihuCallResponse waihuCallResponse = ibotWaihuThriftService.aiCall(req);

            if (waihuCallResponse == null || waihuCallResponse.getCode() != 200) {
                Cat.logEvent("sendNewOutBoundCall", "aiCallFailed");
                LogUtils.logFailLog(log, TagContext.builder().action("sendNewHairCall").build(),
                        WarnMessage.build("sendNewOutBoundCall", "AI拨打外呼失败", ""),
                        req, waihuCallResponse);
                return Pair.of(Optional.ofNullable(waihuCallResponse).map(WaihuCallResponse::getCode).orElse(0), StringUtils.EMPTY);
            }

            LogUtils.logReturnInfo(log, TagContext.builder().action("sendNewOutBoundCall").build(),
                    WarnMessage.build("sendNewOutBoundCall", "AI拨打外呼", ""),
                    req, waihuCallResponse);
            return Pair.of(waihuCallResponse.getCode(), waihuCallResponse.getData());

        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("sendNewOutBoundCall").build(),
                    WarnMessage.build("sendNewOutBoundCall", "AI拨打外呼", ""), req, e);
            return Pair.of(200, StringUtils.EMPTY);
        }
    }
}
