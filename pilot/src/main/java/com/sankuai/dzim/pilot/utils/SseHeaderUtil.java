package com.sankuai.dzim.pilot.utils;

import javax.servlet.http.HttpServletResponse;

/**
 * @author: zhouyibing
 * @date: 2024/7/31
 */
public class SseHeaderUtil {

    public static void setSseResponseHeader(HttpServletResponse httpServletResponse) {
        httpServletResponse.setHeader("X-Accel-Buffering", "no");
        httpServletResponse.setHeader("Cache-Control", "no-cache");
        httpServletResponse.setHeader("Connection", "keep-alive");
    }
}
