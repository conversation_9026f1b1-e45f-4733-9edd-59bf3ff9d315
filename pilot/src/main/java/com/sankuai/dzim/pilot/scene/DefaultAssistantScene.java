package com.sankuai.dzim.pilot.scene;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.dzim.message.dto.MessageDTO;
import com.sankuai.dzim.pilot.acl.HaimaAclService;
import com.sankuai.dzim.pilot.api.enums.assistant.AssistantTypeEnum;
import com.sankuai.dzim.pilot.api.enums.assistant.PlatformEnum;
import com.sankuai.dzim.pilot.buffer.core.PilotBuffer;
import com.sankuai.dzim.pilot.buffer.core.PilotBufferService;
import com.sankuai.dzim.pilot.buffer.stream.vo.BufferMergedVO;
import com.sankuai.dzim.pilot.chain.enums.AIServiceExtraKeyEnum;
import com.sankuai.dzim.pilot.gateway.api.assistant.data.SendBeamMessageReq;
import com.sankuai.dzim.pilot.gateway.api.assistant.data.SseAwareException;
import com.sankuai.dzim.pilot.process.data.AIServiceConfig;
import com.sankuai.dzim.pilot.process.localplugin.data.UserContext;
import com.sankuai.dzim.pilot.scene.data.AssistantExtraConstant;
import com.sankuai.dzim.pilot.scene.data.AssistantSceneConfig;
import com.sankuai.dzim.pilot.scene.data.AssistantSceneContext;
import com.sankuai.dzim.pilot.scene.data.AssistantSceneRequest;
import com.sankuai.dzim.pilot.scene.task.TaskManager;
import com.sankuai.dzim.pilot.scene.task.data.*;
import com.sankuai.dzim.pilot.utils.CompletableFutureExpandUtils;
import com.sankuai.dzim.pilot.utils.LionConfigUtil;
import com.sankuai.dzim.pilot.utils.ThreadPoolUtil;
import com.sankuai.dzim.pilot.utils.context.RequestContext;
import com.sankuai.dzim.pilot.utils.context.RequestContextConstants;
import com.sankuai.it.iam.common_base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @author: zhouyibing
 * @date: 2024/7/26
 */
@Component
@Slf4j
public class DefaultAssistantScene implements AssistantScene {

    @Autowired
    private PilotBufferService pilotBufferService;

    @Autowired
    private TaskManager taskManager;

    @Autowired
    private LionConfigUtil lionConfigUtil;

    @Autowired
    private HaimaAclService haimaAclService;

    public BufferMergedVO process(AssistantSceneRequest request) {

        PilotBuffer buffer = null;
        try {
            // 2.初始化上下文
            AssistantSceneContext context = buildAssistantSceneContext(request);
            RequestContext.setAttribute(RequestContextConstants.ASSISTANT_CONTEXT, context);

            // 3.初始化buffer
            buffer = pilotBufferService.createAndConsume();

            // 3.任务执行
            runTask(context);

            // 4.显式buffer标志写入结束
            buffer.finishBufferConsume();

            // 5.等待buffer返回
            return buffer.getBufferMerged();
        } catch (SseAwareException e) {
            // todo 日志打印规范和异常
            log.error("DefaultAssistantScene.process SseAwareException error", e);
            throw e;
        } catch (Exception e) {
            // todo 日志打印规范和异常
            log.error("DefaultAssistantScene.process error", e);
            throw new RuntimeException("助手流程异常");
        } finally {
            // 异常场景处理，标志写入结束
            if (buffer != null) {
                buffer.finishBufferConsume();
            }
        }
    }

    private void runTask(AssistantSceneContext assistantSceneContext) {
        // 1.构建主任务和子任务
        TaskContext mainTask = taskManager.buildMainTask(assistantSceneContext);
        List<TaskContext> subTasks = taskManager.buildSubTasks(assistantSceneContext);

        // 2.子任务异步执行
        List<CompletableFuture<TaskResult>> subTaskFutures = subTasks.stream()
                .map(taskContext -> CompletableFuture.supplyAsync(() -> taskManager.execute(taskContext), ThreadPoolUtil.ASSISTANT_SUB_TASK_POOL.getExecutor())).collect(Collectors.toList());

        // 3.执行主任务
        TaskResult mainTaskResult = taskManager.execute(mainTask);

        // 4. 主任务完成后，设置子任务3秒超时
        List<CompletableFuture<TaskResult>> timeoutSubTaskFutures = subTaskFutures.stream().map(subTaskFuture -> CompletableFutureExpandUtils.orTimeout(subTaskFuture, 3, TimeUnit.SECONDS).exceptionally(ex -> {
            log.error("子任务异常", ex);
            return taskManager.processException(subTasks.get(subTaskFutures.indexOf(subTaskFuture)));
        })).collect(Collectors.toList());

        // 5.子任务异步执行结果处理
        CompletableFuture<Void> allCompletableFuture = CompletableFuture.allOf(timeoutSubTaskFutures.toArray(new CompletableFuture[timeoutSubTaskFutures.size()]));
        allCompletableFuture.join();
        timeoutSubTaskFutures.forEach(subTaskFuture -> taskManager.afterExecute(subTaskFuture.join(), mainTaskResult));
    }

    private AssistantSceneContext buildAssistantSceneContext(AssistantSceneRequest request) {
        AssistantSceneConfig assistantSceneConfig = buildAssistantSceneConfig(request);
        if (assistantSceneConfig == null) {
            throw new RuntimeException("助手场景配置为空");
        }
        //格式化业务参数
        formatEnvContext(request);

        MessageDTO messageDTO = new MessageDTO();
        messageDTO.setMessage(request.getQuestion());
        messageDTO.setMessageId(request.getMessageId());
        messageDTO.setChatGroupId(request.getChatGroupId() == null ? 0 : request.getChatGroupId().intValue());

        AssistantSceneContext assistantSceneContext = new AssistantSceneContext();
        assistantSceneContext.setAssistantSceneConfig(assistantSceneConfig);
        assistantSceneContext.setMessageDTO(messageDTO);
        assistantSceneContext.setUserId(request.getUserId());
        assistantSceneContext.setAssistantType(request.getAssistantType());
        assistantSceneContext.setReplyMessageId(request.getReplyMessageId());

        Map<String, Object> extra = new HashMap<>();
        extra.put(AssistantExtraConstant.MESSAGE_INPUT_SOURCE, request.getInputSourceType());
        extra.put(AssistantExtraConstant.MESSAGE_BIZ_PARAMS, request.getBizParams());
        extra.put(AIServiceExtraKeyEnum.ENV_CONTEXT.getKey(), request.getEnvContext());
        extra.put(AIServiceExtraKeyEnum.USER_CONTEXT.getKey(), new UserContext(request.getUserId(),
                request.getQuestion(), request.getChatGroupId(), request.getAssistantType(), request.getUserIp()));
        extra.put(AIServiceExtraKeyEnum.IMAGE_URLS.getKey(), request.getImgUrls());
        extra.put(AIServiceExtraKeyEnum.CHAT_GROUP_EXP.getKey(), request.getChatGroupExp());
        extra.put(AIServiceExtraKeyEnum.BEAM_REQUEST_CONTEXT.getKey(), request.getBeamRequestContext());
        fillEnvContextForBeam(request, extra, request.getBeamRequestContext());
        assistantSceneContext.setExtra(extra);
        return assistantSceneContext;
    }

    private void fillEnvContextForBeam(AssistantSceneRequest request, Map<String, Object> extra, SendBeamMessageReq beamRequestContext) {
        if (request.getAssistantType() != AssistantTypeEnum.BEAM_FWLS_AGENT.getType()) {
            return;
        }

        EnvContext envContext = new EnvContext();
        try {
            envContext.setLat(Double.valueOf(beamRequestContext.getUser_info().getIntent_lat()));
            envContext.setLng(Double.valueOf(beamRequestContext.getUser_info().getIntent_lng()));
            envContext.setPlatform(PlatformEnum.MT.getType());
            envContext.setLocationCityId(Integer.valueOf(beamRequestContext.getUser_info().getCity_id()));
            envContext.setCityId(Integer.valueOf(beamRequestContext.getUser_info().getCity_id()));
            envContext.setUserId(parseUserId(request.getUserId()));
            envContext.setDeviceId(beamRequestContext.getUser_info().getUuid());
        } catch (Exception e) {
            log.error("fillEnvContextForBeam error", e);
        }
        extra.put(AIServiceExtraKeyEnum.ENV_CONTEXT.getKey(), JsonCodec.encode(envContext));

    }

    private long parseUserId(String imUserId) {
        if (StringUtils.isBlank(imUserId)) {
            return 0L;
        }
        String userIdStr = imUserId.substring(1);
        return NumberUtils.toLong(userIdStr, 0L);
    }

    private void formatEnvContext(AssistantSceneRequest request) {
        if(StringUtil.isBlank(request.getChannel()) || StringUtil.isBlank(request.getEnvContext())){
            return;
        }
        try {
            EnvContext envContext = JsonCodec.decode(request.getEnvContext(), EnvContext.class);
            envContext.setChannel(request.getChannel());
            request.setBizParams(JsonCodec.encodeWithUTF8(envContext));
        } catch (Exception e) {
            log.error("formatEnvContext error", e);
        }
    }

    private AssistantSceneConfig buildAssistantSceneConfig(AssistantSceneRequest request) {
        //从Lion中获取配置
        AssistantSceneConfig assistantSceneConfig = lionConfigUtil.getAssistantSceneConfigMap(request.getAssistantType());
        if (assistantSceneConfig == null) {
            return null;
        }
        // 如果配置了海马key，则海马配置覆盖lion
        if (assistantSceneConfig.getMainTask() != null && StringUtils.isNotBlank(assistantSceneConfig.getMainTask().getHaimaConfigKey())) {
            overrideAIConfig(assistantSceneConfig.getMainTask());
        }
        //构建主任务和子任务配置
        buildTaskConfig(assistantSceneConfig.getMainTask());
        if (CollectionUtils.isNotEmpty(assistantSceneConfig.getSubTasks())) {
            assistantSceneConfig.getSubTasks().forEach(this::buildTaskConfig);
        }
        return assistantSceneConfig;
    }

    private void overrideAIConfig(TaskConfig taskConfig) {
        if (taskConfig == null) {
            return;
        }
        AIServiceConfig config = haimaAclService.queryAIConfig(taskConfig.getHaimaConfigKey());
        if (config == null) {
            return;
        }
        taskConfig.setAiServiceConfig(config);
    }

    private void buildTaskConfig(TaskConfig taskConfig) {
        if (taskConfig == null) {
            return;
        }
        //没有配置的Task使用Default配置
        if (taskConfig.getAiServiceConfig() == null || taskConfig.getAiServiceConfig().getAiServiceType() <= 0) {
            TaskConfig defaultTaskConfig = MapUtils.getObject(lionConfigUtil.getAssistantDefaultTaskConfigMap(), taskConfig.getTaskType());
            if (defaultTaskConfig != null) {
                taskConfig.setAiServiceConfig(defaultTaskConfig.getAiServiceConfig());
            }
        }
    }
}
