package com.sankuai.dzim.pilot.acl.impl;

import com.dianping.cat.Cat;
import com.dianping.dzim.common.helper.FutureUtils;
import com.dianping.generic.entrance.poiphone.api.PoiPhoneService;
import com.dianping.generic.entrance.poiphone.dto.PhoneDTO;
import com.dianping.poi.bizhour.BizHourForecastService;
import com.dianping.poi.bizhour.dto.BizForecastDTO;
import com.dianping.poi.bizhour.enums.SourceEnum;
import com.dianping.poi.relation.service.api.PoiRelationService;
import com.dianping.poi.relation.service.dto.AdvancedPoiPairDTO;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.dianping.vc.operate.logger.api.message.LogMessage;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.SettableFuture;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonClient;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.meituan.nibmp.mem.vaf.query.thrift.BizCooperationService;
import com.meituan.nibmp.mem.vaf.query.thrift.dto.BatchQueryBizCooperateInfoReqDTO;
import com.meituan.nibmp.mem.vaf.query.thrift.dto.BatchQueryBizCooperateInfoRespDTO;
import com.meituan.nibmp.mem.vaf.query.thrift.dto.BizCooperateInfoDTO;
import com.meituan.service.mobile.mtthrift.netty.ContextStore;
import com.sankuai.athena.biz.Response;
import com.sankuai.athena.client.executor.Futures;
import com.sankuai.athena.inf.AthenaInf;
import com.sankuai.athena.inf.rpc.CallType;
import com.sankuai.athena.inf.rpc.RpcClient;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.acl.ShopAclService;
import com.sankuai.dzim.pilot.acl.data.ShopBackCategoryData;
import com.sankuai.dzim.pilot.acl.data.ShopCardData;
import com.sankuai.dzim.pilot.acl.data.ShopCategoryData;
import com.sankuai.dzim.pilot.acl.data.req.ShopCardQueryReq;
import com.sankuai.dzim.pilot.utils.LionConfigUtil;
import com.sankuai.dztheme.shop.enums.ClientType;
import com.sankuai.dztheme.shop.enums.ThemeCoordType;
import com.sankuai.dztheme.shop.service.DzThemeShopService;
import com.sankuai.dztheme.shop.vo.ShopCardDTO;
import com.sankuai.dztheme.shop.vo.ShopThemePlanRequest;
import com.sankuai.dztheme.shop.vo.v1.ShopThemeResponse;
import com.sankuai.sinai.data.api.dto.DpPoiBackCategoryDTO;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.DpPoiRequest;
import com.sankuai.sinai.data.api.dto.MtPoiDTO;
import com.sankuai.sinai.data.api.service.DpPoiService;
import com.sankuai.sinai.data.api.service.MtPoiService;
import com.sankuai.zdc.tag.apply.api.PoiTagDisplayRPCService;
import com.sankuai.zdc.tag.apply.dto.DisplayTagDto;
import com.sankuai.zdc.tag.apply.dto.FindSceneDisplayTagRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Nonnull;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ShopAclServiceImpl implements ShopAclService {

    @MdpThriftClient(remoteAppKey = "com.sankuai.sinai.data.query", timeout = 3000)
    private DpPoiService dpPoiService;

    @RpcClient(remoteAppkey = "com.sankuai.sinai.data.query", timeout = 1000, callType = CallType.FUTURE)
    private DpPoiService dpPoiServiceFuture;

    @RpcClient(url = "http://service.dianping.com/ZDCTagApplyService/poiTagDisplayRPCService_1.0.0", callType = CallType.FUTURE, timeout = 1000)
    private PoiTagDisplayRPCService poiTagDisplayRPCFutureService;

    @RpcClient(url = "com.dianping.poi.relation.service.api.PoiRelationService", callType = CallType.FUTURE)
    private PoiRelationService poiRelationServiceFuture;

    @MdpThriftClient(remoteAppKey = "com.sankuai.sinai.data.query", timeout = 3000)
    private MtPoiService mtPoiService;

    @MdpThriftClient(remoteAppKey = "com.sankuai.sinai.data.query", timeout = 100, testTimeout = 2000, async = true)
    private MtPoiService mtPoiServiceAsync;

    @MdpPigeonClient(url = "com.sankuai.dztheme.shop.service.DzThemeShopService", timeout = 1000)
    private DzThemeShopService dzThemeShopService;

    @RpcClient(url = "http://service.dianping.com/dpTollPhoneService/poiPhoneService_1.0.0", timeout = 500, callType = CallType.SYNC)
    private PoiPhoneService poiPhoneService;

    @RpcClient(url = "http://service.dianping.com/com.dianping.poi.bizhour.bizHourForecastService_1.0.0", timeout = 1000, callType = CallType.SYNC)
    private BizHourForecastService bizHourForecastService;

    @Autowired
    private LionConfigUtil lionConfigUtil;

    @MdpThriftClient(
            remoteAppKey = "com.sankuai.mem.vaf.query",
            remoteServerPort = 4120,
            timeout = 3000
    )
    private BizCooperationService.Iface bizCooperationService;

    private static ThreadPool phonePool = Rhino.newThreadPool("PhonePool",
            DefaultThreadPoolProperties.Setter().withCoreSize(10).withMaxSize(20).withMaxQueueSize(100));

    private static ThreadPool tagPool = Rhino.newThreadPool("TagPool",
            DefaultThreadPoolProperties.Setter().withCoreSize(10).withMaxSize(20).withMaxQueueSize(100));


    @Override
    public DpPoiDTO getShopInfo(long dpShopId) {
        if (dpShopId <= 0) {
            return null;
        }
        try {
            DpPoiRequest request = buildDpPoiRequest(dpShopId);
            List<DpPoiDTO> dpPoiDTOList = dpPoiService.findShopsByShopIds(request);
            if (CollectionUtils.isEmpty(dpPoiDTOList)) {
                return null;
            }
            return dpPoiDTOList.get(0);
        } catch (TException e) {
            LogUtils.logFailLog(log, TagContext.builder().action("getShopInfo").bizId(String.valueOf(dpShopId)).build(),
                    new WarnMessage("ShopAclService", "查询门店信息失败", null), dpShopId, null, e);
        }
        return null;
    }

    private DpPoiRequest buildDpPoiRequest(long dpShopId) {
        DpPoiRequest request = new DpPoiRequest();
        request.setShopIds(Lists.newArrayList(dpShopId));
        request.setFields(Lists.newArrayList("shopId", "mtPoiId", "shopName", "branchName", "mainRegionName", "address", "crossRoad", "businessHours",
                "shopRegionList", "normPhones", "shopPower", "avgPrice", "hospitalInfo", "poiBizAttrValues", "shopType", "mainCategoryPath", "lat", "lng", "cityId", "shopBackCategoryList", "backMainCategoryPath"));
        return request;
    }

    @Override
    public CompletableFuture<DpPoiDTO> getShopInfoAsync(long dpShopId) {
        DpPoiRequest request = buildDpPoiRequest(dpShopId);

        try {
            CompletableFuture<List<DpPoiDTO>> dpPoiDTOListFuture = AthenaInf.getRpcCompletableFuture(dpPoiServiceFuture.findShopsByShopIds(request));
            return dpPoiDTOListFuture.thenApply(dpPoiDTOList -> {
                if (CollectionUtils.isEmpty(dpPoiDTOList)) {
                    return null;
                }
                return dpPoiDTOList.get(0);
            });
        } catch (TException e) {
            LogUtils.logFailLog(log, TagContext.builder().action("getShopInfo").bizId(String.valueOf(dpShopId)).build(),
                    new WarnMessage("ShopAclService", "查询门店信息失败", null), dpShopId, null, e);
            return null;
        }
    }

    @Override
    public CompletableFuture<List<DisplayTagDto>> getPoiTagsByShopIdAndBizCode(long dpShopId, String bizCode) {
        FindSceneDisplayTagRequest findSceneDisplayTagRequest = buildFindSceneDisplayTagRequest(dpShopId, bizCode);
        CompletableFuture<Response<Map<Long, List<DisplayTagDto>>>> future = AthenaInf.getRpcCompletableFuture(poiTagDisplayRPCFutureService.findSceneDisplayTagOfDp(findSceneDisplayTagRequest));

        return future.exceptionally(e -> {
            LogUtils.logFailLog(log, TagContext.builder().action("getPoiTagsByShopIdAndBizCode").shopId(String.valueOf(dpShopId)).build(),
                    WarnMessage.build("findTagsByBizAndDPShopId", "查询Poi标签异常", ""), new Object[]{dpShopId, bizCode}, e);
            return null;
        }).thenApply(response -> {
            if (response == null || !response.isSuccess()) {
                LogUtils.logFailLog(log, TagContext.builder().action("getPoiTagsByShopIdAndBizCode").shopId(String.valueOf(dpShopId)).build(),
                        WarnMessage.build("findTagsByBizAndDPShopId", "查询Poi标签失败", ""), new Object[]{dpShopId, bizCode}, response);
                return Lists.newArrayList();
            }
            LogUtils.logReturnInfo(log, TagContext.builder().action("getPoiTagsByShopIdAndBizCode").shopId(String.valueOf(dpShopId)).build(),
                    WarnMessage.build("findTagsByBizAndDPShopId", "查询Poi标签成功", ""), new Object[]{dpShopId, bizCode}, response);
            return response.getData().get(dpShopId);
        });
    }

    private FindSceneDisplayTagRequest buildFindSceneDisplayTagRequest(long dpShopId, String bizCode) {
        FindSceneDisplayTagRequest findSceneDisplayTagRequest = new FindSceneDisplayTagRequest();
        findSceneDisplayTagRequest.setShopIds(Lists.newArrayList(dpShopId));
        findSceneDisplayTagRequest.setBizCode(bizCode);
        return findSceneDisplayTagRequest;
    }

    @Override
    public MtPoiDTO getMtShopInfo(long mtShopId) {
        if (mtShopId <= 0) {
            return null;
        }

        try {
            List<Long> mtIds = Lists.newArrayList(mtShopId);
            List<String> fieldList = Arrays.asList("mtPoiId", "frontImg", "dpPoiId", "name", "branchName", "address", "referenceAddress", "businessHours", "normPhones",
                    "mtAvgPrice", "mtAvgScore", "subScores", "subScoreDTOList", "publicTransit", "poiSubways", "poiLandmarks", "mallName", "floor", "shopGuideSteps", "parkingInfo", "latitude", "longitude");
            Map<Long, MtPoiDTO> poiIdMap = mtPoiService.findPoisById(mtIds, fieldList);
            if (MapUtils.isEmpty(poiIdMap) || !poiIdMap.containsKey(mtShopId)) {
                return null;
            }
            return poiIdMap.get(mtShopId);
        } catch (TException e) {
            LogUtils.logFailLog(log, TagContext.builder().action("getMtShopInfo").bizId(String.valueOf(mtShopId)).build(),
                    new WarnMessage("ShopAclService", "查询美团门店信息失败", null), mtShopId, null, e);
        }
        return null;
    }

    @Override
    public Map<Long, MtPoiDTO> batchGetMtShopInfo(List<Long> mtShopIds) {
        if (CollectionUtils.isEmpty(mtShopIds)) {
            return null;
        }

        try {
            mtShopIds = mtShopIds.stream().distinct().collect(Collectors.toList());
            List<List<Long>> partitionShops = Lists.partition(mtShopIds, 50);
            Map<Long, MtPoiDTO> mtPoiDTOMap = new HashMap<>();
            for (List<Long> partition : partitionShops) {
                List<String> fieldList = Arrays.asList("mtPoiId", "dpPoiId", "name", "branchName", "address", "referenceAddress", "businessHours", "normPhones", "closeStatus",
                        "mtAvgPrice", "mtAvgScore", "subScores", "subScoreDTOList", "publicTransit", "poiSubways", "poiLandmarks", "mallName", "floor", "shopGuideSteps", "parkingInfo", "typeHierarchy",
                        "mtLocationName", "mtCityLocationName", "mtBareaName", "poiBrandName", "longitude", "latitude");
                Map<Long, MtPoiDTO> poiIdMap = mtPoiService.findPoisById(partition, fieldList);
                if (MapUtils.isNotEmpty(poiIdMap)) {
                    mtPoiDTOMap.putAll(poiIdMap);
                }
            }
            return mtPoiDTOMap;
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("batchGetMtShopInfo").bizId(String.valueOf(mtShopIds)).build(),
                    new WarnMessage("ShopAclService", "批量查询美团门店信息失败", null), mtShopIds, null, e);
        }
        return null;
    }

    @Override
    public CompletableFuture<Map<Long, MtPoiDTO>> batchGetMtShopInfoAsync(List<Long> mtShopIds) {
        if(CollectionUtils.isEmpty(mtShopIds)){
            return CompletableFuture.completedFuture(Maps.newHashMap());
        }
        try {
            List<String> fieldList = Arrays.asList("mtPoiId", "dpPoiId", "name", "branchName", "address", "referenceAddress", "businessHours", "normPhones", "closeStatus",
                    "mtAvgPrice", "mtAvgScore", "subScores", "subScoreDTOList", "publicTransit", "poiSubways", "poiLandmarks", "mallName", "floor", "shopGuideSteps", "parkingInfo", "typeHierarchy",
                    "mtLocationName", "mtCityLocationName", "mtBareaName", "poiBrandName", "longitude", "latitude");
            mtPoiServiceAsync.findPoisById(mtShopIds, fieldList);
            SettableFuture<Map<Long, MtPoiDTO>> findShopsByShopIdsFuture = (SettableFuture<Map<Long, MtPoiDTO>>) ContextStore.getSettableFuture();
            CompletableFuture<Map<Long, MtPoiDTO>> cf = new CompletableFuture<>();
            com.google.common.util.concurrent.Futures.addCallback(findShopsByShopIdsFuture, new FutureCallback<Map<Long, MtPoiDTO>>() {
                @Override
                public void onSuccess(Map<Long, MtPoiDTO> mtPoiDtoMap) {
                    cf.complete(mtPoiDtoMap);
                }

                @Override
                public void onFailure(@Nonnull Throwable throwable) {
                    Cat.logError(throwable);
                    log.error("batchGetMtShopInfoAsync onFailure, request: {}", mtShopIds);
                    cf.complete(Maps.newHashMap());
                }
            });
            return cf;
        } catch (Exception e) {
            Cat.logError(e);
            log.error("batchGetMtShopInfoAsync error, request: {}", mtShopIds);
            return CompletableFuture.completedFuture(Maps.newHashMap());
        }
    }

    @Override
    public long loadDpShopIdByMtShopId(long mtShopId) {
        try {
            AdvancedPoiPairDTO advancedPoiPairDTO = AthenaInf.getRpcCompletableFuture(poiRelationServiceFuture.queryPoiPairByMtIdL(mtShopId)).join();
            if (advancedPoiPairDTO == null) {
                return 0L;
            }
            return advancedPoiPairDTO.getDpId();
        } catch (Exception e) {
            log.error(LogMessage.createReceiveRPCResponseLog("ShopAclService::queryDpShopIdByMtShopId", "根据mtShopId获取DpShopId出错")
                    .putTag("mtShopId", String.valueOf(mtShopId)).build(), e);
            return 0L;
        }
    }

    @Override
    public ShopCategoryData loadShopFrontCatePath(long dpShopId) {
        DpPoiDTO dpPoiDTO = getShopInfo(dpShopId);
        if (dpPoiDTO == null) {
            return null;
        }
        return buildShopCategoryData(dpPoiDTO);
    }

    private ShopCategoryData buildShopCategoryData(DpPoiDTO dpPoiDTO) {
        if (dpPoiDTO == null) {
            return null;
        }
        Integer shopType = dpPoiDTO.getShopType();
        if (shopType == null) {
            return null;
        }
        int shopSecondCateId = getSecondCategoryId(dpPoiDTO);
        int shopThirdCateId = getThirdCategoryId(dpPoiDTO);

        ShopCategoryData shopCategoryData = new ShopCategoryData();
        shopCategoryData.setDpShopId(dpPoiDTO.getShopId());
        shopCategoryData.setShopType(shopType);
        shopCategoryData.setForeSecondCateId(shopSecondCateId);
        shopCategoryData.setForeThirdCateId(shopThirdCateId);
        return shopCategoryData;
    }

    private int getSecondCategoryId(DpPoiDTO poiDTO) {
        if (poiDTO == null) {
            return 0;
        }
        List<Integer> mainCategoryPath = poiDTO.getMainCategoryPath();
        if (CollectionUtils.isNotEmpty(mainCategoryPath) && mainCategoryPath.size() >= 2) {
            return mainCategoryPath.get(1);
        }
        return 0;
    }

    private int getThirdCategoryId(DpPoiDTO poiDTO) {
        if (poiDTO == null) {
            return 0;
        }
        List<Integer> mainCategoryPath = poiDTO.getMainCategoryPath();
        if (CollectionUtils.isNotEmpty(mainCategoryPath) && mainCategoryPath.size() >= 3) {
            return mainCategoryPath.get(2);
        }
        return 0;
    }

    @Override
    public CompletableFuture<Map<Long, Long>> queryPoiMappingByDpIdL(List<Long> shopIds) {
        try {
            CompletableFuture<Map<Long, AdvancedPoiPairDTO>> pairDTOsFuture = AthenaInf.getRpcCompletableFuture(poiRelationServiceFuture.queryPoiMappingByDpIdL(shopIds));
            return pairDTOsFuture.thenApply(pairDTOs -> {
                if (MapUtils.isEmpty(pairDTOs)) {
                    return Maps.newHashMap();
                }
                return pairDTOs.entrySet().stream()
                        .filter(entry -> entry.getValue() != null)
                        .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().getMtId()));
            });
        } catch (Exception e) {
            log.error("QueryPoiMappingByDpIdL exception, shopIds:{}", JsonCodec.encode(shopIds), e);
            return null;
        }
    }

    @Override
    public CompletableFuture<Map<Long, Long>> queryMtIdLByDpIdL(List<Long> shopIds) {
        try {
            List<List<Long>> partitions = Lists.partition(shopIds, 100);
            List<CompletableFuture<Map<Long, Long>>> futures = partitions.stream()
                    .map(partition -> {
                        try {
                            return AthenaInf.getRpcCompletableFuture(poiRelationServiceFuture.queryPoiMappingByDpIdL(partition))
                                    .thenApply(this::processPairMtDTOs);
                        } catch (Exception e) {
                            log.error("QueryPoiMappingByDpIdL partition exception, partitionShopId:{}", JsonCodec.encode(partition), e);
                        }
                        return null;
                    }).filter(Objects::nonNull)
                    .collect(Collectors.toList());

            return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                    .thenApply(v -> futures.stream()
                            .map(CompletableFuture::join)
                            .flatMap(map -> map.entrySet().stream())
                            .collect(Collectors.toMap(
                                    Map.Entry::getKey,
                                    Map.Entry::getValue,
                                    (v1, v2) -> v1,
                                    HashMap::new
                            )));
        } catch (Exception e) {
            log.error("QueryPoiMappingByDpIdL exception, shopIds:{}", JsonCodec.encode(shopIds), e);
            return CompletableFuture.completedFuture(null);
        }
    }

    private Map<Long, Long> processPairMtDTOs(Map<Long, AdvancedPoiPairDTO> pairDTOs) {
        if (MapUtils.isEmpty(pairDTOs)) {
            return Maps.newHashMap();
        }
        return pairDTOs.entrySet().stream()
                .filter(entry -> entry.getValue() != null)
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().getMtId()));
    }

    @Override
    public CompletableFuture<Map<Long, Long>> queryDpIdLByMtIdL(List<Long> shopIds) {
        try {
            List<List<Long>> partitions = Lists.partition(shopIds, 100);
            List<CompletableFuture<Map<Long, Long>>> futures = partitions.stream()
                    .map(partition -> {
                        try {
                            return AthenaInf.getRpcCompletableFuture(poiRelationServiceFuture.queryPoiMappingByMtIdL(partition))
                                    .thenApply(this::processPairDpDTOs);
                        } catch (Exception e) {
                            log.error("queryPoiMappingByMtIdL partition exception, partitionShopId:{}", JsonCodec.encode(partition), e);
                        }
                        return null;
                    }).filter(Objects::nonNull)
                    .collect(Collectors.toList());

            return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                    .thenApply(v -> futures.stream()
                            .map(CompletableFuture::join)
                            .flatMap(map -> map.entrySet().stream())
                            .collect(Collectors.toMap(
                                    Map.Entry::getKey,
                                    Map.Entry::getValue,
                                    (v1, v2) -> v1,
                                    HashMap::new
                            )));
        } catch (Exception e) {
            log.error("queryPoiMappingByMtIdL exception, shopIds:{}", JsonCodec.encode(shopIds), e);
            return CompletableFuture.completedFuture(null);
        }
    }

    private Map<Long, Long> processPairDpDTOs(Map<Long, AdvancedPoiPairDTO> pairDTOs) {
        if (MapUtils.isEmpty(pairDTOs)) {
            return Maps.newHashMap();
        }
        return pairDTOs.entrySet().stream()
                .filter(entry -> entry.getValue() != null)
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().getDpId()));
    }


    @Override
    public long loadMTShopIdByDPShopId(long dpShopId) {
        CompletableFuture<Map<Long, Long>> dpShopId2MTShopIdMapFuture = queryPoiMappingByDpIdL(Lists.newArrayList(dpShopId));
        Map<Long, Long> dpShopId2MTShopIdMap = FutureUtils.execute(dpShopId2MTShopIdMapFuture);
        if (MapUtils.isEmpty(dpShopId2MTShopIdMap) || dpShopId2MTShopIdMap.get(dpShopId) == null) {
            return 0;
        }
        return dpShopId2MTShopIdMap.get(dpShopId);
    }

    @Override
    public ShopCardData getShopCard(ShopCardQueryReq req) {
        try {
            ShopThemePlanRequest shopThemePlanRequest = buildShopThemePlanRequest(req);
            ShopThemeResponse shopThemeResponse = dzThemeShopService.queryShopTheme(shopThemePlanRequest);
            if (shopThemeResponse == null || MapUtils.isEmpty(shopThemeResponse.getShopCardDTOMap())) {
                LogUtils.logFailLog(log, TagContext.builder().action("getShopCard失败").bizId(String.valueOf(req.getShopId())).build(),
                        new WarnMessage("ShopAclService", "查询商户主题失败", null), req, shopThemeResponse);
                return null;
            }

            LogUtils.logReturnInfo(log, TagContext.builder().action("getShopCard成功").bizId(String.valueOf(req.getShopId())).build(),
                    new WarnMessage("ShopAclService", "查询商户主题成功", null), req, shopThemeResponse);
            return buildShopCardData(shopThemeResponse.getShopCardDTOMap().get(req.getShopId()));
        } catch (Exception e) {
            log.error("getShopCard exception, req:{}", JsonCodec.encode(req), e);
        }
        return null;
    }


    @Override
    public List<String> getShopPhone(long dpShopId) {
        // mock手机号
        List<String> mockPhone = lionConfigUtil.getAiBookMockConfigData().getMockPhone(dpShopId);
        if (CollectionUtils.isNotEmpty(mockPhone)) {
            return mockPhone;
        }

        try {
            List<String> allPhoneNum = poiPhoneService.findAllPhoneNum(dpShopId);
            return Optional.ofNullable(allPhoneNum).orElse(Lists.newArrayList());
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("getShopPhone").build(),
                    WarnMessage.build("getShopPhone", "获取门店号码异常", ""), dpShopId, e);
        }
        return Lists.newArrayList();
    }

    @Override
    public Map<Long, List<PhoneDTO>> batchGetShopPhone(List<Long> dpShopIds) {
        if (CollectionUtils.isEmpty(dpShopIds)) {
            return Maps.newHashMap();
        }

        Map<Long, List<PhoneDTO>> allPhoneDTOByShopIdMap = new ConcurrentHashMap<>();
        List<List<Long>> partitions = Lists.partition(dpShopIds, 20);
        List<Future<Void>> futures = new ArrayList<>();

        for (List<Long> partitionShopId : partitions) {
            Future future = phonePool.submit(() -> {
                Map<Long, List<PhoneDTO>> partitionResult = poiPhoneService.findAllPhoneDTOByShopIdsV2(partitionShopId);
                if (MapUtils.isNotEmpty(partitionResult)) {
                    allPhoneDTOByShopIdMap.putAll(partitionResult);
                }
            });
            futures.add(future);
        }

        // 等待所有任务完成
        for (Future<Void> future : futures) {
            try {
                Futures.get(future);
            } catch (Exception e) {
                log.error("Error while waiting for deal detail query task", e);
            }
        }
        return allPhoneDTOByShopIdMap;
    }

    @Override
    public BizForecastDTO getBizForecast(long dpShopId, String dateString) {
        try {
            BizForecastDTO bizForecast = bizHourForecastService.getBizForecast(dpShopId, SourceEnum.DIANPING, dateString);
            if (bizForecast == null) {
                LogUtils.logFailLog(log, TagContext.builder().action("getBizForecast").build(),
                        WarnMessage.build("getBizForecast", "获取门店营业时间预测失败", ""), dpShopId, null);
                return null;
            }
            return bizForecast;
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("getBizForecast").build(),
                    WarnMessage.build("getBizForecast", "获取门店营业时间预测异常", ""), dpShopId, e);
            return null;
        }
    }

    @Override
    public Map<Long, BizCooperateInfoDTO> queryShopCooperateInfo(BatchQueryBizCooperateInfoReqDTO reqDTO) {
        try {
            BatchQueryBizCooperateInfoRespDTO respDTO = bizCooperationService.batchQueryBizCooperateInfo(reqDTO);
            if (respDTO == null || respDTO.getCommonResp() == null || !respDTO.getCommonResp().isSuccess()) {
                log.error("queryShopCooperateInfo fail, req = {}, resp = {}", JsonCodec.encodeWithUTF8(reqDTO), JsonCodec.encodeWithUTF8(respDTO));
                return null;
            }
            return respDTO.getBizId2CooperateInfoMap();
        } catch (TException e) {
            log.error("queryShopCooperateInfo error, req = {}", JsonCodec.encodeWithUTF8(reqDTO), e);
        }
        return null;
    }

    private ShopCardData buildShopCardData(ShopCardDTO shopCardDTO) {
        ShopCardData shopCardData = new ShopCardData();
        shopCardData.setShopId(shopCardDTO.getLongShopid());
        shopCardData.setShopName(shopCardDTO.getShopName());
        shopCardData.setDetailUrl(shopCardDTO.getShopUrl());
        shopCardData.setHeadPic(shopCardDTO.getHeadPic());
        shopCardData.setStar(shopCardDTO.getStarVal());
        shopCardData.setScore(extractNumericScore(shopCardDTO.getStarStr()));
        shopCardData.setDistance(shopCardDTO.getDistanceStr());
        shopCardData.setArea(shopCardDTO.getBareaName());
        shopCardData.setTags(shopCardDTO.getLabels());
        return shopCardData;
    }

    private static String extractNumericScore(String starStr) {
        if (StringUtils.isBlank(starStr)) {
            return null;
        }
        // 使用正则表达式匹配数字和小数点
        String numericScore = starStr.replaceAll("[^0-9.]", "");
        return numericScore.isEmpty() ? "" : numericScore;
    }

    private ShopThemePlanRequest buildShopThemePlanRequest(ShopCardQueryReq req) {
        ShopThemePlanRequest request = new ShopThemePlanRequest();
        request.setPlanId(req.getPlanId());
        request.setPlatform(req.getPlatform());
        request.setLongShopIds(Lists.newArrayList(req.getShopId()));
        request.setNativeClient(true);
        request.setCityId(req.getCityId());
        request.setUserId(req.getUserId());
        request.setCoordType(ThemeCoordType.GCJ02.getType());
        if (req.getLat() != 0) {
            request.setUserLat(req.getLat());
        }
        if (req.getLng() != 0) {
            request.setUserLng(req.getLng());
        }
        request.setUserId(req.getUserId());
        if (StringUtils.isNotBlank(req.getDeviceId())) {
            request.setDeviceId(req.getDeviceId());
        }
        if (StringUtils.isNotBlank(req.getUnionId())) {
            request.setUnionId(req.getUnionId());
        }
        request.setExtParams(req.getExtra());
        request.setClientVersion(req.getAppVersion());
        request.setClientOS(StringUtils.isNotBlank(req.getClient()) && req.getClient().equals("ios") ? 1 : 2);
        request.setClientType(ClientType.APP.getType());
        return request;
    }

    @Override
    public List<ShopBackCategoryData> getShopBackCategory(long dpShopId) {
        DpPoiDTO dpPoiDTO = getShopInfo(dpShopId);
        if (dpPoiDTO == null) {
            return null;
        }
        return extractShopBackMainCategoryPath(dpPoiDTO);
    }

    @Override
    public List<ShopBackCategoryData> extractShopBackCategoryData(DpPoiDTO dpPoiDTO) {
        if (dpPoiDTO == null || CollectionUtils.isEmpty(dpPoiDTO.getShopBackCategoryList())) {
            return Lists.newArrayList();
        }
        return dpPoiDTO.getShopBackCategoryList().stream()
                .map(category -> {
                    ShopBackCategoryData data = new ShopBackCategoryData();
                    data.setCategoryId(category.getCategoryId());
                    data.setCategoryName(category.getCategoryName());
                    data.setParentId(category.getParentId());
                    data.setLevel(category.getCategoryLevel());
                    data.setIsLeaf(category.getLeaf());
                    data.setIsMain(category.getMain());
                    return data;
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<ShopBackCategoryData> extractShopBackMainCategoryPath(DpPoiDTO dpPoiDTO) {
        if (dpPoiDTO == null || CollectionUtils.isEmpty(dpPoiDTO.getBackMainCategoryPath())) {
            return Lists.newArrayList();
        }
        return dpPoiDTO.getBackMainCategoryPath().stream()
                .map(category -> {
                    ShopBackCategoryData data = new ShopBackCategoryData();
                    data.setCategoryId(category.getCategoryId());
                    data.setCategoryName(category.getCategoryName());
                    data.setParentId(category.getParentId());
                    data.setLevel(category.getCategoryLevel());
                    data.setIsLeaf(category.getLeaf());
                    data.setIsMain(category.getMain());
                    return data;
                })
                .collect(Collectors.toList());
    }
}


