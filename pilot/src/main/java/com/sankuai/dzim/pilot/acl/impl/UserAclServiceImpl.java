package com.sankuai.dzim.pilot.acl.impl;

import com.dianping.account.UserAccountService;
import com.dianping.account.dto.UserAccountDTO;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.meituan.data.ups.thrift.LabelData;
import com.meituan.data.ups.thrift.LabelRequestParam;
import com.meituan.data.ups.thrift.QueryByIdRequest;
import com.meituan.data.ups.thrift.QueryResponse;
import com.meituan.data.ups.thrift.UserProfileServiceV2;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.sankuai.athena.inf.rpc.CallType;
import com.sankuai.athena.inf.rpc.RpcClient;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.message.common.utils.ImAccountTypeUtils;
import com.sankuai.dzim.pilot.acl.UserAclService;
import com.sankuai.dzim.pilot.api.enums.assistant.PlatformEnum;
import com.sankuai.dzim.pilot.utils.AccountUtils;
import com.sankuai.persona.common.Id;
import com.sankuai.persona.common.ResponseStatus;
import com.sankuai.wpt.user.merge.query.thrift.message.BindRelation;
import com.sankuai.wpt.user.merge.query.thrift.message.BindRelationResp;
import com.sankuai.wpt.user.merge.query.thrift.message.UserIdModel;
import com.sankuai.wpt.user.merge.query.thrift.message.UserMergeQueryService;
import com.sankuai.wpt.user.retrieve.thrift.message.RpcUserRetrieveService;
import com.sankuai.wpt.user.retrieve.thrift.message.UserFields;
import com.sankuai.wpt.user.retrieve.thrift.message.UserModel;
import com.sankuai.wpt.user.retrieve.thrift.message.UserRespMsg;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @since 2025/1/9 19:19
 */
@Slf4j
@Service
public class UserAclServiceImpl implements UserAclService {

    @Resource(name = "rpcUserRetrieveService")
    private RpcUserRetrieveService.Iface rpcUserRetrieveService;

    @RpcClient(url = "http://service.dianping.com/userAccountService/userAccountService_2.0.0", timeout = 1000, callType = CallType.SYNC)
    private UserAccountService dpUserAccountService;

    @MdpThriftClient(remoteAppKey = "com.sankuai.mtusercenter.merge.query", timeout = 1000)
    private UserMergeQueryService.Iface userMergeQueryService;

    @MdpThriftClient(remoteAppKey = "com.sankuai.data.ups", timeout = 2000)
    private UserProfileServiceV2.Iface upsClient;


    @Override
    public UserAccountDTO getDpUserAccountDTO(long dpUserId) {
        try {
            UserAccountDTO userAccountDTO = dpUserAccountService.loadById(dpUserId);
            if (userAccountDTO == null) {
                LogUtils.logFailLog(log, TagContext.builder().action("getDpUserAccountDTO").build(),
                        WarnMessage.build("getDpUserAccountDTO", "查询美团用户信息失败", ""),
                        dpUserId, userAccountDTO);
                return null;
            }
            return userAccountDTO;
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("getDpUserAccountDTO").build(),
                    WarnMessage.build("getDpUserAccountDTO", "查询美团用户信息失败", ""),
                    dpUserId, e);
            return null;
        }
    }

    @Override
    public UserModel getMtUserModel(long mtUserId) {
        try {
            UserFields userFields = new UserFields();
            userFields.setUsername(true);
            userFields.setMobile(true);
            userFields.setGender(true);
            UserRespMsg userByIdWithMsg = rpcUserRetrieveService.getUserByIdWithMsg(mtUserId, userFields);
            if (userByIdWithMsg == null || userByIdWithMsg.getUser() == null) {
                LogUtils.logFailLog(log, TagContext.builder().action("getMtUserModel").build(),
                        WarnMessage.build("getMtUserModel", "查询美团用户信息失败", ""),
                        mtUserId, userByIdWithMsg);
                return null;
            }
            return userByIdWithMsg.getUser();
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("getMtUserModel").build(),
                    WarnMessage.build("getMtUserModel", "查询美团用户信息失败", ""),
                    mtUserId, e);
            return null;
        }
    }

    @Override
    public Long getMtRealBindUserId(long dpUserId) {
        if (dpUserId <= 0) {
            return 0L;
        }
        try {
            BindRelationResp bindRelationResp = userMergeQueryService.getRealBindByDpUserId(dpUserId);
            if (bindRelationResp == null || !bindRelationResp.isSuccess()) {
                LogUtils.logFailLog(log, TagContext.builder().build(),
                        WarnMessage.build("getMtRealBindUserId", "根据点评用户ID查询美团实绑定失败", ""),
                        dpUserId, bindRelationResp);
                return 0L;
            }
            return Optional.ofNullable(bindRelationResp.getData()).map(BindRelation::getMtUserId)
                    .map(UserIdModel::getId).orElse(0L);
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("getMtRealBindUserId").build(),
                    WarnMessage.build("getMtRealBindUserId", "点评用户转美团异常", ""),
                    dpUserId, e);
            return 0L;
        }
    }

    @Override
    public Long getDpRealBindUserId(long mtUserId) {
        if (mtUserId <= 0) {
            return 0L;
        }
        try {
            BindRelationResp bindRelationResp = userMergeQueryService.getRealBindByMtUserId(mtUserId);
            if (bindRelationResp == null || !bindRelationResp.isSuccess()) {
                LogUtils.logFailLog(log, TagContext.builder().build(),
                        WarnMessage.build("getDpRealBindUserId", "根据美团用户ID查询点评实绑定失败", ""),
                        mtUserId, bindRelationResp);
                return 0L;
            }
            return Optional.ofNullable(bindRelationResp.getData()).map(BindRelation::getMergedTargetUserId)
                    .map(UserIdModel::getId).orElse(0L);
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("getDpRealBindUserId").build(),
                    WarnMessage.build("getDpRealBindUserId", "美团用户转美团异常", ""),
                    mtUserId, e);
            return 0L;
        }
    }

    @Override
    public List<LabelData> getUserProfile(long mtUserId, List<Integer> tagIds) {
        try {
            List<LabelRequestParam> tagParams = tagIds.stream().map(tagId -> new LabelRequestParam().setId(tagId)).collect(Collectors.toList());
            Id user = new Id().setIdType("MT_USERID").setValue(String.valueOf(mtUserId));
            QueryByIdRequest queryByIdRequest = new QueryByIdRequest()
                    .setIds(Lists.newArrayList(user))
                    .setLabels(tagParams);

            QueryResponse response = upsClient.queryById(queryByIdRequest);
            if (!response.getStatus().equals(ResponseStatus.SUCCESS)) {
                Cat.logEvent("queryProfile", "mt");
                LogUtils.logFailLog(log, TagContext.builder().userId(String.valueOf(mtUserId)).action("queryProfile").build(),
                        new WarnMessage("UserAclService", "美团用户查询用户画像失败", ""), mtUserId, response);
                return Lists.newArrayList();
            }

            LogUtils.logReturnInfo(log, TagContext.builder().userId(String.valueOf(mtUserId)).action("queryProfile").build(),
                    new WarnMessage("UserAclService", "美团用户查询用户画像成功", ""), mtUserId, response);

            return response.getValue().stream().filter(Objects::nonNull).collect(Collectors.toList());
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().userId(String.valueOf(mtUserId)).action("queryProfile").build(),
                    new WarnMessage("UserAclService", "美团用户查询用户画像异常", ""), mtUserId, tagIds, e);
        }

        return Lists.newArrayList();
    }

    @Override
    public UserModel getMtUserByMobile(String userPhone) {
        try {
            UserFields userFields = new UserFields();
            userFields.setUsername(true);
            userFields.setMobile(true);
            userFields.setGender(true);
            UserRespMsg userByIdWithMsg = rpcUserRetrieveService.getUserByMobileWithMsg(userPhone, userFields);
            if (userByIdWithMsg == null || userByIdWithMsg.getUser() == null) {
                LogUtils.logFailLog(log, TagContext.builder().action("getMtUserByMobile").build(),
                                    WarnMessage.build("getMtUserByMobile", "查询美团用户信息失败", ""),
                                    userPhone, userByIdWithMsg);
                return null;
            }
            return userByIdWithMsg.getUser();
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("getMtUserModel").build(),
                                WarnMessage.build("getMtUserByMobile", "查询美团用户信息失败", ""),
                                userPhone, e);
            return null;
        }
    }

    @Override
    public String queryUserPhone(long userId, int platform) {
        if (platform == PlatformEnum.DP.getType()) {
            UserAccountDTO userAccountDTO = getDpUserAccountDTO(userId);
            return Optional.ofNullable(userAccountDTO).map(UserAccountDTO::getMobile).orElse(StringUtils.EMPTY);
        }

        if (platform == PlatformEnum.MT.getType()) {
            UserModel userModel = getMtUserModel(userId);
            return Optional.ofNullable(userModel).map(UserModel::getMobile).orElse(StringUtils.EMPTY);
        }
        return StringUtils.EMPTY;
    }

    @Override
    public UserModel queryUserModel(String imUserId) {
        long userId = ImAccountTypeUtils.getAccountId(imUserId);
        if (ImAccountTypeUtils.isDpUserId(imUserId)) {
            userId = getMtRealBindUserId(userId);
        }
        return getMtUserModel(userId);
    }
}
