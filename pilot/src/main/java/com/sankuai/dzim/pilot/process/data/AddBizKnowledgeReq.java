package com.sankuai.dzim.pilot.process.data;

import com.sankuai.it.xcontract.easypoi.annotation.Excel;
import lombok.Data;

import java.util.List;

@Data
public class AddBizKnowledgeReq {

    /**
     * 业务知识类型
     * @see com.sankuai.dzim.pilot.api.enums.BizKnowledgeTypeEnum
     */
    @Excel(name = "bizType")
    private int bizType;

    /**
     * 问题
     */
    @Excel(name = "question")
    private String question;

    /**
     * 答案
     */
    @Excel(name = "answer")
    private String answer;

    /**
     * 编辑者
     */
    @Excel(name = "editor")
    private String editor;

    /**
     * 关联id
     */
    @Excel(name = "relatedId")
    private Long relatedId;

    /**
     * 要点
     */
    @Excel(name = "keyPoint")
    private String keyPoint;

    @Excel(name = "tagIds")
    private String tagIds;

    @Excel(name = "Meta")
    private String meta;

    @Excel(name = "frequency")
    private Long frequency;
}
