package com.sankuai.dzim.pilot.utils;

import com.sankuai.douhu.absdk.bean.DouHuRequest;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.process.data.AbContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @since 2024/08/22 11:56
 */
@Slf4j
public class DouHuUtils {
    private static final String DP_PLATFORM = "DP";
    private static final String MT_PLATFORM = "MT";

    public static String getDeviceId(AbContext context) {
        return StringUtils.isNotEmpty(context.getUnionId())
                ? context.getUnionId()
                : context.getDeviceId();
    }

    public static String getPlatform(int platform) {
        return platform == 1 ? DP_PLATFORM : MT_PLATFORM;
    }

    public static DouHuRequest buildDouHuRequestByDeviceIdOrUnionId(AbContext context, String expId) {
        String deviceId = getDeviceId(context);
        logIfAbParamInvalid(expId, deviceId, context);
        DouHuRequest request = new DouHuRequest();
        request.setExpId(expId);
        request.setPlatform(getPlatform(context.getPlatform()));
        request.setUnionId(deviceId);
        request.setCityId(context.getCityId());
        request.setOs(context.getOs());
        request.setAppClient(context.getAppVersion());
        request.setUserId(context.getUserId());
        return request;
    }
    private static void logIfAbParamInvalid(String expId, String deviceId, AbContext context) {
        if (StringUtils.isEmpty(expId)) {
            LogUtils.logFailLog(log, TagContext.builder().build(),
                    WarnMessage.build("buildDouHuRequestByDeviceId", "未配置实验ID", ""), context, null);
        }
        if (StringUtils.isEmpty(deviceId)) {
            LogUtils.logFailLog(log, TagContext.builder().build(),
                    WarnMessage.build("buildDouHuRequestByDeviceId", "设备Id为空", ""), new Object[]{context, expId}, null);
        }
    }
}

