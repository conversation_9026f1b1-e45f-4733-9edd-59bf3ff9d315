package com.sankuai.dzim.pilot.buffer.stream.build.ext.data;

import lombok.Builder;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

@Data
@Builder
public class ThinkProcessData {
    private String loadingText;

    private String finishText;

    public Map<String, Object> toMap(String finishText) {
        Map<String, Object> map = new HashMap<>();
        map.put("loadingText", loadingText);
        map.put("finishText", finishText);
        return map;
    }

    public Map<String, Object> toMap() {
        return toMap(finishText);
    }

}
