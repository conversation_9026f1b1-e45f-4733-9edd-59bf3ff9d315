package com.sankuai.dzim.pilot.process.localplugin;

import com.alibaba.fastjson.JSONObject;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.dto.MessageDTO;
import com.sankuai.dzim.pilot.buffer.core.PilotBufferItemDO;
import com.sankuai.dzim.pilot.buffer.utils.BufferUtils;
import com.sankuai.dzim.pilot.chain.data.AIAnswerData;
import com.sankuai.dzim.pilot.chain.data.AIServiceContext;
import com.sankuai.dzim.pilot.chain.enums.AIServiceExtraKeyEnum;
import com.sankuai.dzim.pilot.domain.annotation.LocalPlugin;
import com.sankuai.dzim.pilot.gateway.api.assistant.data.SendBeamMessageReq;
import com.sankuai.dzim.pilot.process.localplugin.param.BeamReserveTaskParam;
import com.sankuai.dzim.pilot.process.localplugin.param.Param;
import com.sankuai.dzim.pilot.utils.AIChatUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;

@Component
@Slf4j
public class BeamReserveTaskPlugin {

    @Autowired
    private AIChatUtil aiChatUtil;

    private static final String TOOL_NAME = "ReserveTaskTool";

    private static final String TOOL_DESC = "用户预约相关的指令（发起预约/确认预约/取消预约）必须调用此工具，禁止直接输出预约成功或者预约已取消";

    @LocalPlugin(name = TOOL_NAME, description = TOOL_DESC, returnDirect = true)
    public AIAnswerData dispatchTask(BeamReserveTaskParam param) {
        LogUtils.logRequestLog(log, TagContext.builder().interfaceName(TOOL_NAME).build(), "BeamReserveTaskPlugin", param);

        // 修复AIServiceContext中的sendBeamMessageReq参数问题
        fixAIServiceContext(param);
        // 获取用户原始输入
        String userInput = getUserInput(param);
        // 预约Agent
        AIAnswerData aiAnswerData = aiChatUtil.chat("BeamReserveBookAgent", userInput, getExtraInfo(param));
        return aiAnswerData;
    }

    private String getUserInput(BeamReserveTaskParam param) {
        String userInput = Optional.ofNullable(param)
                .map(Param::getAiServiceContext)
                .map(AIServiceContext::getMessageDTO)
                .map(MessageDTO::getMessage)
                .orElse(null);
        return StringUtils.isNotBlank(userInput) ? userInput : param.getTaskDescription();
    }

    private void fixAIServiceContext(BeamReserveTaskParam param) {
        // 调用工具参数会改变sendBeamMessageReq类型，这里转换回去
        Object o = Optional.ofNullable(param)
                .map(Param::getAiServiceContext)
                .map(AIServiceContext::getExtraInfo)
                .map(extraInfo -> MapUtils.getObject(extraInfo, AIServiceExtraKeyEnum.BEAM_REQUEST_CONTEXT.getKey(), null))
                .orElse(null);
        if (o instanceof JSONObject) {
            JSONObject jsonObject = (JSONObject) o;
            SendBeamMessageReq sendBeamMessageReq = JsonCodec.decode(jsonObject.toJSONString(), SendBeamMessageReq.class);
            param.getAiServiceContext().getExtraInfo().put(AIServiceExtraKeyEnum.BEAM_REQUEST_CONTEXT.getKey(), sendBeamMessageReq);
        }
    }

    private AIAnswerData writeAnswerIfNeeded(AIAnswerData aiAnswerData) {
        //如果子Agent未直接返回，回复这句话
        if (!aiAnswerData.isReturnDirect()) {
            BufferUtils.writeMainTextBuffer(PilotBufferItemDO.builder().data(aiAnswerData.getAnswer()).build());
        }
        return aiAnswerData;
    }

    private Map<String, Object> getExtraInfo(BeamReserveTaskParam param) {
        return Optional.ofNullable(param)
                .map(Param::getAiServiceContext)
                .map(AIServiceContext::getExtraInfo)
                .orElse(null);
    }
}
