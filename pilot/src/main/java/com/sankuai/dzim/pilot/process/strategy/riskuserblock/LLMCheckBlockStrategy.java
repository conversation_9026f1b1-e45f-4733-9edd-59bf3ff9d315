package com.sankuai.dzim.pilot.process.strategy.riskuserblock;

import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.meituan.mafka.client.bean.MafkaProducer;
import com.meituan.mtrace.Tracer;
import com.sankuai.dzim.pilot.acl.FridayAclService;
import com.sankuai.dzim.pilot.acl.data.fraiday.ChatCompletionRequest;
import com.sankuai.dzim.pilot.acl.data.fraiday.ChatCompletionResponse;
import com.sankuai.dzim.pilot.acl.data.fraiday.FridayChoice;
import com.sankuai.dzim.pilot.acl.data.fraiday.FridayMessage;
import com.sankuai.dzim.pilot.buffer.enums.StreamEventCardTypeEnum;
import com.sankuai.dzim.pilot.buffer.enums.StreamEventDataTypeEnum;
import com.sankuai.dzim.pilot.buffer.stream.vo.StreamEventCardDataVO;
import com.sankuai.dzim.pilot.buffer.stream.vo.StreamEventDataVO;
import com.sankuai.dzim.pilot.buffer.stream.vo.StreamEventVO;
import com.sankuai.dzim.pilot.dal.entity.pilot.BlockListEntity;
import com.sankuai.dzim.pilot.dal.entity.pilot.LLMQualityCheckEntity;
import com.sankuai.dzim.pilot.dal.entity.pilot.PilotChatMessageEntity;
import com.sankuai.dzim.pilot.domain.RiskUserBlockDomainService;
import com.sankuai.dzim.pilot.enums.CheckQualityReasonEnum;
import com.sankuai.dzim.pilot.gateway.enums.BlockTypeEnum;
import com.sankuai.dzim.pilot.gateway.mq.data.BlockStrategyConstant;
import com.sankuai.dzim.pilot.process.UserBlockProcessService;
import com.sankuai.dzim.pilot.process.data.LLMQualityCheckConfig;
import com.sankuai.dzim.pilot.process.data.BlockStrategyConfig;
import com.sankuai.dzim.pilot.gateway.mq.data.RiskBlockMessageData;
import com.sankuai.dzim.pilot.process.strategy.RiskUserBlockStrategy;
import com.sankuai.dzim.pilot.utils.LionConfigUtil;
import com.sankuai.dzim.pilot.utils.ProducerUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

@Component
@Slf4j
public class LLMCheckBlockStrategy implements RiskUserBlockStrategy {
    @Resource(name = "redisClient")
    private RedisStoreClient dzimRedisClient;

    @Autowired
    private LionConfigUtil lionConfigUtil;

    @Autowired
    private UserBlockProcessService userBlockProcessService;

    @Override
    public boolean accept(String name) {
        return name.equals(BlockTypeEnum.LLMBLOCK_STRATEGY.getBlockStrategy());
    }

    @Override
    public void execute(BlockStrategyConfig strategyConfig, RiskBlockMessageData riskMsg) {
        Integer llmResult = riskMsg.getLlmResult();
        addCheckQualityToRedis(llmResult, riskMsg, strategyConfig);
    }

    @Override
    public StoreKey buildKey(Integer assistantType, String userId) {
        return new StoreKey(BlockStrategyConstant.RISK_USER_BLOCK_KEY, BlockTypeEnum.LLMBLOCK_STRATEGY.getBlockStrategy(), assistantType, userId);
    }

    // 质检结果落表 缓存 拉黑
    public void addCheckQualityToRedis(Integer llmResult, RiskBlockMessageData riskMsg, BlockStrategyConfig strategyConfig) {
        if (llmResult == null) {
            return;
        }
        if (llmResult == CheckQualityReasonEnum.PROMPT_ERROR.getVal() || llmResult == CheckQualityReasonEnum.YELLOW_GAMBLING_DRUG_POLITICS.getVal()) {
            StoreKey storeKey = buildKey(riskMsg.getReq().getAssistantType(), riskMsg.getReq().getImUserId());
            userBlockProcessService.blockUser(storeKey, strategyConfig, riskMsg, BlockTypeEnum.LLMBLOCK_STRATEGY);
        }
    }
}
