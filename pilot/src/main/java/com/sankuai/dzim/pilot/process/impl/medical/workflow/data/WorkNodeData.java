package com.sankuai.dzim.pilot.process.impl.medical.workflow.data;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * @author: zhouyibing
 * @date: 2024/9/5
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WorkNodeData {

    private String name;

    private String showName;

    private Map<String, Object> input;

    private Map<String, Object> output;
}
