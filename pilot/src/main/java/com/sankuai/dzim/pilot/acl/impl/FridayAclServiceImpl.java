package com.sankuai.dzim.pilot.acl.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.tea.TeaRequest;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.TextNode;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.acl.FridayAclService;
import com.sankuai.dzim.pilot.acl.annotation.ModelFallback;
import com.sankuai.dzim.pilot.acl.data.ThirdPartyService;
import com.sankuai.dzim.pilot.acl.data.fraiday.*;
import com.sankuai.dzim.pilot.acl.data.fraiday.search.SearchEngineService;
import com.sankuai.dzim.pilot.acl.data.fraiday.search.data.Result;
import com.sankuai.dzim.pilot.acl.data.fraiday.search.data.SearchEngineRequest;
import com.sankuai.dzim.pilot.acl.data.fraiday.search.data.SearchEngineResponse;
import com.sankuai.dzim.pilot.acl.data.fraiday.sft.AliCloudService;
import com.sankuai.dzim.pilot.buffer.core.PilotBufferItemDO;
import com.sankuai.dzim.pilot.buffer.utils.BufferUtils;
import com.sankuai.dzim.pilot.dal.cache.UserAIRecommendFreqRedis;
import com.sankuai.dzim.pilot.domain.annotation.CatLog;
import com.sankuai.dzim.pilot.domain.data.RedisKeys;
import com.sankuai.dzim.pilot.utils.CatUtils;
import com.sankuai.dzim.pilot.utils.ImageUtil;
import com.sankuai.dzim.pilot.utils.JsonUtils;
import com.sankuai.dzim.pilot.utils.VenusUtil;
import com.sankuai.dzim.pilot.utils.Log2HiveUtils;
import com.sankuai.dzim.pilot.utils.context.BusinessKeyConstant;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.NameValuePair;
import org.apache.http.client.utils.URLEncodedUtils;
import org.apache.shiro.util.Assert;
import org.springframework.stereotype.Service;
import retrofit2.Call;
import retrofit2.Response;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;
import retrofit2.converter.jackson.JacksonConverterFactory;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class FridayAclServiceImpl implements FridayAclService {

    @Resource
    private UserAIRecommendFreqRedis userAIRecommendFreqRedis;

    private static final List<String> STOP_TOKEN = Lists.newArrayList("[DONE]", "data: [DONE]");

    private static final List<String> LONG_CAT_SFT_MODEL = Lists.newArrayList("LongCat_13B_yimei_kefu");

    @MdpConfig("com.sankuai.mim.pilot.model.ali.access.key:{}")
    private String aliAccessKey;

    @MdpConfig("com.sankuai.mim.pilot.model.ali.access.secret:{}")
    private String aliAccessSecret;

    private static final String THIRD_URL = "https://api.302ai.cn";

    private static final String ZZZ_URL = "https://api.zhizengzeng.com";

    @Resource(name = "tollPhoneRedisClient")
    private RedisStoreClient redisStoreClient;

    /**
     * friday服务
     */
    private static final FridayService fridayService = new Retrofit.Builder()
            .baseUrl("https://aigc.sankuai.com")
            .addConverterFactory(JacksonConverterFactory.create(new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)))
            .client(new OkHttpClient.Builder()
                    .connectTimeout(10, TimeUnit.SECONDS)
                    .readTimeout(30, TimeUnit.SECONDS)
                    .writeTimeout(30, TimeUnit.SECONDS).build())
            .build().create(FridayService.class);

    /**
     * 第三方服务
     */
    private static final ThirdPartyService thirdService = new Retrofit.Builder()
            .baseUrl(THIRD_URL)
            .addConverterFactory(JacksonConverterFactory.create(new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)))
            .client(new OkHttpClient.Builder()
                    .connectTimeout(10, TimeUnit.SECONDS)
                    .readTimeout(30, TimeUnit.SECONDS)
                    .writeTimeout(30, TimeUnit.SECONDS).build())
            .build().create(ThirdPartyService.class);

    private static final ThirdPartyService zzzImageService = new Retrofit.Builder()
            .baseUrl(ZZZ_URL)
            .addConverterFactory(JacksonConverterFactory.create(new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)))
            .client(new OkHttpClient.Builder()
                    .connectTimeout(60, TimeUnit.SECONDS)
                    .readTimeout(300, TimeUnit.SECONDS)
                    .writeTimeout(300, TimeUnit.SECONDS).build())
            .build().create(ThirdPartyService.class);


    /**
     * 阿里云服务
     */
    private static final AliCloudService aliCloudService = new Retrofit.Builder()
            .baseUrl("http://dzpilot.icu")
            .addConverterFactory(JacksonConverterFactory.create(new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)))
            .client(new OkHttpClient.Builder()
                    .connectTimeout(10, TimeUnit.SECONDS)
                    .readTimeout(30, TimeUnit.SECONDS)
                    .writeTimeout(30, TimeUnit.SECONDS).build())
            .build().create(AliCloudService.class);

    /**
     * 流式请求客户端
     */
    private static final OkHttpClient client = new OkHttpClient.Builder()
            .connectTimeout(100, TimeUnit.SECONDS)
            .readTimeout(300, TimeUnit.SECONDS)
            .writeTimeout(300, TimeUnit.SECONDS)
            .build();

    /**
     * friday搜索服务
     */
    private static final SearchEngineService searchEngineService = new Retrofit.Builder()
            .baseUrl("https://aigc.sankuai.com")
            .addConverterFactory(GsonConverterFactory.create())
            .client(new OkHttpClient.Builder()
                    .connectTimeout(10, TimeUnit.SECONDS)
                    .readTimeout(30, TimeUnit.SECONDS)
                    .writeTimeout(30, TimeUnit.SECONDS).build())
            .build().create(SearchEngineService.class);

    private static final ObjectMapper objectMapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);


    @Override
    @CatLog(name = "embedding")
    @ModelFallback
    public List<Double> embedding(String appId, String model, int tokenLimit, String text) {
        if (StringUtils.isBlank(text)) {
            return Lists.newArrayList();
        }
        StoreKey aigcEmbeddingKey = RedisKeys.getAigcEmbeddingKey(model, text);
        if (redisStoreClient.exists(aigcEmbeddingKey)) {
            return redisStoreClient.get(aigcEmbeddingKey);
        }

        List<Double> vector = embeddingText(appId, buildEmbeddingRequest(model, text));
        try {
            redisStoreClient.set(aigcEmbeddingKey, vector, 600);
        } catch (Exception e) {
            log.error("embedding缓存写入失败, model: {}, text: {}.", model, text, e);
        }
        return vector;
    }

    @Override
    @CatLog(name = "embedding")
    @ModelFallback
    public List<Double> embedding(String appId, EmbeddingRequest request) {
        if (StringUtils.isBlank(request.getInput()) || StringUtils.isBlank(request.getModel())) {
            return Lists.newArrayList();
        }

        return embeddingText(appId, request);
    }

    private List<Double> embeddingText(String appId, EmbeddingRequest request) {
        long startTime = System.currentTimeMillis();
        Transaction transaction = Cat.newTransaction("embedding", request.getModel());
        try {
            metricFridayCnt("embedding", request.getModel(), appId);
            Call<EmbeddingResponse> embedding = fridayService.embedding(appId, request);
            Response<EmbeddingResponse> response = embedding.execute();
            if (!response.raw().isSuccessful() || response.body() == null) {
                LogUtils.logReturnInfo(log, TagContext.builder().action("embedding: Fail").bizId(appId).build(),
                        new WarnMessage("FridaDayAclService", "embedding失败", response.message()), request, response.raw().body());
                metricFridayFail("embedding", request.getModel(), appId, response.code());
                transaction.setStatus("fail");
                return Lists.newArrayList();
            }
            List<Double> result = parseEmbeddingResponse(response);
            if (CollectionUtils.isEmpty(result)) {
                transaction.setStatus("fail");
                return result;
            }
            metricFridaySucc("embedding", request.getModel(), appId);
            transaction.setSuccessStatus();
            return result;
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("embedding: error").bizId(appId).build(),
                    new WarnMessage("FridaDayAclService", "embedding异常", ""), request, e);
            metricFridayFail("embedding", request.getModel(), appId, -1);
            transaction.setStatus(e);
            return Lists.newArrayList();
        } finally {
            transaction.setDurationInMillis(System.currentTimeMillis() - startTime);
            transaction.complete();
        }
    }

    private EmbeddingRequest buildEmbeddingRequest(String model, String text) {
        EmbeddingRequest embeddingRequest = new EmbeddingRequest();
        embeddingRequest.setInput(text);
        embeddingRequest.setModel(model);
        return embeddingRequest;
    }

    private List<Double> parseEmbeddingResponse(Response<EmbeddingResponse> response) {
        if (response == null || response.body() == null) {
            return Lists.newArrayList();
        }
        EmbeddingResponse embeddingResponse = response.body();
        if (CollectionUtils.isEmpty(embeddingResponse.getData())) {
            return Lists.newArrayList();
        }
        return embeddingResponse.getData().get(0).getEmbedding();
    }

    private int calculateTextToken(String text, String modelName) {
        ChatTokenRequest chatTokenRequest = new ChatTokenRequest();
        chatTokenRequest.setModel(modelName);
        chatTokenRequest.setMessages(Lists.newArrayList(new FridayMessage(FridayMessageRoleEnum.USER.getValue(), text)));
        try {
            Call<ChatTokenResponse> tokenResponseCall = fridayService.tikToken(chatTokenRequest);
            Response<ChatTokenResponse> response = tokenResponseCall.execute();
            if (!response.isSuccessful() || response.body() == null) {
                LogUtils.logFailLog(log, TagContext.builder().action("Friday: calculateToken").build(),
                        new WarnMessage("FridayAclService", "token计数调用失败", ""), chatTokenRequest, response.raw().body());
                return 0;
            }
            return response.body().getTokenLen();
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("Friday: calculateToken").build(),
                    new WarnMessage("FridayAclService", "token计数调用异常", ""), chatTokenRequest, e);
            return 0;
        }
    }

    /**
     * ModelFallback手动注解方法，否则流式调用存在问题
     *
     * @param appId   小美租户
     * @param request 请求
     * @return
     */
    @Override
    @CatLog(name = "chatCompletion")
    @ModelFallback
    public ChatCompletionResponse chatCompletion(String appId, ChatCompletionRequest request) {
        if (request == null || StringUtils.isBlank(request.getModel())) {
            return null;
        }

        if (!userAIRecommendFreqRedis.fridayFrequencyLimit(appId, request)) {
            return null;
        }

        // 自研模型
        if (request.getModel().contains("LongCat")) {
            return getChatCompletionMT(appId, request);
        }

        // 外部sft模型
        if (request.getModel().contains("ft:")) {
            return getChatCompletionSft(appId, request);
        }


        // OpenAI
        return getChatCompletionOpenAI(appId, request);
    }

    private ChatCompletionResponse getChatCompletionMT(String appId, ChatCompletionRequest request) {
        // 自研微调模型,需要把messages重新调整一下,systemPrompt放在最后一条消息后面
        if (LONG_CAT_SFT_MODEL.contains(request.getModel())) {
            updateRequest(request);
        }
        try {
            if (request.isStream()) {
                return getChatCompletionMtStream(appId, request);
            }

            long startTime = System.currentTimeMillis();
            Call<MTModelResponse> chatCompletion = null;
            metricFridayCnt("chatCompletion", request.getModel(), appId);
            chatCompletion = fridayService.createChatCompletionMT("Bearer " + appId, request);

            Response<MTModelResponse> response = chatCompletion.execute();

            CatUtils.catMethodTransaction("getChatCompletionMT", startTime);
            if (!response.raw().isSuccessful() || response.body() == null) {
                LogUtils.logReturnInfo(log, TagContext.builder().action("chatCompletionMT: Fail").bizId(appId).build(),
                        new WarnMessage("FridayAclService", "自研模型生成内容失败", response.message()), request, new Object[]{response.raw(),
                                response.errorBody() != null ? response.errorBody().string() : null, response.raw().header("M-TraceId")});
                metricFridayFail("chatCompletion", request.getModel(), appId, response.code());
                return null;
            }

            if (response.body().getData() == null || CollectionUtils.isEmpty(response.body().getData().getChoices())) {
                LogUtils.logReturnInfo(log, TagContext.builder().action("chatCompletionMT: Empty").bizId(appId).build(),
                        new WarnMessage("FridayAclService", "自研模型生成内容为空", response.message()), request, response.body());
                return null;
            }

            LogUtils.logReturnInfo(log, TagContext.builder().action("chatCompletion: Success").bizId(appId).build(),
                    new WarnMessage("FridayAclService", "自研模型生成内容成功", ""), request, response.body());
            metricFridaySucc("chatCompletion", request.getModel(), appId);
            Log2HiveUtils.addFridayAnswerLog(appId, request, response.body().getData());
            return response.body().getData();
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("OpenAI: getAnswer error").bizId(appId).build(),
                    new WarnMessage("FridayAclService", "自研生成内容异常", ""), request, e);
            metricFridayFail("chatCompletion", request.getModel(), appId, -1);
        }
        return null;
    }

    private void updateRequest(ChatCompletionRequest request) {
        if (request == null || CollectionUtils.isEmpty(request.getMessages()) || request.getMessages().size() < 2) {
            return;
        }

        FridayMessage fridayMessage = request.getMessages().get(0);
        FridayMessage userMessage = request.getMessages().get(request.getMessages().size() - 1);
        if (!fridayMessage.getRole().equals(FridayMessageRoleEnum.SYSTEM.getValue()) || !userMessage.getRole().equals(FridayMessageRoleEnum.USER.getValue())) {
            return;
        }

        request.getMessages().remove(0);
        userMessage.setContent(userMessage.getContent() + "\n\n\n请你遵循以下指令: \n" + fridayMessage.getContent());
    }

    private ChatCompletionResponse getChatCompletionOpenAI(String appId, ChatCompletionRequest request) {
        try {
            if (request.isStream()) {
                return getChatCompletionOpenAIStream(appId, request);
            }
            long startTime = System.currentTimeMillis();
            Call<ChatCompletionResponse> chatCompletion = null;

            metricFridayCnt("chatCompletion", request.getModel(), appId);
            chatCompletion = fridayService.createChatCompletion(appId, request);

            Response<ChatCompletionResponse> response = chatCompletion.execute();

            CatUtils.catMethodTransaction("getChatCompletionOpenAI", startTime);
            if (!response.raw().isSuccessful() || response.body() == null) {
                LogUtils.logReturnInfo(log, TagContext.builder().action("chatCompletion: Fail").bizId(appId).build(),
                        new WarnMessage("FridayAclService", "生成内容失败", response.message()), request, new Object[]{response.raw(),
                                response.errorBody() != null ? response.errorBody().string() : null, response.raw().header("M-TraceId")});
                metricFridayFail("chatCompletion", request.getModel(), appId, response.code());
                ChatCompletionResponse chatCompletionResponse = new ChatCompletionResponse();
                chatCompletionResponse.setError(response.message());
                chatCompletionResponse.setCode(response.code());
                return chatCompletionResponse;
            }

            LogUtils.logReturnInfo(log, TagContext.builder().action("chatCompletion: Success").bizId(appId).build(),
                    new WarnMessage("FridayAclService", "生成内容成功", ""), request, response.body());
            metricFridaySucc("chatCompletion", request.getModel(), appId);
            Log2HiveUtils.addFridayAnswerLog(appId, request, response.body());
            return response.body();
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("OpenAI: getAnswer error").bizId(appId).build(),
                    new WarnMessage("FridayAclService", "生成内容异常", ""), request, e);
            metricFridayFail("chatCompletion", request.getModel(), appId, -1);
        }
        return null;
    }

    private ChatCompletionResponse getChatCompletionSft(String appId, ChatCompletionRequest request) {
        try {
            if (request.isStream()) {
                return getChatCompletionAliStream(appId, request);
            }
            long startTime = System.currentTimeMillis();
            Call<ChatCompletionResponse> chatCompletion = null;

            metricFridayCnt("chatCompletion", request.getModel(), appId);
            Map<String, String> auth = getAliCloudAuthParam("POST", "http://dzpilot.icu/dzpilot/sft/completion");
            if (MapUtils.isEmpty(auth)) {
                LogUtils.logReturnInfo(log, TagContext.builder().action("chatCompletionAli: authFail").bizId(appId).build(),
                        new WarnMessage("FridayAclService", "阿里云鉴权失败", ""), request, null);
                metricFridayFail("chatCompletion", request.getModel(), appId, -2);
                return null;
            }
            chatCompletion = aliCloudService.createChatCompletion(auth.get("x-acs-date"), auth.get("authorization"), request);

            Response<ChatCompletionResponse> response = chatCompletion.execute();

            CatUtils.catMethodTransaction("getChatCompletionOpenAI", startTime);
            if (!response.raw().isSuccessful() || response.body() == null) {
                LogUtils.logReturnInfo(log, TagContext.builder().action("chatCompletionAli: Fail").bizId(appId).build(),
                        new WarnMessage("FridayAclService", "外部微调生成内容失败", response.message()), request, new Object[]{response.raw(),
                                response.errorBody() != null ? response.errorBody().string() : null, response.raw().header("M-TraceId")});
                metricFridayFail("chatCompletion", request.getModel(), appId, response.code());
                ChatCompletionResponse chatCompletionResponse = new ChatCompletionResponse();
                chatCompletionResponse.setError(response.message());
                chatCompletionResponse.setCode(response.code());
                return chatCompletionResponse;
            }

            LogUtils.logReturnInfo(log, TagContext.builder().action("chatCompletionAli: Success").bizId(appId).build(),
                    new WarnMessage("FridayAclService", "生成内容成功", ""), request, response.body());
            metricFridaySucc("chatCompletion", request.getModel(), appId);
            return response.body();
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("chatCompletionAli: getAnswer error").bizId(appId).build(),
                    new WarnMessage("FridayAclService", "外部微调生成内容异常", ""), request, e);
            metricFridayFail("chatCompletion", request.getModel(), appId, -1);
        }
        return null;
    }

    public Map<String, String> getAliCloudAuthParam(String method, String url) {
        try {
            Map<String, String> headers = new HashMap<String, String>();
            String date = Instant.now().toString();
            headers.put("x-acs-date", date);

            URI uri = new URI(url);
            Map<String, String> query = new HashMap<String, String>();
            for (NameValuePair pair : URLEncodedUtils.parse(uri, StandardCharsets.UTF_8)) {
                query.put(pair.getName(), pair.getValue());
            }
            TeaRequest req = new TeaRequest();
            req.method = method;
            req.pathname = uri.getPath().replace("$", "%24");
            req.headers = headers;
            req.query = query;

            String auth = com.aliyun.openapiutil.Client.getAuthorization(
                    req, "ACS3-HMAC-SHA256", "", aliAccessKey, aliAccessSecret);
            headers.put("authorization", auth);

            return headers;
        } catch (Exception e) {
            log.error("getAliCloudAuthParam error, url:{}, method:{}.", url, method, e);
        }
        return Maps.newHashMap();
    }

    @Override
    @CatLog(name = "chatCompletionMulti")
    @ModelFallback
    public ChatCompletionResponse chatCompletionMulti(String appId, ChatCompletionMultiRequest request) {
        try {
            Call<ChatCompletionResponse> chatCompletion = null;

            metricFridayCnt("chatCompletionMulti", request.getModel(), appId);
            if ("gemini-2.0-flash-exp-image-generation".equals(request.getModel())) {
                chatCompletion = thirdService.createChatCompletionMulti(appId, request);
            } else {
                chatCompletion = fridayService.createChatCompletionMulti(appId, request);
            }

            Response<ChatCompletionResponse> response = chatCompletion.execute();

            if (!response.raw().isSuccessful() || response.body() == null) {
                LogUtils.logReturnInfo(log, TagContext.builder().action("chatCompletion: Fail").bizId(appId).build(),
                        new WarnMessage("FridayAclService", "生成内容失败", response.message()), request, new Object[]{response.raw(),
                                response.errorBody() != null ? response.errorBody().string() : null, response.raw().header("M-TraceId")});
                metricFridayFail("chatCompletionMulti", request.getModel(), appId, response.code());
                return null;
            }

            LogUtils.logReturnInfo(log, TagContext.builder().action("chatCompletion: Success").bizId(appId).build(),
                    new WarnMessage("FridayAclService", "生成内容成功", ""), request, response.body());
            metricFridaySucc("chatCompletionMulti", request.getModel(), appId);
            return response.body();
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("OpenAI: getAnswer error").bizId(appId).build(),
                    new WarnMessage("FridayAclService", "生成内容异常", ""), request, e);
            metricFridayFail("chatCompletionMulti", request.getModel(), appId, -1);
        }
        return null;
    }

    private ChatCompletionResponse getChatCompletionOpenAIStream(String appId, ChatCompletionRequest request) {
        try {
            metricFridayCnt("chatCompletionStream", request.getModel(), appId);
            MediaType JSON = MediaType.get("application/json; charset=utf-8");
            RequestBody body = RequestBody.create(objectMapper.writeValueAsString(request), JSON);
            Request httpRequest = new Request.Builder()
                    .url("https://aigc.sankuai.com/v1/openai/native/chat/completions")
                    .post(body).header("Authorization", "Bearer " + appId)
                    .build();
            ChatCompletionResponse chatCompletionResponse = parseStream(appId, request, httpRequest);
            LogUtils.logReturnInfo(log, TagContext.builder().action("chatCompletion: Success").bizId(appId).build(),
                    new WarnMessage("FridayAclService", "流式生成内容成功", ""), request, chatCompletionResponse);
            metricFridaySucc("chatCompletionStream", request.getModel(), appId);
            Log2HiveUtils.addFridayAnswerLog(appId, request, chatCompletionResponse);
            return chatCompletionResponse;
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("OpenAIStream: getAnswer error").bizId(appId).build(),
                    new WarnMessage("FridayAclService", "生成流式内容异常", ""), request, e);
            metricFridayFail("chatCompletionStream", request.getModel(), appId, -1);
        }
        return null;
    }

    private ChatCompletionResponse getChatCompletionAliStream(String appId, ChatCompletionRequest request) {
        try {
            metricFridayCnt("chatCompletionStream", request.getModel(), appId);
            Map<String, String> auth = getAliCloudAuthParam("POST", "http://dzpilot.icu/dzpilot/sft/completion");
            if (MapUtils.isEmpty(auth)) {
                LogUtils.logReturnInfo(log, TagContext.builder().action("chatCompletionAli: authFail").bizId(appId).build(),
                        new WarnMessage("FridayAclService", "阿里云鉴权失败", ""), request, null);
                metricFridayFail("chatCompletionStream", request.getModel(), appId, -2);
                return null;
            }
            MediaType JSON = MediaType.get("application/json; charset=utf-8");
            RequestBody body = RequestBody.create(objectMapper.writeValueAsString(request), JSON);
            Request httpRequest = new Request.Builder()
                    .url("http://dzpilot.icu/dzpilot/sft/completion")
                    .post(body).header("x-acs-date", auth.get("x-acs-date")).addHeader("authorization", auth.get("authorization"))
                    .build();
            ChatCompletionResponse chatCompletionResponse = parseStream(appId, request, httpRequest);
            LogUtils.logReturnInfo(log, TagContext.builder().action("chatCompletionAli: Success").bizId(appId).build(),
                    new WarnMessage("FridayAclService", "流式生成内容成功", ""), request, chatCompletionResponse);
            metricFridaySucc("chatCompletionStream", request.getModel(), appId);
            return chatCompletionResponse;
        } catch (Exception e) {
            metricFridayFail("chatCompletionStream", request.getModel(), appId, -1);
            LogUtils.logFailLog(log, TagContext.builder().action("chatCompletionAli: getAnswer error").bizId(appId).build(),
                    new WarnMessage("FridayAclService", "生成流式内容异常", ""), request, e);
        }
        return null;
    }

    private ChatCompletionResponse getChatCompletionMtStream(String appId, ChatCompletionRequest request) {
        try {
            metricFridayCnt("chatCompletionStream", request.getModel(), appId);
            MediaType JSON = MediaType.get("application/json; charset=utf-8");
            RequestBody body = RequestBody.create(objectMapper.writeValueAsString(request), JSON);
            Request httpRequest = new Request.Builder()
                    .url("https://aigc.sankuai.com/v1/host-model/sankuai/inference")
                    .post(body).header("Authorization", "Bearer " + appId)
                    .build();
            ChatCompletionResponse chatCompletionResponse = parseStream(appId, request, httpRequest);
            LogUtils.logReturnInfo(log, TagContext.builder().action("chatCompletionMt: Success").bizId(appId).build(),
                    new WarnMessage("FridayAclService", "流式生成内容成功", ""), request, chatCompletionResponse);
            metricFridaySucc("chatCompletionStream", request.getModel(), appId);
            Log2HiveUtils.addFridayAnswerLog(appId, request, chatCompletionResponse);
            return chatCompletionResponse;
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("MtStream: getAnswer error").bizId(appId).build(),
                    new WarnMessage("FridayAclService", "生成流式内容异常", ""), request, e);
            metricFridayFail("chatCompletionStream", request.getModel(), appId, -1);
        }
        return null;
    }

    private ChatCompletionResponse parseStream(String appId, ChatCompletionRequest request, Request httpRequest) {
        try (okhttp3.Response response = client.newCall(httpRequest).execute()) {
            if (!response.isSuccessful()) {
                LogUtils.logReturnInfo(log, TagContext.builder().action("chatCompletionStream: Fail").bizId(appId).build(),
                        new WarnMessage("FridayAclService", "生成流式内容失败", response.message()), request, new Object[]{response.code(),
                                response.body() != null ? response.body().string() + ", " + response.message() : null, response.headers().get("M-TraceId")});
                ChatCompletionResponse chatCompletionResponse = new ChatCompletionResponse();
                chatCompletionResponse.setError(response.message());
                chatCompletionResponse.setCode(response.code());
                return chatCompletionResponse;
            }

            if (BusinessKeyConstant.TOOL_CALL_ASSISTANT.equals(request.getBusinessKey())) {
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(response.body().byteStream()))) {
                    return readStreamWithToolCall(reader, request);
                } catch (Exception e) {
                    LogUtils.logFailLog(log, TagContext.builder().action("Stream: getAnswer error").bizId(appId).build(),
                            new WarnMessage("FridayAclService", "读取流式内容异常", ""), request, e);
                }
            } else {
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(response.body().byteStream()))) {
                    return readStream(reader, request);
                } catch (Exception e) {
                    LogUtils.logFailLog(log, TagContext.builder().action("Stream: getAnswer error").bizId(appId).build(),
                            new WarnMessage("FridayAclService", "读取流式内容异常", ""), request, e);
                }
            }
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("Stream: getAnswer error").bizId(appId).build(),
                    new WarnMessage("FridayAclService", "生成流式内容异常", ""), request, e);
        }
        return null;
    }

    private ChatCompletionResponse readStreamWithToolCall(BufferedReader reader, ChatCompletionRequest request) throws IOException {
        // 最后按照非流式结构返回,function-call使用
        ChatCompletionResponse chatCompletionResponse = new ChatCompletionResponse();
        String line;
        Boolean isFunctionCall = false;
        String arguments = "";
        StringBuilder reasonText = new StringBuilder(StringUtils.EMPTY);

        while ((line = reader.readLine()) != null) {
            if (StringUtils.isEmpty(line)) {
                continue;
            }
            if (STOP_TOKEN.contains(line)) {
                break;
            }
            line = JsonUtils.getJson(line);
            if (StringUtils.isNotEmpty(line)) {
                ChatCompletionStreamResponse oneResponse = JsonCodec.decode(line, ChatCompletionStreamResponse.class);
                if (oneResponse == null) {
                    continue;
                }
                //判断响应类型
                isFunctionCall |= isToolCallResponse(oneResponse, isFunctionCall);
                // 文本输出
                if (!isFunctionCall) {
                    readTextStream(chatCompletionResponse, oneResponse, reasonText, request);
                }

                // function-call输出
                if (isFunctionCall) {
                    arguments = readFunctionCallStream(chatCompletionResponse, oneResponse, arguments);
                }
            }
        }
        return chatCompletionResponse;
    }

    private Boolean isToolCallResponse(ChatCompletionStreamResponse oneResponse, Boolean isFunctionCall) {
        BulkingData delta = Optional.ofNullable(oneResponse)
                .map(ChatCompletionStreamResponse::getChoices)
                .filter(CollectionUtils::isNotEmpty)
                .map(choices -> choices.get(0))
                .map(FridayChoice::getDelta)
                .orElse(null);
        if (delta == null) {
            return false;
        }
        if (StringUtils.isNotBlank(delta.getContent())) {
            return false;
        }

        if (delta.getTool_calls() != null) {
            return true;
        }
        return false;
    }


    private ChatCompletionResponse readStream(BufferedReader reader, ChatCompletionRequest request) throws IOException {
        // 最后按照非流式结构返回,function-call使用
        ChatCompletionResponse chatCompletionResponse = new ChatCompletionResponse();
        String line;
        Boolean isFunctionCall = null;
        String arguments = "";
        StringBuilder reasonText = new StringBuilder(StringUtils.EMPTY);

        while ((line = reader.readLine()) != null) {
            if (StringUtils.isEmpty(line)) {
                continue;
            }
            if (STOP_TOKEN.contains(line)) {
                break;
            }
            line = JsonUtils.getJson(line);
            if (StringUtils.isNotEmpty(line)) {
                ChatCompletionStreamResponse oneResponse = JsonCodec.decode(line, ChatCompletionStreamResponse.class);
                if (oneResponse == null) {
                    continue;
                }
                //判断响应类型
                isFunctionCall = isFunctionCallResponse(oneResponse, isFunctionCall);
                if (isFunctionCall == null) {
                    continue;
                }
                // 文本输出
                if (!isFunctionCall) {
                    readTextStream(chatCompletionResponse, oneResponse, reasonText, request);
                }

                // function-call输出
                if (isFunctionCall) {
                    arguments = readFunctionCallStream(chatCompletionResponse, oneResponse, arguments);
                }
            }
        }

        return chatCompletionResponse;
    }

    private void readTextStream(ChatCompletionResponse chatCompletionResponse, ChatCompletionStreamResponse oneResponse,
                                StringBuilder reasonText, ChatCompletionRequest request) {
        if (oneResponse == null || oneResponse.getChoices() == null || oneResponse.getChoices().isEmpty()) {
            return;
        }
        // 最后一包,转成完整的响应
        if (oneResponse.isLastOne()) {
            convertStream2Complete(chatCompletionResponse, oneResponse, reasonText);
            return;
        }

        String reasonBulking = oneResponse.getChoices().get(0).getDelta().getReasoning_content();
        if (StringUtils.isNotEmpty(reasonBulking)) {
            reasonText.append(reasonBulking);
            BufferUtils.writeReasonTextBuffer(PilotBufferItemDO.builder().data(reasonBulking).build());
        }

        // 往buffer写入数据
        String bulking = oneResponse.getChoices().get(0).getDelta().getContent();
        if (StringUtils.isNotEmpty(bulking)) {
            BufferUtils.writeMainTextBuffer(PilotBufferItemDO.builder().data(bulking).extra(request.getBufferExtra()).build());
        }

    }

    private void convertStream2Complete(ChatCompletionResponse chatCompletionResponse, ChatCompletionStreamResponse oneResponse,
                                        StringBuilder reasonText) {
        chatCompletionResponse.setCreated(oneResponse.getCreated());
        chatCompletionResponse.setModel(oneResponse.getModel());
        chatCompletionResponse.setUsage(oneResponse.getUsage());
        chatCompletionResponse.setObject(oneResponse.getObject());
        List<FridayChoice> choices = Lists.newArrayList();
        FridayChoice fridayChoice = new FridayChoice();
        fridayChoice.setIndex(oneResponse.getChoices().get(0).getIndex());
        fridayChoice.setFinish_reason(oneResponse.getChoices().get(0).getFinish_reason());
        fridayChoice.setMessage(new FridayMessage(FridayMessageRoleEnum.ASSISTANT.getValue(), oneResponse.getContent()));
        fridayChoice.setReasoning_content(new FridayMessage(FridayMessageRoleEnum.ASSISTANT.getValue(), reasonText.toString()));

        choices.add(fridayChoice);
        chatCompletionResponse.setChoices(choices);
    }

    private String readFunctionCallStream(ChatCompletionResponse chatCompletionResponse, ChatCompletionStreamResponse oneResponse, String arguments) throws JsonProcessingException {
        if (oneResponse == null || oneResponse.getChoices() == null || oneResponse.getChoices().isEmpty()) {
            return arguments;
        }

        //初始化
        initFunctionCallResponse(chatCompletionResponse);

        if (oneResponse.isLastOne()) {
            convertFunctionCallStream2Complete(chatCompletionResponse, oneResponse, arguments);
            return arguments;
        }

        // 工具调用不往buffer中写,只收集所有的信息
        List<ToolCallStream> toolCalls = oneResponse.getChoices().get(0).getDelta().getTool_calls();
        if (CollectionUtils.isEmpty(toolCalls)) {
            return arguments;
        }
        ToolCallStream toolCallStream = toolCalls.get(0);
        List<ToolCall> currentToolCalls = chatCompletionResponse.getChoices().get(0).getMessage().getTool_calls();

        // 判断是否要新增一个工具
        arguments = addNewFunction(arguments, toolCallStream, currentToolCalls);

        // 拼接入参
        if (StringUtils.isNotBlank(toolCallStream.getFunction().getArguments())) {
            arguments += toolCallStream.getFunction().getArguments();
        }

        return arguments;
    }

    private String addNewFunction(String arguments, ToolCallStream toolCallStream, List<ToolCall> currentToolCalls) {
        String oldId = CollectionUtils.isEmpty(currentToolCalls) ? "0" : currentToolCalls.get(currentToolCalls.size() - 1).getId();
        if (StringUtils.isNotBlank(toolCallStream.getId()) && !oldId.equals(toolCallStream.getId())) {
            if (CollectionUtils.isEmpty(currentToolCalls)) {
                currentToolCalls.add(buildNewToolCall(toolCallStream));
            }
            if (!toolCallStream.getId().equals(currentToolCalls.get(currentToolCalls.size() - 1).getId())) {
                // 填充上一次工具调用的参数
                currentToolCalls.get(currentToolCalls.size() - 1).getFunction().setArguments(TextNode.valueOf(arguments));
                arguments = "";
                currentToolCalls.add(buildNewToolCall(toolCallStream));
            }
        }
        return arguments;
    }

    private void initFunctionCallResponse(ChatCompletionResponse chatCompletionResponse) {
        if (CollectionUtils.isEmpty(chatCompletionResponse.getChoices())) {
            FridayChoice fridayChoice = new FridayChoice();
            FridayMessage fridayMessage = new FridayMessage(FridayMessageRoleEnum.ASSISTANT.getValue(), null);
            fridayMessage.setTool_calls(Lists.newArrayList());
            fridayChoice.setMessage(fridayMessage);
            chatCompletionResponse.setChoices(Lists.newArrayList(fridayChoice));
        }
    }

    private void convertFunctionCallStream2Complete(ChatCompletionResponse chatCompletionResponse, ChatCompletionStreamResponse oneResponse, String arguments) throws JsonProcessingException {
        chatCompletionResponse.setCreated(oneResponse.getCreated());
        chatCompletionResponse.setModel(oneResponse.getModel());
        chatCompletionResponse.setUsage(oneResponse.getUsage());
        chatCompletionResponse.setObject(oneResponse.getObject());
        chatCompletionResponse.getChoices().get(0).setIndex(oneResponse.getChoices().get(0).getIndex());
        chatCompletionResponse.getChoices().get(0).setFinish_reason(oneResponse.getChoices().get(0).getFinish_reason());
        List<ToolCall> tool_calls = chatCompletionResponse.getChoices().get(0).getMessage().getTool_calls();
        if (CollectionUtils.isNotEmpty(tool_calls)) {
            tool_calls.get(tool_calls.size() - 1).getFunction().setArguments(TextNode.valueOf(arguments));
        }
    }

    private ToolCall buildNewToolCall(ToolCallStream toolCallStream) {
        ToolCall newToolCall = new ToolCall();
        newToolCall.setId(toolCallStream.getId());
        if (toolCallStream.getFunction() != null && StringUtils.isNotBlank(toolCallStream.getFunction().getName())) {
            newToolCall.setFunction(ToolCall.ToolCallDesc.builder().name(toolCallStream.getFunction().getName()).build());
        }
        return newToolCall;
    }

    private Boolean isFunctionCallResponse(ChatCompletionStreamResponse oneResponse, Boolean isFunctionCall) {
        if (isFunctionCall != null) {
            return isFunctionCall;
        }
        BulkingData delta = oneResponse.getChoices().get(0).getDelta();
        if (delta == null) {
            return null;
        }
        if (StringUtils.isNotBlank(delta.getContent()) || StringUtils.isNotBlank(delta.getReasoning_content())) {
            return false;
        }

        if (delta.getTool_calls() != null) {
            return true;
        }
        return null;
    }

    @Override
    @CatLog(name = "searchByEngine")
    public SearchEngineResponse searchByEngine(SearchEngineRequest request) {
        Assert.isTrue(StringUtils.isNotBlank(request.getQuery()), "搜索关键词不能为空");
        Assert.isTrue(StringUtils.isNotBlank(request.getApi()), "appId不能为空");

        try {
            metricFridayCnt("searchByEngine", request.getApi(), request.getAppId());
            Call<SearchEngineResponse> searchCall = searchEngineService.search("Bearer " + request.getAppId(), request);
            Response<SearchEngineResponse> searchResult = searchCall.execute();
            if (!searchResult.raw().isSuccessful() || searchResult.body() == null) {
                LogUtils.logReturnInfo(log, TagContext.builder().action("searchByEngine: Fail").bizId(request.getApi()).build(),
                        new WarnMessage("FridayAclService", "搜索网页失败", searchResult.message()), request, new Object[]{searchResult.raw(),
                                searchResult.errorBody() != null ? searchResult.errorBody().string() : null, searchResult.raw().header("M-TraceId")});
                metricFridayFail("searchByEngine", request.getApi(), request.getAppId(), searchResult.raw().code());
                return null;
            }

            LogUtils.logReturnInfo(log, TagContext.builder().action("searchByEngine: Success").bizId(request.getApi()).build(),
                    new WarnMessage("FridayAclService", "搜索网页成功", ""), request, searchResult.body());

            if (request.getApi().equals("google-search")) {
                metricFridaySucc("searchByEngine", request.getApi(), request.getAppId());
                return googleSearchResponse(searchResult.body());
            }
            metricFridaySucc("searchByEngine", request.getApi(), request.getAppId());
            return searchResult.body();
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("searchByEngine: error").bizId(request.getApi()).build(),
                    new WarnMessage("FridayAclService", "搜索网页异常", ""), request, e);
            metricFridayFail("searchByEngine", request.getApi(), request.getAppId(), -1);
            return null;
        }
    }

    private SearchEngineResponse googleSearchResponse(SearchEngineResponse body) {
        if (body == null || CollectionUtils.isEmpty(body.getGoogleItems())) {
            return null;
        }

        List<Result> results = Lists.newArrayList();
        for (Object item : body.getGoogleItems()) {
            Result result = new Result();
            JSONObject page = (JSONObject) JSON.toJSON(item);
            if (page.containsKey("title")) {
                result.setTitle(page.getString("title"));
            }
            if (page.containsKey("snippet")) {
                result.setContent(page.getString("snippet"));
            }
            if (page.containsKey("link")) {
                result.setLink(page.getString("link"));
            }
            results.add(result);
        }
        body.setResults(results);
        return body;
    }

    @Override
    public GenerateImageResponse generateImage(String appId, GenerateImageRequest request, List<String> images) {
        // images为空-图像生成
        if (CollectionUtils.isEmpty(images)) {
            return generateImage(appId, request);
        }

        // images不为空-图像编辑
        return editImage(appId, request, images);
    }

    private GenerateImageResponse editImage(String appId, GenerateImageRequest request, List<String> images) {
        if (CollectionUtils.isEmpty(images)) {
            return null;
        }
        try {
            MultipartBody.Builder builder = new MultipartBody.Builder().setType(MultipartBody.FORM);
            builder.addFormDataPart("model", request.getModel());
            builder.addFormDataPart("prompt", request.getPrompt());
            builder.addFormDataPart("n", String.valueOf(request.getN()));
            builder.addFormDataPart("quality", request.getQuality());
            builder.addFormDataPart("size", request.getSize());
            if (StringUtils.isNotEmpty(request.getBackground())) {
                builder.addFormDataPart("background", request.getBackground());
            }
            // 下载每张图片并添加到请求体中
            for (String imageUrl : images) {
                byte[] imageData = ImageUtil.downloadImage(imageUrl);
                String fileName = ImageUtil.getFileNameFromUrl(imageUrl);
                String mimeType = ImageUtil.determineMimeType(imageUrl);

                builder.addFormDataPart("image[]", fileName,
                        RequestBody.create(MediaType.parse(mimeType), imageData));
            }

            // 创建请求
            Request httpRequest = new Request.Builder()
                    .url("https://api.zhizengzeng.com/v1/images/edits")
                    .addHeader("Authorization", "Bearer " + appId)
                    .post(builder.build())
                    .build();

            // 发送请求并处理响应
            long startTime = System.currentTimeMillis();
            try (okhttp3.Response response = client.newCall(httpRequest).execute()) {
                if (!response.isSuccessful()) {
                    LogUtils.logReturnInfo(log, TagContext.builder().action("editImage: Fail").bizId(appId).build(),
                            new WarnMessage("FridayAclService", "editImage失败", response.message()), request, response);
                    return null;
                }
                // 处理响应数据
                String responseData = response.body().string();
                GenerateImageResponse generateImageResponse = JsonCodec.decode(responseData, GenerateImageResponse.class);

                CatUtils.catMethodTransaction("editImage", startTime);

                LogUtils.logReturnInfo(log, TagContext.builder().action("editImage: Success").bizId(appId).build(),
                        new WarnMessage("FridayAclService", "editImage成功", ""), request, generateImageResponse);
                metricFridaySucc("editImage", request.getModel(), appId);

                // base64转图像存url
                if (CollectionUtils.isNotEmpty(generateImageResponse.getData()) && StringUtils.isBlank(generateImageResponse.getData().get(0).getUrl()) && StringUtils.isNotBlank(generateImageResponse.getData().get(0).getB64_json())) {
                    String url = VenusUtil.uploadBase64Image(generateImageResponse.getData().get(0).getB64_json(), "png");
                    if (StringUtils.isNotBlank(url)) {
                        generateImageResponse.getData().get(0).setUrl(url);
                        generateImageResponse.getData().get(0).setB64_json("");
                    }
                }

                return generateImageResponse;
            }
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("generateImage: error").bizId(appId).build(),
                    new WarnMessage("FridayAclService", "editImage异常", ""), request, e);
            metricFridayFail("editImage", request.getModel(), appId, -1);
        }
        return null;
    }

    private GenerateImageResponse generateImage(String appId, GenerateImageRequest request) {
        try {
            long startTime = System.currentTimeMillis();
            metricFridayCnt("generateImage", request.getModel(), appId);
            Call<GenerateImageResponse> generateImageResponseCall = zzzImageService.generateImage("Bearer " + appId, request);

            Response<GenerateImageResponse> response = generateImageResponseCall.execute();

            CatUtils.catMethodTransaction("generateImage", startTime);
            if (!response.raw().isSuccessful() || response.body() == null) {
                LogUtils.logReturnInfo(log, TagContext.builder().action("generateImage: Fail").bizId(appId).build(),
                        new WarnMessage("FridayAclService", "生成图像失败", response.message()), request, new Object[]{response.raw(),
                                response.errorBody() != null ? response.errorBody().string() : null, response.raw().header("M-TraceId")});
                metricFridayFail("generateImage", request.getModel(), appId, response.code());
                return null;
            }

            LogUtils.logReturnInfo(log, TagContext.builder().action("generateImage: Success").bizId(appId).build(),
                    new WarnMessage("FridayAclService", "生成图像成功", ""), request, response.body());
            metricFridaySucc("generateImage", request.getModel(), appId);

            GenerateImageResponse body = response.body();

            // base64转图像存url
            if (CollectionUtils.isNotEmpty(body.getData()) && StringUtils.isBlank(body.getData().get(0).getUrl()) && StringUtils.isNotBlank(body.getData().get(0).getB64_json())) {
                String url = VenusUtil.uploadBase64Image(body.getData().get(0).getB64_json(), "png");
                body.getData().get(0).setUrl(url);
            }

            return body;
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("generateImage: error").bizId(appId).build(),
                    new WarnMessage("FridayAclService", "生成图像异常", ""), request, e);
//            metricFridayFail("generateImage", request.getModel(), appId, -1);
        }
        return null;
    }

    public void metricFridayCnt(String metricName, String model, String appId) {
        Map<String, String> tags = Maps.newHashMap();
        tags.put("model", model);
        tags.put("appId", appId);
        CatUtils.logMetricReq(metricName, tags);
    }

    public void metricFridaySucc(String metricName, String model, String appId) {
        Map<String, String> tags = Maps.newHashMap();
        tags.put("model", model);
        tags.put("appId", appId);
        CatUtils.logMetricSucc(metricName, tags);
    }

    public void metricFridayFail(String metricName, String model, String appId, Integer failCode) {
        Map<String, String> tags = Maps.newHashMap();
        tags.put("model", model);
        tags.put("appId", appId);
        tags.put("code", String.valueOf(failCode));
        CatUtils.logMetricFail(metricName, tags);
    }
}
