package com.sankuai.dzim.pilot.acl;

import com.sankuai.dzim.pilot.acl.data.fraiday.*;
import com.sankuai.dzim.pilot.acl.data.fraiday.search.data.SearchEngineRequest;
import com.sankuai.dzim.pilot.acl.data.fraiday.search.data.SearchEngineResponse;

import java.util.List;

public interface FridayAclService {

    /**
     * 获取embedding向量,不同的模型，维度可能不一样
     *
     * @param appId      小美租户
     * @return 向量
     */
    List<Double> embedding(String appId, String model, int tokenLimit, String text);


    /**
     * 获取embedding向量,不同的模型，维度可能不一样
     *
     * @param appId      小美租户
     * @param request    请求
     * @return 向量
     */
    List<Double> embedding(String appId, EmbeddingRequest request);

    /**
     * 对话生成
     *
     * @param appId   小美租户
     * @param request 请求
     * @return 响应
     */
    ChatCompletionResponse chatCompletion(String appId, ChatCompletionRequest request);

    /**
     * 多模态对话生成
     *
     * @param appId   小美租户
     * @param request 请求
     * @return 响应
     */
    ChatCompletionResponse chatCompletionMulti(String appId, ChatCompletionMultiRequest request);

    /**
     * 调用搜索引擎
     *
     * @param request
     * @return
     */
    SearchEngineResponse searchByEngine(SearchEngineRequest request);

    /**
     * 生成图片
     */
    GenerateImageResponse generateImage(String appId, GenerateImageRequest request, List<String> images);
}
