package com.sankuai.dzim.pilot.gateway.api.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/11/12 14:06
 */
@Data
public class PilotTabInfoVO implements Serializable {
    /**
     * 相关问题
     */
    @MobileDo.MobileField
    private List<PilotRelatedQuestionVO> relatedQuestions;

    /**
     * 卡片外部项目标签
     */
    @MobileDo.MobileField
    private List<PilotProjectTabVO> outerProjectTabs;

    /**
     * 内部项目标签
     */
    @MobileDo.MobileField
    private List<PilotProjectTabVO> innerProjectTabs;

    /**
     * 内容信息
     */
    @MobileDo.MobileField
    private PilotContentVO contentInfo;

    /**
     * 筛选项
     */
    @MobileDo.MobileField
    private PilotFilterItemVO filterItem;



    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PilotFilterItemVO implements Serializable {
        /**
         * 搜索词
         */
        @MobileDo.MobileField
        private String searchWord;

        /**
         * 副标题
         */
        @MobileDo.MobileField
        private String subTitle;

        /**
         * 标题
         */
        @MobileDo.MobileField
        private String title;

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PilotContentVO implements Serializable {
        /**
         * 图片列表
         */
        @MobileDo.MobileField
        private List<PilotPictureVO> pictures;

        /**
         * 内容文本
         */
        @MobileDo.MobileField
        private String content;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PilotPictureVO implements Serializable {
        @MobileDo.MobileField
        private String url;
        /**
         * @see com.sankuai.dzim.pilot.api.enums.search.generative.PicturePositionEnum
         */
        @MobileDo.MobileField
        private Integer position;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PilotRelatedQuestionVO implements Serializable {
        /**
         * 跳链
         */
        @MobileDo.MobileField
        private String link;

        /**
         * 问题
         */
        @MobileDo.MobileField
        private String question;

        /**
         * 图标
         */
        @MobileDo.MobileField
        private String icon;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PilotProjectTabVO implements Serializable {
        /**
         * 搜索词
         */
        @MobileDo.MobileField
        private String searchWord;

        /**
         * 跳链
         */
        @MobileDo.MobileField
        private String link;

        /**
         * 副标题
         */
        @MobileDo.MobileField
        private String subTitle;

        /**
         * 标题
         */
        @MobileDo.MobileField
        private String title;
    }
}