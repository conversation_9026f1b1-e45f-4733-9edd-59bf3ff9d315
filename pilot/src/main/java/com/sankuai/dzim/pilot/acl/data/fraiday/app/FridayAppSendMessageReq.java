package com.sankuai.dzim.pilot.acl.data.fraiday.app;

import lombok.Data;

import java.util.List;

/**
 * FRIDAY应用工厂发送消息
 */
@Data
public class FridayAppSendMessageReq {
    /**
     * 应用Id
     */
    private String appId;

    /**
     * 用户Id
     */
    private String userId;

    /**
     * 用户类型
     */
    private String userType;

    /**
     * 发送的内容
     */
    private List<String> utterances;

    /**
     * 是否流式返回
     */
    private boolean stream = false;

    /**
     * 是否开启debug
     */
    private boolean debug = false;

    /**
     * 租户appKey
     */
    private String clientId;

    /**
     * 租户appSecret
     */
    private String clientSecret;

    /**
     * 授权类型,默认为client_credentials
     */
    private String grantType = "client_credentials";

    /**
     * 输入联想的查询内容
     */
    private String query;

    /**
     * 期望使用的知识库
     */
    private List<String> datasetIds;
    
    /**
     * 推荐生成的suggestion数量，默认为3
     */
    private int count = 3;

    /**
     * 生成的suggestion是否以query为前缀，true: 关键词召回，false: 前缀匹配召回
     * 默认为false(即前缀召回)
     */
    private boolean usePhasePrefix = false;
}
