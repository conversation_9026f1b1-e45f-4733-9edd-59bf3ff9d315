package com.sankuai.dzim.pilot.domain.impl;

import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.google.common.collect.Lists;
import com.meituan.mafka.client.bean.MafkaProducer;
import com.sankuai.dzim.pilot.dal.pilotdao.BlockListDAO;
import com.sankuai.dzim.pilot.dal.entity.pilot.BlockListEntity;
import com.sankuai.dzim.pilot.domain.RiskUserBlockDomainService;
import com.sankuai.dzim.pilot.enums.BlockStatusEnum;
import com.sankuai.dzim.pilot.gateway.enums.BlockTypeEnum;
import com.sankuai.dzim.pilot.gateway.mq.data.RiskBlockMessageData;
import com.sankuai.dzim.pilot.gateway.mq.data.RiskUnBlockMessageData;
import com.sankuai.dzim.pilot.process.data.BlockStrategyConfig;
import com.sankuai.dzim.pilot.utils.LionConfigUtil;
import com.sankuai.dzim.pilot.utils.ProducerUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class RiskUserBlockDomainServiceImpl implements RiskUserBlockDomainService {

    @Autowired
    private BlockListDAO blockListDAO;

    @Autowired
    private MafkaProducer delaySendMessageProducer;

    @Resource(name = "redisClient")
    private RedisStoreClient dzimRedisClient;

    @Autowired
    private LionConfigUtil lionConfigUtil;

    @Override
    public Boolean batchBlockRiskUsers(List<String> userIds, String strategy, Integer assistantType, Integer blockReason, Integer blockStatus) {
        if (CollectionUtils.isEmpty(userIds)) {
            return false;
        }

        BlockTypeEnum blockType = BlockTypeEnum.getByName(strategy);
        if (blockType == null) {
            return false;
        }

        List<BlockListEntity> blockListEntities = blockListDAO.selectByIds(userIds);

        List<BlockListEntity> existBlockUsers = blockListEntities.stream().filter(Objects::nonNull)
                .filter(entity -> entity.getAssistantType() == assistantType)
                .collect(Collectors.toList());
        List<String> existBlockUserIds = existBlockUsers.stream().map(BlockListEntity::getUserId).collect(Collectors.toList());

        List<String> nonExistBlockUserIds = userIds.stream().filter(id -> !existBlockUserIds.contains(id)).collect(Collectors.toList());

        // 存在的拉黑用户更新
        if (CollectionUtils.isNotEmpty(existBlockUserIds)) {
            int updateCnt = blockListDAO.batchUpdate(batchUpdateList(existBlockUsers, blockReason, blockStatus));
            if (updateCnt < existBlockUserIds.size()) {
                log.error("存在用户批量拉黑出错, 需要拉黑的用户id:{}, 已拉黑数量:{}", existBlockUserIds, updateCnt);
            }
        }
        // 不存在的用户新增
        if (CollectionUtils.isNotEmpty(nonExistBlockUserIds)) {
            int insertCnt = blockListDAO.batchInsert(batchInsertList(nonExistBlockUserIds, assistantType, blockReason, blockStatus));
            if (insertCnt < nonExistBlockUserIds.size()) {
                log.error("新增用户批量拉黑出错, 需要拉黑的用户id:{}, 已拉黑数量:{}", nonExistBlockUserIds, insertCnt);
            }
        }
        return true;
    }

    public List<BlockListEntity> batchInsertList(List<String> nonExistBlockUserIds, Integer assistantType, Integer blockType, Integer blockStatus) {
        List<BlockListEntity> insertEntity = Lists.newArrayList();
        for (String id : nonExistBlockUserIds) {
            BlockListEntity entity = new BlockListEntity();
            entity.setUserId(id);
            entity.setBlockedCnt(1);
            entity.setAssistantType(assistantType);
            entity.setBlockType(blockType);
            entity.setBlockedStatus(blockStatus);
            insertEntity.add(entity);
        }
        return insertEntity;
    }

    public List<BlockListEntity> batchUpdateList(List<BlockListEntity> existBlockUsers, Integer blockType, Integer blockStatus) {
        List<BlockListEntity> updatedEntity = Lists.newArrayList();
        for (BlockListEntity entity : existBlockUsers) {
            entity.setBlockedCnt(entity.getBlockedCnt() + 1);
            entity.setBlockType(blockType);
            entity.setBlockedStatus(blockStatus);
            updatedEntity.add(entity);
        }
        return updatedEntity;
    }

    @Override
    public Boolean batchUnBlockRiskUsers(List<String> userIds, Integer assistantType) {
        if (CollectionUtils.isEmpty(userIds)) {
            return false;
        }
        List<BlockListEntity> blockListEntities = blockListDAO.selectByIds(userIds);
        if (CollectionUtils.isEmpty(blockListEntities)) {
            return false;
        }
        List<BlockListEntity> unBlockList = blockListEntities.stream().filter(Objects::nonNull)
                .filter(blockListEntity -> blockListEntity.getAssistantType() == assistantType)
                .collect(Collectors.toList());

        int updateCnt = blockListDAO.batchUpdate(buildUnBlockList(unBlockList));
        if (updateCnt < unBlockList.size()) {
            log.error("用户解禁失败, 需解禁用户ID:{}, 成功解禁数量:{}", unBlockList, updateCnt);
        }

        return true;
    }

    public List<BlockListEntity> buildUnBlockList(List<BlockListEntity> unBlockList) {
        List<BlockListEntity> unBlock = Lists.newArrayList();
        for (BlockListEntity entity : unBlockList) {
            entity.setBlockedStatus(BlockStatusEnum.NORMAL.getStatus());
            unBlock.add(entity);
        }
        return unBlock;
    }

    @Override
    public void checkBlock(long cnt, int blockedCnt, BlockStrategyConfig blockStrategyConfig, BlockListEntity blockListEntity,
                           RiskBlockMessageData riskMsg, Integer blockType) {
        if (cnt >= blockStrategyConfig.getThreshold()) {
            if (blockListEntity == null) {
                blockListEntity = new BlockListEntity();
                blockListEntity.setUserId(riskMsg.getReq().getImUserId());
                blockListEntity.setBlockedCnt(1);
                blockListEntity.setBlockType(blockType);
                blockListEntity.setAssistantType(riskMsg.getReq().getAssistantType());
                blockListEntity.setBlockedStatus(BlockStatusEnum.BLOCKED.getStatus());
                int insertCnt = blockListDAO.insert(blockListEntity);
                if(insertCnt < 1){
                    log.error("新增用户拉黑失败, 需要拉黑的用户id:{}", riskMsg.getReq().getImUserId());
                }
            } else {
                blockListEntity.setBlockedCnt(blockedCnt + 1);
                blockListEntity.setBlockedStatus(BlockStatusEnum.BLOCKED.getStatus());
                blockListEntity.setBlockType(blockType);
                int updateCnt = blockListDAO.update(blockListEntity);
                if(updateCnt < 1){
                    log.error("存在用户拉黑失败, 需要拉黑的用户id:{}", riskMsg.getReq().getImUserId());
                }
            }
            // 发送延时解除消息
            if (blockListEntity.getBlockedCnt() < blockStrategyConfig.getPermanentThreshold()) {
                ProducerUtils.sendDelayRiskUserUnBlockMessage(delaySendMessageProducer, buildRiskUnBlockMessageData(blockStrategyConfig.getBlockStrategy(), riskMsg,
                        blockStrategyConfig.getDelaySec(), blockType), "风险用户解禁");
            } else {
                blockListEntity.setBlockedStatus(BlockStatusEnum.PERMANENT_BLOCK.getStatus());
                int updateCnt = blockListDAO.update(blockListEntity);
                if(updateCnt < 1){
                    log.error("用户永久拉黑失败, 需要拉黑的用户id:{}", riskMsg.getReq().getImUserId());
                }
            }

        }
    }

    @Override
    public void update(BlockListEntity entity) {
        if (entity == null) {
            return;
        }
        int updateCnt = blockListDAO.update(entity);
        if(updateCnt < 1){
            log.error("存在用户信息更新失败, 用户id:{}", entity.getUserId());
        }
    }

    @Override
    public BlockListEntity selectUserByIdAndAssistantType(String userId, Integer assistantType) {
        return blockListDAO.selectUserByIdAndAssistantType(userId, assistantType);
    }

    public RiskUnBlockMessageData buildRiskUnBlockMessageData(String strategy, RiskBlockMessageData riskMsg, Long delaySec, Integer blockType) {
        RiskUnBlockMessageData riskUnBlockMessageData = new RiskUnBlockMessageData();
        riskUnBlockMessageData.setUserId(riskMsg.getReq().getImUserId());
        riskUnBlockMessageData.setAssistantType(riskMsg.getReq().getAssistantType());
        riskUnBlockMessageData.setStrategy(strategy);
        riskUnBlockMessageData.setDelaySec(delaySec);
        riskUnBlockMessageData.setBlockType(blockType);
        return riskUnBlockMessageData;
    }

    @Override
    public Boolean checkPermanentlyBlock(long cnt, BlockStrategyConfig blockStrategyConfig, int blockedCnt,
                                         BlockListEntity blockListEntity, RiskBlockMessageData riskMsg, Integer blockType) {
        if (cnt >= blockStrategyConfig.getThreshold() && blockedCnt + 1 > blockStrategyConfig.getPermanentThreshold()) {
            // 永久拉黑
            if (blockListEntity == null) {
                blockListEntity = new BlockListEntity();
                blockListEntity.setUserId(riskMsg.getReq().getImUserId());
                blockListEntity.setBlockedCnt(1);
                blockListEntity.setBlockType(blockType);
                blockListEntity.setAssistantType(riskMsg.getReq().getAssistantType());
                blockListEntity.setBlockedStatus(BlockStatusEnum.PERMANENT_BLOCK.getStatus());
                int insertCnt = blockListDAO.insert(blockListEntity);
                if(insertCnt < 1){
                    log.error("新增用户永久拉黑失败, 需要拉黑的用户id:{}", riskMsg.getReq().getImUserId());
                }
                return true;
            }
            blockListEntity.setBlockedCnt(blockedCnt + 1);
            blockListEntity.setBlockedStatus(BlockStatusEnum.PERMANENT_BLOCK.getStatus());
            blockListEntity.setBlockType(blockType);
            int updateCnt = blockListDAO.update(blockListEntity);
            if(updateCnt < 1){
                log.error("存在用户永久拉黑失败, 需要拉黑的用户id:{}", riskMsg.getReq().getImUserId());
            }
            return true;
        }
        return false;
    }
}
