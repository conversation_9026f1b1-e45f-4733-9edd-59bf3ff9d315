package com.sankuai.dzim.pilot.acl.impl;

import com.alibaba.fastjson.JSON;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.meituan.nibtp.trade.client.buy.enums.*;
import com.meituan.nibtp.trade.client.combine.agent.AgentProcessService;
import com.meituan.nibtp.trade.client.combine.agent.AgentQueryService;
import com.meituan.nibtp.trade.client.combine.bean.WebParamDTO;
import com.meituan.nibtp.trade.client.combine.requset.*;
import com.meituan.nibtp.trade.client.combine.response.*;
import com.sankuai.dealuser.price.display.api.enums.CouponAssignStatusEnum;
import com.sankuai.dealuser.price.display.api.enums.PromoTypeEnum;
import com.sankuai.dealuser.price.display.api.model.BatchPriceRequest;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.acl.AgentTradeAclService;
import com.sankuai.dzim.pilot.gateway.api.assistant.data.BeamUserInfo;
import com.sankuai.dzim.pilot.gateway.api.assistant.data.SendBeamMessageReq;
import com.sankuai.dzim.pilot.acl.PriceDisplayAclService;
import com.sankuai.dzim.pilot.scene.task.data.EnvContext;
import com.sankuai.dzim.pilot.acl.UserAclService;
import com.sankuai.dzim.pilot.utils.data.Response;
import com.sankuai.nibmkt.promotion.api.common.enums.PromotionPropertyEnum;
import com.sankuai.nibmkt.promotion.api.common.enums.PromotionTypeEnum;
import com.sankuai.nibmkt.promotion.api.common.enums.daozong.PromotionDetailTypeEnum;
import com.sankuai.wpt.user.retrieve.thrift.message.UserModel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: zhouyibing
 * @date: 2025/4/24
 */
@Service
@Slf4j
public class AgentTradeAclServiceImpl implements AgentTradeAclService {
    @Autowired
    private UserAclService userAclService;

    @MdpThriftClient(remoteAppKey = "com.sankuai.mptrade.api", timeout = 3000, testTimeout = 5000)
    private AgentQueryService agentQueryService;

    @MdpThriftClient(remoteAppKey = "com.sankuai.mptrade.api", timeout = 3000, testTimeout = 5000)
    private AgentProcessService agentProcessService;

    private static final Map<String, Integer> CLIENT_TYPE_MAP = new HashMap<>();
    static {
        CLIENT_TYPE_MAP.put("bm-ios", ChannelEnum.IOS_APP.getCode());
        CLIENT_TYPE_MAP.put("bm-android", ChannelEnum.ANDROID_APP.getCode());
        CLIENT_TYPE_MAP.put("bm-pc", ChannelEnum.IOS_APP.getCode());
    }
    @Autowired
    private PriceDisplayAclService priceDisplayAclService;

    @Override
    public AgentPreviewResDTO preview(AgentPreviewReqDTO req) {
        try {
            AgentPreviewResDTO resDTO = agentQueryService.preview(req);
            log.info("AgentTradeAclService preview Return, req = {}, resp = {}", JsonCodec.encodeWithUTF8(req), JsonCodec.encodeWithUTF8(resDTO));
            return resDTO;
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("preview").build(),
                    new WarnMessage("AgentTradeAclService", "Agent预览下单接口调用失败", null), req, null, e);
            return null;
        }
    }

    @Override
    public AgentQueryOrderResDTO queryOrder(AgentQueryOrderReqDTO req) {
        try {
            AgentQueryOrderResDTO resDTO = agentQueryService.queryOrder(req);
            log.info("AgentTradeAclService query Order Return, req = {}, resp = {}", JsonCodec.encodeWithUTF8(req), JsonCodec.encodeWithUTF8(resDTO));
            return resDTO;
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("queryOrder").build(),
                    new WarnMessage("AgentTradeAclService", "Agent查询订单接口调用失败", null), req, null, e);
            return null;
        }
    }

    @Override
    public AgentOrderDTO queryOrderSingle(Long mtUserId, Long orderId) {
        //mtUserId、orderId必填
        WebParamDTO webParamDTO = new WebParamDTO();
        webParamDTO.setUserId(mtUserId);
        webParamDTO.setChannel(PlatformEnum.MT.getCode());
        webParamDTO.setBizSpace(BizSpaceId.DZ.getCode());

        AgentQueryOrderReqDTO req = new AgentQueryOrderReqDTO();
        req.setWebParam(webParamDTO);
        req.setOrderIdList(Lists.newArrayList(orderId));

        //查单个订单，接口返回list
        AgentQueryOrderResDTO resDTO = queryOrder(req);
        if (resDTO == null || !resDTO.isSuccess() || CollectionUtils.isEmpty(resDTO.getOrderList())) {
            return null;
        }

        return resDTO.getOrderList().get(0);
    }

    public AgentCreateOrderResDTO createOrder(AgentCreateOrderReqDTO req) {
        try {
            AgentCreateOrderResDTO resDTO = agentProcessService.createOrder(req);
            log.info("AgentTradeAclService createOrder Return, req = {}, resp = {}", JsonCodec.encodeWithUTF8(req), JsonCodec.encodeWithUTF8(resDTO));
            return resDTO;
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("createOrder").build(),
                    new WarnMessage("agentProcessService", "Agent下单接口调用失败", null), req, null, e);
            return null;
        }
    }

    @Override
    public AgentRefundApplyResDTO refundApply(AgentRefundApplyReqDTO req) {
        try {
            AgentRefundApplyResDTO resDTO = agentProcessService.refundApply(req);
            LogUtils.logReturnInfo(log, TagContext.builder().action("refundApply").build(),
                    new WarnMessage("AgentTradeAclService", "订单退款接口", ""), req, resDTO);
            return resDTO;
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("refundApply").build(),
                    new WarnMessage("agentProcessService", "Agent退款接口调用失败", null), req, null, e);
            return null;
        }
    }

    @Override
    public Response<List<AgentGeneralBookingOrderDTO>> queryGeneralBookingOrder(WebParamDTO webParam, List<AgentQueryGeneralBookingOrderItemReqDTO> orderReq) {
        try {
            AgentQueryGeneralBookingOrderReqDTO req = new AgentQueryGeneralBookingOrderReqDTO();
            req.setWebParam(webParam);
            req.setOrderList(orderReq);
            AgentQueryGeneralBookingOrderResDTO res = agentQueryService.queryGeneralBookingOrder(req);
            LogUtils.logReturnInfo(log, TagContext.builder().action("queryGeneralBookingOrder").build(),
                    new WarnMessage("AgentTradeAclService", "查询预约单接口", ""), req, res);
            if (res == null) {
                return Response.error("查询订单信息为空");
            }
            if (res.isFail()) {
                return Response.error(res.getCode(), res.getMessage());
            }
            return Response.success(res.getOrderList());
        } catch (Exception e) {
            log.error("[TradeAclServiceImpl] queryGeneralBookingOrder error, webParam={}, req={}", JSON.toJSONString(webParam), JSON.toJSONString(orderReq),e);
        }
        return null;
    }

    @Override
    public AgentCreateOrderResDTO createGeneralBookingOrder(WebParamDTO webParam, List<AgentGeneralBookingReqDTO> bookReq) {
        try {
            AgentCreateOrderReqDTO req = new AgentCreateOrderReqDTO();
            req.setWebParam(webParam);
            req.setGeneralBookingReqDTOList(bookReq);
            AgentCreateOrderResDTO resp = agentProcessService.createOrder(req);
            LogUtils.logReturnInfo(log, TagContext.builder().action("createGeneralBookingOrder").build(),
                    new WarnMessage("AgentTradeAclService", "调用预约单创建接口", ""), req, resp);
            return resp;
        } catch (Exception e) {
            log.error("[TradeAclServiceImpl] createGeneralBookingOrder error, webParam={}, bookReq={}", JSON.toJSONString(webParam), JSON.toJSONString(bookReq), e);
        }
        return null;
    }

    @Override
    public Response<List<AgentRefundApplyOrderResDTO>> refundApply(WebParamDTO webParam, List<AgentRefundApplyOrderReqDTO> refundApplyReq) {
        try {
            AgentRefundApplyReqDTO req = new AgentRefundApplyReqDTO();
            req.setWebParam(webParam);
            req.setOrderList(refundApplyReq);
            AgentRefundApplyResDTO res = agentProcessService.refundApply(req);
            LogUtils.logReturnInfo(log, TagContext.builder().action("refundApply").build(),
                    new WarnMessage("AgentTradeAclService", "调用预约单取消接口", ""), req, res);
            if (res == null) {
                return Response.error("取消预约失败");
            }
            if (res.isFail()) {
                return Response.error(res.getCode(), res.getMessage());
            }
            return Response.success(res.getOrderList());
        } catch (Exception e) {
            log.error("[AgentTradeAclService] refundApply error, webParam={}, refundApplyReq={}", JSON.toJSONString(webParam), JSON.toJSONString(refundApplyReq), e);
            return Response.error("取消预约失败");
        }
    }

    @Override
    public WebParamDTO buildWebParam(UserModel mtUserModel, EnvContext envContext) {
        WebParamDTO webParamDTO = new WebParamDTO();
        webParamDTO.setUserId(mtUserModel.getId());
        webParamDTO.setDpUserId(getDpUserIdByMtUserId(mtUserModel.getId()));
        webParamDTO.setMobile(mtUserModel.getMobile());
        webParamDTO.setCityId(envContext.getCityId());
        webParamDTO.setPlatform(PlatformEnum.MT.getCode());
        webParamDTO.setChannel(getChannel(envContext.getChannel()));
        webParamDTO.setLongitude(envContext.getLng());
        webParamDTO.setLatitude(envContext.getLat());
        webParamDTO.setVersion(envContext.getAppVersion());
        webParamDTO.setUuid(envContext.getDeviceId());

        webParamDTO.setBizSpace(BizSpaceId.DZ.getCode());

        Map<String, String>extraMap = new HashMap<>();
        extraMap.put("distributionType", "biz_agent");
        webParamDTO.setExtraMap(extraMap);
        return webParamDTO;
    }

    @Override
    public WebParamDTO buildWebParam(SendBeamMessageReq sendBeamMessageReq) {
        return buildWebParamFromUserInfo(sendBeamMessageReq.getUser_info());
    }

    public WebParamDTO buildWebParamFromUserInfo(BeamUserInfo beamUserInfo) {
        try {
            WebParamDTO webParamDTO = new WebParamDTO();
            long userId = Long.parseLong(beamUserInfo.getUserid());
            webParamDTO.setUserId(userId);
            webParamDTO.setDpUserId(getDpUserIdByMtUserId(userId));
            webParamDTO.setMobile(beamUserInfo.getPhone());
            webParamDTO.setCityId(Optional.ofNullable(beamUserInfo.getCity_id()).map(Integer::parseInt).orElse(null));
            webParamDTO.setPlatform(PlatformEnum.MT.getCode());
            webParamDTO.setChannel(CLIENT_TYPE_MAP.get(beamUserInfo.getClient_type()));
            webParamDTO.setLongitude(Optional.ofNullable(beamUserInfo.getLongitude()).map(Double::parseDouble).orElse(null));
            webParamDTO.setLatitude(Optional.ofNullable(beamUserInfo.getLatitude()).map(Double::parseDouble).orElse(null));
            webParamDTO.setVersion(beamUserInfo.getAppversion());
            webParamDTO.setUuid(beamUserInfo.getUuid());
            webParamDTO.setAppId(AppIdEnum.MT_AGENT_APP.getCode());
            Map<String, String> extraMap = new HashMap<>();
            if (beamUserInfo.getUser_token() != null) {
                extraMap.put("mtToken", beamUserInfo.getUser_token());
            }
            extraMap.put("distributionType", DistributionTypeEnum.MT_AGENT.getCode());
            webParamDTO.setExtraMap(extraMap);
            return webParamDTO;
        } catch (Exception e) {
            log.error("buildWebParam 异常", e);
            return null;
        }
    }

    private Integer getChannel(String channel) {
        if (StringUtils.isEmpty(channel)) {
            return ChannelEnum.IOS_APP.getCode();
        }
        if (channel.equals("IOS")) {
            return ChannelEnum.IOS_APP.getCode();
        } else if (channel.equals("ANDROID")) {
            return ChannelEnum.ANDROID_APP.getCode();
        }
        return ChannelEnum.IOS_APP.getCode();
    }

    private Long getDpUserIdByMtUserId(Long mtUserId) {
       return userAclService.getDpRealBindUserId(mtUserId);
    }

    @Override
    public List<AssignPromotionItemDTO> buildAssignPromotionItemList(BatchPriceRequest batchPriceRequest) {
        Map<Long, List<PriceDisplayDTO>> map = priceDisplayAclService.batchQueryPriceByLongShopId(batchPriceRequest);
        if (MapUtils.isEmpty(map)) {
            return Lists.newArrayList();
        }

        try {
            PriceDisplayDTO priceDisplayDTO = map.values().stream().filter(e -> e != null && e.size() > 0).findFirst().map(list -> list.get(0)).orElse(null);
            if (priceDisplayDTO == null || CollectionUtils.isEmpty(priceDisplayDTO.getUsedPromos())) {
                return Lists.newArrayList();
            }

            return convertToAssignPromotionItems(priceDisplayDTO);
        } catch (Exception e) {
            log.error("buildAssignPromotionItemList error, batchPriceRequest={}", batchPriceRequest, e);
            return Lists.newArrayList();
        }
    }

    private List<AssignPromotionItemDTO> convertToAssignPromotionItems(PriceDisplayDTO priceDisplayDTO) {
        if (CollectionUtils.isEmpty(priceDisplayDTO.getUsedPromos())) {
            return Lists.newArrayList();
        }

        return priceDisplayDTO.getUsedPromos().stream()
                .map(promoDTO -> {
                    PromotionTypeWrapper wrapper = getPromotionTypeAndDetail(promoDTO);
                    if (wrapper == null || wrapper.getDetailType() == null) {
                        return null;
                    }
                    return buildAssignPromotionItem(promoDTO, wrapper);
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private AssignPromotionItemDTO buildAssignPromotionItem(PromoDTO promoDTO, PromotionTypeWrapper wrapper) {
        AssignPromotionItemDTO dto = new AssignPromotionItemDTO();
        dto.setPromotionGroupId(String.valueOf(promoDTO.getIdentity().getPromoId()));
        dto.setPromotionType(wrapper.getPromotionType());
        dto.setPromotionDetailType(wrapper.getDetailType());
        dto.setFinanceExt(wrapper.getFinanceExt());
        return dto;
    }

    private PromotionTypeWrapper getPromotionTypeAndDetail(PromoDTO promoDTO) {
        if (isCouponPromo(promoDTO)) {
            return new PromotionTypeWrapper(
                    PromotionTypeEnum.COUPON.getCode(),
                    null,
                    getDetailTypeForCoupon(promoDTO.getIdentity().getSourceType())
            );
        } else if (isGovernmentConsumeCoupon(promoDTO)) {
            String financeExt = null;
            if (MapUtils.isNotEmpty(promoDTO.getPromotionOtherInfoMap())) {
                financeExt = promoDTO.getPromotionOtherInfoMap().get(PromotionPropertyEnum.FINANCE_EXT.getValue());
            }
            if (StringUtils.isNotBlank(financeExt)) {
                return new PromotionTypeWrapper(
                        PromotionTypeEnum.FINANCIAL_COUPON.getCode(),
                        financeExt,
                        PromotionDetailTypeEnum.financialCoupon.name()
                );
            }
        }
        return null;
    }

    private boolean isCouponPromo(PromoDTO promoDTO) {
        return promoDTO.getIdentity().getPromoType() == PromoTypeEnum.COUPON.getType()
                && promoDTO.isCanAssign();
    }

    private boolean isGovernmentConsumeCoupon(PromoDTO promoDTO) {
        return promoDTO.getIdentity().getPromoType() == PromoTypeEnum.GOVERNMENT_CONSUME_COUPON.getType()
                && CouponAssignStatusEnum.UN_ASSIGNED.getCode() == promoDTO.getCouponAssignStatus();
    }

    private String getDetailTypeForCoupon(int sourceType) {
        return sourceType == 2 ? PromotionDetailTypeEnum.shopCoupon.name()
                : sourceType == 1 ? PromotionDetailTypeEnum.coupon.name()
                : null;
    }

    @Getter
    @AllArgsConstructor
    private static class PromotionTypeWrapper {
        private final Integer promotionType;
        private final String financeExt;
        private final String detailType;
    }
}
