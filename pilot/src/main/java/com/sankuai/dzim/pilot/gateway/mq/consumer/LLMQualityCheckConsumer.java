package com.sankuai.dzim.pilot.gateway.mq.consumer;

import com.dianping.vc.mq.client.api.annotation.Consumer;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.gateway.mq.data.RiskBlockMessageData;
import com.sankuai.dzim.pilot.process.LLMQualityCheckProcessService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class LLMQualityCheckConsumer {
    @Autowired
    private LLMQualityCheckProcessService llmQualityCheckProcessService;

    @Consumer(nameSpace = "daozong", appKey = "com.sankuai.mim.pilot", group = "pilot.assistant.llm.quality.check", topic = "pilot.assistant.message.send")
    public ConsumeStatus recvMessage(String riskBlackMsg) {
        if (StringUtils.isEmpty(riskBlackMsg)) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        try {
            RiskBlockMessageData riskMsg = JsonCodec.decode(riskBlackMsg, RiskBlockMessageData.class);
            if(riskMsg == null){
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            llmQualityCheckProcessService.getLLMResultAndBlock(riskMsg);
        }catch (Exception e){
            LogUtils.logFailLog(log, TagContext.builder().action("LLMQualityCheckConsumer").build(),
                    new WarnMessage("LLMQualityCheckConsumer", "大模型质检消费者执行失败", null), riskBlackMsg, null, e);
            return ConsumeStatus.RECONSUME_LATER;
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
