package com.sankuai.dzim.pilot.utils;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.api.data.ImportTemplateData;
import com.sankuai.dzim.pilot.api.data.TabCardData;
import com.sankuai.dzim.pilot.api.data.search.generative.FilterItemDTO;
import com.sankuai.dzim.pilot.api.data.search.generative.HeadInfoDTO;
import com.sankuai.dzim.pilot.api.data.search.generative.TabInfoDTO;
import com.sankuai.dzim.pilot.api.enums.search.generative.PicturePositionEnum;
import com.sankuai.dzim.pilot.process.data.FlattenImportTemplateData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/09/29 21:23
 */
@Slf4j
public class TemplateDataExcelUtil {

    public static final String NULL = "NULL";

    public static List<ImportTemplateData> importExcel(String filePath) {
        try {
            List<FlattenImportTemplateData> flattenImportTemplateData = ExcelUtils.readFromExcel(filePath,
                    FlattenImportTemplateData.class);
            return flattenImportTemplateData.stream()
                    .filter(Objects::nonNull)
                    .filter(flatten -> StringUtils.isNotBlank(flatten.getQuestion()))
                    .map(TemplateDataExcelUtil::convert2ImportTemplateData)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("importExcel").bizId(filePath).build(),
                    new WarnMessage("TemplateDataExcelUtil", "模板数据导入失败", null), filePath, null, e);
            return Collections.emptyList();
        }
    }

    public static void exportExcel(String fileName, List<ImportTemplateData> importTemplates) {
        try {
            List<FlattenImportTemplateData> flattenTemplates = importTemplates.stream()
                    .map(TemplateDataExcelUtil::convert2FlattenImportTemplateData).collect(Collectors.toList());
            ExcelUtils.exportExcel(fileName, flattenTemplates, FlattenImportTemplateData.class);
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("exportExcel").bizId(fileName).build(),
                    new WarnMessage("TemplateDataExcelUtil", "模板数据导出失败", null), fileName, null, e);
        }
    }

    private static ImportTemplateData convert2ImportTemplateData(FlattenImportTemplateData flattenImportTemplateData) {
        ImportTemplateData importTemplateData = new ImportTemplateData();
        parseBaseInfo(flattenImportTemplateData, importTemplateData);
        parseFilterInfo(flattenImportTemplateData, importTemplateData);
        parseContentInfo(flattenImportTemplateData, importTemplateData);
        parseRelatedQuestion(flattenImportTemplateData, importTemplateData);
        parseTabInfo(flattenImportTemplateData, importTemplateData);
        removeEmptyData(importTemplateData.getTabCards());
        importTemplateData.setAnswer(flattenImportTemplateData.getAnswer());
        return importTemplateData;
    }

    private static void parseTabInfo(FlattenImportTemplateData flattenImportTemplateData,
            ImportTemplateData importTemplateData) {
        if (StringUtils.isBlank(flattenImportTemplateData.getProjectTitles())) {
            return;
        }
        String[] projectTitleArr = Optional.ofNullable(flattenImportTemplateData.getProjectTitles())
                .map(s -> s.split("\n")).orElse(new String[0]);
        String[] projectLinkArr = Optional.ofNullable(flattenImportTemplateData.getProjectLinks())
                .map(s -> s.split("\n")).orElse(new String[0]);
        String[] projectSubtitleArr = Optional.ofNullable(flattenImportTemplateData.getProjectSubtitles())
                .map(s -> s.split("\n")).orElse(new String[0]);
        String[] projectSearchWordArr = Optional.ofNullable(flattenImportTemplateData.getProjectSearchWords())
                .map(s -> s.split("\n")).orElse(new String[0]);
        List<TabCardData> tabCards = importTemplateData.getTabCards();
        Map<String, List<TabInfoDTO.ProjectTabDTO>> filter2ProjectTabs = Maps.newHashMap();
        for (int i = 0; i < projectTitleArr.length; i++) {
            Optional.ofNullable(projectTitleArr[i]).map(s -> s.split("###")).ifPresent(innerProjects -> {
                List<TabInfoDTO.ProjectTabDTO> projectTabs = Arrays.stream(innerProjects).skip(1)
                        .map(innerProject -> new TabInfoDTO.ProjectTabDTO(innerProject, null, null, null))
                        .collect(Collectors.toList());
                filter2ProjectTabs.put(innerProjects[0], projectTabs);
            });
            if (i < projectLinkArr.length) {
                Optional.ofNullable(projectLinkArr[i]).map(s -> s.split("###")).ifPresent(links -> {
                    String key = links[0];
                    List<TabInfoDTO.ProjectTabDTO> projectTabs = filter2ProjectTabs.get(key);
                    for (int j = 0; j < projectTabs.size(); j++) {
                        projectTabs.get(j).setLink(getTextOrNull(links[j + 1]));
                    }
                });
            }
            if (i < projectSubtitleArr.length) {
                Optional.ofNullable(projectSubtitleArr[i]).map(s -> s.split("###")).ifPresent(subtitles -> {
                    String key = subtitles[0];
                    List<TabInfoDTO.ProjectTabDTO> projectTabs = filter2ProjectTabs.get(key);
                    for (int j = 0; j < projectTabs.size(); j++) {
                        projectTabs.get(j).setSubTitle(getTextOrNull(subtitles[j + 1]));
                    }
                });
            }
            if (i < projectSearchWordArr.length) {
                Optional.ofNullable(projectSearchWordArr[i]).map(s -> s.split("###")).ifPresent(searchWords -> {
                    String key = searchWords[0];
                    List<TabInfoDTO.ProjectTabDTO> projectTabs = filter2ProjectTabs.get(key);
                    for (int j = 0; j < projectTabs.size(); j++) {
                        projectTabs.get(j).setSearchWord(getTextOrNull(searchWords[j + 1]));
                    }
                });
            }
        }
        tabCards.forEach(tabCard -> tabCard.setProjectTabs(filter2ProjectTabs.get(tabCard.getFilterItem().getTitle())));
    }

    private static void parseRelatedQuestion(FlattenImportTemplateData flattenImportTemplateData,
            ImportTemplateData importTemplateData) {
        if (StringUtils.isBlank(flattenImportTemplateData.getRelatedQuestions())) {
            return;
        }
        String[] relatedQuestionArr = Optional.ofNullable(flattenImportTemplateData.getRelatedQuestions())
                .map(s -> s.split("\n")).orElse(new String[0]);
        String[] relatedQuestionIconArr = Optional.ofNullable(flattenImportTemplateData.getRelatedQuestionIcons())
                .map(s -> s.split("\n")).orElse(new String[0]);
        String[] relatedQuestionLinkArr = Optional.ofNullable(flattenImportTemplateData.getRelatedQuestionLinks())
                .map(s -> s.split("\n")).orElse(new String[0]);
        if (relatedQuestionArr.length != importTemplateData.getTabCards().size()) {
            throw new IllegalArgumentException(flattenImportTemplateData.getQuestion() + "关联问题数量不一致");
        }
        List<TabCardData> tabCards = importTemplateData.getTabCards();
        Map<String, List<TabInfoDTO.RelatedQuestion>> filter2RelatedQuestions = Maps.newHashMap();
        for (int i = 0; i < relatedQuestionArr.length; i++) {
            Optional.ofNullable(relatedQuestionArr[i]).map(s -> s.split("###")).ifPresent(innerQuestions -> {
                List<TabInfoDTO.RelatedQuestion> questions = Arrays.stream(innerQuestions).skip(1)
                        .map(innerQuestion -> new TabInfoDTO.RelatedQuestion(null, innerQuestion, null))
                        .collect(Collectors.toList());
                filter2RelatedQuestions.put(innerQuestions[0], questions);
            });
            if (i < relatedQuestionIconArr.length) {
                Optional.ofNullable(relatedQuestionIconArr[i]).map(s -> s.split("###")).ifPresent(icons -> {
                    String key = icons[0];
                    List<TabInfoDTO.RelatedQuestion> questions = filter2RelatedQuestions.get(key);
                    for (int j = 0; j < questions.size(); j++) {
                        questions.get(j).setIcon(getTextOrNull(icons[j + 1]));
                    }
                });
            }
            if (i < relatedQuestionLinkArr.length) {
                Optional.ofNullable(relatedQuestionLinkArr[i]).map(s -> s.split("###")).ifPresent(links -> {
                    String key = links[0];
                    List<TabInfoDTO.RelatedQuestion> questions = filter2RelatedQuestions.get(key);
                    if (links.length != questions.size() + 1) {
                        LogUtils.logFailLog(log, TagContext.builder().action("importExcel.parseRelatedQuestion").build(),
                                WarnMessage.build("TemplateDataExcelUtil", "关联问题链接数量不一致", String.valueOf(questions)),
                                flattenImportTemplateData.getQuestion(), null, null);
                    }
                    for (int j = 0; j < questions.size(); j++) {
                        questions.get(j).setLink(getTextOrNull(links[j + 1]));
                    }
                });
            }
        }
        tabCards.forEach(tabCardData -> tabCardData
                .setRelatedQuestions(filter2RelatedQuestions.get(tabCardData.getFilterItem().getTitle())));

    }

    private static String getTextOrNull(String text) {
        return NULL.equalsIgnoreCase(text) ? null : text;
    }

    private static void parseContentInfo(FlattenImportTemplateData flattenImportTemplateData,
            ImportTemplateData importTemplateData) {
        List<TabCardData> tabCards = importTemplateData.getTabCards();
        String contentText = flattenImportTemplateData.getContentText();
        String contentPicture = flattenImportTemplateData.getContentPicture();
        String contentPicturePosition = flattenImportTemplateData.getContentPicturePosition();
        fillContentInfo(tabCards, contentText, contentPicture, contentPicturePosition);
    }

    private static void removeEmptyData(List<TabCardData> tabCards) {
        tabCards.forEach(tabCard -> {
            if (NULL.equalsIgnoreCase(tabCard.getFilterItem().getTitle())) {
                tabCard.getFilterItem().setTitle(null);
            }
        });
    }

    private static void fillContentInfo(List<TabCardData> tabCards, String contentText, String contentPicture,
            String contentPicturePosition) {
        if (contentText == null)
            return;
        String[] contextArr = Optional.of(contentText).map(s -> s.split("######")).orElse(new String[0]);
        String[] pictureArr = Optional.ofNullable(contentPicture).map(s -> s.split("\n")).orElse(new String[0]);
        String[] positionArr = Optional.ofNullable(contentPicturePosition).map(s -> s.split("\n"))
                .orElse(new String[0]);
        if (contextArr.length != tabCards.size() || pictureArr.length != positionArr.length) {
            throw new IllegalArgumentException("内容图片与位置数量不一致");
        }
        if (contextArr.length == 0) {
            return;
        }
        for (int i = 0; i < contextArr.length; i++) {
            List<TabInfoDTO.Picture> pictures = null;
            if (i < pictureArr.length) {
                String[] contentPictures = pictureArr[i].split("###");
                String[] contentPicturePositions = positionArr[i].split("###");
                if (contentPictures.length != contentPicturePositions.length) {
                    throw new IllegalArgumentException("内容图片与位置数量不一致");
                }
                pictures = Lists.newArrayList();
                for (int j = 0; j < contentPictures.length; j++) {
                    pictures.add(new TabInfoDTO.Picture(contentPictures[j],
                            PicturePositionEnum.getCode(contentPicturePositions[j])));
                }
            }
            tabCards.get(i).setContent(new TabInfoDTO.ContentDTO(contextArr[i].trim(), pictures));
        }
    }

    private static void parseFilterInfo(FlattenImportTemplateData flattenImportTemplateData,
            ImportTemplateData importTemplateData) {
        List<TabCardData> tabCards = Lists.newArrayList();
        String[] filterTitleArr = Optional.ofNullable(flattenImportTemplateData.getFilterTitles()).orElse("NULL")
                .split("\n");
        String[] filterSearchWordArr = Optional.ofNullable(flattenImportTemplateData.getFilterSearchWords())
                .map(s -> s.split("\n")).orElse(new String[0]);
        String[] filterSubTitleArr = Optional.ofNullable(flattenImportTemplateData.getFilterSubtitles())
                .map(s -> s.split("\n")).orElse(new String[0]);
        if (filterSubTitleArr.length != 0 && filterSubTitleArr.length != filterTitleArr.length) {
            throw new IllegalArgumentException(flattenImportTemplateData.getQuestion() + "筛选项标题与副标题数量不一致");
        }
        if (filterSearchWordArr.length != 0 && filterSearchWordArr.length != filterTitleArr.length) {
            throw new IllegalArgumentException(flattenImportTemplateData.getQuestion() + "筛选项标题与搜索词数量不一致");
        }
        for (int i = 0; i < filterTitleArr.length; i++) {
            FilterItemDTO filterItemDTO = new FilterItemDTO();
            filterItemDTO.setTitle(filterTitleArr[i]);
            if (filterSubTitleArr.length > 0) {
                filterItemDTO.setSubTitle(getTextOrNull(filterSubTitleArr[i]));
            }
            if (filterSearchWordArr.length > 0) {
                filterItemDTO.setSearchWord(getTextOrNull(filterSearchWordArr[i]));
            }
            tabCards.add(new TabCardData(filterItemDTO, null, null, null));
        }
        importTemplateData.setTabCards(tabCards);
    }

    private static void parseBaseInfo(FlattenImportTemplateData flattenImportTemplateData,
            ImportTemplateData importTemplateData) {
        importTemplateData.setQuestion(flattenImportTemplateData.getQuestion());
        importTemplateData.setBizType(flattenImportTemplateData.getBizType());
        importTemplateData.setHeadInfo(new HeadInfoDTO(
                new HeadInfoDTO.TitleInfoDTO(
                        flattenImportTemplateData.getHeadLeftTitle(), flattenImportTemplateData.getHeadLeftSubtitle(),
                        flattenImportTemplateData.getHeadLeftIcon(), flattenImportTemplateData.getHeadLeftLink()),
                new HeadInfoDTO.MoreInfoDTO(flattenImportTemplateData.getHeadRightTitle(),
                        flattenImportTemplateData.getHeadRightIcon(), flattenImportTemplateData.getHeadRightLink())));
        importTemplateData.setTemplateType(flattenImportTemplateData.getTemplateType());
    }

    private static FlattenImportTemplateData convert2FlattenImportTemplateData(ImportTemplateData importTemplateData) {
        FlattenImportTemplateData flattenImportTemplateData = new FlattenImportTemplateData();
        convertBaseInfo(flattenImportTemplateData, importTemplateData);
        convertFilterInfo(flattenImportTemplateData, importTemplateData.getTabCards());
        convertContentInfo(flattenImportTemplateData, importTemplateData.getTabCards());
        convertRelatedQuestions(flattenImportTemplateData, importTemplateData.getTabCards());
        convertProjectTabs(flattenImportTemplateData, importTemplateData.getTabCards());
        return flattenImportTemplateData;
    }

    private static void convertProjectTabs(FlattenImportTemplateData flattenImportTemplateData,
            List<TabCardData> tabCards) {
        if (CollectionUtils.isEmpty(tabCards)) {
            return;
        }
        StringJoiner titleJoiner = new StringJoiner("\n");
        StringJoiner subTitleJoiner = new StringJoiner("\n");
        StringJoiner linkJoiner = new StringJoiner("\n");
        StringJoiner searchWordJoiner = new StringJoiner("\n");
        tabCards.stream().filter(tabCard -> !CollectionUtils.isEmpty(tabCard.getProjectTabs())).forEach(tabCard -> {
            String title = Optional.ofNullable(tabCard.getFilterItem()).map(FilterItemDTO::getTitle).orElse(NULL);
            String innerTitle = connectWithDelimiter(tabCard.getProjectTabs(), TabInfoDTO.ProjectTabDTO::getTitle,
                    "###");
            String innerSubTitle = connectWithDelimiter(tabCard.getProjectTabs(), TabInfoDTO.ProjectTabDTO::getSubTitle,
                    "###");
            String innerLink = connectWithDelimiter(tabCard.getProjectTabs(), TabInfoDTO.ProjectTabDTO::getLink, "###");
            String innerSearchWord = connectWithDelimiter(tabCard.getProjectTabs(),
                    TabInfoDTO.ProjectTabDTO::getSearchWord, "###");
            addJoinerIfNotEmpty(title, innerTitle, titleJoiner);
            addJoinerIfNotEmpty(title, innerSubTitle, subTitleJoiner);
            addJoinerIfNotEmpty(title, innerLink, linkJoiner);
            addJoinerIfNotEmpty(title, innerSearchWord, searchWordJoiner);
        });
        flattenImportTemplateData.setProjectTitles(titleJoiner.toString());
        flattenImportTemplateData.setProjectSubtitles(subTitleJoiner.toString());
        flattenImportTemplateData.setProjectLinks(linkJoiner.toString());
        flattenImportTemplateData.setProjectSearchWords(searchWordJoiner.toString());
    }

    private static void convertRelatedQuestions(FlattenImportTemplateData flattenImportTemplateData,
            List<TabCardData> tabCards) {
        if (CollectionUtils.isEmpty(tabCards)) {
            return;
        }
        StringJoiner questionJoiner = new StringJoiner("\n");
        StringJoiner iconJoiner = new StringJoiner("\n");
        StringJoiner linkJoiner = new StringJoiner("\n");
        tabCards.stream().filter(tabCard -> !CollectionUtils.isEmpty(tabCard.getRelatedQuestions()))
                .forEach(tabCard -> {
                    String title = Optional.ofNullable(tabCard.getFilterItem()).map(FilterItemDTO::getTitle)
                            .orElse(NULL);
                    String innerQuestion = connectWithDelimiter(tabCard.getRelatedQuestions(),
                            TabInfoDTO.RelatedQuestion::getQuestion, "###");
                    String innerIcon = connectWithDelimiter(tabCard.getRelatedQuestions(),
                            TabInfoDTO.RelatedQuestion::getIcon, "###");
                    String innerLink = connectWithDelimiter(tabCard.getRelatedQuestions(),
                            TabInfoDTO.RelatedQuestion::getLink, "###");
                    addJoinerIfNotEmpty(title, innerQuestion, questionJoiner);
                    addJoinerIfNotEmpty(title, innerIcon, iconJoiner);
                    addJoinerIfNotEmpty(title, innerLink, linkJoiner);
                });
        flattenImportTemplateData.setRelatedQuestions(questionJoiner.toString());
        flattenImportTemplateData.setRelatedQuestionIcons(iconJoiner.toString());
        flattenImportTemplateData.setRelatedQuestionLinks(linkJoiner.toString());
    }

    private static void convertContentInfo(FlattenImportTemplateData flattenImportTemplateData,
            List<TabCardData> tabCards) {
        if (CollectionUtils.isEmpty(tabCards)) {
            return;
        }

        StringJoiner contentJoiner = new StringJoiner("######");
        StringJoiner pictureJoiner = new StringJoiner("\n");
        StringJoiner positionJoiner = new StringJoiner("\n");
        for (int i = 0; i < tabCards.size(); i++) {
            TabCardData tabCardData = tabCards.get(i);
            TabInfoDTO.ContentDTO content = tabCardData.getContent();
            if (content == null) {
                continue;
            }
            contentJoiner.add(content.getContent());
            if (!CollectionUtils.isEmpty(content.getPictures())) {
                pictureJoiner.add(joinPictures(content.getPictures(), "###"));
                positionJoiner.add(joinPositions(content.getPictures(), "###"));
            }
        }
        flattenImportTemplateData.setContentText(contentJoiner.toString());
        flattenImportTemplateData.setContentPicture(pictureJoiner.toString());
        flattenImportTemplateData.setContentPicturePosition(positionJoiner.toString());
    }

    private static void convertFilterInfo(FlattenImportTemplateData flattenImportTemplateData,
            List<TabCardData> tabCards) {
        if (CollectionUtils.isEmpty(tabCards)) {
            return;
        }
        flattenImportTemplateData.setFilterTitles(tabCards.stream()
                .map(tabCard -> tabCard.getFilterItem() == null ? "NULL" : tabCard.getFilterItem().getTitle())
                .collect(Collectors.joining("\n")));
        flattenImportTemplateData
                .setFilterSubtitles(tabCards.stream().map(TabCardData::getFilterItem).filter(Objects::nonNull)
                        .map(FilterItemDTO::getSubTitle).filter(Objects::nonNull).collect(Collectors.joining("\n")));
        flattenImportTemplateData.setFilterSearchWords(tabCards.stream().map(TabCardData::getFilterItem)
                .filter(filterItem -> filterItem != null && filterItem.getSearchWord() != null)
                .map(FilterItemDTO::getSearchWord).collect(Collectors.joining("\n")));
    }

    private static void convertBaseInfo(FlattenImportTemplateData flattenImportTemplateData,
            ImportTemplateData importTemplateData) {
        flattenImportTemplateData.setTemplateType(importTemplateData.getTemplateType());
        flattenImportTemplateData.setQuestion(importTemplateData.getQuestion());
        flattenImportTemplateData.setBizType(importTemplateData.getBizType());
        flattenImportTemplateData.setHeadLeftTitle(Optional.ofNullable(importTemplateData.getHeadInfo())
                .map(HeadInfoDTO::getTitleInfo).map(HeadInfoDTO.TitleInfoDTO::getTitle).orElse(null));
        flattenImportTemplateData.setHeadLeftSubtitle(Optional.ofNullable(importTemplateData.getHeadInfo())
                .map(HeadInfoDTO::getTitleInfo).map(HeadInfoDTO.TitleInfoDTO::getSubTitle).orElse(null));
        flattenImportTemplateData.setHeadLeftIcon(Optional.ofNullable(importTemplateData.getHeadInfo())
                .map(HeadInfoDTO::getTitleInfo).map(HeadInfoDTO.TitleInfoDTO::getIcon).orElse(null));
        flattenImportTemplateData.setHeadLeftLink(Optional.ofNullable(importTemplateData.getHeadInfo())
                .map(HeadInfoDTO::getTitleInfo).map(HeadInfoDTO.TitleInfoDTO::getLink).orElse(null));
        flattenImportTemplateData.setHeadRightTitle(Optional.ofNullable(importTemplateData.getHeadInfo())
                .map(HeadInfoDTO::getMoreInfo).map(HeadInfoDTO.MoreInfoDTO::getTitle).orElse(null));
        flattenImportTemplateData.setHeadRightIcon(Optional.ofNullable(importTemplateData.getHeadInfo())
                .map(HeadInfoDTO::getMoreInfo).map(HeadInfoDTO.MoreInfoDTO::getIcon).orElse(null));
        flattenImportTemplateData.setHeadRightLink(Optional.ofNullable(importTemplateData.getHeadInfo())
                .map(HeadInfoDTO::getMoreInfo).map(HeadInfoDTO.MoreInfoDTO::getLink).orElse(null));
    }

    private static <T> String connectWithDelimiter(List<T> data, Function<T, String> mapper, String delimiter) {
        return data.stream().map(mapper).map(Optional::ofNullable).map(optionalValue -> optionalValue.orElse(NULL))
                .collect(Collectors.joining(delimiter));
    }

    private static void addJoinerIfNotEmpty(String prefix, String innerData, StringJoiner resultJoiner) {
        if (StringUtils.isNotBlank(innerData.replaceAll("NULL|###", ""))) {
            resultJoiner.add(prefix + "###" + innerData);
        }
    }

    private static String joinPictures(List<TabInfoDTO.Picture> pictures, String delimiter) {
        return pictures.stream().map(TabInfoDTO.Picture::getUrl).collect(Collectors.joining(delimiter));
    }

    private static String joinPositions(List<TabInfoDTO.Picture> pictures, String delimiter) {
        return pictures.stream().map(picture -> PicturePositionEnum.valueOfCode(picture.getPosition()))
                .collect(Collectors.joining(delimiter));
    }
}
