package com.sankuai.dzim.pilot.buffer.stream.build;

import com.sankuai.dzim.pilot.buffer.enums.StreamEventTypeEnum;
import com.sankuai.dzim.pilot.buffer.stream.vo.BeamChoiceVO;
import com.sankuai.dzim.pilot.buffer.stream.vo.BeamErrorVO;
import com.sankuai.dzim.pilot.buffer.stream.vo.StreamEventDataVO;
import com.sankuai.dzim.pilot.buffer.stream.vo.StreamEventVO;
import com.sankuai.dzim.pilot.gateway.api.assistant.data.SseAwareException;

/**
 * @author: zhouyibing
 * @date: 2024/8/1
 */
public class StreamEventVOBuilder {

    public static StreamEventVO buildOpenEvent() {
        return StreamEventVO.builder()
                .type(StreamEventTypeEnum.OPEN.getType())
                .build();
    }

    public static StreamEventVO buildCloseEvent() {
        return StreamEventVO.builder()
                .type(StreamEventTypeEnum.CLOSE.getType())
                .build();
    }

    public static StreamEventVO buildErrorEvent(SseAwareException e) {
        return buildErrorEvent(e.getEvent(), e.getContent());
    }

    public static StreamEventVO buildErrorEvent(String event, String content) {
        return StreamEventVO.builder()
                .type(StreamEventTypeEnum.ERROR.getType())
                .data(StreamEventDataVO.builder().event(event).content(content).build())
                .build();
    }

    public static StreamEventVO buildBeamErrorEvent(String message) {
        BeamErrorVO errorVO = new BeamErrorVO();
        errorVO.setMessage(message);
        errorVO.setCode("500");
        errorVO.setType("sever_error");
        return StreamEventVO.builder()
                .error(errorVO)
                .build();
    }

    public static String buildBeamCloseEvent() {
        return "[DONE]";
    }
}
