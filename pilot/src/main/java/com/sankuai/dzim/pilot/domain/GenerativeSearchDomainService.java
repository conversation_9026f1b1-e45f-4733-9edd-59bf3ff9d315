package com.sankuai.dzim.pilot.domain;

import com.sankuai.dzim.pilot.api.data.search.generative.AddAnswerFeedbackRequest;
import com.sankuai.dzim.pilot.api.data.search.generative.QueryAnswerRequest;
import com.sankuai.dzim.pilot.api.data.search.generative.QueryProjectRelatedSuggestionRequest;
import com.sankuai.dzim.pilot.dal.entity.GenerativeSearchAnswerEntity;
import com.sankuai.dzim.pilot.domain.data.GenerativeSearchE;
import com.sankuai.dzim.pilot.domain.data.GenerativeSearchExtendData;
import com.sankuai.dzim.pilot.domain.data.GenerativeSearchWordE;

import java.util.List;
import java.util.Map;

public interface GenerativeSearchDomainService {

    /**
     * 添加生成式搜索答案
     * @return
     */
    Boolean addGenerativeSearchAnswer(GenerativeSearchE generativeSearchE);

    /**
     * 导出生成式搜索答案,按主键遍历
     * @return
     */
    List<GenerativeSearchE> rangeFindGenerativeSearchAnswer(long startId, long endId);

    /**
     * 添加生成式搜索记录
     * @return
     */
    GenerativeSearchE getGenerativeSearchAnswer(QueryAnswerRequest request);

    /**
     * 添加生成式搜索记录
     * @return
     */
    GenerativeSearchE getGenerativeSearchAnswerWithExtend(QueryAnswerRequest request);

    /**
     * 添加答案反馈
     * @param request
     * @return
     */
    Boolean addAnswerFeedback(AddAnswerFeedbackRequest request);

    /**
     * 更新生成式搜索答案
     * @param generativeSearchE
     * @return
     */
    Boolean updateGenerativeSearchAnswer(GenerativeSearchE generativeSearchE);

    /**
     * 查询项目关联的推荐答案
     * @param request
     * @return
     */
    GenerativeSearchE getGenerativeSearchRelatedProject(QueryProjectRelatedSuggestionRequest request);

    /**
     * 根据id查询生成式搜索答案
     * @param id
     * @return
     */
    GenerativeSearchE getGenerativeSearchAnswerById(long id);

    /**
     * 批量查询生成式搜索答案（去除answer字段为空的数据）
     * @param questionList
     * @return
     */
    List<GenerativeSearchE> batchGetGenerativeSearchAnswerByQuestion(int bizType, List<String> questionList);


    /**
     * 批量查询生成式搜索结果，去除templateData为空的数据
     * @param bizType
     * @param questionList
     * @return
     */
    List<GenerativeSearchE> listAnswerByQuestion(int bizType, List<String> questionList);
    /**
     * 删除生成式搜索答案
     * @param idList
     * @return
     */
    Integer deleteGenerativeSearchAnswer(List<Long> idList);

    /**
     * 是否已经存在答案
     * @param bizType
     * @param question
     * @return
     */
    boolean hasQuestion(int bizType, String question);

    /**
     * 查询生成式搜索query词
     * @param bizType
     * @param question
     * @return
     */
    GenerativeSearchWordE getGenerativeSearchWordByQuestion(int bizType, String question);

    /**
     * 批量查询生成式搜索query词
     * @param questionList
     * @return
     */
    List<GenerativeSearchWordE> batchGetGenerativeSearchWordByQuestion(int bizType, List<String> questionList);

    /**
     * 更新生成式搜索query词
     * @param generativeSearchWordE
     * @return
     */
    Boolean updateGenerativeSearchWord(GenerativeSearchWordE generativeSearchWordE);

    /**
     * 添加生成式搜索词
     * @param generativeSearchWordE
     * @return
     */
    Boolean addGenerativeSearchWord(GenerativeSearchWordE generativeSearchWordE);

    /**
     * 遍历生成式搜索query词
     * @param startId
     * @param endId
     * @return
     */
    List<GenerativeSearchWordE> rangeFindGenerativeSearchWord(long startId, long endId);

    /**
     * 根据keyword查询相关词
     * @param bizType
     * @param keyword
     * @return
     */
    List<GenerativeSearchWordE> findByKeyword(int bizType, String keyword);

    /**
     * 查询生成式搜索答案关联extend表
     * 用于sug返回前查询是否有供给
     * @param questionList
     * @param attrMap
     * @return
     */
    Map<String, Integer> getGenerativeSearchAnswerExtendCount(int bizType, List<String> questionList, Map<String, Object> attrMap);

    /**
     * 删除生成式搜索word
     * @param idList
     * @return
     */
    int deleteGenerativeSearchWord(List<Long> idList);

    /**
     * 添加生成式搜索答案扩展数据
     * @param answerId
     * @param extendDatas
     * @return
     */
    Boolean addGenerativeSearchAnswerExtend(Long answerId, List<GenerativeSearchExtendData> extendDatas);

    GenerativeSearchE getGenerativeSearchAnswerListWithExtend(QueryAnswerRequest request, GenerativeSearchWordE word);

    long getGenerativeSearchWordCountByBizType(Integer bizType);

    /**
     * 百补和生成式searchword冲突的词 status设为2
     * @param idList
     * @return
     */
    int deleteBaiBuConflictAnswer(List<Long> idList, Integer status);

    List<GenerativeSearchWordE> batchFindByAssistantAndTemplate(int assistantType, List<Integer> templateTypes);

    /**
     * 插入或更新模板数据
     * @param newData
     */
    void insertOrUpdateTemplateData(GenerativeSearchAnswerEntity newData);
}
