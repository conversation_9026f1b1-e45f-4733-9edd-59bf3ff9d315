package com.sankuai.dzim.pilot.acl.data.fraiday.app;

import retrofit2.Call;
import retrofit2.http.FieldMap;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.POST;

import java.util.Map;

/**
 * FRIDAY鉴权api
 * https://docs.sankuai.com/ai-speech/asr/auth/#http-token
 */
public interface FridayTokenApiService {

    /**
     * friday鉴权
     *
     * @param fields 鉴权参数
     * @return
     */
    @FormUrlEncoded
    @POST("oauth/v2/token")
    Call<FridayTokenResponse<FridayAccessToken>> auth(@FieldMap Map<String, String> fields);
}
