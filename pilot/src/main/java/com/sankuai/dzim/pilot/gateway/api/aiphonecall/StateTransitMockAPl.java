package com.sankuai.dzim.pilot.gateway.api.aiphonecall;

import com.dianping.gateway.enums.APIModeEnum;
import com.dianping.vc.web.api.API;
import com.dianping.vc.web.api.URL;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.pilot.api.enums.assistant.PlatformEnum;
import com.sankuai.dzim.pilot.dal.entity.pilot.medical.DiseaseScienceInfoEntity;
import com.sankuai.dzim.pilot.dal.respository.medical.DiseaseScienceInfoRepositoryService;
import com.sankuai.dzim.pilot.domain.retrieval.impl.DiseaseSciencePageAugmentor;
import com.sankuai.dzim.pilot.enums.AIPhoneCallTaskStatusEnum;
import com.sankuai.dzim.pilot.gateway.api.medical.data.DiseaseScienceInfoVO;
import com.sankuai.dzim.pilot.process.aiphonecall.impl.AIPhoneCallStateManager;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Setter
@Slf4j
@Service
@URL(url = "/dzim/pilot/aiphonecall/transit/test", mode = APIModeEnum.CLIENT, methods = "GET")
public class StateTransitMockAPl implements API {

    @Autowired
    private AIPhoneCallStateManager stateManager;

    private int status;

    private long taskId;

    private String reason;

    @Override
    public Object execute() {
        AIPhoneCallTaskStatusEnum callTaskStatusEnum = AIPhoneCallTaskStatusEnum.getByStatus(status);
        stateManager.transitAICallTaskState(taskId, callTaskStatusEnum, StringUtils.defaultString(reason, "手动执行"));
        return null;
    }
}
