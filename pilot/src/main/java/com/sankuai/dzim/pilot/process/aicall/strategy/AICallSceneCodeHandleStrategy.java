package com.sankuai.dzim.pilot.process.aicall.strategy;

import com.sankuai.dzim.pilot.api.data.beauty.ai.AICallBackReq;
import com.sankuai.dzim.pilot.api.data.beauty.ai.AICallShopResultDTO;
import com.sankuai.dzim.pilot.dal.entity.aicall.AICallDetailEntity;
import com.sankuai.dzim.pilot.dal.entity.aicall.AICallTaskEntity;
import com.sankuai.dzim.pilot.process.aicall.data.UserCallRequest;
import com.sankuai.gaigc.arrange.api.thrift.dto.request.WaihuCallRequest;

import java.util.Date;
import java.util.List;

public interface AICallSceneCodeHandleStrategy {

    boolean accept(Integer sceneCode);

    /**
     * 召回门店
     *
     * @param request
     * @param recalledShops
     * @param pageNo
     * @param pageSize
     * @return
     */
    List<Long> shopRecommandAndFilter(UserCallRequest request, List<Long> recalledShops, int pageNo, int pageSize);

    /**
     * 外呼前判断门店是否可以外呼
     *
     * @param dpShopId
     * @param mtShopId
     * @param detailId
     * @param taskEntity
     * @return
     */
    boolean isShopCanCall(long dpShopId, long mtShopId, long detailId, AICallTaskEntity taskEntity);

    /**
     * 构建外呼入参
     *
     * @param shopName
     * @param shopMobile
     * @param gender
     * @param userPlainPhone
     * @param taskEntity
     * @param detailEntity
     * @return
     */
    WaihuCallRequest buildWaiHuRequest(String shopName, String shopMobile, int gender, String userPlainPhone,
                                       AICallTaskEntity taskEntity, AICallDetailEntity detailEntity);

    /**
     * 获取外呼detail结果
     *
     * @param req
     * @param sceneCode
     * @return
     */
    int getDetailResult(AICallBackReq req, Integer sceneCode);

    /**
     * 处理门店重拨流程
     *
     * @param req
     * @param taskEntity
     * @return
     */
    boolean processCanCallAgain(AICallBackReq req, AICallTaskEntity taskEntity, AICallDetailEntity detailEntity);

    /**
     * 单门店失败，处理单天拉黑流程
     *
     * @param detailEntity
     * @param dpShopId
     * @param mtShopId
     */
    boolean processSpecificAddDailyBlackList(AICallBackReq req, AICallDetailEntity detailEntity, long dpShopId, long mtShopId, int code);

    /**
     * 单门店处理失败，处理多天拉黑流程
     * @param detailEntity
     * @param dpShopId
     * @param mtShopId
     * @param code
     * @param day
     * @return
     */
    boolean processSpecificAddMultiDayBlackList(AICallBackReq req, AICallDetailEntity detailEntity, long dpShopId, long mtShopId, int code);

    /**
     * 二次外呼前检查外呼记录
     *
     * @param detailId
     * @return
     */
    boolean checkRecordBeforeCallAgain(long detailId, long dpShopId, long mtShopId);

    /**
     * 执行外呼流程
     * @param
     * @return
     */
    void executePhoneCall(AICallTaskEntity taskEntity, List<AICallDetailEntity> waitCallDetails);

    /**
     * 构造每个门店外呼结果对象，后续返回mq
     * @param detailId
     * @return
     */
    Object buildShopResultData(long detailId);
}
