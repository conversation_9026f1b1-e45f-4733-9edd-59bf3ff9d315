package com.sankuai.dzim.pilot.chain.eval.data;

import com.sankuai.dzim.pilot.domain.message.Message;
import com.sankuai.it.xcontract.easypoi.annotation.Excel;
import lombok.Data;

import java.util.List;

/**
 * Beam意图理解评测集
 */
@Data
public class BeamIntentUnderStandEvalCase {

    @Excel(name = "context")
    private String context;

    @Excel(name = "userQuery")
    private String userQuery;

    @Excel(name = "history")
    private String history;

    @Excel(name = "rewriteUserQuery")
    private String rewriteUserQuery;

    @Excel(name = "rewriteHistory")
    private String rewriteHistory;

    @Excel(name = "toolName")
    private String toolName;

    @Excel(name = "toolParam")
    private String toolParam;

}
