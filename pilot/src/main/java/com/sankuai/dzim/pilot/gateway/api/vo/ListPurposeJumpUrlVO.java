package com.sankuai.dzim.pilot.gateway.api.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;

@Data
@MobileDo
public class ListPurposeJumpUrlVO implements Serializable {
    @MobileDo.MobileField(key = 0xe539)
    private String jumpUrl;

    @MobileDo.MobileField(key = 0xf39b)
    private String iconUrl;
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    @MobileDo.MobileField(key = 0x6cf9)
    private Integer clickPoiCnt;
    @MobileDo.MobileField(key = 0x9a90)
    private Integer slideDistance;
    @MobileDo.MobileField(key = 0xcdb6)
    private Integer triggerLimit;
    @MobileDo.MobileField(key = 0xd8b3)
    private Integer openKeepTime;
}
