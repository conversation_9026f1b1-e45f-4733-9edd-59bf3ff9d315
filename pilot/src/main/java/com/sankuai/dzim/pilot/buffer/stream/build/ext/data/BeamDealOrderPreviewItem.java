package com.sankuai.dzim.pilot.buffer.stream.build.ext.data;

import lombok.Data;

import java.util.List;

/**
 * 团购订单预览项
 *
 * <AUTHOR>
 * @date 2025/4/29
 */
@Data
public class BeamDealOrderPreviewItem {

    private String headImageUrl;

    private String productName;

    private Integer quantity;

    private Long totalPayAmount;

    private String jumpUrl;

    private List<String> tags;

    private String slaveText;
}
