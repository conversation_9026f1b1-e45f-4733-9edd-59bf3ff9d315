package com.sankuai.dzim.pilot.chain.eval.evaluator;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.chain.eval.data.EvalCase;
import com.sankuai.dzim.pilot.chain.eval.data.EvalCaseResult;
import com.sankuai.dzim.pilot.chain.eval.data.EvalContext;
import com.sankuai.dzim.pilot.domain.message.PluginCall;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.Iterator;
import java.util.List;

/**
 * 函数调用评测器
 *
 * @author: zhouyibing
 * @date: 2025/4/18
 */
@Component
@Slf4j
public class FunctionCallingEvaluator implements Evaluator {

    private final ObjectMapper objectMapper = new ObjectMapper();


    @Override
    public boolean accept(String evaluator) {
        return this.getClass().getSimpleName().equals(evaluator);
    }

    @Override
    public EvalCaseResult measure(EvalContext evalContext, EvalCase evalCase) {
        List<PluginCall> targetCalls = parsePluginCall(evalCase.getTargetOutput());
        List<PluginCall> actualCalls = parsePluginCall(evalCase.getOutput());

        if (CollectionUtils.isEmpty(targetCalls)) {
            throw new RuntimeException("目标工具调用为空");
        }
        if (CollectionUtils.isEmpty(actualCalls)) {
            throw new RuntimeException("实际工具调用为空");
        }

        // todo 多个tool待扩展
        PluginCall targetCall = targetCalls.get(0);
        PluginCall actualCall = actualCalls.get(0);

        // 工具准确率
        boolean toolAccuracy = evaluateToolAccuracy(targetCall, actualCall);
        // 参数准确率，todo 目前是equals完全匹配，语义化参数待扩展
        boolean paramAccuracy = evaluateParamAccuracy(targetCall, actualCall);
        // 整体准确率
        boolean functionCallingAccuracy = toolAccuracy && paramAccuracy;

        // 组装结果
        EvalCaseResult evalCaseResult = new EvalCaseResult();
        BeanUtils.copyProperties(evalCase, evalCaseResult);
        evalCaseResult.setFunctionCallingAccuracy(functionCallingAccuracy);

        return evalCaseResult;
    }

    private List<PluginCall> parsePluginCall(String json) {
        try {
            return objectMapper.readValue(json, new TypeReference<List<PluginCall>>() {});
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("parsePluginCall").build(),
                    new WarnMessage("FunctionCallingEvaluator", "解析模型回复异常", null), json, null, e);
            return null;
        }
    }

    private boolean evaluateToolAccuracy(PluginCall target, PluginCall actual) {
        return target.getPluginName().equals(actual.getPluginName());
    }

    private boolean evaluateParamAccuracy(PluginCall target, PluginCall actual) {
        JsonNode targetArguments = target.getArguments();
        JsonNode actualArguments = actual.getArguments();
        if (targetArguments == null && actualArguments == null) {
            return true;
        }
        if (targetArguments == null || actualArguments == null) {
            return false;
        }

        try {
            // 检查所有key是否匹配
            Iterator<String> targetFields = targetArguments.fieldNames();
            while (targetFields.hasNext()) {
                String key = targetFields.next();
                if (!actualArguments.has(key)) {
                    return false;
                }
                // 比较每个key对应的值
                if (!targetArguments.get(key).equals(actualArguments.get(key))) {
                    return false;
                }
            }

            // 检查actual是否有多余的key
            Iterator<String> actualFields = actualArguments.fieldNames();
            while (actualFields.hasNext()) {
                String key = actualFields.next();
                if (!targetArguments.has(key)) {
                    return false;
                }
            }

            return true;
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("evaluateParamAccuracy").build(),
                    new WarnMessage("FunctionCallingEvaluator", "参数对比异常", null), actual, null, e);
            return false;
        }
    }
}
