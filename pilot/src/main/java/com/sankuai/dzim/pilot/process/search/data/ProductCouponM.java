package com.sankuai.dzim.pilot.process.search.data;

import lombok.Data;

/**
 * Created by float.lu on 2020/9/8.
 */
@Data
public class ProductCouponM {
    /**
     * 购买返券标签
     */
    private String couponTag;

    /**
     * 券ID
     */
    private long couponId;

    /**
     * 分流位id
     */
    private long flowId;

    /**
     * 投放券rowKey
     */
    private String rowKey;

    /**
     * 投放活动id
     */
    private long resourceActivityId;

    /**
     * 投放活动券发券专用-活动id
     */
    private long activityId;

    /**
     * 投放活动券发券专用-物料id
     */
    private String materialId;

    /**
     * 总库存
     */
    private int totalStock;

    /**
     * 总剩余库存
     */
    private int remainStock;
}
