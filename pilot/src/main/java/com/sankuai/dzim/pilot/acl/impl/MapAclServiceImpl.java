package com.sankuai.dzim.pilot.acl.impl;

import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.sankuai.dzim.pilot.acl.MapAclService;
import com.sankuai.map.open.platform.api.MapOpenApiService;
import mtmap.geoinfo.geoinfo_base.SearchServiceRequest;
import mtmap.geoinfo.geoinfo_base.SearchServiceResponse;
import org.apache.thrift.TException;
import org.springframework.stereotype.Repository;

@Repository
public class MapAclServiceImpl implements MapAclService {

    @MdpThriftClient(remoteAppKey = "com.sankuai.apigw.map.facadecenter", timeout = 1000)
    MapOpenApiService.Iface mapOpenApiService;

    @Override
    public SearchServiceResponse searchShop(SearchServiceRequest request) {
        try {
            SearchServiceResponse text = mapOpenApiService.text(request);
            return text;
        } catch (TException e) {
            e.printStackTrace();
        }

        return null;
    }
}
