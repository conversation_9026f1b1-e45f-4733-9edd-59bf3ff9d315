package com.sankuai.dzim.pilot.acl;

import com.dianping.haima.client.response.HaimaResponse;
import com.sankuai.dzim.pilot.process.aireservebook.data.StandardReserveItem;
import com.sankuai.dzim.pilot.process.data.AIServiceConfig;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/12/18 17:40
 */
public interface HaimaAclService {
    HaimaResponse queryConfig(String sceneKey);

    List<String> queryFuzzySkipWords(int bizType);

    String queryQuestionMapping(Integer bizType, String question);

    AIServiceConfig queryAIConfig(String key);

    /**
     * 根据POI后台类目查询当前行业的标准预约要素
     * 仅返回第一个匹配到的POI后台类目对应的结果（会优先按最深层级匹配）
     * @param backCategory POI后台类目
     * @return
     */
    List<StandardReserveItem> queryStandardReserveItems(Integer backCategory);

    /**
     * 行为和queryStandardReserveItems一样，beam子Agent使用
     * @param backCategory
     * @return
     */
    List<StandardReserveItem> queryBeamStandardReserveItems(Integer backCategory);
}
