package com.sankuai.dzim.pilot.scene.task;

import com.sankuai.dzim.pilot.scene.task.data.TaskContext;
import com.sankuai.dzim.pilot.scene.task.data.TaskResult;

/**
 * @author: zhouyi<PERSON>
 * @date: 2024/7/26
 */
public interface Task {

    boolean accept(TaskContext context);

    TaskResult execute(TaskContext context);

    void afterExecute(TaskResult result);

    default TaskResult processException(TaskContext context) {
        return null;
    }
}
