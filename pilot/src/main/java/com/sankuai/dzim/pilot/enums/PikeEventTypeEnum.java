package com.sankuai.dzim.pilot.enums;

import lombok.Getter;

/**
 * @author: zhouyibing
 * @date: 2025/4/24
 */
@Getter
public enum PikeEventTypeEnum {

    ADD_DIALOG("addDialog", "主动推送聊天卡片"),

    CHANGE_DIALOG("changeDialog", "修改历史卡片"),

    INTERRUPT_CHANGE_DIALOG("interruptChangeDialog", "中断对话推送卡片")

    ;

    private String type;

    private String desc;

    PikeEventTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
