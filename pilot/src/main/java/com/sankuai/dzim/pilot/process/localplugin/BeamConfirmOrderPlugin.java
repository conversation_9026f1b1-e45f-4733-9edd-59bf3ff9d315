package com.sankuai.dzim.pilot.process.localplugin;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.deal.common.enums.ClientTypeEnum;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.nibtp.trade.client.combine.bean.WebParamDTO;
import com.meituan.nibtp.trade.client.combine.requset.AgentPreviewReqDTO;
import com.meituan.nibtp.trade.client.combine.requset.AgentProductItemReqDTO;
import com.meituan.nibtp.trade.client.combine.requset.AssignPromotionItemDTO;
import com.meituan.nibtp.trade.client.combine.response.AgentPreviewItemResDTO;
import com.meituan.nibtp.trade.client.combine.response.AgentPreviewResDTO;
import com.sankuai.dealuser.price.display.api.enums.RequestSceneEnum;
import com.sankuai.dealuser.price.display.api.model.*;
import com.sankuai.dzim.pilot.acl.AgentTradeAclService;
import com.sankuai.dzim.pilot.acl.ProductAclService;
import com.sankuai.dzim.pilot.acl.ShopAclService;
import com.sankuai.dzim.pilot.api.data.AIAnswerTypeEnum;
import com.sankuai.dzim.pilot.api.enums.ProductTypeEnum;
import com.sankuai.dzim.pilot.buffer.core.PilotBufferItemDO;
import com.sankuai.dzim.pilot.buffer.enums.BufferItemTypeEnum;
import com.sankuai.dzim.pilot.buffer.enums.StreamEventCardTypeEnum;
import com.sankuai.dzim.pilot.buffer.stream.build.ext.data.BeamDealOrderPreviewData;
import com.sankuai.dzim.pilot.buffer.stream.build.ext.data.BeamDealOrderPreviewItem;
import com.sankuai.dzim.pilot.buffer.utils.BufferUtils;
import com.sankuai.dzim.pilot.chain.data.AIAnswerData;
import com.sankuai.dzim.pilot.domain.annotation.LocalPlugin;
import com.sankuai.dzim.pilot.gateway.api.assistant.data.SendBeamMessageReq;
import com.sankuai.dzim.pilot.process.localplugin.param.BeamConfirmOrderParam;
import com.sankuai.dzim.pilot.utils.PluginContextUtil;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.sinai.data.api.dto.MtPoiDTO;
import com.sankuai.sinai.data.api.util.MtPoiUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * Beam确认下单工具
 *
 * <AUTHOR>
 * @date 2025/4/29
 */
@Component
@Slf4j
public class BeamConfirmOrderPlugin {

    @Resource
    private AgentTradeAclService agentTradeAclService;

    @Resource
    private ProductAclService productAclService;

    @Resource
    private ShopAclService shopAclService;

    @LocalPlugin(name = "ConfirmOrderTool", description = "确认下单工具。确定交易的商品后，在下单支付前先进行下单确认，生成交易预览。", returnDirect = true)
    public AIAnswerData getBeamConfirmOrderAnswer(BeamConfirmOrderParam param) {
        log.info("ConfirmOrderTool invoke param: {}", JsonCodec.encodeWithUTF8(param));
        SendBeamMessageReq sendBeamMessageReq = PluginContextUtil.getBeamRequestContext(param);

        // 校验参数
        Pair<Boolean, AIAnswerData> paramCheckResult = checkParam(param, sendBeamMessageReq);
        if (!paramCheckResult.getLeft()) {
            return paramCheckResult.getRight();
        }

        DealGroupDTO dealGroupDTO = productAclService.getDealBaseInfo(param.getProductId(), true);
        log.info("ConfirmOrderTool, getBeamConfirmOrderAnswer dealGroupDTO = {}", JsonCodec.encodeWithUTF8(dealGroupDTO));
        if (dealGroupDTO == null || dealGroupDTO.getBasic() == null || dealGroupDTO.getBasic().getStatus() == null) {
            log.error("调用下单工具 productAclService.getDealBaseInfo 失败, dealGroupDTO={}", JsonCodec.encodeWithUTF8(dealGroupDTO));
            BufferUtils.writeMainTextBuffer(PilotBufferItemDO.builder().data("订单预览生成失败，请稍后重试～").build());
            return new AIAnswerData(AIAnswerTypeEnum.TEXT.getType(), "订单预览生成失败，请稍后重试～", true);
        }

        if (productAclService.isSpecialDealGroup(dealGroupDTO)) {
            BufferUtils.writeMainTextBuffer(PilotBufferItemDO.builder().data("暂不支持此类团购下单").build());
            return new AIAnswerData(AIAnswerTypeEnum.TEXT.getType(), "暂不支持此类团购下单", true);
        }

        AgentPreviewReqDTO agentPreviewReqDTO = buildAgentPreviewReqDTO(param, sendBeamMessageReq);
        if (agentPreviewReqDTO == null) {
            BufferUtils.writeMainTextBuffer(PilotBufferItemDO.builder().data("订单预览生成失败，请稍后重试～").build());
            return new AIAnswerData(AIAnswerTypeEnum.TEXT.getType(), "订单预览生成失败，请稍后重试～", true);
        }

        AgentPreviewResDTO agentPreviewResDTO = agentTradeAclService.preview(agentPreviewReqDTO);
        if (agentPreviewResDTO == null || agentPreviewResDTO.isFail()) {
            log.error("调用下单工具 agentTradeAclService.preview 失败");
            BufferUtils.writeMainTextBuffer(PilotBufferItemDO.builder().data("订单预览生成失败，请稍后重试～").build());
            return new AIAnswerData(AIAnswerTypeEnum.TEXT.getType(), "订单预览生成失败，请稍后重试～", true);
        }

        BeamDealOrderPreviewData dealOrderPreviewData = new BeamDealOrderPreviewData();
        dealOrderPreviewData.setPreviewId(agentPreviewResDTO.getPreviewId());
        dealOrderPreviewData.setPreviewItemList(buildDealOrderPreviewItem(agentPreviewResDTO.getPreviewItemList()));

        PilotBufferItemDO pilotBufferItemDO = new PilotBufferItemDO();
        pilotBufferItemDO.setType(BufferItemTypeEnum.MAIN_TEXT.getType());
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("previewId", agentPreviewResDTO.getPreviewId());
        jsonObject.put("productId", param.getProductId());
        jsonObject.put("productName", agentPreviewResDTO.getPreviewItemList().get(0).getProductName());
        jsonObject.put("quantity", param.getQuantity());

        String data = StreamEventCardTypeEnum.buildCardContent(StreamEventCardTypeEnum.BEAM_DEAL_ORDER_PREVIEW, JSON.toJSONString(jsonObject));
        pilotBufferItemDO.setData("需要帮你下单吗\n" + data);
        pilotBufferItemDO.setExtra(JsonCodec.converseMap(JsonCodec.encodeWithUTF8(dealOrderPreviewData), String.class, Object.class));
        BufferUtils.writeMainTextBuffer(pilotBufferItemDO);

        return new AIAnswerData(AIAnswerTypeEnum.TEXT.getType(), "交易预览生成成功", true);
    }

    private Pair<Boolean, AIAnswerData> checkParam(BeamConfirmOrderParam param, SendBeamMessageReq sendBeamMessageReq) {
        if (sendBeamMessageReq == null || sendBeamMessageReq.getUser_info() == null || sendBeamMessageReq.getUser_info().getUserid() == null) {
            log.error("ConfirmOrderTool, getBeamConfirmOrderAnswer sendBeamMessageReq is invalid, sendBeamMessageReq={}", sendBeamMessageReq);
            BufferUtils.writeMainTextBuffer(PilotBufferItemDO.builder().data("订单预览生成失败，请稍后重试～").build());
            return Pair.of(false, new AIAnswerData(AIAnswerTypeEnum.TEXT.getType(), "订单预览生成失败，请稍后重试～", true));
        }

        if (sendBeamMessageReq.getContext() == null || sendBeamMessageReq.getContext().getPoi_id() == null) {
            log.error("ConfirmOrderTool, getBeamConfirmOrderAnswer context or poiid is invalid, sendBeamMessageReq={}", sendBeamMessageReq);
            BufferUtils.writeMainTextBuffer(PilotBufferItemDO.builder().data("订单预览生成失败，请稍后重试～").build());
            return Pair.of(false,new AIAnswerData(AIAnswerTypeEnum.TEXT.getType(), "订单预览生成失败，请稍后重试～", true));
        }

        // 不存在商品id时，先和用户确认交易商品
        if (Optional.ofNullable(param.getProductId()).orElse(0L) <= 10) {
            BufferUtils.writeMainTextBuffer(PilotBufferItemDO.builder().data("下单失败，商品未找到。\n").build());
            return Pair.of(false, new AIAnswerData(AIAnswerTypeEnum.TEXT.getType(), "下单失败，商品未找到。请先调用店内找品工具和用户确认交易商品。", false));
        }

        String tips = "暂不支持下单预订商品哦";
        MtPoiDTO mtPoiDTO = shopAclService.getMtShopInfo(Long.parseLong(sendBeamMessageReq.getContext().getPoi_id()));
        String shopName = mtPoiDTO != null? MtPoiUtil.getMtPoiName(mtPoiDTO.getName(), mtPoiDTO.getBranchName()): null;
        if (StringUtils.isNotEmpty(shopName)) {
            tips = String.format("实在抱款，Mia暂时还无法帮你搞定，需要你前往美团搜索%s完成预订", shopName);
        }

        // 预订商品不支持下单
        if (StringUtils.defaultString(param.getProductType()).equals("预订")) {
            BufferUtils.writeMainTextBuffer(PilotBufferItemDO.builder().data(tips).build());
            return Pair.of(false, new AIAnswerData(AIAnswerTypeEnum.TEXT.getType(), tips, true));
        }
        return Pair.of(true, null);
    }

    private List<BeamDealOrderPreviewItem> buildDealOrderPreviewItem(List<AgentPreviewItemResDTO> previewItemList) {
        if (CollectionUtils.isEmpty(previewItemList)) {
            return Lists.newArrayList();
        }
        List<BeamDealOrderPreviewItem> dealOrderPreviewItemList = new ArrayList<>();
        for (AgentPreviewItemResDTO agentPreviewItemResDTO : previewItemList) {
            BeamDealOrderPreviewItem dealOrderPreviewItem = new BeamDealOrderPreviewItem();
            dealOrderPreviewItem.setHeadImageUrl(agentPreviewItemResDTO.getHeadImageUrl());
            dealOrderPreviewItem.setProductName(agentPreviewItemResDTO.getProductName());
            dealOrderPreviewItem.setQuantity(agentPreviewItemResDTO.getQuantity());
            dealOrderPreviewItem.setTotalPayAmount(agentPreviewItemResDTO.getTotalPayAmount());
            dealOrderPreviewItem.setJumpUrl(agentPreviewItemResDTO.getJumpUrl());
            dealOrderPreviewItem.setTags(agentPreviewItemResDTO.getTags());
//            dealOrderPreviewItem.setSlaveText();
            dealOrderPreviewItemList.add(dealOrderPreviewItem);
        }
        return dealOrderPreviewItemList;
    }


    private AgentPreviewReqDTO buildAgentPreviewReqDTO(BeamConfirmOrderParam param, SendBeamMessageReq sendBeamMessageReq) {
        WebParamDTO webParamDTO = agentTradeAclService.buildWebParam(sendBeamMessageReq);
        if (webParamDTO == null) {
            return null;
        }

        List<AssignPromotionItemDTO> assignPromotionItemList = agentTradeAclService.buildAssignPromotionItemList(buildBatchPriceRequest(param, sendBeamMessageReq));

        AgentPreviewReqDTO agentPreviewReqDTO = new AgentPreviewReqDTO();
        AgentProductItemReqDTO agentProductItemReqDTO = new AgentProductItemReqDTO();
        agentProductItemReqDTO.setQuantity(param.getQuantity());
        agentProductItemReqDTO.setProductId(param.getProductId());
        agentProductItemReqDTO.setPoiId(Long.parseLong(sendBeamMessageReq.getContext().getPoi_id()));
        agentProductItemReqDTO.setBizType(2);

        List<AgentProductItemReqDTO> productItemList = new ArrayList<>();
        productItemList.add(agentProductItemReqDTO);
        agentPreviewReqDTO.setProductItemList(productItemList);
        agentPreviewReqDTO.setWebParam(webParamDTO);
        if (CollectionUtils.isNotEmpty(assignPromotionItemList)) {
            agentPreviewReqDTO.setAssignPromotionItemList(assignPromotionItemList);
        }
        return agentPreviewReqDTO;
    }

    private BatchPriceRequest buildBatchPriceRequest(BeamConfirmOrderParam param, SendBeamMessageReq sendBeamMessageReq) {
        Map<Long, List<ProductIdentity>> longShopId2ProductIds = Maps.newHashMap();
        Long poiId = Long.parseLong(sendBeamMessageReq.getContext().getPoi_id());
        longShopId2ProductIds.put(poiId, Lists.newArrayList(new ProductIdentity(param.getProductId().intValue(), ProductTypeEnum.DEAL.getType())));

        ClientEnv clientEnv = new ClientEnv();
        clientEnv.setGpsCityId(Optional.ofNullable(sendBeamMessageReq.getUser_info().getCity_id()).map(Integer::parseInt).orElse(0));
        clientEnv.setCityId(Optional.ofNullable(sendBeamMessageReq.getUser_info().getIntent_city_id()).map(Integer::parseInt).orElse(0));
        clientEnv.setLongitude(Optional.ofNullable(sendBeamMessageReq.getUser_info().getLongitude()).map(Double::parseDouble).orElse(null));
        clientEnv.setLatitude(Optional.ofNullable(sendBeamMessageReq.getUser_info().getLatitude()).map(Double::parseDouble).orElse(null));
        clientEnv.setUuid(sendBeamMessageReq.getUser_info().getUuid());
        clientEnv.setVersion(sendBeamMessageReq.getUser_info().getAppversion());
        clientEnv.setClientType(getClientType(sendBeamMessageReq.getUser_info().getClient_type()));

        BatchPriceRequest batchPriceRequest = new BatchPriceRequest();
        batchPriceRequest.setLongShopId2ProductIds(longShopId2ProductIds);
        batchPriceRequest.setUserId(Long.parseLong(sendBeamMessageReq.getUser_info().getUserid()));
        batchPriceRequest.setClientEnv(clientEnv);
        batchPriceRequest.setScene(RequestSceneEnum.PROMO_DETAIL_DESC_WithDealPromo.getScene());

        return batchPriceRequest;
    }

    private int getClientType(String channel) {
        if (StringUtils.isBlank(channel)) {
            return ClientTypeEnum.mt_mainApp_ios.getType();
        }
        if (channel.equals("bm-ios")) {
            return ClientTypeEnum.mt_mainApp_ios.getType();
        } else if (channel.equals("bm-android")) {
            return ClientTypeEnum.mt_mainApp_android.getType();
        } else if (channel.equals("bm-pc")) {
            return ClientTypeEnum.mt_web.getType();
        }
        return ClientTypeEnum.mt_mainApp_ios.getType();
    }
}
