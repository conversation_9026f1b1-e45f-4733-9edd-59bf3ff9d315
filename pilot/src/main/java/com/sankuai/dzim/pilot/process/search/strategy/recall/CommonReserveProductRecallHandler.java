package com.sankuai.dzim.pilot.process.search.strategy.recall;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.dzim.pilot.acl.ProductAclService;
import com.sankuai.dzim.pilot.process.search.data.ReserveProductM;
import com.sankuai.dzim.pilot.process.search.data.ReserveQueryContext;
import com.sankuai.dzim.pilot.process.search.enums.IndustryBackendCategoryEnum;
import com.sankuai.dzim.pilot.process.search.strategy.filter.ReserveProductFilterFactory;
import com.sankuai.dzim.pilot.process.search.strategy.filter.ReserveProductFilterHandler;
import com.sankuai.dzim.pilot.process.search.utils.BookDateProductQueryUtil;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.ShopDealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import com.sankuai.general.product.query.center.client.enums.FilterOptionEnum;
import com.sankuai.general.product.query.center.client.request.QueryDealGroupIdByShopRequest;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupIdListResult;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Component
public class CommonReserveProductRecallHandler implements ReserveProductRecallHandler {

    private static final List<Integer> COMMON_CATEGORY_IDS = Lists.newArrayList(
            IndustryBackendCategoryEnum.KTV.getCategoryIds(),
            IndustryBackendCategoryEnum.CHESS.getCategoryIds(),
            IndustryBackendCategoryEnum.BALL.getCategoryIds(),
            IndustryBackendCategoryEnum.VENUE.getCategoryIds(),
            IndustryBackendCategoryEnum.BACKROOM.getCategoryIds(),
            IndustryBackendCategoryEnum.ROLE_PLAY.getCategoryIds(),
            IndustryBackendCategoryEnum.BAR.getCategoryIds(),
            IndustryBackendCategoryEnum.PET.getCategoryIds()
    ).stream().flatMap(List::stream).collect(Collectors.toList());

    @Resource
    private ProductAclService productAclService;

    @Resource
    private ReserveProductFilterFactory reserveProductFilterFactory;

    @Override
    public List<ReserveProductM> recall(ReserveQueryContext queryContext) {
        return queryShopProductIds(queryContext);
    }

    private List<ReserveProductM> queryShopProductIds(ReserveQueryContext ctx) {
        if (ctx == null || ctx.getMtShopId() <= 0) {
            return Lists.newArrayList();
        }
        // 查询门店下全量在线预订商品id
        List<Long> onlineReserveProductIds = queryOnlineReserveProductIds(ctx);

        // 请求商品查询中心，获取基本商品信息
        List<DealGroupDTO> onlineDealGroups = queryReserveProductBaseInfo(ctx, onlineReserveProductIds);

        // 根据商品筛选项，进行商品id剪枝
        onlineDealGroups = filterReserveProductIds(ctx, onlineDealGroups);

        paddingFilterCtx(ctx, onlineDealGroups);

        return BookDateProductQueryUtil.buildProductGroupM(ctx, onlineDealGroups);
    }

    private void paddingFilterCtx(ReserveQueryContext ctx, List<DealGroupDTO> onlineDealGroups) {
        // 1. 将平台商品id和综业务商品id的映射关系，存储到上下文中
        storeBizProductId2PlatformProductId(ctx, onlineDealGroups);

        // 2. 填充本次想要查询的sku
        storeBizProductId2BizSkuId(ctx, onlineDealGroups);
    }

    private void storeBizProductId2BizSkuId(ReserveQueryContext ctx, List<DealGroupDTO> onlineDealGroups) {
        if (CollectionUtils.isEmpty(onlineDealGroups)) {
            return;
        }
        Map<Integer, List<Integer>> bizProductId2BizSkuIds = onlineDealGroups.stream().filter(dealGroupDTO -> dealGroupDTO != null && CollectionUtils.isNotEmpty(dealGroupDTO.getDeals()))
                .collect(Collectors.toMap(dealGroupDTO -> dealGroupDTO.getBizProductId().intValue(),
                        a -> a.getDeals().stream().map(DealGroupDealDTO::getBizDealId).filter(Objects::nonNull).map(Long::intValue)
                                .collect(Collectors.toList())));
        if (MapUtils.isEmpty(bizProductId2BizSkuIds)) {
            return;
        }
        Map<String, Object> customPaddingParam = ctx.getCustomPaddingParam();
        if (MapUtils.isEmpty(customPaddingParam)) {
            customPaddingParam = Maps.newHashMap();
        }
        customPaddingParam.put("productId2RecallSkuIdListMap", bizProductId2BizSkuIds);
        ctx.setCustomPaddingParam(customPaddingParam);
    }

    private List<DealGroupDTO> filterReserveProductIds(ReserveQueryContext ctx, List<DealGroupDTO> dealGroupDTOS) {
        List<ReserveProductFilterHandler> handler = reserveProductFilterFactory.getHandler(ctx);
        if (CollectionUtils.isEmpty(handler)) {
            return dealGroupDTOS;
        }
        for (ReserveProductFilterHandler reserveProductFilterHandler : handler) {
            dealGroupDTOS = reserveProductFilterHandler.filter(ctx, dealGroupDTOS);
        }
        return dealGroupDTOS;
    }

    private List<DealGroupDTO> queryReserveProductBaseInfo(ReserveQueryContext ctx, List<Long> onlineReserveProductIds) {
        List<DealGroupDTO> dealGroupDTOS = productAclService.queryReserveProductBaseInfo(onlineReserveProductIds, ctx.getPlatformCategoryId(), buildDealGroupAttr(ctx), buildDealAttr(ctx));
        if (CollectionUtils.isEmpty(dealGroupDTOS)) {
            return Lists.newArrayList();
        }
        return dealGroupDTOS;
    }

    private Set<String> buildDealGroupAttr(ReserveQueryContext ctx) {
        Map<String, Object> extra = ctx.getExtra();
        if (MapUtils.isEmpty(extra)) {
            return Sets.newHashSet();
        }
        return (Set<String>) extra.getOrDefault("dealGroupAttrs", Sets.newHashSet());
    }

    private Set<String> buildDealAttr(ReserveQueryContext ctx) {
        Map<String, Object> extra = ctx.getExtra();
        if (MapUtils.isEmpty(extra)) {
            return Sets.newHashSet();
        }
        return (Set<String>) extra.getOrDefault("dealAttrs", Sets.newHashSet());
    }

    private List<Long> queryOnlineReserveProductIds(ReserveQueryContext ctx) {
        // 如果入参已经带了商品id，优先去入参中的商品id
        if (CollectionUtils.isNotEmpty(ctx.getProductIds())) {
            return ctx.getProductIds();
        }

        QueryDealGroupIdByShopRequest request = buildQueryDealGroupIdByShopRequest(ctx);
        // 发起请求调用 拿到结果
        QueryDealGroupIdListResult queryDealGroupIdListResult = productAclService.queryProductByShopId(request);
        if (queryDealGroupIdListResult == null) {
            return Lists.newArrayList();
        }
        List<ShopDealGroupDTO> shopDealGroups = queryDealGroupIdListResult.getShopDealGroupDTOS();
        if (CollectionUtils.isEmpty(shopDealGroups)) {
            return Lists.newArrayList();
        }

        return getPlatformProductIds(shopDealGroups);
    }

    private void storeBizProductId2PlatformProductId(ReserveQueryContext ctx,
                                                     List<DealGroupDTO> dealGroupDTOS) {
        Map<Long, Integer> platformProductId2BizProductId = dealGroupDTOS.stream().collect(Collectors.toMap(DealGroupDTO::getMtDealGroupId, a -> a.getBizProductId().intValue()));
        if (MapUtils.isEmpty(platformProductId2BizProductId)) {
            return;
        }
        // 反转platformProductId2BizProductId，将key和value对调
        Map<Integer, Long> bizProductId2PlatformProductId = platformProductId2BizProductId.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey));

        ctx.setBizProductId2PlatformProductId(bizProductId2PlatformProductId);
    }

    private List<Long> getPlatformProductIds(List<ShopDealGroupDTO> shopDealGroups) {
        return shopDealGroups.stream().map(ShopDealGroupDTO::getMtDealGroupId).collect(Collectors.toList());
    }

    private QueryDealGroupIdByShopRequest buildQueryDealGroupIdByShopRequest(ReserveQueryContext ctx) {
        QueryDealGroupIdByShopRequest queryDealGroupIdByShopRequest = new QueryDealGroupIdByShopRequest();
        queryDealGroupIdByShopRequest.setMtShopIds(Sets.newHashSet(ctx.getMtShopId()));
        queryDealGroupIdByShopRequest.setPlatformCategories(Sets.newHashSet(ctx.getPlatformCategoryId()));
        queryDealGroupIdByShopRequest.setFilterOption(FilterOptionEnum.ONLINE_VALID.getCode());
        return queryDealGroupIdByShopRequest;
    }

    @Override
    public boolean match(ReserveQueryContext queryContext) {
        if (queryContext == null || queryContext.getShopBackCategoryId() <= 0) {
            return false;
        }
        return COMMON_CATEGORY_IDS.contains(queryContext.getShopBackCategoryId());
    }

}
