package com.sankuai.dzim.pilot.process.localplugin.reserve.beam;

import com.alibaba.fastjson.JSON;
import com.dianping.dzim.common.enums.ImUserType;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.dp.arts.common.util.CollectionUtils;
import com.meituan.nibtp.trade.client.combine.response.AgentCreateOrderResDTO;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.acl.HaimaAclService;
import com.sankuai.dzim.pilot.acl.ProductAclService;
import com.sankuai.dzim.pilot.acl.UserAclService;
import com.sankuai.dzim.pilot.api.enums.assistant.PlatformEnum;
import com.sankuai.dzim.pilot.chain.data.AIAnswerData;
import com.sankuai.dzim.pilot.domain.annotation.LocalPlugin;
import com.sankuai.dzim.pilot.gateway.api.assistant.data.SendBeamMessageReq;
import com.sankuai.dzim.pilot.process.aibook.AIBookCacheProcessService;
import com.sankuai.dzim.pilot.process.aiphonecall.data.AIPhoneCallTaskCreateRes;
import com.sankuai.dzim.pilot.process.aireservebook.AIReserveBookProcessService;
import com.sankuai.dzim.pilot.process.aireservebook.AIReserveBookQueryService;
import com.sankuai.dzim.pilot.process.aireservebook.data.*;
import com.sankuai.dzim.pilot.process.aireservebook.enums.POIIndustryType;
import com.sankuai.dzim.pilot.process.aireservebook.enums.ShopReserveWayType;
import com.sankuai.dzim.pilot.process.localplugin.reserve.param.BeamReserveConfirmParam;
import com.sankuai.dzim.pilot.scene.data.AssistantSceneContext;
import com.sankuai.dzim.pilot.utils.BeamReserveBookCardUtils;
import com.sankuai.dzim.pilot.utils.LionConfigUtil;
import com.sankuai.dzim.pilot.utils.PluginContextUtil;
import com.sankuai.dzim.pilot.utils.ReserveInfoProcessUtils;
import com.sankuai.dzim.pilot.utils.context.RequestContext;
import com.sankuai.dzim.pilot.utils.context.RequestContextConstants;
import com.sankuai.dzim.pilot.utils.data.Response;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.wpt.user.retrieve.thrift.message.UserModel;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * @author: wuwenqiang
 * @create: 2025-04-23
 * @description: 发起预约工具
 */
@Component
@Slf4j
public class BeamMakeReservePlugin {

    @Autowired
    private AIReserveBookProcessService aiReserveBookProcessService;

    @Autowired
    private AIReserveBookQueryService aiReserveBookQueryService;

    @Autowired
    private AIBookCacheProcessService aiBookCacheProcessService;

    @Autowired
    private HaimaAclService haimaAclService;

    @Autowired
    private LionConfigUtil lionConfigUtil;

    @Autowired
    private PluginContextUtil pluginContextUtil;

    @Autowired
    private BeamReserveInfoCollectPlugin beamReserveInfoCollectPlugin;

    @Autowired
    private ProductAclService productAclService;

    @Autowired
    private UserAclService userAclService;

    @ConfigValue(key = "com.sankuai.mim.pilot.beam.reserve.book.agent.feedback.config", defaultValue = "{}")
    private Map<String, String> feedBackConfig;

    private static ThreadPool threadPool = Rhino.newThreadPool("BeamMakeReserveTool",
            DefaultThreadPoolProperties.Setter().withCoreSize(50).withMaxSize(100).withMaxQueueSize(100));

    @LocalPlugin(name = "BeamMakeReserveTool", description = "如果用户的最新消息是和发起预约相关指令且预约信息已确认收集完整，由我来处理", returnDirect = false)
    public AIAnswerData makeReserve(BeamReserveConfirmParam param) {
        log.info("调用插件-MakeReserveTool，入参={}", param);
        try {
            pluginContextUtil.replaceShopIdWithBeamContext(param, lionConfigUtil.getTestShopIdReplaceConfig());
            // 1. 前置判断
            // 1.1 门店ID判断
            if (!validateBasicParams(param)) {
                return BeamReserveBookCardUtils.writeBeamTextAnswer(feedBackConfig.getOrDefault(ReserveAnswerConstants.POI_NOT_EXIST, ReserveAnswerConstants.FEEDBACK_ANSWER));
            }

            // 2. 获取上下文信息, 上游默认传入美团
            AssistantSceneContext assistantSceneContext = RequestContext.getAttribute(RequestContextConstants.ASSISTANT_CONTEXT);
            int platform = getPlatform(assistantSceneContext);

            // 3. 获取门店信息
            DpPoiDTO dpPoiDTO = getDpShopInfo(param, platform);
            if (dpPoiDTO == null) {
                return BeamReserveBookCardUtils.writeBeamTextAnswer(feedBackConfig.getOrDefault(ReserveAnswerConstants.POI_NOT_EXIST, ReserveAnswerConstants.FEEDBACK_ANSWER));
            }

            // 4. 获取行业类型
            // 4.1 门店类目信息获取(二级主后台类目)
            POIIndustryType poiIndustryType = getPoiIndustryType(dpPoiDTO);
            if (poiIndustryType == null) {
                // 非预设行业
                return BeamReserveBookCardUtils.writeBeamTextAnswer(feedBackConfig.getOrDefault(ReserveAnswerConstants.SHOP_NOT_SUPPORT, ReserveAnswerConstants.FEEDBACK_ANSWER));
            }

            // 5. 获取用户预约信息
            UserReserveInfo userReserveInfo = aiReserveBookQueryService.queryBeamUserReserveInfoWithOrderCheck(param, assistantSceneContext.getUserId(), param.getShopId(), platform);
            // 检查重复预约
            if (aiReserveBookProcessService.checkUserDuplicateReserve(userReserveInfo)) {
                if (aiReserveBookProcessService.checkTimeValid(userReserveInfo)) {
                    return BeamReserveBookCardUtils.writeBeamTextAnswer(feedBackConfig.getOrDefault(ReserveAnswerConstants.USER_DUPLICATE_RESERVE, ReserveAnswerConstants.FEEDBACK_ANSWER));
                }
                // 预约时间早于当前时间，则不认为是重复预约，清空记录
                userReserveInfo = null;
            }

            // 6. 发起异步调用，构建 FutureContext
            fillParam(param, userReserveInfo);
            FutureContext futureContext = buildFutureContext(assistantSceneContext, dpPoiDTO, poiIndustryType, param);

            // 7. 判断要素是否齐全
            List<StandardReserveItem> standardReserveItems = futureContext.getStandardReserveItemsFuture() == null ? null : futureContext.getStandardReserveItemsFuture().join();
            if (!validateReserveItemsCompleted(userReserveInfo, standardReserveItems)) {
                // 要素未收集齐全，应该调用要素收集工具
                return beamReserveInfoCollectPlugin.beamReserveElementCollect(param);
            }

            // 8. 获取异步执行结果，构建 ResultContext
            ResultContext resultContext = buildResultContext(futureContext, userReserveInfo, dpPoiDTO, poiIndustryType, standardReserveItems);
            log.info("[MakeReservePlugin] create reserve, shopReserveContext:{}, userReserveInfo:{}", resultContext.getShopReserveContext(), resultContext.getUserReserveInfo());
            // 9. 处理预约
            if (ShopReserveWayType.ONLINE.equals(resultContext.getShopReserveWayType())) {
                // 2.1 在线预约/预订，查询库存 + 发起预约/预订
                return processOnlineReserve(param, resultContext);
            }
            // 2.2 非在线预约/预订，直接发起AI外呼
            return processPhoneCallReserve(param, resultContext, platform);
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("MakeReserve").build(),
                    new WarnMessage("MakeReserveTool", "发起预约插件异常", ""), param, e);
            log.error("调用插件-MakeReserveTool，异常, param={}", param, e);
            return BeamReserveBookCardUtils.writeBeamTextAnswer(ReserveAnswerConstants.FEEDBACK_ANSWER);
        }
    }

    private FutureContext buildFutureContext(AssistantSceneContext assistantSceneContext, DpPoiDTO dpPoiDTO, 
            POIIndustryType poiIndustryType, BeamReserveConfirmParam param) {
        FutureContext context = new FutureContext();
        
        // 标准预约要素
        context.setStandardReserveItemsFuture(CompletableFuture.supplyAsync(
                () -> haimaAclService.queryBeamStandardReserveItems(poiIndustryType.getSecondBackCategoryId()),
                threadPool.getExecutor()));
        
        // 查询团单信息（若用户预约信息中含有团单ID）
        context.setDealGroupFuture(getDealGroupFuture(param));
        
        // 获取用户信息
        context.setUserModelFuture(CompletableFuture.supplyAsync(
                () -> userAclService.queryUserModel(assistantSceneContext.getUserId()), 
                threadPool.getExecutor()));
        
        // 查询门店预约方式
        context.setShopReserveWayTypeF(CompletableFuture.supplyAsync(
                () -> aiReserveBookQueryService.queryShopReserveWayType(dpPoiDTO.getMtPoiId()), 
                threadPool.getExecutor()));
        
        return context;
    }

    private ResultContext buildResultContext(FutureContext futureContext, UserReserveInfo userReserveInfo, 
            DpPoiDTO dpPoiDTO, POIIndustryType poiIndustryType, List<StandardReserveItem> standardReserveItems) {
        ResultContext context = new ResultContext();
        
        // 获取所有 future 的结果
        context.setStandardReserveItems(standardReserveItems);
        context.setDealGroupDTO(futureContext.getDealGroupFuture() == null ? null : futureContext.getDealGroupFuture().join());
        context.setUserModel(futureContext.getUserModelFuture() == null ? null : futureContext.getUserModelFuture().join());
        context.setShopReserveWayType(futureContext.getShopReserveWayTypeF().join());
        context.setUserReserveInfo(userReserveInfo);
        
        // 构建 ShopReserveContext
        ShopReserveContext shopReserveContext = ShopReserveContext.buildShopReserveContext(dpPoiDTO, poiIndustryType, context.getShopReserveWayType());
        context.setShopReserveContext(shopReserveContext);
        if (context.getShopReserveWayType() != null) {
            userReserveInfo.setShopReserveWayType(context.getShopReserveWayType().getType());
        }
        
        return context;
    }

    private void fillParam(BeamReserveConfirmParam param, UserReserveInfo userReserveInfo) {
        if (userReserveInfo == null || param == null) {
            return;
        }
        if (param.getProductId() == null || param.getProductId() <= 0) {
            param.setProductId(userReserveInfo.getDealId());
        }
    }

    private AIAnswerData processOnlineReserve(BeamReserveConfirmParam param, ResultContext resultContext) {
        // 1. 查询库存
        ValidateResult queryTimeStockValidateResult = queryTimeStock(resultContext.getShopReserveContext(), resultContext.getUserReserveInfo(), resultContext.getDealGroupDTO());
        if (!queryTimeStockValidateResult.isPass()) {
            return BeamReserveBookCardUtils.writeBeamTextAnswer(queryTimeStockValidateResult.getQuestion());
        }
        // 可用库存时间片
        List<TimeSliceStockInfo> availableTimeSliceStocks = queryTimeStockValidateResult.getExtraInfoItem("availableTimeSliceStocks");
        // 2. 发起在线预约
        SendBeamMessageReq sendBeamMessageReq = PluginContextUtil.getBeamRequestContext(param);
        AgentCreateOrderResDTO createOrderRes = aiReserveBookProcessService.createBeamReserve(sendBeamMessageReq, resultContext.getUserModel(),
                resultContext.getShopReserveContext(), resultContext.getUserReserveInfo(), null, true, availableTimeSliceStocks);
        // 检查预约结果
        AIAnswerData checkResult = checkReserveResult(createOrderRes);
        if (checkResult != null) {
            LogUtils.logFailLog(log, TagContext.builder().action("MakeReservePlugin").build(),
                    WarnMessage.build("MakeReservePlugin", "发起预约/预订失败", ""),
                    String.format("sendBeamMessageReq:%s, userReserveInfo:%s",
                            JSON.toJSONString(sendBeamMessageReq), JSON.toJSONString(resultContext.getUserReserveInfo())), JSON.toJSONString(createOrderRes));
            return checkResult;
        }
        
        // 3. 更新用户预约信息(缓存) + 发送卡片(预约中)
        return processReserveSuccess(createOrderRes, resultContext.getUserReserveInfo());
    }
    
    /**
     * 检查预约结果
     * @param createOrderRes 预约结果
     * @return 如果有错误则返回错误响应，否则返回null
     */
    private AIAnswerData checkReserveResult(AgentCreateOrderResDTO createOrderRes) {
        if (createOrderRes == null) {
            return BeamReserveBookCardUtils.writeBeamTextAnswer(feedBackConfig.getOrDefault(ReserveAnswerConstants.RESERVE_FAILED_DEFAULT, ReserveAnswerConstants.FEEDBACK_ANSWER));
        }
        if (createOrderRes.isSuccess() && createOrderRes.getMainOrderId() != null && createOrderRes.getMainOrderId() > 0) {
            return null;
        }
        if (StringUtils.isNotBlank(createOrderRes.getMessage())) {
            return BeamReserveBookCardUtils.writeBeamTextAnswer(createOrderRes.getMessage());
        }
        return BeamReserveBookCardUtils.writeBeamTextAnswer(feedBackConfig.getOrDefault(ReserveAnswerConstants.RESERVE_FAILED_DEFAULT, ReserveAnswerConstants.FEEDBACK_ANSWER));
    }
    
    /**
     * 处理预约成功后的逻辑
     * @param createOrderRes 预约结果
     * @param userReserveInfo 用户预约信息
     * @return 预约成功的响应
     */
    private AIAnswerData processReserveSuccess(AgentCreateOrderResDTO createOrderRes, UserReserveInfo userReserveInfo) {
        // 写入预约/预订ID
        userReserveInfo.setReserveId(createOrderRes.getMainOrderId());

        AssistantSceneContext assistantSceneContext = RequestContext.getAttribute(RequestContextConstants.ASSISTANT_CONTEXT);
        refreshUserReserveInfo(userReserveInfo, assistantSceneContext);

        // 返回beam格式卡片
        String bookSuccessText = feedBackConfig.getOrDefault(ReserveAnswerConstants.BEAM_BOOK_CARD_CREATE_SUCCESS, "预约创建成功");
        return BeamReserveBookCardUtils.writeBeamBookCreateCard(bookSuccessText, createOrderRes, userReserveInfo);
    }

    private AIAnswerData processPhoneCallReserve(BeamReserveConfirmParam param, ResultContext resultContext, int platform) {
        // 1.异步更新用户预约信息(前置设置商户预约类型)
        AssistantSceneContext assistantSceneContext = RequestContext.getAttribute(RequestContextConstants.ASSISTANT_CONTEXT);
        CompletableFuture<Void> updateUserReserveInfoF = CompletableFuture.runAsync(() -> refreshUserReserveInfo(resultContext.getUserReserveInfo(), assistantSceneContext), threadPool.getExecutor())
                .exceptionally(e -> {
                    log.error("[AIMakeReservePlugin] refreshUserReserveInfo error", e);
                    return null;
                });
        // 2. 发起AI外呼
        SendBeamMessageReq sendBeamMessageReq = PluginContextUtil.getBeamRequestContext(param);
        Response<AIPhoneCallTaskCreateRes> callAIPhoneCreate = aiReserveBookProcessService.createBeamAIPhoneCall(sendBeamMessageReq, resultContext.getUserModel(),
                resultContext.getShopReserveContext(), resultContext.getUserReserveInfo(), 0L, platform);
        if (callAIPhoneCreate == null || !callAIPhoneCreate.isSuccess()) {
            return BeamReserveBookCardUtils.writeBeamTextAnswer(feedBackConfig.getOrDefault(ReserveAnswerConstants.AI_PHONE_RESERVE_CALL_FAILED, ReserveAnswerConstants.FEEDBACK_ANSWER));
        }
        // 3. 等待缓存写入成功
        updateUserReserveInfoF.join();
        String aiCallSuccessText = feedBackConfig.getOrDefault(ReserveAnswerConstants.BEAM_AI_CALL_CARD_CREATE_SUCCESS, "AI外呼创建成功");
        return BeamReserveBookCardUtils.writeBeamAICallCreateCard(aiCallSuccessText, callAIPhoneCreate.getData(), resultContext.getUserReserveInfo(), resultContext.getShopReserveContext());
    }

    private ValidateResult queryTimeStock(ShopReserveContext shopReserveContext, UserReserveInfo userReserveInfo, DealGroupDTO dealGroupDTO) {
        boolean isAfterOrder = userReserveInfo.getOrderId() != null && userReserveInfo.getOrderId() > 0;
        List<TimeSliceStockInfo> timeSliceStockInfos = aiReserveBookQueryService.beamQueryAvailableTimeStock(shopReserveContext, userReserveInfo.getCollectedReserveInfo(), isAfterOrder, dealGroupDTO);
        List<TimeSliceStockInfo> availableTimeSliceStocks = ReserveInfoProcessUtils.checkStockExist(shopReserveContext.getPoiIndustryType(), userReserveInfo.getCollectedReserveInfo(), timeSliceStockInfos);
        if (CollectionUtils.isEmpty(availableTimeSliceStocks)) {
            return ValidateResult.fail(feedBackConfig.getOrDefault(ReserveAnswerConstants.STOCK_NOT_ENOUGH, ReserveAnswerConstants.FEEDBACK_ANSWER));
        }
        return ValidateResult.success("availableTimeSliceStocks", availableTimeSliceStocks);
    }

    private void refreshUserReserveInfo(UserReserveInfo userReserveInfo, AssistantSceneContext assistantSceneContext) {
        String imUserId = userReserveInfo.getImUserId();
        int platform = getPlatform(assistantSceneContext);
        Long shopId = userReserveInfo.getShopId();
        aiBookCacheProcessService.setBeamAgentReserveAndBookInfo(imUserId, shopId, platform, userReserveInfo);
    }

    private int getPlatform(AssistantSceneContext context) {
        return context.getUserId().startsWith(ImUserType.MT.getPrefix()) ? PlatformEnum.MT.getType() : PlatformEnum.DP.getType();
    }

    private boolean validateBasicParams(BeamReserveConfirmParam param) {
        return param != null && param.getShopId() != null && param.getShopId() > 0;
    }

    private DpPoiDTO getDpShopInfo(BeamReserveConfirmParam param, int platform) {
        return aiReserveBookQueryService.queryDpPoiDTO(param.getShopId(), platform);
    }

    private POIIndustryType getPoiIndustryType(DpPoiDTO dpPoiDTO) {
        Integer secondBackCategoryId = aiReserveBookProcessService.extractMainSecondBackCategoryId(dpPoiDTO);
        return POIIndustryType.getBySecondBackCategoryId(secondBackCategoryId);
    }

    private CompletableFuture<DealGroupDTO> getDealGroupFuture(BeamReserveConfirmParam param) {
        if (param.getProductId() != null && param.getProductId() > 0) {
            return CompletableFuture.supplyAsync(() -> productAclService.getDealBaseInfo(param.getProductId(), true), threadPool.getExecutor());
        }
        return null;
    }

    private boolean validateReserveItemsCompleted(UserReserveInfo userReserveInfo, List<StandardReserveItem> standardReserveItems) {
        if (userReserveInfo == null || userReserveInfo.getCollectedReserveInfo() == null) {
            return false;
        }
        List<StandardReserveItem> lackReserveItems = ReserveInfoProcessUtils.checkLackReserveItems(userReserveInfo.getCollectedReserveInfo(), standardReserveItems);
        return CollectionUtils.isEmpty(lackReserveItems);
    }

    @Data
    private static class FutureContext {
        private CompletableFuture<List<StandardReserveItem>> standardReserveItemsFuture;
        private CompletableFuture<DealGroupDTO> dealGroupFuture;
        private CompletableFuture<UserModel> userModelFuture;
        private CompletableFuture<ShopReserveWayType> shopReserveWayTypeF;
    }

    @Data
    private static class ResultContext {
        private List<StandardReserveItem> standardReserveItems;
        private DealGroupDTO dealGroupDTO;
        private UserModel userModel;
        private ShopReserveWayType shopReserveWayType;
        private UserReserveInfo userReserveInfo;
        private ShopReserveContext shopReserveContext;
    }
}
