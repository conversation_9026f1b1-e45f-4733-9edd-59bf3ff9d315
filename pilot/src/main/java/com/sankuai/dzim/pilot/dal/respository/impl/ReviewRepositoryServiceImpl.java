package com.sankuai.dzim.pilot.dal.respository.impl;

import com.google.common.collect.Lists;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.acl.FridayAclService;
import com.sankuai.dzim.pilot.acl.VexAclService;
import com.sankuai.dzim.pilot.acl.data.VexSearchData;
import com.sankuai.dzim.pilot.acl.data.fraiday.FridayConstant;
import com.sankuai.dzim.pilot.api.enums.MerchantKnowledgeTypeEnum;
import com.sankuai.dzim.pilot.dal.pilotdao.ReviewSyncDAO;
import com.sankuai.dzim.pilot.dal.entity.pilot.ReviewSyncEntity;
import com.sankuai.dzim.pilot.dal.respository.ReviewRepositoryService;
import com.sankuai.dzim.pilot.utils.LionConfigUtil;
import com.sankuai.vex.plus.Operation;
import com.sankuai.vex.plus.VexAddReq;
import com.sankuai.vex.plus.VexDeleteReq;
import com.sankuai.vex.plus.VexSearchReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Repository
@Slf4j
public class ReviewRepositoryServiceImpl implements ReviewRepositoryService {

    /**
     * 对应向量库的表
     */
    private static final String TABLE_NAME = "IM_MerchantKnowledge";

    @Autowired
    private VexAclService vexAclService;

    @Autowired
    private ReviewSyncDAO reviewSyncDAO;

    @Autowired
    private FridayAclService fridayAclService;

    @Autowired
    private LionConfigUtil lionConfigUtil;

    @Override
    public long addReview(ReviewSyncEntity entity) {
        Assert.isTrue(entity != null, "评论不能为空");

        LogUtils.logRequestLog(log, TagContext.builder().action("addReview").build(), "ReviewRepositoryService", entity);

        ReviewSyncEntity existEntity = reviewSyncDAO.findByReviewId(entity.getReviewId());
        if (existEntity != null) {
            return 0;
        }
        // 1.将数据向量化
        String reviewContent = StringUtils.isNotBlank(entity.getTitle()) ? (entity.getTitle() + "\n" + entity.getContent()) : entity.getContent();
        List<Double> vector = fridayAclService.embedding(lionConfigUtil.getKnowledgeEmbeddingAppId(), FridayConstant.TEXT_EMBEDDING_ADA_002, FridayConstant.TOKEN_LIMIT_1000, reviewContent);
        if (CollectionUtils.isEmpty(vector)) {
            LogUtils.logReturnInfo(log, TagContext.builder().action("addReview:embedding").bizId(String.valueOf(entity.getReviewId())).build()
                    , new WarnMessage("ReviewRepositoryService", "评价向量化失败", ""), entity, null);
            return 0;
        }

        // 2.存入向量数据库
        long globalId = vexAclService.addVex(buildVexAddReq(entity, vector));
        if (globalId <= 0) {
            LogUtils.logReturnInfo(log, TagContext.builder().action("addReview:addVex").bizId(String.valueOf(entity.getReviewId())).build()
                    , new WarnMessage("ReviewRepositoryService", "评价写入向量数据库失败", ""), entity, null);
            return 0;
        }

        // 3.存入DB(不保证与向量库的强一致性)
        entity.setVectorId(globalId);
        int row = reviewSyncDAO.insert(entity);
        if (row < 0) {
            LogUtils.logReturnInfo(log, TagContext.builder().action("addReview:addDB").bizId(String.valueOf(entity.getReviewId())).build()
                    , new WarnMessage("ReviewRepositoryService", "评价写入DB失败", ""), entity, null);
            return 0;
        }
        return entity.getId();
    }

    private VexAddReq buildVexAddReq(ReviewSyncEntity entity, List<Double> vector) {
        VexAddReq vexAddReq = new VexAddReq();
        vexAddReq.setSetName(lionConfigUtil.getVexSetName());
        vexAddReq.setTableName(TABLE_NAME);
        vexAddReq.setFeature(vector);
        vexAddReq.setCusLongFst(MerchantKnowledgeTypeEnum.REVIEW_V2.getType());
        vexAddReq.setCusLongSec(entity.getMtShopId());
        if (entity.getDealId() > 0) {
            vexAddReq.setCusStrSmall(String.valueOf(entity.getDealId()));
        }
        if (entity.getTechnicianId() > 0) {
            vexAddReq.setCusStrLarge(String.valueOf(entity.getTechnicianId()));
        }
        if (entity.getMtCityId() > 0 && entity.getCat1Id() > 0) {
            vexAddReq.setCusStrMid(entity.getMtCityId() + "_" + entity.getCat1Id());
        }

        return vexAddReq;
    }

    @Override
    public int deleteReview(Long reviewId) {
        Assert.isTrue(reviewId > 0, "评论Id不合法");

        LogUtils.logRequestLog(log, TagContext.builder().action("deleteReview").build(), "ReviewRepositoryService", reviewId);

        // 1.查知识
        ReviewSyncEntity entity = reviewSyncDAO.findByReviewId(reviewId);
        if (entity == null) {
            return 0;
        }

        // 2.删向量库
        List<Long> globalIds = vexAclService.deleteVex(buildVexDelReq(Lists.newArrayList(entity.getVectorId())));
        if (CollectionUtils.isEmpty(globalIds)) {
            LogUtils.logReturnInfo(log, TagContext.builder().action("deleteReview:delReview").bizId(reviewId.toString()).build()
                    , new WarnMessage("ReviewRepositoryService", "从向量数据删除数据失败", ""), reviewId, globalIds);
            return 0;
        }

        // 2.删DB
        int row = reviewSyncDAO.delete(entity.getId());
        if (row != globalIds.size()) {
            LogUtils.logReturnInfo(log, TagContext.builder().action("deleteReview:DelReviewInconformity").bizId(reviewId.toString()).build()
                    , new WarnMessage("ReviewRepositoryService", "从数据库中删除的数据与向量数据库不一致", ""), reviewId, globalIds);
        }
        return row;
    }

    private VexDeleteReq buildVexDelReq(List<Long> vectorIds) {
        VexDeleteReq vexDeleteReq = new VexDeleteReq();
        vexDeleteReq.setSetName(lionConfigUtil.getVexSetName());
        vexDeleteReq.setTableName(TABLE_NAME);
        vexDeleteReq.setGlobalIds(vectorIds);
        return vexDeleteReq;
    }

    @Override
    public List<ReviewSyncEntity> searchReview(String query, Map<String, String> scalarFilter, int topK) {
        Assert.isTrue(StringUtils.isNotBlank(query), "搜索关键词字为空");
        Assert.isTrue(topK > 0, "topK必须大于0");

        // 0.query向量化
        List<Double> vector = fridayAclService.embedding(lionConfigUtil.getKnowledgeEmbeddingAppId(), FridayConstant.TEXT_EMBEDDING_ADA_002, FridayConstant.TOKEN_LIMIT_1000, query);

        return searchByVector(query, scalarFilter, topK, vector);
    }

    @Override
    public List<ReviewSyncEntity> searchReviewV2(String query, Map<String, String> scalarFilter, int topK) {
        Assert.isTrue(StringUtils.isNotBlank(query), "搜索关键词字为空");
        Assert.isTrue(topK > 0, "topK必须大于0");

        // 0.query向量化
        List<Double> vector = fridayAclService.embedding(lionConfigUtil.getKnowledgeEmbeddingAppId(), FridayConstant.TEXT_EMBEDDING_ADA_002, FridayConstant.TOKEN_LIMIT_1000, query);

        return searchByVectorV2(query, scalarFilter, topK, vector);
    }

    private List<ReviewSyncEntity> searchByVector(String query, Map<String, String> scalarFilter, int topK, List<Double> vector) {
        return searchByVectorInternal(query, scalarFilter, topK, buildVexSearchReq(scalarFilter, topK, vector));
    }

    private List<ReviewSyncEntity> searchByVectorV2(String query, Map<String, String> scalarFilter, int topK, List<Double> vector) {
        return searchByVectorInternal(query, scalarFilter, topK, buildVexSearchReqV2(scalarFilter, topK, vector));
    }

    private List<ReviewSyncEntity> searchByVectorInternal(String query, Map<String, String> scalarFilter, int topK, VexSearchReq vexSearchReq) {
        // 1.向量搜索
        List<VexSearchData> vexSearchDatas = vexAclService.searchVex(vexSearchReq);
        if (CollectionUtils.isEmpty(vexSearchDatas)) {
            LogUtils.logReturnInfo(log, TagContext.builder().action("searchReview").build()
                    , new WarnMessage("ReviewRepositoryService", "用户query无对应的评论", ""), new Object[]{query, scalarFilter, topK}, null);
            return Lists.newArrayList();
        }

        // 2.获取原始文本
        List<Long> globalIds = vexSearchDatas.stream().map(VexSearchData::getGlobalId).collect(Collectors.toList());
        List<ReviewSyncEntity> reviewSyncEntities = reviewSyncDAO.findByVectorId(globalIds);

        if (CollectionUtils.isEmpty(reviewSyncEntities)) {
            LogUtils.logReturnInfo(log, TagContext.builder().action("searchReview:dbEmpty").bizId(globalIds.toString()).build()
                    , new WarnMessage("ReviewRepositoryService", "用户query无对应的知识(DB)", ""), new Object[]{query, scalarFilter, topK, globalIds}, null);
            return Lists.newArrayList();
        }

        fillSimilarity(reviewSyncEntities, vexSearchDatas);

        LogUtils.logReturnInfo(log, TagContext.builder().action("searchReview").build()
                , new WarnMessage("ReviewRepositoryService", "用户query检索知识成功", ""), new Object[]{query, scalarFilter, topK, globalIds}, reviewSyncEntities);

        return reviewSyncEntities.stream().sorted(Comparator.comparing(ReviewSyncEntity::getSimilarity).reversed()).collect(Collectors.toList());
    }


    private void fillSimilarity(List<ReviewSyncEntity> reviewSyncEntities, List<VexSearchData> vexSearchDatas) {
        Map<Long, Double> globalId2SimilarityMap = vexSearchDatas.stream().collect(Collectors.toMap(VexSearchData::getGlobalId, VexSearchData::getSimilarity));
        for (ReviewSyncEntity entity : reviewSyncEntities) {
            if (!globalId2SimilarityMap.containsKey(entity.getVectorId())) {
                continue;
            }
            entity.setSimilarity(globalId2SimilarityMap.get(entity.getVectorId()));
        }
    }

    private VexSearchReq buildVexSearchReq(Map<String, String> scalarFilter, int topK, List<Double> vector) {
        VexSearchReq vexSearchReq = new VexSearchReq();
        vexSearchReq.setSetName(lionConfigUtil.getVexSetName());
        vexSearchReq.setTableName(TABLE_NAME);
        vexSearchReq.setTopK(topK);
        vexSearchReq.setFeature(vector);
        vexSearchReq.setSearchScope(buildSearchScope(scalarFilter));
        return vexSearchReq;
    }

    private VexSearchReq buildVexSearchReqV2(Map<String, String> scalarFilter, int topK, List<Double> vector) {
        VexSearchReq vexSearchReq = new VexSearchReq();
        vexSearchReq.setSetName(lionConfigUtil.getVexSetName());
        vexSearchReq.setTableName(TABLE_NAME);
        vexSearchReq.setTopK(topK);
        vexSearchReq.setFeature(vector);
        vexSearchReq.setSearchScope(buildSearchScopeV2(scalarFilter));
        return vexSearchReq;
    }


    private Map<String, Operation> buildSearchScope(Map<String, String> scalarFilter) {
        Map<String, Operation> searchScope = new HashMap<>();
        Operation mainOperation = new Operation();
        mainOperation.setFieldName("cusLongFst");
        mainOperation.setOp("equal");
        mainOperation.setCompVal(String.valueOf(MerchantKnowledgeTypeEnum.REVIEW.getType()));
        searchScope.put("cusLongFst", mainOperation);
        if (MapUtils.isEmpty(scalarFilter)) {
            return searchScope;
        }
        if (NumberUtils.toLong(scalarFilter.get("mtShopId")) > 0L) {
            Operation operation = new Operation();
            operation.setFieldName("cusLongSec");
            operation.setOp("equal");
            operation.setCompVal(scalarFilter.get("mtShopId"));
            searchScope.put("cusLongSec", operation);
        }
        if (NumberUtils.toLong(scalarFilter.get("dealId")) > 0L) {
            Operation operation = new Operation();
            operation.setFieldName("cusStrSmall");
            operation.setOp("equal");
            operation.setCompVal(scalarFilter.get("dealId"));
            searchScope.put("cusStrSmall", operation);
        }
        if (NumberUtils.toLong(scalarFilter.get("technicianId")) > 0L) {
            Operation operation = new Operation();
            operation.setFieldName("cusStrLarge");
            operation.setOp("equal");
            operation.setCompVal(scalarFilter.get("technicianId"));
            searchScope.put("cusStrLarge", operation);
        }
        return searchScope;
    }

    private Map<String, Operation> buildSearchScopeV2(Map<String, String> scalarFilter) {
        Map<String, Operation> searchScope = new HashMap<>();
        Operation mainOperation = new Operation();
        mainOperation.setFieldName("cusLongFst");
        mainOperation.setOp("equal");
        mainOperation.setCompVal(String.valueOf(MerchantKnowledgeTypeEnum.REVIEW_V2.getType()));
        searchScope.put("cusLongFst", mainOperation);
        if (MapUtils.isEmpty(scalarFilter)) {
            return searchScope;
        }
        paddingSearchScope(searchScope, scalarFilter);
        return searchScope;
    }

    private void paddingSearchScope(Map<String, Operation> searchScope, Map<String, String> scalarFilter) {

        if (NumberUtils.toLong(scalarFilter.get("mtShopId")) > 0L) {
            Operation operation = new Operation();
            operation.setFieldName("cusLongSec");
            operation.setOp("equal");
            operation.setCompVal(scalarFilter.get("mtShopId"));
            searchScope.put("cusLongSec", operation);
        }
        if (NumberUtils.toLong(scalarFilter.get("dealId")) > 0L) {
            Operation operation = new Operation();
            operation.setFieldName("cusStrSmall");
            operation.setOp("equal");
            operation.setCompVal(scalarFilter.get("dealId"));
            searchScope.put("cusStrSmall", operation);
        }
        if (NumberUtils.toLong(scalarFilter.get("technicianId")) > 0L) {
            Operation operation = new Operation();
            operation.setFieldName("cusStrLarge");
            operation.setOp("equal");
            operation.setCompVal(scalarFilter.get("technicianId"));
            searchScope.put("cusStrLarge", operation);
        }

        if (NumberUtils.toInt(scalarFilter.get("mtCityId")) > 0 && NumberUtils.toInt(scalarFilter.get("mtCateId1")) > 0) {
            Operation operation = new Operation();
            operation.setFieldName("cusStrMid");
            operation.setOp("equal");
            operation.setCompVal(scalarFilter.get("mtCityId") + "_" + scalarFilter.get("mtCateId1"));
            searchScope.put("cusStrMid", operation);
        }
    }
}
