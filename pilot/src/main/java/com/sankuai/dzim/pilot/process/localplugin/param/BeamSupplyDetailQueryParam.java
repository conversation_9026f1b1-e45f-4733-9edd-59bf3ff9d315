package com.sankuai.dzim.pilot.process.localplugin.param;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025/04/17 11:19
 */
@Data
public class BeamSupplyDetailQueryParam extends Param {

    @JsonProperty(required = false)
    @JsonPropertyDescription("用户咨询的商品id,请从聊天记录中获取,不要捏造,没有就不需要传")
    private long productId;
    @JsonProperty(required = false)
    @JsonPropertyDescription("语义条件，用户想要要咨询的语义信息，e.g. 推销办卡、停车、技术水平等")
    private String semanticCondition;
}
