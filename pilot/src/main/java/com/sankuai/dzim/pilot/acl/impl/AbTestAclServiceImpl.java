package com.sankuai.dzim.pilot.acl.impl;

import com.sankuai.douhu.absdk.bean.DouHuRequest;
import com.sankuai.douhu.absdk.bean.DouHuResponse;
import com.sankuai.douhu.absdk.client.DouHuClient;
import com.sankuai.douhu.absdk.enums.ErrorCode;
import com.sankuai.douhu.absdk.util.DouHuUtil;
import com.sankuai.dzim.message.common.logger.LogUtils;
import com.sankuai.dzim.message.common.logger.TagContext;
import com.sankuai.dzim.message.common.logger.WarnMessage;
import com.sankuai.dzim.pilot.acl.AbTestAclService;
import com.sankuai.dzim.pilot.acl.data.AbTestData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2024/08/22 11:50
 */
@Slf4j
@Service
public class AbTestAclServiceImpl implements AbTestAclService {

    @Resource(name = "douHuClient")
    private DouHuClient douHuClient;

    @Override
    public AbTestData tryAb(DouHuRequest douHuRequest) {
        Assert.isTrue(douHuRequest != null && StringUtils.isNotBlank(douHuRequest.getExpId()), "策略不存在");
        try {
            DouHuResponse response = douHuClient.tryAb(douHuRequest);
            LogUtils.logReturnInfo(log, TagContext.builder().action("tryAb").build(),
                    WarnMessage.build("AbTestAclServiceImpl", "获取Ab实验结果成功", ""), douHuRequest, response);

            String moduleAbInfo4Front = DouHuUtil.extractExpABInfo4Front(response);
            String strategyKey = null;
            if (ErrorCode.SUCCESS.getCode().equalsIgnoreCase(response.getCode()) && !StringUtils.isEmpty(response.getSk())) {
                strategyKey = response.getSk();
            }
            return buildAbTestData(douHuRequest, moduleAbInfo4Front, strategyKey);
        } catch (Exception e) {
            LogUtils.logFailLog(log, TagContext.builder().action("tryAb").build(),
                    WarnMessage.build("AbTestAclServiceImpl", "获取Ab实验结果异常", ""), douHuRequest, e);
        }
        return null;
    }

    @NotNull
    private AbTestData buildAbTestData(DouHuRequest douHuRequest, String moduleAbInfo4Front, String strategyKey) {
        AbTestData abTestData = new AbTestData();
        abTestData.setExpId(douHuRequest.getExpId());
        abTestData.setStrategyKey(strategyKey);
        abTestData.setAbConfig(moduleAbInfo4Front);
        return abTestData;
    }
}

