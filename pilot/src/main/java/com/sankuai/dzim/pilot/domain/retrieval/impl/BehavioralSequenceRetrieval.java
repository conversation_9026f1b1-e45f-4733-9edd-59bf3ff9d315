package com.sankuai.dzim.pilot.domain.retrieval.impl;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.dzim.pilot.domain.annotation.Retrieval;
import com.sankuai.dzim.pilot.domain.retrieval.RetrievalAugmentor;
import com.sankuai.dzim.pilot.domain.retrieval.data.Knowledge;
import com.sankuai.dzim.pilot.domain.retrieval.data.RetrievalContext;
import com.sankuai.dzim.pilot.domain.retrieval.data.RetrievalResult;
import com.sankuai.dzim.pilot.process.AssistantRetrievalProcessService;
import com.sankuai.dzim.pilot.process.TransRetrievalDataToMarkdownService;
import com.sankuai.dzim.pilot.process.data.ShopRetrievalData;
import com.sankuai.dzim.pilot.scene.task.data.EnvContext;
import com.sankuai.dzim.pilot.utils.IMConstants;
import com.sankuai.dzim.pilot.utils.PluginContextUtil;
import com.sankuai.dzim.pilot.utils.PoiExtractUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/04/16 15:42
 */
@Component
@Slf4j
@Retrieval(name = "BehavioralSequenceRetrieval", desc = "用户行为序列检索器")
public class BehavioralSequenceRetrieval implements RetrievalAugmentor {

    @Autowired
    private TransRetrievalDataToMarkdownService transRetrievalDataToMarkdownService;

    @Autowired
    private AssistantRetrievalProcessService assistantRetrievalProcessService;

    @Autowired
    private PoiExtractUtil poiExtractUtil;

    @Override
    public String retrieval(RetrievalContext context) {
        return "";
    }

    @Override
    public RetrievalResult retrieveKnowledge(RetrievalContext context) {
        RetrievalResult retrievalResult = new RetrievalResult();
        retrievalResult.setKnowledgePrompt("以下是以下是用户最近行为数据:\n**用户行为你仅能用于参考用户的意图和偏好,禁止作为参数传递给任何工具**\n");
        Knowledge knowledge = new Knowledge();
        knowledge.setKnowledge(StringUtils.EMPTY);
        retrievalResult.setKnowledges(Lists.newArrayList(knowledge));
        try {
            String imUserId = context.getExtraInfoValue(IMConstants.IM_USER_ID_KEY);
            EnvContext envContext = PluginContextUtil.getEnvContextFromRequestContext();
            if (envContext == null) {
                envContext = EnvContext.blankEnvContext();
            }
            StringBuilder res = new StringBuilder();
            Pair<List<ShopRetrievalData>, List<ShopRetrievalData>> actionPair = assistantRetrievalProcessService.queryUserOperationShopAndDealGroupDTO(imUserId,10, envContext.getLng(), envContext.getLat());
            String shopActionMarkdown = transRetrievalDataToMarkdownService.transShopOpData2Markdown(actionPair.getLeft());
            String dealActionMarkdown = transRetrievalDataToMarkdownService.transDealOpData2Markdown(actionPair.getRight());
            res.append(shopActionMarkdown).append(dealActionMarkdown);
            knowledge.setKnowledge("以下行为仅供用于参考用户的行为\n" + res.toString());
        } catch (Exception e) {
            log.error("BehavioralSequenceRetrieval error, context={}", JsonCodec.encodeWithUTF8(context), e);
        }
        return retrievalResult;
    }
}
