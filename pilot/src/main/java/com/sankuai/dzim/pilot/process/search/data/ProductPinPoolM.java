package com.sankuai.dzim.pilot.process.search.data;

import lombok.Data;

import java.util.List;

@Data
public class ProductPinPoolM {

    /**
     * 拼场ID
     */

    private long poolId;
    /**
     * SKU ID
     */
    private int productItemId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 拼场时段
     */
    private String period;

    /**
     * 拼场场次描述
     */
    private String description;

    /**
     * 拼场最低人数
     */
    private int minNum;

    /**
     * 拼场最高人数
     */
    private int maxNum;

    /**
     * 当前场次人数
     */
    private int currentNum;

    /**
     * 当前拼场状态 1:初始状态, 2:拼场中, 3:拼场成功, 4:人数不足拼场失败, 5:其他人包场拼场失败, 6:商品变动拼场失败
     */
    private int poolStatus;

    /**
     * 拼场类型，仅包场，仅拼场，可拼可包锁场，可拼可包不锁场等，具体见dztrade-common的com.dianping.dztrade.enums.PoolTypeEnum类
     */
    private int poolType;

    /**
     * 当前用户是否在场次中（区分主客态）
     */
    private Boolean inPool;

    /**
     * 当前拼场是否还可以加入
     */
    private boolean canJoin;

    /**
     * 价格信息：实际售价
     */
    private String price;

    /**
     * 跳转链接
     */
    private String jumpUrl;

    /**
     * 场次开始时间
     */
    private long bookStartTime;

    /**
     * 拼场分组信息
     */
    private List<ProductPinPoolGroupM> poolGroups;

    /**
     * 拼成预估时间
     */
    private String hopeSuccessTimeStr;

    /**
     * 拼成概率
     */
    private String successProbability;

    /**
     * 拼场人员属性标签，例如：半数以上玩过剧本杀
     */
    private List<String> userTags;

    /**
     * 秘钥信息
     */
    private ProductPinPoolCipherM pinPoolCipher;
}
