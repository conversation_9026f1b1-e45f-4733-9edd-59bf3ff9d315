package com.sankuai.dzim.pilot.process.localplugin;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.dzim.pilot.acl.ShopAclService;
import com.sankuai.dzim.pilot.buffer.core.PilotBufferItemDO;
import com.sankuai.dzim.pilot.buffer.enums.BufferItemTypeEnum;
import com.sankuai.dzim.pilot.buffer.utils.BufferUtils;
import com.sankuai.dzim.pilot.chain.data.AIAnswerData;
import com.sankuai.dzim.pilot.chain.data.AIServiceContext;
import com.sankuai.dzim.pilot.chain.impl.AdvancedRagAIService;
import com.sankuai.dzim.pilot.dal.entity.pilot.ReviewSyncEntity;
import com.sankuai.dzim.pilot.dal.respository.ReviewRepositoryService;
import com.sankuai.dzim.pilot.domain.annotation.LocalPlugin;
import com.sankuai.dzim.pilot.process.EsSearchProcessService;
import com.sankuai.dzim.pilot.process.data.AIServiceConfig;
import com.sankuai.dzim.pilot.process.localplugin.param.ShopReviewRecallParam;
import com.sankuai.dzim.pilot.process.localplugin.param.ShopSearchRecommendParam;
import com.sankuai.dzim.pilot.utils.IMConstants;
import com.sankuai.dzim.pilot.utils.LLMApplyUtil;
import com.sankuai.sinai.data.api.dto.MtPoiDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/03/12 21:30
 */
@Slf4j
@Component
public class ShopReviewRecallPlugin {

    @Autowired
    private ShopAclService shopAclService;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    static class RecallParam {

        private Long mtShopId;
        private List<String> reviews;
        private String semanticConditions;
    }

    @Data
    static class RecallResult {

        private Long mtShopId;
        private boolean isMatch;
        private String reason;
    }

    // mock配置
    private static final String IS_MOCK_KEY = "isMock";
    private static final String MOCK_DATA_KEY = "mockData";

    // 每次LLM处理的门店数量
    private static final int BATCH_SIZE = 10;
    // 每个门店召回的评价数量
    private static final int REVIEW_SIZE = 10;

    // 状态文本对应的key
    private static final String STATUS_KEY = "status";

    // 控制查询vex/es对应的key
    private static final String QUERY_VEX_KEY = "queryVex";

    private static final String TOOL_END_FEEDBACK = "\n\n已经为您查看%s等%s个门店共计%s条评价，正在为您查找匹配的门店...\n\n";

    // 处理门店评价召回任务的线程池
    private static ThreadPool threadPool = Rhino.newThreadPool("ShopReviewRecallToolPool",
                                                               DefaultThreadPoolProperties.Setter().withCoreSize(50).withMaxSize(300).withMaxQueueSize(100));

    @ConfigValue(key = "com.sankuai.mim.pilot.plugin.shop.reviewRecall.config", defaultValue = "{}")
    private Map<String, Object> shopReviewRecallPluginConfig;

    @Autowired
    private AdvancedRagAIService advancedRagAIService;

    @Autowired
    private ShopSearchRecommendPlugin shopSearchRecommendPlugin;

    @Autowired
    private ReviewRepositoryService reviewRepositoryService;

    @Autowired
    private EsSearchProcessService esSearchProcessService;

    @LocalPlugin(name = "ShopReviewRecallTool", description = "Filter the given list of shop according to the user's semantic criteria, and return only those shops that meet the semantic requirements in the results. The maximum size of shopIds is 10, you can invoke the tool multiple times.", returnDirect = false)
    public AIAnswerData recallShopByReview(ShopReviewRecallParam shopReviewRecallParam) {
        try {
            log.info("ShopReviewRecallPlugin shopReviewRecallParam: {}", JsonCodec.encodeWithUTF8(shopReviewRecallParam));
            List<Long> mtShopIds = shopReviewRecallParam.getMtShopIds();
            if (CollectionUtils.isEmpty(mtShopIds)) {
                ShopSearchRecommendParam shopSearchRecommendParam = new ShopSearchRecommendParam();

                shopSearchRecommendParam.setProductInfo(shopReviewRecallParam.getAiServiceContext().getMessageDTO().getMessage());
                AIAnswerData aiAnswerData = shopSearchRecommendPlugin.searchRecommendShop(new ShopSearchRecommendParam());
                List<Map<String, String>> result = JSONObject.parseObject(aiAnswerData.getAnswer(), new TypeReference<List<Map<String, String>>>() {
                });
                mtShopIds = result.stream()
                                  .filter(Objects::nonNull)
                                  .map(map -> map.get(ShopSearchRecommendPlugin.MT_SHOP_ID_KEY))
                                  .map(Long::parseLong)
                                  .distinct()
                                  .collect(Collectors.toList());
                shopReviewRecallParam.setMtShopIds(mtShopIds);
            }
            BufferUtils.writeStatusBuffer(PilotBufferItemDO.builder().data(String.valueOf(shopReviewRecallPluginConfig.getOrDefault(STATUS_KEY, "正在为您查询评价信息....."))).build());
//            List<RecallResult> recallResults = Collections.synchronizedList(Lists.newArrayList());
            Map<Long, List<String>> shopId2reviews = Maps.newConcurrentMap();
            // 1.并行处理每个门店的评论召回任务，并返回一个CompletableFuture列表。
            List<List<Long>> partitions = Lists.partition(mtShopIds, BATCH_SIZE);
            CompletableFuture
                    .allOf(partitions.stream()
                                     .filter(Objects::nonNull)
                                     .map(partition -> CompletableFuture
                                             .supplyAsync(() -> recallByLLM(partition, shopReviewRecallParam.getSemanticConditions()), threadPool.getExecutor())
                                             .thenAccept(shopId2reviews::putAll)
                                             .exceptionally(e -> {
                                                 log.error("ShopReviewRecallPlugin recallByLLM error, partition:{}", partition, e);
                                                 return null;
                                             })
                                     ).toArray(CompletableFuture[]::new)).join();

            // 2. 门店评价查询
            Map<Long, List<String>> result = shopId2reviews.entrySet().stream()
                                                           .filter(entry -> CollectionUtils.isNotEmpty(entry.getValue()))
                                                           .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
            int totalSize = result.values().stream().filter(CollectionUtils::isNotEmpty).map(Collection::size).reduce(Integer::sum).orElse(0);

            if (totalSize != 0) {
                sendReviewMessage(result, totalSize);
            }
            // 3. 结果处理
            log.info("ShopReviewRecallPlugin RecallResults: {}", result);
            AIAnswerData answerData = new AIAnswerData();
            answerData.setAnswer(JsonCodec.encodeWithUTF8(result));
            return answerData;
        } catch (Exception e) {
            log.error("ShopReviewRecallPlugin error, param:{}", shopReviewRecallParam, e);
            return new AIAnswerData("执行插件异常");
        }
    }

    private void sendReviewMessage(Map<Long, List<String>> shopId2reviews, int totalSize) {
        Map<Long, MtPoiDTO> mtPoiDTOs = shopAclService.batchGetMtShopInfo(Lists.newArrayList(shopId2reviews.keySet()));
        if (MapUtils.isNotEmpty(mtPoiDTOs)) {
            String poiNames = mtPoiDTOs.values()
                                       .stream()
                                       .filter(Objects::nonNull)
                                       .map(poi -> {
                                           if (StringUtils.isNotBlank(poi.getBranchName())) {
                                               return poi.getName() + "(" + poi.getBranchName() + ")";
                                           } else {
                                               return poi.getName();
                                           }
                                       }).filter(StringUtils::isNotBlank)
                                       .limit(3)
                                       .collect(Collectors.joining("、"));
            sendMessage(String.format(TOOL_END_FEEDBACK, poiNames, shopId2reviews.size(), totalSize));
        }
    }

    private void sendMessage(String message) {
        PilotBufferItemDO pilotBufferItemDO = new PilotBufferItemDO();
        pilotBufferItemDO.setType(BufferItemTypeEnum.MAIN_TEXT.getType());
        pilotBufferItemDO.setData(message);
        BufferUtils.writeMainTextBuffer(pilotBufferItemDO);
    }

    private Map<Long, List<String>> recallByLLM(List<Long> shopIds, String semanticConditions) {
        try {
            // 1. 查询评价
            Map<Long, List<String>> shopId2reviews = queryReviewsByMtShopIds(shopIds, semanticConditions);
            if (MapUtils.isEmpty(shopId2reviews)) {
                return Maps.newHashMap();
            }
            return shopId2reviews;
        } catch (Exception e) {
            log.error("shopReviewRecallParam recallByLLM error, shopIds:{}, semanticConditions:{}", shopIds, semanticConditions, e);
            return Maps.newHashMap();
        }
    }

    public Map<Long, List<String>> queryReviewsByMtShopIds(List<Long> mtShopIds, String semanticConditions) {
        if (CollectionUtils.isEmpty(mtShopIds)) {
            return Maps.newHashMap();
        }
        Map<Long, List<String>> result = Maps.newConcurrentMap();
        CompletableFuture.allOf(mtShopIds.stream()
                                         .filter(Objects::nonNull)
                                         .distinct()
                                         .map(mtShopId -> CompletableFuture.runAsync(() -> {
                                             List<String> reviews = Lists.newArrayList();
                                             if ((boolean) shopReviewRecallPluginConfig.getOrDefault(IS_MOCK_KEY, false)) {
                                                 List<String> res = JSONObject.parseObject(JsonCodec.encodeWithUTF8(shopReviewRecallPluginConfig.get(MOCK_DATA_KEY)), new TypeReference<List<String>>() {
                                                 });
                                                 if (CollectionUtils.isNotEmpty(res) || res.size() >= REVIEW_SIZE) {
                                                     Collections.shuffle(res);
                                                     reviews = res.subList(0, REVIEW_SIZE);
                                                 }
                                             } else if ((boolean) shopReviewRecallPluginConfig.getOrDefault(QUERY_VEX_KEY, false)) {
                                                 reviews = queryReviewsFromVex(mtShopId, semanticConditions);
                                             } else {
                                                 reviews = queryReviewFromEs(mtShopId, semanticConditions);
                                             }
                                             if (CollectionUtils.isNotEmpty(reviews)) {
                                                 result.put(mtShopId, reviews);
                                             }
                                         }, threadPool.getExecutor()))
                                         .toArray(CompletableFuture[]::new)).join();
        result.values().removeIf(CollectionUtils::isEmpty);
        return result;
    }

    private List<String> queryReviewsFromVex(Long mtShopId, String semanticConditions) {
        if (mtShopId == null) {
            return Lists.newArrayList();
        }
        List<ReviewSyncEntity> reviewSyncEntities = getReviewSyncEntities(mtShopId, semanticConditions);
        if (CollectionUtils.isEmpty(reviewSyncEntities)) {
            return Lists.newArrayList();
        }
        return reviewSyncEntities.stream()
                                 .filter(Objects::nonNull)
                                 .map(ReviewSyncEntity::getContent)
                                 .collect(Collectors.toList());
    }

    private List<String> queryReviewFromEs(Long mtShopId, String semanticConditions) {
        return esSearchProcessService.searchShopReview(mtShopId, semanticConditions, REVIEW_SIZE);
    }

    private List<ReviewSyncEntity> getReviewSyncEntities(Long mtShopId, String semanticConditions) {
        Map<String, String> map = new HashMap<>();
        map.put("mtShopId", String.valueOf(mtShopId));
        return reviewRepositoryService.searchReviewV2(semanticConditions, map, REVIEW_SIZE);
    }

    public String invokeLLM(String userMessage) {
        final String AI_CONFIG_KEY = "aiServiceConfig";
        if (!shopReviewRecallPluginConfig.containsKey(AI_CONFIG_KEY)) {
            return null;
        }
        Object configMap = shopReviewRecallPluginConfig.get(AI_CONFIG_KEY);
        AIServiceConfig config = JSONObject.parseObject(JsonCodec.encodeWithUTF8(configMap), AIServiceConfig.class);
        AIServiceContext aiServiceContext = LLMApplyUtil.buildAIServiceContext(config, userMessage);
        if (aiServiceContext == null || StringUtils.isBlank(userMessage)) {
            return StringUtils.EMPTY;
        }
        AIAnswerData answerData = advancedRagAIService.execute(aiServiceContext);
        if (answerData == null || StringUtils.isBlank(answerData.getAnswer()) || IMConstants.DEFAULT_ANSWER.equals(answerData.getAnswer())) {
            return StringUtils.EMPTY;
        }
        return answerData.getAnswer();
    }
}
