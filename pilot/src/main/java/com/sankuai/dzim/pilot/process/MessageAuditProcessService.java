package com.sankuai.dzim.pilot.process;

import com.sankuai.dzim.pilot.dal.entity.pilot.PilotChatMessageEntity;
import com.sankuai.dzim.pilot.gateway.api.assistant.data.SendMessageReq;
import com.sankuai.dzim.pilot.process.data.AuditResult;

import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @since 2025/05/14 15:42
 */
public interface MessageAuditProcessService {
    /**
     * 异步审核用户输入
     *
     * @param sendMessageReq
     * @param userMessageId
     * @param replymessageid
     * @return
     */
    CompletableFuture<AuditResult> auditUserMessage(SendMessageReq sendMessageReq, long userMessageId, long replymessageid);

    /**
     * 异步审核LLM生成
     *
     * @param chatMessageEntity
     * @param userMessageId
     * @param replyMessageId
     * @return
     */
    CompletableFuture<AuditResult> auditLLMGenerateMessage(PilotChatMessageEntity chatMessageEntity, long userMessageId, long replyMessageId);
}
