package com.sankuai.dzim.pilot.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.sankuai.dzim.pilot.chain.data.AIServiceContext;
import com.sankuai.dzim.pilot.chain.enums.AIServiceExtraKeyEnum;
import com.sankuai.dzim.pilot.domain.message.AssistantMessage;
import com.sankuai.dzim.pilot.domain.message.Message;
import com.sankuai.dzim.pilot.domain.message.PluginCall;
import com.sankuai.dzim.pilot.gateway.api.assistant.data.SendBeamMessageReq;
import com.sankuai.dzim.pilot.process.localplugin.data.UserContext;
import com.sankuai.dzim.pilot.process.localplugin.param.Param;
import com.sankuai.dzim.pilot.process.localplugin.reserve.param.BeamReserveConfirmParam;
import com.sankuai.dzim.pilot.scene.data.AssistantSceneContext;
import com.sankuai.dzim.pilot.scene.task.data.EnvContext;
import com.sankuai.dzim.pilot.utils.context.RequestContext;
import com.sankuai.dzim.pilot.utils.context.RequestContextConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2025/04/21 11:16
 */
@Component
@Slf4j
public class PluginContextUtil {

    public static EnvContext getEnvContextFromRequestContext() {
        AssistantSceneContext assistantSceneContext = RequestContext.getAttribute(RequestContextConstants.ASSISTANT_CONTEXT);
        return Optional.ofNullable(assistantSceneContext)
                .map(AssistantSceneContext::getExtra)
                .map(extra -> MapUtils.getString(extra, AIServiceExtraKeyEnum.ENV_CONTEXT.getKey(), StringUtils.EMPTY))
                .map(s -> JsonCodec.decode(s, EnvContext.class))
                .orElse(null);
    }

    /**
     * 基于param获取环境上下文
     *
     * @param param
     * @return
     */
    public static EnvContext getEnvContext(Param param) {
        return Optional.ofNullable(param)
                .map(Param::getAiServiceContext)
                .map(AIServiceContext::getExtraInfo)
                .map(extraInfo -> MapUtils.getString(extraInfo, AIServiceExtraKeyEnum.ENV_CONTEXT.getKey(), StringUtils.EMPTY))
                .map(s -> JsonCodec.decode(s, EnvContext.class))
                .orElse(null);
    }

    public static SendBeamMessageReq getBeamRequestContext(Param param) {
        JSONObject jsonObject = (JSONObject) Optional.ofNullable(param)
                .map(Param::getAiServiceContext)
                .map(AIServiceContext::getExtraInfo)
                .map(extraInfo -> MapUtils.getObject(extraInfo, AIServiceExtraKeyEnum.BEAM_REQUEST_CONTEXT.getKey(), null))
                .orElse(null);
        if (jsonObject == null) {
            return null;
        }
        SendBeamMessageReq req = JsonCodec.decode(jsonObject.toJSONString(), SendBeamMessageReq.class);
        // TODO 去掉mock
        if (req.getUser_info() != null) {
            req.getUser_info().setAppversion("12.34.200");
        }
        return req;
    }

    public static UserContext getUserContext(Param param) {
        if (param == null) {
            return null;
        }
        return getUserContext(param.getAiServiceContext());
    }

    /**
     * 获取用户上下文
     * 注：传入的aiServiceContext的extraInfo中的userContext字段可能是UserContext对象，也可能是JSON
     *
     * @param aiServiceContext
     * @return
     */
    public static UserContext getUserContext(AIServiceContext aiServiceContext) {
        return Optional.ofNullable(aiServiceContext)
                .map(AIServiceContext::getExtraInfo)
                .map(extraInfo -> MapUtils.getObject(extraInfo, AIServiceExtraKeyEnum.USER_CONTEXT.getKey()))
                .map(PluginContextUtil::parseUserContext)
                .orElse(null);
    }

    /**
     * 解析用户上下文
     *
     * @param userContextObj
     * @return
     */
    public static UserContext parseUserContext(Object userContextObj) {
        UserContext userContext = JSONObject.parseObject(JsonCodec.encodeWithUTF8(userContextObj), UserContext.class);
        JSONObject jsonObject = JSONObject.parseObject(JsonCodec.encodeWithUTF8(userContextObj));
        JSONArray memoryJson = jsonObject.getJSONArray("memory");
        if (memoryJson != null) {
            // 填充memory列表
            fillMemory(userContext, memoryJson);
        }
        return userContext;
    }

    /**
     * 填充memory列表
     *
     * @param userContext
     * @param memoryJson
     */
    private static void fillMemory(UserContext userContext, JSONArray memoryJson) {
        userContext.setMemory(Lists.newArrayList());
        for (int i = 0; i < memoryJson.size(); i++) {
            JSONObject messageJson = memoryJson.getJSONObject(i);
            String className = messageJson.getString("@class");
            try {
                String memoryJsonStr = messageJson.toJSONString();
                Object message = JSONObject.parseObject(memoryJsonStr, Class.forName(className));
                if (message instanceof AssistantMessage) {
                    // 特殊处理，针对AssistantMessage填充插件调用信息
                    processAssistantMessage(messageJson, (AssistantMessage) message);
                }
                userContext.getMemory().add((Message) message);
            } catch (Exception e) {
                log.error("getUserContext-error,messageJson:{}", messageJson, e);
            }
        }
    }

    /**
     * 处理AssistantMessage类型的消息
     *
     * @param messageJson
     * @param message
     */
    public static void processAssistantMessage(JSONObject messageJson, AssistantMessage message) {
        JSONArray pluginCallsJson = messageJson.getJSONArray("pluginCalls");
        List<JsonNode> jsonNodes = Lists.newArrayList();
        if (pluginCallsJson != null) {
            // 填充插件调用信息
            message.setPluginCalls(parsePluginCalls(pluginCallsJson, jsonNodes));
        }
    }

    /**
     * 解析插件调用信息
     *
     * @param pluginCallsJson
     * @param jsonNodes
     * @return
     */
    private static List<PluginCall> parsePluginCalls(JSONArray pluginCallsJson, List<JsonNode> jsonNodes) {
        for (int j = 0; j < pluginCallsJson.size(); j++) {
            JSONObject object = pluginCallsJson.getJSONObject(j);
            ObjectMapper mapper = new ObjectMapper();
            jsonNodes.add(mapper.valueToTree(object.get("arguments")));
            pluginCallsJson.getJSONObject(j).remove("arguments");
        }
        List<PluginCall> pluginCalls = JSONObject.parseObject(pluginCallsJson.toJSONString(), new TypeReference<List<PluginCall>>() {
        });
        for (int j = 0; j < pluginCalls.size(); j++) {
            pluginCalls.get(j).setArguments(jsonNodes.get(j));
        }
        return pluginCalls;
    }

    /**
     * 获取Beam用户上下文
     *
     * @param aiServiceContext
     * @return
     */
    public static SendBeamMessageReq geSendBeamMessageReq(AIServiceContext aiServiceContext) {
        return (SendBeamMessageReq) Optional.ofNullable(aiServiceContext)
                .map(AIServiceContext::getExtraInfo)
                .map(extraInfo -> MapUtils.getObject(extraInfo, AIServiceExtraKeyEnum.BEAM_REQUEST_CONTEXT.getKey()))
                .orElse(null);
    }

    public void replaceShopIdWithBeamContext(BeamReserveConfirmParam param, Map<String, String> testShopIdReplaceConfig) {
        if (param == null) {
            return;
        }
        SendBeamMessageReq sendBeamMessageReq = PluginContextUtil.getBeamRequestContext(param);
        if (sendBeamMessageReq == null || sendBeamMessageReq.getContext() == null || sendBeamMessageReq.getContext().getPoi_id() == null) {
            return;
        }
        param.setShopId(Long.parseLong(sendBeamMessageReq.getContext().getPoi_id()));
        replaceTestShopIdWithBeamContext(param, testShopIdReplaceConfig);
    }

    public void replaceTestShopIdWithBeamContext(BeamReserveConfirmParam param, Map<String, String> testShopIdReplaceConfig) {
        if (testShopIdReplaceConfig.containsKey("reserve") && testShopIdReplaceConfig.get("reserve") != null) {
            param.setShopId(Long.parseLong(testShopIdReplaceConfig.get("reserve")));
        }
    }
}
