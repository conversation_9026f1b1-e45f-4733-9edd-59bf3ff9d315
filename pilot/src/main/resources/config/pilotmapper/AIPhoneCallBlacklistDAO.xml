<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sankuai.dzim.pilot.dal.pilotdao.aiphonecall.AIPhoneCallBlacklistDAO">
    <resultMap id="BaseMap" type="com.sankuai.dzim.pilot.dal.entity.aiphonecall.AIPhoneCallBlacklistEntity">
        <result column="ID" property="id"/>
        <result column="DpShopID" property="dpShopId"/>
        <result column="BlackType" property="blackType"/>
        <result column="ExpireTime" property="expireTime"/>
        <result column="OptUser" property="optUser"/>
        <result column="Status" property="status"/>
        <result column="ExtraData" property="extraData"/>
        <result column="AddTime" property="addTime"/>
        <result column="UpdateTime" property="updateTime"/>
    </resultMap>

    <sql id="selectKeys">
        `ID`,
        `DpShopID`,
        `BlackType`,
        `ExpireTime`,
        `OptUser`,
        `Status`,
        `ExtraData`,
        `AddTime`,
        `UpdateTime`
    </sql>

    <select id="getByDpShopId" resultMap="BaseMap" parameterType="map">
        SELECT
        <include refid="selectKeys"/>
        FROM AI_Phone_Call_Blacklist
        WHERE DpShopID = #{dpShopId} AND Status = 1
    </select>

    <insert id="insert" parameterType="com.sankuai.dzim.pilot.dal.entity.aiphonecall.AIPhoneCallBlacklistEntity">
        INSERT INTO AI_Phone_Call_Blacklist
        (
            `DpShopID`,
            `BlackType`,
            `ExpireTime`,
            `OptUser`,
            `Status`,
            `ExtraData`,
            `AddTime`,
            `UpdateTime`
        )
        VALUES
            (
                #{dpShopId},
                #{blackType},
                #{expireTime},
                #{optUser},
                #{status},
                #{extraData},
                NOW(),
                NOW()
            )
    </insert>
    
</mapper>

