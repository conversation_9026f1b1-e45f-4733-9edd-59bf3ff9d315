<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sankuai.dzim.pilot.dal.pilotdao.aiphonecall.AIPhoneCallTaskDAO">
    <resultMap id="BaseMap" type="com.sankuai.dzim.pilot.dal.entity.aiphonecall.AIPhoneCallTaskEntity">
        <result column="ID" property="id"/>
        <result column="BizID" property="bizId"/>
        <result column="SceneType" property="sceneType"/>
        <result column="Status" property="status"/>
        <result column="Platform" property="platform"/>
        <result column="ExtraData" property="extraData"/>
        <result column="BizData" property="bizData"/>
        <result column="Source" property="source" />
        <result column="AddTime" property="addTime"/>
        <result column="UpdateTime" property="updateTime"/>
    </resultMap>

    <sql id="selectKeys">
        `ID`,
        `BizID`,
        `SceneType`,
        `Status`,
        `Platform`,
        `ExtraData`,
        `BizData`,
        `Source`,
        `AddTime`,
        `UpdateTime`
    </sql>

    <select id="getByTaskId" parameterType="map" resultMap="BaseMap">
        SELECT
        <include refid="selectKeys"/>
        FROM AI_Phone_Call_Task
        WHERE ID = #{id}
    </select>

    <select id="getByBizId" resultMap="BaseMap" parameterType="map">
        SELECT
        <include refid="selectKeys"/>
        FROM AI_Phone_Call_Task
        WHERE BizID = #{bizId} AND SceneType = #{sceneType}
    </select>

    <update id = "updateTaskStatusById" parameterType="map">
        UPDATE AI_Phone_Call_Task
        SET
        <if test="status != null">
            Status = #{status},
        </if>
        UpdateTime = now()
        WHERE ID = #{id}
    </update>

    <update id="updateExtraData" parameterType="map">
        UPDATE AI_Phone_Call_Task
        SET
        ExtraData = #{entity.extraData}
        WHERE ID = #{entity.id}
    </update>

    <insert id="insert" useGeneratedKeys="true" keyProperty="entity.id" parameterType="com.sankuai.dzim.pilot.dal.entity.aiphonecall.AIPhoneCallTaskEntity">
        insert into AI_Phone_Call_Task
        (
            `BizID`,
            `SceneType`,
            `Status`,
            `Platform`,
            `ExtraData`,
            `BizData`,
            `Source`,
            `AddTime`,
            `UpdateTime`
        )
        values
            (
                #{entity.bizId},
                #{entity.sceneType},
                #{entity.status},
                #{entity.platform},
                #{entity.extraData},
                #{entity.bizData},
                #{entity.source},
                NOW(),
                NOW()
            )
    </insert>


</mapper>

