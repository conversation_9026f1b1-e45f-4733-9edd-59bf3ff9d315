<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sankuai.dzim.pilot.dal.pilotdao.aibook.AIBookCallCntDAO">
    <resultMap id="BaseMap" type="com.sankuai.dzim.pilot.dal.entity.aibook.AIBookCallCntEntity">
        <result column="ID" property="id"/>
        <result column="LogDate" property="logDate"/>
        <result column="MtCityId" property="mtCityId"/>
        <result column="MtCityName" property="mtCityName"/>
        <result column="MtDistrictId" property="mtDistrictId"/>
        <result column="MtDistrictName" property="mtDistrictName"/>
        <result column="MtRegionId" property="mtRegionId"/>
        <result column="MtRegionName" property="mtRegionName"/>
        <result column="CallCnt" property="callCnt"/>
        <result column="Status" property="status"/>
        <result column="AddTime" property="addTime"/>
        <result column="UpdateTime" property="updateTime"/>
    </resultMap>

    <sql id="selectKeys">
        `ID`,
        `LogDate`,
        `MtCityId`,
        `MtCityName`,
        `MtDistrictId`,
        `MtDistrictName`,
        `MtRegionId`,
        `MtRegionName`,
        `CallCnt`,
        `Status`,
        `AddTime`,
        `UpdateTime`
    </sql>


    <select id="getByCityDistrictRegion" resultMap="BaseMap" parameterType="map">
        SELECT
        <include refid="selectKeys"/>
        FROM AI_Book_Call_Cnt
        WHERE MtCityId = #{cityId}
        <if test="districtId > 0">
            AND MtDistrictId = #{districtId}
        </if>
        <if test="regionId > 0">
            AND MtRegionId = #{regionId}
        </if>
        AND Status = 1
        ORDER BY ID DESC
        LIMIT 1
    </select>


    <select id="getByCityDistrict" resultMap="BaseMap" parameterType="map">
        SELECT
        <include refid="selectKeys"/>
        FROM AI_Book_Call_Cnt
        WHERE MtCityId = #{cityId}
        <if test="districtId > 0">
            AND MtDistrictId = #{districtId}
        </if>
        AND MtRegionId is NULL
        AND Status = 1
        ORDER BY ID DESC
        LIMIT 1
    </select>


</mapper>

