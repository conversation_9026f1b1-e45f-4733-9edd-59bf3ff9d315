<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sankuai.dzim.pilot.dal.pilotdao.medical.DiseaseScienceInfoPageDAO">
    <resultMap id="BaseMap" type="com.sankuai.dzim.pilot.dal.entity.pilot.medical.DiseaseScienceInfoEntity">
        <result column="ID" property="id"/>
        <result column="Name" property="name"/>
        <result column="Overview" property="overview"/>
        <result column="Symptoms" property="symptoms"/>
        <result column="Causes" property="causes"/>
        <result column="Trend" property="trend"/>
        <result column="Advice" property="advice"/>
        <result column="Daily" property="daily"/>
        <result column="VectorID" property="vectorId"/>
        <result column="UpdateTime" property="updateTime"/>
        <result column="Questions" property="questions"/>
    </resultMap>

    <sql id="selectKeys">
        `ID`,
        `Name`,
        `Overview`,
        `Symptoms`,
        `Causes`,
        `Trend`,
        `Advice`,
        `Daily`,
        `VectorId`,
        `UpdateTime`,
        `Questions`
    </sql>

    <select id="selectById" parameterType="long" resultMap="BaseMap">
        SELECT <include refid="selectKeys"></include>
            FROM Pilot_DiseaseScienceInfoPage WHERE ID = #{id}
    </select>

    <select id="selectByName" parameterType="string" resultMap="BaseMap">
        SELECT <include refid="selectKeys"></include>
        FROM Pilot_DiseaseScienceInfoPage WHERE name = #{name}
    </select>


    <select id="batchSelectByIds" parameterType="map" resultMap="BaseMap">
        SELECT
        <include refid="selectKeys">
        </include>
        FROM Pilot_DiseaseScienceInfoPage
        where ID IN
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>

    <select id="batchSelectByPage" parameterType="map" resultMap="BaseMap">
        SELECT
        <include refid="selectKeys"/>
        FROM Pilot_DiseaseScienceInfoPage
        WHERE id > #{start}
        LIMIT #{pageSize}
    </select>


    <sql id="insertKeys">
        `Name`,
        `Overview`,
        `Symptoms`,
        `Causes`,
        `Trend`,
        `Advice`,
        `Daily`,
        `UpdateTime`
    </sql>

    <sql id="insertValues">
        #{entity.name},
        #{entity.overview},
        #{entity.symptoms},
        #{entity.causes},
        #{entity.trend},
        #{entity.advice},
        #{entity.daily},
        #{entity.updateTime}
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="entity.id">
        INSERT INTO Pilot_DiseaseScienceInfoPage(
            <include refid="insertKeys"/>
        )
        VALUES(
            <include refid="insertValues"/>
        )
    </insert>


    <update id="updateContent" parameterType="map">
        UPDATE Pilot_DiseaseScienceInfoPage
        SET Overview = #{entity.overview},
            Symptoms = #{entity.symptoms},
            Causes = #{entity.causes},
            Trend = #{entity.trend},
            Advice = #{entity.advice},
            Daily = #{entity.daily},
            UpdateTime = #{entity.updateTime}
        WHERE Name = #{entity.name}
    </update>

    <update id="updateVectorId" parameterType="map">
        UPDATE Pilot_DiseaseScienceInfoPage
        SET VectorID = #{vectorId}
        WHERE ID = #{id}
    </update>

    <update id="updateQuestions" parameterType="map">
        UPDATE Pilot_DiseaseScienceInfoPage
        SET Questions = #{questions}
        WHERE ID = #{id}
    </update>

    <delete id="deleteById" parameterType="long">
        DELETE FROM Pilot_DiseaseScienceInfoPage WHERE id = #{id}
    </delete>

</mapper>

