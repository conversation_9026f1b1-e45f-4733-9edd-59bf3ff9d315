<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sankuai.dzim.pilot.dal.pilotdao.medical.MedicalCheckUpReportRecordDAO">
    <resultMap id="BaseMap" type="com.sankuai.dzim.pilot.dal.entity.pilot.medical.MedicalCheckUpReportRecordEntity">
        <result column="ID" property="id"/>
        <result column="UserID" property="userId"/>
        <result column="ReportUrl" property="reportUrl"/>
        <result column="ReportImageUrl" property="reportImageUrl"/>
        <result column="OcrResult" property="ocrResult"/>
        <result column="AnalysisResult" property="analysisResult"/>
        <result column="AnalysisStatus" property="analysisStatus"/>
        <result column="Status" property="status"/>
        <result column="ExtraData" property="extraData"/>
        <result column="AddTime" property="addTime"/>
        <result column="UpdateTime" property="updateTime"/>
    </resultMap>

    <sql id="selectKeys">
        `ID`,
        `UserID`,
        `ReportUrl`,
        `ReportImageUrl`,
        `OcrResult`,
        `AnalysisResult`,
        `AnalysisStatus`,
        `Status`,
        `ExtraData`,
        `AddTime`,
        `UpdateTime`
    </sql>

    <insert id="insert" parameterType="map" keyProperty="entity.id" useGeneratedKeys="true">
        INSERT INTO Pilot_MedicalHealthCheckUpReportRecord
            (`UserID`, `ReportUrl`, `ReportImageUrl`,`OcrResult`, `AnalysisResult`, `AnalysisStatus`, `Status`, `ExtraData`, `AddTime`, `UpdateTime`)
        VALUES (#{entity.userId},
                #{entity.reportUrl},
                '',
                #{entity.ocrResult},
                #{entity.analysisResult},
                1,
                1,
                #{entity.extraData},
                now(),
                now())
    </insert>

    <update id="update" parameterType="map">
        UPDATE
            Pilot_MedicalHealthCheckUpReportRecord
        SET
            <if test="entity.reportUrl != null">
                ReportUrl = #{entity.reportUrl},
            </if>
            <if test="entity.reportImageUrl != null">
                ReportImageUrl = #{entity.reportImageUrl},
            </if>
            <if test="entity.ocrResult != null">
                OcrResult = #{entity.ocrResult},
            </if>
            <if test="entity.analysisResult != null">
                AnalysisResult = #{entity.analysisResult},
            </if>
            <if test="entity.analysisStatus != null">
                AnalysisStatus = #{entity.analysisStatus},
            </if>
            <if test="entity.status != null">
                Status = #{entity.status},
            </if>
            <if test="entity.extraData != null">
                ExtraData = #{entity.extraData},
            </if>
            UpdateTime = now()
        WHERE ID = #{entity.id}
    </update>

    <select id="queryById" parameterType="map" resultMap="BaseMap">
        SELECT
            <include refid="selectKeys"/>
        FROM Pilot_MedicalHealthCheckUpReportRecord
        WHERE ID = #{id}
        AND Status = 1
    </select>

    <select id="queryByUserId" parameterType="map" resultMap="BaseMap">
        SELECT
            <include refid="selectKeys"/>
        FROM Pilot_MedicalHealthCheckUpReportRecord
        WHERE UserID = #{userId}
        AND Status = 1
        ORDER BY AddTime DESC
        LIMIT #{offset}, #{pageSize}
    </select>

    <select id="queryCountByUserId" parameterType="map" resultType="java.lang.Integer">
        SELECT
            COUNT(ID)
        FROM Pilot_MedicalHealthCheckUpReportRecord
        WHERE UserID = #{userId}
          AND Status = 1
    </select>

    <select id="queryByIds" parameterType="map" resultMap="BaseMap">
        SELECT
        <include refid="selectKeys"/>
        FROM Pilot_MedicalHealthCheckUpReportRecord
        WHERE ID IN
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
        AND Status = 1
    </select>

    <update id="deleteById" parameterType="map">
        UPDATE
            Pilot_MedicalHealthCheckUpReportRecord
        SET Status = 0
        WHERE ID = #{id}
    </update>
</mapper>
