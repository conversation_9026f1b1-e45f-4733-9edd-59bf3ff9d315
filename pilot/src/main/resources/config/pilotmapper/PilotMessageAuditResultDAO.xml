<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sankuai.dzim.pilot.dal.pilotdao.PilotMessageAuditResultDAO">
    <resultMap id="BaseMap" type="com.sankuai.dzim.pilot.dal.entity.pilot.PilotMessageAuditResultEntity">
        <result column="ID" property="id"/>
        <result column="MessageID" property="messageId"/>
        <result column="Content" property="content"/>
        <result column="AuditStrategy" property="auditStrategy"/>
        <result column="Creator" property="creator"/>
        <result column="AuditResult" property="auditResult"/>
        <result column="Reason" property="reason"/>
        <result column="Extra" property="extra"/>
        <result column="AddTime" property="addTime"/>
        <result column="UpdateTime" property="updateTime"/>
    </resultMap>

    <sql id="selectKeys">
        `ID`,
        `MessageID`,
        `Content`,
        `AuditStrategy`,
        `Creator`,
        `AuditResult`,
        `Reason`,
        `Extra`,
        `AddTime`,
        `UpdateTime`
    </sql>

    <insert id="insert" parameterType="map" keyProperty="entity.id" useGeneratedKeys="true">
        INSERT INTO `Pilot_MessageAuditResult`
        (`MessageID`, `Content`, `AuditStrategy`, `Creator`, `AuditResult`, `Reason`, `Extra`, `AddTime`, `UpdateTime`)
        VALUES (#{entity.messageId},
                #{entity.content},
                #{entity.auditStrategy},
                #{entity.creator},
                #{entity.auditResult},
                #{entity.reason},
                #{entity.extra},
                now(),
                now())
    </insert>


</mapper>