<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.dzim.pilot.dal.pilotdao.PilotQuestionUVDAO">
    <resultMap id="BaseMap" type="com.sankuai.dzim.pilot.dal.entity.pilot.PilotQuestionUVEntity">
        <result column="ID" property="id"/>
        <result column="QuestionID" property="questionId"/>
        <result column="Count" property="count"/>
        <result column="Type" property="type"/>
        <result column="Status" property="status"/>
        <result column="AddTime" property="addTime"/>
        <result column="UpdateTime" property="updateTime"/>
    </resultMap>

    <sql id="selectKeys">
        `ID`,
        `QuestionID`,
        `Count`,
        `Type`,
        `Status`,
        `AddTime`,
        `UpdateTime`
    </sql>

    <select id="queryMaxCountByType" parameterType="map" resultType="long">
        SELECT max(Count)
        FROM Pilot_QuestionUV
        WHERE Type = #{type} AND Status = 1
    </select>

    <select id="queryMinCountByType" parameterType="map" resultType="long">
        SELECT min(Count)
        FROM Pilot_QuestionUV
        WHERE Type = #{type} AND Status = 1
    </select>

    <select id="batchFindByQuestionIds" parameterType="map" resultMap="BaseMap">
        SELECT
        <include refid="selectKeys">
        </include>
        FROM Pilot_QuestionUV
        WHERE QuestionID IN
        <foreach collection="questionIds" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
        AND Status=1
    </select>

    <select id="findByQuestionIdAndType" parameterType="map" resultMap="BaseMap">
        SELECT
        <include refid="selectKeys">
        </include>
        FROM Pilot_QuestionUV
        WHERE QuestionID = #{questionId}
        AND Type = #{type}
        AND Status = 1
    </select>
    <select id="batchFindByType" parameterType="map" resultMap="BaseMap">
        SELECT
        <include refid="selectKeys">
        </include>
        FROM Pilot_QuestionUV
        WHERE Type = #{type}
        AND Status = 1
    </select>

    <update id="update" parameterType="map">
        UPDATE Pilot_QuestionUV
        SET
        <if test="entity.questionId != null">
            QuestionID = #{entity.questionId},
        </if>
        <if test="entity.count != null">
            Count = #{entity.count},
        </if>
        <if test="entity.type != null">
            Type = #{entity.type},
        </if>
        <if test="entity.status != null">
            Status = #{entity.status},
        </if>
        UpdateTime = now()
        WHERE ID = #{entity.id}
    </update>

    <insert id="insert" parameterType="map" keyProperty="entity.id" useGeneratedKeys="true">
        INSERT INTO Pilot_QuestionUV
        (`QuestionID`, `Count`, `Type`, `Status`, `AddTime`, `UpdateTime`)
        VALUES (#{entity.questionId},
                #{entity.count},
                #{entity.type},
                1,
                now(),
                now())
    </insert>
    <insert id="upsertAndIncrement" keyProperty="entity.id" useGeneratedKeys="true">
        INSERT INTO Pilot_QuestionUV
            (`QuestionID`, `Count`, `Type`, `Status`, `AddTime`, `UpdateTime`)
        VALUES (#{entity.questionId},
                #{entity.count},
                #{entity.type},
                1,
                now(),
                now())
            ON DUPLICATE KEY UPDATE
            Count = Count + 1,
            Status = 1,
            UpdateTime = now()
    </insert>
</mapper>