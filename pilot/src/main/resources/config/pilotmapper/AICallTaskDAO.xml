<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sankuai.dzim.pilot.dal.pilotdao.aicall.AICallTaskDAO">
    <resultMap id="BaseMap" type="com.sankuai.dzim.pilot.dal.entity.aicall.AICallTaskEntity">
        <result column="ID" property="id"/>
        <result column="UserId" property="userId"/>
        <result column="ShopId" property="shopId"/>
        <result column="Platform" property="platform"/>
        <result column="ActivityType" property="activityType"/>
        <result column="BizData" property="bizData"/>
        <result column="TaskStatus" property="taskStatus"/>
        <result column="UserPhone" property="userPhone"/>
        <result column="PositionData" property="positionData"/>
        <result column="SceneCode" property="sceneCode"/>
        <result column="TaskSource" property="taskSource"/>
        <result column="ExtraData" property="extraData"/>
        <result column="AddTime" property="addTime"/>
        <result column="UpdateTime" property="updateTime"/>
        <result column="Status" property="status"/>
    </resultMap>

    <sql id="selectKeys">
        `ID`,
        `UserId`,
        `ShopId`,
        `Platform`,
        `ActivityType`,
        `BizData`,
        `TaskStatus`,
        `UserPhone`,
        `PositionData`,
        `SceneCode`,
        `TaskSource`,
        `ExtraData`,
        `AddTime`,
        `UpdateTime`,
        `Status`
    </sql>

    <insert id="insert" parameterType="map" keyProperty="entity.id" useGeneratedKeys="true">
        INSERT INTO AI_Call_Task
        (`UserId`,
         `ShopId`,
         `Platform`,
         `ActivityType`,
         `BizData`,
         `TaskStatus`,
         `UserPhone`,
         `PositionData`,
         `SceneCode`,
         `TaskSource`,
         `AddTime`,
         `UpdateTime`,
         `Status`)
        VALUES (#{entity.userId},
                #{entity.shopId},
                #{entity.platform},
                #{entity.activityType},
                #{entity.bizData},
                #{entity.taskStatus},
                #{entity.userPhone},
                #{entity.positionData},
                #{entity.sceneCode},
                #{entity.taskSource},
                now(),
                now(),
                1)
    </insert>

    <select id="getById" parameterType="map" resultMap="BaseMap">
        SELECT
        <include refid="selectKeys"/>
        FROM AI_Call_Task
        WHERE ID = #{id}
        AND Status = 1
    </select>


    <update id="updateTaskStatus" parameterType="map">
        UPDATE AI_Call_Task
        SET `TaskStatus`= #{taskStatus},
            `UpdateTime` = now()
        WHERE id =  #{id}
    </update>

    <update id="updateTaskCallSuccess" parameterType="map">
        UPDATE AI_Call_Task
        SET `TaskStatus`= #{taskStatus},
            `UpdateTime` = now()
        WHERE id =  #{id}
    </update>

    <select id="queryLatestBookTask" resultMap="BaseMap" parameterType="map">
        SELECT <include refid="selectKeys"/>
        FROM AI_Call_Task
        WHERE UserId = #{userId} AND Platform = #{platform}
        ORDER BY ID DESC LIMIT 1
    </select>

    <select id="queryCallTaskList" resultMap="BaseMap" parameterType="map">
        SELECT <include refid="selectKeys"/>
        FROM AI_Call_Task
        WHERE UserId = #{userId} AND Platform = #{platform}
    </select>

    <select id="queryUserTasksByStatus" resultMap="BaseMap" parameterType="map">
        SELECT
        <include refid="selectKeys"/>
        FROM AI_Call_Task
        WHERE UserId = #{userId} AND Platform = #{platform}
        AND Status = 1
        AND TaskStatus in
        <foreach item="taskStatus" collection="taskStatusList" open="(" close=")" separator="," index="">
            #{taskStatus}
        </foreach>
    </select>

</mapper>

