<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.dzim.pilot.dal.pilotdao.ConsultantKnowledgeDAO">

    <resultMap type="com.sankuai.dzim.pilot.dal.entity.pilot.ConsultantKnowledgeEntity" id="BaseMap">
        <result property="id" column="ID" />
        <result property="imMerchantId" column="ImMerchantID" />
        <result property="knowledgeBaseType" column="KnowledgeBaseType" />
        <result property="question" column="Question" />
        <result property="answer" column="Answer" />
        <result property="images" column="Image" />
        <result property ="quickTag" column ="QuickTag"/>
        <result property="foreWeight" column="ForeWeight"/>
        <result property="backWeight" column="BackWeight"/>
        <result property="foreCategoryId" column="ForeCategoryID"/>
        <result property="backCategoryId" column="BackCategoryID"/>
        <result property="bizExtendId" column="BizExtendID"/>
        <result property="bizKnowledgeId" column="BizKnowledgeID"/>
        <result property="status" column="Status"/>
        <result property="lastEditor" column="LastEditor" />
        <result property="addTime" column="AddTime" />
        <result property="updateTime" column="UpdateTime" />
    </resultMap>

    <sql id="selectKeys">
        `ID`,
        `ImMerchantID`,
        `KnowledgeBaseType`,
        `Question`,
        `Answer`,
        `Image`,
        `QuickTag`,
        `ForeWeight`,
        `BackWeight`,
        `ForeCategoryID`,
        `BackCategoryID`,
        `BizExtendID`,
        `BizKnowledgeID`,
        `Status`,
        `LastEditor`,
        `AddTime`,
        `UpdateTime`
    </sql>

    <insert id = "insert" parameterType="map" keyProperty="entity.id" useGeneratedKeys="true">
        INSERT INTO Pilot_ConsultantKnowledge
        <trim prefix="(" suffix=")" suffixOverrides=",">
            `ID`,
            `ImMerchantID`,
            `KnowledgeBaseType`,
            `Question`,
            `Answer`,
            `Image`,
            `QuickTag`,
            `ForeWeight`,
            `BackWeight`,
            `ForeCategoryID`,
            `BackCategoryID`,
            `BizExtendID`,
            `BizKnowledgeID`,
            `Status`,
            `LastEditor`,
            `AddTime`,
            `UpdateTime`
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{entity.id},
            #{entity.imMerchantId},
            #{entity.knowledgeBaseType},
            #{entity.question},
            #{entity.answer},
            #{entity.images},
            #{entity.quickTag},
            #{entity.foreWeight},
            #{entity.backWeight},
            #{entity.foreCategoryId},
            #{entity.backCategoryId},
            #{entity.bizExtendId},
            #{entity.bizKnowledgeId},
            #{entity.status},
            #{entity.lastEditor},
            now(),
            now(),
        </trim>
    </insert>

    <update id = "update" parameterType="map">
        UPDATE Pilot_ConsultantKnowledge
        SET
        <if test="entity.question != null">
            Question = #{entity.question},
        </if>
        <if test="entity.answer != null">
            Answer = #{entity.answer},
        </if>
        <if test="entity.images != null">
            Image = #{entity.images},
        </if>
        <if test="entity.quickTag != null">
            QuickTag = #{entity.quickTag},
        </if>
        <if test="entity.foreWeight > 0">
            ForeWeight = #{entity.foreWeight},
        </if>
        <if test="entity.backWeight > 0">
            BackWeight = #{entity.backWeight},
        </if>
        <if test="entity.foreCategoryId > 0">
            ForeCategoryID = #{entity.foreCategoryId},
        </if>
        <if test="entity.backCategoryId > 0">
            BackCategoryID = #{entity.backCategoryId},
        </if>
        <if test="entity.bizExtendId > 0">
            BizExtendID = #{entity.bizExtendId},
        </if>
        <if test="entity.bizKnowledgeId > 0">
            BizKnowledgeID = #{entity.bizKnowledgeId},
        </if>
        <if test="entity.status >= 0">
            Status = #{entity.status},
        </if>
        LastEditor = #{entity.lastEditor},
        UpdateTime = now()
        WHERE ID = #{entity.id}
    </update>

    <select id="paginateForeKnowledgeByCategoryIds" parameterType="map" resultMap="BaseMap">
        SELECT
        <include refid="selectKeys"/>
        FROM Pilot_ConsultantKnowledge
        WHERE ForeCategoryID IN
        <foreach item="foreCategoryId" collection="foreCategoryIds" open="(" close=")" separator="," index="">
            #{foreCategoryId}
        </foreach>
        AND ImMerchantID = #{imMerchantId}
        AND Status IN (1, 3)
        ORDER BY ID
        LIMIT #{limit} OFFSET #{offset}
    </select>

    <select id="paginateKnowledgeByForeCategoryAndType" parameterType="map" resultMap="BaseMap">
        SELECT
        <include refid="selectKeys"/>
        FROM Pilot_ConsultantKnowledge
        WHERE ForeCategoryID IN
        <foreach item="foreCategoryId" collection="foreCategoryIds" open="(" close=")" separator="," index="">
            #{foreCategoryId}
        </foreach>
        AND KnowledgeBaseType = #{knowledgeBaseType}
        AND Status IN (1, 3)
        ORDER BY ID
        LIMIT #{limit} OFFSET #{offset}
    </select>

    <select id="paginateBackKnowledgeByWeight" parameterType="map" resultMap="BaseMap">
        SELECT
        <include refid="selectKeys"/>
        FROM Pilot_ConsultantKnowledge
        WHERE ImMerchantID = #{imMerchantId}
        AND Status IN (1, 2)
        AND  <![CDATA[ BackWeight < #{maxBackWeight}]]>
        ORDER BY BackWeight DESC
        LIMIT #{limit}
    </select>

    <select id="paginateBackKnowledgeByWeightAndType" parameterType="map" resultMap="BaseMap">
        SELECT
        <include refid="selectKeys"/>
        FROM Pilot_ConsultantKnowledge
        WHERE KnowledgeBaseType = #{knowledgeBaseType}
        AND Status IN (1, 2)
        AND  <![CDATA[ BackWeight < #{maxBackWeight}]]>
        ORDER BY BackWeight DESC
        LIMIT #{limit}
    </select>

    <select id="batchGet" parameterType="map" resultMap="BaseMap">
        SELECT
        <include refid="selectKeys"/>
        FROM Pilot_ConsultantKnowledge
        WHERE ID IN
        <foreach item="id" collection="ids" open="(" close=")" separator="," index="">
            #{id}
        </foreach>
    </select>

    <select id="batchGetByBizKnowledgeId" parameterType="map" resultMap="BaseMap">
        SELECT
        <include refid="selectKeys"/>
        FROM Pilot_ConsultantKnowledge
        WHERE BizKnowledgeID IN
        <foreach item="bizKnowledgeId" collection="bizKnowledgeIds" open="(" close=")" separator="," index="">
            #{bizKnowledgeId}
        </foreach>
    </select>

    <update id="batchUpdateStatus" parameterType="map">
        UPDATE Pilot_ConsultantKnowledge
        SET
        Status = #{status},
        LastEditor = #{editor},
        UpdateTime = now()
        WHERE ID in
        <foreach item="item" collection="ids" open="(" close=")" separator="," index="">
            #{item}
        </foreach>
    </update>

    <update id="updateBizKnowledgeId" parameterType="map">
        UPDATE Pilot_ConsultantKnowledge
        SET
        BizKnowledgeId = #{bizKnowledgeId},
        UpdateTime = now()
        WHERE ID = #{id}
    </update>

    <select id="rangeFindById" parameterType="map" resultMap="BaseMap">
        SELECT
        <include refid="selectKeys"/>
        FROM Pilot_ConsultantKnowledge
        WHERE ID BETWEEN #{minId} AND #{maxId}
        AND Status > 0
        ORDER BY ID
        LIMIT 200
    </select>

    <select id="findByBackCategoryId" parameterType="map" resultMap="BaseMap">
        SELECT
        <include refid="selectKeys"/>
        FROM Pilot_ConsultantKnowledge
        WHERE BackCategoryID = #{backCategoryId}
        AND ImMerchantID = #{imMerchantId}
        AND Status > 0
        ORDER BY ID
        LIMIT 200
    </select>

    <select id="findByBackCategoryAndType" parameterType="map" resultMap="BaseMap">
        SELECT
        <include refid="selectKeys"/>
        FROM Pilot_ConsultantKnowledge
        WHERE BackCategoryID = #{backCategoryId}
        AND KnowledgeBaseType = #{knowledgeBaseType}
        AND Status > 0
        ORDER BY ID
        LIMIT 200
    </select>


    <select id="findSameCategoryKnowledge" parameterType="map" resultMap="BaseMap">
        SELECT
        <include refid="selectKeys"/>
        FROM Pilot_ConsultantKnowledge
        WHERE ForeCategoryID = #{foreCategoryId}
        AND BackCategoryID = #{backCategoryId}
        AND ImMerchantID = #{imMerchantId}
        AND Status > 0
        ORDER BY ID
        LIMIT 200
    </select>

    <select id="findSameCategoryKnowledgeByType" parameterType="map" resultMap="BaseMap">
        SELECT
        <include refid="selectKeys"/>
        FROM Pilot_ConsultantKnowledge
        WHERE ForeCategoryID = #{foreCategoryId}
        AND BackCategoryID = #{backCategoryId}
        AND KnowledgeBaseType = #{knowledgeBaseType}
        AND Status > 0
        ORDER BY ID
        LIMIT 200
    </select>

    <update id = "updateDzDisableStatus" parameterType="map">
        UPDATE Pilot_ConsultantKnowledge
        SET
        DzRecommendDisable = #{dzRecommendDisable},
        LastEditor = #{lastEditor},
        UpdateTime = now()
        WHERE ID in
        <foreach item="item" collection="ids" open="(" close=")" separator="," index="">
            #{item}
        </foreach>
    </update>
</mapper>