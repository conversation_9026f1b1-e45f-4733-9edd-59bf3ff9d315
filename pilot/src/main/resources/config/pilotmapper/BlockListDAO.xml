<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sankuai.dzim.pilot.dal.pilotdao.BlockListDAO">
    <resultMap id="BaseMap" type="com.sankuai.dzim.pilot.dal.entity.pilot.BlockListEntity">
        <result column="ID" property="id"/>
        <result column="UserID" property="userId"/>
        <result column="BlockedCnt" property="blockedCnt"/>
        <result column="LastBlockedTime" property="lastBlockedTime"/>
        <result column="BlockType" property="blockType"/>
        <result column="AssistantType" property="assistantType"/>
        <result column="BlockedStatus" property="blockedStatus"/>
        <result column="AddTime" property="addTime"/>
        <result column="UpdateTime" property="updateTime"/>
    </resultMap>

    <sql id="selectKeys">
        `ID`,
        `UserID`,
        `BlockedCnt`,
        `LastBlockedTime`,
        `BlockType`,
        `AssistantType`,
        `BlockedStatus`,
        `UpdateTime`,
        `AddTime`
    </sql>

    <select id="selectByIds" parameterType="map" resultMap="BaseMap">
        SELECT
        <include refid="selectKeys"/>
        FROM Pilot_BlockList
        WHERE UserID in
        <foreach collection="userIds" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>


    <select id="selectUserByIdAndAssistantType" parameterType="map" resultMap="BaseMap">
            SELECT
            <include refid="selectKeys"/>
            FROM Pilot_BlockList
            WHERE UserID = #{userId}
            AND AssistantType = #{assistantType}
    </select>


    <insert id="insert" parameterType="map" keyProperty="entity.id" useGeneratedKeys="true">
        INSERT INTO Pilot_BlockList
        (UserID, BlockedCnt, LastBlockedTime, BlockType, AssistantType, BlockedStatus, AddTime, UpdateTime)
        VALUES (#{entity.userId},
                #{entity.blockedCnt},
                now(),
                #{entity.blockType},
                #{entity.assistantType},
                #{entity.blockedStatus},
                now(),
                now())
    </insert>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO Pilot_BlockList
        (UserID, BlockedCnt, LastBlockedTime, BlockType, AssistantType, BlockedStatus, AddTime, UpdateTime)
        VALUES
        <foreach collection="entities" item="entity" separator=",">
            (
            #{entity.userId},
            #{entity.blockedCnt},
            now(),
            #{entity.blockType},
            #{entity.assistantType},
            #{entity.blockedStatus},
            now(),
            now()
            )
        </foreach>
    </insert>


    <update id="update" parameterType="map">
        UPDATE Pilot_BlockList
        SET
        <if test="entity.blockType != null">
            BlockType = #{entity.blockType},
        </if>
        <if test="entity.blockedStatus != null">
            BlockedStatus = #{entity.blockedStatus},
            LastBlockedTime = UpdateTime,
        </if>
        <if test="entity.blockedCnt != null">
            BlockedCnt = #{entity.blockedCnt},
        </if>
        UpdateTime = now()
        WHERE UserID = #{entity.userId}
    </update>

    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="entities" item="entity" index="index" separator=";">
            UPDATE Pilot_BlockList
            <set>
                <if test="entity.blockType != null">
                    BlockType = #{entity.blockType},
                </if>
                <if test="entity.blockedStatus != null">
                    BlockedStatus = #{entity.blockedStatus},
                    LastBlockedTime = UpdateTime,
                </if>
                <if test="entity.blockedCnt != null">
                    BlockedCnt = #{entity.blockedCnt},
                </if>
                UpdateTime = now()
            </set>
            WHERE UserID = #{entity.userId}
        </foreach>
    </update>


</mapper>

