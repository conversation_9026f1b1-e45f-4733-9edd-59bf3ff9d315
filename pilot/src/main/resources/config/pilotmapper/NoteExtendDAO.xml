<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sankuai.dzim.pilot.dal.pilotdao.NoteExtendDAO">
    <resultMap id="BaseMap" type="com.sankuai.dzim.pilot.dal.entity.NoteExtendInfoEntity">
        <result column="ID" property="id"/>
        <result column="NoteID" property="noteId"/>
        <result column="PicArray" property="picArray"/>
        <result column="PicDesc" property="picDesc"/>
        <result column="AddTime" property="addTime"/>
        <result column="UpdateTime" property="updateTime"/>
    </resultMap>

    <sql id="selectKeys">
        `ID`,
        `NoteID`,
        `PicArray`,
        `PicDesc`,
        `AddTime`,
        `UpdateTime`
    </sql>

    <select id="batchFindByNoteIds" parameterType="map" resultMap="BaseMap">
        SELECT
        <include refid="selectKeys">
        </include>
        FROM Pilot_NoteExtend
        where NoteID IN
        <foreach collection="noteIds" item="noteId" separator="," open="(" close=")">
            #{noteId}
        </foreach>
        LIMIT 100
    </select>

    <update id="updatePicDesc" parameterType="map">
        UPDATE Pilot_NoteExtend
        SET
        <if test="entity.picDesc != null and entity.picDesc != ''">
            PicDesc = #{entity.picDesc},
        </if>
        UpdateTime = now()
        WHERE ID = #{entity.id}
    </update>
</mapper>

