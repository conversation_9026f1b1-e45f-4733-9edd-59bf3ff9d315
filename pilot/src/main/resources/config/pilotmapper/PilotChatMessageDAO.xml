<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sankuai.dzim.pilot.dal.pilotdao.PilotChatGroupMessageDAO">
    <resultMap id="BaseMap" type="com.sankuai.dzim.pilot.dal.entity.pilot.PilotChatMessageEntity">
        <result column="ID" property="id"/>
        <result column="MessageID" property="messageId"/>
        <result column="UserID" property="userId"/>
        <result column="ChatGroupID" property="chatGroupId"/>
        <result column="AssistantType" property="assistantType"/>
        <result column="Message" property="message"/>
        <result column="MessageType" property="messageType"/>
        <result column="Direction" property="direction"/>
        <result column="Creator" property="creator"/>
        <result column="AuditStatus" property="auditStatus"/>
        <result column="ExtraData" property="extraData"/>
        <result column="AddTime" property="addTime"/>
        <result column="UpdateTime" property="updateTime"/>
    </resultMap>

    <sql id="selectKeys">
        `ID`,
        `MessageID`,
        `UserID`,
        `ChatGroupID`,
        `AssistantType`,
        `Message`,
        `MessageType`,
        `Direction`,
        `Creator`,
        `AuditStatus`,
        `ExtraData`,
        `AddTime`,
        `UpdateTime`
    </sql>

    <select id="queryMessageByMessageId" parameterType="long" resultMap="BaseMap">
        SELECT
            <include refid="selectKeys"/>
        FROM Pilot_ChatMessage
        WHERE MessageID=#{messageId} AND AuditStatus = 0
    </select>


    <select id="queryMessageByMessageIds" parameterType="long" resultMap="BaseMap">
        SELECT
        <include refid="selectKeys"/>
        FROM Pilot_ChatMessage
        WHERE MessageID in
        <foreach collection="messageIds" item="messageId" separator="," open="(" close=")">
            #{messageId}
        </foreach>
        AND AuditStatus = 0
    </select>

    <insert id="insertMessage" parameterType="map" keyProperty="entity.id" useGeneratedKeys="true">
        INSERT INTO Pilot_ChatMessage
        (`MessageID`, `UserID`, `ChatGroupID`,`AssistantType`, `Message`, `MessageType`, `Direction`, `Creator`, `AuditStatus`,
         `ExtraData`, `AddTime`, `UpdateTime`)
        VALUES (#{entity.messageId},
                #{entity.userId},
                #{entity.chatGroupId},
                #{entity.assistantType},
                #{entity.message},
                #{entity.messageType},
                #{entity.direction},
                #{entity.creator},
                #{entity.auditStatus},
                #{entity.extraData},
                now(),
                now())
    </insert>


    <select id="multiGetLatestMessages" parameterType="map" resultMap="BaseMap">
        SELECT
            <include refid="selectKeys"/>
        FROM Pilot_ChatMessage
        WHERE ChatGroupId = #{chatGroupId} AND AuditStatus = 0
        ORDER BY ID DESC
        LIMIT #{limit}
    </select>

    <select id="multiGetLatestMessagesBeforeMessageId" parameterType="map" resultMap="BaseMap">
        SELECT
        <include refid="selectKeys"/>
        FROM Pilot_ChatMessage
        WHERE ChatGroupId = #{chatGroupId} AND AuditStatus = 0
        AND <![CDATA[ ID < #{lastId} ]]>
        ORDER BY ID DESC
        LIMIT #{limit}
    </select>
    <select id="multiGetLatestMessagesFilterByDirection" parameterType="map" resultMap="BaseMap">
        SELECT
        <include refid="selectKeys"/>
        FROM Pilot_ChatMessage
        WHERE ChatGroupId = #{chatGroupId} AND AuditStatus = 0
        AND Direction NOT IN
        <foreach collection="filterDirections" item="direction" open="(" separator="," close=")">
            #{direction}
        </foreach>
        ORDER BY ID DESC
        LIMIT #{limit}
    </select>
    <select id="multiGetLatestMessagesBeforeMessageIdFilterByDirection" parameterType="map" resultMap="BaseMap">
        SELECT
        <include refid="selectKeys"/>
        FROM Pilot_ChatMessage
        WHERE ChatGroupId = #{chatGroupId} AND AuditStatus = 0
        AND Direction NOT IN
        <foreach collection="filterDirections" item="direction" open="(" separator="," close=")">
            #{direction}
        </foreach>
        AND messageId &lt; #{lastMessageId}
        ORDER BY ID DESC
        LIMIT #{limit}
    </select>

    <update id="updateMessageContentAndExtra" parameterType="map">
        UPDATE Pilot_ChatMessage
        SET `ExtraData`= #{extraData},
            `Message` = #{messageContent},
            `UpdateTime` = now()
        WHERE MessageID = #{messageId}
    </update>

    <update id="updateMessageContent" parameterType="map">
        UPDATE Pilot_ChatMessage
        SET `Message` = #{messageContent},
            `UpdateTime` = now()
        WHERE MessageID = #{messageId}
    </update>

    <update id="updateMessage" parameterType="map" keyProperty="entity.id">
        UPDATE Pilot_ChatMessage
        SET
            `Message` = #{entity.message},
            `AuditStatus` = #{entity.auditStatus},
            `ExtraData` = #{entity.extraData},
            `UpdateTime` = now()
        WHERE MessageID = #{entity.messageId}
    </update>
</mapper>

