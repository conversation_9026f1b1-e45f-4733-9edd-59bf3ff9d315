<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sankuai.dzim.pilot.dal.pilotdao.aicall.AICallDetailDAO">
    <resultMap id="BaseMap" type="com.sankuai.dzim.pilot.dal.entity.aicall.AICallDetailEntity">
        <result column="ID" property="id"/>
        <result column="TaskID" property="taskId"/>
        <result column="ShopId" property="shopId"/>
        <result column="Platform" property="platform"/>
        <result column="ShopPhone" property="shopPhone"/>
        <result column="CallStatus" property="callStatus"/>
        <result column="SceneCode" property="sceneCode"/>
        <result column="ActivityType" property="activityType"/>
        <result column="CallStartTime" property="callStartTime"/>
        <result column="CallbackTime" property="callbackTime"/>
        <result column="ExtraData" property="extraData"/>
        <result column="Status" property="status"/>
        <result column="AddTime" property="addTime"/>
        <result column="UpdateTime" property="updateTime"/>
    </resultMap>

    <sql id="selectKeys">
        `ID`,
        `TaskId`,
        `ShopId`,
        `Platform`,
        `ShopPhone`,
        `CallStatus`,
        `SceneCode`,
        `ActivityType`,
        `CallStartTime`,
        `CallbackTime`,
        `ExtraData`,
        `Status`,
        `AddTime`,
        `UpdateTime`
    </sql>


    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO AI_Call_Detail
        (TaskID, ShopId, Platform, ShopPhone, CallStatus, SceneCode, ActivityType, CallStartTime, CallbackTime, ExtraData, Status, AddTime, UpdateTime)
        VALUES
        <foreach collection="entities" item="entity" separator=",">
            (
            #{entity.taskId},
            #{entity.shopId},
            #{entity.platform},
            #{entity.shopPhone},
            #{entity.callStatus},
            #{entity.sceneCode},
            #{entity.activityType},
            #{entity.callStartTime},
            #{entity.callbackTime},
            #{entity.extraData},
            #{entity.status},
            now(),
            now()
            )
        </foreach>
    </insert>

    <select id="getByTaskId" parameterType="map" resultMap="BaseMap">
        SELECT
        <include refid="selectKeys"/>
        FROM AI_Call_Detail
        WHERE TaskId = #{taskId}
        AND Status = 1
        ORDER BY ID
    </select>

    <select id="getById" parameterType="map" resultMap="BaseMap">
        SELECT
        <include refid="selectKeys"/>
        FROM AI_Call_Detail
        WHERE ID = #{id}
        AND Status = 1
    </select>


    <update id="updateCallResult" parameterType="map">
        UPDATE AI_Call_Detail
        SET `CallStatus`= #{callStatus},
            `UpdateTime` = now()
        WHERE id =  #{id}
    </update>


    <update id="updateCallTime" parameterType="map">
        UPDATE AI_Call_Detail
        SET `CallStartTime` = #{callStartTime},
            `ExtraData`= #{extraData},
            `shopPhone` = #{shopPhone},
            `UpdateTime` = now()
        WHERE id =  #{id}
    </update>

    <update id="updateCallback" parameterType="map">
        UPDATE AI_Call_Detail
        SET `CallStatus` = #{callStatus},
            `ExtraData`= #{extraData},
            `CallbackTime` = #{callbackTime},
            `UpdateTime` = now()
        WHERE id =  #{id}
    </update>


    <select id="queryByShopId" resultMap="BaseMap" parameterType="map">
        SELECT <include refid="selectKeys"/>
        FROM AI_Call_Detail
        WHERE TaskID = #{taskId} AND ShopId = #{shopId} AND Platform = #{platform} LIMIT 1
    </select>


    <update id="batchUpdateCallResultAndExtra" parameterType="java.util.List">
        <foreach collection="entities" item="item" separator=";">
            UPDATE AI_Call_Detail
            <set>
                `CallStatus` = #{item.callStatus},
                <if test="item.extraData != null">
                    `ExtraData` = #{item.extraData},
                </if>
                `UpdateTime` = now()
            </set>
            WHERE id = #{item.id}
        </foreach>
    </update>


    <select id="findById" resultMap="BaseMap" parameterType="map">
        SELECT <include refid="selectKeys"/>
        FROM AI_Call_Detail
        WHERE ID = #{id}
        AND Status = 1
        LIMIT 1
    </select>


    <select id="findSuccessBookByTaskId" resultMap="BaseMap">
        SELECT <include refid="selectKeys"/>
        FROM AI_Call_Detail
        WHERE TaskID = #{taskId}
        AND CallStatus = 1
        LIMIT 1
    </select>

    <select id="queryByTaskIdsAndCallResults" resultMap="BaseMap">
        SELECT
        <include refid="selectKeys"/>
        FROM AI_Call_Detail
        WHERE TaskID in
        <foreach item="taskId" collection="taskIds" open="(" close=")" separator="," index="">
            #{taskId}
        </foreach>
        AND CallStatus in
        <foreach item="callResult" collection="callResults" open="(" close=")" separator="," index="">
            #{callStatus}
        </foreach>
        AND Status = 1
    </select>

    <select id="queryTodayCallResult" resultMap="BaseMap" parameterType="map">
        SELECT <include refid="selectKeys"/>
        FROM AI_Call_Detail
        WHERE ShopId = #{shopId}
        AND Platform = #{platform}
        AND DATE(CallbackTime) = CURDATE()
        AND Status = 1
    </select>


    <update id="updateExtraData" parameterType="map">
        UPDATE AI_Call_Detail
        SET `ExtraData`= #{extraData},
            `UpdateTime` = now()
        WHERE id =  #{id}
    </update>

    <select id="queryMultiDaysByShopId" resultMap="BaseMap" parameterType="map">
        SELECT <include refid="selectKeys"/>
        FROM AI_Call_Detail
        WHERE ShopId = #{shopId}
        AND Platform = #{platform}
        AND Status = 1
        Limit 10
    </select>

</mapper>

