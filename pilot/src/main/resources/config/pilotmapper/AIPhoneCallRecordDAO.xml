<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sankuai.dzim.pilot.dal.pilotdao.aiphonecall.AIPhoneCallRecordDAO">
    <resultMap id="BaseMap" type="com.sankuai.dzim.pilot.dal.entity.aiphonecall.AIPhoneCallRecordEntity">
        <result column="ContactType" property="contactType"/>
        <result column="TenantID" property="tenantId"/>
        <result column="ContactID" property="contactId"/>
        <result column="DisplayNum" property="displayNum"/>
        <result column="OriDnis" property="oriDnis"/>
        <result column="RingStartTime" property="ringStartTime"/>
        <result column="RingTimeLen" property="ringTimeLen"/>
        <result column="TalkingStartTime" property="talkingStartTime"/>
        <result column="TalkingTimeLen" property="talkingTimeLen"/>
        <result column="DialogRawData" property="dialogRawData"/>
        <result column="ReleaseReason" property="releaseReason"/>
        <result column="StartTime" property="startTime"/>
        <result column="EndTime" property="endTime"/>
        <result column="AudioUrl" property="audioUrl"/>
        <result column="AppCallUuid" property="appCallUuid"/>
        <result column="Customer" property="customer"/>
        <result column="KeyPressList" property="keyPressList"/>
        <result column="OntologyVersion" property="ontologyVersion"/>
        <result column="AddTime" property="addTime"/>
        <result column="UpdateTime" property="updateTime"/>
    </resultMap>

    <sql id="selectKeys">
        `ContactType`,
        `TenantID`,
        `ContactID`,
        `DisplayNum`,
        `OriDnis`,
        `RingStartTime`,
        `RingTimeLen`,
        `TalkingStartTime`,
        `TalkingTimeLen`,
        `DialogRawData`,
        `ReleaseReason`,
        `StartTime`,
        `EndTime`,
        `AudioUrl`,
        `AppCallUuid`,
        `Customer`,
        `KeyPressList`,
        `OntologyVersion`,
        `AddTime`,
        `UpdateTime`
    </sql>

    <select id="getByContactId" parameterType="map" resultMap="BaseMap">
        SELECT
        <include refid="selectKeys"/>
        FROM AI_Phone_Call_Record
        WHERE ContactID = #{contactId}
    </select>

    <insert id="insert" parameterType="com.sankuai.dzim.pilot.dal.entity.aiphonecall.AIPhoneCallRecordEntity">
        INSERT INTO AI_Phone_Call_Record
        (
            `ContactType`,
            `TenantID`,
            `ContactID`,
            `DisplayNum`,
            `OriDnis`,
            `RingStartTime`,
            `RingTimeLen`,
            `TalkingStartTime`,
            `TalkingTimeLen`,
            `DialogRawData`,
            `ReleaseReason`,
            `StartTime`,
            `EndTime`,
            `AudioUrl`,
            `AppCallUuid`,
            `Customer`,
            `KeyPressList`,
            `OntologyVersion`,
            `AddTime`,
            `UpdateTime`
        )
        VALUES
        (
            #{contactType},
            #{tenantId},
            #{contactId},
            #{displayNum},
            #{oriDnis},
            #{ringStartTime},
            #{ringTimeLen},
            #{talkingStartTime},
            #{talkingTimeLen},
            #{dialogRawData},
            #{releaseReason},
            #{startTime},
            #{endTime},
            #{audioUrl},
            #{appCallUuid},
            #{customer},
            #{keyPressList},
            #{ontologyVersion},
            NOW(),
            NOW()
        )
    </insert>

</mapper>

