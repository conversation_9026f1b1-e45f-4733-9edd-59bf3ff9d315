<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sankuai.dzim.pilot.dal.pilotdao.ReviewSyncDAO">
    <resultMap id="BaseMap" type="com.sankuai.dzim.pilot.dal.entity.pilot.ReviewSyncEntity">
        <result column="ID" property="id"/>
        <result column="ReviewID" property="reviewId"/>
        <result column="MtShopID" property="mtShopId"/>
        <result column="DealID" property="dealId"/>
        <result column="TechnicianID" property="technicianId"/>
        <result column="VectorID" property="vectorId"/>
        <result column="Title" property="title"/>
        <result column="Content" property="content"/>
        <result column="LikeCnt" property="likeCnt"/>
        <result column="Star" property="star"/>
        <result column="MtCityID" property="mtCityId"/>
        <result column="CreateTime" property="createTime"/>
        <result column="Cat0ID" property="cat0Id"/>
        <result column="Cat1ID" property="cat1Id"/>
        <result column="IsPithy" property="isPithy"/>
        <result column="PicArray" property="picArray"/>
        <result column="IsDelete" property="isDelete"/>
        <result column="AddTime" property="addTime"/>
        <result column="UpdateTime" property="updateTime"/>
    </resultMap>

    <sql id="selectKeys">
        `ID`,
        `ReviewID`,
        `MtShopID`,
        `DealID`,
        `TechnicianID`,
        `VectorID`,
        `Title`,
        `Content`,
        `LikeCnt`,
        `Star`,
        `MtCityID`,
        `CreateTime`,
        `Cat0ID`,
        `Cat1ID`,
        `IsPithy`,
        `PicArray`,
        `IsDelete`,
        `AddTime`,
        `UpdateTime`
    </sql>

    <insert id="insert" parameterType="map" keyProperty="entity.id" useGeneratedKeys="true">
        INSERT INTO Pilot_ReviewSync
            (ReviewID, MtShopID, DealID, TechnicianID, VectorID, Title, Content, LikeCnt, Star, MtCityID, CreateTime, Cat0ID, Cat1ID, IsPithy, PicArray, IsDelete, AddTime, UpdateTime)
        VALUES (#{entity.reviewId},
                #{entity.mtShopId},
                #{entity.dealId},
                #{entity.technicianId},
                #{entity.vectorId},
                #{entity.title},
                #{entity.content},
                #{entity.likeCnt},
                #{entity.star},
                #{entity.mtCityId},
                #{entity.createTime},
                #{entity.cat0Id},
                #{entity.cat1Id},
                #{entity.isPithy},
                #{entity.picArray},
                0,
                now(),
                now())
    </insert>

    <update id="delete" parameterType="map">
        UPDATE Pilot_ReviewSync
        SET
        IsDelete = 1
        WHERE ID = #{id}
    </update>

    <select id="findByReviewId" parameterType="map" resultMap="BaseMap">
        SELECT
        <include refid="selectKeys">
        </include>
        FROM Pilot_ReviewSync
        WHERE ReviewID = #{reviewId}
        AND IsDelete = 0
        LIMIT 1
    </select>

    <select id="findByVectorId" parameterType="map" resultMap="BaseMap">
        SELECT
        <include refid="selectKeys">
        </include>
        FROM Pilot_ReviewSync
        where VectorID IN
        <foreach collection="vectorIds" item="vectorId" separator="," open="(" close=")">
            #{vectorId}
        </foreach>
        AND IsDelete=0
        LIMIT 1000
    </select>

    <select id="countByShopId" parameterType="map" resultType="int">
        SELECT COUNT(`ReviewID`)
        FROM Pilot_ReviewSync
        WHERE MtShopID = #{mtShopId}
        AND IsDelete = 0
    </select>

    <select id="batchFindByTechnicianIds" parameterType="map" resultMap="BaseMap">
        SELECT
        <include refid="selectKeys">
        </include>
        FROM Pilot_ReviewSync
        where TechnicianID IN
        <foreach collection="technicianIds" item="technicianId" separator="," open="(" close=")">
            #{technicianId}
        </foreach>
        AND IsDelete=0
        LIMIT 1000
    </select>
    <select id="queryMtShopIDFilterByCount" resultType="long">
        SELECT MtShopID
        FROM Pilot_ReviewSync WHERE IsDelete = 0
        GROUP BY MtShopID
        HAVING COUNT(ReviewID) > #{threshold}
    </select>

</mapper>

