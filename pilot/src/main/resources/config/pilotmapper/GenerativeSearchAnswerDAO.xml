<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sankuai.dzim.pilot.dal.pilotdao.GenerativeSearchAnswerDAO">
    <resultMap id="BaseMap" type="com.sankuai.dzim.pilot.dal.entity.GenerativeSearchAnswerEntity">
        <result column="ID" property="id"/>
        <result column="Question" property="question"/>
        <result column="QuestionType" property="questionType"/>
        <result column="BizType" property="bizType"/>
        <result column="Answer" property="answer"/>
        <result column="RelatedQuestion" property="relatedQuestion"/>
        <result column="Status" property="status"/>
        <result column="AddTime" property="addTime"/>
        <result column="UpdateTime" property="updateTime"/>
        <result column="TemplateData" property="templateData"/>
    </resultMap>

    <sql id="selectKeys">
        `ID`,
        `Question`,
        `QuestionType`,
        `BizType`,
        `Answer`,
        `RelatedQuestion`,
        `Status`,
        `AddTime`,
        `UpdateTime`,
        `TemplateData`
    </sql>

    <insert id="insert" parameterType="map" keyProperty="entity.id" useGeneratedKeys="true">
        INSERT INTO Pilot_GenerativeSearchAnswer
        (`Question`, `QuestionType`, `BizType`, `Answer`, `RelatedQuestion`, `Status`, `AddTime`, `UpdateTime`, `TemplateData`)
        VALUES (#{entity.question},
                #{entity.questionType},
                #{entity.bizType},
                #{entity.answer},
                #{entity.relatedQuestion},
                #{entity.status},
                now(),
                now(),
               #{entity.templateData})
    </insert>

    <select id="selectById" parameterType="long" resultMap="BaseMap">
        SELECT
        <include refid="selectKeys"/>
        FROM Pilot_GenerativeSearchAnswer
        WHERE ID = #{id}
    </select>

    <select id="rangeFindById" parameterType="map" resultMap="BaseMap">
        SELECT
        <include refid="selectKeys"/>
        FROM Pilot_GenerativeSearchAnswer
        WHERE ID BETWEEN #{minId} AND #{maxId}
        AND Status = 1
        LIMIT 500
    </select>

    <select id="selectByQuestion" parameterType="map" resultMap="BaseMap">
        SELECT
        <include refid="selectKeys"/>
        FROM Pilot_GenerativeSearchAnswer
        WHERE Question = #{question}
        AND BizType = #{bizType}
        AND Status = 1
        ORDER BY ID DESC
        LIMIT 1
    </select>

    <select id="batchSelectByQuestion" parameterType="map" resultMap="BaseMap">
        SELECT
        <include refid="selectKeys"/>
        FROM Pilot_GenerativeSearchAnswer
        WHERE Status = 1
        AND BizType = #{bizType}
        AND Question IN
        <foreach collection="questionList" item="question" separator="," open="(" close=")">
            #{question}
        </foreach>
    </select>

    <update id="update" parameterType="map">
        UPDATE Pilot_GenerativeSearchAnswer
        SET
        <if test="entity.question != null and entity.question != ''">
            Question = #{entity.question},
        </if>
        <if test="entity.templateData != null">
            TemplateData = #{entity.templateData},
        </if>
            Answer = #{entity.answer},
            RelatedQuestion = #{entity.relatedQuestion}
        WHERE ID = #{entity.id}
    </update>

    <update id="delete" parameterType="map">
        UPDATE Pilot_GenerativeSearchAnswer
        SET Status = 0
        WHERE ID IN
        <foreach collection="idList" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </update>

    <select id="countByQuestion" parameterType="map" resultType="java.lang.Integer">
        SELECT
        COUNt(ID)
        FROM Pilot_GenerativeSearchAnswer
        WHERE Status != 0
        AND Question = #{question}
        AND BizType = #{bizType}
    </select>
    <select id="selectAllByQuestion" parameterType="map" resultMap="BaseMap">
        SELECT
        <include refid="selectKeys"/>
        FROM IM_GenerativeSearchAnswer
        WHERE Question = #{question}
        AND BizType = #{bizType}
        AND Status = 1
    </select>
    <select id="countByBizType" parameterType="map" resultType="java.lang.Long">
        SELECT
        COUNT(ID)
        FROM Pilot_GenerativeSearchAnswer
        WHERE BizType = #{bizType}
        AND Status = 1
    </select>
    <select id="pageSelectByBizType" parameterType="map" resultMap="BaseMap">
        SELECT
        <include refid="selectKeys"/>
        FROM Pilot_GenerativeSearchAnswer
        WHERE ID > #{startId}
        AND BizType = #{bizType}
        AND Status = 1
        ORDER BY ID
        LIMIT #{pageSize}
    </select>

    <update id="deleteBaiBuConflict" parameterType="map">
        UPDATE Pilot_GenerativeSearchAnswer
        SET Status = #{status}
        WHERE ID IN
        <foreach collection="idList" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </update>
</mapper>

