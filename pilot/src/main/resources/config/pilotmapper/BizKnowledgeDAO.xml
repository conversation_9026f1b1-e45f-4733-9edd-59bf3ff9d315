<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sankuai.dzim.pilot.dal.pilotdao.BizKnowledgeDAO">
    <resultMap id="BaseMap" type="com.sankuai.dzim.pilot.dal.entity.BizKnowledgeEntity">
        <result column="ID" property="id"/>
        <result column="BizType" property="bizType"/>
        <result column="VectorID" property="vectorId"/>
        <result column="Question" property="question"/>
        <result column="Answer" property="answer"/>
        <result column="Editor" property="editor"/>
        <result column="AddTime" property="addTime"/>
        <result column="UpdateTime" property="updateTime"/>
        <result column="IsDelete" property="isDelete"/>
    </resultMap>

    <sql id="selectKeys">
        `ID`,
        `BizType`,
        `VectorID`,
        `Question`,
        `Answer`,
        `Editor`,
        `AddTime`,
        `UpdateTime`,
        `AddTime`,
        `IsDelete`,
        `Meta`
    </sql>

    <insert id="insert" parameterType="map" keyProperty="entity.id" useGeneratedKeys="true">
        INSERT INTO Pilot_BizKnowledge
        (BizType, VectorID, Question, Answer, Editor, Meta, AddTime, UpdateTime)
        VALUES (#{entity.bizType},
                #{entity.vectorId},
                #{entity.question},
                #{entity.answer},
                #{entity.editor},
                #{entity.meta},
                now(),
                now())
    </insert>

    <select id="batchFindByVectorIds" parameterType="map" resultMap="BaseMap">
        SELECT
        <include refid="selectKeys">
        </include>
        FROM Pilot_BizKnowledge
        where VectorID IN
        <foreach collection="vectorIds" item="vectorId" separator="," open="(" close=")">
            #{vectorId}
        </foreach>
        AND IsDelete=0
    </select>


    <select id="batchFindByIds" parameterType="map" resultMap="BaseMap">
        SELECT
        <include refid="selectKeys">
        </include>
        FROM Pilot_BizKnowledge
        where ID IN
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
        AND IsDelete=0
    </select>

    <update id="batchDeleteByVectorIds" parameterType="map">
        UPDATE Pilot_BizKnowledge
        SET
        IsDelete = 1,
        Editor = #{editor}
        WHERE VectorID IN
        <foreach item="vectorId" collection="vectorIds" separator="," open="(" close=")" index="">
            #{vectorId}
        </foreach>
    </update>
</mapper>

