<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sankuai.dzim.pilot.dal.pilotdao.PilotChatMessageBizRefDAO">
    <resultMap id="BaseMap" type="com.sankuai.dzim.pilot.dal.entity.pilot.PilotChatMessageBizRefEntity">
        <result column="ID" property="id"/>
        <result column="MessageID" property="messageId"/>
        <result column="BizType" property="bizType"/>
        <result column="BizID" property="bizId"/>
        <result column="BizStatus" property="bizStatus"/>
        <result column="BizExtra" property="bizExtra"/>
        <result column="AddTime" property="addTime"/>
        <result column="UpdateTime" property="updateTime"/>
    </resultMap>

    <sql id="selectKeys">
        `ID`,
        `MessageID`,
        `BizType`,
        `BizID`,
        `BizStatus`,
        `BizExtra`,
        `AddTime`,
        `UpdateTime`
    </sql>

    <select id="queryById" parameterType="map" resultMap="BaseMap">
        SELECT <include refid="selectKeys"/>
        FROM Pilot_ChatMessageBizRef
        WHERE ID = #{id}
    </select>

    <select id="queryByMessageId" parameterType="long" resultMap="BaseMap">
        SELECT <include refid="selectKeys"/>
        FROM Pilot_ChatMessageBizRef
        WHERE MessageID = #{messageId}
    </select>

    <select id="queryByBizTypeAndBizId" parameterType="map" resultMap="BaseMap">
        SELECT <include refid="selectKeys"/>
        FROM Pilot_ChatMessageBizRef
        WHERE BizType = #{bizType} AND BizID = #{bizId} AND BizStatus = #{bizStatus}
    </select>

    <insert id="insert" parameterType="map" keyProperty="entity.id" useGeneratedKeys="true">
        INSERT INTO Pilot_ChatMessageBizRef
        (`MessageID`,
         `BizType`,
         `BizID`,
         `BizStatus`,
         `BizExtra`,
         `AddTime`,
         `UpdateTime`)
        VALUES (
                   #{entity.messageId},
                   #{entity.bizType},
                   #{entity.bizId},
                   #{entity.bizStatus},
                   #{entity.bizExtra},
                   now(),
                   now()
               )
    </insert>

    <update id="update" parameterType="map">
        UPDATE Pilot_ChatMessageBizRef
        SET
        <if test="entity.messageId != null">
            MessageID = #{entity.messageId},
        </if>
        <if test="entity.bizType != null">
            BizType = #{entity.bizType},
        </if>
        <if test="entity.bizId != null">
            BizID = #{entity.bizId},
        </if>
        <if test="entity.bizStatus != null">
            BizStatus = #{entity.bizStatus},
        </if>
        <if test="entity.bizExtra != null">
            BizExtra = #{entity.bizExtra},
        </if>
        UpdateTime = now()
        WHERE ID = #{entity.id}
    </update>
</mapper>
