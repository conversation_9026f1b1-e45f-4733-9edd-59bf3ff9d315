<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sankuai.dzim.pilot.dal.dao.MerchantKnowledgeDAO">
    <resultMap id="BaseMap" type="com.sankuai.dzim.pilot.dal.entity.MerchantKnowledgeEntity">
        <result column="ID" property="id"/>
        <result column="Type" property="type"/>
        <result column="SubjectID" property="subjectId"/>
        <result column="VectorID" property="vectorId"/>
        <result column="Question" property="question"/>
        <result column="Answer" property="answer"/>
        <result column="Editor" property="editor"/>
        <result column="AddTime" property="addTime"/>
        <result column="UpdateTime" property="updateTime"/>
        <result column="IsDelete" property="isDelete"/>
    </resultMap>

    <sql id="selectKeys">
        `ID`,
        `Type`,
        `SubjectID`,
        `VectorID`,
        `Question`,
        `Answer`,
        `Editor`,
        `AddTime`,
        `UpdateTime`,
        `AddTime`,
        `IsDelete`
    </sql>

    <insert id="insert" parameterType="map" keyProperty="entity.id" useGeneratedKeys="true">
        INSERT INTO IM_MerchantKnowledge
            (`Type`, SubjectID, VectorID, Question, Answer, Editor, AddTime, UpdateTime)
        VALUES (#{entity.type},
                #{entity.subjectId},
                #{entity.vectorId},
                #{entity.question},
                #{entity.answer},
                #{entity.editor},
                now(),
                now())
    </insert>

    <select id="batchFindByVectorIds" parameterType="map" resultMap="BaseMap">
        SELECT
        <include refid="selectKeys">
        </include>
        FROM IM_MerchantKnowledge
        where VectorID IN
        <foreach collection="vectorIds" item="vectorId" separator="," open="(" close=")">
            #{vectorId}
        </foreach>
        AND IsDelete=0
    </select>

    <select id="batchFindByIds" parameterType="map" resultMap="BaseMap">
        SELECT
        <include refid="selectKeys">
        </include>
        FROM IM_MerchantKnowledge
        where ID IN
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
        AND IsDelete=0
    </select>

    <update id="batchDeleteByVectorIds" parameterType="map">
        UPDATE IM_MerchantKnowledge
        SET
        IsDelete = 1,
        Editor = #{editor}
        WHERE VectorID IN
        <foreach item="vectorId" collection="vectorIds" separator="," open="(" close=")" index="">
            #{vectorId}
        </foreach>
    </update>
</mapper>

