<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
                            http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean id="delayUserAwakeProducer" class="com.meituan.mafka.client.bean.MafkaProducer" init-method="start"
          destroy-method="close">
        <property name="namespace" value="daozong"/>
        <property name="appkey" value="com.sankuai.mim.pilot"/>
        <property name="topic" value="im.inner.user.awake"/>
        <property name="producerType" value="delayProducer"/>
        <property name="openHook" value="false"/>
    </bean>

    <bean id="medicalCheckupReportProducer" class="com.meituan.mafka.client.bean.MafkaProducer" init-method="start"
          destroy-method="close">
        <property name="namespace" value="daozong"/>
        <property name="appkey" value="com.sankuai.mim.pilot"/>
        <property name="topic" value="pilot.medical.checkup.event.notify"/>
    </bean>

    <bean id="sendMessageProducer" class="com.meituan.mafka.client.bean.MafkaProducer" init-method="start"
          destroy-method="close">
        <property name="namespace" value="daozong"/>
        <property name="appkey" value="com.sankuai.mim.pilot"/>
        <property name="topic" value="pilot.assistant.message.send"/>
    </bean>

    <bean id="delaySendMessageProducer" class="com.meituan.mafka.client.bean.MafkaProducer" init-method="start"
          destroy-method="close">
        <property name="namespace" value="daozong"/>
        <property name="appkey" value="com.sankuai.mim.pilot"/>
        <property name="topic" value="pilot.assistant.message.delaysend"/>
        <property name="producerType" value="delayProducer"/>
        <property name="openHook" value="false"/>
    </bean>

<!--    aibook-->
    <bean id="aiBookDispatchDelayProducer" class="com.meituan.mafka.client.bean.MafkaProducer" init-method="start"
          destroy-method="close">
        <property name="namespace" value="daozong"/>
        <property name="appkey" value="com.sankuai.mim.pilot"/>
        <property name="topic" value="pilot.ai.book.dispatch.delay.topic"/>
        <property name="producerType" value="delayProducer"/>
        <property name="openHook" value="false"/>
    </bean>

    <bean id="aiBookCallbackDelayProducer" class="com.meituan.mafka.client.bean.MafkaProducer" init-method="start"
          destroy-method="close">
        <property name="namespace" value="daozong"/>
        <property name="appkey" value="com.sankuai.mim.pilot"/>
        <property name="topic" value="pilot.ai.book.callback.delay.topic"/>
        <property name="producerType" value="delayProducer"/>
        <property name="openHook" value="false"/>
    </bean>

    <bean id="aiBookCallAgainDelayProducer" class="com.meituan.mafka.client.bean.MafkaProducer" init-method="start"
          destroy-method="close">
        <property name="namespace" value="daozong"/>
        <property name="appkey" value="com.sankuai.mim.pilot"/>
        <property name="topic" value="pilot.ai.book.call.again.delay.topic"/>
        <property name="producerType" value="delayProducer"/>
        <property name="openHook" value="false"/>
    </bean>

    <bean id="aiPhoneCallDelayProducer" class="com.meituan.mafka.client.bean.MafkaProducer" init-method="start"
          destroy-method="close">
        <property name="namespace" value="daozong"/>
        <property name="appkey" value="com.sankuai.mim.pilot"/>
        <property name="topic" value="pilot.ai.phone.call.delay.topic"/>
        <property name="producerType" value="delayProducer"/>
        <property name="openHook" value="false"/>
    </bean>

    <bean id="aiPhoneCallbackDelayProducer" class="com.meituan.mafka.client.bean.MafkaProducer" init-method="start"
          destroy-method="close">
        <property name="namespace" value="daozong"/>
        <property name="appkey" value="com.sankuai.mim.pilot"/>
        <property name="topic" value="pilot.ai.phone.callback.check.topic"/>
        <property name="producerType" value="delayProducer"/>
        <property name="openHook" value="false"/>
    </bean>

<!--    aicall-->
    <bean id="aiCallDispatchDelayProducer" class="com.meituan.mafka.client.bean.MafkaProducer" init-method="start"
          destroy-method="close">
        <property name="namespace" value="daozong"/>
        <property name="appkey" value="com.sankuai.mim.pilot"/>
        <property name="topic" value="pilot.ai.call.dispatch.delay.topic"/>
        <property name="producerType" value="delayProducer"/>
        <property name="openHook" value="false"/>
    </bean>

    <bean id="aiCallCallbackDelayProducer" class="com.meituan.mafka.client.bean.MafkaProducer" init-method="start"
          destroy-method="close">
        <property name="namespace" value="daozong"/>
        <property name="appkey" value="com.sankuai.mim.pilot"/>
        <property name="topic" value="pilot.ai.call.callback.delay.topic"/>
        <property name="producerType" value="delayProducer"/>
        <property name="openHook" value="false"/>
    </bean>

    <bean id="aiCallCallAgainDelayProducer" class="com.meituan.mafka.client.bean.MafkaProducer" init-method="start"
          destroy-method="close">
        <property name="namespace" value="daozong"/>
        <property name="appkey" value="com.sankuai.mim.pilot"/>
        <property name="topic" value="pilot.ai.call.callagain.delay.topic"/>
        <property name="producerType" value="delayProducer"/>
        <property name="openHook" value="false"/>
    </bean>


    <bean id="aiCallShopResultProducer" class="com.meituan.mafka.client.bean.MafkaProducer" init-method="start"
          destroy-method="close">
        <property name="namespace" value="daozong"/>
        <property name="appkey" value="com.sankuai.mim.pilot"/>
        <property name="topic" value="pilot.ai.call.shop.result.topic"/>
        <property name="openHook" value="false"/>
    </bean>


</beans>