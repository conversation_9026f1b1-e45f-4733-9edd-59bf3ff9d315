package com.sankuai.dzim.pilot.domain.retrieval;

import com.sankuai.dzim.pilot.BaseTest;
import com.sankuai.dzim.pilot.chain.enums.AIServiceExtraKeyEnum;
import com.sankuai.dzim.pilot.domain.retrieval.data.RetrievalContext;
import com.sankuai.dzim.pilot.domain.retrieval.impl.BanquetHallAugmentor;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.junit.platform.commons.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

public class BanquetHallAugmentorTest extends BaseTest {

    @Autowired
    private BanquetHallAugmentor banquetHallAugmentor;

    @Test
    public void test_singleBanquetHallInfoSearch() {
        RetrievalContext retrievalContext = new RetrievalContext();
        retrievalContext.setQuery("测试数据检索");
        retrievalContext.addExtraInfo(AIServiceExtraKeyEnum.DP_SHOP_ID.getKey(), "15958919");
        retrievalContext.addExtraInfo(AIServiceExtraKeyEnum.BANQUET_HALL_ID.getKey(), String.valueOf(1261648096353L));
        String res = banquetHallAugmentor.retrieval(retrievalContext);
        Assert.assertTrue(StringUtils.isNotBlank(res));
    }
}
