package com.sankuai.dzim.pilot.process.impl.aibook;

import com.google.common.collect.Lists;
import com.sankuai.dzim.pilot.BaseTest;
import com.sankuai.dzim.pilot.gateway.api.aibook.data.NaviVO;
import com.sankuai.dzim.pilot.gateway.api.aibook.data.TimePeriodVO;
import com.sankuai.dzim.pilot.process.aibook.data.AIBookDetailQueryRequest;
import com.sankuai.dzim.pilot.process.aibook.data.PositionRequest;
import com.sankuai.dzim.pilot.process.aibook.data.TimePeriodConfigData;
import com.sankuai.dzim.pilot.process.aibook.impl.AIBookProcessServiceImpl;
import com.sankuai.dzim.pilot.utils.DateUtils;
import com.sankuai.dzim.pilot.utils.LionConfigUtil;
import lombok.var;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public class AIBookProcessServiceImplTest extends BaseTest {


    @Autowired
    private AIBookProcessServiceImpl aiBookProcessService;

    @Autowired
    private LionConfigUtil lionConfigUtil;

    @Test
    public void test_buildPosition() {
        PositionRequest request = new PositionRequest();
        request.setPlatform(2);
        request.setCityId(10);
        aiBookProcessService.buildPosition(request);
    }

    @Test
    public void test_TimePeriod() {
        Map<Integer, TimePeriodConfigData> timePeriodId2ConfigMap = lionConfigUtil.getTimePeriodConfig().values().stream()
                .flatMap(List::stream)
                .collect(Collectors.toMap(TimePeriodConfigData::getId, Function.identity(), (o1, o2) -> o1));
        Date now = new Date();
        for (TimePeriodConfigData timePeriodConfigData: buildTimePeriod()) {
            System.out.println(timePeriodConfigData.getDesc());
//            System.out.println(DateUtils.covertDateStr(timePeriodConfigData.getBeginTime(now, 30)));
            System.out.println(DateUtils.covertDateStr(timePeriodConfigData.getEndTime(now, 30)));
            System.out.println("=======");
        }

    }

    private List<TimePeriodConfigData> buildTimePeriod() {
        Date now = new Date();
        int currentHour = DateUtils.getHourOfDay(now);
        Map<String, List<TimePeriodConfigData>> timePeriodConfig = lionConfigUtil.getTimePeriodConfig();

        // 对 HashMap 的 key 进行降序排序
        List<String> timePeriodKeys = new ArrayList<>(timePeriodConfig.keySet());
        Collections.sort(timePeriodKeys, Collections.reverseOrder());

        for (String hourKey : timePeriodKeys) {
            if (currentHour >= Integer.parseInt(hourKey)) {
                return timePeriodConfig.get(hourKey);
            }
        }
        return Lists.newArrayList();
    }

    @Test
    public void test_flattenNaviList() {
        int mtScore = 46;
        BigDecimal bigDecimalNumber = BigDecimal.valueOf(mtScore);
        BigDecimal result = bigDecimalNumber.divide(BigDecimal.valueOf(10), 1, RoundingMode.HALF_UP);
        System.out.println(result.floatValue());
//        return Lists.newArrayList();
    }


}
