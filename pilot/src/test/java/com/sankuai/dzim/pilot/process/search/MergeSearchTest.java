package com.sankuai.dzim.pilot.process.search;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.meituan.service.mobile.search.poi.thrift.MergeSearchResponse;
import com.sankuai.dzim.pilot.acl.impl.MergeSearchAclServiceImpl;
import com.sankuai.dzim.pilot.process.data.ShopRecallData;
import org.apache.commons.io.IOUtils;
import org.junit.Assert;
import org.junit.jupiter.api.Test;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.List;

public class MergeSearchTest {
    
    private MergeSearchAclServiceImpl mergeSearchAclServiceImpl = new MergeSearchAclServiceImpl(); 

    @Test
    public void testExtractShopRecallData() {
        String content = readResourceFile("mock/MergeSearchResponse1.json");
        // 使用文件内容进行后续处理
        MergeSearchResponse decode = JsonCodec.decode(content, MergeSearchResponse.class);
        List<ShopRecallData> shopRecallData = mergeSearchAclServiceImpl.extractShopRecallData(decode, "");
        Assert.assertNotNull(shopRecallData);
    }

    private String readResourceFile(String fileName) {
        try {
            // 获取资源文件的输入流
            InputStream inputStream = getClass().getClassLoader().getResourceAsStream(fileName);
            if (inputStream == null) {
                throw new IllegalArgumentException("文件不存在: " + fileName);
            }
            // 读取文件内容并转换为字符串
            return IOUtils.toString(inputStream, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new RuntimeException("读取资源文件失败: " + fileName, e);
        }
    }
}
