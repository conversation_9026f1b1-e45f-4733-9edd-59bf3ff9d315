package com.sankuai.dzim.pilot.acl;

import com.google.common.collect.Maps;
import com.sankuai.call.sdk.entity.aicall.AiCallParamBO;
import com.sankuai.call.sdk.entity.aicall.AiCallResponseDTO;
import com.sankuai.dzim.pilot.BaseTest;
import com.sankuai.dzim.pilot.process.aiphonecall.data.AIPhoneCallConfig;
import com.sankuai.dzim.pilot.utils.LionConfigUtil;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

public class JupiterCallAclServiceTest extends BaseTest {

    @Autowired
    private JupiterCallAclService jupiterCallAclService;

    @Autowired
    private LionConfigUtil lionConfigUtil;


    @Test
    public void test_call() {
        // 创建IVR外呼
        AiCallParamBO aiCallParamBO = buildAiCallParamBO();
        AiCallResponseDTO<String> aiCallResponseDTO = jupiterCallAclService.invokeAiCall(aiCallParamBO);
        System.out.println();
    }

    private AiCallParamBO buildAiCallParamBO() {
        AIPhoneCallConfig aiPhoneCallConfig = lionConfigUtil.getAiPhoneCallConfig();

        AiCallParamBO aiCallParamBO = new AiCallParamBO();
        aiCallParamBO.setTenantId(aiPhoneCallConfig.getTenantId());
        aiCallParamBO.setDeviceNum("17376500734");
        aiCallParamBO.setTenantId("6a993715-f911-48db-befa-76512282134d");
        aiCallParamBO.setBotVersion("1");
        Map<String, Object> placeHolder = Maps.newHashMap();
        placeHolder.put("currentTime","2025-04-28 10:00");
        placeHolder.put("duration", "60");
        placeHolder.put("peopleNumber", 1);
        placeHolder.put("userPhone", "17376500734");
        placeHolder.put("reservationTime", "2025-04-29 下午六点");
        placeHolder.put("technicianName", "");
        placeHolder.put("shopName", "hhyy美发店");
        placeHolder.put("projectName", "理发");
        placeHolder.put("opening", "你好");
        aiCallParamBO.setRoutePoint("46181728");
        aiCallParamBO.setPlaceholder(placeHolder);
        aiCallParamBO.setMediaTxt(placeHolder);
        aiCallParamBO.setBotId("60ce50f5-1378-4673-9c24-3c69d251cca1");
        // 1: 小美 2：金融催收 3：金融
        aiCallParamBO.setAiEngineType(1);
        return aiCallParamBO;
    }
}
