package com.sankuai.dzim.pilot.plugin;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.dzim.pilot.BaseTest;
import com.sankuai.dzim.pilot.chain.data.AIAnswerData;
import com.sankuai.dzim.pilot.chain.data.AIServiceContext;
import com.sankuai.dzim.pilot.gateway.api.assistant.data.BeamContext;
import com.sankuai.dzim.pilot.gateway.api.assistant.data.BeamMessage;
import com.sankuai.dzim.pilot.gateway.api.assistant.data.SendBeamMessageReq;
import com.sankuai.dzim.pilot.process.data.ProductRetrievalData;
import com.sankuai.dzim.pilot.process.localplugin.BeamShopProductRecommendPlugin;
import com.sankuai.dzim.pilot.process.localplugin.data.UserContext;
import com.sankuai.dzim.pilot.process.localplugin.param.ShopProductSearchParam;
import com.sankuai.dzim.pilot.process.search.SearchProcessService;
import com.sankuai.dzim.pilot.scene.task.data.EnvContext;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.*;

@SpringBootTest
public class BeamShopProductRecommendPluginTest extends BaseTest {
    @Resource
    private BeamShopProductRecommendPlugin beamShopProductRecommendPlugin;

    @Resource
    private SearchProcessService searchProcessService;

    @Test
    public void testExecute() {
        System.out.println("测试开始");

        ShopProductSearchParam shopProductSearchParam = buildShopProductSearchParam();
//        System.out.println(JSON.toJSONString(shopProductSearchParam));
//        AIAnswerData aiAnswerData = beamShopProductRecommendPlugin.recommendShopProduct(shopProductSearchParam);

    }

    private ShopProductSearchParam buildShopProductSearchParam() {
        BeamContext beamContext = new BeamContext();
//        beamContext.setPoi_id("608162978");
//        beamContext.setPoi_id("455977");
//        beamContext.setPoi_id("606900904");
        beamContext.setPoi_id("42252832");
        beamContext.setQuery_understanding("想做个男士气垫烫，有合适的团购吗？");
        beamContext.setUser_understanding("");
        beamContext.setFixed_knowledge("无");
        beamContext.setChat_history(new ArrayList<>());
        beamContext.setUser_profile("|画像名称|画像值||---|---||用户性别|男||用户年龄段|25 到 30 岁||用户婚姻状况|未婚||用户职业|白领||用户从业行业_预测|科技行业||是否在校大学生|非大学生||是否是亲子_预测|无小孩||亲子年龄|未知||是否有车|有车||常住美团开站城市|上海||常住城市所在省份|上海市||常住行政城市区县|310110||标准场景|非节假日_工作日_morning_非节假日_非节气_非生日期内及未识别_常住城市_熟悉位置_距离工作地中等(1.5km,10km]_居住地附近(100m,1.5km]_实际非极端天气_预测非极端天气_未识别到历史迁徙或近期未迁徙|");
        beamContext.setBusiness_data(null);

        SendBeamMessageReq sendBeamMessageReq = new SendBeamMessageReq();
        sendBeamMessageReq.setContext(beamContext);

        BeamMessage beamMessage = new BeamMessage();
        beamMessage.setRole("user");
        beamMessage.setContent("想做个男士气垫烫，有合适的团购吗？");
        sendBeamMessageReq.setOriginal_user_input(beamMessage);

        UserContext userContext = new UserContext();
        userContext.setImUserId("m123456");

        EnvContext envContext = new EnvContext();
        envContext.setCityId(1);
//        envContext.setLat(31.217405);
//        envContext.setLng(121.371412);
//        envContext.setDeviceId("G4Tcjtsx40WPrXgY");
//
//        envContext.setLat(31.2049);
//        envContext.setLng(121.439087);
//        envContext.setDeviceId("k5Mx2fVD0HEFEiFU");
//
//        envContext.setLat(31.188089);
//        envContext.setLng(121.466755);
//        envContext.setDeviceId("H95IAG4YqrxCtz9r");
        envContext.setLat(31.730528);
        envContext.setLng(121.244579);
        envContext.setDeviceId("H726JDkRbUI47dIu");
        envContext.setAppVersion("12.10.400");

        Map<String, Object> extraInfo = new HashMap<>();
        extraInfo.put("userContext", userContext);
        extraInfo.put("beamRequestContext", sendBeamMessageReq);
        extraInfo.put("envContext", envContext);
        AIServiceContext aiServiceContext = new AIServiceContext();
        aiServiceContext.setExtraInfo(extraInfo);
        ShopProductSearchParam shopProductSearchParam = new ShopProductSearchParam();
        shopProductSearchParam.setAiServiceContext(aiServiceContext);
        return shopProductSearchParam;
    }

    @Test
    public void testBeamProductCardBuilder() {
        System.out.println("测试开始");

        String AIAnswer = "{\n" +
                "  \"introductContent\": \"为您找到以下一个团购，请问需要为您下单吗？\",\n" +
                "  \"recommendDealList\": [\n" +
                "    {\n" +
                "      \"dealId\": \"1037884824\",\n" +
                "      \"recommendReason\": \"该套餐价格实惠，仅需49.90元，提供商务洗剪不限时服务，适合普通剪发需求，性价比高。\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"dealId\": \"1037884825\",\n" +
                "      \"recommendReason\": \"该套餐价格实惠，仅需49.90元，提供商务洗剪不限时服务，适合普通剪发需求，性价比高。\"\n" +
                "    }\n" +
                "  ]\n" +
                "}";
        JSONObject jsonObject = JSON.parseObject(AIAnswer);
        String tag = "BeamProductCard";
        String introductContent = jsonObject.getString("introductContent");
        StringBuilder beamCardContent = new StringBuilder();
        beamCardContent.append(introductContent);
        JSONArray recommendDealList = jsonObject.getJSONArray("recommendDealList");
        Map<String, Object> extraInfo = new HashMap<>();
        for (int i = 0; i < recommendDealList.size(); i++) {
            JSONObject recommendDeal = recommendDealList.getJSONObject(i);
            extraInfo.put(""+(i+1), recommendDeal);
            beamCardContent.append("<"+tag+">"+"FWLS-"+(i+1)+"</"+tag+">");
        }
        System.out.println(beamCardContent);
        System.out.println(extraInfo);


    }


    @Test
    public void testShopProductSearch() {
        ShopProductSearchParam shopProductSearchParam = buildShopProductSearchParam();
        shopProductSearchParam.setShopId(608162978L);
        List<ProductRetrievalData> productRetrievalData = searchProcessService.searchShopProducts(shopProductSearchParam);
        System.out.println(productRetrievalData);
    }
}
