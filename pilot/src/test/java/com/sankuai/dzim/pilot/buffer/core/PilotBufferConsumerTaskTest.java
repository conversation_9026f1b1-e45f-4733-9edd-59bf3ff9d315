package com.sankuai.dzim.pilot.buffer.core;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dzim.pilot.BaseTest;
import com.sankuai.dzim.pilot.buffer.core.cutoff.impl.BoldTagCutOffExt;
import com.sankuai.dzim.pilot.buffer.core.cutoff.impl.CommonTagCutOffExt;
import com.sankuai.dzim.pilot.buffer.enums.BufferItemTypeEnum;
import com.sankuai.dzim.pilot.buffer.stream.vo.StreamEventDataVO;
import com.sankuai.dzim.pilot.chain.data.AIAnswerData;
import com.sankuai.dzim.pilot.process.data.ProductRetrievalData;
import org.apache.commons.math3.util.Pair;
import org.junit.Assert;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @since 2024/8/1 11:33
 */
public class PilotBufferConsumerTaskTest extends BaseTest {


    @Test
    public void testRecognizeTags() {
        CommonTagCutOffExt task = new CommonTagCutOffExt();
        List<Pair<String, String>> pairs = task.recognizeTags("<<<<<<<21223<shopId>12312</shopId>");
        System.out.println(pairs);
        Assert.assertNotNull(pairs);

        BoldTagCutOffExt boldTagCutOffExt = new BoldTagCutOffExt();
        List<Pair<String, String>> boldPairs = boldTagCutOffExt.recognizeTags("**111****2222**11");
        System.out.println(boldPairs);
        Assert.assertNotNull(boldPairs);

    }

    @Test
    public void testConvertTagGroup() {

        List<PilotBufferItemDO> pilotBufferItemDOs = buildBufferItemList();
        PilotBufferConsumerTask task = new PilotBufferConsumerTask(null, null, null, null, null);
        StreamEventDataVO eventData = task.convertTagGroup(pilotBufferItemDOs, new CommonTagCutOffExt());
        System.out.println(JsonCodec.encodeWithUTF8(eventData));
        Assert.assertNotNull(eventData);

    }

    @Test
    public void testBuildCardConvertTagGroup() {

        String beamCard = "为您找到以下团购，需要我帮您下单吗？<BeamProductCard>{\"dealName\":\"【学生特惠】性价比洗剪吹\",\"dealId\":\"1041407737\",\"index\":1}</BeamProductCard><BeamProductCard>{\"dealName\":\"【工作日特惠】总监洗剪吹造型\",\"dealId\":\"1041436657\",\"index\":2}</BeamProductCard><BeamProductCard>{\"dealName\":\"【不满意重做】高级定制洗剪吹\",\"dealId\":\"1041452852\",\"index\":3}</BeamProductCard>";
        PilotBufferItemDO item = new PilotBufferItemDO();
        item.setType(BufferItemTypeEnum.MAIN_TEXT.getType());
        item.setData(beamCard);
        JSONObject productInfo = JSONObject.parseObject("{1={\"dealName\":\"【学生特惠】性价比洗剪吹\",\"dealId\":1041407737,\"recommendReason\":\"价格实惠，仅需19.90元，适合学生党，同时服务无限制，性价比高。\",\"finalPrice\":\"19.90\",\"dealImage\":\"\",\"dealTags\":[]}, 2={\"dealName\":\"【工作日特惠】总监洗剪吹造型\",\"dealId\":1041436657,\"recommendReason\":\"总监级别服务，满足高要求发型设计需求，价格合理，工作日特惠仅需69元。\",\"finalPrice\":\"69.00\",\"dealImage\":\"\",\"dealTags\":[]}, 3={\"dealName\":\"【不满意重做】高级定制洗剪吹\",\"dealId\":1041452852,\"recommendReason\":\"高级定制服务，价值198元的服务仅需78元，并提供不满意重做保障，适合对发型有高要求的用户。\",\"finalPrice\":\"78.00\",\"dealImage\":\"\",\"dealTags\":[]}}");

        item.setExtra(productInfo);
        List<PilotBufferItemDO> pilotBufferItemDOs = new ArrayList<>();


        pilotBufferItemDOs.add(item);
        PilotBufferConsumerTask task = new PilotBufferConsumerTask(null, null, null, null, null);
        StreamEventDataVO eventData = task.convertTagGroup(pilotBufferItemDOs, new CommonTagCutOffExt());
        System.out.println(JsonCodec.encodeWithUTF8(eventData));
        Assert.assertNotNull(eventData);

    }

    private Map<String, Object> getRecommendDealInfo(AIAnswerData recommendAIAnswer, List<ProductRetrievalData> products) {
        JSONObject jsonObject = JSON.parseObject(recommendAIAnswer.getAnswer());
        JSONArray recommendDealList = jsonObject.getJSONArray("recommendDealList");

        Map<Long, Integer> dealIdIndexMap = new HashMap<>();
        for (int i = 0; i < recommendDealList.size(); i++) {
            dealIdIndexMap.put(recommendDealList.getJSONObject(i).getLong("dealId"), i + 1);
        }

        Map<String, Object> recommendDealInfo = new HashMap<>();
        for (int i = 0; i < products.size(); i++) {
            ProductRetrievalData product = products.get(i);
            if (!dealIdIndexMap.containsKey(product.getDpProductId()) || !dealIdIndexMap.containsKey(product.getMtProductId())) {
                continue;
            }
            long productId = dealIdIndexMap.containsKey(product.getDpProductId()) ? product.getDpProductId() : product.getMtProductId();
            int index = dealIdIndexMap.get(productId);
            JSONObject productInfo = new JSONObject();
            productInfo.put("dealId", productId);
            productInfo.put("dealName", product.getTitle());
            productInfo.put("finalPrice", product.getSalePrice());
            productInfo.put("dealImage", product.getHeadPic());
            productInfo.put("dealTags", new JSONArray());
            productInfo.put("recommendReason", recommendDealList.getJSONObject(index - 1).getString("recommendReason"));
            recommendDealInfo.put(String.valueOf(index), productInfo);
        }
        return recommendDealInfo;
    }

    private List<PilotBufferItemDO> buildBufferItemList() {
        PilotBufferItemDO item1 = new PilotBufferItemDO();
        item1.setType(BufferItemTypeEnum.LOADING_STATUS.getType());
        item1.setData("我正在检索医美知识库");
        PilotBufferItemDO item2 = new PilotBufferItemDO();
        item2.setType(BufferItemTypeEnum.LOADING_STATUS.getType());
        item2.setData("检索完毕");
        PilotBufferItemDO item3 = new PilotBufferItemDO();
        item3.setType(BufferItemTypeEnum.MAIN_TEXT.getType());
        item3.setData("基于4篇网络文章查询得到以下内容");
        PilotBufferItemDO item4 = new PilotBufferItemDO();
        item4.setType(BufferItemTypeEnum.MAIN_TEXT.getType());
        item4.setData("<BeamProductCard>sourcekey</BeamProductCard>");
        Map<String, Object> extra4 = Maps.newHashMap();
        extra4.put("sourceContent", "基于公开信息收集整理");
        item4.setExtra(extra4);
        PilotBufferItemDO item5 = new PilotBufferItemDO();
        item5.setType(BufferItemTypeEnum.MAIN_TEXT.getType());
        item5.setData("这家门店<");
        PilotBufferItemDO item6 = new PilotBufferItemDO();
        item6.setType(BufferItemTypeEnum.MAIN_TEXT.getType());
        item6.setData("product");
        PilotBufferItemDO item7 = new PilotBufferItemDO();
        item7.setType(BufferItemTypeEnum.MAIN_TEXT.getType());
        item7.setData(">1244");
        PilotBufferItemDO item8 = new PilotBufferItemDO();
        item8.setType(BufferItemTypeEnum.MAIN_TEXT.getType());
        item8.setData("56312<");
        PilotBufferItemDO item9 = new PilotBufferItemDO();
        item9.setType(BufferItemTypeEnum.MAIN_TEXT.getType());
        item9.setData("/product>");
        PilotBufferItemDO item10 = new PilotBufferItemDO();
        item10.setType(BufferItemTypeEnum.MAIN_TEXT.getType());
        item10.setData("很不错");
        List<PilotBufferItemDO> written = Lists.newArrayList(item1, item2, item3, item4, item5, item6, item7, item8, item9, item10);
        return written;
    }


}