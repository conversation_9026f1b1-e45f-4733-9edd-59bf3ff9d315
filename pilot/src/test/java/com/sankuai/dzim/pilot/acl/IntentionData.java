package com.sankuai.dzim.pilot.acl;

import com.sankuai.it.xcontract.easypoi.annotation.Excel;
import lombok.Data;

@Data
public class IntentionData {

    @Excel(name = "context")
    private String context;

    @Excel(name = "userQuery")
    private String userQuery;

    @Excel(name = "history")
    private String history;

    @Excel(name = "rewriteUserQuery")
    private String rewriteUserQuery;

    @Excel(name = "rewriteHistory")
    private String rewriteHistory;

    @Excel(name = "toolName")
    private String toolName;

    @Excel(name = "toolParam")
    private String toolParam;

}
