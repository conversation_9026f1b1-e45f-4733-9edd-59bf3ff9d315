package com.sankuai.dzim.pilot.process.data;

import com.sankuai.it.xcontract.easypoi.annotation.Excel;
import lombok.Data;

@Data
public class DialogData {

    @Excel(name = "DialogRawData")
    private String dialogRawData;

    @Excel(name = "ExtraData")
    private String extraData;

    @Excel(name = "dialog")
    private String dialog;

    @Excel(name = "AddTime")
    private String addTime;

    @Excel(name = "ContactID")
    private String contactId;

    @Excel(name = "AudioUrl")
    private String audioUrl;
}
