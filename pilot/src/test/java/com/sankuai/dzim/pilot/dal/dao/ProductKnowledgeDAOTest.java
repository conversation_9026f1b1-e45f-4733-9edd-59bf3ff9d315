package com.sankuai.dzim.pilot.dal.dao;

import com.sankuai.dzim.pilot.BaseTest;
import com.sankuai.dzim.pilot.dal.entity.ProductKnowledgeEntity;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;


public class ProductKnowledgeDAOTest extends BaseTest {

    @Autowired
    private ProductKnowledgeDAO productKnowledgeDAO;

    @Test
    public void insertTest() {
        ProductKnowledgeEntity entity = new ProductKnowledgeEntity();
        entity.setShopId(123L);
        entity.setProductType(1);
        entity.setSubjectId("21231");
        entity.setTitle("洗涤护理-任意洗-衣服鞋子任洗");
        entity.setDescription("洗涤护理-任意洗-衣服鞋子任洗");
        entity.setVectorId(123);
        int insert = productKnowledgeDAO.insert(entity);
        Assert.isTrue(insert > 0, "error");
    }

//    @Test
//    public void batchFindByVectorIds() {
//        List<MerchantKnowledgeEntity> bizKnowledgeEntities = merchantKnowledgeDAO.batchFindByVectorIds(Lists.newArrayList(123l));
//        Assert.isTrue(CollectionUtils.isNotEmpty(bizKnowledgeEntities), "error");
//    }
//
//    @Test
//    public void batchDelete() {
//        int res = merchantKnowledgeDAO.batchDeleteByVectorIds(Lists.newArrayList(123l), "chaizijun1");
//        Assert.isTrue(res > 0, "error");
//    }
}
