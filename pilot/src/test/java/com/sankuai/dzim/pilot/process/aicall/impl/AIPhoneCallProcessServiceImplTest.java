package com.sankuai.dzim.pilot.process.aicall.impl;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.dzim.pilot.BaseTest;
import com.sankuai.dzim.pilot.acl.TokenAccessAclService;
import com.sankuai.dzim.pilot.dal.entity.aiphonecall.AIPhoneCallDetailEntity;
import com.sankuai.dzim.pilot.dal.entity.aiphonecall.AIPhoneCallTaskEntity;
import com.sankuai.dzim.pilot.domain.AIPhoneCallDomainService;
import com.sankuai.dzim.pilot.enums.AIPhoneCallDetailStatusEnum;
import com.sankuai.dzim.pilot.enums.AIPhoneCallSceneTypeEnum;
import com.sankuai.dzim.pilot.enums.AIPhoneCallTaskStatusEnum;
import com.sankuai.dzim.pilot.process.aiphonecall.AIPhoneCallProcessService;
import com.sankuai.dzim.pilot.process.aiphonecall.data.AIPhoneCallDetailCreateReq;
import com.sankuai.dzim.pilot.process.aiphonecall.data.AIPhoneCallTaskCreateReq;
import com.sankuai.dzim.pilot.process.aiphonecall.impl.AIPhoneCallProcessServiceImpl;
import com.sankuai.dzim.pilot.process.aiphonecall.impl.AIPhoneCallStateManager;
import com.sankuai.dzim.pilot.utils.data.Response;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.*;

@SpringBootTest
class AIPhoneCallProcessServiceImplTest extends BaseTest {

    @Autowired
    private AIPhoneCallDomainService aiPhoneCallDomainService;

    @Autowired
    private TokenAccessAclService tokenAccessAclService;

    @Autowired
    private AIPhoneCallStateManager callStateManager;

    @Test
    void createAIPhoneCallTask() {

        AIPhoneCallTaskEntity taskEntity = new AIPhoneCallTaskEntity();
        taskEntity.setBizId("123");
        taskEntity.setBizData("{}");
        taskEntity.setPlatform(1);
        taskEntity.setSceneType(3);
        taskEntity.setStatus(AIPhoneCallTaskStatusEnum.CREATED.getStatus());
        taskEntity.setSource(2);
        aiPhoneCallDomainService.insertTaskAndDetail(taskEntity, createDetailList());
    }

    public List<AIPhoneCallDetailEntity> createDetailList() {
        List<AIPhoneCallDetailEntity> callDetailEntityList = Lists.newArrayList();
        AIPhoneCallDetailEntity callDetailEntity = new AIPhoneCallDetailEntity();
        callDetailEntity.setCallPhone(tokenAccessAclService.getTokenMobile("18810000000"));
        callDetailEntity.setSequenceId(3);
        callDetailEntity.setPlatform(1);
        callDetailEntity.setShopId(1234567890);
        callDetailEntity.setDynamicVariable("{}");
        callDetailEntity.setStatus(AIPhoneCallDetailStatusEnum.PENDING.getStatus());
        callDetailEntity.setUserId(1234567890);
        callDetailEntity.setBotId("1234567890");
        callDetailEntity.setRoutePoint("1234567890");
        callDetailEntity.setBotVersion("1");
        callDetailEntity.setDdlDate("2023-03-20");
        callDetailEntity.setExpectedCallTime(new Date());
        callDetailEntityList.add(callDetailEntity);
        callDetailEntityList.add(callDetailEntity);

        return callDetailEntityList;
    }

}