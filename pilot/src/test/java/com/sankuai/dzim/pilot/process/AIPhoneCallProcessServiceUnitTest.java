package com.sankuai.dzim.pilot.process;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dzim.pilot.UnitBaseTest;
import com.sankuai.dzim.pilot.chain.eval.data.EvalCaseResult;
import com.sankuai.dzim.pilot.process.aiphonecall.data.CallDialogMessage;
import com.sankuai.dzim.pilot.process.aiphonecall.data.CallDialogRawData;
import com.sankuai.dzim.pilot.process.data.DialogData;
import com.sankuai.dzim.pilot.utils.ExcelUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Before;
import org.junit.Test;
import org.mockito.MockitoAnnotations;

import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class AIPhoneCallProcessServiceUnitTest extends UnitBaseTest {

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void test_stat() {
        List<EvalCaseResult> evalCaseResultList = ExcelUtils.readFromExcel("/Users/<USER>/Documents/intention_data_sonnet-4_v4_result_LLMCommon.xlsx", EvalCaseResult.class);
        Map<String, Map<String, Integer>> intentionResultMap = Maps.newHashMap();

        for (EvalCaseResult evalCaseResult: evalCaseResultList) {
            String intention = evalCaseResult.getTargetOutput().split("_")[0];
            Map<String, Integer> resultMap = intentionResultMap.getOrDefault(intention, Maps.newHashMap());

            String result = String.valueOf(evalCaseResult.isFunctionCallingAccuracy());
            resultMap.put(result, resultMap.getOrDefault(result, 0) + 1);
            intentionResultMap.put(intention, resultMap);
        }

        int totalTrueCount = 0, totalFalseCount = 0;
        for (Map.Entry<String, Map<String, Integer>> entry: intentionResultMap.entrySet()) {
            String intention = entry.getKey();
            Map<String, Integer> resultMap = entry.getValue();

            int trueCount = resultMap.getOrDefault("true", 0);
            int falseCount = resultMap.getOrDefault("false", 0);
            totalTrueCount += trueCount;
            totalFalseCount += falseCount;

            System.out.println(intention + ":" + trueCount + "/" + (trueCount+falseCount)+ ", acc = " + (trueCount * 1.0 / (trueCount + falseCount)));
        }
        System.out.println(totalTrueCount + "/" + (totalTrueCount + totalFalseCount) + ", acc = " + (totalTrueCount * 1.0 / (totalTrueCount + totalFalseCount)));

    }

    @Test
    public void test_unescape() {
        String jsonStr = "{\"user\": \"帮我来份订单\", \"assistant\": \"下单完成了，商品编号是132232323，一共两件\",\"user\":\"ok\"}";
        StringBuilder result = new StringBuilder();

        // 使用正则表达式匹配所有的 key-value 对
        String pattern = "\"(\\w+)\"\\s*:\\s*\"([^\"]+)\"";
        java.util.regex.Pattern r = java.util.regex.Pattern.compile(pattern);
        java.util.regex.Matcher m = r.matcher(jsonStr);

        // 遍历所有匹配项并格式化输出
        while (m.find()) {
            String role = m.group(1);
            String content = m.group(2);
            result.append(role)
                    .append(":")
                    .append(content)
                    .append("\n");
        }

        System.out.println(result.toString());
    }


    @Test
    public void test_exportData() {
        List<DialogData> rawDialogDataList = ExcelUtils.readFromExcel("/Users/<USER>/Documents/1747050235730.xlsx", DialogData.class);

        for (DialogData dialogData: rawDialogDataList) {
            String natural = convertNaturalDialog(dialogData.getDialogRawData());
            dialogData.setDialog(natural);
        }

        ExcelUtils.exportExcel("/Users/<USER>/Documents/AICallData.xlsx", rawDialogDataList, DialogData.class);
    }

    private String convertNaturalDialog(String dialogRawData) {
        CallDialogRawData callDialogRawData = JsonCodec.decode(dialogRawData, CallDialogRawData.class);
        if (callDialogRawData == null || CollectionUtils.isEmpty(callDialogRawData.getHistory())) {
            return StringUtils.EMPTY;
        }

        StringBuffer callDialog = new StringBuffer(StringUtils.EMPTY);
        for (CallDialogMessage callDialogMessage: callDialogRawData.getHistory()) {
            if (callDialogMessage == null || StringUtils.isEmpty(callDialogMessage.getMessage())) {
                continue;
            }

            if (callDialogMessage.getSource().equals("user")) {
                callDialog.append("商家：" + callDialogMessage.getMessage() + "\n");
            }
            if (callDialogMessage.getSource().equals("machine")) {
                callDialog.append("AI助手：" + callDialogMessage.getMessage() + "\n");
            }
        }
        return callDialog.toString();
    }

    @Test
    public void test_calculateDelay() {
        List<DialogData> rawDialogDataList = ExcelUtils.readFromExcel("/Users/<USER>/Documents/gpt4.1-mini-record.xlsx", DialogData.class);
        List<String> rawDialogList = rawDialogDataList.stream().map(DialogData::getDialogRawData).collect(Collectors.toList());
        long totalDelay = 0L, cnt = 0L;
        for (String rawDialog: rawDialogList) {
            Pair<Long, Long> pair = calculate(rawDialog);
            totalDelay += pair.getKey();
            cnt += pair.getValue();
        }
        System.out.println("total Delay: " + totalDelay);
        System.out.println("cnt: " + cnt);
        System.out.println("average Delay: " + (totalDelay * 1.0 / cnt));
//        String rawDialog = "{\"template\":\"60ce50f5-1378-4673-9c24-3c69d251cca1\",\"voiceName\":\"meishuman\",\"mediaTxt\":{\"duration\":\"120分钟\",\"currentTime\":\"2025-05-07 16:43:55\",\"peopleNumber\":\"1\",\"reservationTime\":\"上午10点到12点\",\"userPhone\":\"***********\",\"technicianName\":\"\",\"shopName\":\"到综团购-足疗店\",\"userPhoneTail\":\"2032\",\"remark\":\"\",\"projectName\":\"足疗\",\"reservationDate\":\"明天\",\"opening\":\"你好，我在美团上看到你们家店，明天上午10点到12点能约吗？想做个足疗。\"},\"ontologyVersion\":4.0,\"rankInfo\":{\"collectResult\":{},\"rankDesc\":\"\",\"rank\":\"A\"},\"conversationId\":\"20250507164356097142-162711\",\"userVariables\":{},\"analysisResult\":{\"avgDelay\":1430.0,\"interruptions\":0.0,\"totalRounds\":3.0},\"formattedQaPairList\":[],\"history\":[{\"play\":true,\"playFinish\":true,\"startTime\":\"0\",\"source\":\"signal\",\"endTime\":\"0\",\"id\":\"SpiNzRum\",\"interrupted\":false,\"message\":\"prepare\",\"extInfo\":{\"mediaTxt\":{\"duration\":\"120分钟\",\"currentTime\":\"2025-05-07 16:43:55\",\"peopleNumber\":\"1\",\"reservationTime\":\"上午10点到12点\",\"userPhone\":\"***********\",\"technicianName\":\"\",\"shopName\":\"到综团购-足疗店\",\"userPhoneTail\":\"2032\",\"remark\":\"\",\"projectName\":\"足疗\",\"reservationDate\":\"明天\",\"opening\":\"你好，我在美团上看到你们家店，明天上午10点到12点能约吗？想做个足疗。\"},\"bizData\":{\"callTaskId\":\"116\",\"businessKey\":\"FWLSMainAgentAIPhoneCall\",\"callDetailId\":\"109\"}}},{\"play\":true,\"playFinish\":true,\"startTime\":\"0\",\"source\":\"signal\",\"endTime\":\"0\",\"id\":\"oMpSHwqC\",\"interrupted\":false,\"message\":\"earlyMedia\"},{\"play\":true,\"playFinish\":true,\"startTime\":\"0\",\"source\":\"signal\",\"endTime\":\"0\",\"id\":\"GFSXfMUv\",\"interrupted\":false,\"message\":\"ring\"},{\"play\":true,\"playFinish\":true,\"startTime\":\"0\",\"source\":\"signal\",\"endTime\":\"0\",\"id\":\"kHeckxzA\",\"interrupted\":false,\"message\":\"pick_up\"},{\"play\":true,\"processInfo\":{\"knowledgeRecallDetails\":[],\"toolRecallDetails\":[],\"userVariables\":{}},\"playFinish\":true,\"estimateBroadDuration\":7200.0,\"startTime\":\"1200\",\"source\":\"machine\",\"endTime\":\"7500\",\"id\":\"OULjkEkU\",\"interrupted\":false,\"message\":\"你好，我在美团上看到你们家店，明天上午10点到12点能约吗？想做个足疗。\"},{\"play\":true,\"playFinish\":true,\"startTime\":\"10570\",\"source\":\"user\",\"endTime\":\"11480\",\"id\":\"MefsDhsr\",\"interrupted\":false,\"message\":\"哦可以。\"},{\"play\":true,\"processInfo\":{\"knowledgeRecallDetails\":[],\"toolRecallDetails\":[],\"userVariables\":{}},\"playFinish\":true,\"estimateBroadDuration\":6000.0,\"startTime\":\"12700\",\"source\":\"machine\",\"endTime\":\"18000\",\"id\":\"PFsnAaZh\",\"interrupted\":false,\"message\":\"好的，那帮我预约明天上午10点到12点的足疗，时长是两小时。\"},{\"play\":true,\"playFinish\":true,\"startTime\":\"19160\",\"source\":\"user\",\"endTime\":\"21340\",\"id\":\"wbuUrMvl\",\"interrupted\":false,\"message\":\"OK，这边留一下手机尾号。\"},{\"play\":true,\"processInfo\":{\"knowledgeRecallDetails\":[],\"toolRecallDetails\":[],\"userVariables\":{}},\"playFinish\":true,\"estimateBroadDuration\":5400.0,\"startTime\":\"23300\",\"source\":\"machine\",\"endTime\":\"28500\",\"id\":\"nRMyqCGG\",\"interrupted\":false,\"message\":\"好的，手机尾号是***********。预约成功了吗？\"},{\"play\":true,\"playFinish\":true,\"startTime\":\"29130\",\"source\":\"user\",\"endTime\":\"30690\",\"id\":\"JcygchJi\",\"interrupted\":false,\"message\":\"嗯，这边已经帮你登记了。\"},{\"play\":true,\"processInfo\":{\"knowledgeRecallDetails\":[],\"toolRecallDetails\":[],\"userVariables\":{}},\"playFinish\":true,\"estimateBroadDuration\":2600.0,\"startTime\":\"31800\",\"source\":\"machine\",\"endTime\":\"35000\",\"id\":\"JIHrRcaa\",\"interrupted\":false,\"message\":\"好的，谢谢，那先这样，拜拜\"},{\"play\":true,\"playFinish\":true,\"startTime\":\"35430\",\"source\":\"user\",\"endTime\":\"36010\",\"id\":\"dVbyoKsu\",\"interrupted\":false,\"message\":\"拜拜。\"},{\"play\":true,\"playFinish\":true,\"startTime\":\"36450\",\"source\":\"user\",\"endTime\":\"37860\",\"id\":\"WwJBfkoh\",\"interrupted\":false,\"message\":\"这种是个足疗的。\"},{\"play\":true,\"playFinish\":true,\"startTime\":\"38400\",\"source\":\"signal\",\"endTime\":\"38400\",\"id\":\"RoJcwvAW\",\"interrupted\":false,\"message\":\"machine_hangup\",\"extInfo\":{\"replyInfo\":\"hangup-normal\"}},{\"play\":true,\"playFinish\":true,\"startTime\":\"38700\",\"source\":\"signal\",\"endTime\":\"38700\",\"id\":\"YwUKljiP\",\"interrupted\":false,\"message\":\"user_hangup\"},{\"play\":true,\"playFinish\":true,\"startTime\":\"38420\",\"source\":\"user\",\"endTime\":\"38780\",\"id\":\"EkOiNbiG\",\"interrupted\":false,\"message\":\"你好。\"}],\"tags\":[],\"dialogType\":\"人机对话\",\"botGroupId\":\"60ce50f5-1378-4673-9c24-3c69d251cca1\",\"extRes\":[],\"traceInfo\":[],\"abTestRecordInfo\":{\"abInfo\":{\"llmBargeInABTest\":\"LLM_BARGE_IN_FLOW\"}},\"speechType\":\"stream\",\"rank\":\"A\",\"dialogInfo\":{},\"qaPair\":[],\"abAsrType\":\"0\",\"abNluType\":\"default\",\"taskBotType\":\"llmEndToEnd\",\"botGroupName\":\"大模型机器人\"}";
    }

    private Pair<Long, Long> calculate(String dialogRawData) {
        CallDialogRawData callDialogRawData = JsonCodec.decode(dialogRawData, CallDialogRawData.class);
        if (callDialogRawData == null || CollectionUtils.isEmpty(callDialogRawData.getHistory())) {
            return Pair.of(0L, 0L);
        }

        StringBuffer callDialog = new StringBuffer(StringUtils.EMPTY);
        long totalDelay = 0L;
        long cnt = 0;
        for (int i = 0; i < callDialogRawData.getHistory().size() - 1; i++) {
            CallDialogMessage callDialogMessage = callDialogRawData.getHistory().get(i);
            CallDialogMessage nextCallDialogMessage = callDialogRawData.getHistory().get(i + 1);
            if (callDialogMessage == null || StringUtils.isEmpty(callDialogMessage.getMessage())) {
                continue;
            }
            if (!callDialogMessage.getSource().equals("user") && !callDialogMessage.getSource().equals("machine")) {
                continue;
            }

            if (callDialogMessage.getSource().equals("user") && nextCallDialogMessage.getSource().equals("machine")) {
                totalDelay += (NumberUtils.toLong(nextCallDialogMessage.getStartTime()) - NumberUtils.toLong(callDialogMessage.getEndTime()));
                cnt++;
            }

        }

        return Pair.of(totalDelay, cnt);
    }
}
