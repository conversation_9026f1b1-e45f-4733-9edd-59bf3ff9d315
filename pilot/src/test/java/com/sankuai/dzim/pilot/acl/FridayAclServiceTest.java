package com.sankuai.dzim.pilot.acl;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.dianping.vc.sdk.concurrent.threadpool.ExecutorServices;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.mtrace.sample.rate.RateLimiter;
import com.sankuai.dzim.pilot.BaseTest;
import com.sankuai.dzim.pilot.chain.data.AIAnswerData;
import com.sankuai.dzim.pilot.chain.data.AIServiceContext;
import com.sankuai.dzim.pilot.chain.enums.AIServiceExtraKeyEnum;
import com.sankuai.dzim.pilot.domain.memory.data.QueryMemoryContext;
import com.sankuai.dzim.pilot.domain.memory.impl.BeamChatMemoryProvider;
import com.sankuai.dzim.pilot.domain.message.AssistantMessage;
import com.sankuai.dzim.pilot.domain.message.Message;
import com.sankuai.dzim.pilot.domain.message.UserMessage;
import com.sankuai.dzim.pilot.gateway.api.assistant.data.SendBeamMessageReq;
import com.sankuai.dzim.pilot.process.localplugin.data.UserContext;
import com.sankuai.dzim.pilot.utils.AIChatUtil;
import com.sankuai.dzim.pilot.utils.ExcelUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

@Slf4j
public class FridayAclServiceTest extends BaseTest {

    @Autowired
    private FridayAclService fridayAclService;

    @Autowired
    private AIChatUtil aiChatUtil;

    @Autowired
    private BeamChatMemoryProvider beamChatMemoryProvider;



    private ExecutorService executor = ExecutorServices.forThreadPoolExecutor("GENERATE_INTENTION_DATA", 20, 200);

    @Test
    public void test_generateIntentionDataset() {
        List<IntentionData> existIntentionDataList = ExcelUtils.readFromExcel("/Users/<USER>/Documents/意图理解测评集_扩充集_sonnet-4_v4.xlsx", IntentionData.class);
        List<IntentionData> intentionDataList = ExcelUtils.readFromExcel("/Users/<USER>/Documents/意图理解测评集.xlsx", IntentionData.class);
        int threadNum = 1;
        double rps = 3 / 60.0;
        RateLimiter rateLimiter = RateLimiter.create(rps);
        List<List<IntentionData>> intentionDataPartition = Lists.partition(intentionDataList, Math.max(1, intentionDataList.size() / threadNum));
        List<CompletableFuture<List<IntentionData>>> futures = Lists.newArrayList();
        for (List<IntentionData> list: intentionDataPartition) {
            CompletableFuture<List<IntentionData>> future = CompletableFuture.supplyAsync(() -> batch_generate(list, rateLimiter, existIntentionDataList), executor);
            futures.add(future);
        }

        List<IntentionData> aiAnswerDataList = futures.stream()
                .map(CompletableFuture::join)
                .flatMap(List::stream)
                .collect(Collectors.toList());
        ExcelUtils.exportExcel("/Users/<USER>/Documents/意图理解测评集_扩充集_sonnet-4_v4_fix.xlsx", aiAnswerDataList, IntentionData.class);
    }

    private List<IntentionData> batch_generate(List<IntentionData> intentionDataList, RateLimiter rateLimiter, List<IntentionData> existIntentionDataList) {
        List<IntentionData> result = Lists.newArrayList();

        for (IntentionData intentionData : intentionDataList) {
            SendBeamMessageReq sendBeamMessageReq = JsonCodec.decode(intentionData.getContext(), SendBeamMessageReq.class);
            UserContext userContext = new UserContext();
            userContext.setChatGroupId(1829L);
            Map<String, Object> extraInfo = Maps.newHashMap();
            sendBeamMessageReq.getContext().getChat_history().stream().forEach(e -> e.setName("fwls_agent"));
            extraInfo.put(AIServiceExtraKeyEnum.BEAM_REQUEST_CONTEXT.getKey(), sendBeamMessageReq);
            extraInfo.put(AIServiceExtraKeyEnum.USER_CONTEXT.getKey(), userContext);

            AIServiceContext context = new AIServiceContext();
            context.setExtraInfo(extraInfo);
            List<Message> messages = beamChatMemoryProvider.getMemory(QueryMemoryContext.builder().aiServiceContext(context).limit(20).build());
            StringBuffer sb = new StringBuffer();
            for (Message message : CollectionUtils.emptyIfNull(messages)) {
                if (message instanceof UserMessage) {
                    sb.append("user:").append(message.getContent()).append("\n");
                }
                if (message instanceof AssistantMessage) {
                    sb.append("assistant:").append(message.getContent().replace("\n\n", "\n")).append("\n");
                }
            }

            String input = "用户query：" + sendBeamMessageReq.getOriginal_user_input().getContent() + "\n\n" + "历史聊天记录：\n" + sb.toString();
            intentionData.setUserQuery(sendBeamMessageReq.getOriginal_user_input().getContent());
            intentionData.setHistory(sb.toString());
            boolean processed = CollectionUtils.emptyIfNull(existIntentionDataList).stream()
                    .filter(data -> data.getUserQuery().equals(intentionData.getUserQuery()) && StringUtils.defaultString(data.getHistory()).equals(StringUtils.defaultString(intentionData.getHistory())))
                    .findAny().isPresent();
            if (processed) {
                continue;
            }
            rateLimiter.acquire();
            AIAnswerData answerData = null;
            try {
                answerData = aiChatUtil.chat("intention_data_build", input, extraInfo);
            } catch (Exception e) {
                log.error("build data error, query = {}", sendBeamMessageReq.getOriginal_user_input().getContent());
                System.out.println("chat error, query = " + sendBeamMessageReq.getOriginal_user_input().getContent());
                continue;
            }
            adjustAnswerJson(answerData);
            IntentionAnswer intentionAnswer = JsonCodec.decode(answerData.getAnswer(), IntentionAnswer.class);
            if (intentionAnswer == null) {
                System.out.println("decode error, query = " + sendBeamMessageReq.getOriginal_user_input().getContent());
                continue;
            }
            System.out.println("处理完：query = " + intentionData.getUserQuery());
            intentionData.setRewriteUserQuery(intentionData.getUserQuery());
            intentionData.setRewriteHistory(intentionData.getHistory());

            fillResult(result, intentionAnswer, intentionData);
            result.add(intentionData);
        }

        return result;
    }

    private void adjustAnswerJson(AIAnswerData answerData) {
        String answer = answerData.getAnswer();
        if (answer.startsWith("```json")) {
            answer = answer.replace("```json", "");
            answer = answer.replace("```", "");
            answer = answer.trim();
            answerData.setAnswer(answer);
        }
    }

    private void fillResult(List<IntentionData> result, IntentionAnswer intentionAnswer, IntentionData intentionData) {
        for (String query: intentionAnswer.getQueryList()) {
            for (Map<String, List<Map<String, String>>> dialog: intentionAnswer.getDialogList()) {
                IntentionData data = new IntentionData();
                data.setUserQuery(intentionData.getUserQuery());
                data.setHistory(intentionData.getHistory());
                data.setToolName(intentionData.getToolName());
                data.setToolParam(intentionData.getToolParam());

                data.setRewriteUserQuery(query);
                String history = intentionData.getHistory();
                for (Map<String, String> rewriteMap: dialog.get("dialog")) {
                    history = history.replace(rewriteMap.get("originContent"), rewriteMap.get("content"));
                }
                data.setRewriteHistory(history);
                result.add(data);
            }
        }
    }


}
