package com.sankuai.dzim.pilot.domain.memroy;

import com.sankuai.dzim.pilot.BaseTest;
import com.sankuai.dzim.pilot.domain.memory.data.QueryMemoryContext;
import com.sankuai.dzim.pilot.domain.memory.impl.ChatMemoryProvider;
import com.sankuai.dzim.pilot.domain.message.Message;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

import java.util.List;

public class ChatMemoryProviderTest extends BaseTest {

    @Autowired
    private ChatMemoryProvider chatMemoryProvider;

    @Test
    public void getMemory() {
        QueryMemoryContext queryMemoryContext = new QueryMemoryContext();
        queryMemoryContext.setImMerchantId("s2210434");
        queryMemoryContext.setImClientId("u9000000000019035648");
        queryMemoryContext.setLimit(10);
//        queryMemoryContext.setLastMessageId(2166824355L);
        List<Message> memory = chatMemoryProvider.getMemory(queryMemoryContext);
        Assert.isTrue(memory.size() > 0, "error");
    }
}