package com.sankuai.dzim.pilot.buffer.stream.build.replacetagext;

import com.sankuai.dzim.pilot.BaseTest;
import com.sankuai.dzim.pilot.buffer.stream.build.replacetagexpt.SearchTagReplaceExt;
import com.sankuai.dzim.pilot.buffer.stream.build.replacetagexpt.data.ReplaceContext;
import com.sankuai.dzim.pilot.scene.data.AssistantSceneContext;
import com.sankuai.dzim.pilot.utils.context.RequestContext;
import com.sankuai.dzim.pilot.utils.context.RequestContextConstants;
import org.apache.commons.math3.util.Pair;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class SearchTagReplaceExtTest extends BaseTest {

    @Autowired
    private SearchTagReplaceExt searchTagReplaceExt;

    @Test
    public void replaceTest() {
        RequestContext.init();
        AssistantSceneContext assistantSceneContext = new AssistantSceneContext();
        assistantSceneContext.setAssistantType(1);
        RequestContext.setAttribute(RequestContextConstants.ASSISTANT_CONTEXT, assistantSceneContext);
        ReplaceContext replaceContext = new ReplaceContext();
        replaceContext.setContext(assistantSceneContext);
        replaceContext.setTagContent("**红蓝光+光子**");
        replaceContext.setData("**红蓝光+光子**");
        replaceContext.setTag(Pair.create("**", "红蓝光+光子"));
        String replace = searchTagReplaceExt.replace(replaceContext);
        System.out.println(replace);
    }
}
