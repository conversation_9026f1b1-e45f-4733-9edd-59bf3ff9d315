package com.sankuai.dzim.pilot.dal.repository;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.dzim.pilot.BaseTest;
import com.sankuai.dzim.pilot.api.enums.ProductTypeEnum;
import com.sankuai.dzim.pilot.dal.entity.ProductKnowledgeEntity;
import com.sankuai.dzim.pilot.dal.respository.ProductKnowledgeRepositoryService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.LongStream;

public class ProductKnowledgeRepositoryTest extends BaseTest {

    @Autowired
    private ProductKnowledgeRepositoryService productKnowledgeRepository;

    @Test
    public void addBizKnowledge() {
        ProductKnowledgeEntity entity = new ProductKnowledgeEntity();
        entity.setShopId(123);
        entity.setProductType(1);
        entity.setSubjectId("123123");
        entity.setTitle("洗涤护理-任意洗-衣服鞋子任洗");
        entity.setDescription("");
        long row = productKnowledgeRepository.addProductKnowledge(entity);
        Assert.isTrue(row > 1, "添加商品知识失败");
    }

    @Test
    public void batchAddProductKnowledgeByShopId() {
        int row = productKnowledgeRepository.batchAddProductKnowledgeByShopId(2210434L, 100);
        Assert.isTrue(row > 0, "批量添加商品知识失败");
    }

    @Test
    public void delete() {
        List<Long> partitionKnowledgeIds = LongStream.range(365, 368).boxed().collect(Collectors.toList());
        int row = productKnowledgeRepository.batchDeleteProductKnowledge(partitionKnowledgeIds);
        Assert.isTrue(row > 0, "批量删除商品知识失败");
    }

    @Test
    public void update() {
        boolean row = productKnowledgeRepository.updateProductKnowledge(2210434L, 100);
        Assert.isTrue(row, "批量更新商品知识失败");
    }

    @Test
    public void searchBizKnowledge() {
        List<ProductKnowledgeEntity> knowledges = productKnowledgeRepository.searchProductKnowledge("洗剪吹", ProductTypeEnum.DEAL.getType(), 2210434L, 100, 5);
        System.out.println(JsonCodec.encodeWithUTF8(knowledges));
    }
}
