package com.sankuai.dzim.pilot.dal.pilotdao.aiphonecall;

import com.sankuai.dzim.pilot.dal.entity.aiphonecall.AIPhoneCallTaskEntity;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class AIPhoneCallTaskDAOTest {

    @Autowired
    private AIPhoneCallTaskDAO aiPhoneCallTaskDAO;


    @Test
    void getByTaskId() {
    }

    @Test
    void insert() {
        AIPhoneCallTaskEntity entity = new AIPhoneCallTaskEntity();
        entity.setBizId("123");
        entity.setSceneType(1);
        entity.setStatus(1);
        entity.setPlatform(1);
        entity.setExtraData("{}");
        entity.setBizData("{}");
        entity.setSource(2);
        entity.setAddTime(new Date());
        entity.setUpdateTime(new Date());
        aiPhoneCallTaskDAO.insert(entity);
    }
}