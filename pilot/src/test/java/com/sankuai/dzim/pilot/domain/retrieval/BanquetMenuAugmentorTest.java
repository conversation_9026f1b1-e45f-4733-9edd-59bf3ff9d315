package com.sankuai.dzim.pilot.domain.retrieval;

import com.sankuai.dzim.pilot.BaseTest;
import com.sankuai.dzim.pilot.chain.enums.AIServiceExtraKeyEnum;
import com.sankuai.dzim.pilot.domain.retrieval.data.RetrievalContext;
import com.sankuai.dzim.pilot.domain.retrieval.impl.BanquetMenuAugmentor;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.junit.platform.commons.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

public class BanquetMenuAugmentorTest extends BaseTest {

    @Autowired
    private BanquetMenuAugmentor banquetMenuAugmentor;

    @Test
    public void test_singleBanquetMenuInfoSearch() {
        RetrievalContext retrievalContext = new RetrievalContext();
        retrievalContext.setQuery("测试数据检索");
        retrievalContext.addExtraInfo(AIServiceExtraKeyEnum.DP_SHOP_ID.getKey(), "15958919");
        String res = banquetMenuAugmentor.retrieval(retrievalContext);
        Assert.assertTrue(StringUtils.isNotBlank(res));
    }
}
