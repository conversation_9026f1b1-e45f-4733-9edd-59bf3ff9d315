package com.sankuai.dzim.pilot.buffer.core;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dzim.pilot.BaseTest;
import com.sankuai.dzim.pilot.buffer.enums.BufferItemTypeEnum;
import com.sankuai.dzim.pilot.buffer.stream.vo.BufferMergedVO;
import com.sankuai.dzim.pilot.buffer.stream.vo.StreamEventVO;
import com.sankuai.dzim.pilot.utils.context.RequestContext;
import com.sankuai.dzim.pilot.utils.context.RequestContextConstants;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @since 2024/8/1 10:28
 */
public class PilotBufferConsumeTest extends BaseTest {

    @Autowired
    private PilotBufferService pilotBufferService;

    @Test
    public void testConsume() {
        RequestContext.init();
        RequestContext.setAttribute(RequestContextConstants.SSE_EMITTER, new SseEmitter());
        PilotBuffer pilotBuffer = pilotBufferService.createAndConsume();
        List<PilotBufferItemDO> pilotBufferItemDOs = buildBufferItemList();
        pilotBuffer.writeBufferData(pilotBufferItemDOs);
        pilotBuffer.finishBufferConsume();
        BufferMergedVO bufferMerged = pilotBuffer.getBufferMerged();
        System.out.println(JsonCodec.encodeWithUTF8(bufferMerged.getStreamEventVOs()));
        Assert.assertTrue(CollectionUtils.isNotEmpty(bufferMerged.getStreamEventVOs()));
    }

    private List<PilotBufferItemDO> buildBufferItemList() {
        PilotBufferItemDO item1 = new PilotBufferItemDO();
        item1.setType(BufferItemTypeEnum.LOADING_STATUS.getType());
        item1.setData("我正在检索医美知识库");
        PilotBufferItemDO item2 = new PilotBufferItemDO();
        item2.setType(BufferItemTypeEnum.LOADING_STATUS.getType());
        item2.setData("检索完毕");
        PilotBufferItemDO item3 = new PilotBufferItemDO();
        item3.setType(BufferItemTypeEnum.MAIN_TEXT.getType());
        item3.setData("基于4篇网络文章查询得到以下内容");
        PilotBufferItemDO item4 = new PilotBufferItemDO();
        item4.setType(BufferItemTypeEnum.MAIN_TEXT.getType());
        item4.setData("<search>sourcekey</search>");
        Map<String, Object> extra4 = Maps.newHashMap();
        extra4.put("sourceContent", "基于公开信息收集整理");
        item4.setExtra(extra4);
        PilotBufferItemDO item5 = new PilotBufferItemDO();
        item5.setType(BufferItemTypeEnum.MAIN_TEXT.getType());
        item5.setData("这家门店<");
        PilotBufferItemDO item6 = new PilotBufferItemDO();
        item6.setType(BufferItemTypeEnum.MAIN_TEXT.getType());
        item6.setData("shopId");
        PilotBufferItemDO item7 = new PilotBufferItemDO();
        item7.setType(BufferItemTypeEnum.MAIN_TEXT.getType());
        item7.setData(">1244");
        PilotBufferItemDO item8 = new PilotBufferItemDO();
        item8.setType(BufferItemTypeEnum.MAIN_TEXT.getType());
        item8.setData("56312<");
        PilotBufferItemDO item9 = new PilotBufferItemDO();
        item9.setType(BufferItemTypeEnum.MAIN_TEXT.getType());
        item9.setData("/shopId>");
        PilotBufferItemDO item10 = new PilotBufferItemDO();
        item10.setType(BufferItemTypeEnum.MAIN_TEXT.getType());
        item10.setData("很不错");
        List<PilotBufferItemDO> written = Lists.newArrayList(item1, item2, item3, item4, item5, item6, item7, item8, item9, item10);
        return written;
    }


}