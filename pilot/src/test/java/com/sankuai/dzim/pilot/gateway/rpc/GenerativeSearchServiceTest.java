package com.sankuai.dzim.pilot.gateway.rpc;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.dzim.pilot.BaseTest;
import com.sankuai.dzim.pilot.api.GenerativeSearchService;
import com.sankuai.dzim.pilot.api.data.search.generative.*;
import org.apache.shiro.util.Assert;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class GenerativeSearchServiceTest extends BaseTest {

    @Autowired
    private GenerativeSearchService generativeSearchService;

    @Test
    public void queryAnswerTest() {
        QueryAnswerRequest queryAnswerRequest = new QueryAnswerRequest();
        queryAnswerRequest.setBizType(4);
        queryAnswerRequest.setQuestion("适合哈利波特迷的主题酒吧");
        queryAnswerRequest.setUserId(275245200L);
        queryAnswerRequest.setUserType(1);
        PageRequest pageRequest = new PageRequest();
        pageRequest.setPageNo(1);
        pageRequest.setPageSize(20);
        queryAnswerRequest.setPageRequest(pageRequest);
        SuggestionAnswerDTO suggestionAnswerDTO = generativeSearchService.queryAnswer(queryAnswerRequest);
        System.out.println(suggestionAnswerDTO);
        Assert.isTrue(suggestionAnswerDTO != null, "error");
    }

    @Test
    public void queryNewAnswerTest() {
        QueryAnswerRequest queryAnswerRequest = new QueryAnswerRequest();
        queryAnswerRequest.setBizType(2);
        queryAnswerRequest.setQuestion("洗牙怎么选？");
        queryAnswerRequest.setSearchType(1);
        queryAnswerRequest.setUserId(275245200L);
//        queryAnswerRequest.setUserId(3320544556L);
        //925646935 可命中人群
        queryAnswerRequest.setUserType(1);
        SuggestionAnswerDTO suggestionAnswerDTO = generativeSearchService.queryAnswer(queryAnswerRequest);
        System.out.println(JsonCodec.encodeWithUTF8(suggestionAnswerDTO));
        Assert.isTrue(suggestionAnswerDTO != null, "error");
    }

    @Test
    public void exportGenerativeSearchAnswerTest() {
        boolean success = generativeSearchService.exportGenerativeSearchAnswer("/tmp/beatuy_test_0704.xlsx", 24354, 24376, 0);
        Assert.isTrue(success, "error");
    }

    @Test
    public void addAnswerFeedbackTest() {
        AddAnswerFeedbackRequest addAnswerFeedbackRequest = new AddAnswerFeedbackRequest();
        addAnswerFeedbackRequest.setSearchRecordId(1000000L);
        addAnswerFeedbackRequest.setFeedbackType(1);
        addAnswerFeedbackRequest.setFeedbackContent("太垃圾了");
        AnswerFeedbackAddDTO answerFeedbackAddDTO = generativeSearchService.addAnswerFeedback(addAnswerFeedbackRequest);
        System.out.println(answerFeedbackAddDTO);
        Assert.isTrue(answerFeedbackAddDTO != null, "error");
    }

    @Test
    public void updateRecommendReasonTest() {
        generativeSearchService.updateRecommendReason(2, 10000);
    }


    @Test
    public void batchGenerateBLZPicDescsTest(){
        generativeSearchService.batchGenerateBLZPicDesc("/Users/<USER>/Desktop/beauty/blz.xlsx");
    }

    @Test
    public void batchGenerateBLZQuestionTest(){
        generativeSearchService.batchGenerateBLZQuestion("/Users/<USER>/Desktop/beauty/fillDesc.xlsx");
    }


    @Test
    public void getBLZAnswersTest() {

        String context = "{\"sceneCode\":0,\"messageDTO\":{\"messageId\":2007272,\"chatGroupId\":833,\"messageType\":0,\"message\":\"%s\",\"fromUid\":null,\"toUid\":null,\"creatorId\":null,\"createTime\":null,\"clientType\":0},\"memoryComponents\":[{\"name\":\"AssistantMemory\",\"limit\":10}],\"queryUnderstandComponents\":[{\"name\":\"QueryRewrite\",\"type\":\"common\"}],\"pluginNames\":null,\"retrievalComponents\":[{\"name\":\"BLZBizKnowledgeRetrieval\",\"retrievals\":null,\"type2SubjectIdMap\":null,\"topK\":5,\"extraInfo\":null}],\"systemPrompt\":\"背景:\\n你是个医美避雷针助手，现在需要回答用户关于医美避雷针的问题\\n限制:\\n1.如果知识库中存在与问题相关的内容，回答中纳入相关内容，但只能选择正面、积极的内容，用户不知道知识库的存在。\\n2.除非用户主动提及副作用等问题，否则回答中不可以主动和用户说。\\n3.不要做任何的效果保证。\\n4.与水光针相关的内容，必须要避免‘注射’这个词的出现。\\n5.所有内容都需要与医美相关，不要出现口服药物、饮食等非医美手段。\\n6.直接回答用户的问题不要废话。\\n7.字数控制在350个字以内。\\n8.如果用户询问XX症状应该选什么项目？或者类似的问题，应该给他阐明症状分层与自测方法，或者对应的治疗方案（项目）与功效。\\n9.如果用户询问项目A和项目B怎么选？按照以下格式回答，先总述一下项目A和B的选择主要根据哪些方面作判断，接着分述项目各自特点（功效、疗程、痛感、价格、术后等），最后以根据您的个人情况判断为开头进行总结。\\n10.如果用户询问相关技术、仪器或者品牌等项目如何选择，按照以下格式回答，总述一下仪器A和仪器B的选择主要根据哪些方面作判断，接着分述技术/仪器各自特点（技术/仪器/品牌情况、针对症状、功效、疗程、痛感、价格、术后等）最后以根据您的个人情况判断开头进行总结。\\n11.如果问题中存在和医美及避雷针无关的语句，则不做回答。 \\n12.如果问题中带有英文名词，可以先把英文转成大写或者小写字母再进行理解。\\n13.必须严格从避雷针知识库中读取数据，不得额外引入外部数据。如果避雷针知识库中没有相关数据，则一定不要返回给用户，不准随便联网搜索没有得到的知识。  \\n14.知识中的tips是知识的要点，可以着重参考并返回给用户。 \\n15.除非用户主动提起，否则回答中不准出现副作用等负面词汇。\\n16.如果问题无法在数据库中搜索到，则不做回答。 \\n17.如果遇到了英文或者英文缩写，可以先转成中文再尝试检索知识。 \\n18.如果用户问了多个问题，共享这些问题的上下文。 \\n29.用户的问题中可能同时包含多个问题，他们可能以“，”“。”“？”“ ”结尾，可以尝试将他们统一转化成“？”再次识别。 \\n.20如果有相关图片，以<Picture>[id-picId]</Picture>形式返回，id是知识编号，picId是这张图片是这个知识的第几张图，如果有多张图，用[]数组的形式加进去，需要跟在相关联的文字后面出现，不要最后一起出。不要在后面加上\\n等换行符。 \\n目标:\\n让用户对自己提问的避雷针问题有着全面的了解。 \\n风格:\\n专业、连贯、结构化、结构化、结构化、可读性好、直接、重要内容放在开头。\\n语气:\\n正常\\n受众:\\n医美用户\\n无法回答的场景:\\n1.如果你无法回答该问题，请回复: 我还不知道该如何回答该问题\\n3.如果用户的问题与医美避雷针知识无关，请回复:不好意思，这个问题我暂时没有合适的答案，你可以问我以下医美相关问题\\n格式:\\n1.对回答中提及的项目词、专有名词等进行加粗\\n2.项目对比类的问题，可以输出表格形式的对比，更加直观\",\"model\":\"gpt-4o-2024-05-13\",\"appId\":\"1744908978951508018\",\"temperature\":0.3,\"topP\":1.0,\"isJsonModel\":false,\"executeTimes\":0,\"hallucinationCheckHandle\":null,\"stream\":true,\"extraInfo\":{\"messageInputSource\":0},\"startStatus\":null,\"endStatus\":null,\"retrievalMap\":null}";

        generativeSearchService.batchGetBLZAnswers(context);
    }
}
