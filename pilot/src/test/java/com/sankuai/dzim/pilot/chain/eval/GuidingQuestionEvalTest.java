package com.sankuai.dzim.pilot.chain.eval;

import com.sankuai.dzim.pilot.BaseTest;
import com.sankuai.dzim.pilot.chain.eval.data.EvalTaskRequest;
import com.sankuai.dzim.pilot.chain.eval.data.EvalTaskResult;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

/**
 * @author: zhouyibing
 * @date: 2025/4/17
 */
public class GuidingQuestionEvalTest extends BaseTest {

    @Autowired
    private EvalTaskService evalTaskService;

    @Test
    public void execute() {
        EvalTaskRequest request = new EvalTaskRequest();
        request.setFilePath("/Users/<USER>/Downloads/引导语生成评测集.xlsx");
        request.setMetric("GuidingQuestionGenerate");
        request.setAiServiceConfig("{\n" +
                "    \"aiServiceType\": 3,\n" +
                "    \"memoryComponents\": null,\n" +
                "    \"retrievalComponents\": null,\n" +
                "    \"pluginNames\": null,\n" +
                "    \"hallucinationHandle\": null,\n" +
                "    \"defaultAnswer\": null,\n" +
                "    \"systemPrompt\": \"你是一个电商平台的智能推荐助手，擅长根据用户特征和行为生成高点击率的个性化引导语。请根据以下信息生成一句吸引用户点击的引导文案：\\n\\n输入数据：\\n\\n用户画像：\\n${userPortrait}\\n\\n实时行为序列\\n${behaviorSequence}\\n\\n输出要求：\\n\\n内容：推测用户最关心的品类、项目和要求\\n语气：口语化、带紧迫感或好奇心\\n\\n示例输出：\\n\\\"想找附近按摩店放松一下？让我来帮你～\\\"\\n\\\"在找附近环境好的精油SPA店吗？让我来帮你～\\\"}\",\n" +
                "    \"model\": \"gpt-4o-2024-11-20\",\n" +
                "    \"appId\": \"1744908978951508018\",\n" +
                "    \"temperature\": 0,\n" +
                "    \"topP\": 1,\n" +
                "    \"isJsonModel\": false,\n" +
                "    \"maxTokens\": 4096\n" +
                "}");
        request.setEvaluator("LLMEvaluator");
        EvalTaskResult taskResult = evalTaskService.execute(request);
        System.out.println(taskResult);
        Assert.isTrue(taskResult != null);
    }
}
