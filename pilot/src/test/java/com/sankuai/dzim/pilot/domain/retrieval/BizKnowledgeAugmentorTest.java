package com.sankuai.dzim.pilot.domain.retrieval;

import com.sankuai.dzim.pilot.BaseTest;
import com.sankuai.dzim.pilot.api.enums.BizKnowledgeTypeEnum;
import com.sankuai.dzim.pilot.domain.retrieval.data.RetrievalContext;
import com.sankuai.dzim.pilot.domain.retrieval.impl.BizKnowledgeAugmentor;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;

public class BizKnowledgeAugmentorTest extends BaseTest {

    @Autowired
    private BizKnowledgeAugmentor bizKnowledgeAugmentor;

    @Test
    public void retrievalTest() {
        RetrievalContext retrievalContext = new RetrievalContext();
        retrievalContext.setQuery("黄金微针");
        retrievalContext.setTopK(5);
        Map<Integer, String> type2SubjectIdMap = new HashMap<>();
        type2SubjectIdMap.put(BizKnowledgeTypeEnum.YIMEI_CONSULTANT.getType(), "");
        type2SubjectIdMap.put(BizKnowledgeTypeEnum.YIMEI_BLZ.getType(), "");
        type2SubjectIdMap.put(BizKnowledgeTypeEnum.YIMEI_WIKI.getType(), "");
        retrievalContext.setType2SubjectIdMap(type2SubjectIdMap);
        String retrieval = bizKnowledgeAugmentor.retrieval(retrievalContext);
        System.out.println(retrieval);
    }
}
