package com.sankuai.dzim.pilot.gateway.job;

import com.google.common.collect.Maps;
import com.sankuai.dzim.pilot.BaseTest;
import com.sankuai.dzim.pilot.chain.eval.data.EvalCase;
import com.sankuai.dzim.pilot.chain.eval.data.EvalCaseResult;
import com.sankuai.dzim.pilot.utils.ExcelUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

public class LLMEvalJobTest extends BaseTest {

    @Test
    public void stat_LLMResult() {
        List<EvalCaseResult> evalCaseResultList = ExcelUtils.readFromExcel("/Users/<USER>/Documents/intention_data_sonnet-4_v4_result_LLMCommon.xlsx", EvalCaseResult.class);
        Map<String, Map<String, Integer>> intentionResultMap = Maps.newHashMap();

        for (EvalCaseResult evalCaseResult: evalCaseResultList) {
            String intention = evalCaseResult.getTargetOutput().split("_")[0];
            Map<String, Integer> resultMap = intentionResultMap.getOrDefault(intention, Maps.newHashMap());

            String result = String.valueOf(evalCaseResult.isFunctionCallingAccuracy());
            resultMap.put(result, resultMap.getOrDefault(result, 0) + 1);
            intentionResultMap.put(intention, resultMap);
        }

        int totalTrueCount = 0, totalFalseCount = 0;
        for (Map.Entry<String, Map<String, Integer>> entry: intentionResultMap.entrySet()) {
            String intention = entry.getKey();
            Map<String, Integer> resultMap = entry.getValue();

            int trueCount = resultMap.getOrDefault("true", 0);
            int falseCount = resultMap.getOrDefault("false", 0);
            totalTrueCount += trueCount;
            totalFalseCount += falseCount;

            System.out.println(intention + ":" + trueCount + "/" + (trueCount+falseCount)+ ", acc = " + (trueCount/ (trueCount + falseCount)));
        }
        System.out.println(totalTrueCount + "/" + (totalTrueCount + totalFalseCount) + ", acc = " + (totalTrueCount/ (totalTrueCount + totalFalseCount)));

    }
}
