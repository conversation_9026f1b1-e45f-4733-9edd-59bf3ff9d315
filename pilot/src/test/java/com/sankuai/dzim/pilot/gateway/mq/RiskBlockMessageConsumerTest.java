package com.sankuai.dzim.pilot.gateway.mq;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.dzim.message.dto.AuditResDTO;
import com.sankuai.dzim.pilot.BaseTest;
import com.sankuai.dzim.pilot.gateway.api.assistant.data.SendMessageReq;
import com.sankuai.dzim.pilot.gateway.mq.consumer.RiskBlockMessageConsumer;
import com.sankuai.dzim.pilot.gateway.mq.data.RiskBlockMessageData;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class RiskBlockMessageConsumerTest extends BaseTest {

    @Autowired
    private RiskBlockMessageConsumer riskBlackMessageConsumer;

    @Test
    public void recvMessageTest(){
        RiskBlockMessageData riskBlockMessageData = new RiskBlockMessageData();
        SendMessageReq sendMessageReq = new SendMessageReq();
        sendMessageReq.setImUserId("1");
        sendMessageReq.setAssistantType(5);
        riskBlockMessageData.setReq(sendMessageReq);

        AuditResDTO auditResDTO = new AuditResDTO();
        auditResDTO.setCode(0);
        riskBlockMessageData.setAudit(auditResDTO);


        riskBlackMessageConsumer.recvMessage(JsonCodec.encodeWithUTF8(riskBlockMessageData));
    }
}
