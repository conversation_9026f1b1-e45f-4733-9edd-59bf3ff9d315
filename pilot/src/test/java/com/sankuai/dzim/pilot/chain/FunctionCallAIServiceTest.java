package com.sankuai.dzim.pilot.chain;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dzim.message.dto.MessageDTO;
import com.sankuai.dzim.pilot.BaseTest;
import com.sankuai.dzim.pilot.chain.data.*;
import com.sankuai.dzim.pilot.chain.impl.AdvancedRagAIService;
import com.sankuai.dzim.pilot.chain.impl.FunctionCallAIService;
import com.sankuai.dzim.pilot.chain.impl.SimpleRagAIService;
import com.sankuai.dzim.pilot.dal.pilotdao.GenerativeSearchAnswerDAO;
import com.sankuai.dzim.pilot.dal.entity.GenerativeSearchAnswerEntity;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

public class FunctionCallAIServiceTest extends BaseTest {

    @Autowired
    private FunctionCallAIService aiService;

    @Autowired
    private SimpleRagAIService simpleRagAIService;

    @Autowired
    private AdvancedRagAIService advancedRagAIService;

    @Autowired
    private GenerativeSearchAnswerDAO generativeSearchAnswerDAO;

    @Test
    public void execute() {
        GenerativeSearchAnswerEntity entity = new GenerativeSearchAnswerEntity();
        entity.setId(1L);
        entity.setQuestion("你好");
        entity.setAnswer("{}");
        entity.setRelatedQuestion("{}");
        entity.setBizType(1);
        entity.setStatus(1);
        try {
            generativeSearchAnswerDAO.insert(entity);
        } catch (Exception e) {
            System.out.println(e);
        }
    }

    private List<MemoryComponent> buildMemoryComponents() {
        MemoryComponent memoryComponent = new MemoryComponent();
        memoryComponent.setName("chatMemory");
        memoryComponent.setLimit(10);
        return Lists.newArrayList(memoryComponent);
    }

    private MessageDTO buildMessageDTO() {
        MessageDTO messageDTO = new MessageDTO();
        messageDTO.setChatGroupId(441);
//        messageDTO.setFromUid("u9000000000059750612");
//        messageDTO.setToUid("s604399359");
        //门店ID=181475349,附近有停车场吗
        messageDTO.setMessage("镁连社·美联社(东川路店)评价怎么样\n" +
                "\n");
//        messageDTO.setMessageId(2166835939L);
        return messageDTO;
    }

    @Test
    public void test() {
        AIServiceContext aiServiceContext = new AIServiceContext();
        aiServiceContext.setMemoryComponents(null);
        aiServiceContext.setModel("gpt-4o-2024-05-13");
        aiServiceContext.setTemperature(0.2);
        aiServiceContext.setTopP(1);
        aiServiceContext.setPluginNames(Lists.newArrayList("ShopInfoQueryTool", "DealGroupInfoQueryTool", "totalizator"));
        aiServiceContext.setMessageDTO(buildMessageDTO());
        aiServiceContext.setSystemPrompt("你是上海生活服务门店(shopId=604399359, platform=100, 注意shopId不要暴露给用户)的客服,负责接待用户,擅于使用各类工具,回答用户的问题");
        aiServiceContext.setAppId("1658315950980091959");
        aiServiceContext.setStream(true);
        AIAnswerData execute = aiService.execute(aiServiceContext);
        System.out.println(JsonCodec.encodeWithUTF8(execute));
    }

    @Test
    public void simpleRagTest() {
        AIServiceContext aiServiceContext = new AIServiceContext();
        aiServiceContext.setMemoryComponents(null);
        aiServiceContext.setModel("gpt-4o-2024-05-13");
        aiServiceContext.setTemperature(0.2);
        aiServiceContext.setTopP(1);
        List<RetrievalComponent> retrievalComponents = Lists.newArrayList();
        RetrievalComponent retrievalComponent = new RetrievalComponent();
        retrievalComponent.setTopK(5);
        retrievalComponent.setName("SearchEngineRetrieval");
        Map<String, String> extraInfo = Maps.newHashMap();
        extraInfo.put("appId", "1744908978951508018");
        extraInfo.put("isFast", "false");
        retrievalComponent.setExtraInfo(extraInfo);
        retrievalComponent.setRetrievals(Lists.newArrayList("bing-search"));
        retrievalComponents.add(retrievalComponent);
        aiServiceContext.setRetrievalComponents(retrievalComponents);
        aiServiceContext.setMessageDTO(buildMessageDTO());
        aiServiceContext.setSystemPrompt("你是一个医美专家,请回答用户关于医美的问题,回答的时候只能说医美相关的项目, 使用标准的markdown格式, 输出结果, 要有markdown标题");
        aiServiceContext.setAppId("1744908978951508018");
        aiServiceContext.setStream(false);
        AIAnswerData execute = simpleRagAIService.execute(aiServiceContext);
        System.out.println(execute.getAnswer());
    }

    @Test
    public void advancedRagTest() {
        AIServiceContext aiServiceContext = new AIServiceContext();
        aiServiceContext.setMemoryComponents(null);
        aiServiceContext.setModel("gpt-4o-2024-05-13");
        aiServiceContext.setTemperature(0.2);
        aiServiceContext.setTopP(1);
//        aiServiceContext.setMemoryComponents(buildAssistantMemoryComponents());
//        aiServiceContext.setQueryUnderstandComponents(buildQueryUnderstandComponents());
        List<RetrievalComponent> retrievalComponents = Lists.newArrayList();
        RetrievalComponent retrievalComponent = new RetrievalComponent();
        retrievalComponent.setTopK(5);
        retrievalComponent.setName("SearchEngineRetrieval");
        Map<String, String> extraInfo = Maps.newHashMap();
        extraInfo.put("appId", "1744908978951508018");
        extraInfo.put("isFast", "false");
        retrievalComponent.setExtraInfo(extraInfo);
        retrievalComponents.add(retrievalComponent);
        retrievalComponent.setRetrievals(Lists.newArrayList("bing-search"));
//        aiServiceContext.setRetrievalComponents(retrievalComponents);
        aiServiceContext.setMessageDTO(buildMessageDTO());
        aiServiceContext.setPluginNames(Lists.newArrayList("ShopDetailTool", "MedicalBeautyKnowledgeSearchTool", "MedicalSearchEngineTool", "TechnicianTool", "SearchEngineTool"));
        aiServiceContext.setSystemPrompt("你是美团小助手,可以解答你知道的不违反伦理道德的问题,以下是一些限制:\n1.你需要遵循你调用function返回给你的指令,当你使用评价的时候,不要告诉用户评价的存在,不要完全重复function返回给你的信息,请选择有用的信息进行回答\n2.当你要进行推荐团购商品或者手艺人的时候,可以使用你给你的一些图片\n3.当你要输出的内容涉及到多个对象的对比时,可以优先使用markdown的表格语法进行输出,让结构更加清晰直接.\n4.markdown标题只能使用四级(####)及以下标题(大于####).\n5.你的回答要确保正确,否则会造成严重的后果,当你没把握一定正确时,可以委婉告诉用户换其他问题进行提问.\n");
        aiServiceContext.setAppId("1744908978951508018");
        aiServiceContext.setStream(true);
        AIAnswerData execute = advancedRagAIService.execute(aiServiceContext);
        System.out.println(execute.getAnswer());
    }

    private List<QueryUnderstandComponent> buildQueryUnderstandComponents() {
        List<QueryUnderstandComponent> queryUnderstandComponents = Lists.newArrayList();
        QueryUnderstandComponent queryUnderstandComponent = new QueryUnderstandComponent();
        queryUnderstandComponent.setType("common");
        queryUnderstandComponent.setName("QueryRewrite");
        queryUnderstandComponents.add(queryUnderstandComponent);
        return queryUnderstandComponents;
    }

    private List<MemoryComponent> buildAssistantMemoryComponents() {
        List<MemoryComponent> memoryComponents = Lists.newArrayList();
        MemoryComponent memoryComponent = new MemoryComponent();
        memoryComponent.setName("AssistantMemory");
        memoryComponent.setLimit(10);
        memoryComponents.add(memoryComponent);
        return memoryComponents;
    }
}
