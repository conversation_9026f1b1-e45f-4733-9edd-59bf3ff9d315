package com.sankuai.dzim.pilot.gateway.rpc;

import com.sankuai.dzim.pilot.BaseTest;
import com.sankuai.dzim.pilot.api.UserBlockService;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class UserBlockServiceTest extends BaseTest {
    @Autowired
    private UserBlockService userBlockService;

    @Test
    public void blockRpcTest() {
        List<String> list = new ArrayList<>();
        list.add("1");
        list.add("2");
        list.add("3");
        boolean b = userBlockService.blockByUserIds(list, 1, 1);
        Assert.assertTrue(b);
    }

    @Test
    public void unBlockRpcTest(){
        List<String> list = new ArrayList<>();
        list.add("1");
        list.add("2");
        boolean b = userBlockService.unBlockByUserIds(list, 1);
        Assert.assertTrue(b);
    }
}
