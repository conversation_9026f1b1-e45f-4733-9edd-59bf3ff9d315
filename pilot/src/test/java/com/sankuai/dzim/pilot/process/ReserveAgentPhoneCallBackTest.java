package com.sankuai.dzim.pilot.process;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.meituan.mtrace.Tracer;
import com.sankuai.dzim.pilot.BaseTest;
import com.sankuai.dzim.pilot.process.aiphonecall.callback.impl.ReserveAgentPhoneCallBack;
import com.sankuai.dzim.pilot.process.aiphonecall.data.AIPhoneCallBackData;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * @author: wuwen<PERSON>ang
 * @create: 2025-04-30
 * @description:
 */
@SpringBootTest
public class ReserveAgentPhoneCallBackTest extends BaseTest {

    @Autowired
    private ReserveAgentPhoneCallBack reserveAgentPhoneCallBack;

//    @Test
//    public void testComplete() {
//        AIPhoneCallBackData aiPhoneCallBackData = buildSuccessCallData();
//        Tracer.setSwimlane("3792-cxxxx");
//        reserveAgentPhoneCallBack.onComplete(aiPhoneCallBackData);
//    }

    private AIPhoneCallBackData buildSuccessCallData() {
        String json = "{\"aiPhoneCallSceneTypeEnum\":\"RESERVATION\",\"bizData\":\"{\\\"envContext\\\":{\\\"appVersion\\\":\\\"12.33.200\\\",\\\"channel\\\":\\\"IOS\\\",\\\"cityId\\\":10,\\\"deviceId\\\":\\\"00000000000001EBFCD4577404991919731E8AAA589C5A165306641745599466\\\",\\\"lat\\\":31.271485247358843,\\\"lng\\\":121.52902922443263,\\\"locationCityId\\\":10,\\\"platform\\\":2,\\\"userId\\\":5186898390},\\\"taskReserveType\\\":2,\\\"type\\\":\\\"reserveAgent\\\",\\\"userId\\\":5186898390}\",\"bizId\":\"154\",\"callDialogExtractInfo\":{\"result\":true,\"duration\":80,\"successRemark\":\"预约成功\",\"beginTime\":\"2025-5-5 18:00\"}}";
        return JsonCodec.decode(json, AIPhoneCallBackData.class);
    }
}
