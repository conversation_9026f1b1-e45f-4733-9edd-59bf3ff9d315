package com.sankuai.dzim.pilot.process;

import com.alibaba.fastjson.JSON;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.meituan.mtrace.Tracer;
import com.meituan.nibtp.trade.client.combine.bean.WebParamDTO;
import com.meituan.nibtp.trade.client.combine.requset.AgentGeneralBookingReqDTO;
import com.meituan.nibtp.trade.client.combine.response.AgentCreateOrderResDTO;
import com.sankuai.dzim.pilot.BaseTest;
import com.sankuai.dzim.pilot.acl.AgentTradeAclService;
import com.sankuai.dzim.pilot.process.aireservebook.AIReserveBookProcessService;
import com.sankuai.dzim.pilot.process.aireservebook.data.UserReserveInfo;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * @author: wuwenqiang
 * @create: 2025-04-29
 * @description:
 */
@SpringBootTest
public class AIReserveBookProcessServiceTest extends BaseTest {

    @Autowired
    private AIReserveBookProcessService aiReserveBookProcessService;

    @Autowired
    private AgentTradeAclService agentTradeAclService;

    @Test
    public void test() {
    }

//    @Test
//    public void testCancelOnlineReserve() {
//        UserReserveInfo userReserveInfo = buildUserReserveInfo();
//        try {
//            String msg = aiReserveBookProcessService.mockCancelOnlineReserve(userReserveInfo);
//            System.out.println(msg);
//        } catch (Exception e) {
//            System.out.println(e);
//        }
//    }

//    @Test
//    public void createReserve() {
//        WebParamDTO webParamDTO = buildWebParamDTO();
//        AgentGeneralBookingReqDTO agentGeneralBookingReqDTO = buildAgentGeneralBookingReqDTO();
//        try {
//            Tracer.setSwimlane("3792-cxxxx");
//            AgentCreateOrderResDTO resp = agentTradeAclService.createGeneralBookingOrder(webParamDTO, Lists.newArrayList(agentGeneralBookingReqDTO));
//            System.out.println(JSON.toJSONString(resp));
//        } catch (Exception e) {
//            System.out.println(e);
//        }
//    }
//
//    @Test
//    public void createReserveAfterOrder() {
//        WebParamDTO webParamDTO = buildWebParamDTO();
//        AgentGeneralBookingReqDTO agentGeneralBookingReqDTO = buildAgentGeneralBookingReqDTO();
//    }

    private WebParamDTO buildWebParamDTO() {
        String json = "{\"bizSpace\":5,\"channel\":1,\"cityId\":0,\"concurrenceKey\":\"5057818071\",\"dpUserId\":9000000000041522094,\"extraMap\":{\"distributionType\":\"biz_agent\"},\"latitude\":40.01350217233022,\"longitude\":116.48093856454648,\"mobile\":\"18868115977\",\"platform\":101,\"userId\":5057818071,\"version\":\"12.27.410\"}";
        return JsonCodec.decode(json, WebParamDTO.class);
    }

    private AgentGeneralBookingReqDTO buildAgentGeneralBookingReqDTO() {
        String json = "{\n" +
                "  \"poiCategoryList\": [48],\n" +
                "  \"poiId\": 307168137,\n" +
                "  \"quantity\": 1,\n" +
                "  \"projectCode\": null,\n" +
                "  \"projectName\": \"足疗按摩\",\n" +
                "  \"orderId\": null,\n" +
                "  \"contactMobileNo\": \"18868115977\",\n" +
                "  \"userRemark\": \"\",\n" +
                "  \"merchantRemark\": null,\n" +
                "  \"bookingChannel\": 2,\n" +
                "  \"arrivalTime\": 1745989200000,\n" +
                "  \"leaveTime\": 1745992800000,\n" +
                "  \"extraMap\": null\n" +
                "}";
        return JsonCodec.decode(json, AgentGeneralBookingReqDTO.class);
    }

    private UserReserveInfo buildUserReserveInfo() {
        UserReserveInfo userReserveInfo = new UserReserveInfo();
        userReserveInfo.setReserveId(123456L);
        userReserveInfo.setTaskReserveType(-1);
        return userReserveInfo;
    }
}
