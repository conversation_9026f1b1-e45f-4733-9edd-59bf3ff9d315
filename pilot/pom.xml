<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.sankuai.dzim</groupId>
        <artifactId>pilot-parent</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>pilot</artifactId>
    <version>${revision}</version>
    <packaging>jar</packaging>
    <name>pilot</name>

    <dependencies>

        <dependency>
            <groupId>com.sankuai.user.collection</groupId>
            <artifactId>coll-client</artifactId>
            <version>1.7.2</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.image</groupId>
            <artifactId>client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.horus</groupId>
            <artifactId>infra-solution-design-client</artifactId>
            <version>1.0.7</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.merchant</groupId>
            <artifactId>merchant-common-filter</artifactId>
            <version>1.1.5</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.poi</groupId>
            <artifactId>poi-rgc-api</artifactId>
            <version>0.1.38</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.poi</groupId>
            <artifactId>poi-bizhour-api</artifactId>
            <version>1.2.3</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.wmdrecsys.aigc.server</groupId>
            <artifactId>waimai_aigc_api_client</artifactId>
            <version>1.0.31</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.leads</groupId>
            <artifactId>leads-common</artifactId>
            <version>0.0.97</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.leads</groupId>
            <artifactId>leads-process-thrift</artifactId>
            <version>0.3.4</version>
            <exclusions>
                <exclusion>
                    <artifactId>hotel-resource-name-client</artifactId>
                    <groupId>com.meituan.hotel.biz</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.wpt.user.merge</groupId>
            <artifactId>user-merge-query-api</artifactId>
            <version>1.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>dp-400phone-api</artifactId>
            <version>2.4.51</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.clr.touch</groupId>
            <artifactId>clr-touch-thrift</artifactId>
            <version>0.0.20</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>call_sdk</artifactId>
            <version>3.2.12-predictResultReason</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.wpt.user.retrieve</groupId>
            <artifactId>retrieve-api</artifactId>
            <version>1.2.30</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>account-api</artifactId>
            <version>4.0.21</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.conch.certify</groupId>
            <artifactId>tokenAccessSdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.service.inf</groupId>
            <artifactId>kms-pangolin-sdk</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>kms-tls-sdk</artifactId>
                    <groupId>com.meituan.service.inf</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.meituan.service.inf</groupId>
            <artifactId>kms-tls-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.inf</groupId>
            <artifactId>idl-kms</artifactId>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15on</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.haima</groupId>
            <artifactId>haima-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.dianping.mobile</groupId>
                    <artifactId>mobile-common-api</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>servlet-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>me.xdrop</groupId>
            <artifactId>fuzzywuzzy</artifactId>
            <version>1.4.0</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.beam</groupId>
            <artifactId>beam-chat-api</artifactId>
            <version>1.0.3</version>
        </dependency>

        <!-- ES8 -->
        <dependency>
            <groupId>com.sankuai.meituan</groupId>
            <artifactId>poros-java-api-client</artifactId>
            <version>1.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan</groupId>
            <artifactId>poros-client</artifactId>
            <version>0.9.23.1-RC4</version>
        </dependency>

        <!--        电话任务client接口-->
        <dependency>
            <groupId>com.sankuai.meituan</groupId>
            <artifactId>banma_service_digit_manager_client</artifactId>
            <version>1.0.1</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.persona</groupId>
            <artifactId>persona-idl</artifactId>
            <version>1.0.68</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mpmctlive</groupId>
            <artifactId>mpmctlive-query-thrift</artifactId>
            <version>0.0.36</version>
            <exclusions>
                <exclusion>
                    <artifactId>mpmctlive-query-common</artifactId>
                    <groupId>com.sankuai.mpmctlive</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.sankuai.mpmctlive</groupId>
            <artifactId>mpmctlive-query-common</artifactId>
            <version>0.0.27</version>
        </dependency>

        <dependency>
            <groupId>com.meituan.mdp</groupId>
            <artifactId>dzviewscene-sug-merger-api</artifactId>
            <version>0.0.2</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.douhu</groupId>
            <artifactId>douhu-absdk</artifactId>
            <version>2.10.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>kafka-clients</artifactId>
                    <groupId>com.meituan.mafka</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mafka-client_2.9</artifactId>
                    <groupId>com.meituan.mafka</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.sankuai.inf.leaf</groupId>
            <artifactId>leaf-idl</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.it.sso</groupId>
            <artifactId>sso-java-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>android-json</artifactId>
                    <groupId>com.vaadin.external.google</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-pigeon</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-thrift</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-zebra</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-squirrel</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-mafka</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-cellar</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>javax.servlet-api</artifactId>
                    <groupId>javax.servlet</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-crane</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.dzim</groupId>
            <artifactId>message-api</artifactId>
            <version>0.1.6</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.dzim</groupId>
            <artifactId>message-common</artifactId>
            <version>0.0.92</version>
        </dependency>

        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>ugc-note-api</artifactId>
            <version>1.18.0</version>
        </dependency>

        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>ugc-act-report-read-api</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.piccentercloud</groupId>
            <artifactId>piccenter-display-api</artifactId>
            <version>0.3.0</version>
        </dependency>

        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>user-base-remote</artifactId>
            <version>2.1.20</version>
        </dependency>

        <dependency>
            <groupId>com.meituan.nibmp</groupId>
            <artifactId>mem-vaf-query-thrift</artifactId>
            <version>0.0.33</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.dzim</groupId>
            <artifactId>pilot-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.nibmp.dzbp</groupId>
            <artifactId>merchant-portal-api</artifactId>
            <version>0.3.63</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>gateway-framework-web</artifactId>
            <version>1.0.69</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>hibernate-validator</artifactId>
                    <groupId>org.hibernate</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jackson-mapper-asl</artifactId>
                    <groupId>org.codehaus.jackson</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>ops-remote-host</artifactId>
                    <groupId>com.dianping</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>avatar-core</artifactId>
                    <groupId>com.dianping</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>pearl</artifactId>
                    <groupId>com.sankuai.pearl</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>xercesImpl</artifactId>
                    <groupId>xerces</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>shop-api</artifactId>
                    <groupId>com.dianping</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>vc-operate-reporter-sdk</artifactId>
                    <groupId>com.dianping</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>vc-threadpool-client</artifactId>
                    <groupId>com.dianping.vc</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>aviator</artifactId>
                    <groupId>com.googlecode.aviator</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>epassport-service-client</artifactId>
                    <groupId>com.sankuai.epassport</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>merchant-portal-api</artifactId>
                    <groupId>com.sankuai.nibmp.dzbp</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>merchant-common-filter</artifactId>
                    <groupId>com.dianping.merchant</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.cip.crane</groupId>
            <artifactId>crane-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.log</groupId>
            <artifactId>scribe-log4j2</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.meituan.service.mobile.group</groupId>
            <artifactId>groupbase</artifactId>
            <version>0.8.2</version>
        </dependency>

        <dependency>
            <groupId>com.meituan.service.mobile</groupId>
            <artifactId>groupgeo</artifactId>
            <version>********</version>
            <exclusions>
                <exclusion>
                    <artifactId>xercesImpl</artifactId>
                    <groupId>xerces</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.sankuai.pearl</groupId>
            <artifactId>pearl</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>athena-bom</artifactId>
            <version>1.0.1</version>
            <exclusions>
                <exclusion>
                    <artifactId>servlet-api</artifactId>
                    <groupId>javax.servlet</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>hibernate-validator</artifactId>
                    <groupId>org.hibernate</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>athena-client</artifactId>
            <version>0.0.41</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.vc</groupId>
            <artifactId>vc-degrade-client</artifactId>
            <version>0.1.8</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>pay-common</artifactId>
                    <groupId>com.dianping</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>5.7.1</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-params</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.vintage</groupId>
            <artifactId>junit-vintage-engine</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <version>3.8.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.sankuai.vex.plus</groupId>
            <artifactId>retriever-client</artifactId>
            <version>0.0.3-UPDATE-INTERNAL</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.deal</groupId>
            <artifactId>deal-shop-api</artifactId>
            <version>2.3.18</version>
            <exclusions>
                <exclusion>
                    <artifactId>deal-common-api</artifactId>
                    <groupId>com.dianping</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.squareup.retrofit2</groupId>
            <artifactId>retrofit</artifactId>
            <version>2.9.0</version>
        </dependency>
        <dependency>
            <groupId>com.squareup.retrofit2</groupId>
            <artifactId>converter-gson</artifactId>
            <version>2.9.0</version>
        </dependency>
        <dependency>
            <groupId>com.squareup.retrofit2</groupId>
            <artifactId>converter-jackson</artifactId>
            <version>2.9.0</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan</groupId>
            <artifactId>xcontract-thrift-client</artifactId>
            <version>3.1.7.3</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.ut.toolkit</groupId>
            <artifactId>runtime</artifactId>
            <version>1.0.7</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.sankuai.sinai</groupId>
            <artifactId>sinai-api</artifactId>
            <version>1.0.52</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.general.product</groupId>
            <artifactId>client</artifactId>
            <version>1.0.49</version>
        </dependency>
        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>1.8.3</version>
        </dependency>

        <dependency>
            <groupId>com.meituan.mdp</groupId>
            <artifactId>im-account-sdk</artifactId>
            <version>1.0.1</version>
            <exclusions>
                <exclusion>
                    <artifactId>merchant-common-filter</artifactId>
                    <groupId>com.dianping.merchant</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.dianping.merchant</groupId>
            <artifactId>merchant-im-api</artifactId>
            <version>1.0.48</version>
        </dependency>

        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.guardian</groupId>
            <artifactId>sig-botdefender-core</artifactId>
            <version>1.1.15</version>
        </dependency>

        <dependency>
            <groupId>ma.glasnost.orika</groupId>
            <artifactId>orika-core</artifactId>
            <version>1.5.4</version>
        </dependency>

        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>ops-remote-host</artifactId>
            <version>1.1.0-IPV6</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.beautycontent</groupId>
            <artifactId>intention-api</artifactId>
            <version>0.0.15</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.beautycontent</groupId>
            <artifactId>beauty-tag-api</artifactId>
            <version>0.0.58</version>
            <exclusions>
                <exclusion>
                    <groupId>com.sankuai</groupId>
                    <artifactId>beautycontent.store.api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>ugc-review-api</artifactId>
            <version>3.3.14</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.general</groupId>
            <artifactId>martgeneral-recommend-api</artifactId>
            <version>1.7.4</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.dzim</groupId>
            <artifactId>consult-api</artifactId>
            <version>0.0.63</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.zdc</groupId>
            <artifactId>zdc-tag-apply-api</artifactId>
            <version>1.3.10</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.mpmctcontent</groupId>
            <artifactId>mpmctcontent-query-thrift</artifactId>
            <version>0.4.4</version>
        </dependency>

        <dependency>
            <groupId>com.dianping.poi</groupId>
            <artifactId>poi-relation-service-api</artifactId>
            <version>1.1.10</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.clr</groupId>
            <artifactId>clr-content-process-thrift</artifactId>
            <version>0.0.83</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.clr</groupId>
            <artifactId>clr-content-process-common</artifactId>
            <version>0.0.46</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.clr</groupId>
            <artifactId>clr-content-process-gateway-thrift</artifactId>
            <version>0.0.43</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.map.maf</groupId>
            <artifactId>openplatform-dependency</artifactId>
            <version>1.2.0</version>
        </dependency>

        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>openapiutil</artifactId>
            <version>0.2.1</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>tea-openapi</artifactId>
            <version>0.3.1</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.horus</groupId>
            <artifactId>horus-proxy-sdk</artifactId>
            <version>1.0.3</version>
        </dependency>

        <dependency>
            <groupId>com.dp.arts</groupId>
            <artifactId>arts-utils</artifactId>
            <version>5.0.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>mss-java-sdk-s3</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>kms-tls-sdk</artifactId>
                    <groupId>com.meituan.service.inf</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.thoughtworks.xstream</groupId>
            <artifactId>xstream</artifactId>
        </dependency>

        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>general-unified-search-api</artifactId>
            <version>1.8.14</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.vc</groupId>
            <artifactId>dztheme-deal-api</artifactId>
            <version>1.1.10</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>dztheme-shop-api</artifactId>
            <version>0.0.163</version>
        </dependency>

        <dependency>
            <groupId>martgeneral-aigc-arrange-service</groupId>
            <artifactId>martgeneral-aigc-arrange-service-api</artifactId>
            <version>2.0.24</version>
            <exclusions>
                <exclusion>
                    <groupId>com.meituan.nibhtp.os</groupId>
                    <artifactId>htp-cms-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


        <!--搜推渲染接口-->
        <dependency>
            <groupId>com.meituan.mdp</groupId>
            <artifactId>dzshoplist-aggregate-dzrender-api</artifactId>
            <version>0.0.5</version>
        </dependency>

        <!--垂搜search merge(ms)服务-->
        <dependency>
            <groupId>com.meituan.service.mobile.poi.search.rerank</groupId>
            <artifactId>poi-bound-search-merge-client</artifactId>
            <version>1.0.15</version>
        </dependency>

        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>deal-sales-display-api</artifactId>
            <version>2.1.3</version>
        </dependency>

        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>deal-sales-common-api</artifactId>
            <version>2.1.3</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.spt</groupId>
            <artifactId>spt-common-api</artifactId>
            <version>1.0.6</version>
        </dependency>

        <!-- 手艺人 -->
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>technician-vc-api</artifactId>
            <version>1.20.13</version>
            <exclusions>
                <exclusion>
                    <artifactId>mafka-kafka_2.10</artifactId>
                    <groupId>com.meituan.mafka</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.cip</groupId>
            <artifactId>pike-message-sdk</artifactId>
            <version>2.1.1-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.cip</groupId>
            <artifactId>pike-message-client</artifactId>
            <version>2.0.5-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.nibtp</groupId>
            <artifactId>trade-client</artifactId>
            <version>2.1.85</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.spt</groupId>
            <artifactId>spt-statequery-api</artifactId>
            <version>1.0.3</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.mpproduct.idservice</groupId>
            <artifactId>idservice-sdk</artifactId>
            <version>1.1.4</version>
            <exclusions>
                <exclusion>
                    <artifactId>hotel-unity-devtools</artifactId>
                    <groupId>com.sankuai.hotel</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.dianping.tpfun</groupId>
            <artifactId>sku-aggregate-api</artifactId>
            <version>1.8.114</version>
            <exclusions>
                <exclusion>
                    <groupId>com.dianping.piccentercloud</groupId>
                    <artifactId>piccenter-display-api</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.tpfun</groupId>
            <artifactId>sku-common</artifactId>
            <version>1.9.53</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>dztheme-generalproduct-api</artifactId>
            <version>2.0.1.9</version>
            <exclusions>
                <exclusion>
                    <artifactId>mdp-boot-starter</artifactId>
                    <groupId>com.meituan.mdp.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.tpfun</groupId>
            <artifactId>tpfun-product-api</artifactId>
            <version>3.6.15</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.joy</groupId>
            <artifactId>joy-order-api</artifactId>
            <version>1.9.14</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.dealuser</groupId>
            <artifactId>price-display-api</artifactId>
            <version>0.0.182</version>
            <exclusions>
                <exclusion>
                    <artifactId>config-standardize-api</artifactId>
                    <groupId>com.sankuai.nib.mkt</groupId>
                </exclusion>
            </exclusions>
        </dependency>

    </dependencies>


    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <requiresUnpack>
                        <dependency>
                            <groupId>com.dianping.dpsf</groupId>
                            <artifactId>dpsf-net</artifactId>
                        </dependency>
                    </requiresUnpack>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.societegenerale.commons</groupId>
                <artifactId>arch-unit-maven-plugin</artifactId>
                <version>2.3.0</version>
                <configuration>
                    <projectPath>${project.basedir}/target</projectPath>
                    <rules>
                        <configurableRules>
                            <configurableRule>
                                <rule>com.sankuai.athena.arch.ArchRules</rule>
                                <applyOn>
                                    <packageName>com.sankuai.redecision</packageName>
                                    <scope>main</scope>
                                </applyOn>
                                <checks>
                                    <check>CYCLOMATIC_COMPLEXITY_10_CHECKER</check>
                                </checks>
                            </configurableRule>
                        </configurableRules>
                    </rules>
                </configuration>
                <executions>
                    <execution>
                        <phase>test</phase>
                        <goals>
                            <goal>arch-test</goal>
                        </goals>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>com.sankuai</groupId>
                        <artifactId>athena-arch-rules</artifactId>
                        <version>0.0.4</version>
                    </dependency>
                </dependencies>
            </plugin>
        </plugins>
    </build>

</project>