<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.meituan.mdp</groupId>
        <artifactId>mdp-basic-parent</artifactId>
        <version>1.7.17.6</version>
        <relativePath/>
    </parent>

    <groupId>com.sankuai.dzim</groupId>
    <artifactId>pilot-parent</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>
    <name>pilot-parent</name>

    <modules>
        <module>pilot-api</module>
        <module>pilot</module>
    </modules>

    <properties>
        <revision>1.0.0-SNAPSHOT</revision>
        <xstream.version>1.4.20</xstream.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>mss-java-sdk-s3</artifactId>
                <version>1.10.0</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.httpcomponents</groupId>
                        <artifactId>httpclient</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping.swallow</groupId>
                <artifactId>swallow-consumerclient</artifactId>
                <version>0.8.11.9</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.zebra</groupId>
                <artifactId>zebra-calcite</artifactId>
                <version>3.3.1</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.zebra</groupId>
                <artifactId>zebra-tool</artifactId>
                <version>3.3.1</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.dzim</groupId>
                <artifactId>pilot-api</artifactId>
                <version>0.0.24</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.it.sso</groupId>
                <artifactId>sso-java-sdk</artifactId>
                <version>2.5.8.4</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.mdp</groupId>
                <artifactId>mdp-boot-initializr-common</artifactId>
                <version>1.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.pearl</groupId>
                <artifactId>pearl</artifactId>
                <version>5.3.8</version>
                <exclusions>
                    <exclusion>
                        <groupId>javax.servlet</groupId>
                        <artifactId>servlet-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.dianping.cat</groupId>
                        <artifactId>cat-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.jboss.netty</groupId>
                        <artifactId>netty</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.thoughtworks.xstream</groupId>
                <artifactId>xstream</artifactId>
                <version>${xstream.version}</version>
            </dependency>
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter-api</artifactId>
                <version>5.7.1</version>
            </dependency>
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter-engine</artifactId>
                <version>5.7.1</version>
            </dependency>
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter-params</artifactId>
                <version>5.7.1</version>
            </dependency>
            <dependency>
                <groupId>org.junit.vintage</groupId>
                <artifactId>junit-vintage-engine</artifactId>
                <version>5.7.1</version>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>3.8.0</version>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-inline</artifactId>
                <version>3.8.0</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                        <configuration>
                            <flattenMode>defaults</flattenMode>
                            <pomElements>
                                <parent>expand</parent>
                                <profiles>expand</profiles>
                                <dependencies>keep</dependencies>
                                <build>keep</build>
                            </pomElements>
                            <updatePomFile>true</updatePomFile>
                        </configuration>
                    </execution>
                    <execution>
                        <id>flatten-clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
