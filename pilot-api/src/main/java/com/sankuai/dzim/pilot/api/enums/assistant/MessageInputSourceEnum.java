package com.sankuai.dzim.pilot.api.enums.assistant;

public enum MessageInputSourceEnum {
    KEYBOARD(1, "键盘"),
    RECOMMEND_QUEStION(2, "推荐问题"),
    REPEAT_GENERATE(3, "重新生成"),


    ;

    private String name;
    private int type;

    MessageInputSourceEnum(int type, String name) {
        this.type = type;
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public int getType() {
        return type;
    }

    public static MessageInputSourceEnum getByType(int type) {
        for (MessageInputSourceEnum inputSourceEnum : MessageInputSourceEnum.values()) {
            if (inputSourceEnum.getType() == type) {
                return inputSourceEnum;
            }
        }
        return null;
    }

}
