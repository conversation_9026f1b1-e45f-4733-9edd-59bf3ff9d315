package com.sankuai.dzim.pilot.api.data.beauty.ai;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


@Data
public class UserBookReq implements Serializable {

    /**
     * 平台 1-点评 2-美团
     */
    private int platform;

    /**
     * 用户ID，跟随平台
     */
    private long userId;

    /**
     * 城市ID，跟随平台
     */
    private int cityId;

    /**
     * 经纬度
     */
    private Double lng;

    /**
     * 经纬度
     */
    private Double lat;

    /**
     * 商详页选择的门店，商详页入口传入
     * 门店ID，跟随平台
     */
    private long shopId;

    /**
     * 是否需要推荐其他商家 商详页入口传入
     */
    private int recommendSwitch;

    /**
     * 用户手机号：明文
     */
    private String userPhone;

    /**
     * 用户选择的位置描述信息
     */
    private String position;

    /**
     * 预约类型
     *
     * @see IntentProjectCodeEnum
     */
    private int bookType;

    /**
     * 预约起始时间
     */
    private Date bookStartTime;

    /**
     * 预约结束时间
     */
    private Date bookEndTime;

    /**
     * 行政区ID
     */
    private List<Integer> districtId;

    /**
     * 商圈ID
     */
    private List<Integer> regionId;

    /**
     * 地铁站ID
     */
    private List<Integer> stationId;

    /**
     * 地铁线ID
     */
    private List<Integer> stationLine;

    /**
     * sortType: 排序方式
     * 智能排序: AI
     * 离我最近: distance
     * 评价最高: comment
     * 人气优先：popularity
     * 价格最低: priceAsc
     * 价格最高：priceDesc
     * 不传时默认 智能排序
     */
    private int sortType;

    /**
     * 距离范围
     * @see com.sankuai.dzim.pilot.process.aibook.data.DistanceRangeEnum
     */
    private String distanceRange;

    /**
     * 设备id
     * 点评: dpid
     * 美团: uuid
     */
    private String deviceId;

    /**
     * 召回团购价格下限
     */
    private Integer lowerPrice;

    /**
     * 召回团购价格上限
     */
    private Integer upperPrice;
}
