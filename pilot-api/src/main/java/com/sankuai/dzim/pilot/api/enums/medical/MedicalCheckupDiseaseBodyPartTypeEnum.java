package com.sankuai.dzim.pilot.api.enums.medical;

import lombok.Getter;

/**
 * @author: z<PERSON><PERSON><PERSON>
 * @date: 2024/9/13
 */
@Getter
public enum MedicalCheckupDiseaseBodyPartTypeEnum {

    HEAD(1, "头部"),

    UPPER_BODY(2, "上半身"),

    LOWER_BODY(3, "下半身"),

    ;

    private int type;

    private String desc;

    MedicalCheckupDiseaseBodyPartTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static MedicalCheckupDiseaseBodyPartTypeEnum getByType(int type) {
        for (MedicalCheckupDiseaseBodyPartTypeEnum typeEnum : MedicalCheckupDiseaseBodyPartTypeEnum.values()) {
            if (typeEnum.getType() == type) {
                return typeEnum;
            }
        }
        return null;
    }
}
