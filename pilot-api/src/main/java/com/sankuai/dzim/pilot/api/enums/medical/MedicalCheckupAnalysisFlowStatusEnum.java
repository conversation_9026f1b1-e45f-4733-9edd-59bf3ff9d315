package com.sankuai.dzim.pilot.api.enums.medical;

import lombok.Getter;

/**
 * @author: zhou<PERSON><PERSON>
 * @date: 2024/9/11
 */
@Getter
public enum MedicalCheckupAnalysisFlowStatusEnum {

    // 以下为解读中的中间状态
    OCR_ING(1, "文本识别中"),

    LLM_ANALYSIS_ING(2, "报告分析中"),

    LLM_OUTPUT_ING(3, "报告解读输出中")

    ;

    private int type;

    private String desc;

    MedicalCheckupAnalysisFlowStatusEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static MedicalCheckupAnalysisFlowStatusEnum getByType(int type) {
        for (MedicalCheckupAnalysisFlowStatusEnum typeEnum : MedicalCheckupAnalysisFlowStatusEnum.values()) {
            if (typeEnum.getType() == type) {
                return typeEnum;
            }
        }
        return null;
    }
}
