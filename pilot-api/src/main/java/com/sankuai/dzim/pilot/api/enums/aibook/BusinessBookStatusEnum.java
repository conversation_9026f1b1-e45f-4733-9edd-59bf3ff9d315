package com.sankuai.dzim.pilot.api.enums.aibook;

import com.sankuai.dzim.pilot.api.enums.aibook.TaskStatusEnum;
import lombok.Getter;

/**
 * 业务形态上的预约状态，区别与技术上的@TaskStatusEnum
 */
@Getter
public enum BusinessBookStatusEnum {
    INITIAL(1, "预约初始态"),
    POLLING(2, "预约轮询态"),
    SUCCESS(3, "预约成功态"),
    FAIL(4, "预约失败态"),
    DELAY(5, "预约轮询延迟态"),
    POLLING_CANCEL(6, "预约轮询中取消"),
    CANCEL(7, "成功后取消"),

    ;

    private int code;
    private String desc;

    BusinessBookStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static Integer getBusinessBookStatus(Integer taskStatus, boolean overBookTime, Integer leadsStatus) {
        if (taskStatus == null) return INITIAL.getCode();

        // 预约初始态
        if (TaskStatusEnum.getEnum(taskStatus) == null || taskStatus == TaskStatusEnum.BOOK_FAILED_EXPIRED.getCode()
                || taskStatus == TaskStatusEnum.CANCEL.getCode()
                || (taskStatus == TaskStatusEnum.BOOK_SUCCESS.getCode() && overBookTime)
                || taskStatus == TaskStatusEnum.CANCEL_INTERUPT.getCode()) {
            return INITIAL.getCode();
        }

        // 预约轮询态
        if (taskStatus == TaskStatusEnum.HAS_TASK_ASKING.getCode()) {
            return POLLING.getCode();
        }

        // 预约轮询延迟态
        if (taskStatus == TaskStatusEnum.HAS_TASK_DELAY.getCode()) {
            return DELAY.getCode();
        }

        // 预约成功态
        if (taskStatus == TaskStatusEnum.BOOK_SUCCESS.getCode() && !overBookTime) {
            if (leadsStatus != null && leadsStatus == 1) {
                return SUCCESS.getCode();
            }
            return INITIAL.getCode();
        }

        // 预约失败态
        if (taskStatus == TaskStatusEnum.BOOK_FAILED.getCode()) {
            return FAIL.getCode();
        }
        return INITIAL.getCode();
    }


    public static Integer getBusinessBookStatusAssistant(Integer taskStatus, boolean overBookTime, Integer leadsStatus) {
        // 预约初始态
        if (taskStatus == null || TaskStatusEnum.getEnum(taskStatus) == null) {
            return INITIAL.getCode();
        }


        if (taskStatus == TaskStatusEnum.CANCEL.getCode()) {
            return CANCEL.getCode();
        }

        if (taskStatus == TaskStatusEnum.CANCEL_INTERUPT.getCode()) {
            return POLLING_CANCEL.getCode();
        }

        // 预约轮询态
        if (taskStatus == TaskStatusEnum.HAS_TASK_ASKING.getCode()) {
            return POLLING.getCode();
        }

        // 预约轮询延迟态
        if (taskStatus == TaskStatusEnum.HAS_TASK_DELAY.getCode()) {
            return DELAY.getCode();
        }

        // 预约成功态
        if (taskStatus == TaskStatusEnum.BOOK_SUCCESS.getCode()) {
            if (leadsStatus != null && leadsStatus == 1) {
                return SUCCESS.getCode();
            }
            return CANCEL.getCode();
        }

        // 预约失败态
        if (taskStatus == TaskStatusEnum.BOOK_FAILED.getCode() || taskStatus == TaskStatusEnum.BOOK_FAILED_EXPIRED.getCode()) {
            return FAIL.getCode();
        }
        return INITIAL.getCode();
    }

}