package com.sankuai.dzim.pilot.api.enums.assistant;

public enum MessageTypeEnum {
    /**
     * 文本
     */
    TEXT(0),
    /**
     * 卡片
     */
    CARD(1),
    /**
     * Tips
     */
    TIPS(2),

    /**
     * 美发预约欢迎语
     */
    HAIR_WELCOME(3),

    /**
     * 美发预约卡片
     */
    HAIR_BOOK_CARD(4),

    /**
     * 工具调用
     */
    TOOL_CALL(5)
    ;


    MessageTypeEnum(int value) {
        this.value = value;
    }

    public final int value;

    public int getValue() {
        return value;
    }

    public static MessageTypeEnum getByValue(int value) {
        for (MessageTypeEnum e : values()) {
            if (e.value == value) {
                return e;
            }
        }
        return null;
    }
}
