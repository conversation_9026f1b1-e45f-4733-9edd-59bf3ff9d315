package com.sankuai.dzim.pilot.api.enums.assistant;

import lombok.Getter;

public enum AssistantTypeEnum {

    MEDICAL_BEAUTY_ASSISTANT(1, "医美小助手"),
    MEDICAL_ASSISTANT(2, "医疗小助手"),
    BEAUTY_ASSISTANT(3, "丽人小助手"),
    PET_ASSISTANT(4, "宠物小助手"),
    ASK_MEDICAL_COSMETIC_ASSISTANT(5, "小美问问-医美小助手"),
    ASK_BEAUTY_ASSISTANT(6, "小美问问-丽人小助手"),
    ASK_MEDICAL_ASSISTANT(7, "小美问问-医疗小助手"),
    AI_GUID_ASSISTANT(8, "找医院导诊助手"),
    MEDICAL_CHECK_UP_ASSISTANT(9, "医疗体检报告解读助手"),
    CHANNEL_SEARCH_REASON_ASSISTANT(10, "垂搜推理小助手"),
    HAIR_CUT_AI_BOOK_ASSISTANT(11, "美发预约小助手"),
    PET_MEDICAL_KNOWLEDGE_SEARCH_ASSISTANT(12, "宠物医疗知识搜一搜助手"),
    NAIL_TRIAL_ASSISTANT(13, "美甲试戴助手"),
    HAIR_DIALOGUE_AGENT(14, "美发/足疗多轮对话助手"),
    BEAM_FWLS_AGENT(15, "服务零售Agent(for beam)"),

    BEAM_RESERVE_AND_BOOK_AGENT_ASSISTANT(992, "beam预约预订agent"),
    RESERVE_AND_BOOK_AGENT_ASSISTANT(993, "预约预订agent"),
    SEARCH_WORKER_AGENT_ASSISTANT(994, "选店选品agent"),
    MULTI_AGENT_ASSISTANT(995, "多Agent测试助手"),
    PLUGIN_ASSISTANT(996, "插件测试助手"),
    MEDICAL_CONSULT_OPENAI_ASSISTANT(998, "医美问诊助手(OpenAI)"),
    MEDICAL_CONSULT_ASSISTANT(999, "医美问诊助手(自研)"),
    INNER_ASSISTANT(1000, "内部助手");

    @Getter
    private int type;
    @Getter
    private String desc;

    AssistantTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static AssistantTypeEnum getByType(int type) {
        for (AssistantTypeEnum assistantTypeEnum : AssistantTypeEnum.values()) {
            if (assistantTypeEnum.getType() == type) {
                return assistantTypeEnum;
            }
        }
        return null;
    }

}
