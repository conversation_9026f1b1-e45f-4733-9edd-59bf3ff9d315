package com.sankuai.dzim.pilot.api.data.search.generative;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AnswerItemE implements Serializable {
    private Boolean hasFilter;

    /**
     * 筛选栏
     */
    private FilterItemDTO filter;
    /**
     * 标题
     */
    private String title;
    /**
     * 正文内容
     */
    private String content;
    /**
     * 图片链接
     */
    private String image;
    /**
     * 正文标题与跳链
     */
    private TitleLinkDTO contentLink;
    /**
     * 下方小标题
     */
    private String relatedLinkTitle;
    /**
     * 下方关联问题
     */
    private List<TitleLinkDTO> relatedLinkList;

    /**
     * 小美问问等图标
     */
    private String icon;
    /**
     * 图标高度
     */
    private Integer iconHeight;
    /**
     * 图标宽度
     */
    private Integer iconWidth;
    /**
     * 多图信息
     */
    private List<MultiImageDTO> images;
}
