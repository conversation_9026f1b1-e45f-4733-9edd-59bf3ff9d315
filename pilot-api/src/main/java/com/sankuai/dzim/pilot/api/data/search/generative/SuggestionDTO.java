package com.sankuai.dzim.pilot.api.data.search.generative;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @author: zhouyibing
 * @date: 2024/5/21
 */
@Data
public class SuggestionDTO implements Serializable {

    /**
     * sug问题
     */
    private String suggestion;

    /**
     * 模版类型
     */
    private Integer templateType;

    /**
     * 关键词列表
     */
    private List<String> keywordList;

    /**
     * 点击后跳转页面，默认跳转搜索结果页
     */
    private String link;

    /**
     * 展示位置
     */
    private int showPosition;
}
