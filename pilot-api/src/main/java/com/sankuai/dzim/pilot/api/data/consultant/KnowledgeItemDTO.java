package com.sankuai.dzim.pilot.api.data.consultant;

import lombok.Data;

import java.io.Serializable;

/**
 * 单条知识详情DTO
 */
@Data
public class KnowledgeItemDTO implements Serializable {

    /**
     * 知识类型
     */
    private KnowledgeTypeItemDTO knowledgeTypeItem;

    /**
     * 项目类型
     */
    private ProjectTypeItemDTO projectTypeItem;

    /**
     * 知识Id
     */
    private String knowledgeId;

    /**
     * 问题
     */
    private String question;

    /**
     * 答案
     */
    private String answer;

}
