package com.sankuai.dzim.pilot.api.enums;

import com.sankuai.dzim.pilot.api.enums.search.generative.GenerativeSearchFeedbackTypeEnum;
import lombok.Getter;

/**
 * @author: zhouyibing
 * @date: 2024/5/23
 */
public enum UserTypeEnum {

    MT(1, "美团"),

    DP(2, "点评"),

    ;

    @Getter
    private int type;

    @Getter
    private final String desc;

    UserTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static UserTypeEnum getByType(int type) {
        for (UserTypeEnum typeEnum : UserTypeEnum.values()) {
            if (typeEnum.getType() == type) {
                return typeEnum;
            }
        }
        return null;
    }
}
