package com.sankuai.dzim.pilot.api.data;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class BizKnowledgeDTO  implements Serializable {

    /**
     * 主键Id
     */
    private long id;

    /**
     * 业务类型
     *
     * @see com.sankuai.dzim.pilot.api.enums.BizKnowledgeTypeEnum
     */
    private int bizType;

    /**
     * 对应向量数据的主键Id
     */
    private long vectorId;

    /**
     * 问题文本
     */
    private String question;

    /**
     * 答案文本
     */
    private String answer;

    /**
     * 与query相似度
     */
    private double similarity;

    /**
     * 编辑者
     */
    private String editor = "";

    /**
     * 添加时间
     */
    private Date addTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
