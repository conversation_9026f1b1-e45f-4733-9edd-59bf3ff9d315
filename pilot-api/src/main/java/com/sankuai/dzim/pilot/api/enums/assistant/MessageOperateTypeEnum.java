package com.sankuai.dzim.pilot.api.enums.assistant;

/**
 * <AUTHOR>
 * @since 2024/8/1 21:06
 */
public enum MessageOperateTypeEnum {
    LIKE(1, "点赞"),
    UNLIKE(2, "踩"),
    CANCEL_LIKE(-1, "取消点赞"),
    CANCEL_UNLIKE(-2, "取消踩"),
    AI_BOOK_SUBMIT(3, "提交预约"),
    ;

    private int type;

    private String desc;

    MessageOperateTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public int getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static MessageOperateTypeEnum getByType(int type) {
        for (MessageOperateTypeEnum typeEnum : MessageOperateTypeEnum.values()) {
            if (typeEnum.getType() == type) {
                return typeEnum;
            }
        }
        return null;
    }
}
