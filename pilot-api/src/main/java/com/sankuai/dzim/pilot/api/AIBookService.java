package com.sankuai.dzim.pilot.api;


import com.sankuai.dzim.pilot.api.data.beauty.BookCallDetailDTO;
import com.sankuai.dzim.pilot.api.data.beauty.QueryBookCallDetailReq;
import com.sankuai.dzim.pilot.api.data.beauty.ai.BookCallbackReq;
import com.sankuai.dzim.pilot.api.data.beauty.ai.UserBookReq;

import java.util.List;

public interface AIBookService {

    /**
     * 美发不用等取消预约,会外呼通知商家
     *
     * @param leadsId
     * @return 是否取消成功
     */
    boolean cancelBook(Long leadsId);


    /**
     * 外呼结果回调
     *
     * @param req req
     * @return
     */
    boolean callbackBookAI(BookCallbackReq req);

    /**
     * 查询预约外呼详情
     * @param queryBookCallDetailReq
     * @return
     */
    BookCallDetailDTO queryBookCallDetail(QueryBookCallDetailReq queryBookCallDetailReq);

    /**
     * 门店召回(测试使用,请勿调用)
     *
     * @param req
     * @param pageNo 当前页
     * @param recallShopIds 已召回的门店
     * @return
     */
    List<Long> recallRecommendShopIds(UserBookReq req, int pageNo, List<Long> recallShopIds);


    /**
     * 单个执行taskId
     *
     * @param taskId taskId
     */
    boolean executeTaskCall(long taskId);

}
