package com.sankuai.dzim.pilot.api.data;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;


@Data
public class RetrievalKnowledgeRequest implements Serializable {

    /**
     * 检索器名称
     */
    private String retrievalName;

    /**
     * 查询的问题
     */
    private String question;

    /**
     * 查询的参数
     */
    private Map<String, String> params;

    /**
     * 返回前几的知识
     */
    private int topK;
}
