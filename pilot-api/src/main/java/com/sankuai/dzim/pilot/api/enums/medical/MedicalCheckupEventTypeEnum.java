package com.sankuai.dzim.pilot.api.enums.medical;

import lombok.Getter;

/**
 * @author: zhouyi<PERSON>
 * @date: 2024/9/14
 */
@Getter
public enum MedicalCheckupEventTypeEnum {

    UPLOAD(1, "上传成功"),

    ;

    private int type;

    private String desc;

    MedicalCheckupEventTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static MedicalCheckupEventTypeEnum getByLevel(int type) {
        for (MedicalCheckupEventTypeEnum typeEnum : MedicalCheckupEventTypeEnum.values()) {
            if (typeEnum.getType() == type) {
                return typeEnum;
            }
        }
        return null;
    }
}
