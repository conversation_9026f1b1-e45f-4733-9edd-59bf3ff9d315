package com.sankuai.dzim.pilot.api.data.assistant;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/07/31 15:04
 */
@Data
public class PilotChatGroupDTO {
    private String userId;
    private Long chatGroupId;
    private Integer assistantType;
    private Long startMessageId;
    private Long lastMessageId;
    private Date addTime;
    private Date updateTime;
    /**
     * 欢迎语信息
     */
    private String title;
    private String avatar;
    private Integer avatarHeight;
    private Integer avatarWidth;
    private String subTitle;
    private String inputText;
    private String theme;
    private String extra;
    /**
     * 消息
     */
    private List<PilotMessageDTO> messages;

    // 是否需要主动请求流式
    private Boolean needCallStream;
}
