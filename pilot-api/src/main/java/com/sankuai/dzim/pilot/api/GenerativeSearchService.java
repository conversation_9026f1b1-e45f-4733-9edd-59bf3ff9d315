package com.sankuai.dzim.pilot.api;

import com.sankuai.dzim.pilot.api.data.ImportTemplateData;
import com.sankuai.dzim.pilot.api.data.search.generative.*;

import java.util.List;
import java.util.Map;

/**
 * 生成式搜索服务
 * @author: zhouyibing
 * @date: 2024/5/21
 */
public interface GenerativeSearchService {

    /**
     * 查询sug
     * 已废弃，请使用querySugList接口
     * @param request
     * @return
     */
    @Deprecated
    SuggestionDTO querySug(QuerySuggestionRequest request);

    /**
     * 查询sug(批量接口)
     * @param request
     * @return
     */
    BatchSuggestionDTO querySugList(QuerySuggestionRequest request);

    /**
     * 查询大模型答案
     * @param request
     * @return
     */
    SuggestionAnswerDTO queryAnswer(QueryAnswerRequest request);

    /**
     * 添加答案反馈
     * @param request
     * @return
     */
    AnswerFeedbackAddDTO addAnswerFeedback(AddAnswerFeedbackRequest request);

    /**
     * 查询项目关联sug
     * @param request
     * @return
     */
    ProjectRelatedSuggestionDTO queryProjectRelatedSug(QueryProjectRelatedSuggestionRequest request);

    /**
     * 导出答案
     * @param fileName
     * @param minId
     * @param maxId
     * @param type 类型 0-导出解析后的格式 1-导出原始格式
     * @return
     */
    boolean exportGenerativeSearchAnswer(String fileName, long minId, long maxId, int type);

    /**
     * 更新答案
     * @param request
     * @return
     */
    boolean updateGenerativeSearchAnswer(UpdateAnswerRequest request);

    /**
     * 删除答案
     * @param idList
     * @return
     */
    int deleteGenerativeSearchAnswer(List<Long> idList);

    /**
     * 更新答案状态
     * @param idList
     * @param status
     * @return
     */
    int updateGenerativeSearchAnswerStatus(List<Long> idList, int status);

    /**
     * 删除query词
     * @param idList
     * @return
     */
    int deleteGenerativeSearchWord(List<Long> idList);

    void updateRecommendReason(int minId, int maxId);

    /**
     * 根据笔记生成word，保存在笔记扩展字段
     * @param bizType
     * @param threadNum
     * @param minId
     * @param maxId
     */
    void batchGenerateWordByNote(int bizType, int threadNum, int minId, int maxId);

    /**
     * 导入生成式word
     * @param fileName
     */
    void importGenerativeSearchWord(String fileName);

    /**
     * 使用笔记召回城市,行政区,以及类目下的poi
     * @param mtCityId2DistrictIdMap
     * @param similarCat1Ids
     */
    void offlineRecallPoiWithNote(int startWordId, int endWordId, Map<String, List<Integer>> mtCityId2DistrictIdMap, List<List<Integer>> similarCat1Ids);

    /**
     * 批量生成Extend数据
     * @param templateType
     * @param fileName
     */
    void offlineGenerateExtend(int templateType, String fileName);

    /**
     * 批量生成Extend数据（直搜词场景）
     * @param templateType
     * @param fileName
     */
    void offlineGenerateExtendWithSave(int templateType, String fileName);

    /**
     * 批量删除生成式搜索答案扩展数据
     * @param minId
     * @param maxId
     */
    void deleteGenerateSearchAnswerExtend(int minId, int maxId);

    /**
     * 批量处理生成式搜索答案扩展数据中的Value字段（将*数据*转换成**数据**）
     * @param minId
     * @param maxId
     */
    void batchProcessSearchAnswerExtendValue(int minId, int maxId);

    /**
     * 批量插入SearchWord
     * @param templateType
     * @param fileName
     */
    void batchInsertGenerateSearchAnswerWord(int templateType, String fileName);

    /**
     * 根据答案id选择图片
     * @param answerIds
     * @param type 业务类型, liren\xiuyu
     */
    int choosePicWithAnswerId(List<Integer> answerIds, String type);

    /**
     * 根据id列表批量删除生成式搜索答案扩展数据
     * @param ids
     */
    void deleteGenerateSearchAnswerExtend(List<Long> ids);

    /**
     * 更新Extend表中的Value信息
     * @param request
     */
    void updateGenerativeSearchAnswerExtend(UpdateAnswerExtendRequest request);

    /**
     * 重写excel里image的desc
     * @param filename
     */
    void batchGenerateBLZPicDesc(String filename);

    /**
     * 重写避雷针question
     */
    void batchGenerateBLZQuestion(String filename);

    /**
     * 删除keyword和百补冲突 status设为2
     */
    void batchDeleteBaiBuConflict(Long startId, Long endId, List<String> words, Integer status, Integer bizType);


    void batchGetBLZAnswers(String context);

    /**
     * 批量新增或更新有分类的问题（Word表）
     * @param fileName
     */
    void batchInsertOrUpdateQuestionCategory(String fileName);

    /**
     * 理疗场景 输入搜索词获取点评笔记
     * @param keyword
     * @return
     */
    List<TherapyNoteDTO> queryTherapyNote(String keyword);


    /**
     * 导入模板数据
     * @param importTemplateData
     * @param fileName
     */
    void importTemplateData(ImportTemplateData importTemplateData, String fileName);

    /**
     * 图片主体检测
     * @param imgUrls
     */
    Map<String, String> imageDetect(List<String> imgUrls);
}
