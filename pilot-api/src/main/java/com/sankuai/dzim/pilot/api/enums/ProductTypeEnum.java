package com.sankuai.dzim.pilot.api.enums;

import lombok.Getter;

public enum ProductTypeEnum {
    DEAL(1, "团购"),
    PREPAY(2, "预付");

    @Getter
    private int type;

    @Getter
    private final String desc;

    ProductTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static ProductTypeEnum getByType(int type) {
        for (ProductTypeEnum typeEnum : ProductTypeEnum.values()) {
            if (typeEnum.getType() == type) {
                return typeEnum;
            }
        }
        return null;
    }
}
