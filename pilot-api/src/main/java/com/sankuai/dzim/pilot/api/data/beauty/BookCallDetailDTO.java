package com.sankuai.dzim.pilot.api.data.beauty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BookCallDetailDTO implements Serializable {

    /**
     * 预约状态
     * @see com.sankuai.dzim.pilot.api.enums.aibook.BusinessBookStatusEnum
     */
    private int bookStatus;

    /**
     * 预约成功的预计剩余时间
     */
    private String remainTime;

    /**
     * 当前外呼商家顺序
     */
    private int callOrder;

    /**
     * 外呼商家总数
     */
    private int totalCallCount;

    /**
     * 预约时间
     */
    private String bookTime;

    /**
     * 预约项目
     */
    private String bookProject;

    /**
     * 门店名
     */
    private String shopName;

    /**
     * 轮询开始时间
     */
    private String pollingBeginTime;

}
