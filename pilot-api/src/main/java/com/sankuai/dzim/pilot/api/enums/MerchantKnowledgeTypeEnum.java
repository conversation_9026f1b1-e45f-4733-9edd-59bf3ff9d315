package com.sankuai.dzim.pilot.api.enums;

import lombok.Getter;

public enum MerchantKnowledgeTypeEnum {
    SHOP(1, "门店"),
    PRODUCT(2, "团购"),
    PREPAY(3, "预付"),
    REVIEW(4, "评论"),
    REVIEW_V2(5, "门店评价-向量数据库支持“城市_类目”检索"),
    NOTE(7, "笔记"),
    NAIL_INFO(8, "美甲款式");

    @Getter
    private int type;

    @Getter
    private final String desc;

    MerchantKnowledgeTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static MerchantKnowledgeTypeEnum getByType(int type) {
        for (MerchantKnowledgeTypeEnum typeEnum : MerchantKnowledgeTypeEnum.values()) {
            if (typeEnum.getType() == type) {
                return typeEnum;
            }
        }
        return null;
    }
}
