package com.sankuai.dzim.pilot.api.enums.search.generative;

import lombok.Getter;

/**
 * @author: zhouyibing
 * @date: 2024/5/22
 */
public enum GenerativeSearchFeedbackTypeEnum {

    LIKE(1, "点赞"),

    DISLIKE(2, "点踩"),

    CANCEL(3, "取消")

    ;

    @Getter
    private int type;

    @Getter
    private final String desc;

    GenerativeSearchFeedbackTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static GenerativeSearchFeedbackTypeEnum getByType(int type) {
        for (GenerativeSearchFeedbackTypeEnum typeEnum : GenerativeSearchFeedbackTypeEnum.values()) {
            if (typeEnum.getType() == type) {
                return typeEnum;
            }
        }
        return null;
    }
}
