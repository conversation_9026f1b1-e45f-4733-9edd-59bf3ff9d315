package com.sankuai.dzim.pilot.api.enums.beauty.ai;

/**
 * <AUTHOR>
 * @since 2025/1/8 20:56
 */
public enum BookResultEnum {
    BOOK_SUCCESS(101, "预约成功"),
    BOOK_FAIL_NO_SUPPLY(102, "预约失败-无供给"),
    BOOK_FAIL_OTHER(103, "预约失败-其他理由"),
    NOT_CONNECTED(104, "未接通-可重拨"),
    NOT_CONNECTED_FAILED(108, "未接通-不可重拨"),
    NOT_CONNECTED_BALCKLIST(109, "未接通-拉黑"),
    CANCEL_SUCCESS(105, "取消成功"),
    CANCEL_FAILED(106, "取消失败"),
    CALL_BACK_TIMEOUT(107, "回调超时"),
    CONNECTED_BLACKLIST_REJECT(110, "已接通-拒绝预约，拉黑1天"),
    CONNECTED_BLACKLIST_EMO(111, "已接通-负向情绪，拉黑7天"),

    ;


    private int code;

    private String desc;


    BookResultEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}