package com.sankuai.dzim.pilot.api.data.assistant;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/8/21 11:02
 */
@Data
public class PilotEntranceReqDTO implements Serializable {

    /**
     * 页面类型 1-频道页 2-商详页 3-团详页
     */
    private int sourceType;

    /**
     * 频道页BizCode，频道页传入
     */
    private String bizCode;

    /**
     * 门店ID，商详页、团详页传入
     */
    private long shopId;

    /**
     * 平台类型：1-点评 2-美团
     */
    private int platform;

    /**
     * 用户ID，跟随平台
     */
    private long userId;

    /**
     * AppId，客户端传入
     */
    private int appId;

    /**
     * 设备ID
     */
    private String deviceId;

    /**
     * 设备UnionId
     */
    private String unionId;

    /**
     * 位置信息
     */
    private String lat;

    /**
     * 位置信息
     */
    private String lng;

    /**
     * App版本号
     */
    private String version;

    /**
     * 客户端类型
     */
    private int clientType;
}
