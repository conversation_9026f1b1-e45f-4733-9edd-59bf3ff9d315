package com.sankuai.dzim.pilot.api;

import com.sankuai.dzim.pilot.api.data.*;

import java.util.List;

/**
 * 知识服务
 */
public interface KnowledgeService {

    /**
     * 添加业务知识
     *
     * @param request
     * @return
     */
    long addBizKnowledge(AddBizKnowledgeRequest request);

    /**
     * 批量添加业务知识, requests为空, 从文件中读取
     *
     * @param fileName
     * @param requests
     * @return
     */
    int batchAddBizKnowledge(String fileName, List<AddBizKnowledgeRequest> requests);

    /**
     * 批量添加避雷针业务知识, requests为空, 从文件中读取
     *
     * @param fileName
     * @param requests
     * @return
     */
    int batchAddBeautyBizKnowledge(String fileName, List<AddBizKnowledgeRequest> requests);

    /**
     * 批量添加避雷针业务知识 图片描述和问题重新通过gpt4生成
      * @param fileName
     * @param requests
     * @return
     */
    int batchAddBeautyKnowledgeAfterGPT4(String fileName, int start, int end);

    /**
     * 删除业务知识
     *
     * @param bizKnowledgeId(BizKnowledge表的主键)
     * @return
     */
    boolean deleteBizKnowledge(long bizKnowledgeId, String editor);

    /**
     * 批量删除业务知识
     *
     * @param bizKnowledgeIds
     * @return
     */
    int batchDeleteBizKnowledge(List<Long> bizKnowledgeIds, String editor);

    /**
     * 添加业务知识
     *
     * @param request
     * @return
     */
    long addMerchantKnowledge(AddMerchantKnowledgeRequest request);

    /**
     * 批量添加业务知识, requests为空, 从文件中读取
     *
     * @param fileName
     * @param requests
     * @return
     */
    int batchAddMerchantKnowledge(String fileName, List<AddMerchantKnowledgeRequest> requests);

    /**
     * 删除业务知识
     *
     * @param merchantKnowledgeId(MerchantKnowledge表的主键)
     * @return
     */
    boolean deleteMerchantKnowledge(long merchantKnowledgeId, String editor);

    /**
     * 批量删除业务知识
     *
     * @param merchantKnowledgeIds
     * @return
     */
    int batchDeleteMerchantKnowledge(List<Long> merchantKnowledgeIds, String editor);

    /**
     *
     * @param query
     * @param bizType
     * @param topK
     * @return
     */
    List<BizKnowledgeDTO> searchBizKnowledge(int bizType, String query, int topK);

    /**
     * 搜索商户知识
     *
     * @param query
     * @param type
     * @param subjectId
     * @param topK
     * @return
     */
    List<MerchantKnowledgeDTO> searchMerchantKnowledge(String query, int type, String subjectId, int topK);

    /**
     * 批量导入笔记
     * @param fileName
     * @return
     */
    int batchAddNote(String fileName);

    /**
     * 检索知识
     * @param request
     * @return
     */
    List<RetrievalKnowledgeDTO> retrievalKnowledge(RetrievalKnowledgeRequest request);

    /**
     * 导入评论
     *
     * @param fileName
     * @return
     */
    int batchAddReview(String fileName);

    /**
     * 删除评论
     * @param reviewId
     * @return
     */
    boolean deleteReview(long reviewId);

    /**
     * 医美问题打分
     * @param name
     * @param size
     * @return
     */
    int addBLZQuestionScore(String fileName, int size);
}
