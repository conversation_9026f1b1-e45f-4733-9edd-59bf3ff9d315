package com.sankuai.dzim.pilot.api.enums.assistant;

public enum MessageSendDirectionEnum {
    /**
     * Client -> Merchant [C端用户发送给商家]
     */
    CLIENT_SEND(0),

    /**
     * ASSISTANT -> Client [小助手发送C端用户]
     */
    ASSISTANT_SEND(1),

    /**
     * SYSTEM -> LLM [系统发送大模型]
     */
    SYSTEM_SEND(2),

    /**
     * LLM -> Tool [工具调用]
     */
    TOOL_CALL(3)
    ;

    MessageSendDirectionEnum(int value) {
        this.value = value;
    }

    public final int value;

    public int getValue() {
        return value;
    }

    public static MessageSendDirectionEnum getByValue(int value) {
        for (MessageSendDirectionEnum e : values()) {
            if (e.value == value) {
                return e;
            }
        }
        return null;
    }

    public static boolean isUserSend(int type) {
        return type == MessageSendDirectionEnum.CLIENT_SEND.value;
    }

}

