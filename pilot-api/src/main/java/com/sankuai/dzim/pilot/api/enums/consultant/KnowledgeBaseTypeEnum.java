package com.sankuai.dzim.pilot.api.enums.consultant;

import lombok.Getter;

@Getter
public enum KnowledgeBaseTypeEnum {
    YIMEI_CONSULTANT(1, "医美有问必答知识库"),

    DENTISTRY_CONSULTANT(2, "齿科有问必答知识库"),

    GYNECOLOGY_CONSULTANT(3, "妇科有问必答知识库"),

    OVERSEAS_YIMEI_CONSULTANT(4, "境外医美有问必答知识库");

    private final int type;

    private final String desc;

    KnowledgeBaseTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static KnowledgeBaseTypeEnum getByType(int type) {
        for (KnowledgeBaseTypeEnum typeEnum : values()) {
            if (typeEnum.getType() == type) {
                return typeEnum;
            }
        }
        return null;
    }
}
